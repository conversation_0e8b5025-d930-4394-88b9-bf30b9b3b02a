<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>20 - 80 &times; 16 &divide; 5 + 15 = ?</p>",
                    question_hi: "<p>1. यदि &lsquo;+&rsquo; और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा \'&times;\' और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ?<br>20 - 80 &times; 16 &divide; 5 + 15 = ?</p>",
                    options_en: ["<p>32</p>", "<p>35</p>", 
                                "<p>30</p>", "<p>36</p>"],
                    options_hi: ["<p>32</p>", "<p>35</p>",
                                "<p>30</p>", "<p>36</p>"],
                    solution_en: "<p>1.(c) <strong>Given :- </strong>20 - 80 &times; 16 <math display=\"inline\"><mo>&#247;</mo></math> 5 + 15<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>20 + 80 <math display=\"inline\"><mo>&#247;</mo></math> 16 &times; 5 - 15<br>20 + 5 &times; 5 - 15 = 30</p>",
                    solution_hi: "<p>1.(c) <strong>दिया गया :-</strong> 20 - 80 &times; 16 <math display=\"inline\"><mo>&#247;</mo></math>5 + 15<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>20 + 80 <math display=\"inline\"><mo>&#247;</mo></math> 16 &times; 5 - 15<br>20 + 5 &times; 5 - 15 = 30</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. What will come in the place of \'?&rsquo; in the following equation, if &lsquo;+\' and \'-&rsquo; are interchanged ?<br>47 + 10 &times; 3 - 32 &divide; 8 = ?</p>",
                    question_hi: "<p>2. यदि \'+\' और &lsquo;-&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ?<br>47 + 10 &times; 3 - 32 &divide; 8 = ?</p>",
                    options_en: ["<p>19</p>", "<p>21</p>", 
                                "<p>31</p>", "<p>27</p>"],
                    options_hi: ["<p>19</p>", "<p>21</p>",
                                "<p>31</p>", "<p>27</p>"],
                    solution_en: "<p>2.(b) <strong>Given :-</strong> 47 + 10 &times; 3 - 32 &divide; 8&nbsp;<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; we get<br>47 - 10 &times; 3 + 32 <math display=\"inline\"><mo>&#247;</mo></math> 8<br>47 - 30 + 4<br>47 - 26 = 21</p>",
                    solution_hi: "<p>2.(b) <strong>दिया गया :- </strong>47 + 10 &times; 3 - 32 &divide; 8<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' को आपस में बदलने पर हमें प्राप्त होता है<br>47 - 10 &times; 3 + 32 <math display=\"inline\"><mo>&#247;</mo></math> 8<br>47 - 30 + 4<br>47 - 26 = 21</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. If &lsquo;Y&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;N&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;M&rsquo; stands for &lsquo;+&rsquo; and &lsquo;G&rsquo; stands for &lsquo;&ndash;&rsquo;, what will come in place of the question mark (?) in the following equation ?&nbsp;<br>100 N 80 G 40 Y 20 M 10 = ?</p>",
                    question_hi: "<p>3. यदि \'Y\' का अर्थ \'&divide;\', \'N\' का अर्थ \'&times;\', \'M\' का अर्थ \'+\' और \'G\' का अर्थ \'&ndash;\' है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?&nbsp;<br>100 N 80 G 40 Y 20 M 10 = ?</p>",
                    options_en: ["<p>8976</p>", "<p>8008</p>", 
                                "<p>5776</p>", "<p>987</p>"],
                    options_hi: ["<p>8976</p>", "<p>8008</p>",
                                "<p>5776</p>", "<p>987</p>"],
                    solution_en: "<p>3.(b) <strong>Given :- </strong>100 N 80 G 40 Y 20 M 10<br>As per given instruction after interchanging the letter with sign we get<br>100 &times; 80 - 40 &divide; 20 + 10<br>8000 - 2 + 10 = 8008</p>",
                    solution_hi: "<p>3.(b) <strong>दिया गया :-</strong> 100 N 80 G 40 Y 20 M 10<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>100 &times; 80 - 40 &divide; 20 + 10<br>8000 - 2 + 10 = 8008</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, then the&nbsp;resultant of which of the following will be 428 ?</p>",
                    question_hi: "<p>4. यदि &lsquo;A&rsquo; का अर्थ &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; का अर्थ &lsquo;&times;&rsquo;, &lsquo;C&rsquo; का अर्थ &lsquo;+&rsquo; और &lsquo;D&rsquo; का अर्थ &lsquo;&ndash;&rsquo; है, तो निम्नलिखित में से किसका परिणाम 428 होगा ?</p>",
                    options_en: ["<p>113 B 4 A 62 D 2 C 7</p>", "<p>113 A 4 D 62 B 2 C 7</p>", 
                                "<p>113 B 4 C 62 A 2 D 7</p>", "<p>113 B 4 D 62 A 2 C 7</p>"],
                    options_hi: ["<p>113 B 4 A 62 D 2 C 7</p>", "<p>113 A 4 D 62 B 2 C 7</p>",
                                "<p>113 B 4 C 62 A 2 D 7</p>", "<p>113 B 4 D 62 A 2 C 7</p>"],
                    solution_en: "<p>4.(d) After going through all the options, option d satisfies. <br>113 B 4 D 62 A 2 C 7 <br>As per given instruction after interchanging the letter with sign we get<br>113 &times; 4 - 62 &divide; 2 + 7<br>452 - 31 + 7 <br>452 - 24 = 428</p>",
                    solution_hi: "<p>4.(d) <br>सभी विकल्पों की जांच करने पर विकल्प d संतुष्ट करता है।<br>113 B 4 D 62 A 2 C 7 <br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>113 &times; 4 - 62 &divide; 2 + 7<br>452 - 31 + 7 <br>452 - 24 = 428</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo;are interchanged and \'-&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>98 - 14 <math display=\"inline\"><mo>&#215;</mo></math> 11 + 6 &divide; 23 = ?</p>",
                    question_hi: "<p>5. यदि &lsquo;+&rsquo;और \'<math display=\"inline\"><mo>&#215;</mo></math>\' को आपस में बदल दिया जाए तथा &lsquo;-&rsquo; और\' को &lsquo;&divide;&rsquo; आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ?<br>98 - 14 <math display=\"inline\"><mo>&#215;</mo></math> 11 + 6 &divide; 23 = ?</p>",
                    options_en: ["<p>56</p>", "<p>52</p>", 
                                "<p>55</p>", "<p>50</p>"],
                    options_hi: ["<p>56</p>", "<p>52</p>",
                                "<p>55</p>", "<p>50</p>"],
                    solution_en: "<p>5.(d) <strong>Given :-</strong> 98 - 14 &times; 11 + 6 &divide; 23<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo; and &lsquo;&divide;&rsquo; we get<br>98 &divide; 14 + 11 &times; 6 - 23<br>7 + 66 - 23<br>73 - 23 = 50</p>",
                    solution_hi: "<p>5.(d) <strong>दिया गया :-</strong> 98 - 14 &times; 11 + 6 &divide; 23<br>दिए गए निर्देश के अनुसार \'+\' और \'&times;\' तथा \'-\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>98 &divide; 14 + 11 &times; 6 - 23<br>7 + 66 - 23<br>73 - 23 = 50</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and are &lsquo;-&rsquo; interchanged and &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?&nbsp;<br>216 <math display=\"inline\"><mo>&#215;</mo></math> 9 + 52 - 7 &divide; 7 = ?</p>",
                    question_hi: "<p>6. यदि &lsquo;+&rsquo;ओर &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo;और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>216 <math display=\"inline\"><mo>&#215;</mo></math> 9 + 52 - 7 &divide; 7 = ?</p>",
                    options_en: ["<p>21</p>", "<p>19</p>", 
                                "<p>20</p>", "<p>22</p>"],
                    options_hi: ["<p>21</p>", "<p>19</p>",
                                "<p>20</p>", "<p>22</p>"],
                    solution_en: "<p>6.(a) <strong>Given :- </strong>216 &times; 9 + 52 - 7 &divide; 7<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; .<br>216 &divide; 9 - 52 + 7 &times; 7<br>24 - 52 + 49<br>24 - 3 = 21</p>",
                    solution_hi: "<p>6.(a) <strong>दिया गया :-</strong> 216 &times; 9 + 52 - 7&divide; 7<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' और \'&times;\' और \'&divide;\' को आपस में बदलने के बाद।<br>216 &divide; 9 - 52 + 7 &times; 7<br>24 - 52 + 49<br>24 - 3 = 21</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. If \'A\' stands for \'&divide;\', \'B\' stands for \'<math display=\"inline\"><mo>&#215;</mo></math>\', \'C\' stands for \'+\' and \'D\' stands for \'-\', what will come in place of the question mark (?) in the following equation ?<br>222 A 2 C 7 B 3 D 11 = ?</p>",
                    question_hi: "<p>7. यदि \'A\' का अर्थ \'&divide;\', \'B\' का अर्थ \'<math display=\"inline\"><mo>&#215;</mo></math>\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ \'-\' है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>222 A 2 C 7 B 3 D 11 = ?</p>",
                    options_en: ["<p>141</p>", "<p>131</p>", 
                                "<p>111</p>", "<p>121</p>"],
                    options_hi: ["<p>141</p>", "<p>131</p>",
                                "<p>111</p>", "<p>121</p>"],
                    solution_en: "<p>7.(d) <strong>Given :- </strong>222 A 2 C 7 B 3 D 11<br>As per given instruction after interchanging the letter with sign we get<br>222 &divide; 2 + 7 &times; 3 - 11<br>111 + 21 - 11<br>100 + 21 = 121</p>",
                    solution_hi: "<p>7.(d) <strong>दिया गया :- </strong>222 A 2 C 7 B 3 D 11<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>222 &divide; 2 + 7 &times; 3 - 11<br>111 + 21 - 11<br>100 + 21 = 121</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Which two numbers should be interchanged to make the given equation correct ?<br>29 + 46 - (48 &divide; 4) + (7 <math display=\"inline\"><mo>&#215;</mo></math> 3) - 26 = 61<br>(<strong>NOTE : </strong>Numbers must be interchanged and not the constituent digits e.g. if 2 and 3 are to be interchanged in the equation 43 <math display=\"inline\"><mo>&#215;</mo></math> 3 + 4 &divide; 2, then interchanged equation is 43 &times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>8. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए ?<br>29 + 46 - (48 &divide; 4) + (7 <math display=\"inline\"><mo>&#215;</mo></math> 3) - 26 = 61<br>(<strong>ध्यान दें : </strong>संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए। उदाहरण के लिए समीकरण 43 <math display=\"inline\"><mo>&#215;</mo></math> 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43 &times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: [" 46 and 26 ", " 46 and 48 ", 
                                " 26 and 29 ", " 4 and 3"],
                    options_hi: [" 46 और 26 ", " 46 और 48 ",
                                " 26 और 29 ", " 4 और 3"],
                    solution_en: "<p>8.(d)<strong> Given :- </strong>29 + 46 - (48 <math display=\"inline\"><mo>&#247;</mo></math> 4) + (7 &times; 3) - 26 = 61<br>After checking all the options one by one , only option (d) satisfies. After interchanging 4 and 3 we get<br>29 + 46 - (48 <math display=\"inline\"><mo>&#247;</mo></math> 3) + (7 &times; 4) - 26<br>29 + 46 - 16 + 28 - 26<br>75 + 28 - 42 <br>75 - 14 = 61<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>8.(d)<strong> दिया गया :- </strong>29 + 46 - (48 <math display=\"inline\"><mo>&#247;</mo></math> 4) + (7 &times; 3) - 26 = 61<br>सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (d) ही संतुष्ट करता है। 4 और 3 को आपस में बदलने पर हमें प्राप्त होता है<br>29 + 46 - (48 <math display=\"inline\"><mo>&#247;</mo></math> 3) + (7 &times; 4) - 26<br>29 + 46 - 16 + 28 - 26<br>75 + 28 - 42 <br>75 - 14 = 61<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. What will come in the place of \'?\' in the following equation if \'+\' and \'&divide;\' are interchanged and \'<math display=\"inline\"><mo>&#215;</mo></math>\' and \'-\' are interchanged ?<br>36 - 42 &divide; 26 + 13 <math display=\"inline\"><mo>&#215;</mo></math> 14 = ?</p>",
                    question_hi: "<p>9. यदि \'+\' और \'&divide;\' को आपस में बदल दिया जाए तथा \'<math display=\"inline\"><mo>&#215;</mo></math>\' और \'&ndash;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ?<br>36 - 42 &divide; 26 + 13 <math display=\"inline\"><mo>&#215;</mo></math> 14 = ?</p>",
                    options_en: ["<p>1200</p>", "<p>1324</p>", 
                                "<p>3467</p>", "<p>1500</p>"],
                    options_hi: ["<p>1200</p>", "<p>1324</p>",
                                "<p>3467</p>", "<p>1500</p>"],
                    solution_en: "<p>9.(d) <strong>Given :-</strong> 36 - 42 <math display=\"inline\"><mo>&#247;</mo></math> 26 + 13 &times; 14 <br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo;<br>36 &times; 42 + 26 <math display=\"inline\"><mo>&#247;</mo></math> 13 - 14<br>1512 + 2 - 14 = 1500</p>",
                    solution_hi: "<p>9.(d) <strong>दिया गया :-</strong> 36 - 42 <math display=\"inline\"><mo>&#247;</mo></math> 26 + 13 &times; 14<br>दिए गए निर्देश के अनुसार \'+\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' तथा \'&times;\' और \'-\' को आपस में बदलने के बाद<br>36 &times; 42 + 26 <math display=\"inline\"><mo>&#247;</mo></math> 13 - 14<br>1512 + 2 - 14 = 1500</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>14 - 15 &divide; 2 + 56 &times; 14 = ?</p>",
                    question_hi: "<p>10. यदि \'+\' और \' - \' को आपस में बदल दिया जाए तथा \'<math display=\"inline\"><mo>&#215;</mo></math>\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>14 - 15 &divide; 2 + 56 &times; 14 = ?</p>",
                    options_en: ["<p>40</p>", "<p>30</p>", 
                                "<p>35</p>", "<p>45</p>"],
                    options_hi: ["<p>40</p>", "<p>30</p>",
                                "<p>35</p>", "<p>45</p>"],
                    solution_en: "<p>10.(a)<strong> Given :-</strong> 14 - 15 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 56 &times; 14<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>14 + 15 &times; 2 - 56 <math display=\"inline\"><mo>&#247;</mo></math> 14<br>14 + 30 - 4<br>44 - 4 = 40</p>",
                    solution_hi: "<p>10.(a) <strong>दिया गया :- </strong>14 - 15 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 56 &times; 14<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>14 + 15 &times; 2 - 56 <math display=\"inline\"><mo>&#247;</mo></math> 14<br>14 + 30 - 4<br>44 - 4 = 40</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>15 <math display=\"inline\"><mo>&#247;</mo></math> 4 - 128 + 123 &times; 3 = ?</p>",
                    question_hi: "<p>11. यदि &lsquo;+&rsquo; और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ?<br>15 <math display=\"inline\"><mo>&#247;</mo></math> 4 - 128 + 123 &times; 3 = ?</p>",
                    options_en: ["<p>167</p>", "<p>127</p>", 
                                "<p>187</p>", "<p>147</p>"],
                    options_hi: ["<p>167</p>", "<p>127</p>",
                                "<p>187</p>", "<p>147</p>"],
                    solution_en: "<p>11.(d) <strong>Given :- </strong>15 <math display=\"inline\"><mo>&#247;</mo></math> 4 - 128 + 123 &times; 3 <br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>15 &times; 4 + 128 - 123 <math display=\"inline\"><mo>&#247;</mo></math> 3<br>60 + 128 - 41 <br>188 - 41 = 147</p>",
                    solution_hi: "<p>11.(d) <strong>दिया गया :-</strong> 15<math display=\"inline\"><mo>&#247;</mo></math> 4 - 128 + 123 &times; 3<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>15 &times; 4 + 128 - 123 <math display=\"inline\"><mo>&#247;</mo></math> 3<br>60 + 128 - 41 <br>188 - 41 = 147</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &rsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>16 <math display=\"inline\"><mo>&#247;</mo></math> 5 - 24 &times; 6 + 3 = ?</p>",
                    question_hi: "<p>12. यदि &lsquo;+&rsquo; और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा &rsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>16 <math display=\"inline\"><mo>&#247;</mo></math> 5 - 24 &times; 6 + 3 = ?</p>",
                    options_en: ["<p>85</p>", "<p>82</p>", 
                                "<p>81</p>", "<p>80</p>"],
                    options_hi: ["<p>85</p>", "<p>82</p>",
                                "<p>81</p>", "<p>80</p>"],
                    solution_en: "<p>12.(c) <strong>Given :- </strong>16 <math display=\"inline\"><mo>&#247;</mo></math> 5 - 24 &times; 6 + 3 <br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>16 &times; 5 + 24 <math display=\"inline\"><mo>&#247;</mo></math> 6 - 3<br>80 + 4 - 3 = 81</p>",
                    solution_hi: "<p>12.(c) <strong>दिया गया :- </strong>16 <math display=\"inline\"><mo>&#247;</mo></math> 5 - 24 &times; 6 + 3<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>16 &times; 5 + 24 <math display=\"inline\"><mo>&#247;</mo></math> 6 - 3<br>80 + 4 - 3 = 81</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. If \'A\' stands for \'&divide;\', \'B\' stands for \'&times;\', \"C\" stands for \'+\' and \'D\' stands for &lsquo;-&rsquo;, what will come in place of the question mark (?) in the following equation ?<br>14 B 11 D 22 C 328 A 8 = ?</p>",
                    question_hi: "<p>13. यदि \'A\' का अर्थ \'&divide;&rsquo;, \'B\' का अर्थ \'&times;\', \'C\' का अर्थ &lsquo;+&rsquo; और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>14 B 11 D 22 C 328 A 8 = ?</p>",
                    options_en: ["<p>173</p>", "<p>133</p>", 
                                "<p>163</p>", "<p>153</p>"],
                    options_hi: ["<p>173</p>", "<p>133</p>",
                                "<p>163</p>", "<p>153</p>"],
                    solution_en: "<p>13.(a) <strong>Given :-</strong> 14 B 11 D 22 C 328 A 8<br>As per given instruction after interchanging the letter with sign we get,<br>14 &times; 11 - 22 + 328 <math display=\"inline\"><mo>&#247;</mo></math> 8<br>154 - 22 + 41<br>154 + 19 = 173</p>",
                    solution_hi: "<p>13.(a) <strong>दिया गया :- </strong>14 B 11 D 22 C 328 A 8<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने के बाद हमें प्राप्त होता है,<br>14 &times; 11 - 22 + 328 <math display=\"inline\"><mo>&#247;</mo></math> 8<br>154 - 22 + 41<br>154 + 19 = 173</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. If &lsquo;A&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;+&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&ndash;&rsquo;, then the&nbsp;resultant of which of the following will be 218 ?</p>",
                    question_hi: "<p>14. यदि &lsquo;A&rsquo; का अर्थ &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; का अर्थ &lsquo;&times;&rsquo;, &lsquo;C&rsquo; का अर्थ &lsquo;+&rsquo; और &lsquo;D&rsquo; का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित में से&nbsp;किसका परिणाम 218 होगा ?</p>",
                    options_en: ["<p>32 A 8 D 160 B 2 C 42</p>", "<p>32 B 8 D 160 C 2 A 42</p>", 
                                "<p>32 C 8 D 160 A 2 B 42</p>", "<p>32 B 8 D 160 A 2 C 42</p>"],
                    options_hi: ["<p>32 A 8 D 160 B 2 C 42</p>", "<p>32 B 8 D 160 C 2 A 42</p>",
                                "<p>32 C 8 D 160 A 2 B 42</p>", "<p>32 B 8 D 160 A 2 C 42</p>"],
                    solution_en: "<p>14.(d) After going through all the options, option (d) satisfies. <br>32 B 8 D 160 A 2 C 42<br>As per given instruction after interchanging the letter with sign we get<br>32 &times; 8 - 160<math display=\"inline\"><mo>&#247;</mo></math>2 + 42<br>256 - 80 + 42<br>256 - 38 = 218</p>",
                    solution_hi: "<p>14.(d)<br>सभी विकल्पों की जांच करने पर विकल्प (d) संतुष्ट करता है।<br>32 B 8 D 160 A 2 C 42<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>32 &times; 8 - 160<math display=\"inline\"><mo>&#247;</mo></math>2 + 42<br>256 - 80 + 42<br>256 - 38 = 218</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which two numbers should be interchanged to make the given equation correct ?<br>15 &times; 5 &minus; 75 + (300 &divide; 6) + 3 &times; 19 = 115<br>(NOTE: Numbers must be interchanged and not the constituent digits e.g. if 2 and 3&nbsp;are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43&nbsp;&times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>15. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए ?<br>15 &times; 5 &minus; 75 + (300 &divide; 6) + 3 &times; 19 = 115<br>(<strong>ध्यान दें:</strong> संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए।&nbsp;उदा. यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43&nbsp;&times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: ["<p>6 and 5</p>", "<p>15 and 19</p>", 
                                "<p>6 and 3</p>", "<p>15 and 6</p>"],
                    options_hi: ["<p>6 और 5</p>", "<p>15 और 19</p>",
                                "<p>6 और 3</p>", "<p>15 और 6</p>"],
                    solution_en: "<p>15.(b) <strong>Given :-</strong> 15 &times; 5 - 75 + (300<math display=\"inline\"><mo>&#247;</mo></math>6) + 3 &times; 19 = 115<br>After going through all the options, option b satisfies. After interchanging 15 and 19 we get,<br>19 &times; 5 - 75 + (50) + 3 &times; 15<br>95 - 75 + 50 + 45<br>20 + 95 = 115<br>LH.S. = R.H.S.</p>",
                    solution_hi: "<p>15.(b) <strong>दिया गया :- </strong>15 &times; 5 - 75 + (300<math display=\"inline\"><mo>&#247;</mo></math>6) + 3 &times; 19 = 115<br>सभी विकल्पों की जांच करने पर विकल्प b संतुष्ट करता है। 15 और 19 को आपस में बदलने पर हमें प्राप्त होता है,<br>19 &times; 5 - 75 + (50) + 3 &times; 15<br>95 - 75 + 50 + 45<br>20 + 95 = 115<br>LH.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.05
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>