<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Two trains are running in opposite direction with the same speed. If the length of each train is 320 metres and they cross each other in 18 seconds, then what is the speed of each train?</p>",
                    question_hi: "<p>1. दो रेलगाड़ियाँ समान चाल से विपरीत दिशा में चल रही हैं। यदि प्रत्येक रेलगाड़ी की लंबाई 320 मीटर है और वे 18 सेकंड में एक दूसरे को पार करती हैं, तो प्रत्येक रेलगाड़ी की चाल कितनी है?</p>",
                    options_en: ["<p>48 km/hr</p>", "<p>64 km/hr</p>", 
                                "<p>72 km/hr</p>", "<p>56 km/hr</p>"],
                    options_hi: ["<p>48 km/hr</p>", "<p>64 km/hr</p>",
                                "<p>72 km/hr</p>", "<p>56 km/hr</p>"],
                    solution_en: "<p>1.(b)<br>Total length of both trains = 320 &times; 2 = 640 m<br>Let the speed of each train be x m/sec<br>Relative speed of train running in opposite direction = x + x = 2x<br>2x &times; 18 = 640<br>36x = 640<br>x = <math display=\"inline\"><mfrac><mrow><mn>640</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math>m/sec = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>640</mn><mn>36</mn></mfrac><mo>&#215;</mo><mfrac><mn>18</mn><mn>5</mn></mfrac></math>&nbsp;= 64 km/hr</p>",
                    solution_hi: "<p>1.(b)<br>दोनों ट्रेनों की कुल लंबाई = 320 &times; 2 = 640 m<br>माना प्रत्येक ट्रेन की गति x मीटर/सेकंड है<br>विपरीत दिशा में चलने वाली ट्रेन की सापेक्ष गति = x + x = 2x<br>2x &times; 18 = 640<br>36x = 640<br>x = <math display=\"inline\"><mfrac><mrow><mn>640</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math>m/sec = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>640</mn><mn>36</mn></mfrac><mo>&#215;</mo><mfrac><mn>18</mn><mn>5</mn></mfrac></math> = 64 km/hr</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A train of length 384 metres crosses an electric pole in 12 seconds and crosses another train of the same length travelling in opposite direction in 12 seconds. What is the speed of the second train ?</p>",
                    question_hi: "<p>2. 384 मीटर लंबी एक रेलगाड़ी एक बिजली के खंभे को 12 सेकंड में पार करती है और विपरीत दिशा में यात्रा कर रही समान लंबाई की दूसरी रेलगाड़ी को 12 सेकंड में पार करती है। दूसरी रेलगाड़ी की चाल कितनी है ?</p>",
                    options_en: ["<p>39 m/s</p>", "<p>45 m/s</p>", 
                                "<p>25 m/s</p>", "<p>32 m/s</p>"],
                    options_hi: ["<p>39 m/s</p>", "<p>45 m/s</p>",
                                "<p>25 m/s</p>", "<p>32 m/s</p>"],
                    solution_en: "<p>2.(d)<br>Speed of train = <math display=\"inline\"><mfrac><mrow><mn>384</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 32 m/sec<br>Total length of both trains = 384 &times; 2 = 768 m<br>Relative speed of train traveling in opposite direction = (32 + x) m/sec<br>(32 + <math display=\"inline\"><mi>x</mi></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>768</mn><mn>12</mn></mfrac></math> = 64<br><math display=\"inline\"><mi>x</mi></math> = 64 - 32 = 32 m/s<br>Hence, speed of second train (<math display=\"inline\"><mi>x</mi></math>) = 32 m/sec.</p>",
                    solution_hi: "<p>2.(d)<br>ट्रेन की गति = <math display=\"inline\"><mfrac><mrow><mn>384</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 32 मीटर/सेकंड<br>दोनों ट्रेनों की कुल लंबाई = 384 &times; 2 = 768 मीटर<br>विपरीत दिशा में चलने वाली ट्रेन की सापेक्ष गति = (32 + <math display=\"inline\"><mi>x</mi></math>) मीटर/सेकंड<br>(32 + <math display=\"inline\"><mi>x</mi></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>768</mn><mn>12</mn></mfrac></math> = 64<br><math display=\"inline\"><mi>x</mi></math> = 64 - 32 = 32 मीटर/सेकंड<br>अतः, दूसरी ट्रेन की गति (<math display=\"inline\"><mi>x</mi></math>) = 32 मीटर/सेकंड।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A person covers first 11 km in 60 minutes and next 22 km in 30 minutes. What is his average speed for the whole journey ?</p>",
                    question_hi: "<p>3. एक व्यक्ति पहले 11 km की दूरी 60 मिनट में और अगले 22 km की दूरी 30 मिनट में तय करता है। पूरी यात्रा के दौरान उसकी औसत चाल कितनी है?</p>",
                    options_en: ["<p>18 km/hr</p>", "<p>22 km/hr</p>", 
                                "<p>27 km/hr</p>", "<p>25 km/hr</p>"],
                    options_hi: ["<p>18 km/hr</p>", "<p>22 km/hr</p>",
                                "<p>27 km/hr</p>", "<p>25 km/hr</p>"],
                    solution_en: "<p>3.(b)<br>Average speed = <math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>22</mn></mrow><mrow><mn>1</mn><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle></mfrac></math> = 33 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>&nbsp;= 22 km/hr</p>",
                    solution_hi: "<p>3.(b)<br>औसत चाल =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>22</mn></mrow><mrow><mn>1</mn><mo>+</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle></mfrac></math> = 33 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> = 22 km/hr</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A bus left 60 minutes later than the scheduled time but in order to reach its destination 300 km away in time, it had to increase its usual speed by 10 km/hr. What is the usual speed of the bus ?</p>",
                    question_hi: "<p>4. एक बस अपने निर्धारित समय से 60 मिनट देरी से रवाना होती है, लेकिन 300 km दूर स्थित गंतव्य तक समय पर पहुंचने के लिए, उसकी सामान्य चाल में 10 km/hr की वृद्धि की जाती है। बस की सामान्य चाल कितनी है ?</p>",
                    options_en: ["<p>50 km/hr</p>", "<p>60 km/hr</p>", 
                                "<p>40 km/hr</p>", "<p>30 km/hr</p>"],
                    options_hi: ["<p>50 km/hr</p>", "<p>60 km/hr</p>",
                                "<p>40 km/hr</p>", "<p>30 km/hr</p>"],
                    solution_en: "<p>4.(a)<br>Let the usual speed of bus be <math display=\"inline\"><mi>x</mi></math> km/hr<br>ATQ,<br><math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mrow><mi>x</mi><mo>+</mo><mn>10</mn></mrow></mfrac></math> = 1<br>300 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>x</mi><mo>(</mo><mi>x</mi><mo>+</mo><mn>10</mn><mo>)</mo></mrow></mfrac></math> = 1<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ 10x = 3000<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ 10x - 3000 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 50 km/hr</p>",
                    solution_hi: "<p>4.(a)<br>माना बस की सामान्य गति <math display=\"inline\"><mi>x</mi></math> किमी/घंटा है<br>प्रश्नानुसार <br><math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mrow><mi>x</mi><mo>+</mo><mn>10</mn></mrow></mfrac></math> = 1<br>300 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>x</mi><mo>(</mo><mi>x</mi><mo>+</mo><mn>10</mn><mo>)</mo></mrow></mfrac></math> = 1<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ 10x = 3000<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ 10x - 3000 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 50 किमी/घंटा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A 600 metres long train crosses a man walking in opposite direction in 20 seconds. If the speed of train is 5 times to the speed of man, then what is the speed of train?</p>",
                    question_hi: "<p>5. 600 मीटर लंबी एक रेलगाड़ी, विपरीत दिशा में चल रहे एक व्यक्ति को 20 सेकंड में पार करती है। यदि रेलगाड़ी की चाल, व्यक्ति की चाल की 5 गुना है, तो रेलगाड़ी की चाल कितनी है?</p>",
                    options_en: ["<p>90 km/hr</p>", "<p>108 km/hr</p>", 
                                "<p>54 km/hr</p>", "<p>72 km/hr</p>"],
                    options_hi: ["<p>90 किलोमीटर प्रति घंटा</p>", "<p>108 किलोमीटर प्रति घंटा</p>",
                                "<p>54 किलोमीटर प्रति घंटा</p>", "<p>72 किलोमीटर प्रति घंटा</p>"],
                    solution_en: "<p>5.(a)<br>Let the speed of man and train be <math display=\"inline\"><mi>x</mi></math> and 5x respectively<br>Relative speed when traveling opposite to each other = <math display=\"inline\"><mi>x</mi></math> + 5x = 6x<br>According to the question,<br>6<math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>20</mn></mfrac></math> = 30 &rArr; x = 5<br>Then, Speed of train = 5<math display=\"inline\"><mi>x</mi></math> = 5 &times; 5 = 25 m/sec = 25 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math>&nbsp;= 90 km/hr</p>",
                    solution_hi: "<p>5.(a)<br>माना व्यक्ति और ट्रेन की गति क्रमशः <math display=\"inline\"><mi>x</mi></math> और 5x है<br>एक दूसरे के विपरीत यात्रा करते समय सापेक्ष गति = <math display=\"inline\"><mi>x</mi></math>+5x = 6x<br>प्रश्न के अनुसार,<br>6<math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>20</mn></mfrac></math> = 30 &rArr; x = 5<br>फिर, ट्रेन की गति = 5<math display=\"inline\"><mi>x</mi></math> = 5 &times; 5 = 25 मीटर/सेकंड = 25 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math> = 90 किमी/घंटा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. X starts from point P at a speed of 12 km/hr and 6 hours after his start, Y also starts from the same point P on his bicycle at a speed of 16 km/hr. At what distance will Y meet X from the starting point P ?</p>",
                    question_hi: "<p>6. X, बिंदु P से, 12 km/hr की चाल से चलना शुरू करता है और उसकी शुरुआत के 6 घंटे बाद, Y भी उसी बिंदु P से उसके पीछे अपनी साइकिल पर 16 km/hr की चाल से चलना शुरू करता है। Y, X को प्रारंभिक बिंदु P से कितनी दूरी पर मिलेगा ?</p>",
                    options_en: ["<p>256 km</p>", "<p>324 km</p>", 
                                "<p>288 km</p>", "<p>224 km</p>"],
                    options_hi: ["<p>256 km</p>", "<p>324 km</p>",
                                "<p>288 km</p>", "<p>224 km</p>"],
                    solution_en: "<p>6.(c) According to question,<br>Ratio of speed of <math display=\"inline\"><mi>x</mi></math> and y = 12 : 16 or 3 : 4<br>Ratio of time of <math display=\"inline\"><mi>x</mi></math> and y = 4 : 3 <br><math display=\"inline\"><mo>&#8658;</mo></math> 1 unit = 6 hours <br><math display=\"inline\"><mo>&#8658;</mo></math> 3 unit = 18 hours <br>So, Required distance = 16 &times; 18 = 288 km</p>",
                    solution_hi: "<p>6.(c) प्रश्न के अनुसार,<br><math display=\"inline\"><mi>x</mi></math> और y की गति का अनुपात = 12 : 16 या 3 : 4<br><math display=\"inline\"><mi>x</mi></math> और y के समय का अनुपात = 4 : 3<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 इकाई = 6 घंटे<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 इकाई = 18 घंटे<br>अतः, आवश्यक दूरी = 16 &times; 18 = 288 km</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Two boys are standing on the opposite ends of a 900 metres long bridge. If they walk towards each other at the speed of 10 metres/minute and 8 metres/minute respectively, then in how much time will they meet each other?</p>",
                    question_hi: "<p>7. दो लड़के एक 900 मीटर लंबे पुल के विपरीत छोरों पर खड़े हैं। यदि वे क्रमशः 10 मीटर/मिनट और 8 मीटर/मिनट की चाल से एक-दूसरे की ओर चलते हैं, तो वे एक-दूसरे से कितने समय में मिलेंगे?</p>",
                    options_en: ["<p>25 minutes</p>", "<p>50 minutes</p>", 
                                "<p>62 minutes</p>", "<p>38 minutes</p>"],
                    options_hi: ["<p>25 मिनट</p>", "<p>50 मिनट</p>",
                                "<p>62 मिनट</p>", "<p>38 मिनट</p>"],
                    solution_en: "<p>7.(b)<br>Relative speed when two boys are approaching each other = 10 + 8 = 18 m/min<br>Required time taken to meet each other = <math display=\"inline\"><mfrac><mrow><mn>900</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 50 minutes</p>",
                    solution_hi: "<p>7.(b)<br>जब दो लड़के एक दूसरे की ओर चल रहे हों तो सापेक्ष गति = 10 + 8 = 18 मीटर/मिनट<br>एक दूसरे से मिलने में लगा आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mn>00</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 50 मिनट</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Two cars start from the same point and travel in opposite directions. If one car travels at 40 km/h and the other at 50 km/h, after 5 hours, how far apart are they?</p>",
                    question_hi: "<p>8. दो कारें एक ही बिंदु से चलना शुरू करती हैं, और एक-दूसरे से विपरीत दिशाओं में यात्रा करती हैं। यदि एक कार 40 km/h और दूसरी कार 50 km/h की चाल से चलती है, तो 5 घंटे बाद, वे दोनों एक दूसरे से कितनी दूरी पर होगी?</p>",
                    options_en: ["<p>450 km</p>", "<p>480 km</p>", 
                                "<p>410 km</p>", "<p>460 km</p>"],
                    options_hi: ["<p>450 km</p>", "<p>480 km</p>",
                                "<p>410 km</p>", "<p>460 km</p>"],
                    solution_en: "<p>8.(a)<br>Relative speed, when two cars travel in opposite directions = 40 + 50 = 90 km/hr<br>Distance between two cars = 90 &times; 5 = 450 km</p>",
                    solution_hi: "<p>8.(a)<br>सापेक्ष गति, जब दो कारें विपरीत दिशाओं में चलती हैं = 40 + 50 = 90 किमी/घंटा<br>दो कारों के बीच की दूरी = 90 &times; 5 = 450 किमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. If a car travels 150 km at a speed of 50 km/h, how long does it take to complete the journey?</p>",
                    question_hi: "<p>9. यदि एक कार 50 km/h की चाल से 150 km की यात्रा करती है, तो उसे यात्रा पूरी करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>3 hours</p>", "<p>3.5 hours</p>", 
                                "<p>4.5 hours</p>", "<p>4 hours</p>"],
                    options_hi: ["<p>3 घंटे</p>", "<p>3.5 घंटे</p>",
                                "<p>4.5 घंटे</p>", "<p>4 घंटे</p>"],
                    solution_en: "<p>9.(a)<br>Time taken by car to complete the journey = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> = 3 hrs</p>",
                    solution_hi: "<p>9.(a)<br>यात्रा पूरी करने में कार द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> = 3 घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A car left 42 minutes early than the scheduled time but in order to reach its destination 210 km away in time, it had to slow its usual speed by 10 km/hr. What is the usual speed of the car?</p>",
                    question_hi: "<p>10. एक कार ने अपने निर्धारित समय से 42 मिनट पहले चलना शुरू किया, लेकिन 210 km दूर अपने गंतव्य तक समय पर पहुंचने के लिए उसे अपनी सामान्य चाल 10 km/hr धीमी करनी पड़ी। कार की सामान्य चाल कितनी है?</p>",
                    options_en: ["<p>50 km/hr</p>", "<p>60 km/hr</p>", 
                                "<p>40 km/hr</p>", "<p>30 km/hr</p>"],
                    options_hi: ["<p>50 km/hr</p>", "<p>60 km/hr</p>",
                                "<p>40 km/hr</p>", "<p>30 km/hr</p>"],
                    solution_en: "<p>10.(b)<br>Let the usual speed of the car be &lsquo;S&rsquo;<br><math display=\"inline\"><mfrac><mrow><mn>210</mn></mrow><mrow><mi>S</mi><mo>-</mo><mn>10</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>210</mn><mi>S</mi></mfrac><mo>=</mo><mfrac><mn>42</mn><mn>60</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math><br>210 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>S</mi><mo>(</mo><mi>S</mi><mo>-</mo><mn>10</mn><mo>)</mo></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math>.<br>S(S - 10) = 3000<br><math display=\"inline\"><msup><mrow><mi>S</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 10S - 3000 = 0<br><math display=\"inline\"><msup><mrow><mi>S</mi></mrow><mrow><mn>2</mn></mrow></msup></math>- 60S + 50S - 3000 = 0<br>S(S - 60) + 50(S - 60) = 0<br>(S - 60) (S + 50) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> S = 60 km/hr or -50 km/hr (speed can\'t be - ve)<br>hence, usual speed of the car = 60 km/h</p>",
                    solution_hi: "<p>10.(b)<br>माना कार की सामान्य गति \'S\' है<br><math display=\"inline\"><mfrac><mrow><mn>210</mn></mrow><mrow><mi>S</mi><mo>-</mo><mn>10</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>210</mn><mi>S</mi></mfrac><mo>=</mo><mfrac><mn>42</mn><mn>60</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math><br>210 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>S</mi><mo>(</mo><mi>S</mi><mo>-</mo><mn>10</mn><mo>)</mo></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math>.<br>S(S - 10) = 3000<br><math display=\"inline\"><msup><mrow><mi>S</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 10S - 3000 = 0<br><math display=\"inline\"><msup><mrow><mi>S</mi></mrow><mrow><mn>2</mn></mrow></msup></math>- 60S + 50S - 3000 = 0<br>S(S - 60) + 50(S - 60) = 0<br>(S - 60) (S + 50) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> S = 60 km/hr या - 50 km/hr (गति ऋणात्मक नही हो सकती है)<br>अत: कार की सामान्य गति = 60 किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A person walks at a speed of 6 km/h and covers a certain distance in 4 hours. If the person increases the speed to 8 km/h, how long will it take to cover the same distance?</p>",
                    question_hi: "<p>11. एक व्यक्ति 6 किलोमीटर प्रति घंटे की चाल से चलता है और 4 घंटे में एक निश्चित दूरी तय करता है। यदि वह व्यक्ति अपनी चाल को बढ़ाकर 8 किलोमीटर प्रति घंटा कर दे, तो उसे समान दूरी तय करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>2 hours</p>", "<p>5 hours</p>", 
                                "<p>3 hours</p>", "<p>4.5hours</p>"],
                    options_hi: ["<p>2 घंटे</p>", "<p>5 घंटे</p>",
                                "<p>3घंटे</p>", "<p>4.5 घंटे</p>"],
                    solution_en: "<p>11.(c)<br>Speed &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>T</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac></math> (when distance is constant)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Old&nbsp; &nbsp; &nbsp;New<br>Speed&nbsp; &nbsp;6&nbsp; &nbsp;:&nbsp; &nbsp; 8 = 3 : 4 <br>Time&nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp;:&nbsp; &nbsp; 3<br>4 unit = 4 hrs<br>Then, 3 unit = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 3 = 3 hrs</p>",
                    solution_hi: "<p>11.(c)<br>गति &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi><mi>&#160;</mi></mrow></mfrac></math> (जब दूरी स्थिर हो)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;पुराना&nbsp; &nbsp;नया<br>गति&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;6&nbsp; &nbsp; :&nbsp; &nbsp;8 = 3 : 4 <br>समय&nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; :&nbsp; &nbsp; 3<br>4 इकाई = 4 घंटे<br>तब , 3 इकाई = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mi>&#160;</mi></math>&times; 3 = 3 घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Two trains, one 390 metres and the other 250 metres long are running in opposite directions on parallel tracks, at the speed of 78 km/hr and 66 km/hr respectively. How much time will they take to cross each other?</p>",
                    question_hi: "<p>12. दो रेलगाड़ियां, एक 390 मीटर और दूसरी 250 मीटर लंबी, समांतर पटरियों पर क्रमशः 78 km/hr और 66 km/hr की चाल से विपरीत दिशाओं में चल रही है। उन्हें एक-दूसरे को पार करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>36 seconds</p>", "<p>16 seconds</p>", 
                                "<p>24 seconds</p>", "<p>30 seconds</p>"],
                    options_hi: ["<p>36 सेकंड</p>", "<p>16 सेकंड</p>",
                                "<p>24 सेकंड</p>", "<p>30 सेकंड</p>"],
                    solution_en: "<p>12.(b)<br>Relative speed, when running in opposite directions = 78 + 66 = 144 km/hr = 144 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 40 m/sec<br>Time taken to cross each other = <math display=\"inline\"><mfrac><mrow><mn>390</mn><mo>+</mo><mn>250</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>640</mn><mn>40</mn></mfrac></math> = 16 sec</p>",
                    solution_hi: "<p>12.(b)<br>विपरीत दिशाओं में चलने पर सापेक्ष गति = 78 + 66 = 144 किमी/घंटा = 144 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 40 मीटर/सेकंड<br>एक दूसरे को पार करने में लगा समय= <math display=\"inline\"><mfrac><mrow><mn>390</mn><mo>+</mo><mn>250</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>640</mn><mn>40</mn></mfrac></math> = 16 सेकंड</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Rohit makes four trips of equal distances. His speed on first trip was 400 km/hr and in each subsequent trip his speed was half of the previous trip. What is the average speed of Rohit in these four trips ?</p>",
                    question_hi: "<p>13. रोहित समान दूरियों की चार यात्राएं करता है। पहली यात्रा में उसकी चाल 400 km/hr थी और प्रत्येक अगली यात्रा में उसकी चाल पिछली यात्रा की चाल से आधी थी। इन चार यात्राओं में रोहित की औसत चाल कितनी है ?</p>",
                    options_en: ["<p>100 km/hr</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>440</mn><mn>3</mn></mfrac></math>km/h</p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mn>3</mn></mfrac></math>km/h</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>320</mn><mn>3</mn></mfrac></math>km/h</p>"],
                    options_hi: ["<p>100 km/hr</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>440</mn><mn>3</mn></mfrac></math>km/h</p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mn>3</mn></mfrac></math>km/h</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>320</mn><mn>3</mn></mfrac></math>km/h</p>"],
                    solution_en: "<p>13.(d)<br>Speed during four trips are 400, 200, 100, 50 km/hr respectively<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731491823159.png\" alt=\"rId4\" width=\"286\" height=\"130\"><br>So, required average speed of Rohit = <math display=\"inline\"><mfrac><mrow><mn>400</mn><mo>&#215;</mo><mn>4</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1600</mn><mn>15</mn></mfrac><mo>=</mo><mfrac><mn>320</mn><mn>3</mn></mfrac></math>km/h</p>",
                    solution_hi: "<p>13.(d)<br>चार यात्राओं के दौरान गति क्रमश: 400, 200, 100, 50 km/hr है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731491823478.png\" alt=\"rId5\" width=\"300\" height=\"127\"><br>तो, रोहित की आवश्यक औसत गति = <math display=\"inline\"><mfrac><mrow><mn>400</mn><mo>&#215;</mo><mn>4</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1600</mn><mn>15</mn></mfrac><mo>=</mo><mfrac><mn>320</mn><mn>3</mn></mfrac></math>km/h</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Two trains are running in opposite direction with the same speed. If the length of each train is 160 metres and they cross each other in 32 seconds, then what is the speed of each train ?</p>",
                    question_hi: "<p>14. दो रेलगाड़ियां समान चाल से विपरीत दिशा में चल रही हैं। यदि प्रत्येक रेलगाड़ी की लंबाई 160 मीटर है और वे एक दूसरे को 32 सेकंड में पार करती हैं, तो प्रत्येक रेलगाड़ी की चाल कितनी है?</p>",
                    options_en: ["<p>72 km/hr</p>", "<p>36 km/hr</p>", 
                                "<p>18 km/hr</p>", "<p>45 km/hr</p>"],
                    options_hi: ["<p>72 km/hr</p>", "<p>36 km/hr</p>",
                                "<p>18 km/hr</p>", "<p>45 km/hr</p>"],
                    solution_en: "<p>14.(c)<br>Let the speed of each train be &lsquo;<math display=\"inline\"><mi>x</mi></math>&rsquo; m<br>Relative speed of trains, running in opposite direction = <math display=\"inline\"><mi>x</mi></math> + x = (2x) m/sec.<br>Total length of trains = 160 &times; 2 = 320 m<br>Distance = Speed &times; Time<br>320 = 2<math display=\"inline\"><mi>x</mi></math> &times; 32 = 64x<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>320</mn><mn>64</mn></mfrac></math> = 5 meter<br>Speed of each train = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 18 km/hr</p>",
                    solution_hi: "<p>14.(c)<br>माना प्रत्येक ट्रेन की गति \'<math display=\"inline\"><mi>x</mi></math>\' मीटर है<br>विपरीत दिशा में चलने वाली ट्रेनों की सापेक्ष गति = <math display=\"inline\"><mi>x</mi></math> + x = (2x) मी/से.<br>ट्रेनों की कुल लंबाई = 160 &times; 2 = 320 मीटर<br>दूरी = गति &times; समय<br>320 = 2<math display=\"inline\"><mi>x</mi></math> &times; 32 = 64x<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>320</mn><mn>64</mn></mfrac></math>&nbsp;= 5 मीटर<br>प्रत्येक ट्रेन की गति = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 18 किमी/घंटा</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Aman goes to his office by car at the speed of 80 km/hr and reaches 10 minutes earlier. If he goes at the speed 30 km/hr. then he reaches 10 minutes late. What will be the speed of the car to reach on time?</p>",
                    question_hi: "<p>15. अमन कार से 80 km/hr की चाल से अपने कार्यालय जाता है और 10 मिनट पहले पहुंचता है। यदि वह 30 km/hr की चाल से जाता है, तो वह 10 मिनट देरी से पहुंचता है। समय पर कार्यालय पहुंचने के लिए कार की चाल कितनी होनी चाहिए?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> km/hr</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> km/hr</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>450</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> km/hr</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> km/hr</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> km/hr</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> km/hr</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>450</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> km/hr</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>480</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> km/hr</p>"],
                    solution_en: "<p>15.(a) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Original&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;New <br>Speed&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 80&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 30 = 8 : 3<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;8<br>8 - 3 = 5 unit ---------- 20 min<br>3 unit ------------- <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; 3 = 12 min<br>Distance covered by Aman = 80 &times; <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 16 km<br>Time taken by Aman to reach the office on time = 12 + 10 = 22 min<br>Required speed of Aman = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mfrac><mrow><mn>22</mn></mrow><mrow><mn>60</mn></mrow></mfrac></mrow></mfrac></math> = 16 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>22</mn></mfrac><mo>=</mo><mfrac><mn>480</mn><mn>11</mn></mfrac></math> km/hr</p>",
                    solution_hi: "<p>15.(a) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;मूल&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;नया <br>गति&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;80&nbsp; &nbsp; :&nbsp; &nbsp; 30 = 8 : 3<br>समय&nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;8<br>8 - 3 = 5 इकाई ---------- 20 मिनट<br>3 इकाई ------------- <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times; 3 = 12 मिनट<br>अमन द्वारा तय की गई दूरी = 80 &times; <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 16 किमी<br>अमन को समय पर कार्यालय पहुंचने में लगा समय = 12 + 10 = 22 मिनट<br>अमन की आवश्यक गति&nbsp;= <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mfrac><mrow><mn>22</mn></mrow><mrow><mn>60</mn></mrow></mfrac></mrow></mfrac></math> = 16 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>22</mn></mfrac><mo>=</mo><mfrac><mn>480</mn><mn>11</mn></mfrac></math> किमी/घंटा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>