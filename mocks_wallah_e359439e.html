<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. In what time will ₹10,000 at 4% per annum, produce the same interest as ₹8,000 does in 4 years at 5% simple interest?</p>",
                    question_hi: "<p>1. कितने समय में 4% वार्षिक दर से ₹10,000 पर उतना ही ब्याज प्राप्त होगा, जितना 5% साधारण ब्याज दर पर 4 वर्ष में ₹8,000 पर प्राप्त होता है?</p>",
                    options_en: ["<p>5 years</p>", "<p>3 years</p>", 
                                "<p>4 years</p>", "<p>6 years</p>"],
                    options_hi: ["<p>5 वर्ष</p>", "<p>3 वर्ष</p>",
                                "<p>4 वर्ष</p>", "<p>6 वर्ष</p>"],
                    solution_en: "<p>1.(c)&nbsp;According to the question , <br><math display=\"inline\"><mfrac><mrow><mn>10000</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8000</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math><br>T = <math display=\"inline\"><mfrac><mrow><mn>8000</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> = 4 years</p>",
                    solution_hi: "<p>1.(c)&nbsp;प्रश्न के अनुसार , <br><math display=\"inline\"><mfrac><mrow><mn>10000</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8000</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math><br>T = <math display=\"inline\"><mfrac><mrow><mn>8000</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> = 4 वर्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "2. If the simple interest at the same interest rate on ₹500 for 4 years and ₹700 for 2 years, combined together, is ₹280, then what is the rate of interest? ",
                    question_hi: "<p>2. यदि समान ब्याज दर पर 500 रूपये का 4 वर्ष और 700 रूपये का 2 वर्ष का साधारण ब्याज मिलाकर 280 रूपये है, तो व्याज की दर कितनी होगी?</p>",
                    options_en: [" 9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%", " 6<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%", 
                                " 8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%", "<p>7<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>6<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>",
                                "<p>8<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>", "<p>7<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>2.(c)<br>Simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math><br>According to question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>700</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 280<br>Rate of interest = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>17</mn></mfrac></math> %</p>",
                    solution_hi: "<p>2.(c)<br>साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>500</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>700</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 280<br>ब्याज दर = <math display=\"inline\"><mfrac><mrow><mn>280</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>17</mn></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3.  A man invests a total sum of ₹10,000 in a company. A part of the sum was invested at 10% simple interest per annum and the remaining part at 15% simple interest per annum. If the total interest accrued to him in two years equals ₹2,400, the sum invested at 15% simple interest per annum is:",
                    question_hi: "<p>3. एक व्यक्ति किसी कंपनी में कुल ₹10,000 की राशि निवेश करता है। राशि का एक हिस्सा 10% वार्षिक साधारण ब्याज पर और शेष भाग 15% वार्षिक साधारण ब्याज पर निवेश किया जाता है। यदि उसे दो वर्षों में अर्जित कुल ब्याज ₹2,400 है, तो 15% वार्षिक साधारण ब्याज पर निवेश की गई राशि है:</p>",
                    options_en: [" ₹8,000", " ₹4,000", 
                                " ₹6,000 ", "<p>₹5,000</p>"],
                    options_hi: ["<p>₹8,000</p>", "<p>₹4,000</p>",
                                "<p>₹6,000</p>", "<p>₹5,000</p>"],
                    solution_en: "<p>3.(b)&nbsp;Let , the sum invested at 10% simple interest be x&nbsp;₹ and <br>sum invested at 15% simple interest be 10000 -&nbsp;x ₹<br>According to thee question , <br><math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10000</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 2400 ₹<br>20<math display=\"inline\"><mi>x</mi></math> + 300000 - 30x = 240000<br>10<math display=\"inline\"><mi>x</mi></math> = 60000 &rArr; x = 6000 ₹<br>Now , Sum invested at 15% S.I. = 4000 ₹<br><strong>Short Trick:</strong><br>Interest in one year = <math display=\"inline\"><mfrac><mrow><mn>2400</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 1200₹<br>Rate of interest per annum = <math display=\"inline\"><mfrac><mrow><mn>1200</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> &times; 100 = 12%<br>Now,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723628455047.png\" alt=\"rId4\" width=\"200\"><br>Total sum (3 + 2) units = 10000 ₹<br>So , sum invested at 15% (2 units) = <math display=\"inline\"><mfrac><mrow><mn>10000</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 2 = 4000 ₹</p>",
                    solution_hi: "<p>3.(b)&nbsp;माना, 10% साधारण ब्याज पर निवेश की गई राशि x&nbsp;₹ है और<br>15% साधारण ब्याज पर निवेश की गई राशि 10000 - x&nbsp;₹ होगी<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10000</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 2400 ₹<br>20x&nbsp;+ 300000 - 30x = 240000<br>10<math display=\"inline\"><mi>x</mi></math> = 60000 &rArr; x = 6000 ₹<br>अब, 15% साधारण ब्याज पर निवेश की गई राशि = 4000 ₹<br><strong>Short Trick:</strong><br>एक साल में ब्याज = <math display=\"inline\"><mfrac><mrow><mn>2400</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 1200₹<br>प्रति वर्ष ब्याज दर = <math display=\"inline\"><mfrac><mrow><mn>1200</mn></mrow><mrow><mn>10000</mn></mrow></mfrac></math> &times; 100 = 12%<br>अब,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1723628455047.png\" alt=\"rId4\" width=\"200\"><br>कुल राशि (3 + 2) इकाई = 10000 ₹<br>तो , 15% पर निवेश की गई राशि (2 इकाई) = <math display=\"inline\"><mfrac><mrow><mn>10000</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 2 = 4000 ₹</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If ₹72 amounts to ₹104.4 in 3 years, what will ₹120 amount to in 5 years at the same rate percent per annum?</p>",
                    question_hi: "<p>4. यदि ₹72 की राशि पर 3 वर्ष में प्राप्त मिश्रधन ₹104.4 है, तो ₹120 की राशि पर समान प्रतिशत वार्षिक दर से 5 वर्ष में प्राप्त मिश्रधन कितना होगा?</p>",
                    options_en: ["<p>₹450</p>", "<p>₹330</p>", 
                                "<p>₹210</p>", "<p>₹215</p>"],
                    options_hi: ["<p>₹450</p>", "<p>₹330</p>",
                                "<p>₹210</p>", "<p>₹215</p>"],
                    solution_en: "<p>4.(c)<br>Simple interest in one years = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>104</mn><mo>.</mo><mn>4</mn><mo>-</mo><mn>72</mn></mrow><mn>3</mn></mfrac></math> = 10.8 ₹<br>Rate % = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>72</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 15%<br>Now, Amount = principal + SI<br>So, ₹120 becomes in 5 year = 120 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = ₹210</p>",
                    solution_hi: "<p>4.(c)<br>एक वर्ष में साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>104</mn><mo>.</mo><mn>4</mn><mo>-</mo><mn>72</mn></mrow><mn>3</mn></mfrac></math> = 10.8 ₹<br>दर = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>72</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 15%<br>अब, मिश्रधन = मूलधन + साधारण ब्याज<br>तो, ₹120 पर 5 वर्ष में प्राप्त मिश्रधन = 120 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mn>15</mn><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= ₹ 210</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. A person deposited ₹500 for 3 years, ₹650 for 5 years, and ₹1,250 for 7 years. He received a total simple interest of ₹1,620. The rate of interest per annum is: ",
                    question_hi: "<p>5. एक व्यक्ति ने 3 साल के लिए ₹500, 5 साल के लिए ₹650 और 7 साल के लिए ₹1,250 जमा किए। उसे कुल ₹1,620 का साधारण ब्याज प्राप्त हुआ। वार्षिक व्याज दर ज्ञात करें।</p>",
                    options_en: [" 12%", " 13%", 
                                " 10.8%", " 11%"],
                    options_hi: ["<p>12%</p>", "<p>13%</p>",
                                "<p>10.8%</p>", "<p>11%</p>"],
                    solution_en: "<p>5.(a)<br>S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>principal</mi><mo>&#215;</mo><mi>rate</mi><mo>&#215;</mo><mi>time</mi></mrow><mn>100</mn></mfrac></math><br>1620 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">r</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn></mrow><mn>100</mn></mfrac><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>650</mn><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>1250</mn><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac></mstyle></math>&nbsp;<br>1620 = 15<math display=\"inline\"><mi>r</mi></math> + 32.5r + 87.5r<br><math display=\"inline\"><mi>r</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1620</mn><mn>135</mn></mfrac></math>= 12%</p>",
                    solution_hi: "<p>5.(a)<br>साधारण व्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>1620 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">r</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn></mrow><mn>100</mn></mfrac><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>650</mn><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>1250</mn><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac></mstyle></math><br>1620 = 15<math display=\"inline\"><mi>r</mi></math> + 32.5r + 87.5r<br><math display=\"inline\"><mi>r</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1620</mn><mn>135</mn></mfrac></math> = 12%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "6. A sum of money invested at a certain rate of simple interest per annum amounts to ₹14,522 in seven years and to ₹18,906 in eleven years. Find the sum invested (in ₹).",
                    question_hi: "<p>6. यदि वार्षिक साधारण ब्याज की एक निश्चित दर पर निवेश की गई राशि पर सात वर्षों में प्राप्त मिश्रधन ₹14,522 और ग्यारह वर्षों में प्राप्त मिश्रधन ₹18,906 है, तो निवेश की गई राशि (₹ में) ज्ञात कीजिए।</p>",
                    options_en: [" 6850", " 6900  ", 
                                "<p>6800</p>", "<p>6750 </p>"],
                    options_hi: ["<p>6850</p>", "<p>6900</p>",
                                "<p>6800</p>", "<p>6750</p>"],
                    solution_en: "<p>6.(a)<br>Simple Interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math><br>According to question,<br>(18906 -&nbsp;14522 ) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>11</mn></mrow><mn>100</mn></mfrac><mo>)</mo><mo>-</mo><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac><mo>)</mo></math><br>4384 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac><mo>)</mo></math> &rArr; PR = 109600<br>Now, (14522 -&nbsp;P) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mn>109600</mn><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac><mo>)</mo></math><br>Principal = 6850 Rs.</p>",
                    solution_hi: "<p>6.(a)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math><br>प्रश्न के अनुसार,<br>(18906 -&nbsp;P) - (14522 - P) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>11</mn></mrow><mn>100</mn></mfrac><mo>)</mo><mo>-</mo><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac><mo>)</mo></math><br>4384 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac><mo>)</mo></math> &rArr; PR = 109600<br>अब, (14522 -&nbsp;P) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mrow><mn>109600</mn><mo>&#215;</mo><mn>7</mn></mrow><mn>100</mn></mfrac><mo>)</mo></math><br>मूलधन = 6850 रुपये।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "7.A person took a loan at 5% per annum simple interest during the first year and with an increase of 0.5% simple interest every year from the second year onwards. After 4 years, he paid ₹4,600 as a total interest to settle the loan completely. How much was the loan?",
                    question_hi: "7. एक व्यक्ति ने पहले वर्ष में 5% की वार्षिक साधारण ब्याज पर ऋण लिया जिसमें दूसरे वर्ष से आगे के प्रत्येक वर्ष में 0.5% के साधारण ब्याज की वृद्धि की जाती है। 4 वर्ष बाद उसने ऋण को पूरी तरह से चुकाने के लिए ब्याज के रूप में कुल ₹4,600 का भुगतान किया। कुल ऋण कितना था ?",
                    options_en: [" ₹20,000", " ₹19,000", 
                                " ₹18,000", " ₹21,000"],
                    options_hi: [" ₹20,000", "<p>₹19,000</p>",
                                "<p>₹18,000</p>", "<p>₹21,000</p>"],
                    solution_en: "<p>7.(a)&nbsp;Total interest in 4 years = 5 + 5.5 + 6 + 6.5 = 23%<br>Let total loan = 100 unit<br>Then, total amount after 4 years = 123 unit<br>According to the question,<br>23 unit = Rs. 4600 <math display=\"inline\"><mo>&#8658;</mo></math> 1 unit = Rs. 200<br>So, loan = 200 &times; 100 = Rs. 20,000</p>",
                    solution_hi: "<p>7.(a)&nbsp;4 वर्षों में कुल ब्याज = 5 + 5.5 + 6 + 6.5 = 23%<br>माना , कुल ऋण = 100 इकाई<br>तो, 4 साल बाद कुल राशि = 123 यूनिट<br>प्रश्न के अनुसार,<br>23 यूनिट = रु. 4600 <math display=\"inline\"><mo>&#8658;</mo></math> 1 यूनिट = रु. 200<br>तो, ऋण = 200 &times; 100 = रु. 20,000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Money was lent on simple interest. After 12 years, its simple interest becomes<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of&nbsp;the money. Find the rate of interest.</p>",
                    question_hi: "<p>8. साधारण ब्याज पर पैसा उधार दिया जाता था। 12 वर्ष बाद इसका साधारण ब्याज <math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> हो जाता है&nbsp;धन। ब्याज दर ज्ञात कीजिये.</p>",
                    options_en: [" 4% p.a.", " 2% p.a.", 
                                " 5% p.a.", "<p>3% p.a.</p>"],
                    options_hi: ["<p>4% प्रति वर्ष</p>", "<p>2% प्रति वर्ष</p>",
                                "<p>5% प्रति वर्ष</p>", "<p>3% प्रति वर्ष</p>"],
                    solution_en: "<p>8.(c)&nbsp;According to the question,<br>Let the Principal = 5x&nbsp;, and S.I. in 12 years = 3x<br>Rate = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = 5%</p>",
                    solution_hi: "<p>8.(c)&nbsp;प्रश्न के अनुसार,<br>माना मूलधन = 5x&nbsp;और साधारण ब्याज 12 साल में = 3x<br>दर= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = 5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "9. If the simple interest for 5 years is equal to 25% of the principal, then the interest will be equal to the principal after ______years.",
                    question_hi: "<p>9. यदि 5 वर्ष का साधारण ब्याज मूलधन के 25% के बराबर है, तो कितने वर्षों बाद ब्याज मूलधन के बराबर होगा?</p>",
                    options_en: [" 20 ", " 30", 
                                " 25", "<p>22</p>"],
                    options_hi: ["<p>20</p>", "<p>30</p>",
                                "<p>25</p>", "<p>22</p>"],
                    solution_en: "<p>9.(a)&nbsp;Let the principal be 100 unit<br>According to question,<br>Interest in 5 years = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 25 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>25</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 20 years</p>",
                    solution_hi: "<p>9.(a)&nbsp;माना, मूलधन = 100 इकाई <br>प्रश्न के अनुसार,<br>5 वर्ष में ब्याज = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 25 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>25</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 20 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10. In how many years will a sum of ₹9,500 amount to ₹11,780 at the rate of 8% per annum at simple interest? ",
                    question_hi: "<p>10. ₹9,500 की राशि पर साधारण ब्याज की 8% वार्षिक दर से कितने वर्षों में प्राप्त मिश्रधन ₹11,780 होगा ?</p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>5</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>4</p>", "<p>5</p>"],
                    solution_en: "<p>10.(a)&nbsp;According to the question,<br>Simple interest = 11,780 -&nbsp;9,500 = ₹2,280<br>2280 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9500</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br>Time (<math display=\"inline\"><mi>t</mi></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2280</mn><mrow><mn>95</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = 3 years</p>",
                    solution_hi: "<p>10.(a)&nbsp;प्रश्न के अनुसार,<br>साधारण ब्याज = 11,780 -&nbsp;9,500 = ₹2,280<br>2280 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9500</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br>समय (<math display=\"inline\"><mi>t</mi></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2280</mn><mrow><mn>95</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = 3 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. An amount becomes double in 8 years on simple interest. In how many years would Rs. 25,000 become Rs. 1,00,000 with the same rate of interest?</p>",
                    question_hi: "<p>11. साधारण ब्याज पर एक राशि 8 वर्षों में दोगुनी हो जाती है। समान ब्याज दर पर Rs.25,000 की राशि कितने वर्षों में Rs.1,00,000 हो जाएगी ?</p>",
                    options_en: [" 32", " 28", 
                                " 16", "<p>24</p>"],
                    options_hi: ["<p>32</p>", "<p>28</p>",
                                "<p>16</p>", "<p>24</p>"],
                    solution_en: "<p>11.(d)&nbsp;Simple interest = Amount -&nbsp;Principal<br>Simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math><br>Let the principal be x.<br>Then, Amount = 2x<br>According to question,<br>(2x - x) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math> &rArr; Rate of interest = 12.5 %<br>(100000 -&nbsp;25000) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25000</mn><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math><br>Required time = 24 years<br><strong>Short tricks :-</strong> <br>Simple interest = 1,00,000 - 25,000 = 75,000 ₹ = 3 &times; principal<br>According to the question , <br>Simple interest in 8 years = 1 &times; principal<br>Time required for 3 &times; principal = 8 &times; 3 = 24 years</p>",
                    solution_hi: "<p>11.(d)&nbsp;साधारण ब्याज = मिश्रधन -&nbsp;मूलधन<br>साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>माना मूलधन = x<br>तो, राशि = 2x<br>प्रश्न के अनुसार,<br>(2x - x) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math> &rArr; ब्याज दर = 12.5 %<br>(100000 - 25000) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25000</mn><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math><br>आवश्यक समय = 24 वर्ष<br><strong>Short tricks :-</strong> <br>साधारण ब्याज = 1,00,000 - 25,000 = 75,000 ₹ = 3 &times; मूलधन<br>प्रश्न के अनुसार ,<br>8 वर्ष में साधारण ब्याज = 1 &times; मूलधन<br>3 &times; मूलधन के लिए आवश्यक समय = 8 &times; 3 = 24 वर्ष</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "12. How much simple interest will ₹6,000 earn in 21 months at 8% per annum?",
                    question_hi: "<p>12. 8% वार्षिक दर से 21 माह में ₹6,000 पर कितना साधारण ब्याज अर्जित होगा ?</p>",
                    options_en: [" ₹ 750  ", " ₹ 880", 
                                " ₹ 620", "<p>₹ 840</p>"],
                    options_hi: ["<p>₹ 750</p>", "<p>₹ 880</p>",
                                "<p>₹ 620</p>", "<p>₹ 840</p>"],
                    solution_en: "<p>12.(d)<br>Simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Principal</mi><mo>&#215;</mo><mi>rate</mi><mo>&#215;</mo><mi>time</mi></mrow><mn>100</mn></mfrac></math><br>Simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6000</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>21</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = 40 &times; 21 = 840 Rs.</p>",
                    solution_hi: "<p>12.(d)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6000</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>21</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = 40 &times; 21 = 840 Rs.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "13.  A man took a loan of ₹32,400 at a certain rate of simple interest per annum. The rate of interest is one-fourth of the number of years for which the loan is taken. If he paid ₹11,664 as interest at the end of the loan period, the rate of interest was:",
                    question_hi: "<p>13. एक आदमी ने वार्षिक साधारण ब्याज की एक निश्चित दर पर ₹32,400 का ऋण लिया। ब्याज की दर उन वर्षों की संख्या का एक चौथाई है, जितने अवधि लिए ऋण लिया गया। यदि उसने ऋण अवधि के अंत में ₹11,664 ब्याज का भुगतान किया, तो ब्याज की दर थी</p>",
                    options_en: [" 3%", "<p>8%</p>", 
                                "<p>5%</p>", "<p>12%</p>"],
                    options_hi: ["<p>3%</p>", "<p>8%</p>",
                                "<p>5%</p>", "<p>12%</p>"],
                    solution_en: "<p>13.(a)&nbsp;According to the question, <br><math display=\"inline\"><mi>T</mi></math> = 4R<br>So, 11664 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32400</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>4</mn><mi mathvariant=\"normal\">R</mi></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11664</mn><mrow><mn>324</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 9<br><math display=\"inline\"><mi>R</mi></math>ate (R) = 3 %</p>",
                    solution_hi: "<p>13.(a)&nbsp;प्रश्न के अनुसार,<br><math display=\"inline\"><mi>T</mi></math> = 4R<br>तो , 11664 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32400</mn><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mn>4</mn><mi mathvariant=\"normal\">R</mi></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11664</mn><mrow><mn>324</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 9<br>दर (<math display=\"inline\"><mi>R</mi></math>) = 3 %</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Archana took a loan of ₹78,000 from a bank on <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>24</mn><mi>th</mi></msup></math> January 2012 at <math display=\"inline\"><mn>8</mn><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>% per annum simple interest and paid it back on <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>18</mn><mi>th</mi></msup></math>June 2012. Find the total amount paid by Archana. (Approximated to nearest integer)</p>",
                    question_hi: "<p>14. अर्चना ने 24 जनवरी 2012 को एक बैंक से 8<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>% वार्षिक साधारण ब्याज पर ₹78,000 का ऋण लिया और इसे 18 जून 2012 को वापस चुका दिया। अर्चना द्वारा भुगतान की गई कुल राशि ज्ञात कीजिए। (निकटतम पूर्णांक में सन्निकटन)</p>",
                    options_en: ["<p><math display=\"inline\"><mi>&#8377;</mi></math>80,723</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>90,730</p>", 
                                "<p><math display=\"inline\"><mi>&#8377;</mi></math>85,733</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>88,730</p>"],
                    options_hi: ["<p><math display=\"inline\"><mi>&#8377;</mi></math>80,723</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>90,730</p>",
                                "<p><math display=\"inline\"><mi>&#8377;</mi></math>85,733</p>", "<p><math display=\"inline\"><mi>&#8377;</mi></math>88,730</p>"],
                    solution_en: "<p>14.(a)&nbsp;Total days from 24 january 2012 to 18 june 2012</p>\n<p>= 7 + 29 + 31 +30 +31 + 18 = 146 days<br>Simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Principal</mi><mo>&#215;</mo><mi>Rate</mi><mo>&#215;</mo><mi>Time</mi></mrow><mn>100</mn></mfrac></math><br>Simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>78000</mn><mo>&#215;</mo><mn>35</mn><mo>&#215;</mo><mn>146</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mn>366</mn></mrow></mfrac></math> = 2722.54&nbsp;= 2723 ₹<br>Amount = 78000 + 2723 = 80723 ₹</p>",
                    solution_hi: "<p>14.(a)&nbsp;24 जनवरी 2012 से 18 जून 2012 तक कुल दिन</p>\n<p>= 7 + 29 + 31 +30 +31 + 18 = 146 दिन <br>साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>78000</mn><mo>&#215;</mo><mn>35</mn><mo>&#215;</mo><mn>146</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mn>366</mn></mrow></mfrac></math> = 2722.54&nbsp;= 2723 ₹<br>मिश्रधन = 78000 + 2723 = 80723 ₹</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. A sum of money at a fixed rate of simple interest amounts to ₹1,630 in 3 years and to&nbsp;₹1,708 in 4 years. Find the sum (in ₹).</p>",
                    question_hi: "<p>15. साधारण ब्याज की एक निश्चित दर पर एक धनराशि पर 3 वर्षों में प्राप्त मिश्रधन ₹1,630 और 4 वर्षों में प्राप्त मिश्रधन ₹1,708 है। वह धनराशि (₹ में) ज्ञात करें।</p>",
                    options_en: [" 1132", "<p>1296</p>", 
                                "<p>1396</p>", "<p>1448</p>"],
                    options_hi: ["<p>1132</p>", "<p>1296</p>",
                                "<p>1396</p>", "<p>1448</p>"],
                    solution_en: "<p>15.(c)&nbsp;According to question,<br>Interest in 1 year = 1708 -&nbsp;1630 = 78 Rs.<br>Interest in 3 years = 78 &times; 3 = 234 Rs.<br>Required Sum = 1630 -&nbsp;234 = 1396 Rs.</p>",
                    solution_hi: "<p>15.(c)&nbsp;प्रश्न के अनुसार,<br>1 वर्ष में ब्याज = 1708 -&nbsp;1630 = 78 रु.<br>3 वर्ष में ब्याज = 78 &times; 3 = 234 रु.<br>आवश्यक राशि = 1630 -&nbsp;234 = 1396 रु.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>