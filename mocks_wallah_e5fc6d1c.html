<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The ratio of the ages of A and B, 3 years ago, was 5 : 7. The ratio of their ages, 13 years from now, will be 9 : 11. If the present age of C is 11 years less than that of B, then the present age of C, in years, is:</p>",
                    question_hi: "<p>1. 3 वर्ष पहले, A और B की आयु का अनुपात 5 : 7 था। अब से 13 वर्ष बाद, उनकी आयु का अनुपात 9 : 11 होगा। यदि C की वर्तमान आयु B की वर्तमान आयु से 11 वर्ष कम है, तो C की वर्तमान आयु (वर्ष में) क्&zwj;या है?</p>",
                    options_en: ["<p>23</p>", "<p>36</p>", 
                                "<p>31</p>", "<p>20</p>"],
                    options_hi: ["<p>23</p>", "<p>36</p>",
                                "<p>31</p>", "<p>20</p>"],
                    solution_en: "<p>1.(d)<br><strong id=\"docs-internal-guid-853d81b2-7fff-c409-7cda-7cd387a5a3a3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdevBUWX2pFtkj7aMZWePwMnQfpVOk_vXoQx7JA5kAC310Ul6X7q3TGOMNMzirqcPt_24M5DUEJx9S2hsB9Z08of2dWv23OJt7-oIqHVDMuh7c2v9CBdC1Sh8haKcCFD4L_PzUcLpe8v5o8BM0Z4i9kMzhF?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"190\" height=\"137\"></strong><br>4 units = 16 years <br>1 units = 4 years<br>Present age of A = 9 &times; 4 - 13 = 23 years<br>Present age of B = 11 &times; 4 - 13 = 31 years<br>Now,<br>Present age of C = 31 - 11 = 20 years</p>",
                    solution_hi: "<p>1.(d)<br><strong id=\"docs-internal-guid-bba92bf2-7fff-ef5f-6e2d-ad715de1da12\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeDdtffUh-DkqbcghQmbog6BdYBz9oV6J1iBz-ZGb4Y5yD3sLTyxsgKoqs59vZ0l-nVtYCtCNiPY7fsTu-C0r9txXqlcTRoXaI_XM6yy8TMHR7iPIBOyxi2sKmzGdsnUkLuFWU-w9WbMKUUdVdrOw79Ve76?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"178\" height=\"125\"></strong><br>4 इकाई = 16 वर्ष <br>1 इकाई = 4 वर्ष<br>A की वर्तमान आयु = 9 &times; 4 - 13 = 23 वर्ष<br>B की वर्तमान आयु = 11 &times; 4 - 13 = 31 वर्ष<br>अब,<br>C की वर्तमान आयु = 31 - 11 = 20 वर्ष</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Let 3t &ndash; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>t</mi></mrow></mfrac></math> = 3, then which of the following expressions has the value equal to 12?</p>",
                    question_hi: "<p>2. मान लीजिए 3t &ndash; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>t</mi></mrow></mfrac></math> = 3 है, तो निम्नलिखित में से किस व्यंजक का मान 12 के बराबर है?</p>",
                    options_en: ["<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> + 2</p>", "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> - 2</p>", 
                                "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1</p>", "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math></p>"],
                    options_hi: ["<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> + 2</p>", "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> - 2</p>",
                                "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1</p>", "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math>&nbsp;</p>"],
                    solution_en: "<p>2.(c) 3t &ndash; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>t</mi></mrow></mfrac></math> = 3 <br>Taking square roots both side,<br><math display=\"inline\"><mo>&#8658;</mo></math> 9t<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> - 2 = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> 9t<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> = 11<br>adding 1 to both sides<br><math display=\"inline\"><mo>&#8658;</mo></math> 9t<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1 = 11 + 1<br><math display=\"inline\"><mo>&#8658;</mo></math> 9t<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1 = 12</p>",
                    solution_hi: "<p>2.(c) 3t &ndash; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>t</mi></mrow></mfrac></math> = 3 <br>दोनों तरफ वर्गमूल करने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math> 9t<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> - 2 = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> 9t<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> = 11<br>दोनों तरफ 1 जोड़ने पर<br><math display=\"inline\"><mo>&#8658;</mo></math> 9t<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1 = 11 + 1<br><math display=\"inline\"><mo>&#8658;</mo></math> 9t<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1 = 12</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Find the area of a rectangle whose length is 52m and breadth is 9m.</p>",
                    question_hi: "<p>3. एक ऐसे आयत का क्षेत्रफल ज्ञात कीजिए जिसकी लंबाई 52 m और चौड़ाई 9 m है।</p>",
                    options_en: ["<p>468<math display=\"inline\"><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>253<math display=\"inline\"><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", 
                                "<p>625<math display=\"inline\"><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>552<math display=\"inline\"><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    options_hi: ["<p>468<math display=\"inline\"><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>253<math display=\"inline\"><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                                "<p>625<math display=\"inline\"><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>552<math display=\"inline\"><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    solution_en: "<p>3.(a) Area of rectangle = length &times; breadth<br>= 52 &times; 9 = 468 cm<sup>2</sup></p>",
                    solution_hi: "<p>3.(a) आयत का क्षेत्रफल = लंबाई &times; चौड़ाई<br>= 52 &times; 9 = 468 cm<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The following table shows the number of students admitted in to a university for higher education from 2010 to 2013.<br><strong id=\"docs-internal-guid-8186d1b5-7fff-32a1-46b8-f1e36a880c06\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdg3dnYWk5G985rw3HRHP0NebYT68fGJD_W_Kc_eoPCHVvgCGsIvIA1deMau1xHDfajHsJDs4cUX_EjY6nHIdn5ZMH6uzA1Lq1FfOCd-_Y9HbNBMQOqeBPrULkHIdG9B2rewcchZA?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"260\" height=\"114\"></strong><br>What is the average number of students admitted in to the university from 2010 to 2013?</p>",
                    question_hi: "<p>4. निम्नलिखित तालिका में वर्ष 2010 से 2013 तक एक विश्वविद्यालय में उच्च शिक्षा के लिए दाखिला लेने वाले विद्यार्थियों की संख्या दर्शाई गई है।<br><strong id=\"docs-internal-guid-39218be3-7fff-50f8-36f7-8d7b438dd412\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdRBN0mroNkjJgpXkNv1X_0lNKJXNHsnaJvTl8J6fKCKNRz4YcoSV8_xhjZqF7dI-nWwrmXUBqH6wdI44xAJ8w8RbRjLowT7VTqDP4Hj214ZRo0iiRRsWQP9ebY_YaWyWCWhsw9Hg?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"224\" height=\"111\"></strong></p>",
                    options_en: ["<p>495</p>", "<p>499</p>", 
                                "<p>510</p>", "<p>522</p>"],
                    options_hi: ["<p>495</p>", "<p>499</p>",
                                "<p>510</p>", "<p>522</p>"],
                    solution_en: "<p>4.(c) Average of number of students = <math display=\"inline\"><mfrac><mrow><mn>450</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>540</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>370</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>680</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mn>4</mn></mfrac></math> = 510</p>",
                    solution_hi: "<p>4.(c) छात्रों की संख्या का औसत = <math display=\"inline\"><mfrac><mrow><mn>450</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>540</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>370</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>680</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2040</mn><mn>4</mn></mfrac></math> = 510</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The monthly production of electric bulbs by a company during the second half of the year 2021 is shown by the following bar graph. Study the bar graph carefully and answer the question given below.<br><strong id=\"docs-internal-guid-356a5af2-7fff-15ef-09c6-f0050c36b7d6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf82tB_Lk8KgR49k0ftnekKFx2og0v01I3kLtU7RWSq6FS1rFeVEC10zYCS2_ELRwUjrtIfPvGb8-tcv2_MXLr2RlDUGYG7ISIvmumTTsIlUVOr5feRG7idNu-U4GFeGZUOquM49pX9OAlKpMC5MDP8cxA?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"406\" height=\"268\"></strong><br>What is the percentage decrement in the production of electric bulbs in October as compared to the production in July? (Correct up to two decimal places.)</p>",
                    question_hi: "<p>5. निम्नलिखित बार ग्राफ में वर्ष 2021 की दूसरी छमाही के दौरान एक कंपनी द्वारा बिजली के बल्बों का किया गया मासिक उत्पादन दर्शाया गया है। बार ग्राफ का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><strong id=\"docs-internal-guid-6139ff07-7fff-5fb2-eff1-a6edf6ec33ab\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeNqUk6Gc-BAz3g2x_cBNgs86lBaqUL7snx9X7klILoTwtCJPwmiKFVfWQLNkIVdH8m33kcNA1bg69yjVUfBEPmq1J9pxP2IGcmH4JzOdOrylmRFjcEouWkMFSzETgyF17ZytMEESMNuaCOrSMEJqwWKi8H?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"394\" height=\"260\"></strong><br>जुलाई में किए गए उत्पादन की तुलना में, अक्तूबर में बिजली के बल्बों के उत्पादन में कितने प्रतिशत की कमी हुई है? (दो दशमलव स्थान तक पूर्णांकित)</p>",
                    options_en: ["<p>30.28%</p>", "<p>31.82%</p>", 
                                "<p>29.82%</p>", "<p>32.28%</p>"],
                    options_hi: ["<p>30.28%</p>", "<p>31.82%</p>",
                                "<p>29.82%</p>", "<p>32.28%</p>"],
                    solution_en: "<p>5.(b) Production of electric bulbs in july = 550<br>Production of electric bulbs in october = 375<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>550</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>375</mn></mrow><mrow><mn>550</mn></mrow></mfrac></math> &times; 100 = 31.82%</p>",
                    solution_hi: "<p>5.(b) जुलाई में विद्युत बल्बों का उत्पादन = 550<br>अक्टूबर में विद्युत बल्बों का उत्पादन = 375<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>550</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>375</mn></mrow><mrow><mn>550</mn></mrow></mfrac></math> &times; 100 = 31.82%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "6. In a team of seven members, the average age of six of the members is 42 years while the age of the seventh member of the team is 36 years more than the average age of all the seven members taken together. Find the age (in years) of the seventh member of the team.",
                    question_hi: "6. सात सदस्यों की एक टीम में, छ: सदस्यों की औसत आयु 42 वर्ष है जबकि टीम के सातवें सदस्य की आयु, सभी सात सदस्यों की औसत आयु से 36 वर्ष अधिक है। टीम के सातवें सदस्य की आयु (वर्ष में) ज्ञात करें।",
                    options_en: [" 90", " 84 ", 
                                " 80  ", " 78"],
                    options_hi: [" 90", " 84 ",
                                " 80  ", " 78"],
                    solution_en: "6.(b) Let the average age of seven member be <math display=\"inline\"><mi>x</mi></math> <br />sum of the age of the six members = 42 × 6 = 252<br />According to the question,<br />7<math display=\"inline\"><mi>x</mi></math> = (36 + x) + 252<br />6<math display=\"inline\"><mi>x</mi></math> = 288<br /><math display=\"inline\"><mi>x</mi></math> = 48 <br />Age of the seventh member = 48 × 7 - 252 = 84 years",
                    solution_hi: "6.(b) माना कि सात सदस्यों की औसत आयु <math display=\"inline\"><mi>x</mi></math> है <br />छह सदस्यों की आयु का योग = 42 × 6 = 252<br />प्रश्न के अनुसार,<br />7<math display=\"inline\"><mi>x</mi></math> = (36 + x) + 252<br />6<math display=\"inline\"><mi>x</mi></math> = 288<br /><math display=\"inline\"><mi>x</mi></math> = 48 <br />सातवें सदस्य की आयु = 48 × 7 - 252 = 84 वर्ष",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The value of <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mn>2</mn><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow></mfrac></math> at &theta;= 45&deg;</p>",
                    question_hi: "<p>7. <math display=\"inline\"><mi>&#952;</mi><mo>=</mo></math> 45&deg; पर <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>&#952;</mi></mrow><mrow><mn>2</mn><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>1</p>", "<p>0</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>1</p>", "<p>0</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>7.(b) <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mn>2</mn><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>4</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>4</mn></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>4</mn></mfrac></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>4</mn></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>0</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 0</p>",
                    solution_hi: "<p>7.(b) <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mn>2</mn><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>4</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>4</mn></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>4</mn></mfrac></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>4</mn></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>0</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 0</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The sale of two kinds of bicycles B1 and B2 (in lakhs) manufactured by a company over a period of three years are shown in the table below.<br><strong id=\"docs-internal-guid-5991a091-7fff-3545-b9a7-6c8d9f4a401f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXefBfOYP-M2UIUr17NWf3mJHvoRHTVvJ-KojGlWbcVh7FfQ_OzfDK3_JvlbgRtaxzrMXjiEuMhrtnzIhd5VlHsH7uICcueCuEad-Z3OnXK2RxciKEbRgTl8CFHqE3MS-n9LPrp6?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"176\" height=\"90\"></strong><br>From 2001 to 2003, the total sale of bicycles B1 is what percent of the total sale of bicycles B2?</p>",
                    question_hi: "<p>8. एक कंपनी द्वारा तीन वर्षों की अवधि में निर्मित दो प्रकार की साइकिलों B1 और B2 की बिक्री (लाखों में) नीचे दी गई तालिका में दर्शाई गई है।<br><strong id=\"docs-internal-guid-ec7b018e-7fff-507d-c4a2-10d0ea503151\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc7-YchUISO5GMbWXV80gkiQwEnTlFMgznd-BNv6N-WGJLY3evFEard5-9CFCFln4Bxo89gTL-Tz4KFQO9EhjMPWUpGJE86nHGetbKGpC1cd2WQY7z08qIW59w00DZTi8IbG1EvwQ?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"182\" height=\"98\"></strong><br>2001 से 2003 तक, साइकिल B1 की कुल बिक्री, साइकिल B2 की कुल बिक्री का कितना प्रतिशत है?</p>",
                    options_en: ["<p>70.6%</p>", "<p>68.2%</p>", 
                                "<p>62.5%</p>", "<p>56.7%</p>"],
                    options_hi: ["<p>70.6%</p>", "<p>68.2%</p>",
                                "<p>62.5%</p>", "<p>56.7%</p>"],
                    solution_en: "<p>8.(c) Total sale of B1 = 95 + 130 + 150 = 375<br>Total sale of B2 = 184 + 200 + 216 = 600<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>375</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>600</mn><mi>&#160;</mi></mrow></mfrac></math> = 62.5%</p>",
                    solution_hi: "<p>8.(c) B1 की कुल बिक्री = 95 + 130 + 150 = 375<br>B2 की कुल बिक्री = 184 + 200 + 216 = 600<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>375</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>600</mn><mi>&#160;</mi></mrow></mfrac></math> = 62.5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "9. A class of 30 students appeared in a test. The average score of 12 students is 62, and that of the rest is 60. What is the average score of the class?",
                    question_hi: "9. 30 छात्रों वाली एक कक्षा के सभी छात्रों ने एक टेस्ट दिया। 12 छात्रों का औसत स्कोर 62 है, और बाकी छात्रों का औसत स्कोर 60 है। कक्षा का औसत स्कोर ज्ञात करें।",
                    options_en: [" 58.8 ", " 61.8", 
                                " 60.8", " 59.8 "],
                    options_hi: [" 58.8 ", " 61.8",
                                " 60.8", " 59.8 "],
                    solution_en: "9.(c) Let the average of 30 student be 61<br />12 students average = 62 <br />Deviation = (62 - 61)12 = +12<br />18 students average = 60<br />Deviation = (60 - 61) × 18 = -18<br />Net deviation = 12 -18 = -6<br />Now,<br />Average of 30 students = 60 + <math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>6</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = 61 - 0.2 = 60.8",
                    solution_hi: "9.(c) माना 30 छात्रों का औसत 61 है<br />12 विद्यार्थियों का औसत = 62  <br />विचलन = (62 - 61)12 = +12<br />18 विद्यार्थियों का औसत = 60<br />विचलन = (60 - 61) × 18 = -18<br />कुल विचलन = 12 - 18 = - 6<br />अब,<br />30 छात्रों का औसत  = 60 + <math display=\"inline\"><mfrac><mrow><mo>-</mo><mn>6</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> = 61 - 0.2 = 60.8",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The length of the tangent drawn from a point 10 cm away from the centre of a circle with radius 4 cm, is:</p>",
                    question_hi: "<p>10. 4 cm त्रिज्या वाले एक वृत्त के केंद्र से 10 cm दूर स्थित एक बिंदु से खींची खीं गई स्पर्श रेखा की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>3<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math></p>", "<p>3<math display=\"inline\"><msqrt><mn>21</mn></msqrt></math></p>", 
                                "<p>2<math display=\"inline\"><msqrt><mn>21</mn></msqrt></math></p>", "<p>2<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math></p>"],
                    options_hi: ["<p>3<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math></p>", "<p>3<math display=\"inline\"><msqrt><mn>21</mn></msqrt></math></p>",
                                "<p>2<math display=\"inline\"><msqrt><mn>21</mn></msqrt></math></p>", "<p>2<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math></p>"],
                    solution_en: "<p>10.(c) <br><strong id=\"docs-internal-guid-b22b362b-7fff-9b67-d2e5-9b88c72237cc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcED6Bt_7zxjppNKYkyjbtKJZSjKCpkD6ML1btoBO9T1wHXXofbrBpeyyf8ms0hp3M6g-kuObYPnKY-_ZyhflaxNamgrHSOeF7S3pfNZK6SNLsNpHJPIwkoyGPaVVkI4hd5cRTTwcvBNB6EoJI0LHMegDEr?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"240\" height=\"117\"></strong><br>In <math display=\"inline\"><mi>&#916;</mi></math>PTO,<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>OT</mi><mn>2</mn></msup></math> + TP<sup>2</sup> = OP<sup>2</sup><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>TP</mi><mn>2</mn></msup></math> = 10<sup>2</sup> - 4<sup>2</sup><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>TP</mi><mn>2</mn></msup></math> = 100 - 16 = 84<br>TP = 2<math display=\"inline\"><msqrt><mn>21</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>10.(c) <br><strong id=\"docs-internal-guid-955af7cb-7fff-f1be-3787-7752ea66cfd6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcED6Bt_7zxjppNKYkyjbtKJZSjKCpkD6ML1btoBO9T1wHXXofbrBpeyyf8ms0hp3M6g-kuObYPnKY-_ZyhflaxNamgrHSOeF7S3pfNZK6SNLsNpHJPIwkoyGPaVVkI4hd5cRTTwcvBNB6EoJI0LHMegDEr?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"240\" height=\"117\"></strong><br><math display=\"inline\"><mi>&#916;</mi></math>PTO में ,<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>OT</mi><mn>2</mn></msup></math> + TP<sup>2</sup> = OP<sup>2</sup><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>TP</mi><mn>2</mn></msup></math> = 10<sup>2</sup> - 4<sup>2</sup><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>TP</mi><mn>2</mn></msup></math> = 100 - 16 = 84<br>TP = 2<math display=\"inline\"><msqrt><mn>21</mn></msqrt></math> cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If an employee\'s income is ₹72,000, the amount he spends is 40% higher than his savings. Therefore, his savings (in ₹) is:</p>",
                    question_hi: "<p>11. यदि किसी कर्मचारी की आय ₹72,000 है, वह जो राशि खर्च करता है वह उसकी बचत से 40% अधिक है। इसलिए, उसकी बचत (₹ में) कितनी है?</p>",
                    options_en: ["<p>30000</p>", "<p>34000</p>", 
                                "<p>32000</p>", "<p>28000</p>"],
                    options_hi: ["<p>30000</p>", "<p>34000</p>",
                                "<p>32000</p>", "<p>28000</p>"],
                    solution_en: "<p>11.(a) Ratio &rarr; Expenditure&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Saving <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 140&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;100<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 5<br>12 units = ₹ 72000 <br>5 units = ₹ 30000<br>Hence, the saving amount is ₹ 30000.</p>",
                    solution_hi: "<p>11.(a) अनुपात &rarr;&nbsp; &nbsp; व्यय&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;बचत <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;140&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;100<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 5<br>12 यूनिट = ₹ 72000 <br>5 यूनिट = ₹ 30000<br>अतः, बचत राशि ₹ 30000 है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A customer pays ₹975 in instalments. The payment is done each month ₹5 less than the previous month. If the first instalment is ₹100, how much time will be taken to pay the entire amount?</p>",
                    question_hi: "<p>12. एक ग्राहक ₹975 का भुगतान किस्तों में करता है। हर महीने, पिछले महीने की तुलना में ₹5 कम का भुगतान किया जाता है। यदि पहली किस्त ₹100 है, तो पूरी राशि का भुगतान करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>14 months</p>", "<p>15 months</p>", 
                                "<p>27 months</p>", "<p>26 months</p>"],
                    options_hi: ["<p>14 महीने</p>", "<p>15 महीने</p>",
                                "<p>27 महीने</p>", "<p>26 महीने</p>"],
                    solution_en: "<p>12.(b) According to the question,<br>100, 95, 90, 85, &hellip;&hellip;..nth term <br><math display=\"inline\"><msub><mrow><mi>s</mi></mrow><mrow><mi>n</mi></mrow></msub></math> = 975, a = 100 and d = -5<br><math display=\"inline\"><mo>&#8658;</mo></math> s<sub>n</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math>[2a + (n - 1)d]<br><math display=\"inline\"><mo>&#8658;</mo></math> 975 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math>[2 &times; 100 + (n - 1) - 5]<br><math display=\"inline\"><mo>&#8658;</mo></math> 1950 = 200n - 5n<sup>2</sup> + 5n<br><math display=\"inline\"><mo>&#8658;</mo></math> 5n<sup>2</sup>- 205n + 1950 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> n<sup>2</sup> - 41n + 390 = 0<br>On solving above equation, we get<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 15 and 26<br>Now, if n = 26, the number of installments will become negative (after 20 installments), hence it is not possible. <br>Total installments = 15 months</p>",
                    solution_hi: "<p>12.(b) प्रश्न के अनुसार,<br>100, 95, 90, 85, &hellip;&hellip;..n वां पद <br><math display=\"inline\"><msub><mrow><mi>s</mi></mrow><mrow><mi>n</mi></mrow></msub></math> = 975, a = 100 and d = -5<br><math display=\"inline\"><mo>&#8658;</mo></math> s<sub>n</sub> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math>[2a + (n - 1)d]<br><math display=\"inline\"><mo>&#8658;</mo></math> 975 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math>[2 &times; 100 + (n - 1) - 5]<br><math display=\"inline\"><mo>&#8658;</mo></math> 1950 = 200n - 5n<sup>2</sup> + 5n<br><math display=\"inline\"><mo>&#8658;</mo></math> 5n<sup>2</sup>- 205n + 1950 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> n<sup>2</sup> - 41n + 390 = 0<br>उपरोक्त समीकरण को हल करने पर, हमें प्राप्त होता है<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 15 and 26<br>अब, यदि n = 26 है, तो किस्तों की संख्या ऋणात्मक हो जाएगी (20 किश्तों के बाद), इसलिए यह संभव नहीं है। <br>कुल किश्तें = 15 माह</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Find the gain percentage, given that Anubha sold her scooter for 31524 gaining <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> th of the selling price .</p>",
                    question_hi: "<p>13. अनुभा अपने स्कूटर को ₹31524 में बेचकर, विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> भाग के बराबर लाभ अर्जित करती है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>5%</p>", "<p>20%</p>", 
                                "<p>30%</p>", "<p>35%</p>"],
                    options_hi: ["<p>5%</p>", "<p>20%</p>",
                                "<p>30%</p>", "<p>35%</p>"],
                    solution_en: "<p>13.(b) Selling price of a scooter = 31524 <br>Gain = 31524 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 5254<br>CP of a scooter = 31524 - 5254 = 26270<br>Gain % = <math display=\"inline\"><mfrac><mrow><mn>5254</mn></mrow><mrow><mn>26270</mn></mrow></mfrac></math> &times; 100 = 20 %</p>",
                    solution_hi: "<p>13.(b) स्कूटर का विक्रय मूल्य = 31524 <br>लाभ = 31524 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 5254<br>एक स्कूटर का CP = 31524 - 5254 = 26270<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>5254</mn></mrow><mrow><mn>26270</mn></mrow></mfrac></math> &times; 100 = 20 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. If in a circle a chord of length 14 cm is at a distance of 24 cm from its centre, then the length of the radius of the circle is:</p>",
                    question_hi: "<p>14. यदि किसी वृत्त में 14 cm लंबाई की एक जीवा इसके केंद्र से 24 cm की दूरी पर है, तो वृत्त की त्रिज्या की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>28 cm</p>", "<p>31 cm</p>", 
                                "<p>24 cm</p>", "<p>25 cm</p>"],
                    options_hi: ["<p>28 cm</p>", "<p>31 cm</p>",
                                "<p>24 cm</p>", "<p>25 cm</p>"],
                    solution_en: "<p>14.(d)<br><strong id=\"docs-internal-guid-5196e700-7fff-9cfd-c7a7-dc98a779412d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf1Z6LkvO9L_t5d2TDxQHtWYfAzJW7Q-CnLt49rSIsJi-hk8d_M4xSielUxLs-QwZt8rFed-OPJwSLB4eIGBFzizzc879lgVEvM7cVDVZ8mD81HAaw47BFiDjBRySr8Dv4g-KJza4nq8odse9lGHiLYdeg?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"173\" height=\"161\"></strong><br>In <math display=\"inline\"><mi>&#916;</mi></math>OMB,<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>OM</mi><mn>2</mn></msup></math> + MB<sup>2</sup> = OB<sup>2</sup><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>OB</mi><mn>2</mn></msup></math> = 24<sup>2</sup> + 7<sup>2</sup><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>OB</mi><mn>2</mn></msup></math> = 576 + 49 = 625<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>OB</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn></msqrt></math> = 25 cm</p>",
                    solution_hi: "<p>14.(d)<br><strong id=\"docs-internal-guid-5196e700-7fff-9cfd-c7a7-dc98a779412d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf1Z6LkvO9L_t5d2TDxQHtWYfAzJW7Q-CnLt49rSIsJi-hk8d_M4xSielUxLs-QwZt8rFed-OPJwSLB4eIGBFzizzc879lgVEvM7cVDVZ8mD81HAaw47BFiDjBRySr8Dv4g-KJza4nq8odse9lGHiLYdeg?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"173\" height=\"161\"></strong><br><math display=\"inline\"><mi>&#916;</mi></math>OMB में ,<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>OM</mi><mn>2</mn></msup></math> + MB<sup>2</sup> = OB<sup>2</sup><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>OB</mi><mn>2</mn></msup></math> = 24<sup>2</sup> + 7<sup>2</sup><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>OB</mi><mn>2</mn></msup></math> = 576 + 49 = 625<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>OB</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>625</mn></msqrt></math> = 25 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. The following table represents the total number of students in different classes and the respective number of students who are vegetarian, in a school. Study the table carefully and answer the question that follows.<br><strong id=\"docs-internal-guid-80b7b1d9-7fff-6eb7-c192-562be4fdbdf7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXep3_ICYAZbi94teqK8Ryn8GKFX4mEmmTfzwVMyOLhZ8TtbmnFAnMikbrPOmqVJS6SE2UGLMbJAd3Fr_OoVKNKPUDqHrIvwWBoiRtQy6tf_EY_q3RZGESahfXMjAcgMXQGVxCRJ9A?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"387\" height=\"127\"></strong><br>What is the percentage of non-vegetarian students in class XII?</p>",
                    question_hi: "<p>15. निम्नलिखित तालिका एक विद्यालय में विभिन्न कक्षाओं में छात्रों की कुल संख्या और शाकाहारी छात्रों की संबंधित संख्या को दर्शाती है। तालिका का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><strong id=\"docs-internal-guid-0bb9e436-7fff-8d64-e6a9-9be141a1bc14\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe7idp0P50S6wCbOYmyHxzrmFwnq7VfTKlcfbDAU_nReeSg0BdMBlAE_KnLX0azwd3QmdQk9bz5guPeupdtPC374yC7lSQjr-Q6AeoBxKaRfqWQlRtzFraLZEzP3vr38jsjnCK0FA?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"377\" height=\"114\"></strong><br>कक्षा XII में मांसाहारी छात्रों का प्रतिशत कितना है?</p>",
                    options_en: ["<p>40%</p>", "<p>30%</p>", 
                                "<p>35%</p>", "<p>25%</p>"],
                    options_hi: ["<p>40%</p>", "<p>30%</p>",
                                "<p>35%</p>", "<p>25%</p>"],
                    solution_en: "<p>15.(b) Non vegetarian students percentage = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>105</mn></mrow><mrow><mn>150</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>150</mn></mfrac></math> &times; 100 = 30 %</p>",
                    solution_hi: "<p>15.(b) मांसाहारी छात्रों का प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>105</mn></mrow><mrow><mn>150</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>150</mn></mfrac></math> &times; 100 = 30 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. If 3x + 2y = 10 and 2xy = 7, then find the value of 3x - 2y (if 3x &ndash; 2y &gt; 0).</p>",
                    question_hi: "<p>16. यदि 3x + 2y = 10 और 2xy = 7 है, तो 3x -2y का मान ज्ञात कीजिए (यदि 3x - 2y &gt; 0)।</p>",
                    options_en: ["<p>4</p>", "<p>10</p>", 
                                "<p>8</p>", "<p>6</p>"],
                    options_hi: ["<p>4</p>", "<p>10</p>",
                                "<p>8</p>", "<p>6</p>"],
                    solution_en: "<p>16.(a) Given: 3x + 2y = 10 and 2xy = 7<br>We know that,<br>(a - b) = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">b</mi></msqrt></math><br>(3x - 2y) = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">y</mi></msqrt></math><br>So,<br>(3x - 2y) = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn></msqrt></math><br>= <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn><mo>-</mo><mn>84</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> = 4</p>",
                    solution_hi: "<p>16.(a) दिया गया है : 3x + 2y = 10 and 2xy = 7<br>हम जानते हैं की ,<br>(a - b) = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">a</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">b</mi></msqrt></math><br>(3x - 2y) = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">y</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">y</mi></msqrt></math><br>इसलिए , <br>(3x - 2y) = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn></msqrt></math><br>= <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>100</mn><mo>-</mo><mn>84</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> = 4</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. If each side of an equilateral triangle is 37<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm, then its altitude (in cm) is equal to</p>",
                    question_hi: "<p>17. यदि एक समबाहु त्रिभुज की प्रत्येक भुजा 37<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm है, तो इसका शीर्षलम्ब (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>37.5</p>", "<p>18.5</p>", 
                                "<p>60.5</p>", "<p>55.5</p>"],
                    options_hi: ["<p>37.5</p>", "<p>18.5</p>",
                                "<p>60.5</p>", "<p>55.5</p>"],
                    solution_en: "<p>17.(d) Altitude of an equilateral = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>a<br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 37<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>= <math display=\"inline\"><mfrac><mrow><mn>37</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 55.5</p>",
                    solution_hi: "<p>17.(d) एक समबाहु की ऊंचाई = <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math>a<br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 37<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>= <math display=\"inline\"><mfrac><mrow><mn>37</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 55.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. In &Delta;PQR, if 4&ang;P = 5&ang;Q = 20&ang;R, then the value of &ang;Q is:</p>",
                    question_hi: "<p>18. &Delta;PQR में, यदि 4&ang;P = 5&ang;Q = 20&ang;R है, तो &ang;Q का मान क्या है?</p>",
                    options_en: ["<p>72&deg;</p>", "<p>36&deg;</p>", 
                                "<p>90&deg;</p>", "<p>45&deg;</p>"],
                    options_hi: ["<p>72&deg;</p>", "<p>36&deg;</p>",
                                "<p>90&deg;</p>", "<p>45&deg;</p>"],
                    solution_en: "<p>18.(a) <strong>Let</strong> 4&ang;P = 5&ang;Q = 20&ang;R <br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;P : &ang;Q : &ang;R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>20</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;P : &ang;Q : &ang;R = 5 : 4 : 1<br>Now,<br>10 units = 180&deg;<br>4 units = 72&deg;</p>",
                    solution_hi: "<p>18.(a) <strong>माना</strong> 4&ang;P = 5&ang;Q = 20&ang;R <br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;P : &ang;Q : &ang;R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>4</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>5</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>20</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;P : &ang;Q : &ang;R = 5 : 4 : 1<br>अब ,<br>10 इकाई = 180&deg;<br>4 इकाई = 72&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. For what value of t is the value of sin<sup>2</sup> (t), half of the value of tan (t)?</p>",
                    question_hi: "<p>19. t के किस मान के लिए sin<sup>2</sup>(t) का मान, tan (t) के मान का आधा है?</p>",
                    options_en: ["<p>60&deg;</p>", "<p>45&deg;</p>", 
                                "<p>30&deg;</p>", "<p>22.5&deg;</p>"],
                    options_hi: ["<p>60&deg;</p>", "<p>45&deg;</p>",
                                "<p>30&deg;</p>", "<p>22.5&deg;</p>"],
                    solution_en: "<p>19.(b) According to the question,<br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times;&nbsp; tan<sup>2</sup>t<br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></mrow></mfrac></math><br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>cos t = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> = cos 45&deg;<br>t = 45&deg;</p>",
                    solution_hi: "<p>19.(b) प्रश्न के अनुसार,<br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times;&nbsp; tan<sup>2</sup>t<br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></mrow></mfrac></math><br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">t</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br>cost =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> = cos 45&deg;<br>t = 45&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. If tanA = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>10</mn></msqrt></mrow></mfrac></math>, A is an acute angle, then the value of sinA + cosecA is:</p>",
                    question_hi: "<p>20. यदि tanA = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>10</mn></msqrt></mrow></mfrac></math>&nbsp;है, A एक न्यून कोण है, तो sinA + cosecA का मान कितना होगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><msqrt><mn>10</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>10</mn></msqrt></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><msqrt><mn>11</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><msqrt><mn>11</mn></msqrt></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><msqrt><mn>10</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>10</mn></msqrt></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><msqrt><mn>11</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><msqrt><mn>11</mn></msqrt></mrow></mfrac></math></p>"],
                    solution_en: "<p>20.(c) tan A = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>10</mn></msqrt></mrow></mfrac></math><br><strong id=\"docs-internal-guid-f8824fa8-7fff-ffe2-4f3c-e18941488a78\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdVE5UD4wiIK77YUj6SIyw-n5U45QgqQIVFYIHV_ia_evLhkJ91FMcZ1yBSgaDJuQTTwWM-Xj9jpYbjP_myPy5LFwhOIqoQUuvG3YRSOimtF225b-iU2wX_38Z7VqQ5mmD5PpFD6TT8Q1Ik_hUTYx-5CQU?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"195\" height=\"146\"></strong><br>By using pythagoras theorem,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AB</mi><mn>2</mn></msup></math> + BC<sup>2</sup> = AC<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AC</mi><mn>2</mn></msup></math> = 1<sup>2</sup> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math>)<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AC</mi><mn>2</mn></msup></math> = 1 + 10 <br><math display=\"inline\"><mi>A</mi><mi>C</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>11</mn></msqrt></math> units<br>Now,<br>sin A + cosec A<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>11</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>11</mn></msqrt><mn>1</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>11</mn></mrow><mrow><msqrt><mn>11</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><msqrt><mn>11</mn></msqrt></mfrac></math></p>",
                    solution_hi: "<p>20.(c) tan A = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>10</mn></msqrt></mrow></mfrac></math><br><strong id=\"docs-internal-guid-9c22de9b-7fff-2e71-1868-67a70e19bbcf\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdVE5UD4wiIK77YUj6SIyw-n5U45QgqQIVFYIHV_ia_evLhkJ91FMcZ1yBSgaDJuQTTwWM-Xj9jpYbjP_myPy5LFwhOIqoQUuvG3YRSOimtF225b-iU2wX_38Z7VqQ5mmD5PpFD6TT8Q1Ik_hUTYx-5CQU?key=ueJlCzTyHS35ln4WUPWfj4XB\" width=\"195\" height=\"146\"></strong><br>पाइथागोरस प्रमेय का उपयोग करके,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AB</mi><mn>2</mn></msup></math> + BC<sup>2</sup> = AC<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AC</mi><mn>2</mn></msup></math> = 1<sup>2</sup> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math>)<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AC</mi><mn>2</mn></msup></math> = 1 + 10<br><math display=\"inline\"><mi>A</mi><mi>C</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>11</mn></msqrt></math> इकाई&nbsp;<br>अब,<br>sin A + cosec A<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>11</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>11</mn></msqrt><mn>1</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>11</mn></mrow><mrow><msqrt><mn>11</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><msqrt><mn>11</mn></msqrt></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. Simplify the following expression.<br>0.07 &times; 0.28 &divide;&nbsp;0.04 + 0.64 &ndash; 1.64 &divide; 0.04</p>",
                    question_hi: "<p>21. दिए गए व्यंजक का सरलीकरण करें।<br>0.07 &times; 0.28 &divide;&nbsp;0.04 + 0.64 &ndash; 1.64 &divide; 0.04</p>",
                    options_en: ["<p>38.56</p>", "<p>- 39.87</p>", 
                                "<p>- 46.48</p>", "<p>36.78</p>"],
                    options_hi: ["<p>38.56</p>", "<p>- 39.87</p>",
                                "<p>- 46.48</p>", "<p>36.78</p>"],
                    solution_en: "<p>21.(b) <br>= 0.07 &times; 0.28 &divide;&nbsp;0.04 + 0.64 - 1.64 &divide; 0.04<br>= 0.07 &times; 7 + 0.64 - 41<br>= 0.49 - 40.36<br>= - 39.87</p>",
                    solution_hi: "<p>21.(b) <br>= 0.07 &times; 0.28 &divide;&nbsp;0.04 + 0.64 - 1.64 &divide; 0.04<br>= 0.07 &times; 7 + 0.64 - 41<br>= 0.49 - 40.36<br>= - 39.87</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. Two pipes A and B can fill a tank in 20 and 30 hours, respectively. Both pipes are opened to fill the tank, but when the tank is one-third full, a leak develops through which one-fourth of the water supplied by both pipes goes out. Find the total time (in hours) taken to fill the tank.</p>",
                    question_hi: "<p>22. दो पाइप A और B एक टंकी को क्रमशः 20 और 30 घंटे में भर सकते हैं। टंकी को भरने के लिए दोनों पाइप खोले जाते हैं, लेकिन जब टंकी एक तिहाई भर जाती है, तो एक रिसाव विकसित होता है जिससे दोनों पाइपों द्वारा भरा गया एक-चौथाई पानी निकल जाता है। टंकी को भरने में लगने वाला कुल समय (घंटे में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>14</p>", 
                                "<p>11<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>14</p>",
                                "<p>11<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>22.(a) <br>Two pipes can separately fill a tank in 20 hrs and 30 hrs respectively<br>Time taken by the two pipes to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>20</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi>&#160;</mi></mrow></mfrac></math> = 12 hours <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of the tank if filled = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>3</mn></mfrac></math> = 4 hours <br>Now , <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> of the supplied water leak out<br><math display=\"inline\"><mo>&#8658;</mo></math> the filler pipes earlier efficiency = 1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> units&nbsp;<br>Work done complete = (12 - 4) &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac></math> = 10<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> hours <br>So, Total time = 4 + 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 14<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> hours</p>",
                    solution_hi: "<p>22.(a) दो पाइप अलग-अलग एक टैंक को क्रमशः 20 घंटे और 30 घंटे में भर सकते हैं<br>दोनों पाइपों द्वारा टंकी को भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>20</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn><mi>&#160;</mi></mrow></mfrac></math> = 12 घंटे <br>यदि टंकी का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> भाग भरा हुआ है = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>3</mn></mfrac></math> = 4 घंटे&nbsp;<br>अब, आपूर्ति किए गए पानी में से <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> लीक हो गए हैं<br><math display=\"inline\"><mo>&#8658;</mo></math> भराव पाइप की पिछली दक्षता = 1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> इकाई<br>पूरा कार्य = (12 - 4) &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac></math>= 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे&nbsp;<br>अत: कुल समय = 4 + 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. A boat covers a distance of 55 km downstream in 5 hours while it takes 11 hours to cover the same distance upstream. What is the speed of the boat?</p>",
                    question_hi: "<p>23. एक नाव धारा की दिशा में 55 km की दूरी 5 घंटे में तय करती है जबकि धारा के विपरीत दिशा में समान दूरी को तय करने में उसे 11 घंटे लगते हैं। नाव की चाल क्या है?</p>",
                    options_en: ["<p>8 km/h</p>", "<p>9 km/h</p>", 
                                "<p>11 km/h</p>", "<p>7 km/h</p>"],
                    options_hi: ["<p>8 km/h</p>", "<p>9 km/h</p>",
                                "<p>11 km/h</p>", "<p>7 km/h</p>"],
                    solution_en: "<p>23.(a) Let the speed of the boat and stream be <math display=\"inline\"><mi>x</mi></math> km/h and y km/h respectively,<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 5<br><math display=\"inline\"><mo>&#8658;</mo></math> x + y = 11 &hellip;&hellip; (i)<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 11<br><math display=\"inline\"><mo>&#8658;</mo></math> x - y = 5 &hellip;&hellip; (ii)<br>Add equation (i) and (ii),<br>2<math display=\"inline\"><mi>x</mi></math> = 16<br><math display=\"inline\"><mi>x</mi></math> = 8 km/h</p>",
                    solution_hi: "<p>23.(a) माना नाव और धारा की गति क्रमशः x किमी/घंटा और y किमी/घंटा है,<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 5<br><math display=\"inline\"><mo>&#8658;</mo></math> x + y = 11 &hellip;&hellip; (i)<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 11<br><math display=\"inline\"><mo>&#8658;</mo></math> x - y = 5 &hellip;&hellip; (ii)<br>समीकरण (i) और (ii) जोड़ें ,<br>2<math display=\"inline\"><mi>x</mi></math> = 16<br><math display=\"inline\"><mi>x</mi></math> = 8 km/h</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. The single discount equivalent to a series discount of 60%, 70% and 80%.</p>",
                    question_hi: "<p>24. 60%, 70% और 80% की क्रमिक छूट किस एकल छूट के बराबर है?</p>",
                    options_en: ["<p>97.6%</p>", "<p>70%</p>", 
                                "<p>85%</p>", "<p>95.5%</p>"],
                    options_hi: ["<p>97.6%</p>", "<p>70%</p>",
                                "<p>85%</p>", "<p>95.5%</p>"],
                    solution_en: "<p>24.(a) Given discount, 60%, 70%, 80%<br>Using formula = <math display=\"inline\"><mi>x</mi></math> + y - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mi mathvariant=\"normal\">y</mi></mrow><mn>100</mn></mfrac></math><br>On solving 60% and 70%<br>Net discount = 60 + 70 - <math display=\"inline\"><mfrac><mrow><mn>60</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 130 - 42 = 88%<br>On solving 88% and 80%<br>Net discount = 88 + 80 - <math display=\"inline\"><mfrac><mrow><mn>88</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 168 - 70.4 = 97.6% <br>So, Equivalent discount of 60%, 70% and 80% is 97.6%</p>",
                    solution_hi: "<p>24.(a) दी गई छूट, 60%, 70%, 80%<br>सूत्र का उपयोग करने पर = <math display=\"inline\"><mi>x</mi></math> + y - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mi mathvariant=\"normal\">y</mi></mrow><mn>100</mn></mfrac></math><br>60% और 70% हल करने पर<br>कुल छूट = 60 + 70 - <math display=\"inline\"><mfrac><mrow><mn>60</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 130 - 42 = 88%<br>88% and 80% हल करने पर<br>कुल छूट = 88 + 80 - <math display=\"inline\"><mfrac><mrow><mn>88</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 168 - 70.4 = 97.6% <br>तो, 60%, 70% और 80% की एकल छूट = 97.6% है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "25. The number 4,29,714 is divisible by:",
                    question_hi: "25. संख्या 4,29,714 निम्नलिखित में से किन संख्‍याओं से विभाज्य है?",
                    options_en: [" 3 and 5", " 6 and 5 ", 
                                " 4 and 6 ", " 3 and 6"],
                    options_hi: [" 3 और 5 ", " 6 और 5 ",
                                " 4 और 6 ", " 3 और 6"],
                    solution_en: "25.(d) <br />Option (a) 3 and 4 (not correct)<br />429714 is divisible by 3 but not divisible by 4<br />Option (b) 6 and 5 (not correct)<br />429714 is divisible by 6 but not divisible by 5<br />Option (c) 4 and 6 (not correct)<br />429714 is divisible by 6 but not divisible by 4<br />Option (d) 3 and 6 (correct)<br />429714 is divisible by both numbers 3 and 6",
                    solution_hi: "25.(d) <br />विकल्प (a) 3 और 4 (सही नहीं)<br />429714 3 से विभाज्य है लेकिन 4 से विभाज्य नहीं है<br />विकल्प (b) 6 और 5 (सही नहीं)<br />429714 6 से विभाज्य है लेकिन 5 से विभाज्य नहीं है<br />विकल्प (c) 4 और 6 (सही नहीं)<br />429714 6 से विभाज्य है लेकिन 4 से विभाज्य नहीं है<br />विकल्प (d) 3 और 6 (सही)<br />429714 संख्या 3 और 6 दोनों से विभाज्य है",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>