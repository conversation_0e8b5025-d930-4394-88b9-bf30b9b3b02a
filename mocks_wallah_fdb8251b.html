<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which is not a component of the finance budget?</p>",
                    question_hi: "<p>1. कौन सा वित्त बजट का एक घटक नहीं है?</p>",
                    options_en: ["<p>Receipts budget</p>", "<p>Appropriation bill</p>", 
                                "<p>Finance bill</p>", "<p>Memorandum expelling grants</p>"],
                    options_hi: ["<p>प्राप्तियां बजट</p>", "<p>विनियोग विधेयक</p>",
                                "<p>वित्त विधेयक</p>", "<p>ज्ञापन निष्कासन अनुदान</p>"],
                    solution_en: "<p>1.(d) Memorandum expelling grants are not a component of the finance budget. The purpose of the financial budget is to estimate the firm\'s cash budget, capital expenditures, and balance sheet line items like assets, liabilities, and owner\'s investment.</p>",
                    solution_hi: "<p>1.(d) ज्ञापन निष्कासन अनुदान वित्त बजट का एक घटक नहीं है। वित्तीय बजट का उद्देश्य फर्म के नकद बजट, पूंजीगत व्यय और बैलेंस शीट लाइन आइटम जैसे संपत्ति, देनदारियों और मालिक के निवेश का अनुमान लगाना है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. Indian Railways has introduced various initiatives for passengers. Rail Bandhu is one of them . It is about:",
                    question_hi: "2. भारतीय रेलवे ने यात्रियों के लिए कई तरह की पहल की है। रेल बंधु उनमें से एक है। इसके बारे में है:",
                    options_en: [" medical officer’s presence in every train", " a rail magazine", 
                                " security guard’s presence in all AC coaches", " website of every rail division"],
                    options_hi: [" प्रत्येक ट्रेन में चिकित्सा अधिकारी की उपस्थिति", " एक रेल पत्रिका",
                                " सभी एसी कोचों में सुरक्षा गार्ड की उपस्थिति", " प्रत्येक रेल मंडल की वेबसाइट"],
                    solution_en: "2.(b)<br />Indian Railways has introduced various initiatives for passengers. Rail Bandhu is one of them . It is about : a rail magazine. It is an Indian Railway’s monthly bilingual magazine that is placed on board all the premium trains i.e. Rajdhani express, Shatabdi express, Duronto express, Gatiman express and other express trains across India.",
                    solution_hi: "2.(b)<br />भारतीय रेलवे ने यात्रियों के लिए कई तरह की पहल की है। रेल बंधु उनमें से एक है। यह इस बारे में है: एक रेल पत्रिका। यह एक भारतीय रेलवे की मासिक द्विभाषी पत्रिका है जिसे पूरे भारत में सभी प्रीमियम ट्रेनों यानी राजधानी एक्सप्रेस, शताब्दी एक्सप्रेस, दुरंतो एक्सप्रेस, गतिमान एक्सप्रेस और अन्य एक्सप्रेस ट्रेनों में रखा जाता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "3. Which of the following articles of the Indian Constitution is directed to establish the right to work, to education and to public assistance in certain cases?",
                    question_hi: "3. भारतीय संविधान के निम्नलिखित में से कौन सा अनुच्छेद कुछ मामलों में काम, शिक्षा और सार्वजनिक सहायता के अधिकार को स्थापित करने के लिए निर्देशित है?",
                    options_en: [" Article 41", " Article 43", 
                                " Article 12", " Article 40"],
                    options_hi: [" अनुच्छेद 41", " अनुच्छेद 43",
                                " अनुच्छेद 12", " अनुच्छेद 40"],
                    solution_en: "3.(a) Article 41 in the constitution of India was added in 1949 which provides for the right to work, education and public assistance in certain cases. Article 43- Living wage, etc, for workers. Article 12-Government and Legislature of each State i.e the Executive and Legislature of the various States of India. All local or other authorities within the territory of India. Article 40-Organisation of village panchayats. ",
                    solution_hi: "3.(a) भारत के संविधान में अनुच्छेद 41 को 1949 में जोड़ा गया था जो कुछ मामलों में काम, शिक्षा और सार्वजनिक सहायता का अधिकार प्रदान करता है। अनुच्छेद 43- श्रमिकों के लिए निर्वाह मजदूरी आदि। अनुच्छेद 12- प्रत्येक राज्य की सरकार और विधायिका अर्थात भारत के विभिन्न राज्यों की कार्यपालिका और विधानमंडल। भारत के राज्यक्षेत्र के भीतर सभी स्थानीय या अन्य प्राधिकरण। अनुच्छेद 40- ग्राम पंचायतों का संगठन।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. The first bullet train is expected to connect which cities of India?",
                    question_hi: "4. पहली बुलेट ट्रेन के भारत के किन शहरों को जोड़ने की उम्मीद है?",
                    options_en: [" Delhi and Lucknow", " Mumbai and Ahmedabad", 
                                " Mumbai and Pune", " Ahmedabad and Surat"],
                    options_hi: [" दिल्ली और लखनऊ", " मुंबई और अहमदाबाद",
                                " मुंबई और पुणे", " अहमदाबाद और सूरत"],
                    solution_en: "4.(b)<br />The first bullet train is expected to connect Mumbai and Ahmedabad in India. Mumbai-Ahmedabad High Speed Rail Corridor is an under-construction high speed rail line connecting India’s economic hub Mumbai with the city of Ahmedabad.",
                    solution_hi: "4.(b)<br />पहली बुलेट ट्रेन के भारत में मुंबई और अहमदाबाद को जोड़ने की उम्मीद है। मुंबई-अहमदाबाद हाई स्पीड रेल कॉरिडोर भारत के आर्थिक केंद्र मुंबई को अहमदाबाद शहर से जोड़ने वाली एक निर्माणाधीन हाई स्पीड रेल लाइन है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. Which of the following is the most polluted city of the world as per the Guinness World Records 2020 analysis of a World Health Organization report?",
                    question_hi: "5. विश्व स्वास्थ्य संगठन की रिपोर्ट के गिनीज वर्ल्ड रिकॉर्ड्स 2020 विश्लेषण के अनुसार निम्नलिखित में से कौन सा दुनिया का सबसे प्रदूषित शहर है?",
                    options_en: [" Kanpur", " Lahore", 
                                " Delhi", " Karachi"],
                    options_hi: [" कानपुर", " लाहौर",
                                " दिल्ली", " कराची"],
                    solution_en: "5.(a)<br />As per the Guinness World Records 2020 analysis of a World Health Organisation report, Kanpur  is the most polluted city of the world. It is a city in Uttar Pradesh with an average PM2.5 level of 173 micrograms/m3 which is 17 times higher than WHO recommended maximum of 10 micrograms/m3.",
                    solution_hi: "5.(a)<br />विश्व स्वास्थ्य संगठन की रिपोर्ट के गिनीज वर्ल्ड रिकॉर्ड्स 2020 के विश्लेषण के अनुसार, कानपुर दुनिया का सबसे प्रदूषित शहर है। यह उत्तर प्रदेश का एक शहर है जिसका औसत PM2.5 स्तर 173 माइक्रोग्राम/m3 है जो WHO द्वारा अनुशंसित अधिकतम 10 माइक्रोग्राम/m3 से 17 गुना अधिक है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Which article deals with the formation of the Finance Commission?",
                    question_hi: "6. कौन सा लेख वित्त आयोग के गठन से संबंधित है?",
                    options_en: [" Article 282", " Article 279", 
                                " Article 280", " Article 281"],
                    options_hi: [" अनुच्छेद 282", " अनुच्छेद 279",
                                " अनुच्छेद 280", " अनुच्छेद 281"],
                    solution_en: "6.(c)<br />Article 280 deals with the formation of the Finance Commission. It is formed to define the financial relations between the central government of India and the individual state governments.",
                    solution_hi: "6.(c)<br />अनुच्छेद 280 वित्त आयोग के गठन से संबंधित है। यह भारत की केंद्र सरकार और अलग-अलग राज्य सरकारों के बीच वित्तीय संबंधों को परिभाषित करने के लिए बनाई गई है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. Which folk dance of the following is not associated with the state of Assam?",
                    question_hi: "7. निम्नलिखित में से कौन सा लोक नृत्य असम राज्य से संबंधित नहीं है?",
                    options_en: [" Nat Puja", " Bihu", 
                                " Kathi", " Chongli"],
                    options_hi: [" नट पूजा", " बिहु",
                                " कैथी", " चोंग्लि"],
                    solution_en: "7.(c)<br />Nat Puja, Bihu, Chongli these are all dance forms of Assam. But Kathi does not belong to Assam. Kathi is a folk dance performed in the state of West Bengal in India. This dance form is noted for its beautiful hand and leg coordination.",
                    solution_hi: "7.(c)<br />नट पूजा, बिहू, चोंगली ये सभी असम के नृत्य रूप हैं। लेकिन काठी असम से संबंधित नहीं है। काठी भारत के पश्चिम बंगाल राज्य में किया जाने वाला एक लोक नृत्य है। यह नृत्य शैली अपने सुंदर हाथ और पैर के समन्वय के लिए विख्यात है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. In October 2019, which noble gas was detected on the lunar exosphere by the Chandrayaan- 2 orbiter?",
                    question_hi: "8. अक्टूबर 2019 में, चंद्रयान- 2 ऑर्बिटर द्वारा चंद्र एक्सोस्फीयर पर किस महान गैस का पता लगाया गया था?",
                    options_en: [" Krypton", " Argon", 
                                " Neon", " Helium"],
                    options_hi: [" क्रिप्टन", " आर्गन",
                                " नियॉन", " हीलियम"],
                    solution_en: "8.(b)<br />In October 2019, an isotope (Argon - 40)  of the noble gas Argon was detected in the lunar exosphere by the Chandrayaan- 2 orbiter. Variation of Argon - 40 observed during one orbit of Chandrayaan 2 during the dayside and nightside of the Moon.",
                    solution_hi: "8.(b)<br />अक्टूबर 2019 में, चंद्रयान- 2 ऑर्बिटर द्वारा चंद्र एक्सोस्फीयर में नोबल गैस आर्गन के एक आइसोटोप (आर्गन - 40) का पता लगाया गया था। आर्गन की भिन्नता - 40 चंद्रयान 2 की एक कक्षा के दौरान चंद्रमा के दिन और रात के दौरान देखी गई।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. Which is the least frictional force of the following?",
                    question_hi: "9. निम्नलिखित में से सबसे कम घर्षण बल कौन सा है?",
                    options_en: [" Sliding", " Fluid", 
                                " Static", " Rolling"],
                    options_hi: [" स्लाइडिंग", " द्रव",
                                " स्टेटिक", " रोलिंग"],
                    solution_en: "9.(d)<br />Rolling Friction is the least frictional force among the four types of frictional forces given below.<br />Whereas Static frictional force is the strongest among all.<br />Static > Sliding > Rolling ;<br />Fluid friction is friction that acts on objects that are moving through a fluid.",
                    solution_hi: "9.(d)<br />नीचे दिए गए चार प्रकार के घर्षण बलों में से रोलिंग घर्षण सबसे कम घर्षण बल है।<br />जबकि स्थैतिक घर्षण बल सभी में सबसे मजबूत है।<br />स्टेटिक> स्लाइडिंग> रोलिंग;<br />द्रव घर्षण वह घर्षण है जो किसी द्रव के माध्यम से गतिमान वस्तुओं पर कार्य करता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. Kaiga Atomic Power station is situated in ______ state of India.",
                    question_hi: "10. कैगा परमाणु ऊर्जा स्टेशन भारत के ______ राज्य में स्थित है।",
                    options_en: [" Andhra Pradesh", " Karnataka", 
                                " Kerala", " Tamilnadu"],
                    options_hi: [" आंध्र प्रदेश", " कर्नाटक",
                                " केरल", " तमिलनाडु"],
                    solution_en: "10.(b)<br />Kaiga Atomic Power station is situated in the Karnataka state of India, situated near the river Kali, Uttara Kannada district. The plant has been in operation since March 2000 and is operated by the Nuclear Power Corporation Limited of India (NPCIL).",
                    solution_hi: "10.(b)<br />कैगा परमाणु ऊर्जा स्टेशन भारत के कर्नाटक राज्य में स्थित है, जो उत्तर कन्नड़ जिले के काली नदी के पास स्थित है। यह संयंत्र मार्च 2000 से प्रचालन में है और इसका संचालन न्यूक्लियर पावर कॉरपोरेशन लिमिटेड ऑफ इंडिया (NPCIL) द्वारा किया जाता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. Ozone layer is responsible for preventing harmful UV rays to enter the earth’s atmosphere. Which of the hydrocarbons is/are not responsible for depletion of the ozone layer?",
                    question_hi: "11. ओजोन परत हानिकारक यूवी किरणों को पृथ्वी के वायुमंडल में प्रवेश करने से रोकने के लिए जिम्मेदार है। ओजोन परत के ह्रास के लिए कौन-सा/से हाइड्रोकार्बन जिम्मेदार नहीं है/हैं?",
                    options_en: [" Asphalt", " propellants", 
                                " Refrigerants", " Foam-blowing agents"],
                    options_hi: [" डामर", " प्रणोदक",
                                " रेफ्रिजरेंट", " फोम उड़ाने वाले एजेंट"],
                    solution_en: "11.(a) <br />Ozone layer is responsible for preventing harmful UV rays from entering the earth’s atmosphere. Asphalt is not responsible for depletion of the ozone layer. Asphalt, also known as Bitumen, is a sticky, black, highly viscous liquid or semi solid form of petroleum.",
                    solution_hi: "11.(a) <br />ओजोन परत हानिकारक यूवी किरणों को पृथ्वी के वायुमंडल में प्रवेश करने से रोकने के लिए जिम्मेदार है। डामर ओजोन परत के क्षरण के लिए जिम्मेदार नहीं है। डामर, जिसे बिटुमेन भी कहा जाता है, पेट्रोलियम का चिपचिपा, काला, अत्यधिक चिपचिपा तरल या अर्ध ठोस रूप है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. Who said , “Political freedom is the life breath of a nation.”",
                    question_hi: "12. किसने कहा, \"राजनीतिक स्वतंत्रता एक राष्ट्र की प्राणवायु है।\"",
                    options_en: [" Rabindranath Tagore", " Gopal Krishna Gokhale", 
                                " Mahatma Gandhi", " Aurobindo Ghose"],
                    options_hi: [" रवींद्रनाथ टैगोर", " गोपाल कृष्ण गोखले",
                                " महात्मा गांधी", " अरबिंदो घोष"],
                    solution_en: "12.(d)<br />Aurobindo Ghose said , “Political freedom is the life-breath of a nation.” He was an Indian philosopher, yoga guru, maharishi, poet and Indian nationalist.",
                    solution_hi: "12.(d)<br />अरबिंदो घोष ने कहा, \"राजनीतिक स्वतंत्रता एक राष्ट्र की जीवन-श्वास है।\" वह एक भारतीय दार्शनिक, योग गुरु, महर्षि, कवि और भारतीय राष्ट्रवादी थे।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. The value of ‘g’ is maximum at:",
                    question_hi: "13. \'g\' का मान अधिकतम होता है:",
                    options_en: [" Tropic of cancer", " Equator", 
                                " Tropic of capricorn", " poles"],
                    options_hi: [" कर्क रेखा", " भूमध्य रेखा",
                                " मकर रेखा", " ध्रुव"],
                    solution_en: "13.(d)<br />The value of ‘g’ is maximum at the poles. Due to Earth’s oblateness the distance of centre from poles is less than distance of centre from equator, so as r is less in poles obviously g will be higher. Therefore, gravity is maximum at poles and minimum at the equators.",
                    solution_hi: "13.(d)<br />ध्रुवों पर \'g\' का मान अधिकतम होता है। पृथ्वी के तिरछेपन के कारण ध्रुवों से केंद्र की दूरी भूमध्य रेखा से केंद्र की दूरी से कम है, इसलिए ध्रुवों में r कम होने से स्पष्ट रूप से g अधिक होगा। इसलिए, गुरुत्वाकर्षण ध्रुवों पर अधिकतम और भूमध्य रेखा पर न्यूनतम होता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. Devdas, a famous novel, was written by:",
                    question_hi: "14. देवदास, एक प्रसिद्ध उपन्यास, किसके द्वारा लिखा गया था:",
                    options_en: [" Samudra Gupta", " Bankim Chandra Chattopadhyay", 
                                " Sarat Chandra Chattopadhyay", " Rabindranath Tagore"],
                    options_hi: [" समुद्र गुप्ता", " बंकिम चंद्र चट्टोपाध्याय",
                                " शरत चंद्र चट्टोपाध्याय", " रवींद्रनाथ टैगोर"],
                    solution_en: "14.(c)<br />Devdas is a Bengali romance novel,  written by  Sarat Chandra Chattopadhyay. Sarat chandra Chattopadhyay was a bengali novelist and short story writer of the early 20th century.",
                    solution_hi: "14.(c)<br />देवदास एक बंगाली रोमांस उपन्यास है, जिसे शरत चंद्र चट्टोपाध्याय ने लिखा है। शरत चंद्र चट्टोपाध्याय 20वीं सदी की शुरुआत के एक बंगाली उपन्यासकार और लघु कथाकार थे।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. The 2012 Summer Olympics were held in which city?",
                    question_hi: "15. 2012 के ग्रीष्मकालीन ओलंपिक किस शहर में आयोजित किए गए थे?",
                    options_en: [" London", " Tokyo", 
                                " Rio", " Paris"],
                    options_hi: [" लंदन", " टोक्यो",
                                " रियो", " पेरिस"],
                    solution_en: "15.(a)<br />The 2012 Summer Olympics were held in London city.",
                    solution_hi: "15.(a)<br />2012 के ग्रीष्मकालीन ओलंपिक लंदन शहर में आयोजित किए गए थे।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. Gautam Buddha is associated with which famous place of the following?",
                    question_hi: "16. गौतम बुद्ध निम्नलिखित में से किस प्रसिद्ध स्थान से संबंधित हैं?",
                    options_en: [" Pawapuri", " Srirangapatnam", 
                                " Barodoli", " Lumbini"],
                    options_hi: [" पावापुरी", " श्रीरंगपट्टनम",
                                " बड़ौदोलिक", " लुंबिनी"],
                    solution_en: "16.(d)<br />Gautam Buddha is associated with the famous place called Lumbini. The Lord Buddha was born in 623 B.C. in the famous gardens of Lumbini, which soon became a place of pilgrimage, which is now developed as a Buddhist pilgrimage centre, where the archaeological remains associated with the birth of Lord Buddha form a central feature.",
                    solution_hi: "16.(d)<br />गौतम बुद्ध का संबंध लुंबिनी नामक प्रसिद्ध स्थान से है। भगवान बुद्ध का जन्म 623 ई.पू. लुंबिनी के प्रसिद्ध उद्यानों में, जो जल्द ही तीर्थ स्थान बन गया, जिसे अब एक बौद्ध तीर्थ केंद्र के रूप में विकसित किया गया है, जहां भगवान बुद्ध के जन्म से जुड़े पुरातात्विक अवशेष एक केंद्रीय विशेषता बनाते हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. Alps is among the largest mountains of the world. Which European country doesn’t have an Alps range located within its geographical boundary?",
                    question_hi: "17. आल्प्स दुनिया के सबसे बड़े पहाड़ों में से एक है। किस यूरोपीय देश की भौगोलिक सीमा के भीतर आल्प्स श्रेणी नहीं है?",
                    options_en: [" Germany", " Liechtenstein", 
                                " Belgium", " France"],
                    options_hi: [" जर्मनी", " लिकटेंस्टीन",
                                " बेल्जियम", " फ्रांस"],
                    solution_en: "17.(c)<br />The Alps are among the largest mountains in the world. The European country that doesn’t have an Alps range located within its geographical boundary amongst the following options is Belgium.",
                    solution_hi: "17.(c)<br />आल्प्स दुनिया के सबसे बड़े पहाड़ों में से हैं। यूरोपीय देश जिसकी भौगोलिक सीमा के भीतर आल्प्स श्रेणी नहीं है, निम्नलिखित विकल्पों में से बेल्जियम है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. In which of the following cities is the Forest Survey of India located?",
                    question_hi: "18. भारतीय वन सर्वेक्षण निम्नलिखित में से किस शहर में स्थित है?",
                    options_en: [" Kolkata", " Dehradun", 
                                " Lucknow", " Delhi"],
                    options_hi: [" कोलकाता", " देहरादून",
                                " लखनऊ", " दिल्ली"],
                    solution_en: "18.(b)<br />Forest Survey of India, founded in June 1981 and headquartered at Dehradun in Uttarakhand, is a government of India - Ministry of Environment, Forest and climate change organisation for conducting forest surveys, studies and research.",
                    solution_hi: "18.(b)<br />भारतीय वन सर्वेक्षण, जून 1981 में स्थापित किया गया और इसका मुख्यालय उत्तराखंड में देहरादून में है, भारत सरकार - पर्यावरण मंत्रालय, वन और जलवायु परिवर्तन संगठन वन सर्वेक्षण, अध्ययन और अनुसंधान करने के लिए है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. Bhutan does not share its boundary with which of the following Indian states?",
                    question_hi: "19. भूटान निम्नलिखित में से किस भारतीय राज्य के साथ अपनी सीमा साझा नहीं करता है?",
                    options_en: [" Assam", " West Bengal", 
                                " Sikkim", " Odisha"],
                    options_hi: [" असम", " पश्चिम बंगाल",
                                " सिक्किम", " ओडिशा"],
                    solution_en: "19.(d)<br />Bhutan does not share its boundary with Odisha.  Bhutan shares its border with the Indian states of Arunachal Pradesh in its east, Sikkim in the west and Assam and West Bengal in the south. It also shares borders with Nepal and China.",
                    solution_hi: "19.(d)<br />भूटान अपनी सीमा ओडिशा के साथ साझा नहीं करता है। भूटान की सीमा भारतीय राज्यों के पूर्व में अरुणाचल प्रदेश, पश्चिम में सिक्किम और दक्षिण में असम और पश्चिम बंगाल से लगती है। इसकी सीमा नेपाल और चीन से भी लगती है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. Which of the following was NOT a part of the revolutionary movement for India Independence?",
                    question_hi: "20. निम्नलिखित में से कौन भारत की स्वतंत्रता के लिए क्रांतिकारी आंदोलन का हिस्सा नहीं था?",
                    options_en: [" Jugantar", " Young Party", 
                                " Anushilan Samiti", " Abhinav Bharat"],
                    options_hi: [" जुगंटारी", " युवा पार्टी",
                                " अनुशीलन समिति", " अभिनव भारती"],
                    solution_en: "20.(b)<br />The Young Party was not a part of the revolutionary movement for Indian Independence.<br />The Young Party is a populist political party in Turkey.",
                    solution_hi: "20.(b)<br />यंग पार्टी भारतीय स्वतंत्रता के क्रांतिकारी आंदोलन का हिस्सा नहीं थी।<br />यंग पार्टी तुर्की में एक लोकलुभावन राजनीतिक दल है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. Which of the following schedules of the Indian Constitution enumerates the allocation of Rajya Sabha seats to states or union territories?",
                    question_hi: "21. भारतीय संविधान की निम्नलिखित में से कौन सी अनुसूची राज्यों या केंद्र शासित प्रदेशों को राज्यसभा सीटों के आवंटन की गणना करती है?",
                    options_en: [" Tenth Schedule", " Fourth Schedule", 
                                " First Schedule", " Sixth Schedule"],
                    options_hi: [" दसवीं अनुसूची", " चौथी अनुसूची",
                                " पहली अनुसूची", " छठी अनुसूची"],
                    solution_en: "21.(b)<br />Fourth Schedule of the Indian Constitution enumerates the allocation of Rajya Sabha seats to states or union territories.",
                    solution_hi: "21.(b)<br />भारतीय संविधान की चौथी अनुसूची राज्यों या केंद्र शासित प्रदेशों को राज्यसभा सीटों के आवंटन की गणना करती है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. The International Boundary line which separates North Korea and South Korea is popularly known as:",
                    question_hi: "22. उत्तर कोरिया और दक्षिण कोरिया को अलग करने वाली अंतर्राष्ट्रीय सीमा रेखा को किस नाम से जाना जाता है?",
                    options_en: ["  Military Demarcation Line", " 49th parallel", 
                                " 24th parallel", " Radcliffe line"],
                    options_hi: [" सैन्य सीमांकन रेखा", " 49 वां समानांतर",
                                " 24 वां समानांतर", " रैडक्लिफ लाइन"],
                    solution_en: "22.(a)<br />The International Boundary line which separates North Korea and South Korea is popularly known as :  Military Demarcation Line. it is also called 38th parallel, popular name given to the latitude <math display=\"inline\"><msup><mrow><mn>38</mn></mrow><mrow><mi>o</mi></mrow></msup></math>N in east Asia roughly demarcates North Korea and South Korea.",
                    solution_hi: "22.(a)<br />अंतर्राष्ट्रीय सीमा रेखा जो उत्तर कोरिया और दक्षिण कोरिया को अलग करती है, उसे सैन्य सीमांकन रेखा के रूप में जाना जाता है। इसे 38वां समानांतर भी कहा जाता है, पूर्वी एशिया में अक्षांश 38oN को दिया गया लोकप्रिय नाम मोटे तौर पर उत्तर कोरिया और दक्षिण कोरिया का सीमांकन करता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "23. What is the full form of ODBC in terms of computing?",
                    question_hi: "23. कंप्यूटिंग के संदर्भ में ODBC का पूर्ण रूप क्या है?",
                    options_en: [" Open data base compliance", " Open data base correction", 
                                " Open data base connection", " Open data base connectivity"],
                    options_hi: [" Open data base compliance", " Open data base correction",
                                " Open data base connection", " Open data base connectivity"],
                    solution_en: "23.(d)<br />The full form of ODBC in terms of computing is  Open database connectivity, by Microsoft, which allows applications to access data in database management systems (DBMS) using SQL as a standard for accessing the data. ",
                    solution_hi: "23.(d)<br />कंप्यूटिंग के संदर्भ में ODBC का पूर्ण रूप Microsoft द्वारा ओपन डेटाबेस कनेक्टिविटी है, जो अनुप्रयोगों को डेटा तक पहुँचने के लिए मानक के रूप में SQL का उपयोग करके डेटाबेस प्रबंधन प्रणालियों (DBMS) में डेटा तक पहुँचने की अनुमति देता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "24. Which reform/ act was passed during the term of Lord Canning in 1856?",
                    question_hi: "24. 1856 में लॉर्ड कैनिंग के कार्यकाल के दौरान कौन सा सुधार/अधिनियम पारित किया गया था?",
                    options_en: [" Widow remarriage", " Morley Minto Reforms", 
                                " Doctrine of lapse", " Prohibition of Sati"],
                    options_hi: [" विधवा पुनर्विवाह", " मॉर्ले मिंटो सुधार",
                                " चूक का सिद्धांत", " सती का निषेध"],
                    solution_en: "24.(a)<br />The Hindu Widows’ Remarriage reform/ Act was passed during the term of Lord Canning on 16th July in 1856, which was drafted by his predecessor Lord Dalhousie before the rebellion.",
                    solution_hi: "24.(a)<br />हिंदू विधवाओं का पुनर्विवाह सुधार / अधिनियम 16 ​​जुलाई 1856 को लॉर्ड कैनिंग के कार्यकाल के दौरान पारित किया गया था, जिसे उनके पूर्ववर्ती लॉर्ड डलहौजी ने विद्रोह से पहले तैयार किया था।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. Who among the following was awarded with the Dhyan Chand award in 2020?",
                    question_hi: "25. निम्नलिखित में से किसे 2020 में ध्यानचंद पुरस्कार से सम्मानित किया गया?",
                    options_en: [" Sikki Reddy", " Rajat Chauhan", 
                                " Nandan Bal", " Arokia Rajiv"],
                    options_hi: [" सिक्की रेड्डी", " रजत चौहान",
                                " नंदन बली", " अरोकिया राजीव"],
                    solution_en: "25.(c)<br />The Indian Tennis player Nandan P. Bal was awarded with the Dhyan Chand award in 2020. “Dhyan Chand Award” is India’s highest award for lifetime achievements in sports and games.",
                    solution_hi: "25.(c)<br />भारतीय टेनिस खिलाड़ी नंदन पी. बाल को 2020 में ध्यानचंद पुरस्कार से सम्मानित किया गया था। \"ध्यान चंद पुरस्कार\" खेल और खेलों में जीवन भर की उपलब्धियों के लिए भारत का सर्वोच्च पुरस्कार है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. When was the currency Euro (common European currency) adopted in Europe?",
                    question_hi: "26. यूरोप में मुद्रा यूरो (सामान्य यूरोपीय मुद्रा) को कब अपनाया गया था?",
                    options_en: [" Jan, 2002", " Jan, 2003", 
                                " Jan , 2005", " Jan 1999"],
                    options_hi: [" जनवरी, 2002", " जनवरी, 2003",
                                " जनवरी, 2005", " जनवरी 1999"],
                    solution_en: "26.(d)<br />The currency Euro (common European currency) was adopted in Europe in 1999, was introduced as a non-cash monetary unit and the currency notes and coins appeared in participating countries on January1, 2002.",
                    solution_hi: "26.(d)<br />मुद्रा यूरो (सामान्य यूरोपीय मुद्रा) को 1999 में यूरोप में अपनाया गया था, एक गैर-नकद मौद्रिक इकाई के रूप में पेश किया गया था और मुद्रा नोट और सिक्के 1 जनवरी, 2002 को भाग लेने वाले देशों में दिखाई दिए।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. The parliament of Norway is called:",
                    question_hi: "27. नॉर्वे की संसद को कहा जाता है:",
                    options_en: [" The National Diet", " Folketing", 
                                " Stortinget", " Parliament"],
                    options_hi: [" राष्ट्रीय आहार", " लोककथा",
                                " स्टॉर्टिंगेट", " संसद"],
                    solution_en: "27.(c)<br />The parliament of Norway is called : Stortinget, is the supreme legislature of Norway, established in 1814 by the constitution of Norway. It is located in Oslo.",
                    solution_hi: "27.(c)<br />नॉर्वे की संसद को कहा जाता है: स्टॉर्टिंगेट, नॉर्वे की सर्वोच्च विधायिका है, जिसकी स्थापना 1814 में नॉर्वे के संविधान द्वारा की गई थी। यह ओस्लो में स्थित है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. In which year was the battle of Haldighati fought?",
                    question_hi: "28. हल्दीघाटी का युद्ध किस वर्ष लड़ा गया था?",
                    options_en: [" 1764 AD", " 1761 AD", 
                                " 1576 AD", " 1756 AD"],
                    options_hi: [" 1764 AD", " 1761 AD",
                                " 1576 AD", " 1756 AD"],
                    solution_en: "28.(c)<br />In the year of 1576 AD the battle of Haldighati was fought between the Armies of Maharana Pratap, the Rana of Mewar and the Mughal emperor Akbar’s forces, led by Man Singh I of Amber. ",
                    solution_hi: "28.(c)<br />1576 ईस्वी में हल्दीघाटी की लड़ाई महाराणा प्रताप की सेनाओं, मेवाड़ के राणा और मुगल सम्राट अकबर की सेनाओं के बीच अंबर के मान सिंह प्रथम के नेतृत्व में लड़ी गई थी।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "29. Tirumala Raya was the founder of which of the following dynasties in ancient India?",
                    question_hi: "29. तिरुमाला राय प्राचीन भारत में निम्नलिखित में से किस राजवंश के संस्थापक थे?",
                    options_en: [" Saluva", " Sangama", 
                                " Tuluva", " Aravidu"],
                    options_hi: [" सालुवा", " संगम:",
                                " तुलुवा", " अरविदु"],
                    solution_en: "29.(d)<br />Tirumala Raya was the founder of the Aravidu dynasties in ancient India, which was the fourth and last Hindu dynasty to rule the Vijayanagara Empire in South India. ",
                    solution_hi: "29.(d)<br />तिरुमाला राय प्राचीन भारत में अरविदु राजवंशों के संस्थापक थे, जो दक्षिण भारत में विजयनगर साम्राज्य पर शासन करने वाला चौथा और अंतिम हिंदू राजवंश था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Which of the following is the largest artificial freshwater lake  in India?",
                    question_hi: "30. निम्नलिखित में से कौन भारत की सबसे बड़ी कृत्रिम मीठे पानी की झील है?",
                    options_en: [" Shivaji Sagar Lake", " Kolleru Lake", 
                                " Kodai Lake", " Nainital lake"],
                    options_hi: [" शिवाजी सागर झील", " कोल्लेरू झील",
                                " कोडाई झील", " नैनीताल झील"],
                    solution_en: "30.(a)<br />Shivaji Sagar Lake is the largest artificial freshwater lake  in India. It is a reservoir in the state of Maharashtra, India. The lake was formed after the Koyna river was impounded by the Koyna Dam. It has a length of 50 km and depth of 80 m.",
                    solution_hi: "30.(a)<br />शिवाजी सागर झील भारत की सबसे बड़ी कृत्रिम मीठे पानी की झील है। यह भारत के महाराष्ट्र राज्य में एक जलाशय है। कोयना बांध द्वारा कोयना नदी को जब्त किए जाने के बाद झील का निर्माण हुआ था। इसकी लंबाई 50 किमी और गहराई 80 मीटर है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "31. Param Shivay, the first supercomputer assembled indigenously, was installed at ______.",
                    question_hi: "31. परम शिवाय, स्वदेश में असेंबल किया गया पहला सुपर कंप्यूटर, ________ में स्थापित किया गया था।",
                    options_en: [" IIT Kharagpur", " IISC, Benguluru", 
                                " IIT BHU", " IISER Pune"],
                    options_hi: ["  IIT  खड़गपुर", " IISC, बेंगलुरु",
                                " IIT BHU", "  IISER पुणे"],
                    solution_en: "31.(c)<br />Param Shivay, the first supercomputer assembled indigenously, was installed at IIT BHU, followed by PARAM Shakti, PARAM Brahma, PARAM Yukti, PARAM Sanganak at IIT Kharagpur, IISER Pune, JNCASR Bengaluru,  and IIT Kanpur respectively.",
                    solution_hi: "31.(c)<br />परम शिवाय, स्वदेशी रूप से असेंबल किया गया पहला सुपर कंप्यूटर, IIT BHU में स्थापित किया गया था, इसके बाद IIT खड़गपुर, IISER पुणे, JNCASR बेंगलुरु और IIT कानपुर में क्रमशः परम शक्ति, परम ब्रह्मा, परम युक्ति, परम संगनक को स्थापित किया गया था।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. Which of the following is not a plant hormone?",
                    question_hi: "32. निम्नलिखित में से कौन-सा पादप हॉर्मोन नहीं है?",
                    options_en: [" Ethylene", " Abscisic Acid", 
                                " Gibberellins", " Prolactin"],
                    options_hi: [" एथिलीन", " एब्सिसिक एसिड",
                                " गिबरेलिन्स", " प्रोलैक्टिन"],
                    solution_en: "32.(d)<br />Prolactin is not a plant hormone. It is a hormone made by the pituitary gland, a small gland at the base of the brain. Prolactin causes the breasts to grow and make milk during pregnancy and after birth.",
                    solution_hi: "32.(d)<br />प्रोलैक्टिन एक पादप हार्मोन नहीं है। यह पिट्यूटरी ग्रंथि द्वारा निर्मित एक हार्मोन है, जो मस्तिष्क के आधार पर एक छोटी ग्रंथि है। प्रोलैक्टिन गर्भावस्था के दौरान और जन्म के बाद स्तनों को बढ़ने और दूध बनाने का कारण बनता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "33. When is the ‘world health day’ celebrated?",
                    question_hi: "33. विश्व स्वास्थ्य दिवस कब मनाया जाता है?",
                    options_en: [" 14th April", " 7th April", 
                                " 8th April", " 10th April"],
                    options_hi: [" 14 अप्रैल", " 7 अप्रैल",
                                " 8 अप्रैल", " 10 अप्रैल"],
                    solution_en: "33.(b)<br />‘World health day’ is celebrated every year on 7th April, under the sponsorship of the World Health Organization. In 1948, the WHO held the first World Health Assembly.",
                    solution_hi: "33.(b)<br />विश्व स्वास्थ्य संगठन के प्रायोजन के तहत हर साल 7 अप्रैल को \'विश्व स्वास्थ्य दिवस\' मनाया जाता है। 1948 में, WHO ने पहली विश्व स्वास्थ्य सभा आयोजित की।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. Which of the following is not true with regard to lokayukta?",
                    question_hi: "34. लोकायुक्त के संबंध में निम्नलिखित में से कौन सा सत्य नहीं है?",
                    options_en: [" He /she can investigate into the grievance of citizens due to injustice and hardship caused by maladministration.", " He /she can supervise the citizen grievance investigation", 
                                " He/she can ask for an inquiry into the allegation of abuse of power by public servants.", " Lokayukta can’t question Prime Minister on the allegation of abuse of power"],
                    options_hi: [" वह कुप्रशासन के कारण हुए अन्याय और कठिनाई के कारण नागरिकों की शिकायतों की जांच कर सकता है।", " वह नागरिक शिकायत जांच की निगरानी कर सकता है",
                                " वह लोक सेवकों द्वारा सत्ता के दुरुपयोग के आरोपों की जांच के लिए कह सकता है।", " सत्ता के दुरुपयोग के आरोप पर लोकायुक्त प्रधानमंत्री से सवाल नहीं कर सकता"],
                    solution_en: "34.(d)<br />The statement : ‘Lokayukta can’t question the Prime Minister on the allegation of abuse of power’ is not true. Lokayuktas provide for inquiry/investigation into complaints of corruption against public servants. He protects citizen’s right against mal-administration corruption delay inefficiency etc.",
                    solution_hi: "34.(d)<br />कथन: \'लोकायुक्त सत्ता के दुरुपयोग के आरोप पर प्रधानमंत्री से सवाल नहीं कर सकता\' सत्य नहीं है। लोकायुक्त लोक सेवकों के खिलाफ भ्रष्टाचार की शिकायतों की जांच/जांच का प्रावधान करते हैं। वह नागरिक अधिकारों की रक्षा कुशासन भ्रष्टाचार देरी अक्षमता आदि से करता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. According to the ‘All India Tiger Estimation report 2018’. Which Indian state has the highest number of tigers?",
                    question_hi: "35. \'ऑल इंडिया टाइगर एस्टीमेशन रिपोर्ट 2018\' के अनुसार। भारत के किस राज्य में बाघों की संख्या सबसे अधिक है?",
                    options_en: [" Uttarakhand", " Karnataka", 
                                " Madhya Pradesh", " Chhattisgarh"],
                    options_hi: [" उत्तराखंड", " कर्नाटक",
                                " मध्य प्रदेश", " छत्तीसगढ़"],
                    solution_en: "35.(c)<br />According to the ‘All India Tiger Estimation report 2018’. the Indian state of Madhya Pradesh    (526 royal Bengal tigers ) has the highest number of tigers, followed by Karnataka and Uttarakhand.",
                    solution_hi: "35.(c)<br />\'ऑल इंडिया टाइगर एस्टीमेशन रिपोर्ट 2018\' के अनुसार। भारतीय राज्य मध्य प्रदेश (526 शाही बंगाल बाघ) में बाघों की संख्या सबसे अधिक है, इसके बाद कर्नाटक और उत्तराखंड हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. Which of the following is not a version of Android?",
                    question_hi: "36. निम्नलिखित में से कौन Android का संस्करण नहीं है?",
                    options_en: [" Marshmallow", " Gingerbread", 
                                " Oreo", " Candy"],
                    options_hi: [" मार्शमैलो", " जिंजरब्रेड",
                                " ओरियो", " कैंडी"],
                    solution_en: "36.(d)<br />Candy is not a version of Android. Android is a mobile operating system based on a forked version of Linux. Unlike iOS, android is open-source software, allowing developers and manufacturers to use the software without paying any royalty.",
                    solution_hi: "36.(d)<br />कैंडी, एंड्रॉइड का संस्करण नहीं है। एंड्रॉइड एक मोबाइल ऑपरेटिंग सिस्टम है जो लिनक्स के फोर्कड वर्जन पर आधारित है। IOS के विपरीत, एंड्रॉइड ओपन-सोर्स सॉफ्टवेयर है, जो डेवलपर्स और निर्माताओं को बिना किसी रॉयल्टी का भुगतान किए सॉफ्टवेयर का उपयोग करने की अनुमति देता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. Long Radio waves were discovered by",
                    question_hi: "37. लंबी रेडियो तरंगों की खोज किसके द्वारा की गई?",
                    options_en: [" Guglielmo Marconi", " Isaac Newton", 
                                " Heinrich Hertz", " W. Rontgen"],
                    options_hi: [" गुग्लिल्मो मार्कोनी", " आइजैक न्यूटन",
                                " हेनरिक हर्ट्ज", " डब्ल्यू रोंटजेन"],
                    solution_en: "37.(a)<br />Long Radio waves were discovered by  Guglielmo Marconi, the Italian inventor who created a practical radio wave based wireless telegraph system. Heinrich Hertz proved the existence of radio waves in the late 1880s.",
                    solution_hi: "37.(a)<br />लंबी रेडियो तरंगों की खोज इतालवी आविष्कारक गुग्लिल्मो मार्कोनी ने की थी, जिन्होंने एक व्यावहारिक रेडियो तरंग आधारित वायरलेस टेलीग्राफ सिस्टम बनाया था। 1880 के दशक के अंत में हेनरिक हर्ट्ज़ ने रेडियो तरंगों के अस्तित्व को साबित किया।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38.___________ is an Integrated Library Management Software developed by National Informatics Centre(NIC), Department of Electronics & Information Technology. The application is useful for automation of in-house activities of libraries and to provide various online member services.",
                    question_hi: "38. ___________ राष्ट्रीय सूचना विज्ञान केंद्र (एनआईसी), इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी विभाग द्वारा विकसित एक एकीकृत पुस्तकालय प्रबंधन सॉफ्टवेयर है। एप्लिकेशन पुस्तकालयों की आंतरिक गतिविधियों के स्वचालन और विभिन्न ऑनलाइन सदस्य सेवाएं प्रदान करने के लिए उपयोगी है।",
                    options_en: ["  Digi LOCKER", " eOffice", 
                                " DISHA", " e-Granthalaya"],
                    options_hi: [" डिजी लॉकर", " ईऑफिस",
                                " दिशा", " ई-ग्रंथालय"],
                    solution_en: "38.(d)<br />e-Granthalaya is an Integrated Library Management Software developed by National Informatics Centre(NIC), Department of Electronics & Information Technology. The application is useful for automation of in-house activities of libraries and to provide various online member services.",
                    solution_hi: "38.(d)<br />ई-ग्रंथालय राष्ट्रीय सूचना विज्ञान केंद्र (NIC), इलेक्ट्रॉनिक्स और सूचना प्रौद्योगिकी विभाग द्वारा विकसित एक एकीकृत पुस्तकालय प्रबंधन सॉफ्टवेयर है। एप्लिकेशन पुस्तकालयों की आंतरिक गतिविधियों के स्वचालन और विभिन्न ऑनलाइन सदस्य सेवाएं प्रदान करने के लिए उपयोगी है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39. Which equipment is used to transform electrical energy into mechanical energy?",
                    question_hi: "39. विद्युत ऊर्जा को यांत्रिक ऊर्जा में बदलने के लिए किस उपकरण का उपयोग किया जाता है?",
                    options_en: [" Battery", " Electric motor", 
                                " Candle", " Photo cell"],
                    options_hi: [" बैटरी", " इलेक्ट्रिक मोटर",
                                " मोमबत्ती", " फोटो सेल"],
                    solution_en: "39.(b)<br />Electric motor is used to transform electric energy into mechanical energy. It operates by using the interaction between the motor’s magnetic field and electric current in a wire winding to generate force in the form of torque applied on the motor’s shaft.",
                    solution_hi: "39.(b)<br />विद्युत ऊर्जा को यांत्रिक ऊर्जा में बदलने के लिए विद्युत मोटर का उपयोग किया जाता है। यह मोटर के शाफ्ट पर लगाए गए टॉर्क के रूप में बल उत्पन्न करने के लिए वायर वाइंडिंग में मोटर के चुंबकीय क्षेत्र और विद्युत प्रवाह के बीच परस्पर क्रिया का उपयोग करके संचालित होता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "40. NASA launched a mission to mars in June, 2020. What is the duration of this mission?",
                    question_hi: "40. नासा ने जून, 2020 में मंगल के लिए एक मिशन लॉन्च किया। इस मिशन की अवधि क्या है?",
                    options_en: [" At least 10 mars years", " At least 1 mars year", 
                                " At least 7.5 mars years", " At least 5 mars years"],
                    options_hi: [" कम से कम 10 मंगल वर्ष", " कम से कम 1 मंगल वर्ष",
                                " कम से कम 7.5 मंगल वर्ष", " कम से कम 5 मंगल वर्ष"],
                    solution_en: "40.(b)<br />NASA launched a mission to Mars in June, 2020. The duration of this mission is at least 1 mars year. The Mars 2020 perseverance rover will search for signs of ancient microbial life, which will advance NASA’s quest to explore the past habitability of Mars.",
                    solution_hi: "40.(b)<br />नासा ने जून, 2020 में मंगल ग्रह पर एक मिशन लॉन्च किया। इस मिशन की अवधि कम से कम 1 मंगल वर्ष है। मार्स 2020 दृढ़ता रोवर प्राचीन माइक्रोबियल जीवन के संकेतों की खोज करेगा, जो मंगल की पिछली निवास क्षमता का पता लगाने के लिए नासा की खोज को आगे बढ़ाएगा।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>