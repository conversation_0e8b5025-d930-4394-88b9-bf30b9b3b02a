<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #7209b7;
            --secondary: #4361ee;
            --accent: #f72585;
            --success: #06d6a0;
            --warning: #ffd166;
            --danger: #ef476f;
            --light: #f8f9fa;
            --dark: #212529;
        }
        body {
            background-color: #f0f2f5;
            font-size: 16px;
            padding-bottom: 60px; /* Space for fixed navigation */
        }
        .navbar {
            background: linear-gradient(135deg, var(--primary), var(--secondary)) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,.1);
            padding: 0.5rem 1rem;
        }
        .navbar-brand {
            font-size: 1.1rem;
            margin-right: 0;
        }
        .navbar .container-fluid {
            justify-content: space-between;
        }
        .controls-group {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
        }
        .timer-display {
            background: linear-gradient(135deg, #4361ee, #3a0ca3);
            color: white;
            border-radius: 50px;
            box-shadow: 0 3px 8px rgba(0,0,0,.1);
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            white-space: nowrap;
        }
        .nav-btn {
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            margin-left: 0.25rem;
        }
        .question-nav {
            position: fixed;
            top: 56px;
            right: 0;
            height: calc(100vh - 56px);
            width: 280px;
            transform: translateX(280px);
            transition: transform .3s;
            z-index: 100;
            background: #fff;
            box-shadow: -5px 0 15px rgba(0,0,0,.1);
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .main-content {
            transition: margin .3s;
            min-height: calc(100vh - 116px);
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.5;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(32px, 1fr));
            gap: 6px;
        }
        .q-box {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid #dee2e6;
            background: #fff;
            transition: all .2s;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 5px rgba(0,0,0,.1);
        }
        .q-box.attempted {
            background-color: var(--secondary);
            color: white;
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,.15);
        }
        .card {
            border-radius: 12px;
            overflow: hidden;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
        }
        .option-label {
            border-radius: 8px;
            transition: all .2s;
            border: 1px solid #dee2e6;
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }
        .option-label:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0,0,0,.05);
        }
        .form-check-input:checked + .option-label {
            border-color: var(--primary);
            background-color: rgba(114,9,183,.05);
        }
        .btn-primary {
            background-color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary {
            color: var(--primary);
            border-color: var(--primary);
        }
        .btn-outline-primary:hover {
            background-color: var(--primary);
            color: white;
        }
        /* Bottom fixed navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,.1);
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            z-index: 90;
        }
        @media (min-width: 992px) {
            .main-content.with-nav {
                margin-right: 280px;
            }
            .question-nav {
                transform: translateX(0);
            }
            .nav-toggle-btn {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(30px, 1fr));
            }
            .option-label {
                padding: 0.625rem 0.875rem;
                font-size: 0.95rem;
            }
            .question-text {
                font-size: 1rem;
            }
            .navbar-brand {
                font-size: 1rem;
            }
            .timer-display {
                font-size: 0.8125rem;
                padding: 0.2rem 0.6rem;
            }
            .nav-btn {
                padding: 0.2rem 0.6rem;
                font-size: 0.8125rem;
            }
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header navbar -->
    <nav class="navbar navbar-dark sticky-top">
        <div class="container-fluid px-2">
            <a class="navbar-brand fw-bold" href="#"><i class="fas fa-graduation-cap me-1"></i></a>
            <div class="controls-group">
                <!-- Timer -->
                <div class="timer-display me-1 fw-bold" id="timer">
                    <i class="far fa-clock me-1"></i><span id="timer-display">90:00</span>
                </div>
                <!-- Submit button -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Language toggle -->
                <button class="btn btn-light btn-sm nav-btn me-1" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="btn btn-light btn-sm nav-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </nav>
    <!-- Main content -->
    <div class="main-content" id="main-content">
        <div class="container py-3">
            <!-- Question counter -->
            <div class="fw-bold text-center mb-3" id="question-counter">Question 1 of 25</div>
            <!-- Questions will be displayed here -->
            <div id="questions-container"></div>
        </div>
    </div>
    <!-- Bottom navigation for mobile -->
    <div class="bottom-nav d-lg-none">
        <button class="btn btn-outline-primary px-3" onclick="prevQuestion()">
            <i class="fas fa-chevron-left me-1"></i> Previous
        </button>
        <button class="btn btn-outline-primary px-3" onclick="nextQuestion()">
            Next <i class="fas fa-chevron-right ms-1"></i>
        </button>
    </div>
    <!-- Question navigation sidebar -->
    <div class="question-nav" id="question-nav">
        <div class="p-3">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="m-0">Questions</h5>
                <div>
                    <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                    <span class="badge bg-secondary" id="total-count">25</span>
                    <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleNav()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <!-- Section selector moved here -->
            <div class="mb-3">
                <label for="section-selector" class="form-label small text-muted">Jump to Section</label>
                <select class="form-select form-select-sm" id="section-selector" onchange="switchSection(this.value)">
                    <option value="all">All Sections</option>
                </select>
            </div>
            <div class="question-grid" id="question-boxes"></div>
        </div>
    </div>
    <!-- Results modal -->
    <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0">
                <div class="modal-header border-0" style="background:linear-gradient(135deg,var(--primary),var(--secondary));color:white">
                    <h5 class="modal-title">Test Results</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="row text-center g-2">
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#4361ee,#3a0ca3);color:white">
                                <h3 id="score-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Score</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#06d6a0,#1b9aaa);color:white">
                                <h3 id="correct-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Correct</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#ef476f,#f72585);color:white">
                                <h3 id="incorrect-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Incorrect</div>
                            </div>
                        </div>
                        <div class="col-6 mt-2">
                            <div class="p-3 rounded shadow-sm" style="background:linear-gradient(135deg,#6c757d,#495057);color:white">
                                <h3 id="unattempted-value" class="mb-0 fw-bold">0</h3>
                                <div class="small">Unattempted</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. The following sentence has been split into four segments. Identify the segment which <span style=\"font-family: Times New Roman;\">contains a grammatical error. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Until I was / still a little weak, / I decided to walk home / from the metro station. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>from the metro station</p>\n", "<p>I decided to walk home</p>\n", 
                                "<p>still a little weak</p>\n", "<p>Until I was</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Until is used to describe an effect that will take place &amp; Still is used to denote the continuity of the action. In the given sentence, the narrator described the </span><span style=\"font-family: Times New Roman;\">continuity of the action of walking</span><span style=\"font-family: Times New Roman;\">. So, we will remove the word &lsquo;until&rsquo;. Hence, &lsquo;I was still a little weak&rsquo; is the most appropriate answer</span></p>\n",
                    solution_hi: "<p>1.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Until is used to describe an effect that will take place &amp; Still is used to denote the continuity of the action. In the given sentence, the narrator described the </span><span style=\"font-family: Times New Roman;\">continuity of the action of walking</span><span style=\"font-family: Times New Roman;\">. So, we will remove the word &lsquo;until&rsquo;. Hence, &lsquo;I was still a little weak&rsquo; is the most appropriate answer</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate option to fill in the blank.</p>\r\n<p><span style=\"font-family: Times New Roman;\">People have overcome poverty, drug addiction, abuse, divorce, mental illness, and virtually </span><span style=\"font-family: Times New Roman;\">every ______ known to man. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>Defiance</p>\n", "<p>Dispute</p>\n", 
                                "<p>Objection</p>\n", "<p>Challenge</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>2.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Challenge means </span><span style=\"font-family: Times New Roman;\">something new and difficult that forces you to make a lot of effort. The given sentence states that </span><span style=\"font-family: Times New Roman;\">people have overcome every difficult thing </span><span style=\"font-family: Times New Roman;\">that forces them to make a lot of effort. </span><span style=\"font-family: Times New Roman;\">Hence, &lsquo;challenge&rsquo; is the most appropriate answer.</span></p>\n",
                    solution_hi: "<p>2.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Challenge means </span><span style=\"font-family: Times New Roman;\">something new and difficult that forces you to make a lot of effort. The given sentence states that </span><span style=\"font-family: Times New Roman;\">people have overcome every difficult thing </span><span style=\"font-family: Times New Roman;\">that forces them to make a lot of effort. </span><span style=\"font-family: Times New Roman;\">Hence, &lsquo;challenge&rsquo; is the most appropriate answer.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p>3. Select the correct passive form of the given sentence. </span></p> <p><span style=\"font-family:Times New Roman\">You can see vast valleys sloping into the distance. </span></p>",
                    question_hi: "",
                    options_en: [" <p> Vast valleys had been seen sloping into the distance. </span></p>", " <p> Vast valleys can be seen sloping into the distance. </span></p>", 
                                " <p> Vast valleys are seen sloping into the distance. </span></p>", " <p> Vast valleys will be seen sloping into the distance. </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>3.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Vast valleys </span><span style=\"font-family:Times New Roman\">had been seen</span><span style=\"font-family:Times New Roman\"> sloping into the distance. (Incorrect Tense) </span></p> <p><span style=\"font-family:Times New Roman\">Vast valleys can be seen sloping into the distance.(Correct) </span></p> <p><span style=\"font-family:Times New Roman\">Vast valleys </span><span style=\"font-family:Times New Roman\">are seen</span><span style=\"font-family:Times New Roman\"> sloping into the distance. (Incorrect Tense) </span></p> <p><span style=\"font-family:Times New Roman\">Vast valleys </span><span style=\"font-family:Times New Roman\">will be seen</span><span style=\"font-family:Times New Roman\"> sloping into the distance. (Incorrect Tense) </span></p>",
                    solution_hi: " <p>3.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Vast valleys </span><span style=\"font-family:Times New Roman\">had been seen</span><span style=\"font-family:Times New Roman\"> sloping into the distance. (Incorrect Tense) </span></p> <p><span style=\"font-family:Times New Roman\">Vast valleys can be seen sloping into the distance.(Correct) </span></p> <p><span style=\"font-family:Times New Roman\">Vast valleys </span><span style=\"font-family:Times New Roman\">are seen</span><span style=\"font-family:Times New Roman\"> sloping into the distance. (Incorrect Tense) </span></p> <p><span style=\"font-family:Times New Roman\">Vast valleys </span><span style=\"font-family:Times New Roman\">will be seen</span><span style=\"font-family:Times New Roman\"> sloping into the distance. (Incorrect Tense) </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate option to substitute the underlined segment in the given <span style=\"font-family: Times New Roman;\">sentence. If there is no need to substitute it, select &lsquo;No substitution&rsquo;. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">No other place in India gets </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Times New Roman;\">much rainfal</span></strong></span><span style=\"font-family: Times New Roman;\"><span style=\"text-decoration: underline;\"><strong>l</strong></span> </span><span style=\"font-family: Times New Roman;\">as Mawsynram. </span></p>",
                    question_hi: "",
                    options_en: ["<p>the most rainfall</p>", "<p>as much rainfall</p>", 
                                "<p>No substitution</p>", "<p>more rainfall</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">The structure &lsquo;as much as&rsquo; is used to say that an amount is as large as another amount. The given sentence states that the amount of rainfall that Mawsyrnram gets is as large as in any other place in India. Hence, &lsquo;as much rainfall as&rsquo; is the most appropriate answer. </span></p>",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">The structure &lsquo;as much as&rsquo; is used to say that an amount is as large as another amount. The given sentence states that the amount of rainfall that Mawsyrnram gets is as large as in any other place in India. Hence, &lsquo;as much rainfall as&rsquo; is the most appropriate answer. </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the option that will improve the underlined segment in the given sentence. In case no <span style=\"font-family: Times New Roman;\">improvement is needed, select \'No improvement\' </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">It is wise to insure one&rsquo;s home</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Times New Roman;\">on</span></strong></span><span style=\"font-family: Times New Roman;\"><strong> </strong>fire. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>No improvement</p>\n", "<p>in</p>\n", 
                                "<p>by</p>\n", "<p>against</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>5.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">We generally &lsquo;insure&rsquo; our life &amp; our belongings </span><span style=\"font-family: Times New Roman;\">against</span><span style=\"font-family: Times New Roman;\"> some mishappenings like an accident, death, etc. Similarly, the given sentence states that it is wise to insure one&rsquo;s home against fire. Hence, &lsquo;against&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>5.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">We generally &lsquo;insure&rsquo; our life &amp; our belongings </span><span style=\"font-family: Times New Roman;\">against</span><span style=\"font-family: Times New Roman;\"> some mishappenings like an accident, death, etc. Similarly, the given sentence states that it is wise to insure one&rsquo;s home against fire. Hence, &lsquo;against&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6. Select the most appropriate ANTONYM of the given word.  </span></p> <p><span style=\"font-family:Times New Roman\">PUNGENT </span></p>",
                    question_hi: "",
                    options_en: [" <p> biting </span></p>", " <p> sour </span></p>", 
                                " <p> mild </span></p>", " <p> sharp </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>6.(c) Mild</span></p> <p><span style=\"font-family:Times New Roman\">Mild- </span><span style=\"font-family:Times New Roman\">not strong</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Pungent- </span><span style=\"font-family:Times New Roman\">very strong</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Biting- </span><span style=\"font-family:Times New Roman\">to cut or attack somebody/something with your teeth</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Sour- </span><span style=\"font-family:Times New Roman\">having a sharp taste like that of a lemon</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Sharp- </span><span style=\"font-family:Times New Roman\">very great and sudden</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    solution_hi: " <p>6.(c) Mild</span></p> <p><span style=\"font-family:Times New Roman\">Mild- </span><span style=\"font-family:Times New Roman\">not strong</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Pungent- </span><span style=\"font-family:Times New Roman\">very strong</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Biting- </span><span style=\"font-family:Times New Roman\">to cut or attack somebody/something with your teeth</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Sour- </span><span style=\"font-family:Times New Roman\">having a sharp taste like that of a lemon</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Sharp- </span><span style=\"font-family:Times New Roman\">very great and sudden</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: " <p>7. Fill in the blank with the most appropriate option.  </span></p> <p><span style=\"font-family:Times New Roman\">The craze for taking selfies has only increased our ______ with our own image. </span></p>",
                    question_hi: "",
                    options_en: [" <p> impression </span></p>", " <p> expression </span></p>", 
                                " <p> possession </span></p>", " <p> obsession </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>7.(d) Obsession</span></p> <p><span style=\"font-family:Times New Roman\">Obsession means </span><span style=\"font-family:Times New Roman\">a person or thing that you think about too much. The given sentence states that </span><span style=\"font-family:Times New Roman\">the craze for taking selfies has only increased our thinking about ourselves. Hence, ‘obsession’ is the most appropriate answer. </span></p>",
                    solution_hi: " <p>7.(d) Obsession</span></p> <p><span style=\"font-family:Times New Roman\">Obsession means </span><span style=\"font-family:Times New Roman\">a person or thing that you think about too much. The given sentence states that </span><span style=\"font-family:Times New Roman\">the craze for taking selfies has only increased our thinking about ourselves. Hence, ‘obsession’ is the most appropriate answer. </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. In the given sentence, identify the segment which contains the grammatical error.</p>\r\n<p><span style=\"font-family: Times New Roman;\">The government has announced a gradual reboot of air travel who was halted two months </span><span style=\"font-family: Times New Roman;\">ago due to lockdown. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>reboot of air travel</p>\n", "<p>due to lockdown</p>\n", 
                                "<p>The government has announced</p>\n", "<p>who was halted</p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Replace &ldquo;who&rdquo; with &ldquo;which&rdquo;</span><span style=\"font-family: Times New Roman;\">. In the given sentence, the halting of air travel </span><span style=\"font-family: Times New Roman;\">due to lockdown was</span><span style=\"font-family: Times New Roman;\"> exact. </span><span style=\"font-family: Times New Roman;\">Hence, &lsquo;which was halted&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>8.(d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Replace &ldquo;who&rdquo; with &ldquo;which&rdquo;</span><span style=\"font-family: Times New Roman;\">. In the given sentence, the halting of air travel </span><span style=\"font-family: Times New Roman;\">due to lockdown was</span><span style=\"font-family: Times New Roman;\"> exact. </span><span style=\"font-family: Times New Roman;\">Hence, &lsquo;which was halted&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p>9. Select the word which means the same as the group of words given.  </span></p> <p><span style=\"font-family:Times New Roman\">An imaginary, perfect state or place </span></p>",
                    question_hi: "",
                    options_en: [" <p> utopia </span></p>", " <p> dystopia </span></p>", 
                                " <p> nostalgia </span></p>", " <p> arcadia </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>9.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Utopia- an imaginary, perfect state or place </span></p> <p><span style=\"font-family:Times New Roman\">Dystopia- </span><span style=\"font-family:Times New Roman\">an imagined state or society</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Nostalgia- </span><span style=\"font-family:Times New Roman\">a feeling of pleasure, mixed with sadness, when you think of happy times in the past</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Arcadia- a mountainous, landlocked region of Greece  </span></p>",
                    solution_hi: " <p>9.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Utopia- an imaginary, perfect state or place </span></p> <p><span style=\"font-family:Times New Roman\">Dystopia- </span><span style=\"font-family:Times New Roman\">an imagined state or society</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Nostalgia- </span><span style=\"font-family:Times New Roman\">a feeling of pleasure, mixed with sadness, when you think of happy times in the past</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Arcadia- a mountainous, landlocked region of Greece  </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: " <p>10. Select the most appropriate meaning of the given idiom.  </span></p> <p><span style=\"font-family:Times New Roman\">A bolt from the blue </span></p>",
                    question_hi: "",
                    options_en: [" <p> something causing a head injury </span></p>", " <p> something that is hotly debated </span></p>", 
                                " <p> a strange dream that wakes one up suddenly </span></p>", " <p> something that happens suddenly </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>10.(d)</span></p> <p><span style=\"font-family:Times New Roman\">A bolt from the blue- something that happens suddenly </span></p> <p><span style=\"font-family:Times New Roman\">Example: The news of his rejection in the interview came like a </span><span style=\"font-family:Times New Roman\">bolt from the blue</span><span style=\"font-family:Times New Roman\">.</span></p>",
                    solution_hi: " <p>10.(d)</span></p> <p><span style=\"font-family:Times New Roman\">A bolt from the blue- something that happens suddenly </span></p> <p><span style=\"font-family:Times New Roman\">Example: The news of his rejection in the interview came like a </span><span style=\"font-family:Times New Roman\">bolt from the blue</span><span style=\"font-family:Times New Roman\">.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the INCORRECTLY spelt word.</p>\n",
                    question_hi: "",
                    options_en: ["<p>ridge</p>\n", "<p>rifel</p>\n", 
                                "<p>ride</p>\n", "<p>right</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>11.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Rifle is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">ridge - a long, narrow hilltop, mountain range, or watershed.</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>11.(b)</p>\r\n<p><span style=\"font-family: Times New Roman;\">Rifle is the correct spelling.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">ridge - a long, narrow hilltop, mountain range, or watershed.</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Examine the four jumbled sentences. Out of the given options, pick the one that gives their <span style=\"font-family: Times New Roman;\">correct order. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A. That abandoned house is the childhood home of the director Achal Misra, who recreates </span><span style=\"font-family: Times New Roman;\">it in this semi-autobiographical film. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B. It is spread over three decades. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">C. &lsquo;Gamak Ghar&rsquo; is a unique movie telling the story of a home. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">D. These are from 1998 to 2019, wherein the film observes the house age as it is gradually </span><span style=\"font-family: Times New Roman;\">abandoned. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>CBDA</p>\n", "<p>ABCD</p>\n", 
                                "<p>DBCA</p>\n", "<p>BACD</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>12.(a) CBDA</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence C will be the starting line as it contains the main idea of the parajumble i.e the name of the movie &lsquo;Gamak Ghar&rsquo;. However, sentence B states that the movie spread over three decades. So, B will follow C. Further, Sentence D tells about that three decades which were from 1998 to 2019 &amp; Sentence A tells about that abandoned house is the childhood home of the director Achal Misra. So, A will follow D. Going through the options, option a has the correct sequence. </span></p>\n",
                    solution_hi: "<p>12.(a) CBDA</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence C will be the starting line as it contains the main idea of the parajumble i.e the name of the movie &lsquo;Gamak Ghar&rsquo;. However, sentence B states that the movie spread over three decades. So, B will follow C. Further, Sentence D tells about that three decades which were from 1998 to 2019 &amp; Sentence A tells about that abandoned house is the childhood home of the director Achal Misra. So, A will follow D. Going through the options, option a has the correct sequence. </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p>13. Select the most appropriate synonym of the given word.  </span></p> <p><span style=\"font-family:Times New Roman\">RESPLENDENT </span></p>",
                    question_hi: "",
                    options_en: [" <p> dazzling </span></p>", " <p> unattractive </span></p>", 
                                " <p> dull </span></p>", " <p> withered </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>13.(a) Dazzling</span></p> <p><span style=\"font-family:Times New Roman\">Dazzling- </span><span style=\"font-family:Times New Roman\">to make somebody unable to see for a short time</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Resplendent- </span><span style=\"font-family:Times New Roman\">brightly colored in an attractive and impressive way</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Unattractive- </span><span style=\"font-family:Times New Roman\">not attractive, pretty, or pleasant to look at</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Dull- </span><span style=\"font-family:Times New Roman\">not interesting or exciting, boring</span><span style=\"font-family:Times New Roman\">   </span></p> <p><span style=\"font-family:Times New Roman\">Withered- </span><span style=\"font-family:Times New Roman\">to become dry and die; to make a plant do this</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    solution_hi: " <p>13.(a) Dazzling</span></p> <p><span style=\"font-family:Times New Roman\">Dazzling- </span><span style=\"font-family:Times New Roman\">to make somebody unable to see for a short time</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Resplendent- </span><span style=\"font-family:Times New Roman\">brightly colored in an attractive and impressive way</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Unattractive- </span><span style=\"font-family:Times New Roman\">not attractive, pretty, or pleasant to look at</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Dull- </span><span style=\"font-family:Times New Roman\">not interesting or exciting, boring</span><span style=\"font-family:Times New Roman\">   </span></p> <p><span style=\"font-family:Times New Roman\">Withered- </span><span style=\"font-family:Times New Roman\">to become dry and die; to make a plant do this</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14. Select the most appropriate meaning of the given idiom.  </span></p> <p><span style=\"font-family:Times New Roman\">Cost an arm and a leg </span></p>",
                    question_hi: "",
                    options_en: [" <p> Be easily available </span></p>", " <p> Be extremely expensive </span></p>", 
                                " <p> Be worthy of the cost </span></p>", " <p> Be rather cheap </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>14.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Cost an arm and a leg- be extremely expensive  </span></p> <p><span style=\"font-family:Times New Roman\">Example: The price of that car costs Rahul </span><span style=\"font-family:Times New Roman\">an arm and a leg.</span></p>",
                    solution_hi: " <p>14.(b)</span></p> <p><span style=\"font-family:Times New Roman\">Cost an arm and a leg- be extremely expensive  </span></p> <p><span style=\"font-family:Times New Roman\">Example: The price of that car costs Rahul </span><span style=\"font-family:Times New Roman\">an arm and a leg.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: " <p>15. Select the most appropriate ANTONYM of the given word.  </span></p> <p><span style=\"font-family:Times New Roman\">INORDINATE </span></p>",
                    question_hi: "",
                    options_en: [" <p> exorbitant </span></p>", " <p> irrational </span></p>", 
                                " <p> disproportionate </span></p>", " <p> reasonable </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>15.(d) Reasonable</span></p> <p><span style=\"font-family:Times New Roman\">Reasonable- </span><span style=\"font-family:Times New Roman\">acceptable and appropriate in a particular situation</span></p> <p><span style=\"font-family:Times New Roman\">Inordinate- </span><span style=\"font-family:Times New Roman\">much greater than usual or expected</span></p> <p><span style=\"font-family:Times New Roman\">Exorbitant- </span><span style=\"font-family:Times New Roman\">much more expensive than it should be</span></p> <p><span style=\"font-family:Times New Roman\">Irrational- </span><span style=\"font-family:Times New Roman\">not based on reason or clear thought</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Disproportionate- </span><span style=\"font-family:Times New Roman\">too large or too small when compared to something else</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    solution_hi: " <p>15.(d) Reasonable</span></p> <p><span style=\"font-family:Times New Roman\">Reasonable- </span><span style=\"font-family:Times New Roman\">acceptable and appropriate in a particular situation</span></p> <p><span style=\"font-family:Times New Roman\">Inordinate- </span><span style=\"font-family:Times New Roman\">much greater than usual or expected</span></p> <p><span style=\"font-family:Times New Roman\">Exorbitant- </span><span style=\"font-family:Times New Roman\">much more expensive than it should be</span></p> <p><span style=\"font-family:Times New Roman\">Irrational- </span><span style=\"font-family:Times New Roman\">not based on reason or clear thought</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Disproportionate- </span><span style=\"font-family:Times New Roman\">too large or too small when compared to something else</span><span style=\"font-family:Times New Roman\"> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: " <p>16. Select the option which means the same as the group of words given.  </span></p> <p><span style=\"font-family:Times New Roman\">So small or unimportant as to be not worth considering </span></p>",
                    question_hi: "",
                    options_en: [" <p> negligible </span></p>", " <p> invincible </span></p>", 
                                " <p> feasible </span></p>", " <p> plausible </span></p> <p><span style=\"font-family:Times New Roman\">  </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>16.(a) Negligible</span></p> <p><span style=\"font-family:Times New Roman\">Negligible- so small or unimportant as to be not worth considering </span></p> <p><span style=\"font-family:Times New Roman\">Invincible-</span><span style=\"font-family:Times New Roman\">too strong or powerful to be defeated</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Feasible- </span><span style=\"font-family:Times New Roman\">possible to do</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Plausible- </span><span style=\"font-family:Times New Roman\">that you can believe, reasonable</span></p>",
                    solution_hi: " <p>16.(a) Negligible</span></p> <p><span style=\"font-family:Times New Roman\">Negligible- so small or unimportant as to be not worth considering </span></p> <p><span style=\"font-family:Times New Roman\">Invincible-</span><span style=\"font-family:Times New Roman\">too strong or powerful to be defeated</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Feasible- </span><span style=\"font-family:Times New Roman\">possible to do</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">Plausible- </span><span style=\"font-family:Times New Roman\">that you can believe, reasonable</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: " <p>17. Select the most appropriate synonym of the given word.  </span></p> <p><span style=\"font-family:Times New Roman\">BULL-HEADED </span></p>",
                    question_hi: "",
                    options_en: [" <p> headstrong </span></p>", " <p> muddle-headed </span></p>", 
                                " <p> clear-headed </span></p>", " <p> weak-minded </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>17.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Bull-Headed- headstrong    </span></p> <p><span style=\"font-family:Times New Roman\">Muddle-headed- </span><span style=\"font-family:Times New Roman\">mentally disorganized or confused.</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Clear-headed- </span><span style=\"font-family:Times New Roman\">able to think clearly, especially if there is a problem</span><span style=\"font-family:Times New Roman\">  </span></p>",
                    solution_hi: " <p>17.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Bull-Headed- headstrong    </span></p> <p><span style=\"font-family:Times New Roman\">Muddle-headed- </span><span style=\"font-family:Times New Roman\">mentally disorganized or confused.</span><span style=\"font-family:Times New Roman\">  </span></p> <p><span style=\"font-family:Times New Roman\">Clear-headed- </span><span style=\"font-family:Times New Roman\">able to think clearly, especially if there is a problem</span><span style=\"font-family:Times New Roman\">  </span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the correct indirect form of the given sentence.</p>\r\n<p><span style=\"font-family: Times New Roman;\">&ldquo;I am going to walk to the market to check which shops are open,&rdquo; said Anant. </span></p>",
                    question_hi: "",
                    options_en: ["<p>Anant said that he was going to walk to the market to check which shops were open.</p>", "<p>Anant said that he is going to walk to the market to check which shops were open.</p>", 
                                "<p>Anant said that he went for a walk to the market to check which shops were open.</p>", "<p>Anant said that he will be going to walk to the market to check which shops were open.</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>18.(a)</p>\r\n<p><span style=\"font-weight: 400;\">(a) Anant said that he was going to walk to the market to check which shops were open. (Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) Anant said that he </span><span style=\"font-weight: 400;\">is going</span><span style=\"font-weight: 400;\"> to walk to the market to check which shops were open. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) Anant said that he </span><span style=\"font-weight: 400;\">went </span><span style=\"font-weight: 400;\">for a walk to the market to check which shops were open. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) Anant said that he </span><span style=\"font-weight: 400;\">will be going</span><span style=\"font-weight: 400;\"> to walk to the market to check which shops were open. (Incorrect Tense)</span></p>",
                    solution_hi: "<p>18.(a)</p>\r\n<p><span style=\"font-weight: 400;\">(a) Anant said that he was going to walk to the market to check which shops were open. (Correct)</span></p>\r\n<p><span style=\"font-weight: 400;\">(b) Anant said that he </span><span style=\"font-weight: 400;\">is going</span><span style=\"font-weight: 400;\"> to walk to the market to check which shops were open. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(c) Anant said that he </span><span style=\"font-weight: 400;\">went </span><span style=\"font-weight: 400;\">for a walk to the market to check which shops were open. (Incorrect Tense)</span></p>\r\n<p><span style=\"font-weight: 400;\">(d) Anant said that he </span><span style=\"font-weight: 400;\">will be going</span><span style=\"font-weight: 400;\"> to walk to the market to check which shops were open. (Incorrect Tense)</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: " <p>19. Select the INCORRECTLY spelt word. </span></p>",
                    question_hi: "",
                    options_en: [" <p> acidentaly </span></p>", " <p> changeable </span></p>", 
                                " <p> possession </span></p>", " <p> tomorrow </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>19.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Accidentally is the correct spelling.</span></p>",
                    solution_hi: " <p>19.(a)</span></p> <p><span style=\"font-family:Times New Roman\">Accidentally is the correct spelling.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Given below are four jumbled sentences. Out of the given options, pick the one that gives <span style=\"font-family: Times New Roman;\">their correct order. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">A. To kill a whale, hunters used spears called harpoons. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">B. Whale hunting is not easy. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">C. These harpoons had very long and strong lines tied to them. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">D. It was extremely difficult and risky before modern methods and machines began to be used. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>BDAC</p>\n", "<p>BADC</p>\n", 
                                "<p>DBAC</p>\n", "<p>DCBA</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>20.(a) BDAC</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence B will be the starting line as it contains the main idea of the parajumble i.e. Whale hunting. However, Sentence D states that it was extremely difficult before modern methods and machines began to be </span><span style=\"font-family: Times New Roman;\">used. So, D will follow B. Further, Sentence A states that to kill a whale, hunters used spears called harpoons </span><span style=\"font-family: \'Times New Roman\';\">&amp; Sentence C states that these harpoons had very long and strong lines tied to them. So, C will follow A. Going through the options, option a has the correct sequence.</span></p>\n",
                    solution_hi: "<p>20.(a) BDAC</p>\r\n<p><span style=\"font-family: Times New Roman;\">Sentence B will be the starting line as it contains the main idea of the parajumble i.e. Whale hunting. However, Sentence D states that it was extremely difficult before modern methods and machines began to be </span><span style=\"font-family: Times New Roman;\">used. So, D will follow B. Further, Sentence A states that to kill a whale, hunters used spears called harpoons </span><span style=\"font-family: \'Times New Roman\';\">&amp; Sentence C states that these harpoons had very long and strong lines tied to them. So, C will follow A. Going through the options, option a has the correct sequence.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Cloze Test :</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Many hundreds of years ago, a Brahmin lived in a town in Northern India. He spent many years (1) ______ the ancient books and talking to the learned priests (2)______ came to visit the </span><span style=\"font-family: Times New Roman;\">city. After much effort, he (3) ______ the knowledge of a certain spell. He was (4)______ proud </span><span style=\"font-family: Times New Roman;\">of this and guarded his secret (5)______ . </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 1.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>studying</p>\n", "<p>study</p>\n", 
                                "<p>to study</p>\n", "<p>studied</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>21.(a) Studying</p>\r\n<p><span style=\"font-family: Times New Roman;\">Studying means spending time learning about something. </span><span style=\"font-family: Times New Roman;\">The given passage states that he spent many years learning something about the ancient books. Hence, &lsquo;studying&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>21.(a) Studying</p>\r\n<p><span style=\"font-family: Times New Roman;\">Studying means spending time learning about something. </span><span style=\"font-family: Times New Roman;\">The given passage states that he spent many years learning something about the ancient books. Hence, &lsquo;studying&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22.Cloze Test :</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Many hundreds of years ago, a Brahmin lived in a town in Northern India. He spent many </span><span style=\"font-family: Times New Roman;\">years (1)______ the ancient books and talking to the learned priests (2)______ came to visit the </span><span style=\"font-family: Times New Roman;\">city. After much effort, he (3) ______ the knowledge of a certain spell. He was (4)______ proud </span><span style=\"font-family: Times New Roman;\">of this and guarded his secret (5)______ . </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 2.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>who</p>\n", "<p>that</p>\n", 
                                "<p>which</p>\n", "<p>whom</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>22.(a) Who</p>\r\n<p><span style=\"font-family: Times New Roman;\">Who is used for saying exactly which person or what kind of person you are talking about like </span><span style=\"font-family: Times New Roman;\">the learned priests in the given passage. Hence, &lsquo;who&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>22.(a) Who</p>\r\n<p><span style=\"font-family: Times New Roman;\">Who is used for saying exactly which person or what kind of person you are talking about like </span><span style=\"font-family: Times New Roman;\">the learned priests in the given passage. Hence, &lsquo;who&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.Cloze Test :</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Many hundreds of years ago, a Brahmin lived in a town in Northern India. He spent many </span><span style=\"font-family: Times New Roman;\">years (1)______ the ancient books and talking to the learned priests (2)______ came to visit the </span><span style=\"font-family: Times New Roman;\">city. After much effort, he (3) ______ the knowledge of a certain spell. He was (4)______ proud </span><span style=\"font-family: Times New Roman;\">of this and guarded his secret (5)______ . </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> Select the most appropriate option for blank no. 3.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>took</p>\n", "<p>found</p>\n", 
                                "<p>obtained</p>\n", "<p>used</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>23.(c) Obtained</p>\r\n<p><span style=\"font-family: Times New Roman;\">Obtain means to get something. </span><span style=\"font-family: Times New Roman;\">The given passage states that he gets the knowledge of a certain spell.Hence, &lsquo;obtained&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>23.(c) Obtained</p>\r\n<p><span style=\"font-family: Times New Roman;\">Obtain means to get something. </span><span style=\"font-family: Times New Roman;\">The given passage states that he gets the knowledge of a certain spell.Hence, &lsquo;obtained&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. Cloze Test :</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Many hundreds of years ago, a Brahmin lived in a town in Northern India. He spent many </span><span style=\"font-family: Times New Roman;\">years (1)______ the ancient books and talking to the learned priests (2)______ came to visit the </span><span style=\"font-family: Times New Roman;\">city. After much effort, he (3) ______ the knowledge of a certain spell. He was (4)______ proud </span><span style=\"font-family: Times New Roman;\">of this and guarded his secret (5)______ . </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 4.</span></p>\n",
                    question_hi: "",
                    options_en: ["<p>very</p>\n", "<p>a lot</p>\n", 
                                "<p>much</p>\n", "<p>many</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>24.(a) Very</p>\r\n<p><span style=\"font-family: Times New Roman;\">Very is used to show positive sense in a sentence &amp; in the given passage the word proud shows a positive sense. Hence, &lsquo;very&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>24.(a) Very</p>\r\n<p><span style=\"font-family: Times New Roman;\">Very is used to show positive sense in a sentence &amp; in the given passage the word proud shows a positive sense. Hence, &lsquo;very&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Cloze Test :</p>\r\n<p><span style=\"font-family: Times New Roman;\">In the following passage some words have been deleted. Fill in the blanks with the help of the </span><span style=\"font-family: Times New Roman;\">alternatives given. Select the most appropriate option for each blank. </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Many hundreds of years ago, a Brahmin lived in a town in Northern India. He spent many </span><span style=\"font-family: Times New Roman;\">years (1)______ the ancient books and talking to the learned priests (2)______ came to visit the </span><span style=\"font-family: Times New Roman;\">city. After much effort, he (3) ______ the knowledge of a certain spell. He was (4)______ proud </span><span style=\"font-family: Times New Roman;\">of this and guarded his secret (5)______ . </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Select the most appropriate option for blank no. 5. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>carefully</p>\n", "<p>excitedly</p>\n", 
                                "<p>deeply</p>\n", "<p>gradually</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>25.(a) Carefully</p>\r\n<p><span style=\"font-family: Times New Roman;\">Carefully means with great attention. The given passage states that he was very proud of this and guarded his secret with great attention. Hence, &lsquo;carefully&rsquo; is the most appropriate answer. </span></p>\n",
                    solution_hi: "<p>25.(a) Carefully</p>\r\n<p><span style=\"font-family: Times New Roman;\">Carefully means with great attention. The given passage states that he was very proud of this and guarded his secret with great attention. Hence, &lsquo;carefully&rsquo; is the most appropriate answer. </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML - more compact
            let html = `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="question-text mb-3">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = 'option-label';
                // When reviewing, highlight user's choice and correct answer
                if (submitted) {
                    if (opt === question.correct) {
                        optionClass += ' border-success bg-success bg-opacity-10';
                    }
                    if (answers[question.id] === opt && opt !== question.correct) {
                        optionClass += ' border-danger bg-danger bg-opacity-10';
                    }
                }
                html += `
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${answers[question.id] === opt ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="form-check-label w-100 ${optionClass}" for="opt-${index}-${opt}">
                            ${submitted && opt === question.correct ? '<i class="fas fa-check-circle text-success me-1"></i>' : ''}
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const alertClass = isCorrect ? 'alert-success' : 'alert-danger';
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="alert ${alertClass} mt-3 py-2">
                        <strong>${resultText}</strong>
                        <div class="mt-1 small">
                            <strong>Solution:</strong>
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { if (currentQuestion > 0) showQuestion(currentQuestion - 1); }
        function nextQuestion() { if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
            // Update button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('main-content');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                if (window.innerWidth >= 992) {
                    content.classList.add('with-nav');
                }
            } else {
                nav.classList.remove('show');
                if (window.innerWidth >= 992) {
                    content.classList.remove('with-nav');
                }
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer - compact
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            if (confirm(`You have attempted ${attempted} of ${questions.length} questions.
${remaining} questions are unattempted.
Are you sure you want to submit?`)) {
                submitTest();
            }
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.classList.replace('btn-light', 'btn-success');
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
        // Add event listener for window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 992) {
                if (document.getElementById('question-nav').classList.contains('show')) {
                    document.getElementById('main-content').classList.add('with-nav');
                }
            } else {
                document.getElementById('main-content').classList.remove('with-nav');
            }
        });
    </script>
</body>
</html>