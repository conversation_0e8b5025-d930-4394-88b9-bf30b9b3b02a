<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The difference between the smallest and the largest fractions among <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>4</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>32</mn></mrow><mrow><mn>39</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>25</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> is :</p>",
                    question_hi: "<p>1. सबसे छोटी और सबसे बड़ी भिन्न के बीच का अंतर ज्ञात कीजिएI<br><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>4</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>32</mn></mrow><mrow><mn>39</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>25</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>56</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>58</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>56</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>58</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>54</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>1.(d) <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 0.85, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>8</mn></mfrac></math>= 0.50, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>39</mn></mfrac></math> = 0.82, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mrow><mn>27</mn><mo>&#160;</mo></mrow></mfrac></math>= 0.92<br>Largest = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> and Smallest = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>8</mn></mfrac></math><br>Required difference = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>8</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>-</mo><mn>108</mn></mrow><mn>216</mn></mfrac><mo>=</mo><mfrac><mn>92</mn><mn>216</mn></mfrac><mo>=</mo><mfrac><mn>23</mn><mn>54</mn></mfrac></math></p>",
                    solution_hi: "<p>1.(d) <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 0.85, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>8</mn></mfrac></math>= 0.50, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>39</mn></mfrac></math> = 0.82, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mrow><mn>27</mn><mo>&#160;</mo></mrow></mfrac></math>= 0.92<br>सबसे बड़ा = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> और सबसे छोटा = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>8</mn></mfrac></math><br>आवश्यक अंतर = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>8</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>-</mo><mn>108</mn></mrow><mn>216</mn></mfrac><mo>=</mo><mfrac><mn>92</mn><mn>216</mn></mfrac><mo>=</mo><mfrac><mn>23</mn><mn>54</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Given that <math display=\"inline\"><msup><mrow><mn>69</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>07</mn></mrow></msup></math> = x, 69<sup>0.67 </sup>= y and x<sup>z</sup> = y<sup>7</sup> , then the value of z is close to:</p>",
                    question_hi: "<p>2. दिया गया है कि <math display=\"inline\"><msup><mrow><mn>69</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>07</mn></mrow></msup></math> = x, 69<sup>0.67</sup> = y और x<sup>z</sup> = y<sup>7</sup> है, तो z का निकटतम मान कितना है ?</p>",
                    options_en: ["<p>69.08</p>", "<p>67.51</p>", 
                                "<p>67</p>", "<p>66.87</p>"],
                    options_hi: ["<p>69.08</p>", "<p>67.51</p>",
                                "<p>67</p>", "<p>66.87</p>"],
                    solution_en: "<p>2.(c) Given: <math display=\"inline\"><msup><mrow><mn>69</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>07</mn></mrow></msup></math> = x and 69<sup>0.67</sup> = y<br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>z </sup>= y<sup>7</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> [69<sup>0.07</sup>]<sup>z</sup> = [69<sup>0.67</sup>]<sup>7</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 69<sup>0.07 &times; z</sup> = 69<sup>0.67 &times; 7</sup><br>On comparing above equation we get, <br><math display=\"inline\"><mo>&#8658;</mo></math> 0.07 &times; z = 0.67 &times; 7<br><math display=\"inline\"><mo>&#8658;</mo></math> z = 67</p>",
                    solution_hi: "<p>2.(c) दिया है: <math display=\"inline\"><msup><mrow><mn>69</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>07</mn></mrow></msup></math> = x और 69<sup>0.67</sup> = y<br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>z </sup>= y<sup>7</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> [69<sup>0.07</sup>]<sup>z</sup> = [69<sup>0.67</sup>]<sup>7</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 69<sup>0.07 &times; z</sup> = 69<sup>0.67 &times; 7</sup><br>उपरोक्त समीकरण की तुलना करने पर हमें मिलता है,<br><math display=\"inline\"><mo>&#8658;</mo></math> 0.07 &times; z = 0.67 &times; 7<br><math display=\"inline\"><mo>&#8658;</mo></math> z = 67</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Express the following into a vulgar fraction.</p>\n<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIQAAAAbCAIAAAAyFrBmAAACOUlEQVRoBe1X7a3DIAxknUidKFIXqjJNpUxTqavwxJexSezCIylR5fxyArbPdxiCsfpchgFzGSQKxH5HjHU23DOvZ6kgJP0yGAEJKf87YpzF94/FVTEuJKiKcVkxnnlvn58yyvdySzvvbXmTucKQn/daJkP2SuJd83IMzphpvZvpQSuowQBzGsB4H7581Bku6LS8vAO2IWs2PN33cPRi21qLX7ENzuE06xADY8M2ZMgGBoBtNOMxGdMhBgaA7ZyhsKTyQYwS63o3JtJdhLO2yOqkZlTEQzY4hn4SxSi8SP6DcLqYPpSHI3XGUWAqygcxnGJka3KM71P2fkyGbE2uqlCPMOQldNPcHCZypF2q/xicSYl5tS7gf8VoAFNTfhJjW3+x/NHy3DRNXq3CEAToEuMgnADG9ojRAgYyCuWrGB2d8bNiuMK4Jx1I1tqW+mvadL8zTgCjnVH8g+TtFKjZFwMN78gPoy0rA5wqtim/dZ57gCc4Apo4ZVtk8g3c9ePM8XrOjBbSIKNQfjozyP3AOW56HKJ1/Nr6GAKamEMSo1zd/8SZq+k4M5pISxmF8kGMeAmIi47+Snl//P/nGYl/twU7wlCEI6CpEOMonImbrs5oAvO5fCQGuZWQO8dGDHJjoncOecgB+iwGEMUZbq3EB29ZjThD9E+dwWGA7w1gnI9QPhUDEqgxggEVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxOc/kHht4BkCK2MAAAAASUVORK5CYII=\"></p>",
                    question_hi: "<p>3. निम्नलिखित को साधारण भिन्न में व्यक्त करें।<br><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIQAAAAbCAIAAAAyFrBmAAACOUlEQVRoBe1X7a3DIAxknUidKFIXqjJNpUxTqavwxJexSezCIylR5fxyArbPdxiCsfpchgFzGSQKxH5HjHU23DOvZ6kgJP0yGAEJKf87YpzF94/FVTEuJKiKcVkxnnlvn58yyvdySzvvbXmTucKQn/daJkP2SuJd83IMzphpvZvpQSuowQBzGsB4H7581Bku6LS8vAO2IWs2PN33cPRi21qLX7ENzuE06xADY8M2ZMgGBoBtNOMxGdMhBgaA7ZyhsKTyQYwS63o3JtJdhLO2yOqkZlTEQzY4hn4SxSi8SP6DcLqYPpSHI3XGUWAqygcxnGJka3KM71P2fkyGbE2uqlCPMOQldNPcHCZypF2q/xicSYl5tS7gf8VoAFNTfhJjW3+x/NHy3DRNXq3CEAToEuMgnADG9ojRAgYyCuWrGB2d8bNiuMK4Jx1I1tqW+mvadL8zTgCjnVH8g+TtFKjZFwMN78gPoy0rA5wqtim/dZ57gCc4Apo4ZVtk8g3c9ePM8XrOjBbSIKNQfjozyP3AOW56HKJ1/Nr6GAKamEMSo1zd/8SZq+k4M5pISxmF8kGMeAmIi47+Snl//P/nGYl/twU7wlCEI6CpEOMonImbrs5oAvO5fCQGuZWQO8dGDHJjoncOecgB+iwGEMUZbq3EB29ZjThD9E+dwWGA7w1gnI9QPhUDEqgxggEVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxOc/kHht4BkCK2MAAAAASUVORK5CYII=\"></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1212</mn></mrow><mrow><mn>9900</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1211</mn></mrow><mrow><mn>9990</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1211</mn></mrow><mrow><mn>9999</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1211</mn></mrow><mrow><mn>9900</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1212</mn></mrow><mrow><mn>9900</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1211</mn></mrow><mrow><mn>9990</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1211</mn></mrow><mrow><mn>9999</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1211</mn></mrow><mrow><mn>9900</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>3.(d)</p>\n<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIQAAAAbCAIAAAAyFrBmAAACOUlEQVRoBe1X7a3DIAxknUidKFIXqjJNpUxTqavwxJexSezCIylR5fxyArbPdxiCsfpchgFzGSQKxH5HjHU23DOvZ6kgJP0yGAEJKf87YpzF94/FVTEuJKiKcVkxnnlvn58yyvdySzvvbXmTucKQn/daJkP2SuJd83IMzphpvZvpQSuowQBzGsB4H7581Bku6LS8vAO2IWs2PN33cPRi21qLX7ENzuE06xADY8M2ZMgGBoBtNOMxGdMhBgaA7ZyhsKTyQYwS63o3JtJdhLO2yOqkZlTEQzY4hn4SxSi8SP6DcLqYPpSHI3XGUWAqygcxnGJka3KM71P2fkyGbE2uqlCPMOQldNPcHCZypF2q/xicSYl5tS7gf8VoAFNTfhJjW3+x/NHy3DRNXq3CEAToEuMgnADG9ojRAgYyCuWrGB2d8bNiuMK4Jx1I1tqW+mvadL8zTgCjnVH8g+TtFKjZFwMN78gPoy0rA5wqtim/dZ57gCc4Apo4ZVtk8g3c9ePM8XrOjBbSIKNQfjozyP3AOW56HKJ1/Nr6GAKamEMSo1zd/8SZq+k4M5pISxmF8kGMeAmIi47+Snl//P/nGYl/twU7wlCEI6CpEOMonImbrs5oAvO5fCQGuZWQO8dGDHJjoncOecgB+iwGEMUZbq3EB29ZjThD9E+dwWGA7w1gnI9QPhUDEqgxggEVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxOc/kHht4BkCK2MAAAAASUVORK5CYII=\"></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>900</mn></mrow></mfrac></math> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>99</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>9</mn></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>1100</mn></mrow><mn>9900</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1211</mn><mn>9900</mn></mfrac></math></p>",
                    solution_hi: "<p>3.(d)</p>\n<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIQAAAAbCAIAAAAyFrBmAAACOUlEQVRoBe1X7a3DIAxknUidKFIXqjJNpUxTqavwxJexSezCIylR5fxyArbPdxiCsfpchgFzGSQKxH5HjHU23DOvZ6kgJP0yGAEJKf87YpzF94/FVTEuJKiKcVkxnnlvn58yyvdySzvvbXmTucKQn/daJkP2SuJd83IMzphpvZvpQSuowQBzGsB4H7581Bku6LS8vAO2IWs2PN33cPRi21qLX7ENzuE06xADY8M2ZMgGBoBtNOMxGdMhBgaA7ZyhsKTyQYwS63o3JtJdhLO2yOqkZlTEQzY4hn4SxSi8SP6DcLqYPpSHI3XGUWAqygcxnGJka3KM71P2fkyGbE2uqlCPMOQldNPcHCZypF2q/xicSYl5tS7gf8VoAFNTfhJjW3+x/NHy3DRNXq3CEAToEuMgnADG9ojRAgYyCuWrGB2d8bNiuMK4Jx1I1tqW+mvadL8zTgCjnVH8g+TtFKjZFwMN78gPoy0rA5wqtim/dZ57gCc4Apo4ZVtk8g3c9ePM8XrOjBbSIKNQfjozyP3AOW56HKJ1/Nr6GAKamEMSo1zd/8SZq+k4M5pISxmF8kGMeAmIi47+Snl//P/nGYl/twU7wlCEI6CpEOMonImbrs5oAvO5fCQGuZWQO8dGDHJjoncOecgB+iwGEMUZbq3EB29ZjThD9E+dwWGA7w1gnI9QPhUDEqgxggEVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxGcVYwTrTE4VgyFmxOc/kHht4BkCK2MAAAAASUVORK5CYII=\"></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>900</mn></mrow></mfrac></math> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>99</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>9</mn></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>1100</mn></mrow><mn>9900</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1211</mn><mn>9900</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Which of the following fractions is the largest ?<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>6</mn></mrow><mrow><mn>21</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>15</mn></mrow><mrow><mn>73</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>18</mn></mrow><mrow><mn>82</mn></mrow></mfrac></math></p>",
                    question_hi: "<p>4. निम्नलिखित में से कौन सा भिन्न सबसे बड़ा है ?<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>6</mn></mrow><mrow><mn>21</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>15</mn></mrow><mrow><mn>73</mn></mrow></mfrac><mo>,</mo><mi>&#160;</mi><mfrac><mrow><mn>18</mn></mrow><mrow><mn>82</mn></mrow></mfrac></math></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>82</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>73</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>82</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>73</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>4.(d) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>55</mn><mo>,</mo><mfrac><mn>6</mn><mn>21</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>28</mn><mo>,</mo><mfrac><mn>15</mn><mn>73</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>20</mn><mo>,</mo><mfrac><mn>18</mn><mn>82</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>21</mn><mo>&#160;</mo></math><br>Largest fraction = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>4.(d) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>55</mn><mo>,</mo><mfrac><mn>6</mn><mn>21</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>28</mn><mo>,</mo><mfrac><mn>15</mn><mn>73</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>20</mn><mo>,</mo><mfrac><mn>18</mn><mn>82</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>21</mn><mo>&#160;</mo></math><br>सबसे बडी भिन्न = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Simplify the following:<br><math display=\"inline\"><mo>[</mo><mo>{</mo><mo>(</mo><mo>-</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>)</mo><mo>+</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>.</mo><mn>6</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>}</mo><mo>&#247;</mo><mn>5</mn><mo>]</mo><mo>&#215;</mo><mn>100</mn></math></p>",
                    question_hi: "<p>5. निम्नलिखित को सरल कीजिए:<br><math display=\"inline\"><mo>[</mo><mo>{</mo><mo>(</mo><mo>-</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>)</mo><mo>+</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>.</mo><mn>6</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>}</mo><mo>&#247;</mo><mn>5</mn><mo>]</mo><mo>&#215;</mo><mn>100</mn></math></p>",
                    options_en: ["<p>517</p>", "<p>512</p>", 
                                "<p>571</p>", "<p>521</p>"],
                    options_hi: ["<p>517</p>", "<p>512</p>",
                                "<p>571</p>", "<p>521</p>"],
                    solution_en: "<p>5.(d) <math display=\"inline\"><mo>[</mo><mo>{</mo><mo>(</mo><mo>-</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>)</mo><mo>+</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>.</mo><mn>6</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>}</mo><mo>&#247;</mo><mn>5</mn><mo>]</mo><mo>&#215;</mo><mn>100</mn></math><br>= [(-0.4 + 21.16 + 5.29) &divide; 5] &times; 100<br>= [26.05 &divide; 5] &times; 100<br>= 5.21 &times; 100 = 521</p>",
                    solution_hi: "<p>5.(d) <math display=\"inline\"><mo>[</mo><mo>{</mo><mo>(</mo><mo>-</mo><mn>0</mn><mo>.</mo><mn>4</mn><mo>)</mo><mo>+</mo><msup><mrow><mo>(</mo><mn>4</mn><mo>.</mo><mn>6</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>3</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>}</mo><mo>&#247;</mo><mn>5</mn><mo>]</mo><mo>&#215;</mo><mn>100</mn></math><br>= [(-0.4 + 21.16 + 5.29) &divide; 5] &times; 100<br>= [26.05 &divide; 5] &times; 100<br>= 5.21 &times; 100 = 521</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <math display=\"inline\"><mfrac><mrow><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mn>5</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>5</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><msup><mrow><mi>&#160;</mi><mi>c</mi></mrow><mrow><mn>8</mn></mrow></msup><mi>&#160;</mi><mo>)</mo></mrow><mrow><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mn>8</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>5</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><msup><mrow><mi>&#160;</mi><mi>c</mi></mrow><mrow><mn>1</mn></mrow></msup><mi>&#160;</mi><mo>)</mo></mrow></mfrac></math> in simplified form is :</p>",
                    question_hi: "<p>6. <math display=\"inline\"><mfrac><mrow><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mn>5</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>5</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><msup><mrow><mi>&#160;</mi><mi>c</mi></mrow><mrow><mn>8</mn></mrow></msup><mi>&#160;</mi><mo>)</mo></mrow><mrow><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mn>8</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>5</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><msup><mrow><mi>&#160;</mi><mi>c</mi></mrow><mrow><mn>1</mn></mrow></msup><mi>&#160;</mi><mo>)</mo></mrow></mfrac></math> का सरलतम रूप ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mo>-</mo><mn>10</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>b</mi></mrow><mrow><mo>-</mo><mn>2</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>0</mn></mrow></msup><mo>)</mo></math></p>", "<p><math display=\"inline\"><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mo>-</mo><mn>6</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>b</mi></mrow><mrow><mo>-</mo><mn>6</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>c</mi></mrow><mrow><mo>-</mo><mn>6</mn></mrow></msup><mo>)</mo></math></p>", 
                                "<p><math display=\"inline\"><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>b</mi></mrow><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>c</mi></mrow><mrow><mo>-</mo><mn>9</mn></mrow></msup><mo>)</mo></math></p>", "<p><math display=\"inline\"><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mo>-</mo><mn>3</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>0</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>7</mn></mrow></msup><mo>)</mo></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mo>-</mo><mn>10</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>b</mi></mrow><mrow><mo>-</mo><mn>2</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>0</mn></mrow></msup><mo>)</mo></math></p>", "<p><math display=\"inline\"><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mo>-</mo><mn>6</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>b</mi></mrow><mrow><mo>-</mo><mn>6</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>c</mi></mrow><mrow><mo>-</mo><mn>6</mn></mrow></msup><mo>)</mo></math></p>",
                                "<p><math display=\"inline\"><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>b</mi></mrow><mrow><mo>-</mo><mn>1</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>c</mi></mrow><mrow><mo>-</mo><mn>9</mn></mrow></msup><mo>)</mo></math></p>", "<p><math display=\"inline\"><mo>(</mo><msup><mrow><mi>a</mi></mrow><mrow><mo>-</mo><mn>3</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>b</mi></mrow><mrow><mn>0</mn></mrow></msup><mo>)</mo><mo>&#215;</mo><mo>(</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>7</mn></mrow></msup><mo>)</mo></math></p>"],
                    solution_en: "<p>6.(d)</p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>a</mi><mn>5</mn></msup><mo>&#215;</mo><msup><mi>b</mi><mn>5</mn></msup><mo>&#215;</mo><msup><mi>c</mi><mn>8</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>a</mi><mn>8</mn></msup><mo>&#215;</mo><msup><mi>b</mi><mn>5</mn></msup><mo>&#215;</mo><msup><mi>c</mi><mn>1</mn></msup><mo>)</mo></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>8</mn></mrow></msup></math> &times; b<sup>5 - 5 </sup>&times; c<sup>8 - 1</sup></p>\n<p>=&nbsp;<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></msup></math> &times; b<sup>0</sup> &times; c<sup>7</sup></p>",
                    solution_hi: "<p>6.(d)</p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msup><mi>a</mi><mn>5</mn></msup><mo>&#215;</mo><msup><mi>b</mi><mn>5</mn></msup><mo>&#215;</mo><msup><mi>c</mi><mn>8</mn></msup><mo>)</mo></mrow><mrow><mo>(</mo><msup><mi>a</mi><mn>8</mn></msup><mo>&#215;</mo><msup><mi>b</mi><mn>5</mn></msup><mo>&#215;</mo><msup><mi>c</mi><mn>1</mn></msup><mo>)</mo></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>8</mn></mrow></msup></math> &times; b<sup>5 - 5 </sup>&times; c<sup>8 - 1</sup></p>\n<p>=&nbsp;<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow></msup></math> &times; b<sup>0</sup> &times; c<sup>7</sup></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Find the value of&nbsp; [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>27</mn><mo>&#215;</mo><mn>11</mn><mo>)</mo><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>&#247;</mo><mn>8</mn><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>19</mn><mi>&#160;</mi><mo>-</mo><mn>15</mn><mo>)</mo></mrow><mn>4</mn></mfrac></math>}]</p>",
                    question_hi: "<p>7. निम्नलिखित का मान ज्ञात कीजिए।<br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>27</mn><mo>&#215;</mo><mn>11</mn><mo>)</mo><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>&#247;</mo><mn>8</mn><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>19</mn><mi>&#160;</mi><mo>-</mo><mn>15</mn><mo>)</mo></mrow><mn>4</mn></mfrac></math>}]</p>",
                    options_en: ["<p>309</p>", "<p>289</p>", 
                                "<p>297</p>", "<p>282</p>"],
                    options_hi: ["<p>309</p>", "<p>289</p>",
                                "<p>297</p>", "<p>282</p>"],
                    solution_en: "<p>7.(c) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>27</mn><mo>&#215;</mo><mn>11</mn><mo>)</mo><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>&#247;</mo><mn>8</mn><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>19</mn><mi>&#160;</mi><mo>-</mo><mn>15</mn><mo>)</mo></mrow><mn>4</mn></mfrac></math>}]<br>= 297 &times; 1 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>= 297</p>",
                    solution_hi: "<p>7.(c) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>27</mn><mo>&#215;</mo><mn>11</mn><mo>)</mo><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><mo>&#247;</mo><mn>8</mn><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>19</mn><mi>&#160;</mi><mo>-</mo><mn>15</mn><mo>)</mo></mrow><mn>4</mn></mfrac></math>}]<br>= 297 &times; 1 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>= 297</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Express the following as a vulgar fraction.</p>\n<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHYAAAAhCAIAAACDTsgJAAAC00lEQVRoBe2YaW4CMQyFuQ5ST4TEhRCnQeI0SFwllZ1J8rxMZoEghDx/msWxn784SdtDim8wgcNg/+E+BeLhRRCIA/FwAsMDRBV/GeLDy9/whL4vwI9U8b6t/8x2/Ajiz8DaFyUQ7+O2YVUg3gBrnykgvp3qjXa6db2B5eHv+pxsn9e/6gAa53vX14pJCLcg7HE91sg67r2lp6dWaFgyuZ9r4OP1IawLYkqjzGFbGHOHEy6pMtZGWVg/L8fmU8xs6aAYbDs+COLxknecgTaUoks42pTjaOsQZ3rKpWSzzoiZFETtiNBTXDiFOGjj8ZIwjNsmWZbd1bMbhGGe5Ib2A9Oe2hyBiDuaVfSeMDTFrU0pkWZMPCM2IUEfOvPaZi0b0U7MVLd20svEOF8tDInrsjAUtKTc7wnzV/CoIp7/DWR9LRxJCOBaksMVZbKYyX5hIk8X8fJdYaND3nNN3Npsw1VsfbngHK/+XUwplUPqLFJDNno1sFPLwlgSvT3tZjBp0wa8HzFpow9viZReq2JGaa9RUUEVl2gQu7kPHO5BXOLgZuRw5bFh2TOIVworQbyf+v3YX8UzfPM7A5g8FWLMcqzTdgrBVTO/IVMFdqebnPKXp2Sjz1mqcSly53M3yzclcypVfNPtZUIHQjz9pL7dAMYXDnQ4rjhn6c2ItRoiWI4Vqs7tDt+UUn+t9dYtlg3CKK74HaZx1Lsuq8yRlId6ew+L+HxgHahwuYrz6S71IhXwgnaFc7dzDxAUdd+DHK/Zz4TErBLGW9VCcx2UemcKRRWh7xRQk9gX1uy4qurZErHIqCCeflefniCzJ1U66zMPVVFPRQx/YoGKV5pMOcecF8YBOL1JnajofOqnGVD7iiyxlnd08o8iJWKxJDpvIwBV/Daf4UgQCMQCx4hOIB5BVfgMxALHiE4gHkFV+AzEAseITiAeQVX4DMQCx4hOIB5BVfj8B2oE3Rmq8zzSAAAAAElFTkSuQmCC\"></p>",
                    question_hi: "<p>8. निम्नलिखित को साधारण भिन्न रूप में व्यक्त करें।<br><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHYAAAAhCAIAAACDTsgJAAAC00lEQVRoBe2YaW4CMQyFuQ5ST4TEhRCnQeI0SFwllZ1J8rxMZoEghDx/msWxn784SdtDim8wgcNg/+E+BeLhRRCIA/FwAsMDRBV/GeLDy9/whL4vwI9U8b6t/8x2/Ajiz8DaFyUQ7+O2YVUg3gBrnykgvp3qjXa6db2B5eHv+pxsn9e/6gAa53vX14pJCLcg7HE91sg67r2lp6dWaFgyuZ9r4OP1IawLYkqjzGFbGHOHEy6pMtZGWVg/L8fmU8xs6aAYbDs+COLxknecgTaUoks42pTjaOsQZ3rKpWSzzoiZFETtiNBTXDiFOGjj8ZIwjNsmWZbd1bMbhGGe5Ib2A9Oe2hyBiDuaVfSeMDTFrU0pkWZMPCM2IUEfOvPaZi0b0U7MVLd20svEOF8tDInrsjAUtKTc7wnzV/CoIp7/DWR9LRxJCOBaksMVZbKYyX5hIk8X8fJdYaND3nNN3Npsw1VsfbngHK/+XUwplUPqLFJDNno1sFPLwlgSvT3tZjBp0wa8HzFpow9viZReq2JGaa9RUUEVl2gQu7kPHO5BXOLgZuRw5bFh2TOIVworQbyf+v3YX8UzfPM7A5g8FWLMcqzTdgrBVTO/IVMFdqebnPKXp2Sjz1mqcSly53M3yzclcypVfNPtZUIHQjz9pL7dAMYXDnQ4rjhn6c2ItRoiWI4Vqs7tDt+UUn+t9dYtlg3CKK74HaZx1Lsuq8yRlId6ew+L+HxgHahwuYrz6S71IhXwgnaFc7dzDxAUdd+DHK/Zz4TErBLGW9VCcx2UemcKRRWh7xRQk9gX1uy4qurZErHIqCCeflefniCzJ1U66zMPVVFPRQx/YoGKV5pMOcecF8YBOL1JnajofOqnGVD7iiyxlnd08o8iJWKxJDpvIwBV/Daf4UgQCMQCx4hOIB5BVfgMxALHiE4gHkFV+AzEAseITiAeQVX4DMQCx4hOIB5BVfj8B2oE3Rmq8zzSAAAAAElFTkSuQmCC\"></p>",
                    options_en: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>8.(a)&nbsp;</p>\n<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHYAAAAhCAIAAACDTsgJAAAC00lEQVRoBe2YaW4CMQyFuQ5ST4TEhRCnQeI0SFwllZ1J8rxMZoEghDx/msWxn784SdtDim8wgcNg/+E+BeLhRRCIA/FwAsMDRBV/GeLDy9/whL4vwI9U8b6t/8x2/Ajiz8DaFyUQ7+O2YVUg3gBrnykgvp3qjXa6db2B5eHv+pxsn9e/6gAa53vX14pJCLcg7HE91sg67r2lp6dWaFgyuZ9r4OP1IawLYkqjzGFbGHOHEy6pMtZGWVg/L8fmU8xs6aAYbDs+COLxknecgTaUoks42pTjaOsQZ3rKpWSzzoiZFETtiNBTXDiFOGjj8ZIwjNsmWZbd1bMbhGGe5Ib2A9Oe2hyBiDuaVfSeMDTFrU0pkWZMPCM2IUEfOvPaZi0b0U7MVLd20svEOF8tDInrsjAUtKTc7wnzV/CoIp7/DWR9LRxJCOBaksMVZbKYyX5hIk8X8fJdYaND3nNN3Npsw1VsfbngHK/+XUwplUPqLFJDNno1sFPLwlgSvT3tZjBp0wa8HzFpow9viZReq2JGaa9RUUEVl2gQu7kPHO5BXOLgZuRw5bFh2TOIVworQbyf+v3YX8UzfPM7A5g8FWLMcqzTdgrBVTO/IVMFdqebnPKXp2Sjz1mqcSly53M3yzclcypVfNPtZUIHQjz9pL7dAMYXDnQ4rjhn6c2ItRoiWI4Vqs7tDt+UUn+t9dYtlg3CKK74HaZx1Lsuq8yRlId6ew+L+HxgHahwuYrz6S71IhXwgnaFc7dzDxAUdd+DHK/Zz4TErBLGW9VCcx2UemcKRRWh7xRQk9gX1uy4qurZErHIqCCeflefniCzJ1U66zMPVVFPRQx/YoGKV5pMOcecF8YBOL1JnajofOqnGVD7iiyxlnd08o8iJWKxJDpvIwBV/Daf4UgQCMQCx4hOIB5BVfgMxALHiE4gHkFV+AzEAseITiAeQVX4DMQCx4hOIB5BVfj8B2oE3Rmq8zzSAAAAAElFTkSuQmCC\"><br>= <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>99</mn></mfrac><mo>+</mo><mn>3</mn><mo>&#160;</mo></math><br>= <math display=\"inline\"><mfrac><mrow><mn>27</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>39</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>297</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>363</mn><mn>99</mn></mfrac><mo>=</mo><mfrac><mn>11</mn><mn>3</mn></mfrac><mo>=</mo><mn>3</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>8.(a)&nbsp;</p>\n<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHYAAAAhCAIAAACDTsgJAAAC00lEQVRoBe2YaW4CMQyFuQ5ST4TEhRCnQeI0SFwllZ1J8rxMZoEghDx/msWxn784SdtDim8wgcNg/+E+BeLhRRCIA/FwAsMDRBV/GeLDy9/whL4vwI9U8b6t/8x2/Ajiz8DaFyUQ7+O2YVUg3gBrnykgvp3qjXa6db2B5eHv+pxsn9e/6gAa53vX14pJCLcg7HE91sg67r2lp6dWaFgyuZ9r4OP1IawLYkqjzGFbGHOHEy6pMtZGWVg/L8fmU8xs6aAYbDs+COLxknecgTaUoks42pTjaOsQZ3rKpWSzzoiZFETtiNBTXDiFOGjj8ZIwjNsmWZbd1bMbhGGe5Ib2A9Oe2hyBiDuaVfSeMDTFrU0pkWZMPCM2IUEfOvPaZi0b0U7MVLd20svEOF8tDInrsjAUtKTc7wnzV/CoIp7/DWR9LRxJCOBaksMVZbKYyX5hIk8X8fJdYaND3nNN3Npsw1VsfbngHK/+XUwplUPqLFJDNno1sFPLwlgSvT3tZjBp0wa8HzFpow9viZReq2JGaa9RUUEVl2gQu7kPHO5BXOLgZuRw5bFh2TOIVworQbyf+v3YX8UzfPM7A5g8FWLMcqzTdgrBVTO/IVMFdqebnPKXp2Sjz1mqcSly53M3yzclcypVfNPtZUIHQjz9pL7dAMYXDnQ4rjhn6c2ItRoiWI4Vqs7tDt+UUn+t9dYtlg3CKK74HaZx1Lsuq8yRlId6ew+L+HxgHahwuYrz6S71IhXwgnaFc7dzDxAUdd+DHK/Zz4TErBLGW9VCcx2UemcKRRWh7xRQk9gX1uy4qurZErHIqCCeflefniCzJ1U66zMPVVFPRQx/YoGKV5pMOcecF8YBOL1JnajofOqnGVD7iiyxlnd08o8iJWKxJDpvIwBV/Daf4UgQCMQCx4hOIB5BVfgMxALHiE4gHkFV+AzEAseITiAeQVX4DMQCx4hOIB5BVfj8B2oE3Rmq8zzSAAAAAElFTkSuQmCC\"><br>= <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>99</mn></mfrac><mo>+</mo><mn>3</mn><mo>&#160;</mo></math><br>= <math display=\"inline\"><mfrac><mrow><mn>27</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>39</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>297</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>363</mn><mn>99</mn></mfrac><mo>=</mo><mfrac><mn>11</mn><mn>3</mn></mfrac><mo>=</mo><mn>3</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Given that <math display=\"inline\"><msup><mrow><mn>137</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>1</mn></mrow></msup></math> = x, 137<sup>0.68</sup> = y and x<sup>Z</sup> = y<sup>4</sup>, then the me value of z is close to:</p>",
                    question_hi: "<p>9. दिया गया है कि <math display=\"inline\"><msup><mrow><mn>137</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>1</mn></mrow></msup></math> = x, 137<sup>0.68</sup> = y और x<sup>Z</sup> = y<sup>4</sup> है, तो z का निकटतम मान कितना है?</p>",
                    options_en: ["<p>27.2</p>", "<p>29.14</p>", 
                                "<p>25.05</p>", "<p>27.13</p>"],
                    options_hi: ["<p>27.2</p>", "<p>29.14</p>",
                                "<p>25.05</p>", "<p>27.13</p>"],
                    solution_en: "<p>9.(a) Given: <math display=\"inline\"><msup><mrow><mn>137</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>1</mn></mrow></msup></math> = x, 137<sup>0.68</sup> = y <br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>Z</sup> = y<sup>4</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 137<sup>(0.1)z</sup> = 137<sup>(0.68) &times; 4</sup><br>On comparing above equation we get,<br><math display=\"inline\"><mo>&#8658;</mo></math> 0.1 &times; z = 0.68 &times; 4<br><math display=\"inline\"><mo>&#8658;</mo></math> z = 27.2</p>",
                    solution_hi: "<p>9.(a) दिया है: <math display=\"inline\"><msup><mrow><mn>137</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>1</mn></mrow></msup></math> = x, 137<sup>0.68 </sup>= y <br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>Z</sup> = y<sup>4</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 137<sup>(0.1)z</sup> = 137<sup>(0.68) &times; 4</sup><br>उपरोक्त समीकरण की तुलना करने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math> 0.1 &times; z = 0.68 &times; 4<br><math display=\"inline\"><mo>&#8658;</mo></math> z = 27.2</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Which of the following fractions is the largest?<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>26</mn></mfrac><mo>,</mo><mfrac><mn>63</mn><mn>64</mn></mfrac><mo>,</mo><mfrac><mn>44</mn><mn>88</mn></mfrac></math></p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन सा भिन्न सबसे बड़ा है?<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>,</mo><mfrac><mn>5</mn><mn>26</mn></mfrac><mo>,</mo><mfrac><mn>63</mn><mn>64</mn></mfrac><mo>,</mo><mfrac><mn>44</mn><mn>88</mn></mfrac></math></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mn>88</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mn>88</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(c) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>62</mn><mo>,</mo><mfrac><mn>5</mn><mrow><mn>26</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>19</mn><mo>,</mo><mo>&#160;</mo><mfrac><mn>63</mn><mn>64</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>98</mn><mo>,</mo><mo>&#160;</mo><mfrac><mn>44</mn><mn>88</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>50</mn></math><br>Hence, largest fraction = <math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>10.(c)<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>5</mn><mn>8</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>62</mn><mo>,</mo><mfrac><mn>5</mn><mrow><mn>26</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>19</mn><mo>,</mo><mo>&#160;</mo><mfrac><mn>63</mn><mn>64</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>98</mn><mo>,</mo><mo>&#160;</mo><mfrac><mn>44</mn><mn>88</mn></mfrac><mo>=</mo><mn>0</mn><mo>.</mo><mn>50</mn></math><br>अतः सबसे बड़ा भिन्न = <math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Express the following as a vulgar fraction.</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>.</mo><mn>1</mn><mover><mn>23</mn><mo>&#175;</mo></mover><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>0</mn><mover><mn>12</mn><mo>&#175;</mo></mover><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn></math></p>",
                    question_hi: "<p>11. निम्नलिखित को साधारण भिन्न (vulgar fraction) के रूप में व्यक्त करें।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>.</mo><mn>1</mn><mover><mn>23</mn><mo>&#175;</mo></mover><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>0</mn><mover><mn>12</mn><mo>&#175;</mo></mover><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn></math></p>",
                    options_en: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>134</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math></p>", 
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>134</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>134</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math></p>",
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>999</mn></mrow></mfrac></math></p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>134</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>11.(d)</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>.</mo><mn>1</mn><mover><mn>23</mn><mo>&#175;</mo></mover><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>0</mn><mover><mn>12</mn><mo>&#175;</mo></mover><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>123</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>990</mn></mfrac><mo>+</mo><mn>3</mn></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>122</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mn>990</mn><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mn>3</mn></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>134</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math> + 3</p>\n<p>= 3<math display=\"inline\"><mfrac><mrow><mn>134</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>11.(d)</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>.</mo><mn>1</mn><mover><mn>23</mn><mo>&#175;</mo></mover><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>0</mn><mover><mn>12</mn><mo>&#175;</mo></mover><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>123</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>990</mn></mfrac><mo>+</mo><mn>3</mn></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>122</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mn>990</mn><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mn>3</mn></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>134</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math> + 3</p>\n<p>= 3<math display=\"inline\"><mfrac><mrow><mn>134</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Express the following into a vulgar fraction.<br><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAAiCAIAAAAmgetyAAACmklEQVRoBe2WXW4DIQyEuU6knihSL1TlNJFymkq5iitsDGP+st48RKqcl7CLbYaPgSVR/A4TSIcjI5AClsMEAStgOQg4QsNZH4KVTv0cYj2hp7S8sM6Lbo+8/x8bsBxrHLBOw7pf61a/3jdVnrevGpj6yN/bpXZ+P+ZVOKZPnIfO3h7VSQSR6ev2NMUebbYrnSYeL6W57uX2y/3YtglETKoOzGpg2lnB5UdUsZqJjsIasvoxds+oDdtjjtFmZZPR9vhOaaKzr1i3IdeChGV+NoUy5Wo5Utk9fy4pXZudslx45HiOyd5bwhqGANWHdRL1UwA79zqZ3VKPDl9hZdImejZPzTL/eWCFZTqIBk1EAuKet6oZDjN3sM7rJCDSc+TtohsCpZi2whr17R3eivRL3XpYnFWgwbDIEK/NUYz2FNZyVsjLozrl/Cp7Ygrr5U58G1bWOnqEofBWa1uSRC7vyg/AYkm6AwbLmyOsLk3XeA8Wz3m3IGbZYQeNsKRU/YyaBhyROQweywLYN90U+TFbCROtcu59fca/AcuON1NI5dPJ3w3jfM49c2adgtWTEq2in1fletfzYTENea2w4PAr8dkU/YeslTpECmGxz41f+AG+v7b4yixgT0nY65RvInqqDYOtXNYer9hb2hVWj9YYoUtck8pZei5w0kIEVzjjLLnlAeKdzjWp/swyx0U32/ZYYZXLbpmATebSFTx7BOS2YiQ3gxrJN52pPc/DOq5TLi4Lh7IAtdJ2RjA9gFUOy7JVcNkRlrSH/QQbVnwnEcZlMOweFgTOm3kty2+lU25VGtX+FVBZV+loL+fjlbcW1jY0OgOWwwMBK2A5CDhCw1kBy0HAERrOClgOAo7QcFbAchBwhIazApaDgCP0D/N+QnXeCheMAAAAAElFTkSuQmCC\">&nbsp;</p>",
                    question_hi: "<p>12. निम्नलिखित को साधारण भिन्न में व्यक्त करें।<br><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAAiCAIAAAAmgetyAAACmklEQVRoBe2WXW4DIQyEuU6knihSL1TlNJFymkq5iitsDGP+st48RKqcl7CLbYaPgSVR/A4TSIcjI5AClsMEAStgOQg4QsNZH4KVTv0cYj2hp7S8sM6Lbo+8/x8bsBxrHLBOw7pf61a/3jdVnrevGpj6yN/bpXZ+P+ZVOKZPnIfO3h7VSQSR6ev2NMUebbYrnSYeL6W57uX2y/3YtglETKoOzGpg2lnB5UdUsZqJjsIasvoxds+oDdtjjtFmZZPR9vhOaaKzr1i3IdeChGV+NoUy5Wo5Utk9fy4pXZudslx45HiOyd5bwhqGANWHdRL1UwA79zqZ3VKPDl9hZdImejZPzTL/eWCFZTqIBk1EAuKet6oZDjN3sM7rJCDSc+TtohsCpZi2whr17R3eivRL3XpYnFWgwbDIEK/NUYz2FNZyVsjLozrl/Cp7Ygrr5U58G1bWOnqEofBWa1uSRC7vyg/AYkm6AwbLmyOsLk3XeA8Wz3m3IGbZYQeNsKRU/YyaBhyROQweywLYN90U+TFbCROtcu59fca/AcuON1NI5dPJ3w3jfM49c2adgtWTEq2in1fletfzYTENea2w4PAr8dkU/YeslTpECmGxz41f+AG+v7b4yixgT0nY65RvInqqDYOtXNYer9hb2hVWj9YYoUtck8pZei5w0kIEVzjjLLnlAeKdzjWp/swyx0U32/ZYYZXLbpmATebSFTx7BOS2YiQ3gxrJN52pPc/DOq5TLi4Lh7IAtdJ2RjA9gFUOy7JVcNkRlrSH/QQbVnwnEcZlMOweFgTOm3kty2+lU25VGtX+FVBZV+loL+fjlbcW1jY0OgOWwwMBK2A5CDhCw1kBy0HAERrOClgOAo7QcFbAchBwhIazApaDgCP0D/N+QnXeCheMAAAAAElFTkSuQmCC\"></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>99</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>12.(b)&nbsp;</p>\n<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAAiCAIAAAAmgetyAAACmklEQVRoBe2WXW4DIQyEuU6knihSL1TlNJFymkq5iitsDGP+st48RKqcl7CLbYaPgSVR/A4TSIcjI5AClsMEAStgOQg4QsNZH4KVTv0cYj2hp7S8sM6Lbo+8/x8bsBxrHLBOw7pf61a/3jdVnrevGpj6yN/bpXZ+P+ZVOKZPnIfO3h7VSQSR6ev2NMUebbYrnSYeL6W57uX2y/3YtglETKoOzGpg2lnB5UdUsZqJjsIasvoxds+oDdtjjtFmZZPR9vhOaaKzr1i3IdeChGV+NoUy5Wo5Utk9fy4pXZudslx45HiOyd5bwhqGANWHdRL1UwA79zqZ3VKPDl9hZdImejZPzTL/eWCFZTqIBk1EAuKet6oZDjN3sM7rJCDSc+TtohsCpZi2whr17R3eivRL3XpYnFWgwbDIEK/NUYz2FNZyVsjLozrl/Cp7Ygrr5U58G1bWOnqEofBWa1uSRC7vyg/AYkm6AwbLmyOsLk3XeA8Wz3m3IGbZYQeNsKRU/YyaBhyROQweywLYN90U+TFbCROtcu59fca/AcuON1NI5dPJ3w3jfM49c2adgtWTEq2in1fletfzYTENea2w4PAr8dkU/YeslTpECmGxz41f+AG+v7b4yixgT0nY65RvInqqDYOtXNYer9hb2hVWj9YYoUtck8pZei5w0kIEVzjjLLnlAeKdzjWp/swyx0U32/ZYYZXLbpmATebSFTx7BOS2YiQ3gxrJN52pPc/DOq5TLi4Lh7IAtdJ2RjA9gFUOy7JVcNkRlrSH/QQbVnwnEcZlMOweFgTOm3kty2+lU25VGtX+FVBZV+loL+fjlbcW1jY0OgOWwwMBK2A5CDhCw1kBy0HAERrOClgOAo7QcFbAchBwhIazApaDgCP0D/N+QnXeCheMAAAAAElFTkSuQmCC\"></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>234</mn><mo>-</mo><mn>2</mn></mrow><mn>990</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>99</mn></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>232</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>99</mn></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>232</mn><mo>+</mo><mn>290</mn></mrow><mn>990</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>522</mn><mrow><mn>990</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>29</mn><mn>55</mn></mfrac></math></p>",
                    solution_hi: "<p>12.(b)&nbsp;</p>\n<p><img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAAiCAIAAAAmgetyAAACmklEQVRoBe2WXW4DIQyEuU6knihSL1TlNJFymkq5iitsDGP+st48RKqcl7CLbYaPgSVR/A4TSIcjI5AClsMEAStgOQg4QsNZH4KVTv0cYj2hp7S8sM6Lbo+8/x8bsBxrHLBOw7pf61a/3jdVnrevGpj6yN/bpXZ+P+ZVOKZPnIfO3h7VSQSR6ev2NMUebbYrnSYeL6W57uX2y/3YtglETKoOzGpg2lnB5UdUsZqJjsIasvoxds+oDdtjjtFmZZPR9vhOaaKzr1i3IdeChGV+NoUy5Wo5Utk9fy4pXZudslx45HiOyd5bwhqGANWHdRL1UwA79zqZ3VKPDl9hZdImejZPzTL/eWCFZTqIBk1EAuKet6oZDjN3sM7rJCDSc+TtohsCpZi2whr17R3eivRL3XpYnFWgwbDIEK/NUYz2FNZyVsjLozrl/Cp7Ygrr5U58G1bWOnqEofBWa1uSRC7vyg/AYkm6AwbLmyOsLk3XeA8Wz3m3IGbZYQeNsKRU/YyaBhyROQweywLYN90U+TFbCROtcu59fca/AcuON1NI5dPJ3w3jfM49c2adgtWTEq2in1fletfzYTENea2w4PAr8dkU/YeslTpECmGxz41f+AG+v7b4yixgT0nY65RvInqqDYOtXNYer9hb2hVWj9YYoUtck8pZei5w0kIEVzjjLLnlAeKdzjWp/swyx0U32/ZYYZXLbpmATebSFTx7BOS2YiQ3gxrJN52pPc/DOq5TLi4Lh7IAtdJ2RjA9gFUOy7JVcNkRlrSH/QQbVnwnEcZlMOweFgTOm3kty2+lU25VGtX+FVBZV+loL+fjlbcW1jY0OgOWwwMBK2A5CDhCw1kBy0HAERrOClgOAo7QcFbAchBwhIazApaDgCP0D/N+QnXeCheMAAAAAElFTkSuQmCC\"></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>234</mn><mo>-</mo><mn>2</mn></mrow><mn>990</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>99</mn></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>232</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>99</mn></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>232</mn><mo>+</mo><mn>290</mn></mrow><mn>990</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>522</mn><mrow><mn>990</mn><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>29</mn><mn>55</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Evaluate:- 33 &divide;&nbsp;11 &times; 3 - 3 &times; 3</p>",
                    question_hi: "<p>13. मूल्यांकन कीजिए:- 33 &divide; 11 &times; 3 - 3 &times; 3</p>",
                    options_en: ["<p>3</p>", "<p>-1</p>", 
                                "<p>0</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>-1</p>",
                                "<p>0</p>", "<p>2</p>"],
                    solution_en: "<p>13.(c) 33 &divide; 11 &times; 3 - 3 &times; 3<br>= 3 &times; 3 - 9<br>= 0</p>",
                    solution_hi: "<p>13.(c) 33 &divide; 11 &times; 3 - 3 &times; 3<br>= 3 &times; 3 - 9<br>= 0</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Find the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mn>72</mn><mo>&#247;</mo><mn>3</mn></mrow></mfenced><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>88</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>19</mn><mn>1</mn></mfrac></mstyle></math>&times; (8 - 7}]</p>",
                    question_hi: "<p>14. निम्नलिखित का मान ज्ञात कीजिए। <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mn>72</mn><mo>&#247;</mo><mn>3</mn></mrow></mfenced><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>88</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>19</mn><mn>1</mn></mfrac></mstyle></math>&times; (8 - 7}]</p>",
                    options_en: ["<p>984</p>", "<p>987</p>", 
                                "<p>998</p>", "<p>991</p>"],
                    options_hi: ["<p>984</p>", "<p>987</p>",
                                "<p>998</p>", "<p>991</p>"],
                    solution_en: "<p>14.(a)[</p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mn>72</mn><mo>&#247;</mo><mn>3</mn></mrow></mfenced><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>88</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>19</mn><mn>1</mn></mfrac></mstyle></math>&times; (8 - 7}]</p>\n<p>= 24 &times; {<math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mn>22</mn><mo>+</mo><mn>19</mn></mrow></mfenced></math>}</p>\n<p>= 24 &times; 41 = 984</p>",
                    solution_hi: "<p>14.(a)[</p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mrow><mn>72</mn><mo>&#247;</mo><mn>3</mn></mrow></mfenced><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>88</mn><mn>4</mn></mfrac><mo>+</mo><mfrac><mn>19</mn><mn>1</mn></mfrac></mstyle></math>&times; (8 - 7}]</p>\n<p>= 24 &times; {<math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mn>22</mn><mo>+</mo><mn>19</mn></mrow></mfenced></math>}</p>\n<p>= 24 &times; 41 = 984</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Find the value of [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>27</mn><mo>&#247;</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>80</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>2</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mo>-</mo><mn>3</mn><mo>)</mo></mstyle></math>}]</p>",
                    question_hi: "<p>15. निम्नलिखित का मान ज्ञात कीजिए। <br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>27</mn><mo>&#247;</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>80</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>2</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mo>-</mo><mn>3</mn><mo>)</mo></mstyle></math>}]</p>",
                    options_en: ["<p>78</p>", "<p>81</p>", 
                                "<p>63</p>", "<p>82</p>"],
                    options_hi: ["<p>78</p>", "<p>81</p>",
                                "<p>63</p>", "<p>82</p>"],
                    solution_en: "<p>15.(a) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>27</mn><mo>&#247;</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>80</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>2</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mo>-</mo><mn>3</mn><mo>)</mo></mstyle></math>}]<br>= 3 &times; <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mfrac><mrow><mn>80</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>+</mo><mn>16</mn></mrow></mfenced></math><br>= 3 &times; 26 = 78</p>",
                    solution_hi: "<p>15.(a) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>27</mn><mo>&#247;</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>80</mn><mn>8</mn></mfrac><mo>+</mo><mfrac><mn>16</mn><mn>2</mn></mfrac><mo>&#215;</mo><mo>(</mo><mn>5</mn><mo>-</mo><mn>3</mn><mo>)</mo></mstyle></math>}]<br>= 3 &times; <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mfrac><mrow><mn>80</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>+</mo><mn>16</mn></mrow></mfenced></math><br>= 3 &times; 26 = 78</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>