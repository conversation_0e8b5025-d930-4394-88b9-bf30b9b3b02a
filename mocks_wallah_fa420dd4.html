<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> A retailer offers a discount scheme on shirts, that is, buy 3, </span><span style=\"font-family: Cambria Math;\">get</span><span style=\"font-family: Cambria Math;\"> 2 free. What is the discount percentage?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2369;&#2335;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2358;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2309;&#2352;&#2381;&#2341;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 2 </span><span style=\"font-family: Mangal;\">&#2350;&#2369;&#2347;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>35%</p>\n", "<p>50%</p>\n", 
                                "<p>30%</p>\n", "<p>40%</p>\n"],
                    options_hi: ["<p>35%</p>\n", "<p>50%</p>\n",
                                "<p>30%</p>\n", "<p>40%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the MP of an article be &#8377;1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP of 5 articles = &#8377;5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of 3 articles = &#8377;3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Discount% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mn>5</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 40%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Mangal;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;1 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;3</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mn>5</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 40%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> The marked price of every item being sold by a wholesaler was &#8377;300. The wholesaler was offering a stock-clearance sale under which, for every three items paid for, one item was being given free. In addition to this, a further 10% discount on the amount payable on the &lsquo;Buy 3, Get 1 free&rsquo; scheme price was being offered to anyone making purchases worth more than &#8377;10,000. Ramesh made purchases for which this amount payable was &#8377;18,000. What was the effective percentage discount that was offered to Ramesh during this transaction?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2379;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;300 </span><span style=\"font-family: Mangal;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2379;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2381;&#2335;&#2377;&#2325;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Mangal;\">&#2344;&#2367;&#2325;&#2366;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2375;&#2358;&#2325;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2361;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2369;&#2347;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, 10,000 </span><span style=\"font-family: Mangal;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'3 </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 1 </span><span style=\"font-family: Mangal;\">&#2350;&#2369;&#2347;&#2364;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Mangal;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2350;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> &#8377;18,000 </span><span style=\"font-family: Mangal;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2350;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>32%</p>\n", "<p>31.5%</p>\n", 
                                "<p>32.5%</p>\n", "<p>32.75%</p>\n"],
                    options_hi: ["<p>32%</p>\n", "<p>31.5%</p>\n",
                                "<p>32.5%</p>\n", "<p>32.75%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let ,</span><span style=\"font-family: Cambria Math;\"> MP of items be &#8377;1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP of 4 items = &#8377;4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of 3 items = &#8377;3</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Discount % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 25%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Since ,an</span><span style=\"font-family: Cambria Math;\"> additional discount of 10% is given .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, effective discount% = 25 + 10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 35 - 2.5 = 32.5 %</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Mangal;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;3</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 25%</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2330;&#2370;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, 10% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2340;&#2367;&#2352;&#2367;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\">% = 25 + 10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 35 - 2.5 = 32.5 %</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">A new mobile is introduced into the market and is marked at 25% above its original </span><span style=\"font-family: Cambria Math;\">cost</span><span style=\"font-family: Cambria Math;\"> price. What discount should be given on the marked price to gain a profit of 20%?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2379;&#2348;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2332;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Mangal;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    options_en: ["<p>2.50%</p>\n", "<p>5%</p>\n", 
                                "<p>4%</p>\n", "<p>10%</p>\n"],
                    options_hi: ["<p>2.50%</p>\n", "<p>5%</p>\n",
                                "<p>4%</p>\n", "<p>10%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let discount on mobile be x%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective rate = 25 - x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>x</mi></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 25 - x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = (25 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20% = (25 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5% =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 4%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2379;&#2348;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> x% </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 25 &ndash; x &ndash; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>x</mi></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 25 &ndash; x &ndash; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = (25 </span><span style=\"font-family: Cambria Math;\">&ndash; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20% </span><span style=\"font-family: Cambria Math;\">= (</span><span style=\"font-family: Cambria Math;\">25 &ndash; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">)%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5% =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = 4%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">A shopkeeper marks up his goods by 30% and then gives a discount of 10% on the marked price. Apart from this, he uses a faulty balance which reads 1 kg for 870 g. What is his net profit percentage?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 Afternoon</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Mangal;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2352;&#2366;&#2332;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> 870 gm </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2354;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2367;&#2326;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 24</span><span style=\"font-family: Cambria Math;\">/05/2022 Afternoon</span></p>\n",
                    options_en: ["<p>38.42%</p>\n", "<p>34.84%</p>\n", 
                                "<p>34.48%</p>\n", "<p>38.24%</p>\n"],
                    options_hi: ["<p>38.42%</p>\n", "<p>34.84%</p>\n",
                                "<p>34.48%</p>\n", "<p>38.24%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">At the time of shopkeeper uses faulty balance, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For him SP = </span><span style=\"font-family: Cambria Math;\">1000g ,</span><span style=\"font-family: Cambria Math;\"> CP = 870g</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective rate of 30% and 10% = 30 - 10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 20 - 3 =17%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;CP&nbsp; &nbsp;:&nbsp; &nbsp; SP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;117</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 870&nbsp; &nbsp; :&nbsp; &nbsp; 1000 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;_________________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 29&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;39</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Net profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>39</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>29</mn></mrow><mn>29</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>29</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 100 = 34.48%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2352;&#2366;&#2395;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 1000g,</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 870g</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">30% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 30 - 10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 20 - 3 =17%</span></p>\r\n<p><span style=\"font-family: Mangal;\">&nbsp;&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Mangal;\">&#2351;&nbsp;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;</span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; &nbsp;:&nbsp; </span><span style=\"font-family: Cambria Math;\">&nbsp;117</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;870&nbsp; &nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;1000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;___________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 29&nbsp; &nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;39</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>39</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>29</mn></mrow><mn>29</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>29</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 100 = 34.48%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">What will be a single equivalent discount for successive discounts of 20%, 40% and 75% on the marked price of an item?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 25</span><span style=\"font-family: Cambria Math;\">/05/2022 Afternoon</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20%, 40% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 75% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 25</span><span style=\"font-family: Cambria Math;\">/05/2022 Afternoon</span></p>\n",
                    options_en: ["<p>65%</p>\n", "<p>78%</p>\n", 
                                "<p>88%</p>\n", "<p>45%</p>\n"],
                    options_hi: ["<p>65%</p>\n", "<p>78%</p>\n",
                                "<p>88%</p>\n", "<p>45%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective single discount rate equivalent to three successive </span><span style=\"font-family: Cambria Math;\">discount</span><span style=\"font-family: Cambria Math;\"> of 20%, 40% and 75%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= a + b + c - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>a</mi><mi>c</mi></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mi>c</mi></mrow><mn>10000</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">(where a, b, c are discount rates)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 20 + 40 + 75 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&times;</mo><mn>40</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>40</mn><mo>&times;</mo><mn>75</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>75</mn><mo>&times;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&times;</mo><mn>40</mn><mo>&times;</mo><mn>75</mn></mrow><mn>10000</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 135 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5300</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60000</mn><mn>10000</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 135 - 53 + 6 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 88%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20%, 40% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 75% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2327;&#2366;&#2340;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= a + b + c - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mi>c</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>a</mi><mi>c</mi></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mi>a</mi><mi>b</mi><mi>c</mi></mrow><mn>10000</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Mangal;\">&#2332;&#2361;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> a, b, c </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 20 + 40 + 75 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&times;</mo><mn>40</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>40</mn><mo>&times;</mo><mn>75</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>75</mn><mo>&times;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&times;</mo><mn>40</mn><mo>&times;</mo><mn>75</mn></mrow><mn>10000</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 135 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5300</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60000</mn><mn>10000</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 135 - 53 + 6 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 88%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">A shopkeeper wanted to sell &#8377;2,700 worth of products. But he had two options &mdash; giving two successive discounts of 10% and 15%, respectively, or giving a single discount of 25%. What was the difference between the two options?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 25</span><span style=\"font-family: Cambria Math;\">/05/2022 Afternoon</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;2,700 </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 25</span><span style=\"font-family: Cambria Math;\">/05/2022 Afternoon</span></p>\n",
                    options_en: ["<p>&#8377;40.50</p>\n", "<p>&#8377;42.75</p>\n", 
                                "<p>&#8377;44.00</p>\n", "<p>&#8377;47.50</p>\n"],
                    options_hi: ["<p>&#8377;40.50</p>\n", "<p>&#8377;42.75</p>\n",
                                "<p>&#8377;44.00</p>\n", "<p>&#8377;47.50</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Net Effective discount of 10% and 15% = 10 + 15 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&times;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 25 - 1.5 = 23.5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Single Discount given = 25%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Difference of two discounts = 25 - 23.5 = 1.5% of 2700 = &#8377;40.50</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 10 + 15 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&times;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 25 - 1.5 = 23.5%</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 25%</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;</span><span style=\"font-family: Mangal;\">&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 25 - 23.5 = 1.5% of 2700 = &#8377;40.50</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> The price of a certain laptop is discounted by 20% and the reduced price is then discounted by 20%. This series of successive discounts is equivalent to a single discount of:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 25</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 25</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>20%</p>\n", "<p>26%</p>\n", 
                                "<p>30%</p>\n", "<p>36%</p>\n"],
                    options_hi: ["<p>20%</p>\n", "<p>26%</p>\n",
                                "<p>30%</p>\n", "<p>36%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective discount percent = -20 - 20 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&times;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = -36%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, single discount = 36%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = -20 - 20 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&times;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = -36%</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 36%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">A shopkeeper offers 25% discount on all table lamps. He offers a further discount of 8% on the reduced price to those customers who pay cash. What does a customer have to pay in cash for a table lamp of &#8377;2,500?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 25</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2335;&#2375;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2376;&#2306;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 25% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2340;&#2367;&#2352;&#2367;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> &#8377;2,500 </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2335;&#2375;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2376;&#2306;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 25</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>&#8377;1,725</p>\n", "<p>&#8377;1,865</p>\n", 
                                "<p>&#8377;2,220</p>\n", "<p>&#8377;1,780</p>\n"],
                    options_hi: ["<p>&#8377;1,725</p>\n", "<p>&#8377;1,865</p>\n",
                                "<p>&#8377;2,220</p>\n", "<p>&#8377;1,780</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required price = 2500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>25</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mrow><mn>100</mn><mo>-</mo><mn>8</mn></mrow><mn>100</mn></mfrac><mo>=</mo></math></span><span style=\"font-family: Cambria Math;\"> 1725</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 2500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>25</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mfrac><mrow><mn>100</mn><mo>-</mo><mn>8</mn></mrow><mn>100</mn></mfrac><mo>=</mo></math></span><span style=\"font-family: Cambria Math;\"> 1725</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> After allowing 15% discount, a dealer wishes to sell a machine for &#8377;1</span><span style=\"font-family: Cambria Math;\">,22,700</span><span style=\"font-family: Cambria Math;\">. At what price must the machine be marked? (Consider up to two decimals)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2337;&#2368;&#2354;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\">, &#8377;1</span><span style=\"font-family: Cambria Math;\">,22,700</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">? (</span><span style=\"font-family: Mangal;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>&#8377;1<span style=\"font-family: Cambria Math;\">,22,352.94</span></p>\n", "<p>&#8377;1<span style=\"font-family: Cambria Math;\">,44,352.94</span></p>\n", 
                                "<p>&#8377;1<span style=\"font-family: Cambria Math;\">,48,352.94</span></p>\n", "<p>&#8377;1<span style=\"font-family: Cambria Math;\">,36,352.94</span></p>\n"],
                    options_hi: ["<p>&#8377;1<span style=\"font-family: Cambria Math;\">,22,352.94</span></p>\n", "<p>&#8377;1<span style=\"font-family: Cambria Math;\">,44,352.94</span></p>\n",
                                "<p>&#8377;1<span style=\"font-family: Cambria Math;\">,48,352.94</span></p>\n", "<p>&#8377;1<span style=\"font-family: Cambria Math;\">,36,352.94</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the marked price = x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&times;</mo><mfrac><mn>85</mn><mn>100</mn></mfrac><mo>=</mo><mn>1</mn><mo>,</mo><mn>22</mn><mo>,</mo><mn>700</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = &#8377;1</span><span style=\"font-family: Cambria Math;\">,44,352.94</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, marked price = &#8377;1</span><span style=\"font-family: Cambria Math;\">,44,352.94</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = x</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&times;</mo><mfrac><mn>85</mn><mn>100</mn></mfrac><mo>=</mo><mn>1</mn><mo>,</mo><mn>22</mn><mo>,</mo><mn>700</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = &#8377;1</span><span style=\"font-family: Cambria Math;\">,44,352.94</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;1</span><span style=\"font-family: Cambria Math;\">,44,352.94</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> The price of an article is raised by 45% and then two successive discounts of 15% each are allowed. Ultimately the price of the article is _________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 45% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2340;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Mangal;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2327;</span><span style=\"font-family: Mangal;\">&#2368;</span><span style=\"font-family: Cambria Math;\"> ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>decreased by 7.7625%</p>\n", "<p>increased by 4.7625%</p>\n", 
                                "<p>decreased by 4.7625%</p>\n", "<p>increased by 7.7625%</p>\n"],
                    options_hi: ["<p>7.7625% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>4.7625% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>4.7625% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>7.7625% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">45% increase =&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">20&nbsp; &nbsp;:</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; 29</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15% discount =&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">20&nbsp; &nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;17</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15% discount =&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">20&nbsp; &nbsp; :&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 17</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;____________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 8000&nbsp; &nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp;8381</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Percentage increase = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>381</mn><mn>8000</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 4.7625%</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">45% </span><span style=\"font-family: Mangal;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> =&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">20&nbsp; &nbsp;:&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 29</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15% </span><span style=\"font-family: Mangal;\">&#2331;&#2370;</span><span style=\"font-family: Mangal;\">&#2335;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp;20&nbsp; &nbsp;:&nbsp; &nbsp;17</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15% </span><span style=\"font-family: Mangal;\">&#2331;&#2370;</span><span style=\"font-family: Mangal;\">&#2335;</span><span style=\"font-family: Cambria Math;\"> =&nbsp; &nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 20&nbsp; &nbsp;:&nbsp; &nbsp;17</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ______________________</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;8000&nbsp; &nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; 8381</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>381</mn><mn>8000</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> = 4.7625%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> X&rsquo;s salary is increased by 20% and then decreased by 20%. What is the change in salary?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> X </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;</span><span style=\"font-family: Mangal;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>4% decrease</p>\n", "<p>4% increase</p>\n", 
                                "<p>2% decrease</p>\n", "<p>2% increase</p>\n"],
                    options_hi: ["<p>4% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2350;&#2368;</span></p>\n", "<p>4% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n",
                                "<p>2% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2350;&#2368;</span></p>\n", "<p>2% <span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective discount percent = (+20</span><span style=\"font-family: Cambria Math;\">) +</span><span style=\"font-family: Cambria Math;\"> (&ndash; 20) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mo>-</mo><mn>20</mn><mo>)</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mo>+</mo><mn>20</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = - 4%</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= (</span><span style=\"font-family: Cambria Math;\">+20) + (&ndash; 20) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mo>-</mo><mn>20</mn><mo>)</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mo>(</mo><mo>+</mo><mn>20</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= - 4%</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> The marked price of a toy was &#8377;4,875. Successive discounts of 28% and 30% were offered on it during a clearance sale. What was the selling price of the toy?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;4,875 </span><span style=\"font-family: Mangal;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2354;&#2368;&#2351;&#2352;&#2375;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 28% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2312;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    options_en: ["<p>&#8377;2,457</p>\n", "<p>&#8377;2,047.50</p>\n", 
                                "<p>&#8377;2,057.50</p>\n", "<p>&#8377;2,467</p>\n"],
                    options_hi: ["<p>&#8377;2,457</p>\n", "<p>&#8377;2,047.50</p>\n",
                                "<p>&#8377;2,057.50</p>\n", "<p>&#8377;2,467</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective discount of 28% and 30% = 28 + 30 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>28</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>30</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 49.6%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP = 4875 &times; (100 - 49.6</span><span style=\"font-family: Cambria Math;\">)%</span><span style=\"font-family: Cambria Math;\"> = 4875 &times; 50.4% = &#8377;2457</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">28% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 28 + 30 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>28</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>30</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 49.6%</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 4875</span><span style=\"font-family: Cambria Math;\"> &times; (100 - 49.6)% = 4875 &times; 50.4% = &#8377;2457</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> After successive discounts of 25% and 10% a shirt was sold for &#8377;480. What was the original price of the shirt (Nearest to a &#8377;)?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">25% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2350;&#2368;&#2332;</span><span style=\"font-family: Cambria Math;\"> &#8377;480 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2312;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2358;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2367;&#2325;&#2335;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    options_en: ["<p>&#8377;723</p>\n", "<p>&#8377;720</p>\n", 
                                "<p>&#8377;711</p>\n", "<p>&#8377;708</p>\n"],
                    options_hi: ["<p>&#8377;723</p>\n", "<p>&#8377;720</p>\n",
                                "<p>&#8377;711</p>\n", "<p>&#8377;708</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP &times; </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>3</mn><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>9</mn><mn>10</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 480</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP = 480 &times; </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>10</mn><mn>9</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP = &#8377;711</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>3</mn><mrow><mn>4</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>9</mn><mn>10</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 480</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 480 &times; </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>10</mn><mn>9</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;711</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The marked price of a study table is &#8377;3,200. It will be offered for &#8377;2,448 after two successive discounts. If the first discount is 10%, the second discount is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2381;&#2335;&#2337;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2335;&#2375;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;3,200 </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;2,448 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2319;&#2327;&#2366;&#2404; </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;</span><span style=\"font-family: Mangal;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>13%</p>\n", "<p>10%</p>\n", 
                                "<p>15%</p>\n", "<p>18%</p>\n"],
                    options_hi: ["<p>13%</p>\n", "<p>10%</p>\n",
                                "<p>15%</p>\n", "<p>18%</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the second discount be x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP &times; Discount% = SP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; x = 2448</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2448</mn><mn>3200</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mrow><mn>9</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>272</mn><mn>320</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required discount = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>17</mn></mrow><mn>20</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 15%</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Mangal;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = x </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> % = </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; x = 2448</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2448</mn><mn>3200</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mrow><mn>9</mn><mo>&nbsp;</mo></mrow></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>272</mn><mn>320</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>17</mn></mrow><mn>20</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 15%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A tractor is sold after allowing three successive discounts of 10%, 5% and 2%. If the marked price of the tractor is Rs 4</span><span style=\"font-family: Cambria Math;\">,88,000</span><span style=\"font-family: Cambria Math;\">, find its net selling price.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2335;&#2381;&#2352;&#2376;&#2325;&#2381;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10%, 5% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2335;&#2381;&#2352;&#2376;&#2325;&#2381;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 4</span><span style=\"font-family: Cambria Math;\">,88,000</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2358;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 26</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>Rs 5<span style=\"font-family: Cambria Math;\">,08,895.2</span></p>\n", "<p>Rs 4<span style=\"font-family: Cambria Math;\">,49,895.06</span></p>\n", 
                                "<p>Rs 4<span style=\"font-family: Cambria Math;\">,18,895.45</span></p>\n", "<p>Rs 4<span style=\"font-family: Cambria Math;\">,08,895.2</span></p>\n"],
                    options_hi: ["<p>Rs 5<span style=\"font-family: Cambria Math;\">,08,895.2</span></p>\n", "<p>Rs 4<span style=\"font-family: Cambria Math;\">,49,895.06</span></p>\n",
                                "<p>Rs 4<span style=\"font-family: Cambria Math;\">,18,895.45</span></p>\n", "<p>Rs 4<span style=\"font-family: Cambria Math;\">,08,895.2</span></p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP &times; Discount % = SP</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">488000 &times; </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>19</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>49</mn><mn>50</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>,</mo><mn>88</mn><mo>,</mo><mn>952</mn></mrow><mn>10</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;4,0</span><span style=\"font-family: Cambria Math;\">8,895.2</span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> % = </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">488000 &times; </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>19</mn><mn>20</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>49</mn><mn>50</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>,</mo><mn>88</mn><mo>,</mo><mn>952</mn></mrow><mn>10</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;4,0</span><span style=\"font-family: Cambria Math;\">8,895.2</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The marked price of a mobile phone is &#8377;36,000.A shopkeeper gives a discount of 11%</span><span style=\"font-family: Cambria Math;\">on the marked price. Further, if a customer purchases it through credit card the discount increases by 15%.Pooja purchases it through the credit card. How much does she pay?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2379;&#2348;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;36,000 </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 11% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2375;&#2337;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2370;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2375;&#2337;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2370;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Morning )</span></p>\n",
                    options_en: ["<p>&#8377;31,000</p>\n", "<p>&#8377;32,000</p>\n", 
                                "<p>&#8377;30,880</p>\n", "<p>&#8377;30,600</p>\n"],
                    options_hi: ["<p>&#8377;31,000</p>\n", "<p>&#8377;32,000</p>\n",
                                "<p>&#8377;30,880</p>\n", "<p>&#8377;30,600</p>\n"],
                    solution_en: "<p>16<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Marked price of mobile phone = &#8377;36000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Since Pooja purchased it through a credit card. So, she get 15% discount.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The payment given by Pooja for mobile phone = 36000 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = &#8377;30600</span></p>\n",
                    solution_hi: "<p>16<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2379;&#2348;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;36000</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2330;&#2370;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2370;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2375;&#2337;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2367;&#2354;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2346;&#2370;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2379;&#2348;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 36000 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>85</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = &#8377;30600</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The price of a cell phone is &#8377;20,000. On Sundays, the shopkeeper offers a cash discount of &#8377;1,000 on the purchase of the cell phone. Further, if someone purchases it through a credit card, he gives 5% additional discount. If someone is purchasing the cell phone on a Sunday through a credit card, then how much does he/she have to pay?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    question_hi: "<p>17<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> &#8377;20,000 </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2357;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,000 </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2309;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2375;&#2337;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Mangal;\">&#2309;&#2340;&#2367;&#2352;&#2367;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2357;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2375;&#2337;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    options_en: ["<p>&#8377;18,350</p>\n", "<p>&#8377;18,900</p>\n", 
                                "<p>&#8377;18,050</p>\n", "<p>&#8377;18,500</p>\n"],
                    options_hi: ["<p>&#8377;18,350</p>\n", "<p>&#8377;18,900</p>\n",
                                "<p>&#8377;18,050</p>\n", "<p>&#8377;18,500</p>\n"],
                    solution_en: "<p>17<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Discount% </span><span style=\"font-family: Cambria Math;\">on purchasing</span><span style=\"font-family: Cambria Math;\"> cell phone through cash = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>20000</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective discount of 5% = 5 + 5 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 10 - 0.25 = 9.75% </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Payment by a person through credit card = 20000 &times; 90.25% = &#8377;18050 </span></p>\n",
                    solution_hi: "<p>17<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2344;&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2326;&#2352;&#2368;&#2342;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>20000</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 5%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 5 + 5 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 10 - 0.25 = 9.75% </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2375;&#2337;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 20000 &times; 90.25% = &#8377;18050 </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The difference between a discount of 35% on &#8377;8,000 and two successive discounts of20% and 15% on the same amount is:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    question_hi: "<p>18<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">&#8377;8,000 </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 35% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Afternoon )</span></p>\n",
                    options_en: ["<p>&#8377;240</p>\n", "<p>&#8377;320</p>\n", 
                                "<p>&#8377;540</p>\n", "<p>&#8377;800</p>\n"],
                    options_hi: ["<p>&#8377;240</p>\n", "<p>&#8377;320</p>\n",
                                "<p>&#8377;540</p>\n", "<p>&#8377;800</p>\n"],
                    solution_en: "<p>18<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Effective discount of 20% and 15% = 20 + 15 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&times;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 35 - 3 = 32%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required difference = 35% - 32% = 3% of 8000 = &#8377;240</span></p>\n",
                    solution_hi: "<p>18<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20% </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = 20 + 15 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&times;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 35 - 3 = 32%</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;</span><span style=\"font-family: Mangal;\">&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 35% - 32% = 3% of 8000 = &#8377;240</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A discount of 20% on the marked price of an article is allowed and then the article is sold for &#8377;1,380. The marked price of the article is _________.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,380 </span><span style=\"font-family: Mangal;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> _________ </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>&#8377;1,850</p>\n", "<p>&#8377;1,800</p>\n", 
                                "<p>&#8377;1,725</p>\n", "<p>&#8377;1,625</p>\n"],
                    options_hi: ["<p>&#8377;1,850</p>\n", "<p>&#8377;1,800</p>\n",
                                "<p>&#8377;1,725</p>\n", "<p>&#8377;1,625</p>\n"],
                    solution_en: "<p>19<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of an article = &#8377;1380</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP of an article = 1380 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>80</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6900</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;1725</span></p>\n",
                    solution_hi: "<p>19<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;1380</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 1380 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>80</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6900</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;1725</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A shopkeeper makes a profit of 12.5% after allowing a discount of 10% on the marked price of an article. Find his profit percentage if the article is sold at the marked price, allowing no discount.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    question_hi: "<p>20<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Mangal;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> 12.5% &#8203;&#8203;</span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2367;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2368;&#2332;&#2367;</span><span style=\"font-family: Mangal;\">&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC </span><span style=\"font-family: Cambria Math;\">CHSL 27</span><span style=\"font-family: Cambria Math;\">/05/2022 ( Evening )</span></p>\n",
                    options_en: ["<p>25%</p>\n", "<p>30%</p>\n", 
                                "<p>22.5%</p>\n", "<p>27%</p>\n"],
                    options_hi: ["<p>25%</p>\n", "<p>30%</p>\n",
                                "<p>22.5%</p>\n", "<p>27%</p>\n"],
                    solution_en: "<p>20<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let CP be &#8377;100</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of an article = 100 &times; 112.5% = &#8377;112.5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP of an article = 112.5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;125 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Since there is no discount on an article.</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, MP of an article = new SP of article</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>125</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 25% </span></p>\n",
                    solution_hi: "<p>20<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;100 </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 100 &times; 112.5% = &#8377;112.5</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Mangal;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 112.5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#8377;125 </span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2330;&#2370;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Mangal;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Mangal;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Mangal;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Mangal;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>125</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 25% </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>