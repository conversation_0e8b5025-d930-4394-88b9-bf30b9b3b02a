<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which famous experiment was done by Michael Faraday in 1831?</p>",
                    question_hi: "<p>1. माइकल फैराडे ने 1831 में कौन सा प्रसिद्ध प्रयोग किया था?</p>",
                    options_en: ["<p>Discovery of quantum magnetometers</p>", "<p>Discovery of law of elasticity</p>", 
                                "<p>Discovery of electromagnetic induction</p>", "<p>Discovery of natural radioactivity</p>"],
                    options_hi: ["<p>क्वांटम मैग्नेटोमीटर की खोज</p>", "<p>प्रत्यास्थता के नियम की खोज</p>",
                                "<p>विद्युत चुम्बकीय प्रेरण की खोज</p>", "<p>प्राकृतिक रेडियोधर्मिता की खोज</p>"],
                    solution_en: "<p>1.(c) <strong>Discovery of electromagnetic induction</strong>. Faraday\'s law states that an electromotive force is generated in an electrical conductor encircling a varying magnetic flux. Faraday\'s law of induction underpins the operation of various devices, including generators, transformers, inductors, and electric motors.</p>",
                    solution_hi: "<p>1.(c) <strong>विद्युत चुम्बकीय प्रेरण की खोज।</strong> फैराडे का नियम कहता है कि एक विद्युत चालक में एक विद्युत चालक बल उत्पन्न होता है जो एक परिवर्तनशील चुंबकीय प्रवाह को घेरे रहता है। फैराडे का प्रेरण का नियम जनरेटर, ट्रांसफार्मर, प्रेरक और इलेक्ट्रिक मोटर सहित विभिन्न उपकरणों के संचालन को आधार प्रदान करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. The book &lsquo;Poverty and Un-British Rule in India&rsquo; was written by _______.</p>",
                    question_hi: "<p>2. \'पॉवर्टी एंड अन-ब्रिटिश रूल इन इंडिया\' पुस्तक की रचना _______ के द्वारा की गई थी।</p>",
                    options_en: ["<p>MN Roy</p>", "<p>Dadabhai Naoroji</p>", 
                                "<p>Bipin Chandra Pal</p>", "<p>Lala Lajpat Rai</p>"],
                    options_hi: ["<p>एम.एन. रॉय</p>", "<p>दादाभाई नौरोजी</p>",
                                "<p>बिपिन चंद्र पाल</p>", "<p>लाला लाजपत राय</p>"],
                    solution_en: "<p>2.(b) <strong>Dadabhai Naoroji.</strong> He is known as the \"Grand Old Man of India\". He served as the President of the Indian National Congress three times: first in 1886, then in 1893, and finally in 1906. He was the first Indian who was elected to the British parliament. In pre-independent India, Dadabhai Naoroji was the first to discuss the concept of a poverty line.</p>",
                    solution_hi: "<p>2.(b) <strong>दादाभाई नौरोजी।</strong> उन्हें \"भारत के ग्रैंड ओल्ड मैन\" के रूप में जाना जाता है। उन्होंने तीन बार भारतीय राष्ट्रीय कांग्रेस के अध्यक्ष के रूप में कार्य किया: पहली बार 1886 में, फिर 1893 में और अंत में 1906 में। वे पहले भारतीय थे जो ब्रिटिश संसद के लिए चुने गए थे। स्वतंत्रता-पूर्व भारत में, दादाभाई नौरोजी गरीबी रेखा की अवधारणा पर चर्चा करने वाले पहले व्यक्ति थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Who reconciled Dalton\'s atomic hypothesis with Gay-Lussac\'s results on the combination of volumes in 1811 ?</p>",
                    question_hi: "<p>3. 1811 में आयतनों के संयोजन (combination of volumes) पर गे-लुसाक के परिणामों के साथ डाल्टन की परमाण्वीय परिकल्पना का मिलान किसने किया था?</p>",
                    options_en: ["<p>Robert Boyle</p>", "<p>Amadeo Avogadro</p>", 
                                "<p>Fred Hoyle</p>", "<p>Jacques Charles</p>"],
                    options_hi: ["<p>रॉबर्ट बॉयल (Robert Boyle)</p>", "<p>अमेदिओ अवोगाद्रो (Amadeo Avogadro)</p>",
                                "<p>फ़्रेड हॉयल (Fred Hoyle)</p>", "<p>जैक्स चार्ल्स (Jacques Charles)</p>"],
                    solution_en: "<p>3.(b) <strong>Amadeo Avogadro.</strong> Avogadro\'s notable paper sought to reconcile Dalton\'s atomic hypothesis with Gay-Lussac\'s findings on combining volumes. Dalton\'s theory, grounded in the law of conservation of mass and the law of constant composition, was the first comprehensive explanation of matter in terms of atoms and formed the foundation of chemistry. Dalton\'s atomic theory includes the following key points: atoms of the same element are identical; atoms of different elements differ; compounds consist of various types of atoms; and chemical reactions rearrange atoms.</p>",
                    solution_hi: "<p>3.(b) <strong>अमेदिओ अवोगाद्रो।</strong> अवोगाद्रो के उल्लेखनीय शोधपत्र में डाल्टन की परमाणु परिकल्पना को गे-लुसाक के आयतन संयोजन संबंधी निष्कर्षों के साथ सामंजस्य स्थापित करने का प्रयास किया गया था। डाल्टन का सिद्धांत, द्रव्यमान के संरक्षण के नियम और निरंतर संरचना के नियम पर आधारित, परमाणुओं के संदर्भ में पदार्थ की पहली व्यापक व्याख्या थी और इसने रसायन विज्ञान की नींव रखी। डाल्टन के परमाणु सिद्धांत में निम्नलिखित मुख्य बिंदु शामिल हैं: एक ही तत्व के परमाणु समान होते हैं; विभिन्न तत्वों के परमाणु भिन्न-भिन्न होते हैं; यौगिक विभिन्न प्रकार के परमाणुओं से मिलकर बने होते हैं; और रासायनिक अभिक्रियाएँ परमाणुओं को पुनर्व्यवस्थित करती हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Who was the chief coordinator for the G20 summit hosted by India?</p>",
                    question_hi: "<p>4. भारत द्वारा आयोजित G20 शिखर सम्मेलन के लिए मुख्य समन्वयक कौन है?</p>",
                    options_en: ["<p>S Jaishankar</p>", "<p>Vijay Gokhale</p>", 
                                "<p>Vinay Kwatra</p>", "<p>Harsh Vardhan Shringla</p>"],
                    options_hi: ["<p>एस जयशंकर</p>", "<p>विजय गोखले</p>",
                                "<p>विनय क्वात्रा</p>", "<p>हर्षवर्धन श्रृंगला</p>"],
                    solution_en: "<p>4.(d) <strong>Harsh Vardhan Shringla.</strong> The G20, or Group of 20, is an intergovernmental forum consisting of 19 sovereign countries, the European Union (EU), and the African Union (AU). It addresses major global economic issues, including international financial stability, climate change, and sustainable development. India hosted the 18th G20 summit in New Delhi on 9 September 2023, with the theme \"One Earth, One Family, One Future,\" inspired by ancient Sanskrit texts. Brazil will host the G20 in 2024, followed by South Africa in 2025.</p>",
                    solution_hi: "<p>4.(d) <strong>हर्षवर्धन श्रृंगला।</strong> जी-20 या ग्रुप ऑफ 20 एक अंतर-सरकारी मंच है जिसमें 19 संप्रभु देश, यूरोपीय संघ (EU) और अफ्रीकी संघ (AU) शामिल हैं। इसमें अंतर्राष्ट्रीय वित्तीय स्थिरता, जलवायु परिवर्तन और सतत विकास सहित प्रमुख वैश्विक आर्थिक मुद्दों पर चर्चा की गई है। भारत ने 9 सितंबर 2023 को नई दिल्ली में 18वें G20 शिखर सम्मेलन की मेजबानी की, जिसकी थीम \"वन अर्थ, वन फैमिली, वन फ्यूचर\" था, जो प्राचीन संस्कृत ग्रंथों से प्रेरित था। 2024 में ब्राज़ील G20 की मेजबानी करेगा, उसके बाद 2025 में दक्षिण अफ्रीका मेजबानी करेगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. The number of ministers, including the Chief Minister, in a state shall NOT be less than _______.</p>",
                    question_hi: "<p>5. किसी राज्य में मुख्यमंत्री सहित मंत्रियों की संख्या _______ से कम नहीं होनी चाहिए।</p>",
                    options_en: ["<p>11</p>", "<p>10</p>", 
                                "<p>13</p>", "<p>12</p>"],
                    options_hi: ["<p>11</p>", "<p>10</p>",
                                "<p>13</p>", "<p>12</p>"],
                    solution_en: "<p>5.(d) <strong>12.</strong> According to Article 164(1A) of the Indian Constitution, the total number of Ministers, including the Chief Minister, in a State shall not exceed 15% of the total number of members of the Legislative Assembly of that State. However, it also states that the number of Ministers shall not be less than 12.</p>",
                    solution_hi: "<p>5.(d) <strong>12.</strong> भारतीय संविधान के अनुच्छेद 164(1A) के अनुसार, किसी राज्य में मुख्यमंत्री सहित मंत्रियों की कुल संख्या उस राज्य की विधान सभा के कुल सदस्यों की संख्या के 15% से अधिक नहीं होगी। हालाँकि, इसमें यह भी कहा गया है कि मंत्रियों की संख्या 12 से कम नहीं होगी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Who among the following founded the Pala dynasty in 8th Century CE ?</p>",
                    question_hi: "<p>6. निम्नलिखित में से किसने 8वीं शताब्दी ईस्वी में पाल वंश की स्थापना की थी?</p>",
                    options_en: ["<p>Dharmapala</p>", "<p>Ramapala</p>", 
                                "<p>Devapala</p>", "<p>Gopala</p>"],
                    options_hi: ["<p>धर्मपाल</p>", "<p>रामपाल</p>",
                                "<p>देवपाल</p>", "<p>गोपाल</p>"],
                    solution_en: "<p>6.(d) <strong>Gopala.</strong> He was the first ruler of the Pala dunasty. Last notable ruler of the Pala dynasty was Rampala. The Pala dynasty ruled Bengal and Bihar from the 8th to the 12th century, promoting Buddhism and opposing Brahmanism. The rulers of the Pala dynasty in chronological order are Gopala, Dharmapala, Devapala, Mahipala I, and Madanapala.</p>",
                    solution_hi: "<p>6.(d) <strong>गोपाल।</strong> वे पाल राजवंश के पहले शासक थे। पाल राजवंश का अंतिम उल्लेखनीय शासक रामपाल था। पाल राजवंश ने 8वीं से 12वीं शताब्दी तक बंगाल और बिहार पर शासन किया और बौद्ध धर्म को बढ़ावा दिया तथा ब्राह्मणवाद का विरोध किया। कालानुक्रमिक क्रम में पाल राजवंश के शासक गोपाल, धर्मपाल, देवपाल, महिपाल प्रथम और मदनपाल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. ____________ Directive principles of State Policy were added to the Indian Constitution by the 42nd Constitutional Amendment Act of 1976.</p>",
                    question_hi: "<p>7. राज्य के ________ नीति निदेशक तत्&zwj;वों को 1976 के 42वें संविधान संशोधन अधिनियम द्वारा भारतीय संविधान में जोड़ा गया था।</p>",
                    options_en: ["<p>Two</p>", "<p>Three</p>", 
                                "<p>Five</p>", "<p>Four</p>"],
                    options_hi: ["<p>दो</p>", "<p>तीन</p>",
                                "<p>पाँच</p>", "<p>चार</p>"],
                    solution_en: "<p>7.(d) <strong>Four.</strong> These Directive Principles of State Policy (DPSP) are: Article 39(f) which provided that &ldquo;the State shall direct its policy towards securing for the children opportunities and facilities to develop in a healthy manner and in conditions of freedom and dignity and for protecting children and youth from exploitation and against material abandonment&rdquo;; Article 39(A) promotes equal justice and free legal aid for the poor; Article 43(A) secures workers\' participation in industry management; and Article 48(A) protects the environment and wildlife.</p>",
                    solution_hi: "<p>7.(d) <strong>चार।</strong> राज्य के नीति निर्देशक तत्व (DPSP) इस प्रकार हैं: अनुच्छेद 39 (f) जिसमें यह प्रावधान है कि &ldquo;राज्य अपनी नीति बच्चों को स्वस्थ तरीके से और स्वतंत्रता और सम्मान की स्थिति में विकसित होने के अवसर और सुविधाएं सुनिश्चित करने और बच्चों और युवाओं को शोषण और भौतिक परित्याग से बचाने की दिशा में निर्देशित करेगा&rdquo;; अनुच्छेद 39 (A) गरीबों के लिए समान न्याय और नि:शुल्क विधिक सहायता को बढ़ावा देता है; अनुच्छेद 43 (A) उद्योग प्रबंधन में श्रमिकों की भागीदारी सुनिश्चित करता है; और अनुच्छेद 48 (A) पर्यावरण और वन्य जीवन की रक्षा करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who published the logistic equation model of population growth in 1838 ?</p>",
                    question_hi: "<p>8. 1838 में किसने जनसंख्या वृद्धि के लॉजिस्टिक समीकरण मॉडल को प्रकाशित किया था?</p>",
                    options_en: ["<p>Howard Thomas Odum</p>", "<p>Alfred Russel Wallace</p>", 
                                "<p>Pierre Fran&ccedil;ois Verhulst</p>", "<p>George Evelyn Hutchinson</p>"],
                    options_hi: ["<p>हावर्ड थॉमस ओडुम (Howard Thomas Odum)</p>", "<p>अल्फ्रेड रसेल वॉलेस (Alfred Russel Wallace)</p>",
                                "<p>पियरे फ़्राँस्वा वेरहल्स्ट (Pierre Fran&ccedil;ois Verhulst)</p>", "<p>जॉर्ज एवलिन हचिंसन (George Evelyn Hutchinson)</p>"],
                    solution_en: "<p>8.(c) <strong>Pierre Fran&ccedil;ois Verhulst</strong>. He was a Belgian mathematician who earned a doctorate in number theory from the University of Ghent in 1825. He is renowned for the logistic growth model, which is widely applied in population growth modeling. This model, first proposed by Pierre-Fran&ccedil;ois Verhulst in 1838, states that the reproduction rate is proportional to both the existing population and the available resources, assuming all else is equal.</p>",
                    solution_hi: "<p>8.(c) <strong>पियरे फ़्राँस्वा वेरहल्स्ट (Pierre Fran&ccedil;ois Verhulst)।</strong> वे बेल्जियम के गणितज्ञ थे, जिन्होंने 1825 में गेन्ट विश्वविद्यालय से संख्या सिद्धांत में डॉक्टरेट की उपाधि प्राप्त की थी। वे लॉजिस्टिक ग्रोथ मॉडल के लिए प्रसिद्ध हैं, जिसका व्यापक रूप से जनसंख्या वृद्धि मॉडलिंग में उपयोग किया जाता है। यह मॉडल, जिसे सर्वप्रथम 1838 में पियरे फ़्राँस्वा वेरहल्स्ट ने प्रस्तावित किया था, जिसमें कहा गया था कि प्रजनन दर मौजूदा जनसंख्या और उपलब्ध संसाधनों दोनों के समानुपाती है, यह मानते हुए कि अन्य सभी समान है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. The ICAR &ndash; Sugarcane Breeding Institute is located at:</p>",
                    question_hi: "<p>9. ICAR - गन्ना प्रजनन संस्थान (Sugarcane Breeding Institute) कहां स्थित है?</p>",
                    options_en: ["<p>Vellore</p>", "<p>Coimbatore</p>", 
                                "<p>Tirunelveli</p>", "<p>Cuddalore</p>"],
                    options_hi: ["<p>वेल्लोर</p>", "<p>कोयंबटूर</p>",
                                "<p>तिरुनेलवेली</p>", "<p>कुड्डालोर</p>"],
                    solution_en: "<p>9.(b) <strong>Coimbatore.</strong> The Sugarcane Breeding Institute (SBI): Established in 1912. The Indian Council of Agricultural Research (ICAR) , an autonomous body, coordinates agricultural education and research in India and reports to the Department of Agricultural Research and Education, Ministry of Agriculture, established on July 16, 1929, in New Delhi.</p>",
                    solution_hi: "<p>9.(b) <strong>कोयंबटूर।</strong> गन्ना प्रजनन संस्थान (SBI): 1912 में स्थापित। भारतीय कृषि अनुसंधान परिषद (ICAR), एक स्वायत्त निकाय है, जो भारत में कृषि शिक्षा और अनुसंधान का समन्वय करता है और कृषि अनुसंधान और शिक्षा विभाग, कृषि मंत्रालय को रिपोर्ट करता है, जिसकी स्थापना 16 जुलाई, 1929 को नई दिल्ली में हुई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Who assumed charge as the new Vice Chief of the Indian Navy on 31 July 2021 ?</p>",
                    question_hi: "<p>10. 31 जुलाई 2021 को भारतीय नौसेना के नए उप प्रमुख के रूप में किसने पदभार ग्रहण किया?</p>",
                    options_en: ["<p>Karambir Singh</p>", "<p>Ravneet Singh</p>", 
                                "<p>SN Ghormade</p>", "<p>Dinesh K Tripathi</p>"],
                    options_hi: ["<p>करमबीर सिंह</p>", "<p>रवनीत सिंह</p>",
                                "<p>एस.एन. घोरमडे</p>", "<p>दिनेश के. त्रिपाठी</p>"],
                    solution_en: "<p>10.(c) <strong>SN Ghormade.</strong> The Indian Navy: Founded on January 26, 1950, its headquarters is in New Delhi. The navy\'s motto is \"Sam no Varunah\" (May the lord of oceans be auspicious unto us), and Navy Day is celebrated on December 4.</p>",
                    solution_hi: "<p>10.(c) <strong>एस.एन. घोरमडे।</strong> भारतीय नौसेना: 26 जनवरी, 1950 को स्थापित, इसका मुख्यालय नई दिल्ली में स्थित है। नौसेना का आदर्श वाक्य \"सम नो वरुणः\" (महासागरों के स्वामी हमारे लिए शुभ हों) है, और नौसेना दिवस 4 दिसंबर को मनाया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which Act was passed by the British Parliament in the year 1773 AD to regulate the activities of the East India Company?</p>",
                    question_hi: "<p>11. ईस्ट इंडिया कंपनी की गतिविधियों को विनियमित करने के लिए ब्रिटिश संसद ने वर्ष 1773 ई. में कौन-सा अधिनियम पारित किया था?</p>",
                    options_en: ["<p>Pitt&rsquo;s India Act</p>", "<p>Indian Slavery Act</p>", 
                                "<p>Age of Consent Act</p>", "<p>Regulating Act</p>"],
                    options_hi: ["<p>पिट्स इंडिया अधिनियम</p>", "<p>भारतीय दासता अधिनियम</p>",
                                "<p>सहमति की आयु अधिनियम</p>", "<p>विनियमन अधिनियम</p>"],
                    solution_en: "<p>11.(d) <strong>Regulating Act.</strong> This Act was passed in June 1773. It was a response to the East India Company\'s semi-sovereign status in Bengal, Bihar, and Orissa, which was created after the battles of Plassey and Buxar. The act was intended to address the company\'s administration, but it wasn\'t a long-term solution. In 1784, the Pitt\'s India Act was passed as a more radical reform.</p>",
                    solution_hi: "<p>11.(d) <strong>विनियमन अधिनियम।</strong> यह अधिनियम जून 1773 में पारित किया गया था। यह बंगाल, बिहार और उड़ीसा में ईस्ट इंडिया कंपनी की अर्ध-संप्रभु स्थिति की प्रतिक्रिया थी, जिसे प्लासी और बक्सर के युद्ध के बाद बनाया गया था। इस अधिनियम का उद्देश्य कंपनी के प्रशासन को संबोधित करना था, लेकिन यह दीर्घकालिक समाधान नहीं था। 1784 में, पिट्स इंडिया अधिनियम को अधिक क्रांतिकारी सुधार के रूप में पारित किया गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Match the following items.<br><strong id=\"docs-internal-guid-7f4c319e-7fff-5348-5b82-08c6aca385c8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdb3hjtHqLbilJDpVCB9R90SZH4y8-cW_NhBcXG_VNJmvkFpvTEkKquQTYwLI9YEzI-3iDaj_FUGj3MfSFUJkqeZmU6AY4wcGaw_QbDnCoC15ZEr6TP9hHWWc7GFRTk93hT5BX1?key=6zXzYYSm3-7jZ_enT9UUL1uY\" width=\"430\" height=\"130\"></strong></p>",
                    question_hi: "<p>12. निम्नलिखित मदों का मिलान कीजिए I<br><strong id=\"docs-internal-guid-52853536-7fff-8688-aa55-1d09782bd981\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdHzzSPNOhwFAQ8-dI-ics36WAc9hihC5mPmcvWLfXLANNWpL82tqUczhHKQeGXxSmnY0ghN9oukid4-kSSmbGzVMqwTkxI_R3OgW8wvaYfZQzbrlkWB3cl9_LfdkcIlnlpJdbJ5Q?key=6zXzYYSm3-7jZ_enT9UUL1uY\" width=\"409\" height=\"140\"></strong></p>",
                    options_en: ["<p>i (b), ii (d), iii (a), iv (c)</p>", "<p>i (d), ii (a), iii (b), iv (c)</p>", 
                                "<p>i (b), ii (c), iii (d), iv (a)</p>", "<p>i (c), ii (d), iii (b), iv (a)</p>"],
                    options_hi: ["<p>i (b), ii (d), iii (a), iv (c)</p>", "<p>i (d), ii (a), iii (b), iv (c)</p>",
                                "<p>i (b), ii (c), iii (d), iv (a)</p>", "<p>i (c), ii (d), iii (b), iv (a)</p>"],
                    solution_en: "<p>12.(a) <strong>i (b), ii (d), iii (a), iv (c).</strong> The Kasturba Gandhi Balika Vidyalaya (KGBV) is a government-run residential school for girls from weaker sections of society. The Mid-Day Meal Scheme (MDM) provides free, nutritious meals to school children across India. The National Assessment and Accreditation Council (NAAC) accredits higher education institutions, while the All India Council for Technical Education (AICTE) oversees technical education under the Department of Higher Education.</p>",
                    solution_hi: "<p>12.(a) <strong>i (b), ii (d), iii (a), iv (c).</strong> कस्तूरबा गांधी बालिका विद्यालय (KGBV) समाज के कमजोर वर्गों की लड़कियों के लिए सरकार द्वारा संचालित आवासीय विद्यालय है। मध्याह्न भोजन योजना (MDM) पूरे भारत में स्कूली बच्चों को निःशुल्क, पौष्टिक भोजन प्रदान करती है। राष्ट्रीय मूल्यांकन और प्रत्यायन परिषद (NAAC) उच्च शिक्षा संस्थानों को मान्यता देती है, जबकि अखिल भारतीय तकनीकी शिक्षा परिषद (AICTE) उच्च शिक्षा विभाग के तहत तकनीकी शिक्षा की देखरेख करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Where was the first meeting of the Employment Working Group of G20 conducted ?</p>",
                    question_hi: "<p>13. G20 के रोजगार कार्य समूह की पहली बैठक कहाँ आयोजित की गई?</p>",
                    options_en: ["<p>Jaipur</p>", "<p>Jodhpur</p>", 
                                "<p>Lucknow</p>", "<p>Kolkata</p>"],
                    options_hi: ["<p>जयपुर</p>", "<p>जोधपुर</p>",
                                "<p>लखनऊ</p>", "<p>कोलकाता</p>"],
                    solution_en: "<p>13.(b) <strong>Jodhpur.</strong> The first G20 Employment Working Group (EWG)This meeting was part of India\'s presidency of the G20, which focused on addressing employment challenges and promoting inclusive labor markets among member countries. The discussions at this meeting aimed to formulate strategies for enhancing employment opportunities and tackling the impacts of global economic changes on the workforce. The G20, an intergovernmental forum of 19 countries and the EU, was founded in 1999 in response to global economic crises.</p>",
                    solution_hi: "<p>13.(b) <strong>जोधपुर।</strong> पहला G20 रोजगार कार्य समूह (EWG) यह बैठक G20 की भारत की अध्यक्षता का हिस्सा थी, जिसका ध्यान रोजगार चुनौतियों को संबोधित करने और सदस्य देशों के बीच समावेशी श्रम बाजारों को बढ़ावा देने पर केंद्रित था। इस बैठक में चर्चा का उद्देश्य रोजगार के अवसरों को बढ़ाने और कार्यबल पर वैश्विक आर्थिक परिवर्तनों के प्रभावों से निपटने के लिए रणनीति तैयार करना था। G20, 19 देशों और यूरोपीय संघ का एक अंतर-सरकारी मंच है, जिसकी स्थापना 1999 में वैश्विक आर्थिक संकटों के जवाब में की गई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. As per the Census of India 2011, the increase in literacy rates for males and females, respectively, as compared to 2001 Census was:</p>",
                    question_hi: "<p>14. भारत की जनगणना 2011 के अनुसार, 2001 की जनगणना की तुलना में पुरुषों और महिलाओं की साक्षरता दर में क्रमशः कितनी वृद्धि हुई है?</p>",
                    options_en: ["<p>3.88% and 13.79%</p>", "<p>5.88% and 12.79%</p>", 
                                "<p>6.88% and 11.79%</p>", "<p>4.88% and 10.79%</p>"],
                    options_hi: ["<p>3.88% और 13.79%</p>", "<p>5.88% और 12.79%</p>",
                                "<p>6.88% और 11.79%</p>", "<p>4.88% और 10.79%</p>"],
                    solution_en: "<p>14.(c) <strong>6.88% and 11.79%</strong>. Literacy is the ability to read and write with comprehension for those aged seven and above. According to the 2011 Census, India&rsquo;s overall literacy rate was 74.04%, which indeed represents an increase of approximately 14% from the previous census in 2001, with rural women showing the highest rise at 26%. Kerala had the highest literacy rate at 93.91%, and Bihar the lowest at 63.82%.</p>",
                    solution_hi: "<p>14.(c) <strong>6.88% और 11.79%</strong>. साक्षरता सात वर्ष या उससे अधिक आयु के बच्चों के लिए समझ के साथ पढ़ने और लिखने की क्षमता है। 2011 की जनगणना के अनुसार, भारत की कुल साक्षरता दर 74.04% थी, जो वास्तव में 2001 की पिछली जनगणना से लगभग 14% की वृद्धि दर्शाती है, जिसमें ग्रामीण महिलाओं में सर्वाधिक 26% की वृद्धि देखी गई। केरल में साक्षरता दर सबसे अधिक 93.91% थी, और बिहार में सबसे कम 63.82% थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. What is the salinity of the Dead Sea (per litre of water)?</p>",
                    question_hi: "<p>15. मृत सागर की लवणता (प्रति लीटर जल की) कितनी है?</p>",
                    options_en: ["<p>440 grams</p>", "<p>240 grams</p>", 
                                "<p>340 grams</p>", "<p>390 grams</p>"],
                    options_hi: ["<p>440 ग्राम</p>", "<p>240 ग्राम</p>",
                                "<p>340 ग्राम</p>", "<p>390 ग्राम</p>"],
                    solution_en: "<p>15.(c) <strong>340 grams.The</strong> Dead Sea has an extremely high salinity level, with approximately 340 grams of dissolved salts per liter of water. This is roughly 8-9 times saltier than regular seawater, which has an average salinity of around 35 grams per liter. No outlet: The Dead Sea has no outlet to the ocean, causing salts to accumulate. High evaporation: Water evaporates quickly due to the hot desert climate. Mineral-rich inflows: Rivers and streams flowing into the Dead Sea carry high concentrations of minerals.</p>",
                    solution_hi: "<p>15.(c) <strong>340 ग्राम।</strong> मृत सागर में लवणता का स्तर सर्वाधिक है, प्रति लीटर जल में लगभग 340 ग्राम घुलित लवण हैं। यह सामान्य समुद्री जल की तुलना में लगभग 8-9 गुना अधिक खारा है, जिसकी औसत लवणता लगभग 35 ग्राम प्रति लीटर है। कोई निकास नहीं: मृत सागर का समुद्र में कोई निकास नहीं है, जिससे लवण जमा हो जाते हैं। उच्च वाष्पीकरण: गर्म रेगिस्तानी जलवायु के कारण जल जल्दी वाष्पित हो जाता है। खनिज-समृद्ध अंतर्वाह: मृत सागर में प्रवाहित होने वाली नदियाँ और धाराएँ खनिजों की उच्च सांद्रता ले जाती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Papanasam Ramayya Sivan was an Indian composer of which music style?</p>",
                    question_hi: "<p>16. पापनासम रामय्या सिवन किस संगीत शैली के भारतीय संगीतकार थे?</p>",
                    options_en: ["<p>Qawwali</p>", "<p>Carnatic music</p>", 
                                "<p>Bhajan</p>", "<p>Folk music</p>"],
                    options_hi: ["<p>क़व्वाली</p>", "<p>कर्नाटक संगीत</p>",
                                "<p>भजन</p>", "<p>लोक संगीत</p>"],
                    solution_en: "<p>16.(b) <strong>Carnatic music.</strong> Paapanaasam Raamayya Sivan was awarded the Sangeetha Kalanidhi by the Madras Music Academy in 1971. He also composed film scores for Kannada and Tamil cinema in the 1930s and 1940s and was known as Tamil Thyagaraja. Amir Khusrow (1253-1325) is credited with creating qawwali, while Tukadoji Maharaj introduced the Bhajan and the Kanjiri Bhajan, performed with the Kanjiri instrument.</p>",
                    solution_hi: "<p>16.(b) <strong>कर्नाटक संगीत।</strong> पापनासम रामय्या सिवन को 1971 में मद्रास संगीत अकादमी द्वारा संगीत कलानिधि से सम्मानित किया गया था। उन्होंने 1930 और 1940 के दशक में कन्नड़ और तमिल सिनेमा के लिए फ़िल्म संगीत भी तैयार किया और उन्हें तमिल त्यागराज के नाम से जाना जाता था। अमीर खुसरो (1253-1325) को कव्वाली बनाने का श्रेय दिया जाता है, जबकि तुकादोजी महाराज ने भजन और कंजरी भजन की शुरुआत की, जिसे कंजरी वाद्य यंत्र के साथ बजाया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which of the following is the first Indian woman Grandmaster in Chess?</p>",
                    question_hi: "<p>17. निम्नलिखित में से कौन शतरंज में पहली भारतीय महिला ग्रैंडमास्टर है?</p>",
                    options_en: ["<p>Harika Dronavalli</p>", "<p>S Vijayalakshmi</p>", 
                                "<p>Tania Sachdev</p>", "<p>R Vaishali</p>"],
                    options_hi: ["<p>हरिका द्रोणावल्ली</p>", "<p>एस. विजयालक्ष्मी</p>",
                                "<p>तानिया सचदेव</p>", "<p>आर. वैशाली</p>"],
                    solution_en: "<p>17.(b)<strong> S Vijayalakshmi.</strong> She is an Indian chess player holding the FIDE titles of International Master (IM) and Woman Grandmaster (WGM). Notable Indian female chess players include Koneru Humpy, Vaishali Rameshbabu, Harika Dronavalli, Eesha Karavade, Divya Deshmukh, and Mary Ann Gomes.</p>",
                    solution_hi: "<p>17.(b) <strong>एस. विजयालक्ष्मी।</strong> वह एक भारतीय शतरंज खिलाड़ी हैं, जिन्होंने FIDE के अंतर्राष्ट्रीय मास्टर (IM) और महिला ग्रैंडमास्टर (WGM) की खिताब जीती हैं। अन्य उल्लेखनीय भारतीय महिला शतरंज खिलाड़ियों में कोनेरू हंपी, वैशाली रमेशबाबू, हरिका द्रोणावल्ली, ईशा करावडे, दिव्या देशमुख और मैरी एन गोम्स शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. Which of the following is NOT a major climate control of a place?",
                    question_hi: " 18. निम्नलिखित में से कौन सा किसी स्थान का प्रमुख जलवायु नियंत्रण नहीं है?",
                    options_en: [" Altitude   ", " Latitude ", 
                                " Interior of the earth ", " Ocean currents"],
                    options_hi: [" उन्नतांश ", " अक्षांश",
                                " भूगर्भ ", " महासागरीय धाराएँ"],
                    solution_en: "<p>18.(c) <strong>Interior of the earth</strong>. Climate refers to the long-term weather patterns of a region, influenced by its atmospheric and environmental conditions. Six major factors control the climate of any place: latitude, altitude, pressure and wind systems, distance from the sea, continentality, ocean currents, and relief features.</p>",
                    solution_hi: "<p>18.(c) <strong>भूगर्भ।</strong> जलवायु किसी क्षेत्र के दीर्घकालिक मौसम प्रतिरूपों को संदर्भित करता है, जो उसके वायुमंडलीय और पर्यावरणीय स्थितियों से प्रभावित होता है। किसी भी स्थान की जलवायु को नियंत्रित करने वाले छह प्रमुख कारक हैं: अक्षांश, ऊँचाई, दाब और वायु प्रणाली, समुद्र से दूरी, महाद्वीपीयता, महासागरीय धाराएँ और राहत विशेषताएँ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Who among the following is known as &lsquo;Captain Cool&rsquo; in Indian Cricket?</p>",
                    question_hi: "<p>19. भारतीय क्रिकेट में निम्नलिखित में से किसे \'कैप्टन कूल\' के नाम से जाना जाता है?</p>",
                    options_en: ["<p>Virat Kohli</p>", "<p>Mahendra Singh Dhoni</p>", 
                                "<p>Kapil Dev</p>", "<p>Sachin Tendulkar</p>"],
                    options_hi: ["<p>विराट कोहली</p>", "<p>महेन्द्र सिंह धोनी</p>",
                                "<p>कपिल देव</p>", "<p>सचिन तेंदुलकर</p>"],
                    solution_en: "<p>19.(b) <strong>Mahendra Singh Dhoni</strong>. He is also known as \"Thala,\" and \"MSD,\". Virat Kohli, nicknamed \"Cheeku,\" \"King Kohli,\" and \"The Run Machine,\" is renowned for his batting. Kapil Dev, known as the \"Haryana Hurricane,\" was famous for his aggressive play, while Sachin Tendulkar is affectionately called the \"God of Cricket\" and \"Master Blaster&rdquo;.</p>",
                    solution_hi: "<p>19.(b) <strong>महेंद्र सिंह धोनी।</strong> उन्हें \"थाला\" और \"MSD\" के नाम से भी जाना जाता है। विराट कोहली, जिन्हें \"चीकू\", \"किंग कोहली\" और \"द रन मशीन\" के नाम से जाना जाता है, अपनी बल्लेबाजी के लिए प्रसिद्ध हैं। कपिल देव, जिन्हें \"हरियाणा हरिकेन\" के नाम से जाना जाता है, अपने आक्रामक खेल के लिए प्रसिद्ध थे, जबकि सचिन तेंदुलकर को प्यार से \"क्रिकेट का भगवान\" और \"मास्टर ब्लास्टर\" कहा जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. In which of the following states is the Chu-Faat dance performed?</p>",
                    question_hi: "<p>20. निम्नलिखित में से किस राज्य में चू-फाट (Chu-Faat) नृत्य किया जाता है?</p>",
                    options_en: ["<p>Haryana</p>", "<p>Uttarakhand</p>", 
                                "<p>West Bengal</p>", "<p>Sikkim</p>"],
                    options_hi: ["<p>हरियाणा</p>", "<p>उत्तराखंड</p>",
                                "<p>पश्चिम बंगाल</p>", "<p>सिक्किम</p>"],
                    solution_en: "<p>20.(d) <strong>Sikkim.</strong> Chu-Faat, meaning \"Snowy Range\" (Chu) and \"Worship\" (Faat), is a folk dance of Sikkim performed in honor of Mount Khangchendzonga. Other folk dances of Sikkim - Sikmari, Singhi Chaam or the Snow Lion Dance, Yak Chaam, Khukuri Naach. Other states and their folk dance: Haryana - Jhumar, Phag, Daph, Dhamal, Loor, Gugga, Khor, Gagor. West Bengal - Kathi, Gambhira, Dhali, Jatra, Baul, Marasia, Mahal, Keertan. Uttarakhand - Garhwali, Kumayuni, Kajari, Jhora, Raslila, Chappeli.</p>",
                    solution_hi: "<p>20.(d) <strong>सिक्किम।</strong> चू-फ़ाट, (चू) जिसका अर्थ है \"बर्फीला परिसर\" और (फ़ाट) का अर्थ \"पूजा\" है , सिक्किम का एक लोक नृत्य है जो कंचनजंगा पर्वत के सम्मान में किया जाता है। सिक्किम के अन्य लोक नृत्य - सिकमारी, सिंघई चाम या स्नो लायन नृत्य, याक छाम, खूखूरी नाच। अन्य राज्य और उनके लोक नृत्य: हरियाणा - झूमर, फाग, दाफ़ा, धमाल, लूर, गुगा, खोरिया , गागोरो। पश्चिम बंगाल - काठी, गंभीरा, संधाली, जात्रा, बाउल, मरासिया, महल, कीर्तन। उत्तराखंड - गढ़वाली, कुमायुनी, कजरी, झोरा, रासलीला, छपेली।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Digestion of food is an important function of the animal body. In animals like lions, cows, humans, etc., the process involves use of various organs starting from the mouth and ending with the anus. The longest part of this canal is known as the ________.</p>",
                    question_hi: "<p>21. खाद्य पदार्थों का पाचन, जन्तु शरीर का एक महत्वपूर्ण कार्य होता है। शेर, गाय, मनुष्य आदि जंतुओं में, मुख से शुरू होकर गुदा (anus) तक विभिन्न अंगों का उपयोग इस प्रक्रिया में शामिल होता है। इस मार्ग के दीर्घतम भाग को ________ कहा जाता है।</p>",
                    options_en: ["<p>stomach</p>", "<p>large intestine</p>", 
                                "<p>oesophagus</p>", "<p>small intestine</p>"],
                    options_hi: ["<p>आमाशय (stomach)</p>", "<p>बड़ी आंत (large intestine)</p>",
                                "<p>ग्रसिका (oesophagus)</p>", "<p>छोटी आंत (small intestine)</p>"],
                    solution_en: "<p>21.(d) <strong>Small intestine.</strong> It is a 20-foot tube in the digestive system, breaks down food and absorbs nutrients. It has three parts: the duodenum, jejunum, and ileum, and helps digest food from the stomach while absorbing vitamins, minerals, carbs, fats, proteins, and water. The large intestine, about 5 feet long, is wider but shorter than the small intestine. The esophagus in adults is typically 10 - 13 inches long and about &frac34; inch in diameter at its narrowest point.</p>",
                    solution_hi: "<p>21.(d) <strong>छोटी आंत।</strong> यह पाचन तंत्र में 20 फुट लंबी नलिका होती है, जो खाद्य पदार्थ को तोड़ती है और पोषक तत्वों को अवशोषित करती है। इसके तीन भाग होते हैं: डुओडेनम, जेजुनम ​​और इलियम, और यह आमाशय से भोजन को पचाने में मदद करता है जबकि विटामिन, खनिज, कार्बोहाइड्रेट, वसा, प्रोटीन और पानी को अवशोषित करता है। बड़ी आंत, लगभग 5 फीट लंबी, छोटी आंत से चौड़ी लेकिन छोटी होती है। वयस्कों में ग्रासनली आमतौर पर 10 - 13 इंच लंबी होती है और इसके सबसे संकीर्ण बिंदु पर लगभग &frac34; इंच व्यास की होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. The famous santoor player Pandit Shivkumar Sharma received the__________________ in the year 1986.",
                    question_hi: " 22. वर्ष 1986 में, प्रसिद्ध संतूर वादक पंडित शिवकुमार शर्मा को ____________ से सम्मानित किया गया था।",
                    options_en: [" Maharashtra Gurav Puraskar", " Sangeet Natak Akademi Award ", 
                                " Sanskriti Award", " Kalidas Samman"],
                    options_hi: [" महाराष्ट्र गौरव पुरस्कार", " संगीत नाटक अकादमी पुरस्कार ",
                                " संस्कृति पुरस्कार ", " कालिदास सम्मान<br /> "],
                    solution_en: "<p>22. (b) <strong>Sangeet Natak Akademi Award</strong>. Also known as the Akademi Puraskar, this award honors excellence in the performing arts. The santoor, a trapezoid-shaped hammered dulcimer made of walnut, is popular in India. Pandit Shivkumar Sharma received the Sangeet Natak Akademi Award in 1986, followed by the Padma Shri in 1991 and the Padma Bhushan in 2001. Other notable santoor players include Pandit Bhajan Sopori and Pandit Tarun Bhattacharya.</p>",
                    solution_hi: "<p>22.(b) <strong>संगीत नाटक अकादमी पुरस्कार</strong>। अकादमी पुरस्कार के नाम से भी जाना जाने वाला यह पुरस्कार प्रदर्शन कला में उत्कृष्टता का सम्मान करता है। अखरोट से बना समलम्बाकार आकार का संतूर भारत में लोकप्रिय है। पंडित शिवकुमार शर्मा को 1986 में संगीत नाटक अकादमी पुरस्कार मिला, उसके बाद 1991 में पद्म श्री और 2001 में पद्म भूषण से सम्मानित किया गया। अन्य उल्लेखनीय संतूर वादकों में पंडित भजन सोपोरी और पंडित तरुण भट्टाचार्य शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Which of the following is a festival primarily celebrated in the state of Odisha?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन-सा त्योहार मुख्य रूप से ओडिशा राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Baishagu</p>", "<p>Chhath</p>", 
                                "<p>Raja Parba</p>", "<p>Me-Dam-Me-Phi</p>"],
                    options_hi: ["<p>बैशागु</p>", "<p>छठ</p>",
                                "<p>रज पर्व</p>", "<p>मी-डैम-मी-फी</p>"],
                    solution_en: "<p>23.(c) <strong>Raja Parba.</strong> It is a unique festival celebrating the onset of the monsoon and the earth&rsquo;s womanhood. Odisha\'s famous festivals include Konark Dance Festival, Kalinga Mahotsava, Durga Puja, Chhau Festival, Puri Beach Festival, Nuakhai, Pakhala Dibasa, and Laxmi Puja. Baishagu, celebrated by the Boro Kacharis of Assam, marks the new year. Chhath is a major Hindu festival in Bihar and eastern Uttar Pradesh, while Me-Dam-Me-Phi is an ancestor worship festival of the Ahom people in Assam.</p>",
                    solution_hi: "<p>23.(c) <strong>रज पर्व।</strong> यह मानसून की शुरुआत और पृथ्वी की नारीत्व का जश्न मनाने वाला एक अनूठा त्योहार है। ओडिशा के प्रसिद्ध त्योहारों में कोणार्क नृत्य महोत्सव, कलिंग महोत्सव, दुर्गा पूजा, छऊ महोत्सव, पुरी बीच महोत्सव, नुआखाई, पखला दिवस और लक्ष्मी पूजा शामिल हैं। असम के बोरो कचारियों द्वारा मनाया जाने वाला बैशागु, नए वर्ष का प्रतीक है। छठ, बिहार और पूर्वी उत्तर प्रदेश में एक प्रमुख हिंदू त्योहार है, जबकि मे-डैम-मे-फी असम में अहोम लोगों का एक पूर्वज पूजा त्योहार है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "24. Which of the following is a difference between self-help groups (SHGs) and microfinance institutions (MFIs) in India?",
                    question_hi: "24. निम्नलिखित में से कौन-सा विकल्प भारत में स्वयं सहायता समूहों (SHGs) और सूक्ष्म वित्त संस्थाओं (MFIs) के बीच अंतर है?",
                    options_en: ["  SHGs are non-profit organisations, while MFIs are for-profit organisations", "  SHGs provide only credit, while MFIs provide a range of financial services.", 
                                "  SHGs are typically small, community-based organisations, while MFIs are larger and more formal institutions.", " SHGs rely on government subsidies for funding, while MFIs rely on commercial sources of funding."],
                    options_hi: [" SHGs गैर-लाभकारी संगठन हैं, जबकि MFIs लाभकारी संगठन हैं। ", " SHG केवल क्रेडिट प्रदान करते हैं, जबकि MFI वित्तीय सेवाओं की एक शृंखला प्रदान करते हैं।",
                                " SHG आमतौर पर छोटे, समुदाय-आधारित संगठन होते हैं, जबकि MFI बड़े और अधिक औपचारिक संस्था होते हैं। ", " SHGs वित्तपोषण के लिए सरकारी सब्सिडी पर निर्भर होते हैं, जबकि MFIs वित्तपोषण के वाणिज्यिक स्रोतों पर भरोसा करते हैं।"],
                    solution_en: "24.(c) Self-Help Groups (SHGs) in India, started in the late 1980s by NGOs, typically consist of 10 to 20 women, serving as financial intermediaries. Examples include Amba Foundation, ASRLM, and Chamoli SHG. Microfinance institutions (MFIs), like Ujivan Financial Services and Annapurna Finance, provide banking services to low-income groups, playing a key role in poverty alleviation.",
                    solution_hi: "24.(c) भारत में स्वयं सहायता समूह (SHG) 1980 के दशक के अंत में NGO द्वारा शुरू किए गए थे, जिनमें आमतौर पर 10 से 20 महिलाएं शामिल होती हैं, जो वित्तीय मध्यस्थ के रूप में काम करती हैं। उदाहरणों में अंबा फाउंडेशन, ASRLM और चमोली SHG शामिल हैं। उज्वजीवन फाइनेंशियल सर्विसेज और अन्नपूर्णा फाइनेंस जैसी माइक्रोफाइनेंस संस्थाएं (MFI) कम आय वाले समूहों को बैंकिंग सेवाएं प्रदान करती हैं, जो गरीबी उन्मूलन में महत्वपूर्ण भूमिका निभाती हैं।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. The Aryans lived in the land known as &lsquo;Sapta Sindhu&rsquo; (Land of the Seven Rivers). Which of the following was NOT a part of it?</p>",
                    question_hi: "<p>25. आर्य उस भूमि पर रहते थे जिसे सप्त सिंधु (सात नदियों की भूमि) कहा जाता था। निम्नलिखित में से कौन-सा इसका भाग नहीं था?</p>",
                    options_en: ["<p>Indus</p>", "<p>Shipra</p>", 
                                "<p>Ravi</p>", "<p>Jhelum</p>"],
                    options_hi: ["<p>सिंधु</p>", "<p>शिप्रा</p>",
                                "<p>रावी</p>", "<p>झेलम</p>"],
                    solution_en: "<p>25.(b) <strong>Shipra.</strong> The Shipra (Kshipra), a 195 km tributary of the Chambal River in Madhya Pradesh, flows across the Malwa Plateau, passing through Ujjain, Ratlam, and Mandsaur before joining the Chambal. The Sapta Sindhu, mentioned in Vedic literature, refers to the \"Land of Seven Rivers\" in Punjab, home to early Aryan settlements. The seven rivers include: Indus, Sutlej (Sutudri), Ravi (Parusni), Chenab (Asikni), Jhelum (Vitasta), Beas (Vipas), and Saraswati.</p>",
                    solution_hi: "<p>25.(b) <strong>शिप्रा।</strong> यह मध्य प्रदेश में चंबल नदी की 195 किलोमीटर लंबी सहायक नदी शिप्रा (क्षिप्रा) मालवा पठार से होकर गुजरती है, जो चंबल में मिलने से पहले उज्जैन, रतलाम और मंदसौर से होकर गुजरती है। वैदिक साहित्य में वर्णित सप्त सिंधु, पंजाब में \"सात नदियों की भूमि\" को संदर्भित करता है, जो प्रारंभिक आर्य बस्तियों का निवास स्थान है। सात नदियों में सिंधु, सतलुज (सुतुद्री), रावी (परुषणी), चिनाब (अस्किनी), झेलम (वितस्ता), ब्यास (विपाशा) और सरस्वती शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>