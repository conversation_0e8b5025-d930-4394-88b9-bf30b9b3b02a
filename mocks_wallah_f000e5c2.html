<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 26</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">26</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 25,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "1. Select the option that is related to the third term in the same way as the second term is related to the first term.<br />Book  : Write  : : Cricket : ?",
                    question_hi: "1. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br />पुस्तक :  लिखना : :  क्रिकेट : ?",
                    options_en: [" Enjoy ", " Score ", 
                                " Match ", " Play "],
                    options_hi: [" आनन्द ", " स्कोर",
                                " मैच ", " खेलना"],
                    solution_en: "1.(d)<br />Book is written by someone. Similarly, Cricket is played.",
                    solution_hi: "1.(d)<br />किताब किसी ने लिखी है। इसी तरह क्रिकेट खेला जाता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the option that CANNOT be concluded from the given statements.<br>Statement: Some keys are boats. <br>Statement: Some boats are pipes. <br>Statement: All pipes are fish.</p>",
                    question_hi: "<p>2. उस विकल्प का चयन कीजिये जो दिए गए कथनों से निष्कर्ष नहीं निकाला जा सकता है।<br>कथन 1: कुछ चाबियां नाव हैं<br>कथन 2: कुछ नौकाएं पाइप हैं <br>कथन 3: सभी पाइप मछली हैं</p>",
                    options_en: ["<p>Some fish are keys</p>", "<p>Some pipes are boats.</p>", 
                                "<p>Some fish are boats.</p>", "<p>Some boats are keys.</p>"],
                    options_hi: ["<p>कुछ मछली चाबियां हैं</p>", "<p>कुछ पाइप नाव हैं।</p>",
                                "<p>कुछ मछली नाव हैं।</p>", "<p>कुछ नावें चाबियां हैं ।</p>"],
                    solution_en: "<p>2.(a)</p>\n<p><strong id=\"docs-internal-guid-ea80c0b7-7fff-2ad5-ba3d-2ff0cbea4f17\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe5nhqkI84yvs1zzQ0XT4bkbfY5eCh7-ewBORKtid2tHI4LfWN86sx6GGYa66xe4_Jl-C1zv82z2p_3772W0Awqx4e8KoVYOHc-gy09fKiSooeN-Pre5O_rGUIBBOBsJ_871346DX-D5BOkJFwI1h_2g98?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"214\" height=\"56\"></strong><br>From the above diagram it is clear that No fish are keys.</p>",
                    solution_hi: "<p>2.(a)<br><strong id=\"docs-internal-guid-b0f2dc5a-7fff-5413-6036-235f48c9834d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcEOrWDIK9kJ3cnWAP1abWwN0ko1oX0jTdOvvIPylR0UsW_4KtDSc3k2l5D9YOGNSRqlzzSFIYdPdgjEz8mZmkXVvcV_N-6-MfsdjgCaQRS13KE7S2Lp50UKpgL5_SujOBbktc3n4LAniH2CgW4?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"225\" height=\"58\"></strong><br>ऊपर दिए गए आरेख से यह स्पष्ट है कि कोई मछली चाबियां नहीं हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "3. Out of the four words listed three are alike in some manner and one is different. Select  the odd one.",
                    question_hi: "3. सूचीबद्ध चार शब्दों में से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। विजातीय का चयन करें।",
                    options_en: [" Umpire ", " Pitch ", 
                                " Ground ", " Boundary "],
                    options_hi: [" अंपायर", " पिच ",
                                " ग्राउंड ", " बाउंड्री "],
                    solution_en: "3.(a)<br />All are related to ground but Umpire is not.",
                    solution_hi: "3.(a)<br />सभी ग्राउंड से जुड़े हुए हैं लेकिन अंपायर नहीं हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. The following figure is a combination of 4 figures, A, B, C and D. Figure A and B represent students who are enrolled in science club and debate club respectively. Figure C and D represent students who have joined drama club and sports club respectively. The numbers inside the figures represent the number of students in that particular region. How many students have joined more than two clubs?<br><strong id=\"docs-internal-guid-4db7583b-7fff-9f4d-2b4c-25f3dfacbcd7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeROpB7_v6LrbkC1IH0fyLdl4RnflPr1W68OcLDMo0Gf3y368mmhgEGFGnFu-_sAAPSOMeMN61gSu670n4z5Xn_XcktVDxmc245X9snGufmnd_79NeE8xmcR1T78-LP4fplU3QP0jEiESJLutrM5CyYD6TN?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"154\" height=\"134\"></strong></p>",
                    question_hi: "<p>4. निम्नलिखित आकृति 4 अंकों का एक संयोजन है, A, B, C और D । आकृति A और B क्रमशः विज्ञान क्लब और वाद-विवाद क्लब में नामांकित छात्रों का प्रतिनिधित्व करते हैं। आकृति C और D उन छात्रों का प्रतिनिधित्व करते हैं जो क्रमशः ड्रामा क्लब और खेल क्लब में शामिल हुए हैं। आकृति के अंदर की संख्या उस विशेष क्षेत्र में छात्रों की संख्या का प्रतिनिधित्व करती है। कितने छात्र दो से अधिक क्लबों में शामिल हुए हैं?<br><strong id=\"docs-internal-guid-4db7583b-7fff-9f4d-2b4c-25f3dfacbcd7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeROpB7_v6LrbkC1IH0fyLdl4RnflPr1W68OcLDMo0Gf3y368mmhgEGFGnFu-_sAAPSOMeMN61gSu670n4z5Xn_XcktVDxmc245X9snGufmnd_79NeE8xmcR1T78-LP4fplU3QP0jEiESJLutrM5CyYD6TN?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"154\" height=\"134\"></strong></p>",
                    options_en: ["<p>2</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>2</p>", "<p>6</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>4.(d)<br>Students who joined more than two clubs are represented by the common region among at least 3 figures.<br>2 + 1 = 3</p>",
                    solution_hi: "<p>4.(d)<br>दो से अधिक क्लबों में शामिल होने वाले छात्रों को कम से कम 3 आंकड़ों के बीच सामान्य क्षेत्र द्वारा दर्शाया जाता है।<br>2 + 1 = 3</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. Out of the four letter pairs listed, three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "5. सूचीबद्ध चार अक्षर युग्मों में से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। विजातीय का चयन करें।",
                    options_en: [" Tg", " Jh", 
                                " Pk", " Yb"],
                    options_hi: [" Tg", " Jh",
                                " Pk", " Yb"],
                    solution_en: "5.(b)<br />Except o7tion (b), in all other options the 2nd letter is opposite of the 1st.",
                    solution_hi: "5.(b)<br />विकल्प (b) को छोड़कर, अन्य सभी विकल्पों में दूसरा अक्षर पहले के विपरीत है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Study the given pattern carefully and select the letter that can replace the question mark (?) in it.<br><strong id=\"docs-internal-guid-07110280-7fff-15f6-e68c-86f0f527d325\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc367uKr_-5Y-OYdBsbyPINvJ16eBr3KjH7LK6GbTBAcDhgeRENm3scGs00tb3_CBhkpCXLvYhgC39vqPZcdFZSVAbiIN9jH8WidKKAw1cdw0N3G1AU8_o7PMlDFHes9nYuZpy6hOVVpdZgVCq_8xhcMG1l?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"101\" height=\"99\"></strong></p>",
                    question_hi: "<p>6. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और उस अक्षर का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।<br><strong id=\"docs-internal-guid-07110280-7fff-15f6-e68c-86f0f527d325\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc367uKr_-5Y-OYdBsbyPINvJ16eBr3KjH7LK6GbTBAcDhgeRENm3scGs00tb3_CBhkpCXLvYhgC39vqPZcdFZSVAbiIN9jH8WidKKAw1cdw0N3G1AU8_o7PMlDFHes9nYuZpy6hOVVpdZgVCq_8xhcMG1l?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"101\" height=\"99\"></strong></p>",
                    options_en: ["<p>A</p>", "<p>B</p>", 
                                "<p>H</p>", "<p>F</p>"],
                    options_hi: ["<p>A</p>", "<p>B</p>",
                                "<p>H</p>", "<p>F</p>"],
                    solution_en: "<p>6.(a)<br>J = 10<br>F + D = 6 + 4 = 10<br>D + A + E = 4 + 1 + 5 = 10<br>A + E + C + A = 10</p>",
                    solution_hi: "<p>6.(a)<br>J = 10<br>F + D = 6 + 4 = 10<br>D + A + E = 4 + 1 + 5 = 10<br>A + E + C + A = 10</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a code language , PLANT is written as RNCPV, and HERB is written as JGTD. How will Flower be written in that code language?</p>",
                    question_hi: "<p>7. एक कूट भाषा में PLANT को RNCPV और HERB को JGTD लिखा जाता है। उसी कोड भाषा में फूल को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>HNQYGT</p>", "<p>EKNVGT</p>", 
                                "<p>DJMUGT</p>", "<p>GMPZGT</p>"],
                    options_hi: ["<p>HNQYGT</p>", "<p>EKNVGT</p>",
                                "<p>DJMUGT</p>", "<p>GMPZGT</p>"],
                    solution_en: "<p>7.(a)<br><strong id=\"docs-internal-guid-656b6393-7fff-b753-2f9b-c9a6797c988f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf3xjrA9KeHgRtqoz0CedMVZG30Rzl1UDe8s-tkR4XmNI9WzeOngzIShacdpUVt47Vucvol4ilN2ncqrLRSBIx0N6XhMnljEnb3yoGO5ivrCvk85GlP084_AFkk-U9EvzAZe3qsVK5tNHQZ5aSm_5soX5BX?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"129\" height=\"76\"></strong><br><strong id=\"docs-internal-guid-a3959c88-7fff-bec5-eb66-581ff4310b35\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdjlaFIgsSHSP36unyPjQ125Qf2f09z1zswTgx-7VmSePVropIZ16GpsJ2rdDR3tUQwfpXQkhpTazI6p9JY4ARSzKO4jpJVu1Ziin5gXn3RHTs9kJxSiTV2DP6kAEnkRLbCm35NhYbJfr5exTR_XzohKgyQ?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"107\" height=\"79\"></strong><br>Similarly,<br><strong id=\"docs-internal-guid-6ec9fe5e-7fff-e11e-df07-342c79ba061f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeiq2xphZexyib8mijUPHHhSw6R2Uqou-lY3JNKg0LAZ1S9qeKGbrH5MMdrNMfDatqb6D4xhSNVFOf-qo6OZuHvARw66PfCfWf-6jSVYM64XI9rXKtPHNBXyCsW-n2hG3RL2Py9M17Vwmdh-m6Skg--lO8O?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"153\" height=\"78\"></strong></p>",
                    solution_hi: "<p>7.(a)<br><strong id=\"docs-internal-guid-ee4586d9-7fff-01bd-68a2-7a8060887a7b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf3xjrA9KeHgRtqoz0CedMVZG30Rzl1UDe8s-tkR4XmNI9WzeOngzIShacdpUVt47Vucvol4ilN2ncqrLRSBIx0N6XhMnljEnb3yoGO5ivrCvk85GlP084_AFkk-U9EvzAZe3qsVK5tNHQZ5aSm_5soX5BX?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"129\" height=\"76\"></strong><br><strong id=\"docs-internal-guid-b7057dbc-7fff-fa9d-ce0b-89ff20ee39e3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdjlaFIgsSHSP36unyPjQ125Qf2f09z1zswTgx-7VmSePVropIZ16GpsJ2rdDR3tUQwfpXQkhpTazI6p9JY4ARSzKO4jpJVu1Ziin5gXn3RHTs9kJxSiTV2DP6kAEnkRLbCm35NhYbJfr5exTR_XzohKgyQ?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"107\" height=\"79\"></strong><br>उसी प्रकार&nbsp;,<br><strong id=\"docs-internal-guid-6ec9fe5e-7fff-e11e-df07-342c79ba061f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeiq2xphZexyib8mijUPHHhSw6R2Uqou-lY3JNKg0LAZ1S9qeKGbrH5MMdrNMfDatqb6D4xhSNVFOf-qo6OZuHvARw66PfCfWf-6jSVYM64XI9rXKtPHNBXyCsW-n2hG3RL2Py9M17Vwmdh-m6Skg--lO8O?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"153\" height=\"78\"></strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. The following paper cutting is folded along the lines to make a cubical box. Select the correct image of the resulting box from among the options.<br><strong id=\"docs-internal-guid-1ed86188-7fff-c198-b016-dce299d78cbb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXczadCn0FdKvHQkJt_ynPbozXtOQvudzmYHb1wtZfn0DyIVK6MIgIX1hZtSpkYasPF3YXr2L1sQtvkLYjziHnDEAo0qf5mT4gPhJFxYvbyPmGTJe4yReYXZDGaA7RLDXqRxjv-hW9R8hqM9VvQR3RyK20M1?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"191\" height=\"77\"></strong></p>",
                    question_hi: "<p>8. निम्नलिखित पेपर कटिंग को एक घनीय बॉक्स बनाने के लिए लाइनों के साथ मोड़ा जाता है। विकल्पों में से परिणामी बॉक्स की सही छवि का चयन करें।<br><strong id=\"docs-internal-guid-c0b06006-7fff-3179-7482-6886aeefd380\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXczadCn0FdKvHQkJt_ynPbozXtOQvudzmYHb1wtZfn0DyIVK6MIgIX1hZtSpkYasPF3YXr2L1sQtvkLYjziHnDEAo0qf5mT4gPhJFxYvbyPmGTJe4yReYXZDGaA7RLDXqRxjv-hW9R8hqM9VvQR3RyK20M1?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"191\" height=\"77\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-92eb4bc4-7fff-d909-8afa-12df34eaf32f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeFsOTLDwrPmwuwt51tSVA01Idi4jDaqnrog3IVAzMIRyT-WoXrUqMVpJs1gt3MrawYJY_ODXkXttr9tdLv7BXfrLkGWM61Zh-wW65CLa929RfciqrY-U1jIt4XbkZbofZ6powgzCXU-rxY_RIYGMphAelW?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"60\" height=\"63\"></strong></p>", "<p><strong id=\"docs-internal-guid-b48581ee-7fff-f6c8-c4f0-d1a1290e315e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdGuxP0wFMh7SjewDRmf5-C8OzIVL0l2usx7XnD3RBHGXPuPsnVQ5-O3sEu92ivduNlOgSff2ofwAG22VXHfimzH1lsqTiGlwSCfiT8fSBgup7hCDwDUAUgARvBY5wWNaNS6PWnzbdJk_gV7YQChnEsfB2a?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"69\" height=\"70\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-9efb97ff-7fff-0b90-2582-b2e7ca678a8a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXes32e94FqSkjK3XoXb6ezsmQZTfskezUjNyNaAH8JhxOUU3Jw0sNkEXKmZd415wYruV66V-J4qC8pC_2W-bDCW8LwtImAPjpQVOCbpeO0S6U-F1bSV03HlNrol61IDS1WeFeM9_LISUosMamM11oeWV2fe?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"71\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-c8a1457f-7fff-1f02-a876-055e0f2f1fc3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeKHUlRgCHJ-CK2u9_XaQdksTAAz8SoBZpUNv_7ecUNuyeV1JPKHuHBWN8qGrRXFpwE-R0Ek6t3DNbs0WILfgnUKG5WjdyctLpQzcOo6Xs8srdRCx0xxvOmzgjfNefc4K-Zd_bG4HHjtV8ksPTzZjTawu0?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"70\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-7ea5013f-7fff-48f1-ac89-8a2a2d349758\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeFsOTLDwrPmwuwt51tSVA01Idi4jDaqnrog3IVAzMIRyT-WoXrUqMVpJs1gt3MrawYJY_ODXkXttr9tdLv7BXfrLkGWM61Zh-wW65CLa929RfciqrY-U1jIt4XbkZbofZ6powgzCXU-rxY_RIYGMphAelW?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"60\" height=\"63\"></strong></p>", "<p><strong id=\"docs-internal-guid-191fceaf-7fff-615f-580e-419122e63a5e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdGuxP0wFMh7SjewDRmf5-C8OzIVL0l2usx7XnD3RBHGXPuPsnVQ5-O3sEu92ivduNlOgSff2ofwAG22VXHfimzH1lsqTiGlwSCfiT8fSBgup7hCDwDUAUgARvBY5wWNaNS6PWnzbdJk_gV7YQChnEsfB2a?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"69\" height=\"70\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-3feb19ce-7fff-771b-bb58-89a8d00263da\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXes32e94FqSkjK3XoXb6ezsmQZTfskezUjNyNaAH8JhxOUU3Jw0sNkEXKmZd415wYruV66V-J4qC8pC_2W-bDCW8LwtImAPjpQVOCbpeO0S6U-F1bSV03HlNrol61IDS1WeFeM9_LISUosMamM11oeWV2fe?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"71\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-7060c1ce-7fff-8b18-bb2c-0aa40c19cdfb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeKHUlRgCHJ-CK2u9_XaQdksTAAz8SoBZpUNv_7ecUNuyeV1JPKHuHBWN8qGrRXFpwE-R0Ek6t3DNbs0WILfgnUKG5WjdyctLpQzcOo6Xs8srdRCx0xxvOmzgjfNefc4K-Zd_bG4HHjtV8ksPTzZjTawu0?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"70\" height=\"72\"></strong></p>"],
                    solution_en: "<p>8.(c)<br>Opposite of <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927170456.png\" alt=\"rId16\"><br>Opposite of <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927170555.png\" alt=\"rId17\"><br>Opposite of <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927170669.png\" alt=\"rId18\"><br>Resulting Box = <strong id=\"docs-internal-guid-12f7fcaf-7fff-ec0a-9bcb-152709f394a9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf6L7Rsf-TDVJuAfJkP0G48kjZ55eHXMFXx1d_aoO0jvSdWSs6dSm0SWAhAEW0FoCFU-Vo86w8NyypiDe3ObK0mJh7LHLaP7NFbCZNCLGhPHF3J0WUbGNlkK4iekDatA1FNT3hL42whY3aLeqgUpyNu8vf_?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"55\" height=\"58\"></strong></p>",
                    solution_hi: "<p>8.(c)<br>से उल्टा <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927170456.png\" alt=\"rId16\"><br>से उल्टा <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927170555.png\" alt=\"rId17\"><br>से उल्टा <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729927170669.png\" alt=\"rId18\"><br>परिणामी बॉक्स = <strong id=\"docs-internal-guid-12f7fcaf-7fff-ec0a-9bcb-152709f394a9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf6L7Rsf-TDVJuAfJkP0G48kjZ55eHXMFXx1d_aoO0jvSdWSs6dSm0SWAhAEW0FoCFU-Vo86w8NyypiDe3ObK0mJh7LHLaP7NFbCZNCLGhPHF3J0WUbGNlkK4iekDatA1FNT3hL42whY3aLeqgUpyNu8vf_?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"55\" height=\"58\"></strong></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Read the given statement and courses of action carefully and decide which of the courses of action logically follow(s) from the statement.<br><strong>Statement:</strong><br>If Ritu studies effectively for 6 h daily from today, she will be able to pass the exam which is due after 10 days.<br><strong>Courses of Action:</strong><br>(i) Ritu must effectively study science for 6 h daily for 10 days to be able to prepare enough to pass her exam. <br>(ii) Ritu must plan her time ahead of preparation and give sufficient time to every subject. <br>(iii) Ritu must not play during these days which are crucial for her for preparation for exams.</p>",
                    question_hi: "<p>9. दिए गए कथन और कार्रवाई के पाठ्यक्रम को ध्यान से पढ़ें और बताइये कि कौन सी कार्रवाई तार्किक रूप से कथन का अनुसरण करती है।<br><strong>कथन:</strong><br>यदि रितु आज से प्रतिदिन 6 घंटे प्रभावी ढंग से अध्ययन करती है, तो वह 10 दिनों के बाद होने वाली परीक्षा में उत्तीर्ण हो सकेगी।<br><strong>क्रिया:</strong><br>(i) रितु को अपनी परीक्षा पास करने के लिए पर्याप्त तैयारी करने में सक्षम होने के लिए 10 दिनों तक रोजाना 6 घंटे प्रभावी ढंग से विज्ञान का अध्ययन करना चाहिए।<br>(ii) रितु को तैयारी से पहले अपने समय की योजना बनानी चाहिए और हर विषय को पर्याप्त समय देना चाहिए।<br>(iii) रितु को इन दिनों के दौरान नहीं खेलना चाहिए जो उसके लिए परीक्षा की तैयारी के लिए महत्वपूर्ण हैं।</p>",
                    options_en: ["<p>Only action (i) and (ii) follow.</p>", "<p>Only action (ii) follows.</p>", 
                                "<p>Only action (ii) and (iii) follow.</p>", "<p>All the actions (i), (ii) and (iii) follow.</p>"],
                    options_hi: ["<p>केवल क्रिया (i) और (ii) अनुसरण करती है।</p>", "<p>केवल क्रिया (ii) अनुसरण करती है।</p>",
                                "<p>केवल क्रिया (ii) और (iii) अनुसरण करती है।</p>", "<p>सभी क्रियाएं (i), (ii) और (iii) अनुसरण करती हैं।</p>"],
                    solution_en: "<p>9.(b)<br>From the given statement it is clear that Ritu must plan her time ahead of preparation and give sufficient time to every subject.</p>",
                    solution_hi: "<p>9.(b)<br>दिए गए कथन से यह स्पष्ट है कि रितु को तैयारी से पहले अपने समय की योजना बनानी चाहिए और प्रत्येक विषय को पर्याप्त समय देना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Which of the following options is the fifth to the right of the 20th letter from the left in English alphabet?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन सा विकल्प अंग्रेजी वर्णमाला में बाएं से 20वें अक्षर के दायें से पांचवां है?</p>",
                    options_en: ["<p>V</p>", "<p>Y</p>", 
                                "<p>Z</p>", "<p>E</p>"],
                    options_hi: ["<p>V</p>", "<p>Y</p>",
                                "<p>Z</p>", "<p>E</p>"],
                    solution_en: "<p>10.(b)<br>20 + 5 = 25 = Y</p>",
                    solution_hi: "<p>10.(b)<br>20 + 5 = 25 = Y</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Study the given pattern carefully and select the number that can replace the question mark(?) in it.<br><strong id=\"docs-internal-guid-4836a52f-7fff-aa8b-8ede-04903c94e9a8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdxcw0xUkKr0cIUXuzchzmFefUPniVMP7SJ3-FcbCc_V1L9wMNarCCxHVpwXx8pDfnyUKfkpeeYB3rZjxyhKZNKITE46qzMHOrT2DTh1-ctwQkTbn7NFYO-JX___ekRfnNLetZ4pJ2zIohI6LDjP70wGphg?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"280\" height=\"71\"></strong></p>",
                    question_hi: "<p>11. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन करें और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।<br><strong id=\"docs-internal-guid-4836a52f-7fff-aa8b-8ede-04903c94e9a8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdxcw0xUkKr0cIUXuzchzmFefUPniVMP7SJ3-FcbCc_V1L9wMNarCCxHVpwXx8pDfnyUKfkpeeYB3rZjxyhKZNKITE46qzMHOrT2DTh1-ctwQkTbn7NFYO-JX___ekRfnNLetZ4pJ2zIohI6LDjP70wGphg?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"280\" height=\"71\"></strong></p>",
                    options_en: ["<p>8</p>", "<p>5</p>", 
                                "<p>4</p>", "<p>9</p>"],
                    options_hi: ["<p>8</p>", "<p>5</p>",
                                "<p>4</p>", "<p>9</p>"],
                    solution_en: "<p>11.(a)<br>6 <math display=\"inline\"><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>12</mn><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mn>18</mn></math><br>6 <math display=\"inline\"><mo>&#215;</mo><mn>8</mn><mo>=</mo><mn>20</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>28</mn></math><br>7 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>8</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn></math></p>",
                    solution_hi: "<p>11.(a)<br>6 <math display=\"inline\"><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>12</mn><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mn>18</mn></math><br>6 <math display=\"inline\"><mo>&#215;</mo><mn>8</mn><mo>=</mo><mn>20</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>28</mn></math><br>7 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>8</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the number that will come next in the following series.<br>2, 6, 14, 26, 42, ?</p>",
                    question_hi: "<p>12. निम्नलिखित श्रृंखला में आगे आने वाली संख्या का चयन करें।<br>2, 6, 14, 26, 42, ?</p>",
                    options_en: ["<p>64</p>", "<p>54</p>", 
                                "<p>52</p>", "<p>62</p>"],
                    options_hi: ["<p>64</p>", "<p>54</p>",
                                "<p>52</p>", "<p>62</p>"],
                    solution_en: "<p>12.(d)<br>2 + 4 = 6<br>6 + 8 = 14<br>14 + 12 = 26<br>26 + 16 = 42<br>42 + 20 = 62</p>",
                    solution_hi: "<p>12.(d)<br>2 + 4 = 6<br>6 + 8 = 14<br>14 + 12 = 26<br>26 + 16 = 42<br>42 + 20 = 62</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. Select the option that is related to the third term in the same way as the second term is related to the first term. <br />Predict  : Future  : : Recall  : ?",
                    question_hi: "13. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br /> भविष्यवाणी करना :  भविष्य : :  याद करना : ?",
                    options_en: [" Memory", " Dream", 
                                " Forget", " Past"],
                    options_hi: [" स्मृति", " सपना",
                                " भूलना", " भूतकाल"],
                    solution_en: "13.(d)<br />We can predict our future in the same way We can recall our past.",
                    solution_hi: "13.(d)<br />हम अपने भविष्य की भविष्यवाणी उसी तरह कर सकते हैं जैसे हम अपने अतीत को याद कर सकते हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Read the given statement and conclusions carefully and decide which of the conclusions logically follow(s) from the statement.<br><strong>Statement</strong> :<br>Increasing use of non-biodegradable plastic will pose a grave threat to plant and animal life in the long run.<br><strong>Conclusions</strong> :<br>1. If non-biodegradable plastic is banned, humanity will not face extinction. <br>2. Humanity will not be impacted by extensive use of plastic.</p>",
                    question_hi: "<p>14. दिए गए कथन और निष्कर्षों को ध्यान से पढ़ें और बताइये कि कौन सा निष्कर्ष कथन का तार्किक रूप से अनुसरण करता है।<br><strong>कथन</strong> :<br>गैर-जैव निम्नीकरणीय प्लास्टिक के बढ़ते उपयोग से लंबे समय में पौधे और पशु जीवन के लिए गंभीर खतरा पैदा हो जाएगा ।<br><strong>निष्कर्ष:</strong><br>1. यदि गैर-जैव निम्नीकरणीय प्लास्टिक पर प्रतिबंध लगा दिया जाता है, तो मानवता विलुप्त होने का सामना नहीं करेगी<br>2. प्लास्टिक के व्यापक उपयोग से मानवता प्रभावित नहीं होगी</p>",
                    options_en: ["<p>2 follows but 1 does not follow.</p>", "<p>1 and 2 follow.</p>", 
                                "<p>Neither 1 nor 2 follows.</p>", "<p>1 follows but 2 does not follow.</p>"],
                    options_hi: ["<p>2 अनुसरण करता है लेकिन 1 अनुसरण नहीं करता है।</p>", "<p>1 और 2 अनुसरण करता है।</p>",
                                "<p>न तो 1 और न ही 2 अनुसरण करता है।</p>", "<p>1 अनुसरण करता है लेकिन 2 अनुसरण नहीं करता</p>"],
                    solution_en: "<p>14.(c)<br>From the given statement it is clear that Neither 1 nor 2 follows.</p>",
                    solution_hi: "<p>14.(c)<br>दिए गए कथन से यह स्पष्ट है कि न तो 1 और न ही 2 अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Study the given pattern carefully and select the number that can replace the question mark (?) in it.<br><strong id=\"docs-internal-guid-018f857f-7fff-fcfe-3e92-9593edce1317\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeT59ijpI7B2a_bh7g4qCqBGCOXxfDxR92QrCfDPinrkfu3yDLZ5ut_0HpbN8icOVAUebLs4LOKglCcKdt4rshIrAe_VDgzI-fBTAazvKnHzel9aZ0QRUWTR22NRndh0wsJEsUAYnHaMieNeYb1nNN8A5fL?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"228\" height=\"61\"></strong></p>",
                    question_hi: "<p>15. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके<br><strong id=\"docs-internal-guid-018f857f-7fff-fcfe-3e92-9593edce1317\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeT59ijpI7B2a_bh7g4qCqBGCOXxfDxR92QrCfDPinrkfu3yDLZ5ut_0HpbN8icOVAUebLs4LOKglCcKdt4rshIrAe_VDgzI-fBTAazvKnHzel9aZ0QRUWTR22NRndh0wsJEsUAYnHaMieNeYb1nNN8A5fL?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"228\" height=\"61\"></strong></p>",
                    options_en: ["<p>23</p>", "<p>25</p>", 
                                "<p>6</p>", "<p>5</p>"],
                    options_hi: ["<p>23</p>", "<p>25</p>",
                                "<p>6</p>", "<p>5</p>"],
                    solution_en: "<p>15.(b)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></msup><mo>=</mo><mn>64</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>49</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>25</mn></math></p>",
                    solution_hi: "<p>15.(b)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></msup><mo>=</mo><mn>64</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>49</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mn>25</mn></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the Venn diagram that best represents the relationship between the given set of classes.<br>Females, Wife, Mothers</p>",
                    question_hi: "<p>16. वेन आरेख का चयन करें जो दिए गए वर्गों के बीच संबंध का सबसे अच्छा प्रतिनिधित्व करता है।<br>महिला, पत्नी, माता</p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-87338250-7fff-c0b6-e39b-28db0b5a8d03\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddWPcjN_JFpsQQ_FBmezAScMyd8PB0TL_OyU5kVKZ3qRRLvE_TRevdqdZgQYb5Ce-6nzIrHJNo-6s9H9WiknBoFFapkCGcj14rji3CBsqOitqVqit_DGcmiL7oj9n55P_yIAGPZgke1n8gvVRBINXIF6bJ?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"65\" height=\"63\"></strong></p>", "<p><strong id=\"docs-internal-guid-48d53e5b-7fff-d8a8-9f9c-d85b3e6f25fe\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFuOCGzT14aaUpAcz28whO5g6tSSe7fhl_SrJFMtAquDXJK6CU670zM38SAUFqMPT20ZGMKCYt-iRVc8nNOy5BVkaTSszQyEI5jjHcbyOAn8X_akx-yV2WU6OaRE1Bri3HwtekEr5rz6UAHNM_hVYVHXmv?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"118\" height=\"51\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-408ea7f7-7fff-eda2-6f35-5ccb2b4a10a3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeXdao7V6dN8FZoI8v2hqD2JwCDno7hOkS1seLv-e3r1N-9a-rR_7GTmIm8NDtLYOTasPJ8_eagt--BfecigKBKy8hj6HVIvzNycQ6W-4rcM1eh8_8ATmovVbl_NNpaVFMdKInvPL7v0EqidDJiRRiP_fE?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"87\" height=\"57\"></strong></p>", "<p><strong id=\"docs-internal-guid-67904aa6-7fff-042f-ed36-a3a3fc4dd3ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_h8QqlF6hsu-Q2VyQ7Mb0nGUf6KNRfw4wU95_T34uzj5809LJKCzXkLg1jirZQ2B3WyUwH_dx55ZAS73clj4vqzpI9tK58zrb16Wj29jZ32hRj2XhYMh7vwMWOYjPNa8HJYcv98swHUwHyuyzHCHT7w0?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"73\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-78ca4406-7fff-2fdc-3ecc-535a12d94165\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddWPcjN_JFpsQQ_FBmezAScMyd8PB0TL_OyU5kVKZ3qRRLvE_TRevdqdZgQYb5Ce-6nzIrHJNo-6s9H9WiknBoFFapkCGcj14rji3CBsqOitqVqit_DGcmiL7oj9n55P_yIAGPZgke1n8gvVRBINXIF6bJ?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"65\" height=\"63\"></strong></p>", "<p><strong id=\"docs-internal-guid-4151575e-7fff-4460-4c75-666c8f165448\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFuOCGzT14aaUpAcz28whO5g6tSSe7fhl_SrJFMtAquDXJK6CU670zM38SAUFqMPT20ZGMKCYt-iRVc8nNOy5BVkaTSszQyEI5jjHcbyOAn8X_akx-yV2WU6OaRE1Bri3HwtekEr5rz6UAHNM_hVYVHXmv?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"118\" height=\"51\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-2a5b5a3f-7fff-5a87-c702-6127f11cdf3b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeXdao7V6dN8FZoI8v2hqD2JwCDno7hOkS1seLv-e3r1N-9a-rR_7GTmIm8NDtLYOTasPJ8_eagt--BfecigKBKy8hj6HVIvzNycQ6W-4rcM1eh8_8ATmovVbl_NNpaVFMdKInvPL7v0EqidDJiRRiP_fE?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"87\" height=\"57\"></strong></p>", "<p><strong id=\"docs-internal-guid-b538580e-7fff-7049-7c41-2fc042ad442a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_h8QqlF6hsu-Q2VyQ7Mb0nGUf6KNRfw4wU95_T34uzj5809LJKCzXkLg1jirZQ2B3WyUwH_dx55ZAS73clj4vqzpI9tK58zrb16Wj29jZ32hRj2XhYMh7vwMWOYjPNa8HJYcv98swHUwHyuyzHCHT7w0?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"73\" height=\"72\"></strong></p>"],
                    solution_en: "<p>16.(a)<br><strong id=\"docs-internal-guid-54ddb547-7fff-8743-7d95-d45dc6b69ab8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddWPcjN_JFpsQQ_FBmezAScMyd8PB0TL_OyU5kVKZ3qRRLvE_TRevdqdZgQYb5Ce-6nzIrHJNo-6s9H9WiknBoFFapkCGcj14rji3CBsqOitqVqit_DGcmiL7oj9n55P_yIAGPZgke1n8gvVRBINXIF6bJ?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"65\" height=\"63\"></strong></p>",
                    solution_hi: "<p>16.(a)<br><strong id=\"docs-internal-guid-54ddb547-7fff-8743-7d95-d45dc6b69ab8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddWPcjN_JFpsQQ_FBmezAScMyd8PB0TL_OyU5kVKZ3qRRLvE_TRevdqdZgQYb5Ce-6nzIrHJNo-6s9H9WiknBoFFapkCGcj14rji3CBsqOitqVqit_DGcmiL7oj9n55P_yIAGPZgke1n8gvVRBINXIF6bJ?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"65\" height=\"63\"></strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "17. Select the number that will come next in the following series.<br />3, 7, 16, 35, ?",
                    question_hi: "17. निम्नलिखित श्रृंखला में आगे आने वाली संख्या का चयन कीजिये<br />3, 7, 16, 35, ?",
                    options_en: [" 73", " 57", 
                                " 64", " 74"],
                    options_hi: [" 73", " 57",
                                " 64", " 74"],
                    solution_en: "17.(d)<br />3 <math display=\"inline\"><mo>×</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>7</mn></math><br />7 <math display=\"inline\"><mo>×</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>=</mo><mn>16</mn></math><br />16 <math display=\"inline\"><mo>×</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>=</mo><mn>35</mn></math><br />35 <math display=\"inline\"><mo>×</mo><mn>2</mn><mo>+</mo><mn>4</mn><mo>=</mo><mn>74</mn></math>",
                    solution_hi: "17.(d)<br />3 <math display=\"inline\"><mo>×</mo><mn>2</mn><mo>+</mo><mn>1</mn><mo>=</mo><mn>7</mn></math><br />7 <math display=\"inline\"><mo>×</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>=</mo><mn>16</mn></math><br />16 <math display=\"inline\"><mo>×</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>=</mo><mn>35</mn></math><br />35 <math display=\"inline\"><mo>×</mo><mn>2</mn><mo>+</mo><mn>4</mn><mo>=</mo><mn>74</mn></math>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. I am the only daughter of X, and Y is my husband Z&rsquo;s sister&rsquo;s only daughter. If D is the maternal grandfather of Y, how am I related to D?</p>",
                    question_hi: "<p>18. मैं X की इकलौती बेटी हूं, और Y मेरे पति Z की बहन की इकलौती बेटी है। यदि D, Y का नाना है, तो मैं D से किस प्रकार संबंधित हूँ?</p>",
                    options_en: ["<p>Daughter- in - law</p>", "<p>Sister - in - law</p>", 
                                "<p>Paternal Aunt</p>", "<p>Husband&rsquo;s sister&rsquo;s daughter</p>"],
                    options_hi: ["<p>बहू</p>", "<p>भाभी</p>",
                                "<p>चाची</p>", "<p>पति की बहन की बेटी</p>"],
                    solution_en: "<p>18.(a)<br><strong id=\"docs-internal-guid-8773939b-7fff-159c-2df6-a97e42ed69e1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0FMho0vUdk-zIGnxn6vvUC5JL9OTjajaxEw_oxv6c2EOWHAY-BkVsQG7HGL23bYam0IkhAaRpdBf8n5WHpYiEfvYeYRlV3NVEh5fL_dAv-rg2gLpTXrDj6R37-15gp2hPh-uAuCaaoPlARKjCQITu_-k?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"114\" height=\"124\"></strong></p>",
                    solution_hi: "<p>18.(a)<br><strong id=\"docs-internal-guid-8773939b-7fff-159c-2df6-a97e42ed69e1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf0FMho0vUdk-zIGnxn6vvUC5JL9OTjajaxEw_oxv6c2EOWHAY-BkVsQG7HGL23bYam0IkhAaRpdBf8n5WHpYiEfvYeYRlV3NVEh5fL_dAv-rg2gLpTXrDj6R37-15gp2hPh-uAuCaaoPlARKjCQITu_-k?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"114\" height=\"124\"></strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In the following relationship chart, A &rarr; B means A is the mother (or any other relationship) of B. Now using the chart, select the option that can replace the question mark (?) in it, if all the mentioned individuals, 1, 2, 3 and 4 are males.<br><strong id=\"docs-internal-guid-4bb7c1cc-7fff-6971-f877-8128f5eacb16\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe_IdgSh7P89Dqmt2vVTVAA1325-T2vLaABXZiPdcs0EuoxiBrfkJFRh3x0ipQmNB-KaKYhNc6M-vTwjYosadRVIoqcROzWl-NH-zepQV0bPOgiOdystHC9RS1DxobFRTEOMkdl6bL4gX29BKRotWubozyx?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"105\" height=\"113\"></strong></p>",
                    question_hi: "<p>19. निम्नलिखित संबंध चार्ट में, A &rarr; B का अर्थ है A, B की मां (या कोई अन्य रिश्ता) है। अब चार्ट का उपयोग करके, उस विकल्प का चयन करें जो इसमें प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है, यदि सभी उल्लिखित व्यक्ति, 1 ,2, 3 और 4 पुरुष हैं।<br><strong id=\"docs-internal-guid-01a474ba-7fff-8482-aa26-17def71f381d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdTY4oHxGjiXcF75wc7zHG0wvUnVgeQ-2HltAGwcwbWiWSxHuKg5OsUAGO0ykwdLXMW31EIOSKpPyGT_DWiK2JikTJB6bI6kwZqNoBDSkPCN_I6U9owW9QABuV9ZBcj_01vqrrz8_IiVp9J4I2kkbhSZJ29?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"95\" height=\"99\"></strong></p>",
                    options_en: ["<p>Grandfather</p>", "<p>Son</p>", 
                                "<p>Uncle</p>", "<p>Father</p>"],
                    options_hi: ["<p>दादा</p>", "<p>बेटा</p>",
                                "<p>चाचा</p>", "<p>पिता</p>"],
                    solution_en: "<p>19.(d)<br><strong id=\"docs-internal-guid-f8393569-7fff-a5c8-7a45-051877de174d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdUIf_UkkewJ9krfCooQDDtyw8XXzIMs9XRHFMsu3DOiuFrp8WSE_BvFT4QNzhh8wrDzgZbJPUnsFVr6ZZYsMTxoPYR-cPF8IsYz55sudlZeYc1-O3zYNWGAwb0FBo5Wzlhre5Kz0WrG5kxAU9Fdw2Krdu1?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"88\" height=\"145\"></strong></p>",
                    solution_hi: "<p>19.(d)<br><strong id=\"docs-internal-guid-f8393569-7fff-a5c8-7a45-051877de174d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdUIf_UkkewJ9krfCooQDDtyw8XXzIMs9XRHFMsu3DOiuFrp8WSE_BvFT4QNzhh8wrDzgZbJPUnsFVr6ZZYsMTxoPYR-cPF8IsYz55sudlZeYc1-O3zYNWGAwb0FBo5Wzlhre5Kz0WrG5kxAU9Fdw2Krdu1?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"88\" height=\"145\"></strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Study the given pattern carefully and select the number that can replace the question mark (?) in it.<br><strong id=\"docs-internal-guid-589184ae-7fff-f10a-36c9-9c22700f7097\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeUIoDz48ows_zYix6tohwV6Tn3BpF45pVQcvuN6tQZNfsA5WPBXJijUd68cVGEOt7ztax74RLPAekzfVrDfPb-ElVnaj3uD6nN7MkQeQIyOrgSnNzzifOSZQ66NcU5sDvXnADNYAyEaeQK6N2yRkhNuzVD?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"203\" height=\"82\"></strong></p>",
                    question_hi: "<p>20. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।<br><strong id=\"docs-internal-guid-589184ae-7fff-f10a-36c9-9c22700f7097\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeUIoDz48ows_zYix6tohwV6Tn3BpF45pVQcvuN6tQZNfsA5WPBXJijUd68cVGEOt7ztax74RLPAekzfVrDfPb-ElVnaj3uD6nN7MkQeQIyOrgSnNzzifOSZQ66NcU5sDvXnADNYAyEaeQK6N2yRkhNuzVD?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"203\" height=\"82\"></strong></p>",
                    options_en: ["<p>200</p>", "<p>8</p>", 
                                "<p>315</p>", "<p>137</p>"],
                    options_hi: ["<p>200</p>", "<p>8</p>",
                                "<p>315</p>", "<p>137</p>"],
                    solution_en: "<p>20.(c)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>3</mn></msup><mo>+</mo><msup><mn>4</mn><mn>4</mn></msup><mo>+</mo><msup><mn>2</mn><mn>5</mn></msup><mo>=</mo><mn>27</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>256</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>32</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>315</mn></math></p>",
                    solution_hi: "<p>20.(c)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>3</mn></msup><mo>+</mo><msup><mn>4</mn><mn>4</mn></msup><mo>+</mo><msup><mn>2</mn><mn>5</mn></msup><mo>=</mo><mn>27</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>256</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>32</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>315</mn></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Study the given pattern carefully and select the number that can replace the question mark (?) in it.<br><strong id=\"docs-internal-guid-a919e00d-7fff-7392-db4e-5cf3b24f2af4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf4IYULjDNWyvbl0TTS8PXtSQdFRRDjCAzMy5x2WhH6YH6Qg0rnfYO73hAUwAj8an6_Ab_y6mRQz3HHIroDtoOf4BnK3X0Eiu-9yB4_GOHThaMOxew3z4RNS0bvozNg2KxapsTK3J-BQAyKi0AEnvJzcxQ?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"355\" height=\"100\"></strong></p>",
                    question_hi: "<p>21. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और उस संख्या का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके<br><strong id=\"docs-internal-guid-a919e00d-7fff-7392-db4e-5cf3b24f2af4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf4IYULjDNWyvbl0TTS8PXtSQdFRRDjCAzMy5x2WhH6YH6Qg0rnfYO73hAUwAj8an6_Ab_y6mRQz3HHIroDtoOf4BnK3X0Eiu-9yB4_GOHThaMOxew3z4RNS0bvozNg2KxapsTK3J-BQAyKi0AEnvJzcxQ?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"355\" height=\"100\"></strong></p>",
                    options_en: ["<p>15</p>", "<p>11</p>", 
                                "<p>12</p>", "<p>7</p>"],
                    options_hi: ["<p>15</p>", "<p>11</p>",
                                "<p>12</p>", "<p>7</p>"],
                    solution_en: "<p>21.(c)<br>3<math display=\"inline\"><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>90</mn></math> = 15 &times; 6<br>4<math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>1</mn><mo>&#215;</mo><mn>6</mn><mo>=</mo><mn>48</mn></math> = 12 &times; 4<br>3 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>4</mn><mo>=</mo><mn>48</mn><mo>=</mo><mn>4</mn><mo>&#215;</mo><mn>12</mn></math></p>",
                    solution_hi: "<p>21.(c)<br>3<math display=\"inline\"><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>90</mn></math> = 15 &times; 6<br>4<math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>1</mn><mo>&#215;</mo><mn>6</mn><mo>=</mo><mn>48</mn></math> = 12 &times; 4<br>3 <math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>4</mn><mo>=</mo><mn>48</mn><mo>=</mo><mn>4</mn><mo>&#215;</mo><mn>12</mn></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "22. 5 students, A, B, C, D and E are standing in a queue during the school assembly. C is standing behind D. E is not standing behind B. A is standing ahead of C but not ahead of B. If behind means ‘ being exactly next to someone’s back’ and ahead does not mean ‘exactly in front of’, who among the 5 students is standing at the third position?",
                    question_hi: "22. 5 विद्यार्थी, A, B, C, D और E स्कूल असेंबली के दौरान एक कतार में खड़े हैं। C, D के पीछे खड़ा है। E, B के पीछे नहीं खड़ा है। A, C के आगे खड़ा है, लेकिन B से आगे नहीं। यदि पीछे का अर्थ है \'किसी की पीठ के बिल्कुल बगल में होना\' और आगे का अर्थ \'बिल्कुल सामने\' नहीं है, तो इनमें से कौन 5 छात्र तीसरे स्थान पर खड़े हैं?",
                    options_en: [" A", " E", 
                                " B", " D"],
                    options_hi: [" A", " E",
                                " B", " D"],
                    solution_en: "22.(a)<br /> E - B - A - D - C",
                    solution_hi: "22.(a)<br /> E - B - A - D - C",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "23. If in a code language, LIVE is written as OREV, then how will PRAY be written in that code langage?",
                    question_hi: "23. यदि किसी कूट भाषा में LIVE को OREV लिखा जाता है, तो उसी कूट भाषा में PRAY को कैसे लिखा जाएगा?",
                    options_en: [" KIZB", " RSBZ", 
                                " LIZB", " QSBZ"],
                    options_hi: [" KIZB", " RSBZ",
                                " LIZB", " QSBZ"],
                    solution_en: "23.(a)<br />Logic:- opposite letters are written as code.<br />Code for PRAY  = KIZB",
                    solution_hi: "23.(a)<br />तर्क:- विपरीत अक्षरों को कूट के रूप में लिखा जाता है।<br />PRAY के लिए कोड = KIZB",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Study the given pattern carefully and select the letter that can replace the question mark (?) in it.<br><strong id=\"docs-internal-guid-e1336cf4-7fff-10b2-a29f-55b850db7ef2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXczT0xyhPFuFKR-Ti5z6LWsfPrp638dtRejGTOWDHOYNz1_XLb9i2TABhqVsIHfKpObEL8k9p2xIxrBQT70vSBiHpn_i2HHOf02WNB80qVWATbtDMFE2EbLnSeppTXKkQb-Ut-YUVOxsAjlxQEhFLNBfeqE?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"93\" height=\"90\"></strong></p>",
                    question_hi: "<p>24. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और उस अक्षर का चयन करें जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके।<br><strong id=\"docs-internal-guid-e1336cf4-7fff-10b2-a29f-55b850db7ef2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXczT0xyhPFuFKR-Ti5z6LWsfPrp638dtRejGTOWDHOYNz1_XLb9i2TABhqVsIHfKpObEL8k9p2xIxrBQT70vSBiHpn_i2HHOf02WNB80qVWATbtDMFE2EbLnSeppTXKkQb-Ut-YUVOxsAjlxQEhFLNBfeqE?key=wweQhhNv9PrDxwZtmWS8Fg\" width=\"93\" height=\"90\"></strong></p>",
                    options_en: ["<p>W</p>", "<p>U</p>", 
                                "<p>A</p>", "<p>X</p>"],
                    options_hi: ["<p>W</p>", "<p>U</p>",
                                "<p>A</p>", "<p>X</p>"],
                    solution_en: "<p>24.(a)<br>C + 2 = E<br>E + 3 = H<br>H + 4 = L<br>L + 5 = Q<br>Q + 6 = W</p>",
                    solution_hi: "<p>24.(a)<br>C + 2 = E<br>E + 3 = H<br>H + 4 = L<br>L + 5 = Q<br>Q + 6 = W</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "25. Select the option that is different from the rest.",
                    question_hi: "25. उस विकल्प का चयन कीजिये जो बाकी से अलग है।",
                    options_en: [" Eden Gardens, Kolkata ", " Lodhi Gardens, Delhi ", 
                                " Hanging Gardens, Mumbai ", " Brindavan Gardens, Mysore "],
                    options_hi: [" ईडन गार्डन्स, कोलकाता", " लोधी गार्डन, दिल्ली",
                                " हैंगिंग गार्डन, मुंबई", " बृंदावन गार्डन, मैसूर"],
                    solution_en: "25.(a) <br />Eden Garden, Kolkata is a Cricket stadium while the rest are gardens.",
                    solution_hi: "25.(a) <br />ईडन गार्डन, कोलकाता एक क्रिकेट स्टेडियम है जबकि बाकी बगीचे हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "26. If different combinations are formed using the letters B,A,R,E and arranged in the order in which they would appear in an English dictionary, the combination ‘BARE’ will be at the __________ position.",
                    question_hi: "26. यदि अलग-अलग संयोजन B,A,R,E अक्षरों का उपयोग करके बनाए जाते हैं और उस क्रम में व्यवस्थित किए जाते हैं जिस क्रम में वे अंग्रेजी शब्दकोश में दिखाई देंगे, तो संयोजन \'BARE\' __________ स्थिति में होगा।",
                    options_en: [" 8th", " 10th", 
                                " 7th", " 6th"],
                    options_hi: [" 8th", " 10th",
                                " 7th", " 6th"],
                    solution_en: "26.(a)<br />The word formed using A in 1st place = 1 <math display=\"inline\"><mo>×</mo><mn>3</mn><mo>!</mo><mi>&nbsp;</mi><mo>=</mo><mi>&nbsp;</mi><mn>1</mn><mi>&nbsp;</mi><mo>×</mo><mn>6</mn><mo>=</mo><mn>6</mn></math><br />The 1st word formed using B in 1st place = BAER<br />The 2nd word formed using B in 2nd place = BARE<br />So the position of BARE = 6 + 2 = 8th",
                    solution_hi: "26.(a)<br />पहले स्थान पर A का प्रयोग करके बनने वाला शब्द = 1<math display=\"inline\"><mo>×</mo></math> 3! = 1× 6=6<br />पहले स्थान पर B का प्रयोग करके बनने वाला पहला शब्द = BAER<br />दूसरे स्थान पर B का प्रयोग करके बनाया गया दूसरा शब्द = BARE<br />तो BARE की स्थिति = 6+ 2 = 8th",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>