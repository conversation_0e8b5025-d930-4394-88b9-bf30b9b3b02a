<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that represents the letters that when sequentially placed from left to right in the blanks below will complete the letter series.<br>_ _ C _ D C _ _ C C _ _</p>",
                    question_hi: "<p>1. उस विकल्प का चयन कीजिए जो उन अक्षरों को प्रदर्शित करता है जिन्हें नीचे दिए गए रिक्&zwj;त स्&zwj;थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी। <br>_ _ C _ D C _ _ C C _ _</p>",
                    options_en: [
                        "<p>CCDCCDD</p>",
                        "<p>CDCCDDC</p>",
                        "<p>DCCCDCC</p>",
                        "<p>DDCCCDD</p>"
                    ],
                    options_hi: [
                        "<p>CCDCCDD</p>",
                        "<p>CDCCDDC</p>",
                        "<p>DCCCDCC</p>",
                        "<p>DDCCCDD</p>"
                    ],
                    solution_en: "<p>1.(b)<br><span style=\"text-decoration: underline;\"><strong>C</strong> <strong>D</strong></span> C /<span style=\"text-decoration: underline;\"><strong>C</strong></span> D C/ <span style=\"text-decoration: underline;\"><strong>C</strong> <strong>D</strong></span> C/ C <span style=\"text-decoration: underline;\"><strong>D C</strong></span></p>",
                    solution_hi: "<p>1.(b)<br><span style=\"text-decoration: underline;\"><strong>C</strong> <strong>D</strong></span> C /<span style=\"text-decoration: underline;\"><strong>C</strong></span> D C/ <span style=\"text-decoration: underline;\"><strong>C</strong> <strong>D</strong></span> C/ C <span style=\"text-decoration: underline;\"><strong>D C</strong></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the figure that will replace the question mark (?) in the following figure series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500131.png\" alt=\"rId4\" width=\"380\" height=\"77\"></p>",
                    question_hi: "<p>2. उस आकृति का चयन कीजिए, जो नीचे दी गई आकृति श्रृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित करेगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500131.png\" alt=\"rId4\" width=\"380\" height=\"77\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500276.png\" alt=\"rId5\" width=\"59\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500375.png\" alt=\"rId6\" width=\"59\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500476.png\" alt=\"rId7\" width=\"59\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500587.png\" alt=\"rId8\" width=\"59\" height=\"60\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500276.png\" alt=\"rId5\" width=\"59\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500375.png\" alt=\"rId6\" width=\"59\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500476.png\" alt=\"rId7\" width=\"59\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500587.png\" alt=\"rId8\" width=\"59\" height=\"60\"></p>"
                    ],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500375.png\" alt=\"rId6\" width=\"69\" height=\"70\"></p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500375.png\" alt=\"rId6\" width=\"68\" height=\"70\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language,<br>\'A + B\' means \'A is the mother of B\';<br>\'A - B\' means \'A is the brother of B\';<br>\'A <math display=\"inline\"><mo>&#215;</mo></math> B\' means \'A is the wife of B\' and<br>\'A <math display=\"inline\"><mo>&#247;</mo></math> B\' means \'A is the father of B\'.<br>Based on the above, how is 4 related to 1 if \'4 <math display=\"inline\"><mo>&#215;</mo></math> 3 - 2 - 1\'?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में, <br>&lsquo;A + B&rsquo; का अर्थ है &lsquo;A, B की माता है&rsquo;; <br>&lsquo;A &minus; B&rsquo; का अर्थ है &lsquo;A, B का भाई है&rsquo;;<br>&lsquo;A &times; B&rsquo; का अर्थ है &lsquo;A, B की पत्नी है&rsquo; और <br>&lsquo;A &divide; B&rsquo; का अर्थ है &lsquo;A, B का पिता है&rsquo; <br>उपरोक्त के आधार पर, यदि &lsquo;4 &times; 3 &minus; 2 &ndash; 1&rsquo; है, तो 4 का 1 से क्या सम्बन्ध है?</p>",
                    options_en: [
                        "<p>Mother</p>",
                        "<p>Father\'s sister</p>",
                        "<p>Sister</p>",
                        "<p>Brother\'s wife</p>"
                    ],
                    options_hi: [
                        "<p>माता</p>",
                        "<p>पिता की बहन</p>",
                        "<p>बहन</p>",
                        "<p>भाई की पत्नी</p>"
                    ],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500686.png\" alt=\"rId9\" width=\"254\" height=\"45\"><br>Hence, 4 is the brother&rsquo;s wife of 1.</p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500686.png\" alt=\"rId9\" width=\"254\" height=\"45\"><br>अतः, 4, 1 के भाई की पत्नी है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given&nbsp;statement.<br><strong>Statements :</strong><br>All shoes are ties.<br>Some ties are pants.<br>All pants are buttons.<br><strong>Conclusion (I) :</strong> Some buttons are shoes.<br><strong>Conclusion (II) :</strong> No ties are buttons.</p>",
                    question_hi: "<p>4. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे&nbsp;समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong><br>सभी जूते, टाई हैं।<br>कुछ टाई, पैंट हैं।<br>सभी पैंट, बटन हैं।<br><strong>निष्कर्ष (I) : </strong>कुछ बटन, जूते हैं।<br><strong>निष्कर्ष (II) : </strong>कोई टाई, बटन नहीं है।</p>",
                    options_en: [
                        "<p>Neither conclusion (I) nor (II) follows.</p>",
                        "<p>Only conclusion (II) follows.</p>",
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Both conclusions (I) and (II) follow.</p>"
                    ],
                    options_hi: [
                        "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                        "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972500866.png\" alt=\"rId10\" width=\"273\" height=\"86\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972501025.png\" alt=\"rId11\" width=\"318\" height=\"100\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Which of the following terms will replace the question mark (?) in the given series? <br>MJPT, PGMW, SDJZ, ? , YXDF</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्&zwj;न चिह्न (?) का स्थान लेगा?<br>MJPT, PGMW, SDJZ, ? , YXDF</p>",
                    options_en: [
                        "<p>VAGC</p>",
                        "<p>VZFC</p>",
                        "<p>VAGD</p>",
                        "<p>WAGD</p>"
                    ],
                    options_hi: [
                        "<p>VAGC</p>",
                        "<p>VZFC</p>",
                        "<p>VAGD</p>",
                        "<p>WAGD</p>"
                    ],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972501237.png\" alt=\"rId12\" width=\"332\" height=\"112\"></p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972501237.png\" alt=\"rId12\" width=\"332\" height=\"112\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, \'78623\' is coded as \'TAKEN\' and \'32685\' is coded as \'TOKEN\' What is the code for \'5\' in that language?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में, \'78623\' को \'TAKEN\' लिखा जाता है और \'32685\' को \'TOKEN\' लिखा जाता है। तो उस कूट भाषा में \'5\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>O</p>",
                        "<p>T</p>",
                        "<p>E</p>",
                        "<p>A</p>"
                    ],
                    options_hi: [
                        "<p>O</p>",
                        "<p>T</p>",
                        "<p>E</p>",
                        "<p>A</p>"
                    ],
                    solution_en: "<p>6.(a) 78623 &rarr; TAKEN&hellip;&hellip;(i)<br>32685 &rarr; TOKEN&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;2683&rsquo; and &lsquo;TKEN&rsquo; are common. The code of &lsquo;5&rsquo; = &lsquo;O&rsquo;.</p>",
                    solution_hi: "<p>6.(a) 78623 &rarr; TAKEN&hellip;&hellip;(i)<br>32685 &rarr; TOKEN&hellip;&hellip;(ii)<br>(i) और (ii) से \'2683\' और \'TKEN\' उभयनिष्ठ हैं। \'5\' का कोड = \'O\'.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972501435.png\" alt=\"rId13\" width=\"139\" height=\"128\"></p>",
                    question_hi: "<p>7. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972501435.png\" alt=\"rId13\" width=\"139\" height=\"128\"></p>",
                    options_en: [
                        "<p>15</p>",
                        "<p>16</p>",
                        "<p>13</p>",
                        "<p>14</p>"
                    ],
                    options_hi: [
                        "<p>15</p>",
                        "<p>16</p>",
                        "<p>13</p>",
                        "<p>14</p>"
                    ],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972501776.png\" alt=\"rId15\" width=\"140\" height=\"139\"><br>There are 15 triangle<br>ABC , BDC, CDE, CFI , IFH, FGH,CFO, CFH, KIN, LMN, IHN, EJI, ACD, CKI , JIL</p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972501776.png\" alt=\"rId15\" width=\"140\" height=\"139\"><br>15 त्रिभुज हैं<br>ABC , BDC, CDE, CFI , IFH, FGH,CFO, CFH, KIN, LMN, IHN, EJI, ACD, CKI , JIL</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "8. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972501883.png\" alt=\"rId16\" /> ",
                    question_hi: "8. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972501883.png\" alt=\"rId16\" /> ",
                    options_en: [
                        " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502001.png\" alt=\"rId17\" />",
                        " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502147.png\" alt=\"rId18\" />",
                        " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502253.png\" alt=\"rId19\" />",
                        " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502355.png\" alt=\"rId20\" />"
                    ],
                    options_hi: [
                        " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502001.png\" alt=\"rId17\" />",
                        " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502147.png\" alt=\"rId18\" />",
                        " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502253.png\" alt=\"rId19\" />",
                        " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502355.png\" alt=\"rId20\" />"
                    ],
                    solution_en: "8.(b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502147.png\" alt=\"rId18\" />",
                    solution_hi: "8.(b)<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502147.png\" alt=\"rId18\" />",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the word pair that best represents a similar relationship to the one expressed in the pair of words given below. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br>Love : Harmony</p>",
                    question_hi: "<p>9. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्द-युग्म में व्यक्त किए गए संबंध के समान संबंध को सबसे अच्छा दर्शाता है। (शब्दों को अर्थपूर्ण हिंदी शब्द माना जाना चाहिए और इन्&zwj;हें शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए)<br>प्रेम : मैत्री</p>",
                    options_en: [
                        "<p>Calm : Sad</p>",
                        "<p>Peace : Bliss</p>",
                        "<p>Rage : Cool</p>",
                        "<p>Anger : Excitement</p>"
                    ],
                    options_hi: [
                        "<p>शांत : उदास</p>",
                        "<p>शांति : परमानंद</p>",
                        "<p>रोष : धैर्य</p>",
                        "<p>क्रोध : आवेश</p>"
                    ],
                    solution_en: "<p>9.(b)<br>As &lsquo;Love&rsquo; is the synonym of &lsquo;Harmony&rsquo; similarly &lsquo;Peace&rsquo; is the synonym of &lsquo;Bliss&rsquo;.</p>",
                    solution_hi: "<p>9.(b)<br>जैसे \'प्रेम\', \'मैत्री\' का पर्यायवाची है, उसी प्रकार \'शांति\', \'परमानंद\' का पर्यायवाची है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>20 - 80 &times; 16 &divide; 5 + 15 = ?</p>",
                    question_hi: "<p>10. यदि &lsquo;+&rsquo; और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा \'&times;\' और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा?<br>20 - 80 &times; 16 &divide; 5 + 15 = ?</p>",
                    options_en: [
                        "<p>32</p>",
                        "<p>35</p>",
                        "<p>30</p>",
                        "<p>36</p>"
                    ],
                    options_hi: [
                        "<p>32</p>",
                        "<p>35</p>",
                        "<p>30</p>",
                        "<p>36</p>"
                    ],
                    solution_en: "<p>10.(c) <strong>Given :-</strong> 20 - 80 &times; 16 <math display=\"inline\"><mo>&#247;</mo></math> 5 + 15<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; we get<br>20 + 80 <math display=\"inline\"><mo>&#247;</mo></math> 16 &times; 5 - 15<br>20 + 5&times;5 - 15 = 30</p>",
                    solution_hi: "<p>10.(c) <strong>दिया गया :-</strong> 20 - 80 &times; 16 <math display=\"inline\"><mo>&#247;</mo></math>5 + 15<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने पर हमें प्राप्त होता है<br>20 + 80 <math display=\"inline\"><mo>&#247;</mo></math> 16 &times; 5 - 15<br>20 + 5&times;5 - 15 = 30</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502454.png\" alt=\"rId21\" width=\"300\" height=\"65\"></p>",
                    question_hi: "<p>11. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502454.png\" alt=\"rId21\" width=\"323\" height=\"70\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502557.png\" alt=\"rId22\" width=\"60\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502657.png\" alt=\"rId23\" width=\"60\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502734.png\" alt=\"rId24\" width=\"61\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502830.png\" alt=\"rId25\" width=\"60\" height=\"60\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502557.png\" alt=\"rId22\" width=\"70\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502657.png\" alt=\"rId23\" width=\"70\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502734.png\" alt=\"rId24\" width=\"71\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502830.png\" alt=\"rId25\" width=\"72\" height=\"72\"></p>"
                    ],
                    solution_en: "<p>11.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502557.png\" alt=\"rId22\" width=\"71\" height=\"71\"></p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502557.png\" alt=\"rId22\" width=\"71\" height=\"71\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<strong>&nbsp;(NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g., 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(4, 16, 32)<br>(7, 49, 98)</p>",
                    question_hi: "<p>12. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं।&nbsp;<strong>(ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओ पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(4, 16, 32)<br>(7, 49, 98)</p>",
                    options_en: [
                        "<p>(12, 144, 278)</p>",
                        "<p>(12, 134, 288)</p>",
                        "<p>(14, 144, 288)</p>",
                        "<p>(12, 144, 288)</p>"
                    ],
                    options_hi: [
                        "<p>(12, 144, 278)</p>",
                        "<p>(12, 134, 288)</p>",
                        "<p>(14, 144, 288)</p>",
                        "<p>(12, 144, 288)</p>"
                    ],
                    solution_en: "<p>12.(d) <strong>Logic :-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>3</mn><mi>rd</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi>nd</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi></msqrt></math> = 1st number<br>(4, 16, 32) :- (<math display=\"inline\"><msqrt><mn>32</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>16</mn></msqrt></math>) &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> = 4<br>(7, 49, 98) :- (<math display=\"inline\"><msqrt><mn>98</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>49</mn></msqrt></math>) &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> = 7<br>Similarly,<br>(12, 144, 288) :- (<math display=\"inline\"><msqrt><mn>288</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>144</mn></msqrt></math>) &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math> = 12</p>",
                    solution_hi: "<p>12.(d) <strong>तर्क :-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mi>&#2340;&#2368;&#2360;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>&#2342;&#2370;&#2360;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>)</mo></msqrt></math> = पहली संख्या<br>(4, 16, 32) :- (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>32</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>16</mn></msqrt></math>) &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> = 4<br>(7, 49, 98) :- (<math display=\"inline\"><msqrt><mn>98</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>49</mn></msqrt></math>) &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> = 7<br>इसी प्रकार,<br>(12, 144, 288) :- (<math display=\"inline\"><msqrt><mn>288</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>144</mn></msqrt></math>) &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math> = 12</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In the following series, only one letter-cluster is incorrect. Select the incorrect letter-cluster.<br>ZXU SQN LKI ECZ XVS</p>",
                    question_hi: "<p>13. निम्नलिखित शृंखला में, केवल एक अक्षर-समूह गलत है। गलत अक्षर-समूह का चयन कीजिए।<br>ZXU SQN LKI ECZ XVS</p>",
                    options_en: [
                        "<p>XVS</p>",
                        "<p>ECZ</p>",
                        "<p>LKI</p>",
                        "<p>SQN</p>"
                    ],
                    options_hi: [
                        "<p>XVS</p>",
                        "<p>ECZ</p>",
                        "<p>LKI</p>",
                        "<p>SQN</p>"
                    ],
                    solution_en: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502995.png\" alt=\"rId26\" width=\"251\" height=\"110\"></p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972502995.png\" alt=\"rId26\" width=\"251\" height=\"110\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, \'candle main\' is coded as \'fire blaze\' and \'line candle\' is coded as \'number fire\'. What is the code word for \'main\'?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में \'candle main\' को \'fire blaze\' के रूप में कूटबद्ध किया जाता है और \'line candle\' को \'number fire\' के रूप में कूटबद्ध किया जाता है। \'main\'\' के लिए कूट शब्द क्या होगा?</p>",
                    options_en: [
                        "<p>over</p>",
                        "<p>candle</p>",
                        "<p>blaze</p>",
                        "<p>fire</p>"
                    ],
                    options_hi: [
                        "<p>over</p>",
                        "<p>candle</p>",
                        "<p>blaze</p>",
                        "<p>fire</p>"
                    ],
                    solution_en: "<p>14.(c) candle main &rarr; fire blaze&hellip;&hellip;. (i) <br>line candle &rarr; number fire&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;candle&rsquo; and &lsquo;fire&rsquo; are common. The code of &lsquo;main&rsquo; = &lsquo;blaze&rsquo;</p>",
                    solution_hi: "<p>14.(c) candle main &rarr; fire blaze&hellip;&hellip;. (i) <br>line candle &rarr; number fire&hellip;&hellip;.(ii)<br>(i) और (ii) से &lsquo;candle&rsquo; और &lsquo;fire&rsquo; उभयनिष्ठ हैं। \'main\' का कूट = \'blaze\'</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. 529 is related to 23 following a certain logic. Following the same logic, 324 is related to 18. To which of the following is 625 related following the same logic?&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>15. एक निश्चित तर्क का अनुसरण करते हुए 529, 23 से संबंधित है। उसी तर्क का अनुसरण करते हुए 324, 18 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 625 निम्नलिखित में से किससे संबंधित है?&nbsp;(ध्यान दें : संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>23</p>",
                        "<p>35</p>",
                        "<p>25</p>",
                        "<p>27</p>"
                    ],
                    options_hi: [
                        "<p>23</p>",
                        "<p>35</p>",
                        "<p>25</p>",
                        "<p>27</p>"
                    ],
                    solution_en: "<p>15.(c) <strong>Logic :-</strong> (2nd number)&sup2; = (1st number)<br>(529, 23) :- (23)&sup2; = 529<br>(324 ,18) :- (18)&sup2; = 324<br>Similarly,<br>(625 , 25) :- (25)&sup2; = 625</p>",
                    solution_hi: "<p>15.(c) <strong>तर्क :</strong>- (दूसरी संख्या)&sup2; = (पहली संख्या)<br>(529, 23) :- (23)&sup2; = 529<br>(324 ,18) :- (18)<strong>&sup2;</strong> = 324<br>इसी प्रकार,<br>(625 , 25) :- (25)&sup2; = 625</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different.&nbsp;(Note : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>16. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए।</p>",
                    options_en: [
                        "<p>KPQJ</p>",
                        "<p>TGHS</p>",
                        "<p>NMNM</p>",
                        "<p>ZABX</p>"
                    ],
                    options_hi: [
                        "<p>KPQJ</p>",
                        "<p>TGHS</p>",
                        "<p>NMNM</p>",
                        "<p>ZABX</p>"
                    ],
                    solution_en: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972503136.png\" alt=\"rId27\" width=\"90\" height=\"95\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972503275.png\" alt=\"rId28\" width=\"93\" height=\"95\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972503482.png\" alt=\"rId29\" width=\"96\" height=\"96\"><br>But, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972503670.png\" alt=\"rId30\" width=\"82\" height=\"95\"></p>",
                    solution_hi: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972503839.png\" alt=\"rId31\" width=\"90\" height=\"95\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972504095.png\" alt=\"rId32\" width=\"92\" height=\"95\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972504240.png\" alt=\"rId33\" width=\"96\" height=\"95\"><br><br>लेकिन, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972504403.png\" alt=\"rId34\" width=\"82\" height=\"95\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. In a certain code language, \'NORMAL\' is written as \'LMPOCN\' and \'PEOPLE\' is written as \'NCMRNG\'. How will \'MOMENT\' be written in that language ?</p>",
                    question_hi: "<p>17. एक निश्चित कोड भाषा में \'NORMAL\' को \'LMPOCN\' लिखा जाता है और \'PEOPLE\' को \'NCMRNG\' लिखा जाता है। उस भाषा में \'MOMENT\' कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>KNKGPU</p>",
                        "<p>KMKGPV</p>",
                        "<p>LMLGPV</p>",
                        "<p>KMKHPU</p>"
                    ],
                    options_hi: [
                        "<p>KNKGPU</p>",
                        "<p>KMKGPV</p>",
                        "<p>LMLGPV</p>",
                        "<p>KMKHPU</p>"
                    ],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972504680.png\" alt=\"rId35\" width=\"130\" height=\"60\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972504781.png\" alt=\"rId36\" width=\"130\" height=\"60\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972504906.png\" alt=\"rId37\" width=\"131\" height=\"60\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972504680.png\" alt=\"rId35\" width=\"130\" height=\"60\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972504781.png\" alt=\"rId36\" width=\"130\" height=\"60\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972504906.png\" alt=\"rId37\" width=\"131\" height=\"60\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. What should come in place of the question mark (?) in the given series? <br>4, 20, 100, 500, 2500, ?</p>",
                    question_hi: "<p>18. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>4, 20, 100, 500, 2500, ?</p>",
                    options_en: [
                        "<p>12700</p>",
                        "<p>12500</p>",
                        "<p>12600</p>",
                        "<p>12200</p>"
                    ],
                    options_hi: [
                        "<p>12700</p>",
                        "<p>12500</p>",
                        "<p>12600</p>",
                        "<p>12200</p>"
                    ],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505014.png\" alt=\"rId38\" width=\"253\" height=\"64\"></p>",
                    solution_hi: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505014.png\" alt=\"rId38\" width=\"253\" height=\"64\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In this question, three statements are given, followed by three conclusions numbered&nbsp;I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements.<br><strong>Statements :</strong><br>All ladders are chairs.<br>All chairs are tables.<br>No table is a stair.<br><strong>Conclusions :</strong><br>I. Some tables are ladders.<br>II. No chair is a stair.<br>III. Some ladders are stairs.</p>",
                    question_hi: "<p>19. इस प्रश्न में तीन कथन दिए गए हैं, जिनके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए भले ही वे सामान्य रूप से ज्ञात तथ्यों से अलग प्रतीत होते हों, निर्धारित करें कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>सभी सीढ़ियाँ, कुर्सियाँ हैं।<br>सभी कुर्सियाँ, मेजें हैं।<br>कोई मेज, जीना नहीं है।<br><strong>निष्कर्ष :</strong><br>I. कुछ मेजें, सीढ़ियाँ हैं।<br>II. कोई कुर्सी, जीना नहीं है।<br>III. कुछ सीढ़ियाँ, जीने हैं।</p>",
                    options_en: [
                        "<p>Only conclusion III follows.</p>",
                        "<p>Only conclusion I follows.</p>",
                        "<p>Both conclusions I and II follow.</p>",
                        "<p>Only conclusion II follows.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष III अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>",
                        "<p>निष्कर्ष I और II, दोनों अनुसरण करते हैं।</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505114.png\" alt=\"rId39\" width=\"285\" height=\"80\"><br>Both Conclusion I and II follow.</p>",
                    solution_hi: "<p>19.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505252.png\" alt=\"rId40\" width=\"318\" height=\"80\"><br>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. All the six members of a family, 1, 2, 3, 4, 5 and 6, live together. 2 is the daughter of 3, but 3 is not the mother of 2. 1 and 3 are a married couple. 5 is the sister of 3. 4 is the daughter of 1. 6 is the brother of 2. Who is the mother of 2?</p>",
                    question_hi: "<p>20. एक परिवार के सभी छ: सदस्य 1, 2, 3, 4, 5 और 6 एक साथ रहते हैं। 2, 3 की पुत्री है, लेकिन 3, 2 की माता नहीं है। 1 और 3 विवाहित युगल हैं। 5, 3 की बहन है। 4, 1 की पुत्री है। 6, 2 का भाई है। 2 की माता कौन है?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>5</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>5</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505348.png\" alt=\"rId41\" width=\"173\" height=\"90\"><br>We can see that 1 is the mother of 2.</p>",
                    solution_hi: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505348.png\" alt=\"rId41\" width=\"173\" height=\"90\"><br>हम देख सकते हैं कि 1, 2 की माँ है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Bananas, Apple, Grapes</p>",
                    question_hi: "<p>21. उस वेन-आरेख का चयन कीजिए, जो निम्नलिखित वर्गों के बीच के संबंध को सर्वोत्तम रूप से दर्शाता है।<br>केले, सेब, अंगूर</p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505598.png\" alt=\"rId42\" width=\"84\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505687.png\" alt=\"rId43\" width=\"119\" height=\"45\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505802.png\" alt=\"rId44\" width=\"76\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505914.png\" alt=\"rId45\" width=\"122\" height=\"64\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505598.png\" alt=\"rId42\" width=\"75\" height=\"63\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505687.png\" alt=\"rId43\" width=\"119\" height=\"45\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505802.png\" alt=\"rId44\" width=\"76\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972505914.png\" alt=\"rId45\" width=\"122\" height=\"64\"></p>"
                    ],
                    solution_en: "<p>21.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506007.png\" alt=\"rId46\" width=\"248\" height=\"60\"></p>",
                    solution_hi: "<p>21.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506132.png\" alt=\"rId47\" width=\"249\" height=\"60\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. If \'A\' stands for \'<math display=\"inline\"><mo>&#247;</mo></math>\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for &lsquo;-\', what will come in place of the question mark (?) in the following equation?<br>45 B 6 D 78 A 3 C 15=?</p>",
                    question_hi: "<p>22. यदि \'A\' का अर्थ \'<math display=\"inline\"><mo>&#247;</mo></math>&rsquo;, \'B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>45 B 6 D 78 A 3 C 15=?</p>",
                    options_en: [
                        "<p>257</p>",
                        "<p>256</p>",
                        "<p>258</p>",
                        "<p>259</p>"
                    ],
                    options_hi: [
                        "<p>257</p>",
                        "<p>256</p>",
                        "<p>258</p>",
                        "<p>259</p>"
                    ],
                    solution_en: "<p>22.(d) <strong>Given :-</strong> 45 B 6 D 78 A 3 C 15<br>As per given instruction after interchanging the letter with sign we get<br>45 &times; 6 - 78 <math display=\"inline\"><mo>&#247;</mo></math> 3 + 15<br>270 - 26 + 15 <br>270 - 11 = 259</p>",
                    solution_hi: "<p>22.(d) <strong>दिया गया :- </strong>45 B 6 D 78 A 3 C 15<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>45 &times; 6 - 78 <math display=\"inline\"><mo>&#247;</mo></math> 3 + 15<br>270 - 26 + 15 <br>270 - 11 = 259</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Which of the following terms will replace the question mark (?) in the given series?<br>KES, MIQ, PMN, TQJ, YUE, ?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्&zwj;न चिह्न (?) का स्थान लेगा?<br>KES, MIQ, PMN, TQJ, YUE, ?</p>",
                    options_en: [
                        "<p>EXY</p>",
                        "<p>FYX</p>",
                        "<p>EYY</p>",
                        "<p>EXZ</p>"
                    ],
                    options_hi: [
                        "<p>EXY</p>",
                        "<p>FYX</p>",
                        "<p>EYY</p>",
                        "<p>EXZ</p>"
                    ],
                    solution_en: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506231.png\" alt=\"rId48\" width=\"314\" height=\"93\"></p>",
                    solution_hi: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506231.png\" alt=\"rId48\" width=\"314\" height=\"93\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Six letters U, V, W, X, Y and Z are written on different faces of a dice. Two positions of this dice are shown in the figure. Find the letter on the face opposite to V.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506447.png\" alt=\"rId49\" width=\"159\" height=\"78\"></p>",
                    question_hi: "<p>24. एक पासे के विभिन्न फलकों पर छ: अक्षर U, V, W, X, Y और Z लिखे गए हैं। नीचे चित्र में इस पासे की दो स्थितियाँ दिखाई गई है। अक्षर V के विपरीत फलक पर कौन-सा अक्षर है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506447.png\" alt=\"rId49\" width=\"135\" height=\"66\"></p>",
                    options_en: [
                        "<p>X</p>",
                        "<p>Y</p>",
                        "<p>Z</p>",
                        "<p>W</p>"
                    ],
                    options_hi: [
                        "<p>X</p>",
                        "<p>Y</p>",
                        "<p>Z</p>",
                        "<p>W</p>"
                    ],
                    solution_en: "<p>24.(c) in the dice 1 and 2 &lsquo;U&rsquo; and &lsquo;X&rsquo; are common<br>Hence, &lsquo;Z&rsquo; is the opposite of &lsquo;V&rsquo;</p>",
                    solution_hi: "<p>24.(c) पासे 1 और 2 में \'U\' और \'X\' उभयनिष्ठ हैं<br>इसलिए, \'Z\', \'V\' के विपरीत है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;DOWNSIZE&rsquo; is re-arranged in the English alphabetical order from left to right?</p>",
                    question_hi: "<p>25. यदि शब्द &lsquo;DOWNSIZE&rsquo; के प्रत्येक अक्षर को अंग्रेजी वर्णमाला के क्रम में बाएँ से दाएँ पुनर्व्यवस्थित किया जाए तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी?</p>",
                    options_en: [
                        "<p>Four</p>",
                        "<p>Three</p>",
                        "<p>Two</p>",
                        "<p>One</p>"
                    ],
                    options_hi: [
                        "<p>चार</p>",
                        "<p>तीन</p>",
                        "<p>दो</p>",
                        "<p>एक</p>"
                    ],
                    solution_en: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506595.png\" alt=\"rId50\" width=\"208\" height=\"100\"><br>From the above figure we can see that the positions of &lsquo;D&rsquo; and &lsquo;N&rsquo; remain unchanged.</p>",
                    solution_hi: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506595.png\" alt=\"rId50\" width=\"208\" height=\"100\"><br>उपरोक्त चित्र से हम देख सकते हैं कि \'D\' और \'N\' की स्थिति अपरिवर्तित रहती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. In the context of periodicity, a unit called picometre is used to measure the __________.</p>",
                    question_hi: "<p>26. आवर्तता (पीरियोडिसिटी) के संदर्भ में ________ को मापने के लिए पिकोमीटर नामक इकाई का उपयोग किया जाता है।</p>",
                    options_en: [
                        "<p>atomic radius</p>",
                        "<p>molar mass</p>",
                        "<p>atomic density</p>",
                        "<p>spin quantum number</p>"
                    ],
                    options_hi: [
                        "<p>परमाणु त्रिज्या</p>",
                        "<p>मोलर द्रव्यमान</p>",
                        "<p>परमाणु घनत्व</p>",
                        "<p>स्पिन क्वांटम संख्या</p>"
                    ],
                    solution_en: "<p>26.(a) <strong>atomic radius</strong> (Atomic Radii). It is the total distance from the nucleus of an atom to the outermost orbital of its electron. In chemistry, the molar mass of a chemical compound is defined as the ratio between the mass and the amount of substance of any sample of the compound. The atomic number density is the number of atoms of a given type per unit volume (V, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math>) of the material.</p>",
                    solution_hi: "<p>26.(a) <strong>परमाणु त्रिज्या</strong>। यह किसी परमाणु के नाभिक से उसके इलेक्ट्रॉन की सबसे बाहरी कक्षा तक की कुल दूरी है। रसायन विज्ञान में, किसी रासायनिक यौगिक के मोलर द्रव्यमान को यौगिक के किसी भी सैम्पल के द्रव्यमान और पदार्थ की मात्रा के बीच के अनुपात के रूप में परिभाषित किया जाता है। परमाणु संख्या घनत्व पदार्थ के प्रति इकाई आयतन (V, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math>) में किसी दिए गए प्रकार के परमाणुओं की संख्या है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following institutions govern and control the cricket matches?</p>",
                    question_hi: "<p>27. निम्नलिखित में से कौन-सी संस्था क्रिकेट मैचों का संचालन और नियंत्रण करती है ?</p>",
                    options_en: [
                        "<p>FIFA</p>",
                        "<p>FIDE</p>",
                        "<p>FIH</p>",
                        "<p>ICC</p>"
                    ],
                    options_hi: [
                        "<p>FIFA</p>",
                        "<p>FIDE</p>",
                        "<p>FIH</p>",
                        "<p>ICC</p>"
                    ],
                    solution_en: "<p>27.(d) <strong>ICC</strong> (International Cricket Council) - Founded in: 1909. Headquarters: Dubai, United Arab Emirates. FIFA (Federation Internationale de football association) - Founded in 1904 in Paris, (France); Headquarters: Zurich (Switzerland). FIDE (International Chess Federation) - Founded in 1924 in Paris (France); Headquarters: Lausanne (Switzerland). FIH (International Hockey Federation) - Founded in 1924 in Paris (France); Headquarters: Lausanne (Switzerland).</p>",
                    solution_hi: "<p>27.(d) <strong>ICC </strong>(अंतर्राष्ट्रीय क्रिकेट परिषद) - स्थापना: 1909। मुख्यालय: दुबई, संयुक्त अरब अमीरात। FIFA (फेडरेशन इंटरनेशनेल डी फुटबॉल एसोसिएशन) - 1904 में पेरिस, फ्रांस में स्थापित; मुख्यालय: ज्यूरिख (स्विट्जरलैंड)। FIDE (अंतर्राष्ट्रीय शतरंज महासंघ) - 1924 में पेरिस (फ्रांस) में स्थापित, मुख्यालय: लॉज़ेन (स्विट्जरलैंड)। FIH (अंतर्राष्ट्रीय हॉकी महासंघ) - 1924 में पेरिस (फ्रांस) में स्थापित, मुख्यालय: लॉज़ेन (स्विट्जरलैंड)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which among the following is NOT a classification criterion of drugs?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सा दवाओं का वर्गीकरण मानदंड नहीं है?</p>",
                    options_en: [
                        "<p>Chemical structure</p>",
                        "<p>Molecular target</p>",
                        "<p>Behavioural condition</p>",
                        "<p>Pharmacological effect</p>"
                    ],
                    options_hi: [
                        "<p>रासायनिक संरचना</p>",
                        "<p>आणविक लक्ष्य</p>",
                        "<p>व्यावहारिक स्थिति</p>",
                        "<p>औषधीय प्रभाव</p>"
                    ],
                    solution_en: "<p>28.(c) <strong>Behavioural condition.</strong> Drugs can be classified in different ways : Pharmacological Effect - This refers to how the drug affects cells in the body. Some drugs act on specific groups of cells, while others affect the entire body, depending on their type. Molecular Target - All drugs work by acting on specific target molecules in the body. Even if drugs are meant to treat the same condition, they can work in different ways depending on their target. Molecular Structure - Drugs with similar chemical structures often have similar effects and responses in the body.</p>",
                    solution_hi: "<p>28.(c) <strong>व्यावहारिक स्थिति। </strong>दवाओं को विभिन्न तरीकों से वर्गीकृत किया जा सकता है: औषधीय प्रभाव - यह दर्शाता है कि दवा शरीर में कोशिकाओं को कैसे प्रभावित करती है। कुछ दवाएँ कोशिकाओं के विशिष्ट समूहों पर कार्य करती हैं, जबकि अन्य सम्पूर्ण शरीर को प्रभावित करती हैं, जो उनके प्रारूप पर निर्भर करती है। आणविक लक्ष्य - सभी दवाएँ शरीर में विशिष्ट लक्ष्य अणुओं पर कार्य करके काम करती हैं। भले ही दवाएँ एक ही स्थिति का इलाज करने के लिए बनाई गई हों, लेकिन वे अपने लक्ष्य के आधार पर अलग-अलग तरीकों से काम कर सकती हैं। आणविक संरचना - समान रासायनिक संरचना वाली दवाओं का अक्सर शरीर में समान प्रभाव और अभिक्रियाएँ होती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The type of reaction that is typically found when a material that is required for the reaction to proceed, such as a surface or a catalyst, is saturated by the reactants is called a ________ order reaction.</p>",
                    question_hi: "<p>29. अभिक्रिया का वह प्रकार, जो आम तौर पर तब पाया जाता है जब अभिक्रिया के आगे बढ़ने के लिए आवश्यक सामग्री, जैसे सतह या उत्प्रेरक, अभिकारकों द्वारा संतृप्त होता है, __________ कोटि की अभिक्रिया कहलाता है।</p>",
                    options_en: [
                        "<p>first</p>",
                        "<p>second</p>",
                        "<p>zero</p>",
                        "<p>third</p>"
                    ],
                    options_hi: [
                        "<p>प्रथम</p>",
                        "<p>द्वितीय</p>",
                        "<p>शून्य</p>",
                        "<p>तृतीय</p>"
                    ],
                    solution_en: "<p>29.(c) <strong>zero-order reaction:</strong> It is a type of chemical reaction in which the rate is independent of the concentration of reactants, remains constant regardless of reactant concentration, and is typically limited by factors other than the concentration of the reactants.</p>",
                    solution_hi: "<p>29.(c) <strong>शून्य-कोटि अभिक्रिया:</strong> यह एक प्रकार की रासायनिक अभिक्रिया है जिसमें दर अभिकारकों की सांद्रता से स्वतंत्र होती है, अभिकारक सांद्रता की परवाह किए बिना स्थिर रहती है, तथा सामान्यतः अभिकारकों की सांद्रता के अलावा अन्य कारकों द्वारा सीमित होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. What happens when two forces act in the opposite directions on an object?</p>",
                    question_hi: "<p>30. जब किसी वस्तु पर दो बल विपरीत दिशाओं में कार्य करते हैं तो क्या होता है?</p>",
                    options_en: [
                        "<p>The net force acting on the object is the difference between the two objects.</p>",
                        "<p>The net force acting on the object is the difference between the two forces.</p>",
                        "<p>The net force acting on the object is the total of the two forces.</p>",
                        "<p>The net force acting on the object is the sum of the two objects.</p>"
                    ],
                    options_hi: [
                        "<p>वस्तु पर कार्य करने वाला शुद्ध बल दो वस्तुओं के बीच का अंतर होता है।</p>",
                        "<p>वस्तु पर कार्य करने वाला शुद्ध बल दो बलों के बीच का अंतर होता है।</p>",
                        "<p>वस्तु पर कार्य करने वाला शुद्ध बल दो बलों का योग होता है।</p>",
                        "<p>वस्तु पर कार्य करने वाला शुद्ध बल दो वस्तुओं का योग होता है।</p>"
                    ],
                    solution_en: "<p>30.(b) When two forces act in opposite directions on an object, the net force (or resultant force) is the difference between the magnitudes of the two forces. This is because the forces are opposing each other. For example, if a 5 N force acts to the right and a 3 N force acts to the left, the net force would be: = 5 N - 3 N = 2 N (to the right).</p>",
                    solution_hi: "<p>30.(b) जब दो बल किसी वस्तु पर विपरीत दिशाओं में कार्य करते हैं, तो कुल बल (या परिणामी बल) दोनों बलों के परिमाण के बीच का अंतर होता है। ऐसा इसलिए है क्योंकि बल एक-दूसरे का विरोध कर रहे हैं। उदाहरण के लिए, यदि 5 N बल दाईं ओर कार्य करता है और 3 N बल बाईं ओर कार्य करता है, तो कुल बल होगा: = 5 N - 3 N = 2 N (दाईं ओर)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which essential amino acid enhances calcium absorption and also plays an important role in the formation of collagen?</p>",
                    question_hi: "<p>31. कौन-सा आवश्यक एमिनो अम्ल कैल्शियम अवशोषण को बढ़ाता है और कोलेजन के निर्माण में भी महत्वपूर्ण भूमिका निभाता है?</p>",
                    options_en: [
                        "<p>Arginine</p>",
                        "<p>Tyrosine</p>",
                        "<p>Lysine</p>",
                        "<p>Histidine</p>"
                    ],
                    options_hi: [
                        "<p>आर्जीनिन</p>",
                        "<p>टायरोसीन</p>",
                        "<p>लाइसीन</p>",
                        "<p>हिस्टीडीन</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>Lysine</strong> is an essential amino acid, which means that humans cannot synthesize it so it must be eaten in the diet. Sources include meat, fish, dairy, and eggs. Collagen contains hydroxylysine, which is derived from lysine by the action of the enzyme lysyl hydroxylase. Amino acids are classified as acidic, basic, or neutral based on the relative number of amino and carboxyl groups in their molecules. L-arginine is an amino acid that helps the body build protein. Histidine is an essential amino acid that is used in the biosynthesis of proteins. Tyrosine is a non-essential amino-acid found in both proteins and the free form in most living organisms.</p>",
                    solution_hi: "<p>31.(c) <strong>लाइसीन</strong> एक आवश्यक अमीनो अम्ल है, जिसका अर्थ है कि मनुष्य इसका संश्लेषण नहीं कर सकते हैं इसलिए इसे आहार में खाना चाहिए। स्रोतों में मांस, मछली, डेयरी और अंडे शामिल हैं। कोलेजन में हाइड्रॉक्सीलाइसीन होता है, जो लाइसिल हाइड्रॉक्सीलेज एंजाइम की क्रिया द्वारा लाइसीन से प्राप्त होता है। अमीनो एसिड को उनके अणुओं में अमीनो और कार्बोक्सिल समूहों की सापेक्ष संख्या के आधार पर अम्लीय, क्षारीय या उदासीन के रूप में वर्गीकृत किया जाता है। एल-आर्जिनीन एक अमीनो एसिड है जो शरीर को प्रोटीन बनाने में मदद करता है। हिस्टिडीन एक आवश्यक अमीनो एसिड है जिसका उपयोग प्रोटीन के जैव संश्लेषण में किया जाता है। टायरोसीन एक गैर-आवश्यक अमीनो एसिड है जो अधिकांश जीवित जीवों में प्रोटीन और मुक्त रूप दोनों में पाया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Who among the following is the author of the novel \'Ret Samadhi\' ?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन \'रेत समाधि\' उपन्यास के/की लेखक/लेखिका हैं ?</p>",
                    options_en: [
                        "<p>Rita Kumari</p>",
                        "<p>Geetanjali Shree</p>",
                        "<p>Shashi Tharoor</p>",
                        "<p>Jhumpa Lahiri</p>"
                    ],
                    options_hi: [
                        "<p>रीता कुमारी</p>",
                        "<p>गीतांजलि श्री</p>",
                        "<p>शशि थरूर</p>",
                        "<p>झुम्पा लाहिड़ी</p>"
                    ],
                    solution_en: "<p>32.(b)<strong> Geetanjali Shree. </strong>Her book \'Tomb of Sand (Ret Samadhi)\' has become the first Hindi novel to be awarded the prestigious 2022 International Booker Prize. The book was translated into English by Daisy Rockwell. Hamara Shahar Us Baras, Khali Jagah and Pratinidhi Kahaniyan are other books by her. Other Authors and Books : Shashi Tharoor - The Great Indian Novel, A Wonderland of Words. Jhumpa Lahiri - The Namesake, The Lowland.</p>",
                    solution_hi: "<p>32.(b) <strong>गीतांजलि श्री।</strong> उनकी पुस्तक \'टॉम्ब ऑफ सैंड (रेत समाधि)\' प्रतिष्ठित 2022 अंतर्राष्ट्रीय बुकर पुरस्कार से सम्मानित होने वाला प्रथम हिंदी उपन्यास बन गया है। इस पुस्तक का अंग्रेजी में अनुवाद डेज़ी रॉकवेल ने किया था। हमारा शहर उस बरस, खाली जगह एण्ड प्रतिनिधि कहानियाँ उनकी अन्य पुस्तकें हैं। अन्य लेखक और उनकी पुस्तकें: शशि थरूर - द ग्रेट इंडियन नॉवेल, ए वंडरलैंड ऑफ़ वर्ड्स। झुम्पा लाहिड़ी - द नेमसेक, द लोलैंड।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. The Dang Darbar Fair is held every year in which Indian state?</p>",
                    question_hi: "<p>33. डांग दरबार मेला हर साल किस भारतीय राज्य में आयोजित किया जाता है?</p>",
                    options_en: [
                        "<p>Madhya Pradesh</p>",
                        "<p>Rajasthan</p>",
                        "<p>Maharashtra</p>",
                        "<p>Gujarat</p>"
                    ],
                    options_hi: [
                        "<p>मध्य प्रदेश</p>",
                        "<p>राजस्थान</p>",
                        "<p>महाराष्ट्र</p>",
                        "<p>गुजरात</p>"
                    ],
                    solution_en: "<p>33.(d) <strong>Gujarat. </strong>Dangs Darbar is a fair held during the Holi festival in Ahwa district in Gujarat. Traditionally, tribal people perform dance and music, celebrating it as a festival. Notable festivals in Gujarat include Navratri, Rann Utsav, International Kite Festival (Uttarayan), Modhera Dance Festival, Makar Sankranti, Janmashtami, and Diwali.</p>",
                    solution_hi: "<p>33.(d) <strong>गुजरात।</strong> डांग दरबार गुजरात के आहवा जिले में होली के त्योहार के दौरान आयोजित होने वाला एक मेला है। परंपरागत रूप से, आदिवासी लोग नृत्य और संगीत प्रस्तुत करते हैं, इसे एक त्योहार के रूप में मनाते हैं। गुजरात के उल्लेखनीय त्योहारों में नवरात्रि, रण उत्सव, अंतर्राष्ट्रीय पतंग महोत्सव (उत्तरायण), मोढेरा नृत्य महोत्सव, मकर संक्रांति, जन्माष्टमी और दिवाली शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which Article of the Constitution of India specifically addresses the qualifications for the office of the President?</p>",
                    question_hi: "<p>34. भारत के संविधान का कौन-सा अनुच्छेद विशेष रूप से राष्ट्रपति पद के लिए योग्यताओं से संबंधित है?</p>",
                    options_en: [
                        "<p>Article 50</p>",
                        "<p>Article 56</p>",
                        "<p>Article 58</p>",
                        "<p>Article 60</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 50</p>",
                        "<p>अनुच्छेद 56</p>",
                        "<p>अनुच्छेद 58</p>",
                        "<p>अनुच्छेद 60</p>"
                    ],
                    solution_en: "<p>34.(c)<strong> Article 58. </strong>The President of India is the head of state of the Republic of India. He is the formal head of the executive, legislature, and judiciary of India and is also the commander-in-chief of the Indian Armed Forces. Articles related to the President of India : Article 52 - The President of India, Article 54 : Election of President, Article 55 : Manner of Election of President, Article 56 : Term of office of President, Article 60 : Oath or affirmation by the President. Article 50 deals with the separation of the judiciary from the executive.</p>",
                    solution_hi: "<p>34.(c) <strong>अनुच्छेद 58.</strong> भारत का राष्ट्रपति भारतीय गणराज्य का राष्ट्रप्रमुख होता हैं। वह भारत की कार्यपालिका, विधायिका और न्यायपालिका का औपचारिक प्रमुख होता है और भारतीय सशस्त्र बलों का कमांडर-इन-चीफ भी होता है। भारत के राष्ट्रपति से संबंधित अनुच्छेद: अनुच्छेद 52 - भारत का राष्ट्रपति, अनुच्छेद 54: राष्ट्रपति का निर्वाचन, अनुच्छेद 55: राष्ट्रपति के निर्वाचन की प्रक्रिया, अनुच्छेद 56: राष्ट्रपति की पदावधि, अनुच्छेद 60: राष्ट्रपति के द्वारा शपथ या प्रतिज्ञान। अनुच्छेद 50: न्यायपालिका को कार्यपालिका से अलग करने से संबंधित है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which state of India does the musician Shyamamani Devi belong to ?</p>",
                    question_hi: "<p>35.संगीतकार श्याममणि देवी भारत के किस राज्य से संबंधित हैं?</p>",
                    options_en: [
                        "<p>Odisha</p>",
                        "<p>Bihar</p>",
                        "<p>West Bengal</p>",
                        "<p>Uttar Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>ओडिशा</p>",
                        "<p>बिहार</p>",
                        "<p>पश्चिम बंगाल</p>",
                        "<p>उत्तर प्रदेश</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Odisha.</strong> Shyamamani Devi was an Odissi classical music vocalist and composer. She received Padma Shri in 2022. Some famous Odissi musicians include Tarini Charan Patra, Pandit Ramhari Das, \'Suramani\' Pandit Raghunath Panigrahi, and Singhari Shyamasundar Kar.</p>",
                    solution_hi: "<p>35.(a) <strong>ओडिशा। </strong>श्याममणि देवी एक ओडिसी शास्त्रीय संगीत गायिका और संगीतकार थीं। उन्हें 2022 में पद्मश्री मिला। कुछ प्रसिद्ध ओडिसी संगीतकारों में तारिणी चरण पात्र, पंडित रामहरि दास, \'सुरमणि\' पंडित रघुनाथ पाणिग्रही और सिंघारी श्यामसुंदर कर शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following is Fisher\'s equation of exchange?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन-सा फिशर का विनिमय समीकरण है?</p>",
                    options_en: [
                        "<p>M V = P T</p>",
                        "<p>M T = V P</p>",
                        "<p>M = S + I</p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>M</mi><mi>V</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>T</mi><mi>P</mi></mfrac></mstyle></math></p>"
                    ],
                    options_hi: [
                        "<p>M V = P T</p>",
                        "<p>M T = V P</p>",
                        "<p>M = S + I</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>M</mi></mrow><mrow><mi>V</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>T</mi><mi>P</mi></mfrac></mstyle></math></p>"
                    ],
                    solution_en: "<p>36.(a) <strong>MV = PT. Fisher Equation </strong>states that the nominal interest rate is equal to the sum of the real interest rate plus inflation. It lies at the heart of the Quantity Theory of Money. MV = PT, where M = Money Supply, V = Velocity of circulation, P = Price Level, and T = Transactions.</p>",
                    solution_hi: "<p>36.(a) <strong>MV = PT. फिशर समीकरण कहता </strong>है कि नाममात्र ब्याज दर वास्तविक ब्याज दर और मुद्रास्फीति के योग के बराबर होती है। यह मुद्रा के परिमाण सिद्धांत के केंद्र में है। MV = PT, जहाँ M = मुद्रा आपूर्ति, V = परिसंचरण वेग, P = मूल्य स्तर, और T = लेनदेन है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. ______ leaves are used to make bidis.</p>",
                    question_hi: "<p>37. बीड़ी बनाने के काम में आने वाले पत्तों की पहचान कीजिए।</p>",
                    options_en: [
                        "<p>Neem</p>",
                        "<p>Champa</p>",
                        "<p>Betel</p>",
                        "<p>Tendu</p>"
                    ],
                    options_hi: [
                        "<p>नीम</p>",
                        "<p>चंपा</p>",
                        "<p>पान</p>",
                        "<p>तेंदू</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>Tendu</strong> tree (Diospyros melanoxylon Roxb.) belonging to Family Ebenaceae, which is endemic to the Indian subcontinent. Bidi is an indigenous leaf-rolled cigarette made from coarse uncured tobacco, tied with a coloured string at one end.</p>",
                    solution_hi: "<p>37.(d) <strong>तेंदू</strong> वृक्ष (डायोस्पायरोस मेलानॉक्सीलॉन रॉक्सब.) एबेनेसी परिवार से संबंधित है, जो भारतीय उपमहाद्वीप में स्थानिक है। बीड़ी एक देशी पत्ती से लपेटी गई सिगरेट है जो मोटे, कच्चे तम्बाकू से बनाई जाती है, तथा एक सिरे पर रंगीन धागे से बंधी होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Each of the following statements includes two terms. In three cases, the two terms mean the same as each other. In which of the following cases do the two terms NOT mean the same as each other?</p>",
                    question_hi: "<p>38. निम्नलिखित में से प्रत्येक कथन में दो शब्द शामिल हैं। तीन मामलों में दो शब्दों का अर्थ एक-दूसरे के समान है। निम्नलिखित में से किस मामले में दो शब्दों का अर्थ एक-दूसरे के समान नहीं है?</p>",
                    options_en: [
                        "<p>Nominal GDP and GDP at current prices</p>",
                        "<p>The base period and the reference period</p>",
                        "<p>Changes in real GDP and the GDP deflator</p>",
                        "<p>Real GDP and GDP at constant prices</p>"
                    ],
                    options_hi: [
                        "<p>नॉमिनल जीडीपी और वर्तमान कीमतों पर जीडीपी</p>",
                        "<p>आधार अवधि और संदर्भ अवधि</p>",
                        "<p>वास्तविक जीडीपी और जीडीपी अपस्फीति&zwj;कारक में परिवर्तन</p>",
                        "<p>वास्तविक जीडीपी और स्थिर कीमतों पर जीडीपी</p>"
                    ],
                    solution_en: "<p>38.(c) <strong>Changes in real GDP and the GDP deflator. </strong>Gross Domestic Product (GDP) is the standard measure of value added through the production of goods and services in a country over a specific period. As a result, GDP also reflects the income generated from that production or the total expenditure on final goods and services (minus imports). GDP is calculated using the formula: GDP = Consumption + Investment + Government Spending + Net Exports.</p>",
                    solution_hi: "<p>38.(c) <strong>वास्तविक जीडीपी और जीडीपी अपस्फीति&zwj;कारक में परिवर्तन। </strong>सकल घरेलू उत्पाद (GDP) किसी देश में किसी विशिष्ट अवधि में वस्तुओं और सेवाओं के उत्पादन के माध्यम से जोड़े गए मूल्य का मानक माप है। सामान्यत:, GDP उस उत्पादन से उत्पन्न आय या अंतिम वस्तुओं और सेवाओं पर कुल व्यय (आयात को छोड़कर) को भी दर्शाता है। GDP की गणना इस सूत्र का उपयोग करके की जाती है: GDP = उपभोग + निवेश + सरकारी खर्च + शुद्ध निर्यात।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Who among the following was the first Sultan of Delhi to style himself as &lsquo;Zil-i-Ilahi&rsquo;?</p>",
                    question_hi: "<p>39. निम्नलिखित में से कौन-सा दिल्ली का पहला सुल्तान था जिसने खुद को \'जिल-ए-इलाही\' के रूप में पेश किया था?</p>",
                    options_en: [
                        "<p>Feroz Shah</p>",
                        "<p>Iltutmish</p>",
                        "<p>Balban</p>",
                        "<p>Qutbuddin Aibak</p>"
                    ],
                    options_hi: [
                        "<p>फिरोज़ शाह</p>",
                        "<p>इल्तुतमिश</p>",
                        "<p>बलबन</p>",
                        "<p>कुतुबुद्दीन एबक</p>"
                    ],
                    solution_en: "<p>39.(c) <strong>Balban. </strong>Ghiyas-ud-din Balban (ruled from 1266 AD to 1287 AD) was the first Muslim ruler to formulate the \'theory of kingship,\'akin to the \'theory of the divine right of kings.\' He established a strong centralized army, created the military department Diwan-i-Arz, and separated military affairs from the finance department (Diwan-i-Wazarat).</p>",
                    solution_hi: "<p>39.(c) <strong>बलबन।</strong> गियासुद्दीन बलबन (1266 ई. से 1287 ई. तक शासन किया) पहला मुस्लिम शासक था जिसने \'राजाओं के दैवीय अधिकार के सिद्धांत\' के समान \'राजत्व के सिद्धांत\' को प्रतिपादित किया। उन्होंने एक मजबूत केंद्रीकृत सेना की स्थापना की, सैन्य विभाग दीवान-ए-अर्ज़ बनाया और सैन्य मामलों को वित्त विभाग (दीवान-ए-वज़रात) से अलग कर दिया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. In which of the following games is the term \'Dribbling\' used?</p>",
                    question_hi: "<p>40. निम्नलिखित में से किस खेल में \'ड्रिबलिंग\' (Dribbling) शब्द का प्रयोग किया जाता है?</p>",
                    options_en: [
                        "<p>Basketball</p>",
                        "<p>Cricket</p>",
                        "<p>Baseball</p>",
                        "<p>Volleyball</p>"
                    ],
                    options_hi: [
                        "<p>बास्केटबॉल</p>",
                        "<p>क्रिकेट</p>",
                        "<p>बेसबॉल</p>",
                        "<p>वालीबॉल</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Basketball.</strong> Dribbling is the act of continuously bouncing the ball on the floor with one hand at a time. It\'s the only legal way for a player to maintain possession while walking or running. Other Key terms of Basketball: Assist, Block, Blocking, Boxing out, Center, Charging, Crossover, Defense, Dribbling, Dunk, Field goal, Forward, Free throw, Guard, Key, Offense, Pass, Rebound, Shot, Three-point-line, Traveling, and Turnover.</p>",
                    solution_hi: "<p>40.(a) <strong>बास्केटबॉल। </strong>ड्रिब्लिंग एक ऐसा कार्य है जिसमें एक बार में एक हाथ से गेंद को लगातार फर्श पर उछाला जाता है। यह खिलाड़ी के लिए चलते या दौड़ते समय गेंद पर कब्ज़ा बनाए रखने का एकमात्र वैध तरीका है। बास्केटबॉल के अन्य मुख्य शब्दावली : असिस्ट, ब्लॉक, ब्लॉकिंग, बॉक्सिंग आउट, सेंटर, चार्जिंग, क्रॉसओवर, डिफेंस, ड्रिब्लिंग, डंक, फील्ड गोल, फॉरवर्ड, फ्री थ्रो, गार्ड, की, ऑफेंस, पास, रिबाउंड, शॉट, थ्री-पॉइंट-लाइन, ट्रैवलिंग और टर्नओवर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following statements is/are correct about the famous place Mawsynram? <br>A. It is the wettest place on the earth. <br>B. It is located in Meghalaya state of India.</p>",
                    question_hi: "<p>41. निम्नलिखित में से कौन-सा/कौन-से कथन प्रसिद्ध स्&zwj;थल मासिनराम (Mawsynram) के संबंध में सही है/हैं? <br>A. यह पृथ्वी पर सबसे आर्द्र स्थान (wettest place) है। <br>B. यह भारत के मेघालय राज्य में स्थित है।</p>",
                    options_en: [
                        "<p>Only A is correct</p>",
                        "<p>Neither A nor B is correct.</p>",
                        "<p>Only B is correct</p>",
                        "<p>A and B both are correct</p>"
                    ],
                    options_hi: [
                        "<p>केवल A सही है।</p>",
                        "<p>न तो A और न ही B सही है।</p>",
                        "<p>केवल B सही है।</p>",
                        "<p>A और B दोनों सही हैं।</p>"
                    ],
                    solution_en: "<p>41.(d) <strong>A and B both are correct.</strong> The south-west monsoon drastically changes India&rsquo;s weather, bringing heavy rains to the windward side of the Western Ghats (over 250 cm) and moderate rain to the Deccan Plateau. Mawsynram in Meghalaya records the world&rsquo;s highest average rainfall. Rain decreases from east to west in the Ganga valley, with Rajasthan and parts of Gujarat receiving scanty rainfall.</p>",
                    solution_hi: "<p>41.(d) <strong>A और B दोनों सही हैं। </strong>दक्षिण-पश्चिम मानसून भारत के मौसम में व्यापक परिवर्तन लाता है, जिससे पश्चिमी घाट के पवन-पक्षी भाग (250 सेमी से अधिक) में अधिक वर्षा होती है और दक्कन के पठार पर मध्यम वर्षा होती है। मेघालय के मासिनराम में विश्व की सबसे अधिक औसत वर्षा दर्ज की गई है। गंगा घाटी में पूर्व से पश्चिम की ओर वर्षा में कमी होती है, जबकि राजस्थान और गुजरात के कुछ हिस्सों में बहुत कम वर्षा होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. What is the full form of &lsquo;PM-SYM&rsquo;?</p>",
                    question_hi: "<p>42. &lsquo;PM-SYM&rsquo; का पूर्ण रूप क्या है?</p>",
                    options_en: [
                        "<p>Pradhan Mantri Sanyukt Yojana Man-dhyan</p>",
                        "<p>Pradhan Mantri MukhiyaSukrasha Yojana</p>",
                        "<p>Pradhan Mantri Shram Yogi Maan-dhan</p>",
                        "<p>Pradhan Mantri Shrestha Yojana Mulyankan</p>"
                    ],
                    options_hi: [
                        "<p>Pradhan Mantri Sanyukt Yojana Man-dhyan (प्रधान मंत्री संयुक्त योजना मन-ध्यान)</p>",
                        "<p>Pradhan Mantri MukhiyaSukrasha Yojana (प्रधान मंत्री मुखिया सुरक्षा योजना)</p>",
                        "<p>Pradhan Mantri Shram Yogi Maan-dhan (प्रधान मंत्री श्रम योगी मान-धन)</p>",
                        "<p>Pradhan Mantri Shrestha Yojana Mulyankan (प्रधान मंत्री श्रेष्ठ योजना मूल्यांकन)</p>"
                    ],
                    solution_en: "<p>42.(c) <strong>Pradhan Mantri Shram Yogi Maan-Dhan:</strong> It is a Central Sector Scheme announced in the Interim Budget of 2019. Nodal Ministry - Ministry of Labour and Employment. Aim - To provide social security and old age protection to unorganized workers in India. A minimum monthly pension of Rs 3,000 after the beneficiary turns 60 years old.</p>",
                    solution_hi: "<p>42.(c) <strong>Pradhan Mantri Shram Yogi Maan-dhan (प्रधान मंत्री श्रम योगी मान-धन) :</strong> यह 2019 के अंतरिम बजट में घोषित एक केंद्रीय क्षेत्र की योजना है। नोडल मंत्रालय - श्रम और रोजगार मंत्रालय। उद्देश्य - भारत में असंगठित श्रमिकों को सामाजिक सुरक्षा और वृद्धावस्था सुरक्षा प्रदान करना। इसमें लाभार्थी की आयु 60 वर्ष हो जाने के बाद उसे न्यूनतम 3,000 रुपये मासिक पेंशन मिलेगी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. With reference to medieval Indian architecture style, what is &lsquo;Pietra Dura&rsquo;?</p>",
                    question_hi: "<p>43. मध्यकालीन भारतीय वास्तुकला शैली के संदर्भ में, \'पच्चीकारी (Pietra Dura)\' क्या है?</p>",
                    options_en: [
                        "<p>Sculpture art of the Mauryan period</p>",
                        "<p>Oil paintings on the walls of Taj Mahal</p>",
                        "<p>Fine quality clothes used by the Mughals</p>",
                        "<p>Decorating the walls with floral designs made of semi-precious stones</p>"
                    ],
                    options_hi: [
                        "<p>मौर्य काल की मूर्तिशिल्प कला</p>",
                        "<p>ताज महल की दीवारों पर तैल चित्रण (Oil paintings)</p>",
                        "<p>मुगलों द्वारा उपयोग किए जाने वाले उत्तम गुणवत्ता के वस्त्र</p>",
                        "<p>दीवारों को उपरत्नों (semi-precious stones) से बने हुए पुष्प डिजाइनों से सजाना</p>"
                    ],
                    solution_en: "<p>43.(d) Pietra Dura is a decorative art style involving inlaying semi-precious stones into marble to create intricate floral or geometric designs. It is famously used in Mughal architecture, especially the Taj Mahal, along with arabesque, calligraphy, relief carving, and jalis. Lapis lazuli was often used in interiors and canopies, adding to the elegance of the structures.</p>",
                    solution_hi: "<p>43.(d) पच्चीकारी एक सजावटी कला शैली है जिसमें जटिल पुष्प या ज्यामितीय डिजाइन बनाने के लिए संगमरमर में उपरत्नों को जड़ा जाता है। इसका प्रसिद्ध रूप से मुगल वास्तुकला, विशेष रूप से ताजमहल, अरबी, सुलेख, राहत नक्काशी और जालियों में उपयोग किया जाता है। लाजवर्त का उपयोग अक्सर अंदरूनी हिस्सों और छतरियों में किया जाता था, जिससे संरचनाओं की सुंदरता बढ़ जाती थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Vasudeva I who issued coins in gold (dinars and quarter dinars) and copper (single denomination) was a _________king.</p>",
                    question_hi: "<p>44. सोने (दीनार और चौथाई दीनार) और तांबे (एकल मूल्यवर्ग) में सिक्के जारी करने वाला, वासुदेव प्रथम, एक _________शासक था।</p>",
                    options_en: [
                        "<p>Shunga</p>",
                        "<p>Vakataka</p>",
                        "<p>Shaka</p>",
                        "<p>Kushana</p>"
                    ],
                    options_hi: [
                        "<p>शुंग</p>",
                        "<p>वाकाटक</p>",
                        "<p>शक</p>",
                        "<p>कुषाण</p>"
                    ],
                    solution_en: "<p>44.(d)<strong> Kushana</strong>. Vasudeva I was the last of the \"Great Kushans.\" He ruled in Northern India and Central Asia, where he minted coins in the city of Balkh. The Kushana dynasty was founded by Kujula Kadphises.</p>",
                    solution_hi: "<p>44.(d) <strong>कुषाण। </strong>वासुदेव प्रथम \"महान कुषाणों\" में से अंतिम शासक थे। उन्होंने उत्तरी भारत और मध्य एशिया में शासन किया था, जहाँ उन्होंने बल्ख (बैक्ट्रिया) शहर में सिक्के ढाले। कुषाण राजवंश की स्थापना कुजुल कडफिसेस ने की थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which is an advanced weather satellite of India configured with improved imaging system and atmospheric sounder ?</p>",
                    question_hi: "<p>45. भारत का कौन-सा एक ऐसा उन्नत मौसम उपग्रह है जिसे उन्नत प्रतिबिंबन प्रणाली (इमेजिंग सिस्टम) और वायुमंडलीय ध्वनित्र (साउंडर) के साथ तैयार किया गया है ?</p>",
                    options_en: [
                        "<p>GSAT-7A</p>",
                        "<p>INSAT -3D</p>",
                        "<p>RS-D1</p>",
                        "<p>SROSS-2</p>"
                    ],
                    options_hi: [
                        "<p>GSAT-7A</p>",
                        "<p>INSAT -3D</p>",
                        "<p>RS-D1</p>",
                        "<p>SROSS-2</p>"
                    ],
                    solution_en: "<p>45.(b)<strong> INSAT - 3D: </strong>Launched - 2013; Launch Vehicle - Ariane-5 VA-214. Important Satellites: SARAL (2013): Climate &amp; Environment, Earth Observation. RISAT-1 (2012): Earth Observation. RISAT-2 (2009): Earth Observation. Bhaskara-I (1979): Earth Observation, Experimental. GSAT-7A (2018): Communication. Rohini Satellite RS-D1 (1981): Earth Observation.</p>",
                    solution_hi: "<p>45.(b) <strong>INSAT - 3D: </strong>लॉन्च - 2013, प्रक्षेपण यान - एरियन-5 VA-214 । महत्वपूर्ण उपग्रह : SARAL (2013) - जलवायु और पर्यावरण, पृथ्वी अवलोकन। RISAT-1 (2012): पृथ्वी अवलोकन। RISAT-2 (2009): पृथ्वी अवलोकन। भास्कर-I (1979): पृथ्वी अवलोकन, प्रायोगिक। GSAT-7A (2018): संचार। रोहिणी उपग्रह RS-D1 (1981) : पृथ्वी अवलोकन।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who was the Indian Buddhist monk who is credited with the development of many ancient martial art forms in the 5th century?</p>",
                    question_hi: "<p>46. वह भारतीय बौद्ध भिक्षु कौन थे जिन्हें 5वीं शताब्दी में कई प्राचीन मार्शल आर्टशैलियों के विकास का श्रेय दिया जाता है?</p>",
                    options_en: [
                        "<p>Bodhidharma</p>",
                        "<p>Paramartha</p>",
                        "<p>Atisa</p>",
                        "<p>Bodhiruchi</p>"
                    ],
                    options_hi: [
                        "<p>बोधिधर्म (Bodhidharma)</p>",
                        "<p>परमार्था (Paramartha)</p>",
                        "<p>अतिसा (Atisa)</p>",
                        "<p>बोधिरुचि (Bodhiruchi)</p>"
                    ],
                    solution_en: "<p>46.(a) <strong>Bodhidharma</strong>. He was a semi-legendary Buddhist monk who lived during the 5th or 6th century CE. In Indian philosophy, he is commonly considered the founder of Chan Buddhism in China. It was later known as Zen in Japan. Bodhidharma is also credited with founding the famous Shaolin school of Chinese martial arts. He is known as a Tripitaka Dharma Master.</p>",
                    solution_hi: "<p>46.(a) <strong>बोधिधर्म। </strong>वे एक अर्ध-पौराणिक बौद्ध भिक्षु थे जो 5वीं या 6वीं शताब्दी ई.पू. में रहते थे। भारतीय दर्शन में, उन्हें आमतौर पर चीन में चैन बौद्ध धर्म का संस्थापक माना जाता है। इसे बाद में जापान में ज़ेन के नाम से जाना गया। बोधिधर्म को चीनी मार्शल आर्ट के प्रसिद्ध शाओलिन स्कूल की स्थापना का श्रेय भी दिया जाता है। उन्हें त्रिपिटक धर्म गुरु के रूप में जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. The Election Laws Amendment Bill passed in the Parliament recently seeks to implement certain electoral reforms. One of them is the eligibility date. Till now, only one date &ndash; 1 January was considered for deciding eligibility with respect to age. What reform does the Bill seek in this regard?</p>",
                    question_hi: "<p>47. हाल ही में संसद में पारित चुनाव कानून संशोधन विधेयक का उद्देश्य कुछ चुनावी सुधारों को लागू करना है। उनमें से एक पात्रता तिथि है। अब तक, केवल एक तिथि - 1 जनवरी को आयु के संबंध में पात्रता तय करने के लिए माना जाता था। इस संबंध में विधेयक क्या सुधार चाहता है?</p>",
                    options_en: [
                        "<p>Introducing two dates &ndash; 1 January and 1 June</p>",
                        "<p>Eliminating the idea of eligibility date &ndash; enrolment throughout the year</p>",
                        "<p>Introducing four dates &ndash; 1 January, 1 April, 1 July, 1 October</p>",
                        "<p>Introducing three dates &ndash; 1 January, 1 May, 1 September</p>"
                    ],
                    options_hi: [
                        "<p>दो तिथियां &ndash; 1 जनवरी और 1 जून लागू करना</p>",
                        "<p>वर्ष भर पात्रता तिथि &ndash; नामांकन के विचार को समाप्त करना</p>",
                        "<p>चार तिथियां &ndash; 1 जनवरी, 1 अप्रैल, 1 जुलाई, 1 अक्टूबर लागू करना</p>",
                        "<p>तीन तिथियां &ndash; 1 जनवरी, 1 मई, 1 सितंबर लागू करना</p>"
                    ],
                    solution_en: "<p>47.(c) The Election Laws (Amendment) Bill, 2021 was introduced in the Lok Sabha on December 20, 2021. The Bill amends the Representation of the People Act, 1950 and the Representation of the People Act, 1951 to implement certain electoral reforms. The 1950 Act provides for allocation of seats and delimitation of constituencies for elections, qualifications of voters, and preparation of electoral rolls. The 1951 Act provides for the conduct of elections, and offences and disputes related to elections.</p>",
                    solution_hi: "<p>47.(c) 2021 का चुनाव कानून (संशोधन) विधेयक , 20 दिसंबर, 2021 को लोकसभा में पेश किया गया था। यह विधेयक कुछ चुनावी सुधारों को लागू करने के लिए जनप्रतिनिधित्व अधिनियम, 1950 और जनप्रतिनिधित्व अधिनियम, 1951 में संशोधन करता है। 1950 का अधिनियम चुनावों के लिए सीटों के आवंटन और निर्वाचन क्षेत्रों के परिसीमन, मतदाताओं की योग्यता और मतदाता सूची तैयार करने का प्रावधान करता है। 1951 का अधिनियम चुनावों के संचालन और चुनावों से संबंधित अपराधों और विवादों का प्रावधान करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which function is used to calculate the maximum value in a selected column in MS Excel?</p>",
                    question_hi: "<p>48. MS एक्सेल में चयनित कॉलम में अधिकतम मान की गणना करने के लिए इनमें से किस फंक्शन का उपयोग किया जाता है?</p>",
                    options_en: [
                        "<p>Auto max</p>",
                        "<p>Max</p>",
                        "<p>Auto high</p>",
                        "<p>High</p>"
                    ],
                    options_hi: [
                        "<p>ऑटो मैक्स (Auto max)</p>",
                        "<p>मैक्स (Max)</p>",
                        "<p>ऑटो हाई (Auto high)</p>",
                        "<p>हाई (High)</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>Max</strong>. Excel Formulas and Functions: The SUM() function, as the name suggests, gives the total of the selected range of cell values. It performs the mathematical operation which is addition. The AVERAGE() function focuses on calculating the average of the selected range of cell values. The function COUNT() counts the total number of cells in a range that contains a number. The SUBTOTAL() function returns the subtotal in a database. The MOD() function works on returning the remainder when a particular number is divided by a divisor.</p>",
                    solution_hi: "<p>48.(b) <strong>मैक्स (Max)।</strong> एक्सेल सूत्र और फ़ंक्शन: SUM() फ़ंक्शन, जैसा कि नाम से पता चलता है, सेल मानों की सेलेक्ट रेंज का योग प्रदान करता है। यह गणितीय संक्रिया को निष्पादित करता है जो कि जोड़ है। AVERAGE() फ़ंक्शन सेल मानों की सेलेक्ट रेंज के औसत की गणना करने पर केंद्रित है। फ़ंक्शन COUNT() किसी संख्या वाली श्रेणी में कुल सेलों की संख्या की गणना करता है। SUBTOTAL () फ़ंक्शन रिटर्न द सब-टोटल इन ए डेटाबेस। MOD() फ़ंक्शन किसी विशेष संख्या को भाजक से विभाजित करने पर शेष राशि रिटर्न करने पर कार्य करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Where is the Kailasa temple built by Rashtrakutas located ?</p>",
                    question_hi: "<p>49. राष्ट्रकूटों द्वारा बनवाया गया कैलाश मंदिर कहाँ स्थित है?</p>",
                    options_en: [
                        "<p>Amravati</p>",
                        "<p>Hampi</p>",
                        "<p>Ellora</p>",
                        "<p>Kanchi</p>"
                    ],
                    options_hi: [
                        "<p>अमरावती</p>",
                        "<p>हम्पी</p>",
                        "<p>एलोरा</p>",
                        "<p>कांची</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>Ellora. </strong>The Ellora Caves are a UNESCO World Heritage Site (1983) in Aurangabad district, Maharashtra (now renamed to Chhatrapati Sambhaji Nagar district). It is one of the largest rock-cut Hindu temple cave complexes in the world. Kailashnath Temple also known as Kailash Temple is a famous temple located in Ellora, Maharashtra. Its construction is generally attributed to the eighth-century Rashtrakuta king Krishna I (756 &ndash; 773). It is a Hindu temple in the Dravidian architectural style. It is dedicated to Lord Shiva and is known for its historical importance.</p>",
                    solution_hi: "<p>49.(c) <strong>एलोरा।</strong> एलोरा की गुफाएँ महाराष्ट्र के औरंगाबाद जिले (अब इसका नाम बदलकर छत्रपति संभाजी नगर जिला कर दिया गया है) में स्थित UNESCO विश्व धरोहर स्थल (1983) हैं। यह दुनिया के सबसे बड़े रॉक-कट हिंदू मंदिर गुफा परिसरों में से एक है। कैलाशनाथ मंदिर जिसे कैलाश मंदिर के नाम से भी जाना जाता है, महाराष्ट्र के एलोरा में स्थित एक प्रसिद्ध मंदिर है। इसके निर्माण का श्रेय आम तौर पर आठवीं शताब्दी के राष्ट्रकूट राजा कृष्ण प्रथम (756 - 773) को दिया जाता है। यह द्रविड़ स्थापत्य शैली में बना एक हिंदू मंदिर है। यह भगवान शिव को समर्पित है और यह अपने ऐतिहासिक महत्व के लिए जाना जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which Article mentions the Comptroller and Auditor General of India?</p>",
                    question_hi: "<p>50. किस अनुच्छेद में भारत के नियंत्रक एवं महालेखा परीक्षक का उल्लेख है?</p>",
                    options_en: [
                        "<p>Article 154</p>",
                        "<p>Article 156</p>",
                        "<p>Article 148</p>",
                        "<p>Article 136</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 154</p>",
                        "<p>अनुच्छेद 156</p>",
                        "<p>अनुच्छेद 148</p>",
                        "<p>अनुच्छेद 136</p>"
                    ],
                    solution_en: "<p>50.(c) <strong>Article 148. </strong>The Comptroller and Auditor General (CAG) audits the accounts of Union and state governments, ensuring proper use of public funds, and reports to the President. Article 154 - Deals with the executive power of states. Article 156 - Term of office of Governor. Article 136 - Special leave to appeal by the Supreme Court.</p>",
                    solution_hi: "<p>50.(c) <strong>अनुच्छेद 148</strong>. नियंत्रक एवं महालेखा परीक्षक (CAG) संघ और राज्य सरकारों के खातों का लेखा-जोखा करता है, सार्वजनिक धन का उचित उपयोग सुनिश्चित करता है, और राष्ट्रपति को रिपोर्ट करता है। अनुच्छेद 154 - राज्यों की कार्यकारी शक्ति से संबंधित है। अनुच्छेद 156 - राज्यपाल का कार्यकाल। अनुच्छेद 136 - सर्वोच्च न्यायालय द्वारा अपील की विशेष अनुमति।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Simplify the following expression.<br>(3x&nbsp;+ 17 - 2y)(3x - 17 - 2y)</p>",
                    question_hi: "<p>51. निम्नलिखित व्यंजक को हल कीजिए।<br>(3x&nbsp;+ 17 - 2y)(3x - 17 - 2y)</p>",
                    options_en: [
                        "<p>9x&sup2; - 12xy + 4y&sup2; + 289</p>",
                        "<p>9x&sup2;&nbsp;+ 12xy + 4y&sup2; - 289</p>",
                        "<p>9x&sup2; - 12xy + 4y&sup2; - 289</p>",
                        "<p>9x&sup2; - 6xy + 4y&sup2; - 289</p>"
                    ],
                    options_hi: [
                        "<p>9x&sup2; - 12xy + 4y&sup2; + 289</p>",
                        "<p>9x&sup2; + 12xy + 4y&sup2; - 289</p>",
                        "<p>9x&sup2; - 12xy + 4y&sup2; - 289</p>",
                        "<p>9x&sup2; - 6xy + 4y&sup2; - 289</p>"
                    ],
                    solution_en: "<p>51.(c) (3x&nbsp;+ 17 - 2y)(3x - 17 - 2y)<br>After rearranging we can write it, <br>= [(3x&nbsp;- 2y) +17 ] [ ( 3x - 2y) - 17)<br>= (a + b )(a - b) = a&sup2; - b&sup2;&nbsp;<br>= (3x - 2y)&sup2; - 17&sup2;&nbsp;<br>= 9x&sup2;&nbsp; - 12xy + 4y&sup2;&nbsp; - 289</p>",
                    solution_hi: "<p>51.(c) (3<math display=\"inline\"><mi>x</mi></math> + 17 - 2y)(3x - 17 - 2y)<br>पुनर्व्यवस्थित करने के बाद हम इसे लिख सकते हैं, <br>= [(3x&nbsp;- 2y) +17 ] [ ( 3x - 2y) - 17)<br>= (a + b )(a - b) = a&sup2; - b&sup2;&nbsp;<br>= (3x - 2y)&sup2; - 17&sup2;&nbsp;<br>= 9x&sup2;&nbsp; - 12xy + 4y&sup2;&nbsp; - 289</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Simplify [1- sin&sup2;32&deg;+&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math>].</p>",
                    question_hi: "<p>52. [1- sin&sup2;32&deg;+ <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math>].को सरल कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>sin32&deg;</p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>sin32&deg;</p>",
                        "<p>1</p>",
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>52.(c) [1- sin&sup2;32&deg;+&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math>]<br>= <math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>32</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math> [ ∵ 1 - sin&sup2;A = cos&sup2;A, 1 + tan&sup2;A = sec&sup2;A]<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>2</mn></msup><mn>32</mn><mo>&#176;</mo></math> + cos &sup2;58&deg;&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>2</mn></msup><mn>32</mn><mo>&#176;</mo></math> + Sin &sup2;32&deg;&nbsp;<br>= 1</p>",
                    solution_hi: "<p>52.(c) [1- sin&sup2;32&deg;+ <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math>]<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>2</mn></msup><mn>32</mn><mo>&#176;</mo><mo>+</mo><mfrac><mn>1</mn><mrow><msup><mi>sec</mi><mn>2</mn></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math> [ ∵ 1 - sin&sup2;A = cos&sup2;A, 1 + tan&sup2;A = sec&sup2;A]<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>2</mn></msup><mn>32</mn><mo>&#176;</mo></math> + cos &sup2;58&deg;&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>2</mn></msup><mn>32</mn><mo>&#176;</mo></math> + Sin &sup2;32&deg;&nbsp;<br>= 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. On dividing a certain number by 459, we get 19 as remainder. What will be the remainder, when the same number is divided by 17?</p>",
                    question_hi: "<p>53. एक निश्चित संख्या को 459 से विभाजित करने पर, हमें शेषफल के रूप में 19 प्राप्त होता है। उसी संख्या को 17 से विभाजित करने पर शेषफल क्या होगा?</p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>1</p>",
                        "<p>13</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>1</p>",
                        "<p>13</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>53.(d) <br>Let divisor be x.<br>Then, no will be (459<math display=\"inline\"><mi>x</mi></math> + 19)<br>Now, remainder(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>459</mn><mi>x</mi><mo>+</mo><mn>19</mn></mrow><mn>17</mn></mfrac></math>) = rem(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>19</mn><mn>17</mn></mfrac></mstyle></math>) = 2</p>",
                    solution_hi: "<p>53.(d) <br>माना भाजक x&nbsp;है।<br>तो, संख्या होगी (459x&nbsp;+ 19)<br>अब, शेषफल (<math display=\"inline\"><mfrac><mrow><mn>459</mn><mi>x</mi><mo>+</mo><mn>19</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>) = शेषफल (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>19</mn><mn>17</mn></mfrac></mstyle></math>) = 2</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. There are two circles touching each other externally. The radius of the first circle with centre O is 12 cm. The radius of the second circle with centre A is 5 cm. Find the length of their common tangent touching the two circles at points P and Q .</p>",
                    question_hi: "<p>54. दो वृत्त हैं जो एक दूसरे को बाह्य रूप से स्पर्श करते हैं। केंद्र O वाले पहले वृत्त की त्रिज्या 12 cm है। केंद्र A वाले दूसरे वृत्त की त्रिज्या 5 cm है। दोनों वृत्तों को बिंदु P और Q पर स्पर्श करने वाली उनकी उभयनिष्ठ स्पर्श रेखा की लंबाई ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>5<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>",
                        "<p>14<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> cm</p>",
                        "<p>4 <math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> cm</p>",
                        "<p>5<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math> cm</p>"
                    ],
                    options_hi: [
                        "<p>5<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>",
                        "<p>14<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> cm</p>",
                        "<p>4 <math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> cm</p>",
                        "<p>5<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math> cm</p>"
                    ],
                    solution_en: "<p>54.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506689.png\" alt=\"rId51\" width=\"209\" height=\"135\"><br>Length of common tangent (<math display=\"inline\"><mi>l</mi></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><msub><mi>r</mi><mn>1</mn></msub><msub><mi>r</mi><mn>2</mn></msub></msqrt></math><br>= <math display=\"inline\"><mn>2</mn><msqrt><mn>12</mn><mo>&#215;</mo><mn>5</mn></msqrt></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math>cm</p>",
                    solution_hi: "<p>54.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506689.png\" alt=\"rId51\" width=\"209\" height=\"135\"><br>उभयनिष्ठ स्पर्शरेखा की लंबाई (<math display=\"inline\"><mi>l</mi></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><msqrt><msub><mi>r</mi><mn>1</mn></msub><msub><mi>r</mi><mn>2</mn></msub></msqrt></math><br>= <math display=\"inline\"><mn>2</mn><msqrt><mn>12</mn><mo>&#215;</mo><mn>5</mn></msqrt></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math> cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The given pie-diagram shows the expenditure incurred on the preparation of a book by a publisher, under various heads. Study the pie-diagram and answer the question that follows.<br>Various Expenditures (in percentage) incurred in Publishing a Book<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506788.png\" alt=\"rId52\" width=\"304\" height=\"189\"> <br>The marked price of a book is 20% more than the CP. If the marked price of the book is ₹30, then what is the cost of paper used in a single copy of the book ?</p>",
                    question_hi: "<p>55. दिया गया पाई-आरेख एक प्रकाशक द्वारा पुस्तक तैयार करने के लिए विभिन्न मदों के अंतर्गत किए गए व्यय को दर्शाता है। पाई-आरेख का अध्ययन कीजिए और नीचे दिए गए प्रश्नो के उत्तर दीजिए।<br>किसी पुस्तक के प्रकाशन में होने वाले विभिन्न व्यय (प्रतिशत में)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972506920.png\" alt=\"rId53\" width=\"331\" height=\"200\"> <br>एक पुस्तक का अंकित मूल्य, लागत मूल्य (CP) से 20% अधिक है। यदि पुस्तक का अंकित मूल्य ₹30 है, तो पुस्तक की एक प्रति में प्रयुक्त कागज की लागत क्या है ?</p>",
                    options_en: [
                        "<p>₹6.50</p>",
                        "<p>₹4.50</p>",
                        "<p>₹5</p>",
                        "<p>₹6</p>"
                    ],
                    options_hi: [
                        "<p>₹6.50</p>",
                        "<p>₹4.50</p>",
                        "<p>₹5</p>",
                        "<p>₹6</p>"
                    ],
                    solution_en: "<p>55.(c)<br>Ratio - CP&nbsp; :&nbsp; MP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp;:&nbsp; &nbsp;6<br>(MP) 6 units = ₹ 30<br>(CP) 5 units = ₹ 25<br>cost of paper used in a single copy of the book = 25 &times; 20% = ₹ 5</p>",
                    solution_hi: "<p>55.(c)<br>अनुपात - क्रय मूल्य&nbsp; :&nbsp; अंकित मूल्य<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;6<br>(अंकित मूल्य) 6 इकाई= ₹ 30<br>(क्रय मूल्य) 5 इकाई = ₹ 25<br>पुस्तक की एक प्रति में प्रयुक्त कागज की लागत = 25 &times; 20% = ₹ 5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A seller sold 19 cookies for ₹95 instead of 20 cookies in one kg, cheating a customer. What is the gain (in ₹) if the seller bought the cookies for ₹76?</p>",
                    question_hi: "<p>56. एक विक्रेता ने एक ग्राहक को धोखा देकर एक Kg में 20 कुकीज के बजाय 19 कुकीज देकर ₹95 में बेच दीं। यदि विक्रेता ने ₹76 में कुकीज़ खरीदीं, तो लाभ (₹ में) क्या होगा?</p>",
                    options_en: [
                        "<p>28.2</p>",
                        "<p>26.2</p>",
                        "<p>22.8</p>",
                        "<p>25.8</p>"
                    ],
                    options_hi: [
                        "<p>28.2</p>",
                        "<p>26.2</p>",
                        "<p>22.8</p>",
                        "<p>25.8</p>"
                    ],
                    solution_en: "<p>56.(c) <br>CP of 20 cookies for seller = <math display=\"inline\"><mi>&#8377;</mi></math>76<br>CP of 1 cookies for seller = <math display=\"inline\"><mi>&#8377;</mi></math>3.8<br>SP of 19 cookies for seller = <math display=\"inline\"><mi>&#8377;</mi></math>95<br>Profit = 95 - 76 = <math display=\"inline\"><mi>&#8377;</mi></math>19<br>Total profit = 19 + 3.8 (remaining 1 cookies price ) = <math display=\"inline\"><mi>&#8377;</mi></math>22.8</p>",
                    solution_hi: "<p>56.(c) <br>विक्रेता के लिए 20 कुकीज़ का क्रय० मू० = ₹76<br>विक्रेता के लिए 1 कुकीज़ का क्रय० मू० = ₹3.8<br>विक्रेता के लिए 19 कुकीज़ का वि० मू० = ₹95<br>लाभ = 95 - 76 = ₹19<br>कुल लाभ = 19 + 3.8 (शेष 1 कुकीज़ की कीमत) = ₹22.8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If the brick size is 25 cm &times; 12 cm &times; 9 cm, then how many bricks are required to construct a wall of length 10 m, breadth 22.5 cm and height 6 m?</p>",
                    question_hi: "<p>57. यदि ईंट का आकार 25 cm &times; 12 cm &times; 9 cm है, तो 10 m लंबी, 22.5 cm चौड़ी और 6 m ऊंची दीवार&nbsp;बनाने के लिए कितनी ईंटों की आवश्यकता होगी?</p>",
                    options_en: [
                        "<p>4500</p>",
                        "<p>6000</p>",
                        "<p>8000</p>",
                        "<p>5000</p>"
                    ],
                    options_hi: [
                        "<p>4500</p>",
                        "<p>6000</p>",
                        "<p>8000</p>",
                        "<p>5000</p>"
                    ],
                    solution_en: "<p>57.(d)<br>Required bricks = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>22</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mo>(</mo><mn>6</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn><mo>)</mo></mrow><mrow><mn>25</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>9</mn></mrow></mfrac></math> = 40 &times; 2.5 &times; 50 = 5000</p>",
                    solution_hi: "<p>57.(d)<br>आवश्यक ईंटें = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>22</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mo>(</mo><mn>6</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn><mo>)</mo></mrow><mrow><mn>25</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>9</mn></mrow></mfrac></math> = 40 &times; 2.5 &times; 50 = 5000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Simplify the following expression:<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math></p>",
                    question_hi: "<p>58. निम्नलिखित व्यंजक को सरल कीजिए:<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math></p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>58.(b)<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup></mrow><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>+</mo><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>0</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup><mi>&#160;</mi></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo><mi>&#160;</mi></mrow><mrow><mn>3</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>4</mn><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>17</mn><mi>&#160;</mi></mrow><mn>15</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>58.(b)<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup></mrow><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>+</mo><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo><mi>&#160;</mi><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>0</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup><mi>&#160;</mi></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>2</mn><mo>+</mo><mn>0</mn><mo>.</mo><mn>2</mn><mo>)</mo><mi>&#160;</mi></mrow><mrow><mn>3</mn><mo>.</mo><mn>2</mn><mo>-</mo><mn>0</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>4</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>17</mn><mi>&#160;</mi></mrow><mn>15</mn></mfrac></mstyle></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. If 2 tan&theta; = 3, then <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi></mrow><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> is equal to:</p>",
                    question_hi: "<p>59. यदि 2 tan&theta; = 3 है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>59.(d)<strong> Given: </strong>2 tan&theta; = 3 <br>then <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle></math> &rArr; sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mn>2</mn></mfrac></mstyle></math>cos&theta;<br>Now,<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi></mrow><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi></mrow><mrow><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mfrac><mrow><mn>9</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>9</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mn>13</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi></mrow><mn>13</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>59.(d) दिया गया है : 2 tan&theta; = 3 <br>तब <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mn>2</mn></mfrac></mstyle></math> &rArr; sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi></mrow><mn>2</mn></mfrac></mstyle></math>cos&theta;</p>\n<p>अब,<br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi></mrow><mrow><mn>3</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#160;</mo><mo>&#215;</mo><mi>&#160;</mi><mfrac><mn>3</mn><mn>2</mn></mfrac><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi></mrow><mrow><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mn>3</mn><mn>2</mn></mfrac><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mfrac><mrow><mn>9</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>9</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mn>13</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>5</mn><mn>13</mn></mfrac></mstyle></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. An observer 1.5 m tall is 24.5 m away from a 26 m high tower. The angle of elevation of the top of the tower from the eye of the observer is:</p>",
                    question_hi: "<p>60. 1.5 मीटर लंबा एक पर्यवेक्षक 26 मीटर ऊंचे टावर से 24.5 मीटर दूर है। प्रेक्षक की आँख से मीनार के शीर्ष का उन्नयन कोण है:</p>",
                    options_en: [
                        "<p>60&deg;</p>",
                        "<p>30&deg;</p>",
                        "<p>45&deg;</p>",
                        "<p>75&deg;</p>"
                    ],
                    options_hi: [
                        "<p>60&deg;</p>",
                        "<p>30&deg;</p>",
                        "<p>45&deg;</p>",
                        "<p>75&deg;</p>"
                    ],
                    solution_en: "<p>60.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972507024.png\" alt=\"rId54\" width=\"236\" height=\"110\"><br>Given , AD = 26m, BC = 24.5 m, CE = 1.5 m <br>In triangle ABC <br>AB = 24.5 m , BC = 24. 5 m <br>tan<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>p</mi><mi>&#160;</mi></mrow><mi>b</mi></mfrac></mstyle></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>24</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi></mrow><mrow><mn>24</mn><mo>.</mo><mn>5</mn></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>tan&theta;= 1 <br><math display=\"inline\"><mo>&#8658;</mo></math>tan&theta; = tan 45&deg; <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math> = 45&deg;</p>",
                    solution_hi: "<p>60.(c)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972507024.png\" alt=\"rId54\" width=\"236\" height=\"110\"><br>दिया गया है, AD = 26m, BC = 24.5 m, CE = 1.5 m<br>त्रिभुज ABC में,<br>AB = 24.5 m , BC = 24. 5 m <br>tan<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>p</mi><mi>&#160;</mi></mrow><mi>b</mi></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>24</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi></mrow><mrow><mn>24</mn><mo>.</mo><mn>5</mn></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>tan&theta;= 1 <br><math display=\"inline\"><mo>&#8658;</mo></math>tan&theta; = tan 45&deg; <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#952;</mi></math> = 45&deg;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. If tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>, then find the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>7</mn><mi>sin</mi><mi>A</mi><mo>-</mo><mn>3</mn><mi>cos</mi><mi>A</mi><mi>&#160;</mi></mrow><mrow><mn>7</mn><mi>sin</mi><mi>A</mi><mo>+</mo><mn>3</mn><mi>cos</mi><mi>A</mi></mrow></mfrac></mstyle></math> + 4.</p>",
                    question_hi: "<p>61. यदि tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> है, तो&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>7</mn><mi>sin</mi><mi>A</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mi>cos</mi><mi>A</mi></mrow><mrow><mn>7</mn><mi>sin</mi><mi>A</mi><mo>+</mo><mo>&#160;</mo><mn>3</mn><mi>cos</mi><mi>A</mi></mrow></mfrac></mstyle></math> + 4 का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>61.(a)<br>tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>Now,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sinA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi>cosA</mi></mrow><mrow><mn>7</mn><mi>sinA</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi>cosA</mi></mrow></mfrac></math> + 4<br>On dividing by cosA&nbsp;in fraction we get,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn></mrow><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + 4 = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>4</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>61.(a)<br>tanA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>अब,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>sinA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi>cosA</mi></mrow><mrow><mn>7</mn><mi>sinA</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi>cosA</mi></mrow></mfrac></math> + 4<br>भिन्न में cosA&nbsp;से भाग देने पर हमें प्राप्त होता है, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn></mrow><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">A</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>5</mn><mn>7</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn></mrow><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>5</mn><mn>7</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn></mrow></mfrac></math> + 4<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + 4 = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>4</mn></mfrac></mstyle></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. If a boatman can row upstream at 10 km/h and downstream at 12 km/h, then find the ratio of the speed of the boat in still water and the speed of the current.</p>",
                    question_hi: "<p>62. यदि एक नाविक धारा के विपरीत दिशा में 10 km/h की चाल से और धारा की दिशा में 12 km/h की चाल से नाव चला सकता है, तो स्थिर जल में नाव की चाल और धारा की चाल का अनुपात ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>2 : 11</p>",
                        "<p>11 : 1</p>",
                        "<p>11 : 2</p>",
                        "<p>1 : 11</p>"
                    ],
                    options_hi: [
                        "<p>2 : 11</p>",
                        "<p>11 : 1</p>",
                        "<p>11 : 2</p>",
                        "<p>1 : 11</p>"
                    ],
                    solution_en: "<p>62.(b) Let Speed of boat in still water = x km/h<br>Speed of the current = y km/h <br>According to question , <br>x - y = 10km/h &hellip;..(i)<br>x + y = 12km/h &hellip;&hellip;(ii)<br>After solving( i) and (ii) we get <br>x = 11 km/h , y = 1km/h</p>",
                    solution_hi: "<p>62.(b) माना शांत पानी में नाव की गति = xkm/h<br>धारा की गति = y km/h <br>प्रश्न के अनुसार, <br>x - y = 10km/h &hellip;..(i)<br>x + y = 12km/h &hellip;&hellip;(ii)<br>(i) और (ii) को हल करने के बाद हमें मिलता है <br>x = 11 km/h , y = 1km/h</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Raju spends 80% of his income. His income increased by 20%, while his spending rise by 10%. The percentage of increase in his savings is:</p>",
                    question_hi: "<p>63. राजू अपनी आय का 80% खर्च करता है। उसकी आय में 20% की वृद्धि हुई, जबकि उसके खर्च में 10% की वृद्धि हुई। उसकी बचत में वृद्धि का प्रतिशत क्या है?</p>",
                    options_en: [
                        "<p>48%</p>",
                        "<p>30%</p>",
                        "<p>60%</p>",
                        "<p>40%</p>"
                    ],
                    options_hi: [
                        "<p>48%</p>",
                        "<p>30%</p>",
                        "<p>60%</p>",
                        "<p>40%</p>"
                    ],
                    solution_en: "<p>63.(c) <br>According to the question,<br>Let the income of the Raju be 100 units<br>Ratio -&nbsp; &nbsp;income : expenditure :&nbsp; saving<br>Before -&nbsp; &nbsp;100&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 80&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;20<br>After -&nbsp; &nbsp; &nbsp; 120&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 88&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;32<br>require% = <math display=\"inline\"><mfrac><mrow><mn>32</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 60%</p>",
                    solution_hi: "<p>63.(c) <br>प्रश्न के अनुसार,<br>माना , राजू की आय = 100 इकाई <br>अनुपात -&nbsp; &nbsp;आय&nbsp; :&nbsp; &nbsp;व्यय&nbsp; :&nbsp; बचत<br>पहले -&nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; :&nbsp; &nbsp; 80&nbsp; :&nbsp; 20<br>बाद में -&nbsp; &nbsp; &nbsp;120&nbsp; :&nbsp; &nbsp; 88&nbsp; :&nbsp; 32<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>32</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 60%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. In a college, the average weight of 40 boys in a section among 72 students is 33 kg and that of the remaining students is 15 kg. What is the average weight of all the students in the section ?</p>",
                    question_hi: "<p>64. एक कॉलेज में, 72 छात्रों के एक समूह में से 40 लड़कों का औसत भार 33 kg है और शेष छात्रों का औसत भार 15 kg है। समूह के सभी छात्रों का औसत भार कितना है?</p>",
                    options_en: [
                        "<p>24 kg</p>",
                        "<p>25 kg</p>",
                        "<p>22 kg</p>",
                        "<p>18 kg</p>"
                    ],
                    options_hi: [
                        "<p>24 kg</p>",
                        "<p>25 kg</p>",
                        "<p>22 kg</p>",
                        "<p>18 kg</p>"
                    ],
                    solution_en: "<p>64.(b)<br>Sum of 40 students weight = 40 &times; 33 = 1320 kg<br>Sum of 32 students weight = 32 &times; 15 = 480 kg<br>Average of total students = <math display=\"inline\"><mfrac><mrow><mn>1320</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>480</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> = 25 kg<br>Short tricks:- 40 : 32 = 5 : 4<br>Ratio -&nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; &nbsp;4<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;33&nbsp; &nbsp; :&nbsp; &nbsp;15<br>----------------------------<br>Final - 165&nbsp; :&nbsp; &nbsp;60<br>Required average = <math display=\"inline\"><mfrac><mrow><mn>165</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> = 25 kg</p>",
                    solution_hi: "<p>64.(b)<br>40 विद्यार्थियों का कुल भार = 40 &times; 33 = 1320 किग्रा<br>32 विद्यार्थियों का कुल भार = 32 &times; 15 = 480 किग्रा<br>कुल विद्यार्थियों का औसत = <math display=\"inline\"><mfrac><mrow><mn>1320</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>480</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> = 25 किग्रा<br>शार्ट ट्रिक्स : - 40 : 32 = 5 : 4<br>अनुपात-&nbsp; &nbsp; 5&nbsp; &nbsp; :&nbsp; &nbsp; 4<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 33&nbsp; &nbsp; :&nbsp; &nbsp;15<br>----------------------------<br>अंतिम - 165&nbsp; &nbsp; :&nbsp; &nbsp;60<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>165</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> = 25 kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. ∆ABC and ∆DEF are similar and their areas are respectively 0.64 m&sup2; and 0.0121 m&sup2;. If EF = 0.154 m, then BC is:</p>",
                    question_hi: "<p>65. ∆ABC और ∆DEF समरूप हैं और उनके क्षेत्रफल क्रमशः 0.64 m&sup2; और 0.0121 m&sup2; हैं। यदि EF = 0.154 m है, तो BC का माप क्या होगा?</p>",
                    options_en: [
                        "<p>11.2 m</p>",
                        "<p>1.12 m</p>",
                        "<p>1.15 m</p>",
                        "<p>2.12 m</p>"
                    ],
                    options_hi: [
                        "<p>11.2 m</p>",
                        "<p>1.12 m</p>",
                        "<p>1.15 m</p>",
                        "<p>2.12 m</p>"
                    ],
                    solution_en: "<p>65.(b) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>E</mi><mi>F</mi></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>64</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>0121</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>E</mi><mi>F</mi></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>0</mn><mo>.</mo><mn>154</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>0</mn><mo>.</mo><mn>154</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac></mstyle></math><br>BC = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>154</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac></math> = 1.12 m</p>",
                    solution_hi: "<p>65.(b) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8710;</mo><mi>ABC</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>&#8710;</mo><mi>DEF</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>E</mi><mi>F</mi></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>64</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>0121</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mi>E</mi><mi>F</mi></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>0</mn><mo>.</mo><mn>154</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac></mstyle><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mi>B</mi><mi>C</mi></mrow><mrow><mn>0</mn><mo>.</mo><mn>154</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac></mstyle></math><br>BC = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>154</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>8</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>11</mn></mrow></mfrac></math> = 1.12 m</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Find the area of a sector with an arc length of 44 cm, which subtends a central angle of 30&deg;.</p>",
                    question_hi: "<p>66. 44 cm की चाप लंबाई वाले एक ऐसे त्रिज्यखंड का क्षेत्रफल ज्ञात करें, जो 30&deg; का केंद्रीय कोण अंतरित करता है।</p>",
                    options_en: [
                        "<p>1488 cm&sup2;</p>",
                        "<p>1584 cm&sup2;</p>",
                        "<p>1848 cm&sup2;</p>",
                        "<p>1884 cm&sup2;</p>"
                    ],
                    options_hi: [
                        "<p>1488 cm&sup2;</p>",
                        "<p>1584 cm&sup2;</p>",
                        "<p>1848 cm&sup2;</p>",
                        "<p>1884 cm&sup2;</p>"
                    ],
                    solution_en: "<p>66.(c)<br>Arc length of sector = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mn>360</mn></mfrac></math> &times;&nbsp;2&pi;r&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math>44 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mn>360</mn></mfrac></mstyle></math> &times; 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle></math> &times; r<br><math display=\"inline\"><mo>&#8658;</mo></math>r = 84 cm<br>area of a sector = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mn>360</mn></mfrac></math> &times; &pi;r&sup2;<br>= <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle></math> &times; 84 &times; 84<br>= 1848 cm&sup2;</p>",
                    solution_hi: "<p>66.(c)<br>त्रिज्यखंड की चाप लंबाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mn>360</mn></mfrac></math>&nbsp;&times; 2&pi;r&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math>44 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mn>360</mn></mfrac></mstyle></math> &times; 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle></math> &times; r<br><math display=\"inline\"><mo>&#8658;</mo></math>r = 84 cm<br>एक त्रिज्यखंड का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mn>360</mn></mfrac></math> &times; &pi;r&sup2;<br>= <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle></math> &times; 84 &times; 84<br>= 1848 cm&sup2;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. PBA and PDC are two secants. AD is the diameter of the circle with the centre at O. &ang;A&nbsp;= 30&deg;, &ang;P = 20&deg;. Find the measure of &ang;DBC.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972507178.png\" alt=\"rId55\" width=\"244\" height=\"131\"></p>",
                    question_hi: "<p>67. PBA और PDC दो छेदक रेखाएँ हैं। AD, O पर केंद्र वाले वृत्त का व्यास है। &ang;A = 30&deg;, &ang;P = 20&deg; है।&nbsp;&ang;DBC की माप ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972507178.png\" alt=\"rId55\" width=\"244\" height=\"131\"></p>",
                    options_en: [
                        "<p>45&deg;</p>",
                        "<p>30&deg;</p>",
                        "<p>50&deg;</p>",
                        "<p>40&deg;</p>"
                    ],
                    options_hi: [
                        "<p>45&deg;</p>",
                        "<p>30&deg;</p>",
                        "<p>50&deg;</p>",
                        "<p>40&deg;</p>"
                    ],
                    solution_en: "<p>67.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972507178.png\" alt=\"rId55\" width=\"244\" height=\"131\"><br>&ang;DAB = &ang;DCB = 30&deg;(angles in same segment are equal)<br>&ang;ABD = 90&deg; (angle in semicircle)<br>Then, &ang;DBP = 90&deg;<br>In <math display=\"inline\"><mi>&#916;</mi></math>BDP, &ang;PDB = 180&deg; - (90&deg;+20&deg;) = 70&deg;<br>Now, in <math display=\"inline\"><mo>&#9651;</mo></math>BCD,&ang;BDP is exterior angle. So, we have ;<br>&ang;DCB+&ang;DBC = &ang;BDP<br>30&deg; + &ang;DBC = 70&deg;<br>&ang;DBC = 70&deg; - 30&deg; = 40&deg;</p>",
                    solution_hi: "<p>67.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972507178.png\" alt=\"rId55\" width=\"244\" height=\"131\"><br>&ang;DAB = &ang;DCB = 30&deg; (एक ही खंड मे बने कोण बराबर होते है)<br>&ang;ABD = 90&deg; (अर्धवृत में कोण)<br>फिर, &ang;DBP = 90&deg;<br><math display=\"inline\"><mi>&#916;</mi></math>BDP में, &ang;PDB = 180&deg; - (90&deg;+20&deg;) = 70&deg;<br>अब, <math display=\"inline\"><mo>&#9651;</mo></math>BCD में,&ang;BDP वाह्य कोण है, तो हमारे पास है,<br>&ang;DCB+&ang;DBC = &ang;BDP<br>30&deg; + &ang;DBC = 70&deg;<br>&ang;DBC = 70&deg; - 30&deg; = 40&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A dealer sells an article at a discount of 5% on the marked price. If the marked price is 20% above the cost price and the article was sold for ₹2,280, then the cost price would be:</p>",
                    question_hi: "<p>68. एक डीलर किसी वस्तु को अंकित मूल्य पर 5% की छूट देकर बेचता है। यदि अंकित मूल्य, क्रय मूल्य से 20% अधिक है और वस्तु ₹2,280 में बेची गई, तो क्रय मूल्य कितना होगा?</p>",
                    options_en: [
                        "<p>₹2,500</p>",
                        "<p>₹2,000</p>",
                        "<p>₹3,500</p>",
                        "<p>₹3,000</p>"
                    ],
                    options_hi: [
                        "<p>₹2,500</p>",
                        "<p>₹2,000</p>",
                        "<p>₹3,500</p>",
                        "<p>₹3,000</p>"
                    ],
                    solution_en: "<p>68.(b)<br>Let CP = 100 unit<br><math display=\"inline\"><mo>&#8658;</mo></math>MP = 100 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"12px\"><mfrac><mn>120</mn><mn>100</mn></mfrac></mstyle></math> = 120 unit<br>Then, SP = 120 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn mathsize=\"12px\">19</mn><mn mathsize=\"12px\">20</mn></mfrac></math>= 114 unit<br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math>114 unit = 2280<br><math display=\"inline\"><mo>&#8658;</mo></math>100 unit = ₹ 2000<br>So, CP = ₹ 2000</p>",
                    solution_hi: "<p>68.(b)<br>माना क्रय मूल्य = 100 इकाई<br><math display=\"inline\"><mo>&#8658;</mo></math>अंकित मूल्य = 100 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn mathsize=\"12px\">120</mn><mn mathsize=\"12px\">100</mn></mfrac></math> = 120 इकाई<br>तो, विक्रय मूल्य = 120 <math display=\"inline\"><mo>&#215;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn mathsize=\"12px\">19</mn><mn mathsize=\"12px\">20</mn></mfrac></math>&nbsp;= 114 इकाई<br>प्रश्न के अनुसार, <br><math display=\"inline\"><mo>&#8658;</mo></math>114 इकाई = 2280<br><math display=\"inline\"><mo>&#8658;</mo></math>100 इकाई = ₹ 2000<br>इसलिए, क्रय मूल्य = ₹ 2000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. What conclusion can be drawn about the solution of the following system of linear equations in two variables:&nbsp;<br>3x&nbsp;+ 2y = 7 <br>2x&nbsp;+ 3y = 7</p>",
                    question_hi: "<p>69. दो चरों में रैखिक समीकरणों के निम्नलिखित निकाय के हल के बारे में क्या निष्कर्ष निकाला जा सकता है? <br>3x&nbsp;+ 2y = 7 <br>2x + 3y = 7</p>",
                    options_en: [
                        "<p>No solution</p>",
                        "<p>Unique solution</p>",
                        "<p>Infinite solutions</p>",
                        "<p>More than two solution</p>"
                    ],
                    options_hi: [
                        "<p>कोई हल नहीं</p>",
                        "<p>अद्वितीय हल</p>",
                        "<p>अनंत हल</p>",
                        "<p>दो से अधिक हल</p>"
                    ],
                    solution_en: "<p>69.(b) <strong>Given ,</strong> <br>3x + 2y = ......(i)<br>2x + 3y = .......(ii)<br>If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>a</mi><mn>1</mn></msub><msub><mi>a</mi><mn>2</mn></msub></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></mstyle></math>, then these equations have a unique Solution&nbsp;<br>Here ,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub><mo>=</mo><mn>3</mn><mo>,</mo><msub><mi>a</mi><mn>2</mn></msub><mo>=</mo><mn>2</mn><mo>,</mo><msub><mi>b</mi><mn>1</mn></msub><mo>=</mo><mn>2</mn><mo>,</mo><msub><mi>b</mi><mn>2</mn></msub><mo>=</mo><mn>3</mn><mo>&#160;</mo></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &ne;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math><br>&there4; it has unique solution</p>",
                    solution_hi: "<p>69.(b) दिया गया है , <br>3<math display=\"inline\"><mi>x</mi></math> + 2y = 7 &hellip;. (i)<br>2<math display=\"inline\"><mi>x</mi></math> + 3y= 7 &hellip;..(ii)<br>यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>a</mi><mn>1</mn></msub><msub><mi>a</mi><mn>2</mn></msub></mfrac></math> &ne;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></mstyle></math> , है, तो इन समीकरणों का एक अद्वितीय हल है <br>यहाँ,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub><mo>=</mo><mn>3</mn><mo>,</mo><msub><mi>a</mi><mn>2</mn></msub><mo>=</mo><mn>2</mn><mo>,</mo><msub><mi>b</mi><mn>1</mn></msub><mo>=</mo><mn>2</mn><mo>,</mo><msub><mi>b</mi><mn>2</mn></msub><mo>=</mo><mn>3</mn><mo>&#160;</mo></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &ne;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math><br>&there4; इसका अद्वितीय हल है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. If 49 : x :: x : 81 and 64 : y :: y : 169, where x and y are both natural numbers, then find the value of 2x + 3y.</p>",
                    question_hi: "<p>70. यदि 49 : x :: x : 81 और 64 : y :: y : 169 है, जहाँ x और y दोनों प्राकृत संख्याएँ हैं, तो 2x + 3y का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>312</p>",
                        "<p>126</p>",
                        "<p>348</p>",
                        "<p>438</p>"
                    ],
                    options_hi: [
                        "<p>312</p>",
                        "<p>126</p>",
                        "<p>348</p>",
                        "<p>438</p>"
                    ],
                    solution_en: "<p>70.(d)<br>In 49 : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> :: x : 81<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>&#215;</mo><mn>81</mn></msqrt></math> &rArr; x = 63<br>In 64 : <math display=\"inline\"><mi>y</mi></math> :: y : 169<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn><mo>&#215;</mo><mn>169</mn></msqrt></math> &rArr; y = 104<br>Now<br>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> + 3y = 2 &times; 63 + 3 &times; 104 = 126 + 312 = 438</p>",
                    solution_hi: "<p>70.(d)<br>49 : <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> :: x : 81 में<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>81</mn></msqrt></math> &rArr; x = 63<br>64 : <math display=\"inline\"><mi>y</mi></math> :: y : 169 में<br><math display=\"inline\"><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>64</mn><mo>&#215;</mo><mn>169</mn></msqrt></math> &rArr; y = 104<br>अब<br>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> + 3y = 2 &times; 63 + 3 &times; 104 = 126 + 312 = 438</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Study the given three-dimensional chart and answer the question that follows. The chart details the sale of fruits in different months.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972507449.png\" alt=\"rId57\" width=\"306\" height=\"240\"> <br>Which fruit was the most sold in the month of May and how much more than the lowest-sold fruit in that month?</p>",
                    question_hi: "<p>71. दिए गए त्रि-विमीय चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्नों का उत्तर दीजिए। चार्ट विभिन्न महीनों में फलों की बिक्री का विवरण देता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972507568.png\" alt=\"rId58\" width=\"306\" height=\"240\"> <br>मई के महीने में कौन-सा फल सबसे ज्यादा बिका और सबसे कम बिकने वाले फल से कितना ज्यादा बिका?</p>",
                    options_en: [
                        "<p>Kiwi, 23</p>",
                        "<p>Orange, 23</p>",
                        "<p>Apple, 28</p>",
                        "<p>Kiwi, 22</p>"
                    ],
                    options_hi: [
                        "<p>कीवी, 23</p>",
                        "<p>संतरा, 23</p>",
                        "<p>सेब, 28</p>",
                        "<p>कीवी, 22</p>"
                    ],
                    solution_en: "<p>71.(c)<br>Most sold fruit in may = apple (70 fruits)<br>Lowest sold fruit in may = kiwi (42 fruits)<br>Required difference = 70 - 42 = 28 fruits</p>",
                    solution_hi: "<p>71.(c)<br>मई में सर्वाधिक बिकने वाला फल = सेब (70 फल)<br>मई में सबसे कम बिकने वाला फल = कीवी (42 फल)<br>आवश्यक अंतर = 70 - 42 = 28 फल</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Reena and Riya together can complete a piece of work in 36 days. Riya and Geeta together can complete it in 54 days. Reena and Geeta together can complete it in 81 days. In how many days can Reena alone complete the work?</p>",
                    question_hi: "<p>72. रीना और रिया साथ मिलकर एक काम को 36 दिनों में पूरा कर सकती हैं। रिया और गीता साथ मिलकर इसे 54 दिनों में पूरा कर सकती है। रीना और गीता साथ मिलकर इसे 81 दिनों में पूरा कर सकती है। रीना अकेले इस काम को कितने दिनों में पूरा कर सकती है?</p>",
                    options_en: [
                        "<p>82<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p>97<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p>87<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                        "<p>92<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>"
                    ],
                    options_hi: [
                        "<p>82<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>",
                        "<p>97<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>",
                        "<p>87<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>",
                        "<p>92<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>"
                    ],
                    solution_en: "<p>72.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972507807.png\" alt=\"rId59\" width=\"298\" height=\"150\"><br>Efficiency of (Reena + Riya + Geeta) =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>19</mn><mn>2</mn></mfrac></mstyle></math> unit<br>Then, efficiency of Reena = <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 6 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle></math> unit <br>Time taken by Reena to complete the whole work = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>324</mn><mrow><mfrac><mn>7</mn><mn>2</mn></mfrac><mi>&#160;</mi></mrow></mfrac></math> = 324&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mrow><mn>7</mn><mi>&#160;</mi></mrow></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>648</mn><mrow><mn>7</mn><mi>&#160;</mi></mrow></mfrac></mstyle></math> = 92<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mrow><mn>7</mn><mi>&#160;</mi></mrow></mfrac></mstyle></math> days</p>",
                    solution_hi: "<p>72.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742972508105.png\" alt=\"rId60\" width=\"246\" height=\"142\"><br>(रीना + रिया + गीता) की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>19</mn><mn>2</mn></mfrac></mstyle></math> इकाई <br>तो, रीना की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> - 6 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle></math> इकाई <br>रीना द्वारा पूरा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>324</mn></mrow><mrow><mfrac><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi>&#160;</mi></mrow></mfrac></math> = 324 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mrow><mn>7</mn><mi>&#160;</mi></mrow></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>648</mn><mrow><mn>7</mn><mi>&#160;</mi></mrow></mfrac></mstyle></math> = 92<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>7</mn></mfrac></mstyle></math> दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A person divides his total route of journey into three equal parts and decides to travel the three parts at the speeds of 80 km/h, 60 km/h and 30 km/h, respectively. What is the average speed during the journey?</p>",
                    question_hi: "<p>73. एक व्यक्ति अपनी यात्रा की कुल दूरी को तीन बराबर भागों में बांटता है और तीनों भागों को क्रमशः 80 km/h, 60 km/h और 30 km/h की चाल से तय करने का निर्णय लेता है। यात्रा के दौरान औसत चाल क्या है?</p>",
                    options_en: [
                        "<p>49 km/h</p>",
                        "<p>40 km/h</p>",
                        "<p>45 km/h</p>",
                        "<p>48 km/h</p>"
                    ],
                    options_hi: [
                        "<p>49 km/h</p>",
                        "<p>40 km/h</p>",
                        "<p>45 km/h</p>",
                        "<p>48 km/h</p>"
                    ],
                    solution_en: "<p>73.(d) Let the total distance be 240 km<br>According to question,<br>Average speed = <math display=\"inline\"><mfrac><mrow><mi>T</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mfrac><mn>80</mn><mn>80</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>80</mn><mn>60</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>80</mn><mn>30</mn></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>240</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></mrow></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mn>240</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>8</mn></mrow></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>720</mn><mn>15</mn></mfrac></mstyle></math>&nbsp;= 48 km/h</p>",
                    solution_hi: "<p>73.(d) माना कि कुल दूरी 240 किमी है<br>प्रश्न के अनुसार,<br>औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mfrac><mrow><mn>80</mn></mrow><mrow><mn>80</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>80</mn></mrow><mrow><mn>60</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>80</mn></mrow><mrow><mn>30</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>8</mn><mn>3</mn></mfrac></mrow></mfrac><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mn>240</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>8</mn></mrow></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>720</mn><mn>15</mn></mfrac></mstyle></math> = 48 km/h</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A person spends 10% of his income on groceries, 10% on medicines, 20% on children&rsquo;s education, 15% on house rent, and he saves the remaining amount. If his monthly income is ₹30,000, find his savings.</p>",
                    question_hi: "<p>74. एक व्यक्ति अपनी आय का 10% किराने के सामान पर, 10% दवाओं पर, 20% बच्चों की शिक्षा पर, 15% घर के किराए पर खर्च करता है, और वह शेष राशि बचाता है। यदि उसकी मासिक आय ₹30,000 है, तो उसकी बचत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹13,500</p>",
                        "<p>₹16,500</p>",
                        "<p>₹12,500</p>",
                        "<p>₹15,500</p>"
                    ],
                    options_hi: [
                        "<p>₹13,500</p>",
                        "<p>₹16,500</p>",
                        "<p>₹12,500</p>",
                        "<p>₹15,500</p>"
                    ],
                    solution_en: "<p>74.(a)<br>Expenditure of the man = 10% + 10% + 20% + 15% = 55%<br>Saving of the man = 100% - 55% = 45%<br>(income) 100% = 30000<br>(saving) 45% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30000</mn><mn>100</mn></mfrac></math> &times; 45 = 13,500</p>",
                    solution_hi: "<p>74.(a)<br>आदमी का व्यय = 10% + 10% + 20% + 15% = 55%<br>आदमी की बचत = 100% - 55% = 45%<br>(आय) 100% = 30000<br>(बचत) 45% = <math display=\"inline\"><mfrac><mrow><mn>30000</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 45 = 13,500</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. A retailer gets a discount of 40% on the printed price of an article. If the retailer sells it at the printed price, then his gain percentage is:</p>",
                    question_hi: "<p>75. एक खुदरा विक्रेता को एक वस्तु के अंकित मूल्य पर 40% की छूट मिलती है। यदि खुदरा विक्रेता इसे अंकित मूल्य पर बेचता है, तो उसका लाभ प्रतिशत कितना है?</p>",
                    options_en: [
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>",
                        "<p>37<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>",
                        "<p>66<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>",
                        "<p>66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>"
                    ],
                    options_hi: [
                        "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>",
                        "<p>37<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>",
                        "<p>66<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>",
                        "<p>66<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> %</p>"
                    ],
                    solution_en: "<p>75.(d) <br>Let MP = 100<br>Then, SP = 100 &times; 60% = 60<br>Now, if retailer sells it at the printed price<br>CP for retailer = 60<br>SP for the retailer = 100<br>Hence gain% = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> &times; 100 = 66<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math> %</p>",
                    solution_hi: "<p>75.(d) <br>मान लीजिए अंकित मूल्य = 100<br>तब, विक्रय मूल्य = 100 &times; 60% = 60<br>अब, यदि खुदरा विक्रेता इसे मुद्रित मूल्य पर बेचता है<br>खुदरा विक्रेता के लिए क्रय मूल्य = 60<br>खुदरा विक्रेता के लिए विक्रय मूल्य = 100<br>अतः लाभ% = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> &times; 100 = 66<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been split into four segments. Identify the segment that contains an error.<br>The principal gave up / the prizes to / the winners on / Republic Day.</p>",
                    question_hi: "<p>76. The following sentence has been split into four segments. Identify the segment that contains an error.&nbsp;The principal gave up / the prizes to / the winners on / Republic Day.</p>",
                    options_en: [
                        "<p>The principal gave up</p>",
                        "<p>the winners on</p>",
                        "<p>the prizes to</p>",
                        "<p>Republic Day</p>"
                    ],
                    options_hi: [
                        "<p>The principal gave up</p>",
                        "<p>the winners on</p>",
                        "<p>the prizes to</p>",
                        "<p>Republic Day</p>"
                    ],
                    solution_en: "<p>76.(a) The principal gave up<br>&lsquo;Gave out&rsquo; is the correct phrasal verb to use here. &lsquo;Give out&rsquo; means distributing something. The given sentence talks about the distribution of the prizes. Hence, &lsquo;The principal gave out&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) The principal gave up<br>यहाँ प्रयोग करने के लिए सही phrasal verb \'gave out\' है। Give out&rsquo; का अर्थ है कुछ वितरित करना। दिया गया sentence पुरस्कारों के वितरण के बारे में बात करता है। अतः, &lsquo;The principal gave out&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Find a word that is the antonym of<br>Assiduous</p>",
                    question_hi: "<p>77. Find a word that is the antonym of<br>Assiduous</p>",
                    options_en: [
                        "<p>Anxious</p>",
                        "<p>Destitute</p>",
                        "<p>Unique</p>",
                        "<p>Idle</p>"
                    ],
                    options_hi: [
                        "<p>Anxious</p>",
                        "<p>Destitute</p>",
                        "<p>Unique</p>",
                        "<p>Idle</p>"
                    ],
                    solution_en: "<p>77.(d)<br><strong>Assiduous </strong>- working very hard and taking great care that everything is done as well as it can be<br><strong>Idle - </strong>not wanting to work hard<br><strong>Anxious -</strong> worried and afraid<br><strong>Destitute - </strong>without any money, food, or a home<br><strong>Unique -</strong> not like anything else; being the only one of its type</p>",
                    solution_hi: "<p>77.(d)<br><strong>Assiduous </strong>- मेहनती <br><strong>Idle </strong>- जो मेहनत नहीं करना चाहता हो<br><strong>Anxious -</strong> चिंतित और डरा हुआ<br><strong>Destitute </strong>- बिना पैसे, भोजन या घर के<br><strong>Unique -</strong> किसी और की तरह नहीं; अपने प्रकार का एकमात्र सर्वोत्तम</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Amit is also a <span style=\"text-decoration: underline;\">chip off the old block</span> like his brother, Tandon.</p>",
                    question_hi: "<p>78. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Amit is also a <span style=\"text-decoration: underline;\">chip off the old block</span> like his brother, Tandon.</p>",
                    options_en: [
                        "<p>resembling his parents in character and appearance</p>",
                        "<p>talking to others tough like a stone</p>",
                        "<p>planning things ahead neatly</p>",
                        "<p>getting ready for the future endeavours</p>"
                    ],
                    options_hi: [
                        "<p>resembling his parents in character and appearance</p>",
                        "<p>talking to others tough like a stone</p>",
                        "<p>planning things ahead neatly</p>",
                        "<p>getting ready for the future endeavours</p>"
                    ],
                    solution_en: "<p>78.(a) <strong>chip off the old block- </strong>resembling his parents in character and appearance.</p>",
                    solution_hi: "<p>78.(a) <strong>chip off the old block -</strong> resembling his parents in character and appearance./चरित्र और रूप में अपने माता-पिता जैसा होना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the correctly spelt word.</p>",
                    question_hi: "<p>79. Select the correctly spelt word.</p>",
                    options_en: [
                        "<p>Luminescent</p>",
                        "<p>Luminscent</p>",
                        "<p>Lumeniscent</p>",
                        "<p>Luminascent</p>"
                    ],
                    options_hi: [
                        "<p>Luminescent</p>",
                        "<p>Luminscent</p>",
                        "<p>Lumeniscent</p>",
                        "<p>Luminascent</p>"
                    ],
                    solution_en: "<p>79.(a) Luminescent is the correct spelling.</p>",
                    solution_hi: "<p>79.(a) Luminescent- सही spelling है </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The air conditioner has made ceiling fans a little <strong><span style=\"text-decoration: underline;\">redundant</span></strong> in today&rsquo;s world.</p>",
                    question_hi: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The air conditioner has made ceiling fans a little <strong><span style=\"text-decoration: underline;\">redundant</span></strong> in today&rsquo;s world.</p>",
                    options_en: [
                        "<p>Superfluous</p>",
                        "<p>Obsolete</p>",
                        "<p>Extinct</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>Superfluous</p>",
                        "<p>Obsolete</p>",
                        "<p>Extinct</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>80.(b) <strong>Obsolete </strong>- no longer produced or used; out of date.<br><strong>Superfluous -</strong> unnecessary, especially being more than enough<br><strong>Extinct -</strong> no longer in existence</p>",
                    solution_hi: "<p>80.(b)<strong> Obsolete </strong>- अप्रचलित।<br><strong>Superfluous</strong> - अनावश्यक, विशेष रूप से पर्याप्त से अधिक होने के कारण।<br><strong>Extinct -</strong> अब अस्तित्व में नहीं ; विलुप्त हो जाना ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Identify from the given options the word which is similar in meaning to the following word. <br>Efface</p>",
                    question_hi: "<p>81. Identify from the given options the word which is similar in meaning to the following word. <br>Efface</p>",
                    options_en: [
                        "<p>Hidden</p>",
                        "<p>Makeup</p>",
                        "<p>Destroy</p>",
                        "<p>Daily</p>"
                    ],
                    options_hi: [
                        "<p>Hidden</p>",
                        "<p>Makeup</p>",
                        "<p>Destroy</p>",
                        "<p>Daily</p>"
                    ],
                    solution_en: "<p>81.(c) <strong>Destroy- </strong>to obliterate or put an end to something.<br><strong>Efface-</strong> to erase or remove something completely.<br><strong>Hidden</strong>- kept out of sight.<br><strong>Makeup-</strong> cosmetics applied to enhance or alter appearance.<br><strong>Daily</strong>- occurring or done every day.</p>",
                    solution_hi: "<p>81.(c) <strong>Destroy</strong> (नष्ट करना) - to obliterate or put an end to something.<br><strong>Efface</strong> (विलोप) - to erase or remove something completely.<br><strong>Hidden</strong> (अदृश्य) - kept out of sight.<br><strong>Makeup</strong> (श्रृंगार) - cosmetics applied to enhance or alter appearance.<br><strong>Daily</strong> (दैनिक) - occurring or done every day.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the option that expresses the given sentence in passive voice.<br>Sindhu mailed the letter.</p>",
                    question_hi: "<p>82. Select the option that expresses the given sentence in passive voice.<br>Sindhu mailed the letter.</p>",
                    options_en: [
                        "<p>Sindhu was mailed by the letter.</p>",
                        "<p>The letter was mailed by the Sindhu.</p>",
                        "<p>The letter was mailed by Sindhu.</p>",
                        "<p>Letter mailed by the Sindhu.</p>"
                    ],
                    options_hi: [
                        "<p>Sindhu was mailed by the letter.</p>",
                        "<p>The letter was mailed by the Sindhu.</p>",
                        "<p>The letter was mailed by Sindhu.</p>",
                        "<p>Letter mailed by the Sindhu.</p>"
                    ],
                    solution_en: "<p>82.(c) The letter was mailed by Sindhu. (Correct)<br>(a) Sindhu was mailed by the letter. (Incorrect Sentence Structure)<br>(b) The letter was mailed by <span style=\"text-decoration: underline;\">the</span> Sindhu. (Incorrect use of Article)<br>(c) Letter mailed by the Sindhu.(Helping verb is missing)</p>",
                    solution_hi: "<p>82.(c) The letter was mailed by Sindhu. (Correct)<br>(a) Sindhu was mailed by <span style=\"text-decoration: underline;\">the</span> letter. (गलत Sentence Structure)<br>(b) The letter was mailed by the Sindhu. (Article का गलत use)<br>(c) Letter mailed by the Sindhu.(Helping verb, missing है)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the word which means the same as the group of words given.<br>A decorative handwriting</p>",
                    question_hi: "<p>83. Select the word which means the same as the group of words given.<br>A decorative handwriting</p>",
                    options_en: [
                        "<p>Calligraphy</p>",
                        "<p>Manuscript</p>",
                        "<p>Inscription</p>",
                        "<p>Hagiography</p>"
                    ],
                    options_hi: [
                        "<p>Calligraphy</p>",
                        "<p>Manuscript</p>",
                        "<p>Inscription</p>",
                        "<p>Hagiography</p>"
                    ],
                    solution_en: "<p>83.(a) Calligraphy<br><strong>Calligraphy</strong> - Decorative handwriting or handwritten lettering.<br><strong>Manuscript -</strong> A book, document, or piece of music written by hand rather than typed or printed.<br><strong>Inscriptio</strong>n - A thing inscribed, as on a monument or in a book.<br><strong>Hagiography</strong> - A biography that treats its subject with undue reverence.</p>",
                    solution_hi: "<p>83.(a) <strong>Calligraphy</strong></p>\n<p><strong>Calligraphy</strong> - सजावटी लिखावट या हस्तलिखित अक्षर।<br><strong>Manuscript </strong>- टाइप या मुद्रित होने के बजाय हाथ से लिखी गई एक किताब, दस्तावेज़ ।<br><strong>Inscription</strong> - किसी स्मारक या किसी पुस्तक में लिखी कोई चीज।<br><strong>Hagiography </strong>- एक जीवनी जो अपने विषय के साथ बहुत सम्मानपूर्वक व्यवहार करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) He knew that we were all angry with him, even the servants. <br>(B) I scolded him, for elephants understand words as well as children.<br>(C) His pride was so injured that he never stole another thing from the dining-room.&nbsp;<br>(D)I said to him, &ldquo;Next time I see you stealing fruit, you will be whipped.&rdquo;</p>",
                    question_hi: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) He knew that we were all angry with him, even the servants. <br>(B) I scolded him, for elephants understand words as well as children.<br>(C) His pride was so injured that he never stole another thing from the dining-room.&nbsp;<br>(D)I said to him, &ldquo;Next time I see you stealing fruit, you will be whipped.&rdquo;</p>",
                    options_en: [
                        "<p>BDAC</p>",
                        "<p>BACD</p>",
                        "<p>ACBD</p>",
                        "<p>DCBA</p>"
                    ],
                    options_hi: [
                        "<p>BDAC</p>",
                        "<p>BACD</p>",
                        "<p>ACBD</p>",
                        "<p>DCBA</p>"
                    ],
                    solution_en: "<p>84. (a) BDAC<br>Sentence B is the first sentence as the main idea of the parajumble is mentioned in it- &ldquo;the scolding to the elephant&rdquo;. D will follow B as D explains the reason for the scolding. Further, Sentence A states that he knew that we were all angry with him and Sentence C states that his pride was so injured that he never stole another thing from the dining-room . So, C will follow A. Going through the options, option (a) BDAC has the correct sequence.</p>",
                    solution_hi: "<p>84. (a) BDAC<br>Sentence B first sentence है क्योंकि इसमें parajumble का मुख्य विचार &ldquo;the scolding to the elephant&rdquo; उल्लिखित है। B के बाद D आएगा क्योंकि D डांटने का कारण बताता है। इसके अलावा, Sentence A कहता है कि वह जानता था कि हम सभी उससे नाराज थे और Sentence C कहता है कि उसका अभिमान इतना आहत था कि उसने भोजन कक्ष से कभी कोई दूसरी चीज नहीं चुराई। इसलिए, A के बाद C आएगा । options के माध्यम से जाने पर , option (a) BDAC में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Identify the word that is not correctly spelt.</p>",
                    question_hi: "<p>85. Identify the word that is not correctly spelt.</p>",
                    options_en: [
                        "<p>Expelled</p>",
                        "<p>Sartorial</p>",
                        "<p>Implicate</p>",
                        "<p>Sabotege</p>"
                    ],
                    options_hi: [
                        "<p>Expelled</p>",
                        "<p>Sartorial</p>",
                        "<p>Implicate</p>",
                        "<p>Sabotege</p>"
                    ],
                    solution_en: "<p>85.(d) Sabotege<br>correct spelling -Sabotage</p>",
                    solution_hi: "<p>85.(d) Sabotege<br>सही spelling -Sabotage </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate meaning of idiom in the sentence.<br>The police <span style=\"text-decoration: underline;\"><strong>smelt the rat</strong></span> behind the death of the girl.</p>",
                    question_hi: "<p>86. Select the most appropriate meaning of idiom in the sentence.<br>The police <strong><span style=\"text-decoration: underline;\">smelt the rat</span></strong> behind the death of the girl.</p>",
                    options_en: [
                        "<p>got very much confused</p>",
                        "<p>Identified the cause of death</p>",
                        "<p>Suspected that something is fishy</p>",
                        "<p>Jumped to the conclusion</p>"
                    ],
                    options_hi: [
                        "<p>got very much confused</p>",
                        "<p>Identified the cause of death</p>",
                        "<p>Suspected that something is fishy</p>",
                        "<p>Jumped to the conclusion</p>"
                    ],
                    solution_en: "<p>86.(c) Smelt the rat- Suspected that something is fishy<br>Example- He\'s been meeting the opposition leaders of late - I smell a rat!</p>",
                    solution_hi: "<p>86.(c) Smelt the rat- शक होना कि कुछ गड़बड़ है ; संदेह होना। <br>Example- He\'s been meeting the opposition leaders of late - I smell a rat!<br>(वह हाल ही में विपक्षी नेताओं से मिल रहा है - मुझे शक है कि कुछ गड़बड़ है।)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) It can drink a lot of water at one time<br>(B) Camels can do without water for days together.<br>(C) The reason is they sweat very little.<br>(D) The camel, is popularly known as the &lsquo;ship of the desert&rsquo;.</p>",
                    question_hi: "<p>87. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) It can drink a lot of water at one time<br>(B) Camels can do without water for days together.<br>(C) The reason is they sweat very little.<br>(D) The camel, is popularly known as the &lsquo;ship of the desert&rsquo;.</p>",
                    options_en: [
                        "<p>DBAC</p>",
                        "<p>DABC</p>",
                        "<p>DCBA</p>",
                        "<p>DACB</p>"
                    ],
                    options_hi: [
                        "<p>DBAC</p>",
                        "<p>DABC</p>",
                        "<p>DCBA</p>",
                        "<p>DACB</p>"
                    ],
                    solution_en: "<p>87. (b) DABC<br>Sentence D will be the first sentence as it talks about the subject i.e. the camel .A will follow D as it mentions the nature of the subject i.e. the camel can drink a lot of water at one time. B will follow A as it mentions it can survive without water for days together. In C the reason for their survival without water is mentioned . So <strong>(b)</strong> DABC has the correct sequence.</p>",
                    solution_hi: "<p>87. (b) DABC<br>Sentence D first sentence होगा क्योंकि यह विषय &lsquo;the camel&rsquo; के बारे में बात करता है। D के बाद A आएगा क्योंकि यह camel की प्रकृति का उल्लेख करता है - ऊंट एक समय में बहुत सारा पानी पी सकता है। A के बाद B आएगा क्योंकि यह उल्लेख करता है कि यह पानी के बिना एक साथ कई दिनों तक जीवित रह सकता है। C में पानी के बिना उनके जीवित रहने का कारण बताया गया है। तो (b) DABC का सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate meaning of the given idiom.<br>Chip on the shoulder</p>",
                    question_hi: "<p>88. Select the most appropriate meaning of the given idiom.<br>Chip on the shoulder</p>",
                    options_en: [
                        "<p>Elated</p>",
                        "<p>Emotional</p>",
                        "<p>Happy</p>",
                        "<p>Offended</p>"
                    ],
                    options_hi: [
                        "<p>Elated</p>",
                        "<p>Emotional</p>",
                        "<p>Happy</p>",
                        "<p>Offended</p>"
                    ],
                    solution_en: "<p>88.(d)<strong> Chip on the shoulder-</strong> offended.<br>E.g.- He has a chip on his shoulder because he wasn&rsquo;t chosen for the team.</p>",
                    solution_hi: "<p>88.(d) <strong>Chip on the shoulder</strong> - offended./अपमानित।<br>E.g.- He has a chip on his shoulder because he wasn&rsquo;t chosen for the team.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89.&nbsp;Select the most appropriate option to fill in the blank.&nbsp;<br>We usually give our servants the old clothes we cast_________</p>",
                    question_hi: "<p>89.&nbsp;Select the most appropriate option to fill in the blank.&nbsp;<br>We usually give our servants the old clothes we cast_________</p>",
                    options_en: [
                        "<p>off</p>",
                        "<p>aside</p>",
                        "<p>away</p>",
                        "<p>None</p>"
                    ],
                    options_hi: [
                        "<p>off</p>",
                        "<p>aside</p>",
                        "<p>away</p>",
                        "<p>None</p>"
                    ],
                    solution_en: "<p>89.(a) off<br>&lsquo;Cast off&rsquo; is a phrasal verb which means something not used anymore. The given sentence states that we usually give our servants the old clothes we cast off (do not use anymore). Hence, &lsquo;off &rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(a) off<br>&lsquo;Cast off&rsquo; एक phrasal verb है जिसका अर्थ है कि अब कुछ उपयोग नहीं किया जाता है। दिए गए वाक्य में कहा गया है कि हम आमतौर पर अपने नौकरों को पुराने कपड़े देते हैं (जिन्हे अब उपयोग नहीं करते हैं)। इसलिए, &lsquo;off&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>Just as I was entering the room the family was going for a party .</p>",
                    question_hi: "<p>90. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>Just as I was entering the room the family was going for a party .</p>",
                    options_en: [
                        "<p>Just as</p>",
                        "<p>I was entering the room</p>",
                        "<p>the family was going for a party</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>Just as</p>",
                        "<p>I was entering the room</p>",
                        "<p>the family was going for a party</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>90.(b) I was entering the room <br>Replace &ldquo;was entering&rdquo; by &ldquo;entered&rsquo;&rsquo;. Just as I entered the room the family was going for a party</p>",
                    solution_hi: "<p>90.(b) I was entering the room <br>&ldquo;Was entering&rdquo; को &ldquo;entered&rsquo;&rsquo; से replace करें। Just as I entered the room the family was going for a party.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the option that can be used as a one-word substitute for the given group of words. <br>Words inscribed on a tomb.</p>",
                    question_hi: "<p>91. Select the option that can be used as a one-word substitute for the given group of words. <br>Words inscribed on a tomb.</p>",
                    options_en: [
                        "<p>Prologue</p>",
                        "<p>Epilogue</p>",
                        "<p>Post-script</p>",
                        "<p>Epitaph</p>"
                    ],
                    options_hi: [
                        "<p>Prologue</p>",
                        "<p>Epilogue</p>",
                        "<p>Post-script</p>",
                        "<p>Epitaph</p>"
                    ],
                    solution_en: "<p>91.(d)<strong> Epitaph</strong>- words inscribed on a tomb.<br><strong>Prologue-</strong> a piece of writing or a speech that introduces a play, poem, etc.<br><strong>Epilogue</strong>- a speech or piece of text that is added to the end of a play or book<br><strong>Post-script-</strong> an extra message added at the end of a story or letter.</p>",
                    solution_hi: "<p>91.(d) <strong>Epitaph </strong>(समाधि-लेख) - words inscribed on a tomb.<br><strong>Prologue</strong> (प्रस्तावना) - a piece of writing or a speech that introduces a play, poem, etc.<br><strong>Epilogue</strong> (उपसंहार) - a speech or piece of text that is added to the end of a play or book<br><strong>Post-script</strong> (परिशिष्ट भाग) - an extra message added at the end of a story or letter.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Find a word that is the synonym of<br>Livid</p>",
                    question_hi: "<p>92. Find a word that is the synonym of<br>Livid</p>",
                    options_en: [
                        "<p>Closed</p>",
                        "<p>Clear</p>",
                        "<p>Pale</p>",
                        "<p>Furious</p>"
                    ],
                    options_hi: [
                        "<p>Closed</p>",
                        "<p>Clear</p>",
                        "<p>Pale</p>",
                        "<p>Furious</p>"
                    ],
                    solution_en: "<p>92.(d) <strong>Livid </strong>- very angry<br><strong>Furious- </strong>very angry<br><strong>Closed-</strong> not open<br><strong>Clear- </strong>easy to see, hear or understand<br><strong>Pale-</strong> not bright or strong in colour</p>",
                    solution_hi: "<p>92.(d) <strong>Livid -</strong> क्रोधित<br><strong>Furious</strong>- अत्यधिक गुस्सा या क्रोध <br><strong>Closed-</strong> बंद <br><strong>Clear-</strong> देखने, सुनने या समझने में आसान<br><strong>Pale-</strong> फीका या पीला पड़ना। </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the correct passive form of the given sentence.<br>Chhattisgarh organised the first ever &lsquo;All India Folk Dance Festival&rsquo; in January 2020.</p>",
                    question_hi: "<p>93. Select the correct passive form of the given sentence.<br>Chhattisgarh organised the first ever &lsquo;All India Folk Dance Festival&rsquo; in January 2020.</p>",
                    options_en: [
                        "<p>The first ever &lsquo;All India Folk Dance Festival&rsquo; was organised by the Chhattisgarh State in January 2020.</p>",
                        "<p>The first ever &lsquo;All India Folk Dance Festival&rsquo; was being organised by the Chhattisgarh State in January 2020.</p>",
                        "<p>The first ever &lsquo;All India Folk Dance Festival&rsquo; will be organised by the Chhattisgarh State in January 2020.</p>",
                        "<p>The first ever &lsquo;All India Folk Dance Festival&rsquo; had been organised by the Chhattisgarh State in January 2020.</p>"
                    ],
                    options_hi: [
                        "<p>The first ever &lsquo;All India Folk Dance Festival&rsquo; was organised by the Chhattisgarh State in January 2020.</p>",
                        "<p>The first ever &lsquo;All India Folk Dance Festival&rsquo; was being organised by the Chhattisgarh State in January 2020.</p>",
                        "<p>The first ever &lsquo;All India Folk Dance Festival&rsquo; will be organised by the Chhattisgarh State in January 2020.</p>",
                        "<p>The first ever &lsquo;All India Folk Dance Festival&rsquo; had been organised by the Chhattisgarh State in January 2020.</p>"
                    ],
                    solution_en: "<p>93.(a) The first ever &lsquo;All India Folk Dance Festival&rsquo; was organised by the Chhattisgarh State in January 2020.(Correct)<br>(b) The first ever &lsquo;All India Folk Dance Festival&rsquo; <span style=\"text-decoration: underline;\">was being</span> organised by the Chhattisgarh State in January 2020. (Incorrect Verb)<br>(c) The first ever &lsquo;All India Folk Dance Festival&rsquo; <span style=\"text-decoration: underline;\">will be</span> organised by the Chhattisgarh State in January 2020. (Tense has changed)<br>(d) The first ever &lsquo;All India Folk Dance Festival&rsquo; <span style=\"text-decoration: underline;\">had been</span> organised by the Chhattisgarh State in January 2020. (Tense has changed)</p>",
                    solution_hi: "<p>93.(a) The first ever &lsquo;All India Folk Dance Festival&rsquo; was organised by the Chhattisgarh State in January 2020. (Correct)<br>(b) The first ever &lsquo;All India Folk Dance Festival&rsquo; <span style=\"text-decoration: underline;\">was being</span> organised by the Chhattisgarh State in January 2020. (गलत verb (was being organised)) का प्रयोग किया गया है। (was organised) का प्रयोग होगा ।&nbsp;<br>(c) The first ever &lsquo;All India Folk Dance Festival&rsquo; <span style=\"text-decoration: underline;\">will be</span> organised by the Chhattisgarh State in January 2020.(गलत tense (simple future) का प्रयोग किया गया है। (was organised) (simple past) का प्रयोग होगा ।<br>(d) The first ever &lsquo;All India Folk Dance Festival&rsquo; <span style=\"text-decoration: underline;\">had been</span> organised by the Chhattisgarh State in January 2020. (गलत tense (past perfect) का प्रयोग किया गया है। (was organised) (simple past ) का प्रयोग होगा ।&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The beauty of cherry blossoms is <span style=\"text-decoration: underline;\">ephemeral</span>, as the delicate pink petals bloom for only a short period.</p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The beauty of cherry blossoms is <span style=\"text-decoration: underline;\">ephemeral</span>, as the delicate pink petals bloom for only a short period.</p>",
                    options_en: [
                        "<p>Transient</p>",
                        "<p>Permanent</p>",
                        "<p>Uniform</p>",
                        "<p>Unwavering</p>"
                    ],
                    options_hi: [
                        "<p>Transient</p>",
                        "<p>Permanent</p>",
                        "<p>Uniform</p>",
                        "<p>Unwavering</p>"
                    ],
                    solution_en: "<p>94.(b)<strong> Permanent- </strong>lasting for a long time or forever.<br><strong>Ephemeral</strong>- lasting or used for only a short period of time.<br><strong>Transient-</strong> lasting or continuing for a short period of time. <br><strong>Uniform-</strong> unchanging in form or character.<br><strong>Unwavering-</strong> continuing in a strong and steady way.</p>",
                    solution_hi: "<p>94.(b)<strong> Permanent </strong>(स्थायी) - lasting for a long time or forever.<br><strong>Ephemeral</strong> (अल्पकालिक) - lasting or used for only a short period of time.<br><strong>Transient</strong> (क्षणिक) - lasting or continuing for a short period of time. <br><strong>Uniform </strong>(वर्दी) - unchanging in form or character.<br><strong>Unwavering </strong>(अडिग) - continuing in a strong and steady way.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>We __________ not hurry, we have got plenty of time.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>We __________ not hurry, we have got plenty of time.</p>",
                    options_en: [
                        "<p>must</p>",
                        "<p>need</p>",
                        "<p>would</p>",
                        "<p>should</p>"
                    ],
                    options_hi: [
                        "<p>must</p>",
                        "<p>need</p>",
                        "<p>would</p>",
                        "<p>should</p>"
                    ],
                    solution_en: "<p>95.(b) need.<br>&ldquo;Need not&rdquo; is used to show that something is not necessary.</p>",
                    solution_hi: "<p>95.(b) need.<br>जब कोई चीज आवश्यक न हो तो वहाँ \"Need Not\" का प्रयोग किया</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: [
                        "<p>rather</p>",
                        "<p>change</p>",
                        "<p>apart</p>",
                        "<p>alone</p>"
                    ],
                    options_hi: [
                        "<p>rather</p>",
                        "<p>change</p>",
                        "<p>apart</p>",
                        "<p>alone</p>"
                    ],
                    solution_en: "<p>96.(d) &lsquo;Alone&rsquo; means without any other person. The given passage states that Yoga is a personal journey for everyone as now most people are turning to it, not for fitness alone. Hence, &lsquo;alone&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) \'Alone\' का अर्थ है अकेला । दिए गए passage में कहा गया है कि योग सभी के लिए एक व्यक्तिगत यात्रा है क्योंकि अब ज्यादातर लोग इसकी ओर रुख कर रहे हैं, लोग केवल फिटनेस के लिए रुख नहीं कर रहे हैं। इसलिए, \'alone\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97.<strong> Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: [
                        "<p>settled</p>",
                        "<p>came</p>",
                        "<p>originated</p>",
                        "<p>involved</p>"
                    ],
                    options_hi: [
                        "<p>settled</p>",
                        "<p>came</p>",
                        "<p>originated</p>",
                        "<p>involved</p>"
                    ],
                    solution_en: "<p>97.(c) &lsquo;Originated&rsquo; means to happen or appear for the first time in a particular place or situation. The given passage states that Yoga is an ancient practice that originated in India. Hence, &lsquo;originated&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(c) \'Originated\' का अर्थ है किसी विशेष स्थान या स्थिति में पहली बार घटित होना या प्रकट होना। दिए गए passage में कहा गया है कि योग एक प्राचीन प्रथा है जिसकी उत्पत्ति भारत में हुई थी। अतः \'originated\' सर्वाधिक उपयुक्त उत्तर है I</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: [
                        "<p>senses</p>",
                        "<p>techniques</p>",
                        "<p>phases</p>",
                        "<p>pursuit</p>"
                    ],
                    options_hi: [
                        "<p>senses</p>",
                        "<p>techniques</p>",
                        "<p>phases</p>",
                        "<p>pursuit</p>"
                    ],
                    solution_en: "98.(b) ‘Techniques’ means a particular way of doing something. The given passage states that it involves movement, meditation, and breathing techniques to promote mental and physical well-being. Hence, ‘techniques’ is the most appropriate answer.",
                    solution_hi: "98.(b) \'Techniques\' का अर्थ है कुछ करने का एक विशेष तरीका। दिए गए passage में कहा गया है कि इसमें मानसिक और शारीरिक कल्याण को बढ़ावा देने के लिए गति(movement), ध्यान(meditation) और सांस लेने(breathing) की तकनीक शामिल है। इसलिए, \'techniques\' सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: [
                        "<p>choose</p>",
                        "<p>familiarise</p>",
                        "<p>make</p>",
                        "<p>strike</p>"
                    ],
                    options_hi: [
                        "<p>choose</p>",
                        "<p>familiarise</p>",
                        "<p>make</p>",
                        "<p>strike</p>"
                    ],
                    solution_en: "<p>99.(a) &lsquo;Choose&rsquo; means to decide which thing or person you want out of the ones that are available. The given passage states that a person should choose a style based on their goals and fitness level. Hence, &lsquo;choose&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) \'Choose\' का अर्थ है यह तय करना कि आप उपलब्ध चीजों में से कौन सी चीज या व्यक्ति चाहते हैं। दिए गए passage में कहा गया है कि एक व्यक्ति को अपने लक्ष्य और फिटनेस के स्तर के आधार पर एक शैली का चयन करना चाहिए। इसलिए, \'choose\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100.<strong> Cloze Test:-</strong><br>Yoga is a personal journey for everyone as now most people are turning to it, not for fitness (96) ________, but for the union of both, body and soul. Yoga is an ancient practice that (97) _________ in India. It involves movement, meditation and breathing (98) _________ to promote mental and physical well-being. There are many styles of yoga. A person should (99) __________ a style based on their goals and fitness level. If yoga and pranayam are complemented with other forms of spiritual practice to purify the mental body and other bodies, then a person can achieve rapid spiritual progress in his lifetime. Thus a person can make progress to achieve the purpose of life which is to (100) __________ with God.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: [
                        "<p>compete</p>",
                        "<p>progress</p>",
                        "<p>merge</p>",
                        "<p>mark</p>"
                    ],
                    options_hi: [
                        "<p>compete</p>",
                        "<p>progress</p>",
                        "<p>merge</p>",
                        "<p>mark</p>"
                    ],
                    solution_en: "<p>100.(c) &lsquo;Merge&rsquo; means to join things together so that they become one. The given passage states that a person can make progress to achieve the purpose of life which is to merge with God. Hence, &lsquo;merge&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) &lsquo;\'Merge\' का अर्थ है चीजों को एक साथ जोड़ना ताकि वे एक हो जाएं। दिए गए passage में कहा गया है कि व्यक्ति जीवन के उद्देश्य को प्राप्त करने के लिए प्रगति कर सकता है जो कि भगवान के साथ विलय करना है। इसलिए, \'Merge\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>