<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "1. If the loss incurred on selling an article is 38 percent, then what will be the ratio of its cost price and selling price",
                    question_hi: "1.  यदि किसी वस्तु को बेचने पर होने वाली हानि 38 प्रतिशत है, तो उसके क्रय मूल्य और विक्रय मूल्य का अनुपात कितना होगा?",
                    options_en: ["  50 : 31", "  40 : 31", 
                                "  31 : 50", "  31 : 40"],
                    options_hi: ["  50 : 31", "  40 : 31",
                                "  31 : 50", "  31 : 40"],
                    solution_en: "1.(a) let cost price  = 100 unit <br />Selling price = 100 - 38 = 62 unit <br />Ratio of C.P. and S.P. = 100 : 62 <math display=\"inline\"><mo>⇒</mo></math> 50 : 31 ",
                    solution_hi: "1.(a) माना लागत मूल्य = 100 इकाई<br />विक्रय मूल्य = 100 - 38 = 62 इकाई<br />लागत मूल्य और विक्रय मूल्य का अनुपात = 100 : 62 <math display=\"inline\"><mo>⇒</mo></math> 50 : 31 ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "2. Siraj earns a profit of 32% on selling price. Find the approximate profit percent on the cost price",
                    question_hi: "2. सिराज विक्रय मूल्य पर 32% का लाभ कमाता है। क्रय मूल्य पर अनुमानित (approximate) लाभ प्रतिशत ज्ञात कीजिए।",
                    options_en: [" 47%", " 51%", 
                                " 45%", " 49%"],
                    options_hi: [" 47%", " 51%",
                                " 45%", " 49%"],
                    solution_en: "2.(a) 32 % = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> <br />Let the S.P. = 25 unit <br />Cost price = 25 - 8 = 17 unit <br />Profit % = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> × 100 = 47.05% 〜 47%",
                    solution_hi: "2.(a) 32 % = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> <br />माना विक्रय मूल्य  = 25 इकाई<br />क्रय मूल्य  = 25 - 8 = 17 इकाई<br />लाभ % = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> × 100 = 47.05% 〜 47%",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. A person incurs a loss of 30 percent by selling 54 chairs for Rs 70. What should be the selling price of 54 chairs to earn a profit of 15 percent?",
                    question_hi: "3. एक व्यक्ति को 70 रुपए में 54 कुर्सियां बेचने पर 30 प्रतिशत की हानि होती है। 15 प्रतिशत का लाभ अर्जित करने के लिए 54 कुर्सियों का विक्रय मूल्य कितना होना चाहिए?",
                    options_en: [" Rs 135", " Rs 115", 
                                " Rs 120", " Rs 140"],
                    options_hi: [" 135 रुपए ", " 115 रुपए",
                                " 120 रुपए ", " 140 रुपए"],
                    solution_en: "3.(b)<br />Cost price of 54 chairs = 70 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> = Rs. 100<br />For the profit of 15% ,<br />Selling price of 54 chairs = 100 × <math display=\"inline\"><mfrac><mrow><mn>115</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = Rs. 115",
                    solution_hi: "3.(b)<br />54 कुर्सियों का लागत मूल्य = 70 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> = रु. 100<br />15% के लाभ के लिए ,<br />54 कुर्सियों का विक्रय मूल्य = 100 × <math display=\"inline\"><mfrac><mrow><mn>115</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = रु. 115",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "4. The selling price of one chair is Rs 7790. If the loss percentage is 18 percent, what is the cost price of the chair?",
                    question_hi: "4. एक कुर्सी का विक्रय मूल्य 7790 रुपए है। यदि हानि प्रतिशत 18 प्रतिशत है, तो कुर्सी का क्रय मूल्य कितना है?",
                    options_en: [" Rs 9000", " Rs 8800", 
                                " Rs 9500", " Rs 9200"],
                    options_hi: [" 9000 रुपए", " 8800 रुपए",
                                " 9500 रुपए ", " 9200 रुपए"],
                    solution_en: "4.(c)<br />Cost price = 7790 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>82</mn></mrow></mfrac></math> = Rs. 9500",
                    solution_hi: "4.(c)<br />लागत मूल्य = 7790 × <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>82</mn></mrow></mfrac></math> = रु. 9500",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A person bought some pens at the rate of 14 for a rupee and sold them at the rate of 12 for a rupee. What is the profit percentage?</p>",
                    question_hi: "<p>5. एक व्यक्ति ने एक रुपए में 14 पेन की दर से कुछ पेन खरीदी और उन्हें एक रुपए में 12 पेन की दर से बेच दिया। लाभ प्रतिशत कितना है?</p>",
                    options_en: ["<p>20 percent</p>", "<p>16.66 percent</p>", 
                                "<p>10 percent</p>", "<p>25 percent</p>"],
                    options_hi: ["<p>20 प्रतिशत</p>", "<p>16.66 प्रतिशत</p>",
                                "<p>10 प्रतिशत</p>", "<p>25 प्रतिशत</p>"],
                    solution_en: "<p>5.(b)<br>CP of 1 pen = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math><br>SP of 1 pen = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math><br>Profit % = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>14</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>14</mn></mrow></mfrac></mrow></mfrac></math> &times; 100</p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mn>7</mn><mo>-</mo><mn>6</mn></mrow><mn>84</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>14</mn></mfrac></mstyle></mfrac><mo>&#215;</mo><mn>100</mn></math> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>84</mn></mrow></mfrac></math>&times; 100</p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= 16.66%<br><strong>Alternate method :-</strong><br>CP for pen <math display=\"inline\"><mo>&#8594;</mo></math> ₹ 1 : 14 ) &times; 6 = ₹ 6<br>SP for pen <math display=\"inline\"><mo>&#8594;</mo></math> ₹ 1 : 12 ) &times; 7 = ₹ 7<br>Now, <br>Profit % = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>7</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>6</mn><mo>)</mo></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 100 = 16.66 %</p>",
                    solution_hi: "<p>5.(b)<br>1 पेन का क्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math><br>1 पेन का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math><br>लाभ% = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>14</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>14</mn></mrow></mfrac></mrow></mfrac></math> &times; 100</p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mn>7</mn><mo>-</mo><mn>6</mn></mrow><mn>84</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>14</mn></mfrac></mstyle></mfrac><mo>&#215;</mo><mn>100</mn></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>84</mn></mrow></mfrac></math>&times; 100</p>\n<p>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= 16.66%<br><strong>वैकल्पिक विधि :-</strong><br>पेन का क्रय मूल्य <math display=\"inline\"><mo>&#8594;</mo></math> ₹ 1 : 14 ) &times; 6 = ₹ 6<br>पेन का विक्रय मूल्य <math display=\"inline\"><mo>&#8594;</mo></math> ₹ 1 : 12 ) &times; 7 = ₹ 7<br>अब, <br>लाभ % = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>7</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>6</mn><mo>)</mo></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; 100 = 16.66 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A dealer sells two laptops at the rate of Rs. 10000 per laptop. On one he earns a profit of 40 percent and on the other he loses 40 percent. What is his profit /loss percentage in the whole transaction?</p>",
                    question_hi: "<p>6. एक व्यापारी 10000 रुपए प्रति लैपटॉप की दर से दो लैपटॉप बेचता है। एक पर वह 40 प्रतिशत का लाभ अर्जित करता है और दूसरे पर उसे 40 प्रतिशत की हानि होती है। पूरे लेन-देन में उसका लाभ हानि प्रतिशत कितना है?</p>",
                    options_en: ["<p>20 percent profit</p>", "<p>20 percent loss</p>", 
                                "<p>16 percent profit</p>", "<p>16 percent loss</p>"],
                    options_hi: ["<p>20 प्रतिशत लाभ</p>", "<p>20 प्रतिशत हानि</p>",
                                "<p>16 प्रतिशत लाभ</p>", "<p>16 प्रतिशत हानि</p>"],
                    solution_en: "<p>6.(d)<br>Ratio -&nbsp; &nbsp; &nbsp; CP : SP<br>1st laptop -&nbsp; 5 : 7 <strong>) &times; 3</strong><br>2nd laptop - 5 : 3<strong> ) &times; 7</strong><br>CP of both laptops = (5 &times; 3) + (5 &times; 7) = 50 units<br>SP of both laptops = (7 &times; 3) + (3 &times; 7) = 42 units<br>loss% = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>42</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 16%</p>",
                    solution_hi: "<p>6.(d)<br>अनुपात - क्रय मूल्य : विक्रय मूल्य<br>पहला लैपटॉप&nbsp; -&nbsp; 5&nbsp; &nbsp;:&nbsp; &nbsp;7 <strong>) &times; 3</strong><br>दूसरा लैपटॉप -&nbsp; &nbsp;5&nbsp; &nbsp;:&nbsp; &nbsp;3 <strong>) &times; 7</strong><br>दोनो लैपटॅाप का क्रय मूल्य = (5 &times; 3) + (5 &times; 7) = 50 इकाई <br>दोनो लैपटॉप का विक्रय मूल्य = (7 &times; 3) + (3 &times; 7) = 42 इकाई <br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>42</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 16%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. A shopkeeper allows a 25% discount on the marked price of an article and he suffers a loss of 10%. If the article is sold at the marked price, then find the profit percentage.</p>",
                    question_hi: "<p>7. एक दुकानदार किसी वस्तु के अंकित मूल्य पर 25% की छूट देता है और उसे 10% की हानि होती है। यदि वस्तु को अंकित मूल्य पर बेचा जाता है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>30%</p>", "<p>10%</p>", 
                                "<p>15%</p>", "<p>20%</p>"],
                    options_hi: ["<p>30%</p>", "<p>10%</p>",
                                "<p>15%</p>", "<p>20%</p>"],
                    solution_en: "<p>7.(d)<br>Let the MP of the article = 100 units <br>So the SP will be = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 75 units<br>CP = 75 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>750</mn><mn>9</mn></mfrac></math> units<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>750</mn></mrow><mrow><mn>9</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>750</mn></mrow><mrow><mn>9</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>&#215;</mo><mn>9</mn></mrow><mrow><mn>9</mn><mo>&#215;</mo><mn>750</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>7.(d)<br>मान वस्तु का अंकित मूल्य = 100 इकाई <br>तो विक्रय मूल्य होगा = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 75 इकाई <br>क्रय मूल्य = 75 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>750</mn><mn>9</mn></mfrac></math>इकाई&nbsp;<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>750</mn></mrow><mrow><mn>9</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>750</mn></mrow><mrow><mn>9</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>&#215;</mo><mn>9</mn></mrow><mrow><mn>9</mn><mo>&#215;</mo><mn>750</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. 50 percent of marked price is equal to 70 percent of cost price. If no discount is given, then what will be the profit percentage?</p>",
                    question_hi: "<p>8. अंकित मूल्य का 50 प्रतिशत, क्रय मूल्य के 70 प्रतिशत के बराबर है। यदि कोई छूट नहीं दी जाती है, तो लाभ प्रतिशत क्या होगा?</p>",
                    options_en: ["<p>20 percent</p>", "<p>50 percent</p>", 
                                "<p>60 percent</p>", "<p>40 percent</p>"],
                    options_hi: ["<p>20 प्रतिशत</p>", "<p>50 प्रतिशत</p>",
                                "<p>60 प्रतिशत</p>", "<p>40 प्रतिशत</p>"],
                    solution_en: "<p>8.(d) According to question, <br>50% <math display=\"inline\"><mo>&#215;</mo></math> M.P. = 70% &times; C.P. <br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow><mrow><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> Profit% = 25 &times; 100 = 40%</p>",
                    solution_hi: "<p>8.(d) प्रश्न के अनुसार,<br>50% <math display=\"inline\"><mo>&#215;</mo></math> अंकित मूल्य = 70% &times; क्रय मूल्य <br><math display=\"inline\"><mo>&#8658;</mo></math><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mo>&#160;</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>5</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> लाभ% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>&times; 100 = 40%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "9. What is the profit percentage in selling an article at a discount of 30% which was earlier being sold at a 45% profit?",
                    question_hi: "9. एक वस्तु को 30% की छूट पर बेचने पर लाभ प्रतिशत कितना होगा, जिसे पहले 45% लाभ पर बेचा जा रहा था?",
                    options_en: [" 1.3%", " 1.5%", 
                                " 2.5 %", " 1.1%"],
                    options_hi: [" 1.3%", " 1.5%",
                                " 2.5 %", " 1.1%"],
                    solution_en: "9.(b) let the cost price is 100 unit<br />Selling price = 145 unit<br />Selling price at 30% discount  = 145 <math display=\"inline\"><mo>×</mo><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mi>&nbsp;</mi></math>= 101.5<br />Profit % = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>×</mo><mn>100</mn></math> = 1.5% ",
                    solution_hi: "9.(b) माना लागत मूल्य 100 इकाई है<br />विक्रय मूल्य = 145 इकाई <br />30% छूट पर विक्रय मूल्य  = 145 <math display=\"inline\"><mo>×</mo><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mi>&nbsp;</mi></math>= 101.5<br />लाभ % = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>×</mo><mn>100</mn></math> = 1.5% ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10. Vikas purchased two bicycles, first for Rs. 15000 and the second for Rs. 14000. He sold both the bicycles, first one at the profit of 10 percent and the second at a loss of 20 percent. What is the overall profit or loss?",
                    question_hi: "10. विकास दो साइकिलें, पहली 15000 रुपए में और दूसरी 14000 रुपए में खरीदता है। वह दोनों साइकिलें, पहली 10 प्रतिशत के लाभ पर और दूसरी 20 प्रतिशत की हानि पर बेच देता है। कुल लाभ या हानि कितनी है?",
                    options_en: [" Loss =  Rs. 1300", " Profit = Rs. 1000", 
                                " Profit = Rs. 1200", " Loss = Rs. 1400"],
                    options_hi: [" हानि = 1300 रुपए", " लाभ = 1000 रुपए",
                                " लाभ = 1200 रुपए", " हानि = 1400 रुपए"],
                    solution_en: "10.(a) cost price of both article = 29000<br />Selling price of first cycle = 15000 <math display=\"inline\"><mo>×</mo><mfrac><mrow><mn>110</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 16500<br />Selling price of second cycle = 14000 <math display=\"inline\"><mo>×</mo><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 11200<br />Total selling price of both cycle = 16500 + 11200 = 27700<br />Loss = 29000 - 27700  = Rs. 1300",
                    solution_hi: "10.(a) दोनों वस्तुओं का लागत मूल्य  = 29000<br />पहली साइकिल का विक्रय मूल्य = 15000 <math display=\"inline\"><mo>×</mo><mfrac><mrow><mn>110</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 16500<br />दूसरी साइकिल का विक्रय मूल्य  = 14000 <math display=\"inline\"><mo>×</mo><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 11200<br />दोनों साइकिल का कुल विक्रय मूल्य = 16500 + 11200 = 27700<br />हानि = 29000 - 27700  = 1300 रुपए",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "11. The cost of 25 chairs is 80 percent more than the cost of 20 tables. If the cost of 22 tables is Rs 10648, then what is the cost of 50 chairs ?",
                    question_hi: "11. 25 कुर्सियों का मूल्य 20 मेजों के मूल्य से 80 प्रतिशत अधिक है। यदि 22 मेजों का मूल्य 10648 रुपए है, तो 50 कुर्सियों का मूल्य कितना है ?",
                    options_en: [" Rs 34888 ", " Rs 34848 ", 
                                " Rs 38848 ", " Rs 34488 "],
                    options_hi: [" 34888 रुपए ", " 34848 रुपए ",
                                " 38848 रुपए ", " 34488 रुपए"],
                    solution_en: "11.(b)<br />Cost of 1 table = <math display=\"inline\"><mfrac><mrow><mn>10648</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> = Rs. 484<br />Cost of 20 tables = 20 × 484 = Rs. 9680<br />Now, according to the question,<br />Cost of 25 chairs = 9680 × <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = Rs. 17424<br />Hence, cost of 50 chairs  = 17424 × 2 = Rs. 34848",
                    solution_hi: "11.(b)<br />1 मेजों की लागत = <math display=\"inline\"><mfrac><mrow><mn>10648</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math> = रु. 484<br />20 मेजों की लागत = 20 × 484 = रु.9680<br />अब, प्रश्न के अनुसार,<br />25 कुर्सियों की लागत = 9680 × <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = रु.17424<br />अतः, 50 कुर्सियों की लागत = 17424 × 2 = रु.34848",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "12. The cost price of a cooler is Rs 4500. If the profit percentage is 18 percent, then what is the selling price of the cooler ?",
                    question_hi: "12. एक कूलर का क्रय मूल्य 4500 रुपए है। यदि लाभ प्रतिशत 18 प्रतिशत है, तो कूलर का विक्रय मूल्य कितना है ?",
                    options_en: [" Rs 5420 ", " Rs 5310 ", 
                                " Rs 5530 ", " Rs 5270 "],
                    options_hi: [" 5420 रुपए ", " 5310 रुपए ",
                                " 5530 रुपए ", " 5270 रुपए"],
                    solution_en: "12.(b)<br />SP of cooler = 4500 × <math display=\"inline\"><mfrac><mrow><mn>118</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = Rs. 5310",
                    solution_hi: "12.(b)<br />कूलर का विक्रय मूल्य  = 4500 × <math display=\"inline\"><mfrac><mrow><mn>118</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = रु.5310",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "13. The cost price of 16 articles is equal to the selling price of 18 articles. Find the percentage loss in the transaction.",
                    question_hi: "13. 16 वस्तुओं का क्रय मूल्य, 18 वस्तुओं के विक्रय मूल्य के बराबर है। लेन-देन में हानि प्रतिशत ज्ञात कीजिए।",
                    options_en: [" 12% ", " 13.5% ", 
                                " 14.2% ", " 11.11%"],
                    options_hi: [" 12% ", " 13.5% ",
                                " 14.2% ", " 11.11%"],
                    solution_en: "13.(d) <br />According to the question,<br />16CP = 18SP<br />CP : SP  = 9 : 8<br />So, loss % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> × 100 = 11.11%",
                    solution_hi: "13.(d) <br />प्रश्न के अनुसार,<br />16 × क्रय मूल्य = 18 × विक्रय मूल्य <br />क्रय मूल्य : विक्रय मूल्य = 9 : 8<br />तो, हानि % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> × 100 = 11.11%",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "14. The ratio of cost price and selling price of a fan is 8:9 respectively. What is the profit percentage? ",
                    question_hi: "14. एक पंखे के क्रय मूल्य और विक्रय मूल्य का अनुपात क्रमशः 8:9 है। लाभ प्रतिशत कितना है?",
                    options_en: [" 18.5 percent ", " 35 percent ", 
                                " 10 percent ", " 12.5 percent "],
                    options_hi: [" 18.5 प्रतिशत ", " 35 प्रतिशत",
                                " 10 प्रतिशत", " 12.5 प्रतिशत"],
                    solution_en: "14.(d)<br />Ratio of CP and SP = 8 : 9<br />Profit % = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>-</mo><mn>8</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> × 100 = 12.5%",
                    solution_hi: "14.(d)<br />क्रय मूल्य और विक्रय मूल्य का अनुपात = 8 : 9<br />लाभ % = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>-</mo><mn>8</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> × 100 = 12.5%",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "15. The difference between cost price and selling price is Rs. 204. If loss percentage is 24 percent, then what is the selling price? ",
                    question_hi: "15. क्रय मूल्य और विक्रय मूल्य के बीच का अंतर  204 रुपए है। यदि हानि प्रतिशत 24 प्रतिशत है, तो विक्रय मूल्य कितना है?",
                    options_en: [" Rs. 716", " Rs. 608", 
                                " Rs. 650 ", " Rs. 646"],
                    options_hi: [" 716 रुपए", " 608 रुपए",
                                " 650 रुपए", " 646 रुपए"],
                    solution_en: "15.(d)<br />According to the question,<br />(difference) 24% = 204<br />(CP) 100% = <math display=\"inline\"><mfrac><mrow><mn>204</mn></mrow><mrow><mn>2</mn><mn>4</mn></mrow></mfrac></math> × 100 = 850<br />SP = 850 × <math display=\"inline\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>24</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 646 Rs",
                    solution_hi: "15.(d)<br />प्रश्न के अनुसार<br />(अन्तर) 24% = 204<br />(क्रय मूल्य) 100% = <math display=\"inline\"><mfrac><mrow><mn>204</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> × 100 = 850<br />विक्रय मूल्य = 850 × <math display=\"inline\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>24</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 646 Rs",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>