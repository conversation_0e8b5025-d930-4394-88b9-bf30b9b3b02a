<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>UNICEF builds partnerships across the <span style=\"text-decoration: underline;\">global community on accelerate</span> gender equality.</p>",
                    question_hi: "<p>1. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>UNICEF builds partnerships across the <span style=\"text-decoration: underline;\">global community on accelerate</span> gender equality.</p>",
                    options_en: ["<p>global community to accelerate</p>", "<p>global community till accelerate</p>", 
                                "<p>global community in accelerate</p>", "<p>global community with accelerate</p>"],
                    options_hi: ["<p>global community to accelerate</p>", "<p>global community till accelerate</p>",
                                "<p>global community in accelerate</p>", "<p>global community with accelerate</p>"],
                    solution_en: "<p>1.(a) global community to accelerate<br>&lsquo;To + V<sub>1</sub>&rsquo; is the correct grammatical structure. Hence, &lsquo;global community to accelerate(V<sub>1</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(a) global community to accelerate<br>&lsquo;To + V<sub>1</sub>&rsquo; सही Grammatical structure है। अतः, &lsquo;global community to accelerate(V<sub>1</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Parts of the given sentence have been underlined. One of them contains an error. The underlined parts are given as options with some changes. Select the option that correctly rectifies the error.<br><span style=\"text-decoration: underline;\">Raveena&rsquo;s daughter</span> Kavya is <span style=\"text-decoration: underline;\">most intelligent</span> student and <span style=\"text-decoration: underline;\">she consistently</span> achieves <span style=\"text-decoration: underline;\">top grades.</span></p>",
                    question_hi: "<p>2. Parts of the given sentence have been underlined. One of them contains an error. The underlined parts are given as options with some changes. Select the option that correctly rectifies the error.<br><span style=\"text-decoration: underline;\">Raveena&rsquo;s daughter</span> Kavya is <span style=\"text-decoration: underline;\">most intelligent</span> student and <span style=\"text-decoration: underline;\">she consistently</span> achieves <span style=\"text-decoration: underline;\">top grades.</span></p>",
                    options_en: ["<p>an intelligent</p>", "<p>topper grades</p>", 
                                "<p>she has consistently</p>", "<p>Raveenas&rsquo; daughter</p>"],
                    options_hi: ["<p>an intelligent</p>", "<p>topper grades</p>",
                                "<p>she has consistently</p>", "<p>Raveenas&rsquo; daughter</p>"],
                    solution_en: "<p>2.(a) an intelligent<br>&lsquo;Most&rsquo; will be removed as there is no comparison between the intelligence of kavya and other students. Article &lsquo;an&rsquo; will be used as the word &lsquo;intelligence&rsquo; begins with a vowel sound. Hence, &lsquo;an intelligent&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(a) an intelligent<br>&lsquo;Most&rsquo; हटा दिया जाएगा क्योंकि काव्या तथा अन्य छात्रों की बुद्धिमत्ता (intelligence) के बीच कोई Comparison नहीं है। &lsquo;intelligence&rsquo; एक Vowel sound से प्रारंभ होता है इसलिए Article &lsquo;an&rsquo; का प्रयोग किया जाएगा। अतः, &lsquo;an intelligent&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. The given sentence is divided into three segments. Select the option that has the segment with a grammatical error. If there is no error, select ‘No error’.<br />The volunteer gave on to / the food packets to the / children of the orphanage.",
                    question_hi: "3. The given sentence is divided into three segments. Select the option that has the segment with a grammatical error. If there is no error, select ‘No error’.<br />The volunteer gave on to / the food packets to the / children of the orphanage.",
                    options_en: [" No error ", " The volunteer gave on to ", 
                                " children of the orphanage. ", " the food packets to the "],
                    options_hi: [" No error ", " The volunteer gave on to ",
                                " children of the orphanage. ", " the food packets to the "],
                    solution_en: "3.(b) The volunteer gave on to <br />The phrase ‘gave on to’ must be replaced with the phrasal verb ‘gave out’ which means to distribute something among a number of people. The given sentence talks about the distribution of food packets. Hence, ‘The volunteer gave out’ is the most appropriate answer.",
                    solution_hi: "3.(b) The volunteer gave on to <br />Phrase ‘gave on to’ के स्थान पर Phrasal verb ‘gave out’ का प्रयोग होगा जिसका अर्थ है कई लोगों के बीच कुछ वितरित (distribute) करना। दिया गया Sentence भोजन के पैकेटों के वितरण के बारे में बात करता है। अतः, ‘The volunteer gave out’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. The given sentence is divided into four segments. Select the option that has the segment with a grammatical error.<br>It was my niece / who was leaving college / last year, and / not my son.</p>",
                    question_hi: "<p>4. The given sentence is divided into four segments. Select the option that has the segment with a grammatical error.<br>It was my niece / who was leaving college / last year, and / not my son.</p>",
                    options_en: ["<p>who was leaving college</p>", "<p>It was my niece</p>", 
                                "<p>last year, and</p>", "<p>not my son.</p>"],
                    options_hi: ["<p>who was leaving college</p>", "<p>It was my niece</p>",
                                "<p>last year, and</p>", "<p>not my son.</p>"],
                    solution_en: "<p>4.(a) who was leaving college <br>The word &lsquo;last year&rsquo; indicates the use of simple past tense. &lsquo;Left&rsquo; is the past form(V<sub>2</sub>) of &lsquo;leave&rsquo;. Hence, &lsquo;who left(V<sub>2</sub>) college&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(a) who was leaving college <br>Word &lsquo;last year&rsquo;, Simple past tense के प्रयोग को दर्शाता है। &lsquo;Left&rsquo;, &lsquo;leave&rsquo; का Past form(V<sub>2</sub>) है। अतः, &lsquo;who left(V<sub>2</sub>) college&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 5.</p>",
                    question_hi: "<p>5.<strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 5.</p>",
                    options_en: ["<p>book</p>", "<p>manual</p>", 
                                "<p>guide</p>", "<p>text</p>"],
                    options_hi: ["<p>book</p>", "<p>manual</p>",
                                "<p>guide</p>", "<p>text</p>"],
                    solution_en: "<p>5.(a) book<br>The given passage states that it has now become my favourite book. Hence &lsquo;book&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(a) book<br>दिए गए Passage में कहा गया है कि यह अब मेरी Favourite book बन गई है। अतः &lsquo;book&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 6.</p>",
                    question_hi: "<p>6. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 6.</p>",
                    options_en: ["<p>very</p>", "<p>finally</p>", 
                                "<p>nicely</p>", "<p>properly</p>"],
                    options_hi: ["<p>very</p>", "<p>finally</p>",
                                "<p>nicely</p>", "<p>properly</p>"],
                    solution_en: "<p>6.(a) very<br>&lsquo;Very&rsquo; is an adverb of degree which means extremely. The given sentence states that it is about a boy named Charlie who is very poor. Hence, &lsquo;very&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(a) very<br>&lsquo;Very&rsquo; एक Adverb of degree है जिसका अर्थ है अत्यंत (extremely)। दिए गए Sentence में कहा गया है कि यह चार्ली नाम के एक लड़के के बारे में है जो बहुत गरीब (very poor) है। अतः, &lsquo;very&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 7.</p>",
                    question_hi: "<p>7. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 7.</p>",
                    options_en: ["<p>allows</p>", "<p>suffers</p>", 
                                "<p>gets</p>", "<p>bears</p>"],
                    options_hi: ["<p>allows</p>", "<p>suffers</p>",
                                "<p>gets</p>", "<p>bears</p>"],
                    solution_en: "<p>7.(a) allows<br>&lsquo;Allows&rsquo; means to give permission to do something. The given passage states that he wins a golden ticket that allows him to enter the most famous chocolate factory in the world. Hence, &lsquo;allows&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(a) allows<br>&lsquo;Allows&rsquo; का अर्थ है किसी काम को करने की अनुमति देना। दिए गए Passage में कहा गया है कि वह एक Golden ticket जीतता है जो उसे विश्व की सबसे प्रसिद्ध Chocolate factory में प्रवेश करने की अनुमति देता है। अतः, &lsquo;allows&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 8.</p>",
                    question_hi: "<p>8. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 8.</p>",
                    options_en: ["<p>examines</p>", "<p>visits</p>", 
                                "<p>inspects</p>", "<p>explores</p>"],
                    options_hi: ["<p>examines</p>", "<p>visits</p>",
                                "<p>inspects</p>", "<p>explores</p>"],
                    solution_en: "<p>8.(b) visits<br>&lsquo;Visit&rsquo; means to go to a place for a short time. The given passage states that he visits it with his grandfather. Hence, &lsquo;visits&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(b) visits<br>&lsquo;Visit&rsquo; का अर्थ है किसी स्थान पर थोड़े समय के लिए जाना। दिए गए Passage में कहा गया है कि वह अपने दादा (grandfather) के साथ उस स्थान पर जाता है। अतः, &lsquo;visits&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 9.</p>",
                    question_hi: "<p>9. <strong>Cloze Test:</strong><br>In the following passage, some words have been deleted. Select the most appropriate option for each blank.<br>I was reading a book two days back. It was titled Charlie and the Chocolate Factory by Roland Dahl. It was fantastic. It has now become my favourite (5) ________. It is about a boy named Charlie who is (6) ________ poor. He wins a golden ticket that (7) ________ him to enter the most famous chocolate factory in the world. He (8) ________ it with his grandfather. But the other kids who accompany him are nasty. They are all spoilt. But you have to read the book to (9) ________ out more about them. The amazing part of the book is that it is like a dream come true.<br>Select the most appropriate option to fill in blank no. 9.</p>",
                    options_en: ["<p>find</p>", "<p>unlearn</p>", 
                                "<p>spot</p>", "<p>locate</p>"],
                    options_hi: ["<p>find</p>", "<p>unlearn</p>",
                                "<p>spot</p>", "<p>locate</p>"],
                    solution_en: "<p>9.(a) find<br>&lsquo;Find&rsquo; means to discover by study. The given passage states that you have to read the book to find out more about them. Hence, &lsquo;find&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(a) find<br>&lsquo;Find&rsquo; का अर्थ है अध्ययन करके खोजना। दिए गए Passage में कहा गया है कि आपको उनके बारे में अधिक जानने के लिए पुस्तक पढ़नी होगी। अतः, &lsquo;find&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Select the most appropriate option to fill in the blank.<br />I find that students nowadays are not interested in _________ letters by hand.",
                    question_hi: "10. Select the most appropriate option to fill in the blank.<br />I find that students nowadays are not interested in _________ letters by hand.",
                    options_en: [" writing", " rating", 
                                " rioting", " righting"],
                    options_hi: [" writing", " rating",
                                " rioting", " righting"],
                    solution_en: "10.(a) writing<br />The given sentence states that I find that students nowadays are not interested in writing letters by hand. Hence, ‘writing’ is the most appropriate answer.",
                    solution_hi: "10.(a) writing<br />दिए गए Sentence में कहा गया है कि मुझे लगता है कि आजकल (nowadays) के छात्र हाथ से पत्र लिखने में रुचि (interest) नहीं रखते हैं। अतः, ‘writing’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the sentence that brings out the most appropriate meaning of the idiom<br>&lsquo;Dog in the manger&rsquo;.</p>",
                    question_hi: "<p>11. Select the sentence that brings out the most appropriate meaning of the idiom<br>&lsquo;Dog in the manger&rsquo;.</p>",
                    options_en: ["<p>Mohit was so loyal and committed to his company that he rejected lucrative offers from different companies.</p>", "<p>Rachit had some important work and so he cancelled the show tickets for everyone.</p>", 
                                "<p>To prove this is not a trap, I want to check the credibility of your plan.</p>", "<p>You are here just because you have recognised your hidden potential.</p>"],
                    options_hi: ["<p>Mohit was so loyal and committed to his company that he rejected lucrative offers from different companies.</p>", "<p>Rachit had some important work and so he cancelled the show tickets for everyone.</p>",
                                "<p>To prove this is not a trap, I want to check the credibility of your plan.</p>", "<p>You are here just because you have recognised your hidden potential.</p>"],
                    solution_en: "<p>11.(b) Rachit had some important work and so he cancelled the show tickets for everyone. <br><strong>Dog in the manger-</strong> a person who prevents others from having something they cannot use.</p>",
                    solution_hi: "<p>11.(b) Rachit had some important work and so he cancelled the show tickets for everyone. <br><strong>Dog in the manger-</strong> a person who prevents others from having something they cannot use./एक व्यक्ति जो दूसरों को ऐसी चीज़ रखने से रोकता है जिसका वे उपयोग नहीं कर सकते।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate synonym of the given word.<br>Alert</p>",
                    question_hi: "<p>12. Select the most appropriate synonym of the given word.<br>Alert</p>",
                    options_en: ["<p>Dull</p>", "<p>Smart</p>", 
                                "<p>Inactive</p>", "<p>Vigilant</p>"],
                    options_hi: ["<p>Dull</p>", "<p>Smart</p>",
                                "<p>Inactive</p>", "<p>Vigilant</p>"],
                    solution_en: "<p>12.(d) <strong>Vigilant-</strong> ​very careful to notice any signs of danger or trouble.<br><strong>Alert- </strong>being watchful and attentive.<br><strong>Dull-</strong> boring or uninteresting.<br><strong>Smart-</strong> intelligent or clever.<br><strong>Inactive-</strong> not being engaged in any activity.</p>",
                    solution_hi: "<p>12.(d) <strong>Vigilant </strong>(सतर्क) - ​very careful to notice any signs of danger or trouble.<br><strong>Alert </strong>(सजग) - being watchful and attentive.<br><strong>Dull </strong>(सुस्त) - boring or uninteresting.<br><strong>Smart </strong>(होशियार) - intelligent or clever.<br><strong>Inactive </strong>(निष्क्रिय) - not being engaged in any activity.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate synonym of the given word.<br>Retract</p>",
                    question_hi: "<p>13. Select the most appropriate synonym of the given word.<br>Retract</p>",
                    options_en: ["<p>Revoke</p>", "<p>Disturb</p>", 
                                "<p>Please</p>", "<p>Implement</p>"],
                    options_hi: ["<p>Revoke</p>", "<p>Disturb</p>",
                                "<p>Please</p>", "<p>Implement</p>"],
                    solution_en: "<p>13.(a) <strong>Revoke-</strong> to take back a decision.<br><strong>Retract-</strong> to take back or withdraw something.<br><strong>Disturb-</strong> to interfere with someone&rsquo;s peace.<br><strong>Please-</strong> cause to feel happy or satisfied.<br><strong>Implement-</strong> to put a plan or agreement into action.</p>",
                    solution_hi: "<p>13.(a) <strong>Revoke </strong>(निरस्त करना) - to take back a decision.<br><strong>Retract </strong>(वापस लेना) - to take back or withdraw something.<br><strong>Disturb </strong>(परेशान करना) - to interfere with someone&rsquo;s peace.<br><strong>Please </strong>(कृपया) - cause to feel happy or satisfied.<br><strong>Implement </strong>(लागू करना) - to put a plan or agreement into action.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate ANTONYM of the given word.<br>Enormous</p>",
                    question_hi: "<p>14. Select the most appropriate ANTONYM of the given word.<br>Enormous</p>",
                    options_en: ["<p>Strong</p>", "<p>Minute</p>", 
                                "<p>Tremendous</p>", "<p>Monstrous</p>"],
                    options_hi: ["<p>Strong</p>", "<p>Minute</p>",
                                "<p>Tremendous</p>", "<p>Monstrous</p>"],
                    solution_en: "<p>14.(b) <strong>Minute-</strong> something very small or tiny.<br><strong>Enormous-</strong> very large or huge in size.<br><strong>Strong-</strong> having great physical power.<br><strong>Tremendous- </strong>very large, great or intense.<br><strong>Monstrous-</strong> inhumanly or outrageously evil or wrong.</p>",
                    solution_hi: "<p>14.(b) <strong>Minute </strong>(सूक्ष्म) - something very small or tiny.<br><strong>Enormous </strong>(विशाल) - very large or huge in size.<br><strong>Strong </strong>(बलवान) - having great physical power.<br><strong>Tremendous </strong>(ज़बरदस्त) - very large, great or intense.<br><strong>Monstrous </strong>(राक्षसी) - inhumanly or outrageously evil or wrong.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate meaning of the underlined idiom.<br>The technicians who were repairing the broken elevator <span style=\"text-decoration: underline;\">called it a day.</span></p>",
                    question_hi: "<p>15. Select the most appropriate meaning of the underlined idiom.<br>The technicians who were repairing the broken elevator <span style=\"text-decoration: underline;\">called it a day.</span></p>",
                    options_en: ["<p>Stopped to take a short break</p>", "<p>Felt unhappy</p>", 
                                "<p>Stopped work for the day</p>", "<p>Shouted with anger</p>"],
                    options_hi: ["<p>Stopped to take a short break</p>", "<p>Felt unhappy</p>",
                                "<p>Stopped work for the day</p>", "<p>Shouted with anger</p>"],
                    solution_en: "<p>15.(c) <strong>Called it a day-</strong> stopped work for the day</p>",
                    solution_hi: "<p>15.(c) <strong>Called it a day-</strong> stopped work for the day./दिन भर के लिए काम बंद कर देना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the option that will improve the underlined part of the given sentence.<br><span style=\"text-decoration: underline;\">Allusive handwriting</span> creates a poor impression.</p>",
                    question_hi: "<p>16. Select the option that will improve the underlined part of the given sentence.<br><span style=\"text-decoration: underline;\">Allusive handwriting</span> creates a poor impression.</p>",
                    options_en: ["<p>Eligible handwriting</p>", "<p>Descriptive handwriting</p>", 
                                "<p>Illegible handwriting</p>", "<p>Legal handwriting</p>"],
                    options_hi: ["<p>Eligible handwriting</p>", "<p>Descriptive handwriting</p>",
                                "<p>Illegible handwriting</p>", "<p>Legal handwriting</p>"],
                    solution_en: "<p>16.(c) Illegible handwriting<br>&lsquo;Illegible handwriting&rsquo; means writing that is difficult to read because it is unclear. The given sentence states that illegible handwriting creates a poor impression. Hence, &lsquo;Illegible handwriting&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>16.(c) Illegible handwriting<br>&lsquo;Illegible handwriting&rsquo; का अर्थ है ऐसी Writing जिसे पढ़ना मुश्किल है क्योंकि यह अस्पष्ट (unclear) है। दिए गए Sentence में कहा गया है कि अस्पष्ट लिखावट (illegible handwriting) एक खराब प्रभाव (poor impression) उत्पन्न करता है। अतः, &lsquo;Illegible handwriting&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the most appropriate synonym of the underlined word.<br>For Rana, unable to see a thing, the <span style=\"text-decoration: underline;\">parade</span> seemed to last forever.</p>",
                    question_hi: "<p>17. Select the most appropriate synonym of the underlined word.<br>For Rana, unable to see a thing, the <span style=\"text-decoration: underline;\">parade</span> seemed to last forever.</p>",
                    options_en: ["<p>darkness</p>", "<p>procession</p>", 
                                "<p>concealment</p>", "<p>hiding</p>"],
                    options_hi: ["<p>darkness</p>", "<p>procession</p>",
                                "<p>concealment</p>", "<p>hiding</p>"],
                    solution_en: "<p>17.(b) <strong>Procession-</strong> a group of people walking together for a special event or ceremony.<br><strong>Parade-</strong> a public procession, especially one celebrating a special day or event.<br><strong>Darkness-</strong> absence of light.<br><strong>Concealment-</strong> the act of hiding something.<br><strong>Hiding-</strong> keeping someone or something out of sight.</p>",
                    solution_hi: "<p>17.(b) <strong>Procession </strong>(जुलूस) - a group of people walking together for a special event or ceremony.<br><strong>Parade </strong>(जुलूस) - a public procession, especially one celebrating a special day or event.<br><strong>Darkness </strong>(अंधकार) - absence of light.<br><strong>Concealment </strong>(छिपाना) - the act of hiding something.<br><strong>Hiding </strong>(छिपाना) - keeping someone or something out of sight.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate option to correct the sentence by using the denotation of the underlined word.<br>Mr. Sen is <span style=\"text-decoration: underline;\">clever</span> enough to handle his professional hazards.</p>",
                    question_hi: "<p>18. Select the most appropriate option to correct the sentence by using the denotation of the underlined word.<br>Mr. Sen is <span style=\"text-decoration: underline;\">clever</span> enough to handle his professional hazards.</p>",
                    options_en: ["<p>Bold</p>", "<p>Intelligent</p>", 
                                "<p>Callous</p>", "<p>Foolish</p>"],
                    options_hi: ["<p>Bold</p>", "<p>Intelligent</p>",
                                "<p>Callous</p>", "<p>Foolish</p>"],
                    solution_en: "<p>18.(b) Intelligent<br>&lsquo;Intelligent&rsquo; means someone who is smart or knowledgeable. The given sentence states that Mr. Sen is intelligent enough to handle his professional hazards. Hence, &lsquo;intelligent&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>18.(b) Intelligent<br>&lsquo;Intelligent&rsquo; का अर्थ है कोई ऐसा व्यक्ति जो होशियार या जानकार हो। दिए गए Sentence में कहा गया है कि श्री सेन अपने पेशेवर खतरों (professional hazards) को संभालने के लिए पर्याप्त बुद्धिमान हैं। अतः, &lsquo;intelligent&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "19. Select the INCORRECTLY spelt word in the given sentence.<br />The Chinese are believed to have a longer history than that of any other civilisation in the wolrd.",
                    question_hi: "19. Select the INCORRECTLY spelt word in the given sentence.<br />The Chinese are believed to have a longer history than that of any other civilisation in the wolrd.",
                    options_en: [" Wolrd", " Chinese ", 
                                " Believed ", " Civilisation"],
                    options_hi: [" Wolrd", " Chinese ",
                                " Believed ", " Civilisation"],
                    solution_en: "19.(a) Wolrd<br />‘World’ is the correct spelling.",
                    solution_hi: "19.(a) Wolrd<br />‘World’ सही spelling है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>The housing loan taken <span style=\"text-decoration: underline;\">by Ramesh effected</span> his daily expenses adversely.</p>",
                    question_hi: "<p>20. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>The housing loan taken <span style=\"text-decoration: underline;\">by Ramesh effected</span> his daily expenses adversely.</p>",
                    options_en: ["<p>from Ramesh effected</p>", "<p>of Ramesh effected</p>", 
                                "<p>by Ramesh affected</p>", "<p>for Ramesh effected</p>"],
                    options_hi: ["<p>from Ramesh effected</p>", "<p>of Ramesh effected</p>",
                                "<p>by Ramesh affected</p>", "<p>for Ramesh effected</p>"],
                    solution_en: "<p>20.(c) by Ramesh affected<br>&lsquo;Effected&rsquo; must be replaced with &lsquo;affected&rsquo; as the given sentence needs a verb. Hence &lsquo;by Ramesh affected&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>20.(c) by Ramesh affected<br>&lsquo;Effected&rsquo; के स्थान पर &lsquo;affected&rsquo; का प्रयोग होना चाहिए क्योंकि दिए गए Sentence में एक Verb की आवश्यकता है। अतः &lsquo;by Ramesh affected&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Select the option that will improve the underlined part of the given sentence. In case no improvement is needed, select No improvement required.<br>Although uncountable files stack up on the tables of government officials, they prefer doing their work <span style=\"text-decoration: underline;\">at a more leisure pace.</span></p>",
                    question_hi: "<p>21. Select the option that will improve the underlined part of the given sentence. In case no improvement is needed, select No improvement required.<br>Although uncountable files stack up on the tables of government officials, they prefer doing their work <span style=\"text-decoration: underline;\">at a more leisure pace.</span></p>",
                    options_en: ["<p>No improvement needed</p>", "<p>at a more leisure paced</p>", 
                                "<p>at a more leisurely pace</p>", "<p>at a more leisurely paced</p>"],
                    options_hi: ["<p>No improvement needed</p>", "<p>at a more leisure paced</p>",
                                "<p>at a more leisurely pace</p>", "<p>at a more leisurely paced</p>"],
                    solution_en: "<p>21.(c) at a more leisurely pace<br>The given sentence needs an adjective to describe the noun &lsquo;pace&rsquo;. &lsquo;More leisurely&rsquo; is the comparative form of the adjective &lsquo;leisurely&rsquo;. Hence, &lsquo;at a more leisurely pace&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(c) at a more leisurely pace<br>दिए गए वाक्य में Noun &lsquo;pace&rsquo; को Describe करने के लिए एक Adjective की आवश्यकता है। &lsquo;More leisurely&rsquo;, Adjective &lsquo;leisurely&rsquo; का Comparative form है। अतः, &lsquo;at a more leisurely pace&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. Select the option that rectifies the spelling of the underlined word in the given sentence.<br>It was a <span style=\"text-decoration: underline;\">privvilege</span> to receive the award from the President.</p>",
                    question_hi: "<p>22. Select the option that rectifies the spelling of the underlined word in the given sentence.<br>It was a <span style=\"text-decoration: underline;\">privvilege</span> to receive the award from the President.</p>",
                    options_en: ["<p>privillege</p>", "<p>privilegge</p>", 
                                "<p>privileege</p>", "<p>privilege</p>"],
                    options_hi: ["<p>privillege</p>", "<p>privilegge</p>",
                                "<p>privileege</p>", "<p>privilege</p>"],
                    solution_en: "<p>22.(d) privilege<br>&lsquo;Privilege&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>22.(d) privilege<br>&lsquo;Privilege&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "23. Select the most appropriate option that can replace the bracketed word segment in the following sentence.<br />Classical buildings in ancient Greek and Roman times were (characteristically built) from marble or some other attractive, durable stone.",
                    question_hi: "23. Select the most appropriate option that can replace the bracketed word segment in the following sentence.<br />Classical buildings in ancient Greek and Roman times were (characteristically built) from marble or some other attractive, durable stone.",
                    options_en: [" strange build ", " typically built ", 
                                " fascinating to build ", " mainly having been built"],
                    options_hi: [" strange build ", " typically built ",
                                " fascinating to build ", " mainly having been built"],
                    solution_en: "23.(b) typically built<br />‘Typically’ means in a way that shows the characteristics of a particular kind of thing.  The given sentence states that classical buildings in ancient aGreek and Roman times were typically built from marble or some other attractive, durable stone. Hence, ‘typically built’ is the most appropriate answer.",
                    solution_hi: "23.(b) typically built<br />‘Typically’ का अर्थ है एक तरह से जो किसी विशेष प्रकार की चीज़ की विशेषताओं को दर्शाता है। दिए गए Sentence में कहा गया है कि प्राचीन ग्रीक तथा रोमन काल में शास्त्रीय इमारतें (classical buildings) आमतौर पर संगमरमर या किसी अन्य आकर्षक, टिकाऊ पत्थर (durable stone) से बनाई जाती थीं। अतः, ‘typically built’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. Select the most appropriate ANTONYM of the given word.<br>Sluggish</p>",
                    question_hi: "<p>24. Select the most appropriate ANTONYM of the given word.<br>Sluggish</p>",
                    options_en: ["<p>Hassled</p>", "<p>Leisurely</p>", 
                                "<p>Rapid</p>", "<p>Lethargic</p>"],
                    options_hi: ["<p>Hassled</p>", "<p>Leisurely</p>",
                                "<p>Rapid</p>", "<p>Lethargic</p>"],
                    solution_en: "<p>24.(c) <strong>Rapid- </strong>happening very quickly.<br><strong>Sluggish-</strong> moving slowly.<br><strong>Hassled- </strong>being bothered or annoyed.<br><strong>Leisurely-</strong> done in a relaxed manner.<br><strong>Lethargic-</strong> lacking energy or enthusiasm.</p>",
                    solution_hi: "<p>24.(c) <strong>Rapid </strong>(तेजी से) - happening very quickly.<br><strong>Sluggish </strong>(सुस्त) - moving slowly.<br><strong>Hassled </strong>(परेशान) - being bothered or annoyed.<br><strong>Leisurely </strong>(इत्मीनान से) - done in a relaxed manner.<br><strong>Lethargic </strong>(सुस्त) - lacking energy or enthusiasm.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Select the most appropriate ANTONYM of the given word.<br>Timid</p>",
                    question_hi: "<p>25. Select the most appropriate ANTONYM of the given word.<br>Timid</p>",
                    options_en: ["<p>Resolute</p>", "<p>Assertive</p>", 
                                "<p>Audacious</p>", "<p>Blunt</p>"],
                    options_hi: ["<p>Resolute</p>", "<p>Assertive</p>",
                                "<p>Audacious</p>", "<p>Blunt</p>"],
                    solution_en: "<p>25.(c) <strong>Audacious-</strong> showing a willingness to take surprisingly bold risks.<br><strong>Timid-</strong> easily frightened.<br><strong>Resolute-</strong> firmly resolved or determined.<br><strong>Assertive- </strong>behaving confidently and not being frightened to say what you want or believe.<br><strong>Blunt-</strong> being very direct and straightforward.</p>",
                    solution_hi: "<p>25.(c) <strong>Audacious </strong>(साहसी) - showing a willingness to take surprisingly bold risks.<br><strong>Timid </strong>(डरपोक) - easily frightened.<br><strong>Resolute </strong>(धीर/संकल्पवान) - firmly resolved or determined.<br><strong>Assertive </strong>(मुखर) - behaving confidently and not being frightened to say what you want or believe.<br><strong>Blunt </strong>(मुँहफट) - being very direct and straightforward.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>