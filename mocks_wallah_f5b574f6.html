<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["6"] = {
                name: "Reasoning",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "1. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br />The teacher was / taken back by / the audacity of / the new student.<br />A B C D   ",
                    question_hi: "1. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br />The teacher was / taken back by / the audacity of / the new student.<br />A B C D   ",
                    options_en: [" B ", " C ", 
                                " D ", " A"],
                    options_hi: [" B ", " C ",
                                " D ", " A"],
                    solution_en: "1.(a) B - taken back by<br />‘Taken back by’ must be replaced with ‘taken aback by’. The phrase ‘to be taken aback’ means to be very shocked or surprised. The given sentence states that the teacher was shocked by the audacity of the new student. Hence, ‘taken aback by’ is the most appropriate answer.",
                    solution_hi: "1.(a) B - taken back by<br />‘Taken back by’ के स्थान पर ‘taken aback by’ का प्रयोग होगा। Phrase ‘to be taken aback’ का अर्थ है बहुत हैरान होना (shocked) या चौंक जाना (surprised)। दिए गए sentence में कहा गया है कि teacher नए छात्र की दुस्साहसता (audacity) से हैरान था। अतः, ‘taken aback by’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "2. Select the most appropriate article to fill in the blank.<br /> _________ bed you sleep in is broken. ",
                    question_hi: "2. Select the most appropriate article to fill in the blank.<br /> _________ bed you sleep in is broken. ",
                    options_en: [" On", " The ", 
                                " An", " A"],
                    options_hi: [" On", " The ",
                                " An", " A"],
                    solution_en: "2.(b) The<br />‘Bed’ mentioned in the given sentence is specific and we generally use the definite article ‘the’ before any specific or particular noun. Hence, ‘the bed’ is the most appropriate answer. ",
                    solution_hi: "2.(b) The<br />दिए गए sentence में उल्लिखित (mentioned) ‘Bed’ specific है। और हम सामान्यतः किसी specific या particular noun से पहले definite article ‘the’ का प्रयोग करते हैं। अतः, ‘the bed’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. Select the option that will improve the given sentence by using the most appropriate verb. <br />That Macbook belong to Xavier. ",
                    question_hi: "3. Select the option that will improve the given sentence by using the most appropriate verb. <br />That Macbook belong to Xavier. ",
                    options_en: [" That Macbook belonging to Xavier. ", " That Macbook is belong to Xavier. ", 
                                " That Macbook belongs to Xavier.  ", " That Macbook is belonged to Xavier."],
                    options_hi: [" That Macbook belonging to Xavier. ", " That Macbook is belong to Xavier. ",
                                " That Macbook belongs to Xavier.  ", " That Macbook is belonged to Xavier."],
                    solution_en: "3.(c) That Macbook belongs to Xavier.<br />According to the “Subject-Verb Agreement Rule”, a singular subject always takes a singular verb and a plural subject always takes a plural verb. At the end of a singular verb, s/es is used. In the given sentence, ‘That Macbook’ is a singular subject that will take ‘belongs’ as a singular verb. Hence, the sentence given in option (c) is correct.",
                    solution_hi: "3.(c) That Macbook belongs to Xavier.<br />“Subject-Verb Agreement Rule” के अनुसार, singular subject के साथ हमेशा singular verb का प्रयोग होता है और  plural subject के साथ हमेशा plural verb का प्रयोग होता है। Singular verb  के अंत में s/es का प्रयोग होता है। दिए गए sentence में, ‘That Macbook’ एक singular subject है जिसके साथ ‘belongs’ singular verb का प्रयोग होगा। अतः, option (c) में दिया गया sentence सही है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Identify the grammatical error in the following sentence and select the correct tense from the options given below. <br>I have met my friend yesterday</p>",
                    question_hi: "<p>4. Identify the grammatical error in the following sentence and select the correct tense from the options given below. <br>I have met my friend yesterday</p>",
                    options_en: ["<p>was meeting</p>", "<p>have been meeting</p>", 
                                "<p>had been meeting</p>", "<p>met my friend</p>"],
                    options_hi: ["<p>was meeting</p>", "<p>have been meeting</p>",
                                "<p>had been meeting</p>", "<p>met my friend</p>"],
                    solution_en: "<p>4.(d) met my friend<br>We generally use &lsquo;simple past tense(V<sub>2</sub>)&rsquo; to describe actions, events, or states that occurred and were completed in the past. Similarly, in the given sentence, the action was completed yesterday. Hence, &lsquo;met(V<sub>2</sub>) my friend&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(d) met my friend<br>हम सामान्यतः Past में घटित और पूर्ण हो चुके कार्यों (Actions), घटनाओं (events) या स्थितियों (states) को describe करने के लिए &lsquo;simple past tense(V<sub>2</sub>)&rsquo; का प्रयोग करते हैं। इसी तरह, दिए गए sentence में, action, yesterday को complete हुआ था। अतः, &lsquo;met(V<sub>2</sub>) my friend&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <strong>Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 5.</p>",
                    question_hi: "<p>5. <strong>Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 5.</p>",
                    options_en: ["<p>planned</p>", "<p>believed</p>", 
                                "<p>hoped</p>", "<p>wished</p>"],
                    options_hi: ["<p>planned</p>", "<p>believed</p>",
                                "<p>hoped</p>", "<p>wished</p>"],
                    solution_en: "<p>5.(b) believed<br>&lsquo;Believed&rsquo; means held as an opinion. The given passage states that it is generally believed that the methodology of teaching-learning is the concern of teachers only. Hence, &lsquo;believed&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(b) believed<br>&lsquo;Believed&rsquo; का अर्थ है एक राय (opinion) के रूप में रखा जाना। दिए गए passage में बताया गया है कि आमतौर पर यह माना जाता है कि शिक्षण-अधिगम (teaching-learning) की पद्धति (methodology) केवल शिक्षकों की चिंता है। अतः, &lsquo;believed&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 6</p>",
                    question_hi: "<p>6. <strong>Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 6</p>",
                    options_en: ["<p>sparse</p>", "<p>little</p>", 
                                "<p>light</p>", "<p>minute</p>"],
                    options_hi: ["<p>sparse</p>", "<p>little</p>",
                                "<p>light</p>", "<p>minute</p>"],
                    solution_en: "<p>6.(b) little<br>&lsquo;Little&rsquo; means almost nothing. The given passage states that learners have little or nothing to do with it. Hence, &lsquo;little&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(b) little<br>&lsquo;Little&rsquo; का अर्थ है लगभग कुछ भी नहीं (nothing)। दिए गए passage में बताया गया है कि शिक्षार्थियों (learners) का इससे बहुत कम या कोई लेना-देना नहीं है। अतः, &lsquo;little&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 7.</p>",
                    question_hi: "<p>7.<strong> Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 7.</p>",
                    options_en: ["<p>forms</p>", "<p>customs</p>", 
                                "<p>breaks</p>", "<p>methods</p>"],
                    options_hi: ["<p>forms</p>", "<p>customs</p>",
                                "<p>breaks</p>", "<p>methods</p>"],
                    solution_en: "<p>7.(d) methods<br>&lsquo;Method&rsquo; is a particular way of doing something. The given passage states that the teaching-learning methods are concerns of learners as well. Hence, &lsquo;methods&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(d) methods<br>&lsquo;Method&rsquo; किसी काम को करने का एक विशेष तरीका (particular way) है। दिए गए passage में बताया गया है कि शिक्षण-अधिगम विधियाँ (teaching-learning methods) शिक्षार्थियों की भी चिंता का विषय हैं। अतः, &lsquo;methods&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 8</p>",
                    question_hi: "<p>8. <strong>Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 8</p>",
                    options_en: ["<p>recurrence</p>", "<p>relevance</p>", 
                                "<p>reference</p>", "<p>consistency</p>"],
                    options_hi: ["<p>recurrence</p>", "<p>relevance</p>",
                                "<p>reference</p>", "<p>consistency</p>"],
                    solution_en: "<p>8.(b) relevance<br>&lsquo;Relevance&rsquo; means the state of being closely connected or appropriate. The given passage states that it has more relevance for the learners of physical education. Hence, &lsquo;relevance&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(b) relevance<br>&lsquo;Relevance&rsquo; का अर्थ है निकटता से जुड़े या उपयुक्त होने की स्थिति। दिए गए passage में बताया गया है कि शारीरिक शिक्षा (physical education) के शिक्षार्थियों के लिए इसकी अधिक प्रासंगिकता (relevance) है। अतः, &lsquo;relevance&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 9.</p>",
                    question_hi: "<p>9. <strong>Cloze Test:</strong><br>It is generally (5)_______ that the methodology of teaching-learning is the concern of teachers only. Learners have (6) ____ or nothing to do with it. But this is not true. The teaching-learning (7) ______ are concerns of learners as well. Knowing and understanding that how different subjects are taught are important for the learners of all subjects, but it has more (8) ______ for the learners of physical education. When we talk about you as learners of physical education, we mean that you have actually participated in the subject area, rather than (9) ______ studied it. <br>Select the most appropriate option to fill in blank no. 9.</p>",
                    options_en: ["<p>purely</p>", "<p>entirely</p>", 
                                "<p>merely</p>", "<p>hardly</p>"],
                    options_hi: ["<p>purely</p>", "<p>entirely</p>",
                                "<p>merely</p>", "<p>hardly</p>"],
                    solution_en: "<p>9.(c) merely<br>&lsquo;Merely&rsquo; means just. The given passage states that we mean that you have actually participated in the subject area, rather than merely studied it. Hence, &lsquo;merely&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(c) merely<br>&lsquo;Merely&rsquo; का अर्थ है केवल (just)। दिए गए passage में बताया गया है कि हमारा तात्पर्य यह है कि आपने विषय क्षेत्र (subject area) में वास्तव में भाग लिया (participate) है, न कि केवल उसका अध्ययन (studied) किया है। अतः, &lsquo;merely&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Select the most appropriate option to fill in the blank. <br />She could easily eat the _________ biryani by herself. ",
                    question_hi: "10. Select the most appropriate option to fill in the blank. <br />She could easily eat the _________ biryani by herself. ",
                    options_en: [" hole", " haul ", 
                                " whole ", " hall"],
                    options_hi: [" hole", " haul ",
                                " whole ", " hall"],
                    solution_en: "10.(c) whole<br />‘Whole’ means all of something. The given sentence states that she could easily eat the whole biryani by herself. Hence, ‘whole’ is the most appropriate answer.",
                    solution_hi: "10.(c) whole<br />‘Whole’ का अर्थ है किसी चीज़ का पूरा हिस्सा। दिए गए sentence में कहा गया है कि वह पूरी बिरयानी (biryani) अकेले ही आसानी से खा सकती है। अतः, ‘whole’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate synonym of the underlined word. <br>Let us <span style=\"text-decoration: underline;\">assume</span> that we will do the job honestly</p>",
                    question_hi: "<p>11. Select the most appropriate synonym of the underlined word. <br>Let us <span style=\"text-decoration: underline;\">assume</span> that we will do the job honestly</p>",
                    options_en: ["<p>think</p>", "<p>take</p>", 
                                "<p>allow</p>", "<p>give</p>"],
                    options_hi: ["<p>think</p>", "<p>take</p>",
                                "<p>allow</p>", "<p>give</p>"],
                    solution_en: "<p>11.(a) <strong>Think</strong>- to believe something or have an opinion or idea.<br><strong>Assume</strong>- to accept or believe that something is true even though you have no proof.<br><strong>Allow</strong>- to give permission to do something.</p>",
                    solution_hi: "<p>11.(a) <strong>Think </strong>(सोचना) - to believe something or have an opinion or idea.<br><strong>Assume </strong>(मान लेना) - to accept or believe that something is true even though you have no proof.<br><strong>Allow </strong>(अनुमति देना) - to give permission to do something.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate synonym of the given word. <br>Remission</p>",
                    question_hi: "<p>12. Select the most appropriate synonym of the given word. <br>Remission</p>",
                    options_en: ["<p>Retribution</p>", "<p>Overlook</p>", 
                                "<p>Punishment</p>", "<p>Pardon</p>"],
                    options_hi: ["<p>Retribution</p>", "<p>Overlook</p>",
                                "<p>Punishment</p>", "<p>Pardon</p>"],
                    solution_en: "<p>12.(d) <strong>Pardon</strong>- forgive or excuse (a person, error, or offence).<br><strong>Remission</strong>- a reduction of the time that a person has to stay in prison.<br><strong>Retribution</strong>- punishment for something morally wrong that was done.<br><strong>Overlook</strong>- to fail to see or notice something.<br><strong>Punishment</strong>- the infliction of some kind of pain or loss upon a person for a misdeed.</p>",
                    solution_hi: "<p>12.(d) <strong>Pardon </strong>(क्षमा) - forgive or excuse (a person, error, or offence).<br><strong>Remission </strong>(माफी/छूट) - a reduction of the time that a person has to stay in prison.<br><strong>Retribution </strong>(प्रतिशोध) - punishment for something morally wrong that was done.<br><strong>Overlook </strong>(अनदेखा करना) - to fail to see or notice something.<br><strong>Punishment </strong>(सज़ा) - the infliction of some kind of pain or loss upon a person for a misdeed.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the option that rectifies the spelling of the underlined word in the given sentence. <br>It has been a usual <span style=\"text-decoration: underline;\">ocurrence</span> for a few days now.</p>",
                    question_hi: "<p>13. Select the option that rectifies the spelling of the underlined word in the given sentence. <br>It has been a usual ocurrence for a few days now.</p>",
                    options_en: ["<p>occurrance</p>", "<p>occurence</p>", 
                                "<p>occurrence</p>", "<p>occerrence</p>"],
                    options_hi: ["<p>occurrance</p>", "<p>occurence</p>",
                                "<p>occurrence</p>", "<p>occerrence</p>"],
                    solution_en: "<p>13.(c) occurrence<br>&lsquo;Occurrence&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>13.(c) occurrence<br>&lsquo;Occurrence&rsquo; सही spelling है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate option that can substitute the underlined segment in the given sentence.&nbsp;<br>It will take two hours to walk <span style=\"text-decoration: underline;\">across</span> the forest.</p>",
                    question_hi: "<p>14. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>It will take two hours to walk <span style=\"text-decoration: underline;\">across</span> the forest.</p>",
                    options_en: ["<p>between</p>", "<p>over</p>", 
                                "<p>away</p>", "<p>through</p>"],
                    options_hi: ["<p>between</p>", "<p>over</p>",
                                "<p>away</p>", "<p>through</p>"],
                    solution_en: "<p>14.(d) through<br>&lsquo;Through&rsquo; means from one side to the other. The given sentence states that it will take two hours to walk through the forest. Hence, &lsquo;through&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>14.(d) through<br>&lsquo;Through&rsquo; का अर्थ है एक तरफ (side) से दूसरी तरफ। दिए गए Sentence में कहा गया है कि जंगल (forest) से होकर चलने में दो घंटे (two hours) लगेंगे। अतः, &lsquo;through&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br>The plot of the novel is very <span style=\"text-decoration: underline;\">dull</span>.</p>",
                    question_hi: "<p>15. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br>The plot of the novel is very <span style=\"text-decoration: underline;\">dull</span>.</p>",
                    options_en: ["<p>Riveting</p>", "<p>Diverting</p>", 
                                "<p>Plodding</p>", "<p>Charming</p>"],
                    options_hi: ["<p>Riveting</p>", "<p>Diverting</p>",
                                "<p>Plodding</p>", "<p>Charming</p>"],
                    solution_en: "<p>15.(a) <strong>Riveting</strong>- extremely interesting or exciting.<br><strong>Dull</strong>- not interesting or exciting. <br><strong>Diverting</strong>- to change the direction or course of action.<br><strong>Plodding</strong>- slow-moving and unexciting.<br><strong>Charming</strong>- very pleasing or attractive</p>",
                    solution_hi: "<p>15.(a) <strong>Riveting </strong>(दिलचस्प) - extremely interesting or exciting.<br><strong>Dull </strong>(नीरस) - not interesting or exciting. <br><strong>Diverting </strong>(भटकाना) - to change the direction or course of action.<br><strong>Plodding </strong>(धीमी गति से) - slow-moving and unexciting.<br><strong>Charming </strong>(आकर्षक) - very pleasing or attractive</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No improvement required&rsquo;. <br>We <span style=\"text-decoration: underline;\">set up</span> on the journey early in the morning.</p>",
                    question_hi: "<p>16. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No improvement required&rsquo;. <br>We <span style=\"text-decoration: underline;\">set up</span> on the journey early in the morning.</p>",
                    options_en: ["<p>set about</p>", "<p>No improvement required</p>", 
                                "<p>set in</p>", "<p>set off</p>"],
                    options_hi: ["<p>set about</p>", "<p>No improvement required</p>",
                                "<p>set in</p>", "<p>set off</p>"],
                    solution_en: "<p>16.(d) set off<br>&lsquo;Set off&rsquo; is the correct phrasal verb to use here. &lsquo;Set off&rsquo; means to begin a journey. The given sentence states that we set off on the journey early in the morning. Hence, &lsquo;set off&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>16.(d) set off<br>&lsquo;Set off&rsquo; सही phrasal verb है। &lsquo;Set off&rsquo; का अर्थ है यात्रा (journey) शुरू करना। दिए गए sentence में बताया गया है कि हम सुबह जल्दी ही यात्रा पर निकल पड़े। अतः, &lsquo;set off&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Read the sentence carefully and select the most appropriate option to substitute the underlined part.&nbsp;<br>You can be sentenced for <span style=\"text-decoration: underline;\">trespassing</span> on someone else&rsquo;s property</p>",
                    question_hi: "<p>17. Read the sentence carefully and select the most appropriate option to substitute the underlined part.&nbsp;<br>You can be sentenced for <span style=\"text-decoration: underline;\">trespassing</span> on someone else&rsquo;s property</p>",
                    options_en: ["<p>conspiring against others</p>", "<p>leaving without permission</p>", 
                                "<p>entering without permission</p>", "<p>showing unselfish devotion</p>"],
                    options_hi: ["<p>conspiring against others</p>", "<p>leaving without permission</p>",
                                "<p>entering without permission</p>", "<p>showing unselfish devotion</p>"],
                    solution_en: "<p>17.(c) <strong>Trespassing </strong>- entering without permission.</p>",
                    solution_hi: "<p>17.(c) <strong>Trespassing </strong>- entering without permission./बिना अनुमति के प्रवेश करना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate idiomatic expression that can substitute the underlined segment in the given sentence.:&nbsp;<br>It feels like I <span style=\"text-decoration: underline;\">made a promise of more than my ability</span> when I promised to complete this worksheet in one day.</p>",
                    question_hi: "<p>18. Select the most appropriate idiomatic expression that can substitute the underlined segment in the given sentence.: <br>It feels like I <span style=\"text-decoration: underline;\">made a promise of more than my ability</span> when I promised to complete this worksheet in one day.</p>",
                    options_en: ["<p>bit off more than I could chew</p>", "<p>beat the drum</p>", 
                                "<p>beat my brain out</p>", "<p>burnt a hole in my pocket</p>"],
                    options_hi: ["<p>bit off more than I could chew</p>", "<p>beat the drum</p>",
                                "<p>beat my brain out</p>", "<p>burnt a hole in my pocket</p>"],
                    solution_en: "<p>18.(a) <strong>Bit off more than I could chew</strong> - made a promise of more than my ability.</p>",
                    solution_hi: "<p>18.(a) <strong>Bit off more than I could chew</strong> - made a promise of more than my ability./अपनी क्षमता से अधिक का वादा करना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br>Few people turned up to attend the <span style=\"text-decoration: underline;\">tawdry</span> promo of the new movie.</p>",
                    question_hi: "<p>19. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br>Few people turned up to attend the <span style=\"text-decoration: underline;\">tawdry</span> promo of the new movie.</p>",
                    options_en: ["<p>Elegant</p>", "<p>Showy</p>", 
                                "<p>Gawdy</p>", "<p>Inferior</p>"],
                    options_hi: ["<p>Elegant</p>", "<p>Showy</p>",
                                "<p>Gawdy</p>", "<p>Inferior</p>"],
                    solution_en: "<p>19.(a) <strong>Elegant</strong>- graceful and stylish in appearance or manner.<br><strong>Tawdry</strong>- unpleasant and immoral.<br><strong>Showy</strong>- having a striking appearance or style.<br><strong>Inferior</strong>- lower in quality, rank, or importance.</p>",
                    solution_hi: "<p>19.(a) <strong>Elegant </strong>(सुरुचिपूर्ण) - graceful and stylish in appearance or manner.<br><strong>Tawdry </strong>(अप्रिय) - unpleasant and immoral.<br><strong>Showy </strong>(दिखावटी) - having a striking appearance or style.<br><strong>Inferior </strong>(निम्न/घटिया) - lower in quality, rank, or importance.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "20. Identify the incorrectly spelt word and select its correct spelling. <br />Maybe he wanted to make sure he didn’t embarrese her.",
                    question_hi: "20. Identify the incorrectly spelt word and select its correct spelling. <br />Maybe he wanted to make sure he didn’t embarrese her.",
                    options_en: [" embarrass", " emberess ", 
                                " shure", " sware"],
                    options_hi: [" embarrass", " emberess ",
                                " shure", " sware"],
                    solution_en: "20.(a) embarrass<br />‘Embarrass’ is the correct spelling.",
                    solution_hi: "20.(a) embarrass<br />‘Embarrass’ सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>The wizard, while performing an item, accidentally set his own hat on fire and prompted <span style=\"text-decoration: underline;\">the fire alarm to go on</span>.</p>",
                    question_hi: "<p>21. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>The wizard, while performing an item, accidentally set his own hat on fire and prompted <span style=\"text-decoration: underline;\">the fire alarm to go on</span>.</p>",
                    options_en: ["  the fire alarm to go above", "  the fire alarm to go off ", 
                                "  the fire alarm to go under", " the fire alarm to go away"],
                    options_hi: ["  the fire alarm to go above", "  the fire alarm to go off ",
                                "  the fire alarm to go under", " the fire alarm to go away"],
                    solution_en: "21.(b) the fire alarm to go off<br />The phrasal verb ‘go off’ means to start to ring loudly. The given sentence talks about the alarm ringing loudly due to the hat on fire. Hence, ‘the fire alarm to go off’ is the most appropriate answer.",
                    solution_hi: "21.(b) the fire alarm to go off<br />Phrasal verb ‘go off’ का अर्थ है जोर से बजना (ring loudly)। दिया गया sentence टोपी में आग (fire) लगने के कारण अलार्म के जोर से बजने (ringing) के बारे में बात करता है। अत:, ‘the fire alarm to go off’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. Select the most appropriate option that collocates with &lsquo;wish&rsquo; to fill in the blank. <br>The genie asked Aladdin to ______ a wish.</p>",
                    question_hi: "<p>22. Select the most appropriate option that collocates with &lsquo;wish&rsquo; to fill in the blank. <br>The genie asked Aladdin to ______ a wish.</p>",
                    options_en: ["<p>make</p>", "<p>take</p>", 
                                "<p>ask</p>", "<p>tell</p>"],
                    options_hi: ["<p>make</p>", "<p>take</p>",
                                "<p>ask</p>", "<p>tell</p>"],
                    solution_en: "<p>22.(a) Make<br>&lsquo;Make&rsquo; is the correct verb to use with &lsquo;wish&rsquo;. The phrase &lsquo;make a wish&rsquo; means to wish for something. The given sentence states that the genie asked Aladdin to make a wish. Hence, &lsquo;make&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) Make<br>&lsquo;Wish&rsquo; के साथ प्रयोग करने के लिए &lsquo;Make&rsquo; सही verb है। Phrase &lsquo;make a wish&rsquo; का अर्थ है किसी चीज़ की इच्छा करना। दिए गए sentence में कहा गया है कि जिन्न (genie) ने अलादीन (Aladdin) से इच्छा करने के लिए कहा। अतः, &lsquo;make&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. Select the most appropriate ANTONYM of the underlined word in the following sentence. The <span style=\"text-decoration: underline;\">upshot</span> of the consultation with the doctor was an operation scheduled for the next week.</p>",
                    question_hi: "<p>23. Select the most appropriate ANTONYM of the underlined word in the following sentence. The <span style=\"text-decoration: underline;\">upshot</span> of the consultation with the doctor was an operation scheduled for the next week.</p>",
                    options_en: ["<p>Cause</p>", "<p>Blame</p>", 
                                "<p>Estimate</p>", "<p>Result</p>"],
                    options_hi: ["<p>Cause</p>", "<p>Blame</p>",
                                "<p>Estimate</p>", "<p>Result</p>"],
                    solution_en: "<p>23.(a) <strong>Cause</strong>- someone or something that makes something else happen.<br><strong>Upshot</strong>- something that happens as a result of other actions, events or decisions.<br><strong>Blame</strong>- to think or say that a certain person or thing is responsible for something bad that has happened.<br><strong>Estimate</strong>- a guess or judgement about something.</p>",
                    solution_hi: "<p>23.(a) <strong>Cause </strong>(कारण) - someone or something that makes something else happen.<br><strong>Upshot </strong>(परिणाम) - something that happens as a result of other actions, events or decisions.<br><strong>Blame </strong>(दोष) - to think or say that a certain person or thing is responsible for something bad that has happened.<br><strong>Estimate </strong>(अनुमान) - a guess or judgement about something.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. Select the most appropriate ANTONYM of the word given in the bracket to fill in the blank. The State media houses present ______ (vociferous) views compared to private media.</p>",
                    question_hi: "<p>24. Select the most appropriate ANTONYM of the word given in the bracket to fill in the blank. The State media houses present ______ (vociferous) views compared to private media.</p>",
                    options_en: ["<p>assertive</p>", "<p>biased</p>", 
                                "<p>political</p>", "<p>mild</p>"],
                    options_hi: ["<p>assertive</p>", "<p>biased</p>",
                                "<p>political</p>", "<p>mild</p>"],
                    solution_en: "<p>24.(d) <strong>mild</strong>- not severe, serious, or harsh.<br><strong>Vociferous</strong>- expressing your opinions or feelings in a loud and confident way.<br><strong>Assertive</strong>- confident and able to express oneself clearly .<br><strong>Biased</strong>- unfairly prejudiced for or against someone or something.<br><strong>Political</strong>- relating to public affairs of a country.</p>",
                    solution_hi: "<p>24.(d) <strong>mild </strong>(सौम्य) - not severe, serious, or harsh.<br><strong>Vociferous </strong>( कोलाहलपूर्ण) - expressing your opinions or feelings in a loud and confident way.<br><strong>Assertive </strong>(निश्चयात्मक) - confident and able to express oneself clearly .<br><strong>Biased </strong>(पक्षपाती) - unfairly prejudiced for or against someone or something.<br><strong>Political </strong>(राजनीतिक) - relating to public affairs of a country.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. Select the most appropriate meaning of the given idiom. <br>On its last legs</p>",
                    question_hi: "<p>25. Select the most appropriate meaning of the given idiom. <br>On its last legs</p>",
                    options_en: ["<p>Last choice</p>", "<p>In a bad condition</p>", 
                                "<p>Creeping on legs</p>", "<p>Slow movement</p>"],
                    options_hi: ["<p>Last choice</p>", "<p>In a bad condition</p>",
                                "<p>Creeping on legs</p>", "<p>Slow movement</p>"],
                    solution_en: "<p>25.(a) <strong>On its last legs</strong> - in a bad condition.<br>E.g.- The old car has been breaking down frequently. It is really on its last legs.</p>",
                    solution_hi: "<p>25.(a) <strong>On its last legs</strong> - in a bad condition./खराब स्थिति में। <br>E.g.- The old car has been breaking down frequently. It is really on its last legs.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p>26. Select the letter-cluster pair that best represents a similar relationship to the one expressed in the pairs of letter-clusters given below. <br>GKI : MQO <br>CGE : IMK</p>",
                    question_hi: "<p>26. उस अक्षर-समूह युग्म का चयन कीजिए जो नीचे दिए गए अक्षर-समूह युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br>GKI : MQO <br>CGE : IMK</p>",
                    options_en: ["<p>HLJ : NRO</p>", "<p>RUT : VZX</p>", 
                                "<p>JOM : PUR</p>", "<p>LPN : RVT</p>"],
                    options_hi: ["<p>HLJ : NRO</p>", "<p>RUT : VZX</p>",
                                "<p>JOM : PUR</p>", "<p>LPN : RVT</p>"],
                    solution_en: "<p>26.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382225691.png\" alt=\"rId29\" width=\"120\" height=\"73\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382225809.png\" alt=\"rId30\" width=\"120\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382225958.png\" alt=\"rId31\" width=\"120\"></p>",
                    solution_hi: "<p>26.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382225691.png\" alt=\"rId29\" width=\"120\" height=\"73\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382225809.png\" alt=\"rId30\" width=\"120\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382225958.png\" alt=\"rId31\" width=\"120\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "6",
                    question_en: "<p>27. Each of the letters in the word FRAMED is arranged in alphabetical order. How many letters are there in the English alphabetical series between the letter which is second from the left and the one which is second from the right in the new letter cluster thus formed?</p>",
                    question_hi: "<p>27. शब्द FRAMED के प्रत्येक अक्षर को वर्णमाला के क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर समूह में बाएँ से दूसरे अक्षर और दाएँ से दूसरे अक्षर के बीच अँग्रेजी वर्णमाला श्रृंखला में कितने अक्षर हैं?</p>",
                    options_en: ["<p>Eight</p>", "<p>Seven</p>", 
                                "<p>Nine</p>", "<p>Six</p>"],
                    options_hi: ["<p>आठ</p>", "<p>सात</p>",
                                "<p>नौ</p>", "<p>छः</p>"],
                    solution_en: "<p>27.(a) <strong>Given </strong>:- FRAMED. After arranging in the alphabetical order we get<br>ADEFMR . Second from the left is D and second from the right is M. There are 8 letters between D and M in English alphabetical order.</p>",
                    solution_hi: "<p>27.(a) <strong>दिया गया:</strong>- FRAMED वर्णमाला क्रम में व्यवस्थित करने पर हमें प्राप्त होता है<br>ADEFMR <br>बाएं से दूसरा D है और दाएं से दूसरा M है। अंग्रेजी वर्णमाला क्रम में D और M के बीच 8 अक्षर हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "6",
                    question_en: "<p>28. What will come in the place of &lsquo;?&rsquo; in the following equation if &lsquo;+&rsquo; and &lsquo;&times;&rsquo; are interchanged and &lsquo;-&rsquo; and &lsquo;&divide;&rsquo; are interchanged?&nbsp;<br>90 - 10 &times; 36 + 52 &divide; 49 = ?</p>",
                    question_hi: "<p>28. यदि \'+\' और \'&times;\' को आपस में बदल दिया जाए तथा \'-\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा?<br>90 - 10 &times; 36 + 52 &divide; 49 = ?</p>",
                    options_en: ["<p>1832</p>", "<p>567</p>", 
                                "<p>654</p>", "<p>879</p>"],
                    options_hi: ["<p>1832</p>", "<p>567</p>",
                                "<p>654</p>", "<p>879</p>"],
                    solution_en: "<p>28.(a) <strong>Given </strong>:- 90 - 10 &times; 36 + 52 &divide;&nbsp;49<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo; and &lsquo;&divide;&rsquo; we get<br>90 &divide;&nbsp;10 + 36 &times; 52 - 49<br>9 + 1872 - 49 = 1832</p>",
                    solution_hi: "<p>28.(a) <strong>दिया गया</strong> :- 90 - 10 &times; 36 + 52 &divide;&nbsp;49<br>दिए गए निर्देश के अनुसार \'+\' और \'&times;\' तथा \'-\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>90 &divide;&nbsp;10 + 36 &times; 52 - 49<br>9 + 1872 - 49 = 1832</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "6",
                    question_en: "<p>29. Which of the following numbers will replace the question mark(?) in the given series ?<br>12 60 20 100 60 300 260 ?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>12 60 20 100 60 300 260 ?</p>",
                    options_en: ["<p>1300</p>", "<p>1240</p>", 
                                "<p>1169</p>", "<p>1280</p>"],
                    options_hi: ["<p>1300</p>", "<p>1240</p>",
                                "<p>1169</p>", "<p>1280</p>"],
                    solution_en: "<p>29.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226101.png\" alt=\"rId32\" width=\"300\"></p>",
                    solution_hi: "<p>29.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226101.png\" alt=\"rId32\" width=\"300\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "6",
                    question_en: "<p>30. In a certain code language, &lsquo;BROW&rsquo; is coded as &lsquo;7542&rsquo; and &lsquo;WORM&rsquo; is coded as &lsquo;7295&rsquo;. What is the code for &lsquo;B&rsquo; in the given code language?</p>",
                    question_hi: "<p>30. एक निश्चित कूट भाषा में, \'BROW\' को \'7542\' लिखा जाता है और \'WORM\' को \'7295\' लिखा जाता है। उसी कूट भाषा में \'B\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>2</p>", "<p>9</p>", 
                                "<p>7</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>9</p>",
                                "<p>7</p>", "<p>4</p>"],
                    solution_en: "<p>30.(d) BROW &rarr; 7542&hellip;&hellip;. (i)<br>WORM &rarr; 7295&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;ORW&rsquo; and &lsquo;752&rsquo; are common. The code of &lsquo;B&rsquo; = &lsquo;4&rsquo;.</p>",
                    solution_hi: "<p>30.(d) BROW &rarr; 7542&hellip;&hellip;. (i)<br>WORM &rarr; 7295&hellip;&hellip;.(ii)<br>(i) और (ii) से \'ORW\' और \'752\' उभयनिष्ठ हैं। \'B\' का कोड = \'4\'.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "6",
                    question_en: "<p>31. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226259.png\" alt=\"rId33\" width=\"150\"></p>",
                    question_hi: "<p>31. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226259.png\" alt=\"rId33\" width=\"150\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226351.png\" alt=\"rId34\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226445.png\" alt=\"rId35\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226541.png\" alt=\"rId36\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226631.png\" alt=\"rId37\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226351.png\" alt=\"rId34\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226445.png\" alt=\"rId35\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226541.png\" alt=\"rId36\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226631.png\" alt=\"rId37\"></p>"],
                    solution_en: "<p>31.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226351.png\" alt=\"rId34\"></p>",
                    solution_hi: "<p>31.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226351.png\" alt=\"rId34\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "6",
                    question_en: "<p>32. &lsquo;A + B&rsquo; means &lsquo;A is the brother of B&rsquo;. <br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the sister of B&rsquo;. <br>&lsquo;A &times; B&rsquo; means &lsquo;A is the father of B&rsquo;. <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the mother of B&rsquo;. <br>Using the same meaning of the mathematical operators as given above, which of the following shows&lsquo;P is the father&rsquo;s father of S&rsquo;?</p>",
                    question_hi: "<p>32. &lsquo;A + B\' का अर्थ है \'A, B का भाई है\'।<br>\'A - B\' का अर्थ है \'A, B की बहन है\'।<br>\'A &times; B\' का अर्थ है \'A, B के पिता है\'।<br>\'A &divide; B\' का अर्थ है \'A, B की माँ है\'।<br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि निम्नलिखित में से कौन-सा विकल्प यह प्रदर्शित करता है कि \'P, S के पिता के पिता है\'?</p>",
                    options_en: ["<p>P + Q &ndash; R &divide; S</p>", "<p>P &ndash; Q + R &divide; S</p>", 
                                "<p>P &times; Q &times; R + S</p>", "<p>P + Q &divide; R &times; S</p>"],
                    options_hi: ["<p>P + Q &ndash; R &divide; S</p>", "<p>P &ndash; Q + R &divide; S</p>",
                                "<p>P &times; Q &times; R + S</p>", "<p>P + Q &divide; R &times; S</p>"],
                    solution_en: "<p>32.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226733.png\" alt=\"rId38\" width=\"125\"><br>P is the father of S&rsquo;s father.</p>",
                    solution_hi: "<p>32.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226733.png\" alt=\"rId38\" width=\"125\"><br>P, S के पिता का पिता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "6",
                    question_en: "<p>33. Each of the letters in the word JUSTICE are arranged in alphabetical order. How many letters are there in the English alphabetical series between the letter that is second from the left and the one that is fourth from the right in the new letter cluster thus formed ?</p>",
                    question_hi: "<p>33. JUSTICE शब्द के प्रत्येक अक्षर को वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर समूह में बाएं से दूसरे अक्षर और दाएं से चौथे अक्षर के बीच अंग्रेजी वर्णमाला श्रृंखला में कितने अक्षर होंगे?</p>",
                    options_en: ["<p>6</p>", "<p>7</p>", 
                                "<p>5</p>", "<p>4</p>"],
                    options_hi: ["<p>6</p>", "<p>7</p>",
                                "<p>5</p>", "<p>4</p>"],
                    solution_en: "<p>33.(d) <strong>Given </strong>:- JUSTICE. After arranging in the alphabetical order we get<br>CEIJSTU. Second from the left is E and fourth from the right is J.Number of letters between E and J is 4.</p>",
                    solution_hi: "<p>33.(d) <strong>दिया गया :</strong>- JUSTICE . वर्णमाला क्रम में व्यवस्थित करने पर हमें प्राप्त होता है CEIJSTU<br>बाएं से दूसरा E है और दाएं से चौथा J है। E और J के बीच अक्षरों की संख्या 4 है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "6",
                    question_en: "<p>34. Select the correct mirror image of the given figure when the mirror is placed at MN. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226831.png\" alt=\"rId39\" width=\"120\"></p>",
                    question_hi: "<p>34. जब दर्पण को MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226831.png\" alt=\"rId39\" width=\"120\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226915.png\" alt=\"rId40\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226979.png\" alt=\"rId41\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227120.png\" alt=\"rId42\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227210.png\" alt=\"rId43\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226915.png\" alt=\"rId40\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382226979.png\" alt=\"rId41\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227120.png\" alt=\"rId42\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227210.png\" alt=\"rId43\"></p>"],
                    solution_en: "<p>34.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227120.png\" alt=\"rId42\"></p>",
                    solution_hi: "<p>34.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227120.png\" alt=\"rId42\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "6",
                    question_en: "<p>35. If \'A\' stands for \'&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for -, what will come in place of the question mark (?) in the following equation?<br>11 B 11 D 68 A 4 C 4 = ?</p>",
                    question_hi: "35. यदि \'A\' का अर्थ \'÷\', \'B\' का अर्थ \'×\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ \'-\' है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br />11 B 11 D 68 A 4 C 4 = ?",
                    options_en: [" 108 ", " 104 ", 
                                "  102", " 106"],
                    options_hi: [" 108 ", " 104 ",
                                "  102", " 106"],
                    solution_en: "<p>35.(a) <strong>Given </strong>:- 11 B 11 D 68 A 4 C 4<br>As per given instruction after interchanging the letter with sign we get<br>11 &times; 11 - 68 &divide;&nbsp;4 + 4<br>121 - 17 + 4 = 108</p>",
                    solution_hi: "<p>35.(a) <strong>दिया गया:-</strong> 11 B 11 D 68 A 4 C 4<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>11 &times; 11 - 68 &divide;&nbsp;4 + 4<br>121 - 17 + 4 = 108</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "6",
                    question_en: "<p>36. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>(18, 7, 119) <br>(11, 9, 90)</p>",
                    question_hi: "<p>36. उस समुच्चय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्चयों की संख्याएं संबंधित हैं।<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(18, 7, 119) <br>(11, 9, 90)</p>",
                    options_en: ["<p>(16, 5, 65)</p>", "<p>(13, 5, 58)</p>", 
                                "<p>(17, 6, 102)</p>", "<p>(12, 4, 44) </p>"],
                    options_hi: ["<p>(16, 5, 65)</p>", "<p>(13, 5, 58)</p>",
                                "<p>(17, 6, 102)</p>", "<p>(12, 4, 44)</p>"],
                    solution_en: "<p>36.(d) <strong>Logic :- </strong>(1st number &times; 2nd number) - (2nd number) = 3rd number<br>(18, 7 ,119) :- (18 &times; 7) - (7) &rArr; (126) - (7) = 119<br>(11, 9, 90) :- (11 &times; 9) - (9) &rArr; (99) - (9) = 90<br>Similarly,<br>(12, 4 ,44) :- (12 &times; 4) - (4) &rArr; (48) - (4) = 44</p>",
                    solution_hi: "<p>36.(d) <strong>तर्क</strong>:- (पहली संख्या &times; दूसरी संख्या) - (दूसरी संख्या) = तीसरी संख्या <br>(18, 7 ,119) :- (18 &times; 7) - (7) &rArr; (126) - (7) = 119<br>(11, 9, 90) :- (11 &times; 9) - (9) &rArr; (99) - (9) = 90<br>इसी प्रकार,<br>(12, 4 ,44) :- (12 &times; 4) - (4) &rArr; (48) - (4) = 44</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "6",
                    question_en: "<p>37. What should come in place of the question mark (?) in the given series based on the English alphabetical order? <br>ECF, JHK, OMP, TRU, ?</p>",
                    question_hi: "<p>37. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आना चाहिए?<br>ECF, JHK, OMP, TRU, ?</p>",
                    options_en: ["<p>YWZ</p>", "<p>YVZ</p>", 
                                "<p>XYZ</p>", "<p>XVZ</p>"],
                    options_hi: ["<p>YWZ</p>", "<p>YVZ</p>",
                                "<p>XYZ</p>", "<p>XVZ</p>"],
                    solution_en: "<p>37.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227322.png\" alt=\"rId44\" width=\"300\"></p>",
                    solution_hi: "<p>37.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227322.png\" alt=\"rId44\" width=\"300\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "6",
                    question_en: "<p>38. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements :</strong> <br>All fire is water. <br>Some water is earth. <br>No earth is air. <br><strong>Conclusion (I) :</strong> Some fire is earth. <br><strong>Conclusion (II) : </strong>Some fire is air.</p>",
                    question_hi: "<p>38. तीन कथनों के बाद ।, ॥ क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथनः</strong><br>सभी आग, पानी है।<br>कुछ पानी, पृथ्वी है।<br>कोई पृथ्वी, वायु नहीं है।<br><strong>निष्कर्ष (I): </strong>कुछ आग, पृथ्वी है।<br><strong>निष्कर्ष (II):</strong> कुछ आग, वायु है।</p>",
                    options_en: ["<p>Only conclusion (I) follows.</p>", "<p>Neither conclusion (I) nor (II) follows.</p>", 
                                "<p>Only conclusion (II) follows.</p>", "<p>Both conclusions (I) and (II) follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>", "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>",
                                "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>", "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>"],
                    solution_en: "<p>38.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227561.png\" alt=\"rId45\" width=\"300\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>38.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227770.png\" alt=\"rId46\" width=\"300\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "6",
                    question_en: "<p>39. In a certain code language, &lsquo;SORT&rsquo; is coded as &lsquo;4698&rsquo; and &lsquo;RUST&rsquo; is coded as 6429&rsquo;. How is &lsquo;O&rsquo; coded in the given language ?</p>",
                    question_hi: "<p>39. एक निश्चित कूट भाषा में, \'SORT\' को \'4698\' लिखा जाता है और \'RUST\' को 6429\' लिखा जाता है। उसी कूट भाषा में \'O\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>4</p>", "<p>9</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>4</p>", "<p>9</p>"],
                    solution_en: "<p>39.(b) SORT &rarr; 4698&hellip;&hellip;. (i)<br>RUST &rarr; 6429&hellip;&hellip;. (ii)<br>From (i) and (ii) &lsquo;SRT&rsquo; and &lsquo;649&rsquo; are common. The code of &lsquo;O&rsquo; = &lsquo;8&rsquo;.</p>",
                    solution_hi: "<p>39.(b) SORT &rarr; 4698&hellip;&hellip;. (i)<br>RUST &rarr; 6429&hellip;&hellip;. (ii)<br>(i) और (ii) से \'SRT\' और \'649\' उभयनिष्ठ हैं। \'O\' का कोड = \'8\'.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "6",
                    question_en: "<p>40. If \'A\' stands for \'&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for &lsquo;-&rsquo;, what will come in place of the question mark (?) in the following equation?<br>95 B 8 D 12 A 2 C 16 = ?</p>",
                    question_hi: "<p>40. यदि \'A\' का अर्थ \'&divide;\', \'B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ \'-\' है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>95 B 8 D 12 A 2 C 16 = ?</p>",
                    options_en: ["<p>770</p>", "<p>730</p>", 
                                "<p>750</p>", "<p>710</p>"],
                    options_hi: ["<p>770</p>", "<p>730</p>",
                                "<p>750</p>", "<p>710</p>"],
                    solution_en: "<p>40.(a) <strong>Given </strong>:- 95 B 8 D 12 A 2 C 16<br>As per given instruction after interchanging the letter with sign we get<br>95 &times; 8 - 12 &divide;&nbsp;2 + 16<br>760 - 6 + 16 = 770</p>",
                    solution_hi: "<p>40.(a) <strong>दिया गया </strong>:- 95 B 8 D 12 A 2 C 16<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>95 &times; 8 - 12 &divide;&nbsp;2 + 16<br>760 - 6 + 16 = 770</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "6",
                    question_en: "<p>41. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227928.png\" alt=\"rId47\" width=\"250\"></p>",
                    question_hi: "<p>41. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओ में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छिद्र किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382227928.png\" alt=\"rId47\" width=\"250\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228013.png\" alt=\"rId48\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228174.png\" alt=\"rId49\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228298.png\" alt=\"rId50\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228405.png\" alt=\"rId51\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228013.png\" alt=\"rId48\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228174.png\" alt=\"rId49\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228298.png\" alt=\"rId50\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228405.png\" alt=\"rId51\" width=\"90\"></p>"],
                    solution_en: "<p>41.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228013.png\" alt=\"rId48\" width=\"90\"></p>",
                    solution_hi: "<p>41.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228013.png\" alt=\"rId48\" width=\"90\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "6",
                    question_en: "<p>42. 9 is related to 47 following a certain logic. Following the same logic, 12 is related to 62. To which of the following is 17 related, following the same logic ? <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/ subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>42. एक निश्चित तर्क का अनुसरण करते हुए 9 का संबंध 47 से है। समान तर्क का अनुसरण करते हुए 12 का संबंध 62 से है। समान तर्क का अनुसरण करते हुए निम्नलिखित में से किसका संबंध 17 से है?<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>97</p>", "<p>87</p>", 
                                "<p>85</p>", "<p>75</p>"],
                    options_hi: ["<p>97</p>", "<p>87</p>",
                                "<p>85</p>", "<p>75</p>"],
                    solution_en: "<p>42.(b) <strong>Logic </strong>:- (1st number) &times; 5 + 2 = (2nd number)<br>(9, 47) :- (9) &times; 5 + 2 = 47<br>(12, 62) :- (12) &times; 5 + 2 = 62<br>Similarly,<br>(17 , 87) :- (17) &times; 5 + 2 = 87</p>",
                    solution_hi: "<p>42.(b) <strong>तर्क </strong>:- (पहली संख्या) &times; 5 + 2 = (दूसरी संख्या)<br>(9, 47) :- (9) &times; 5 + 2 = 47<br>(12, 62) :- (12) &times; 5 + 2 = 62<br>इसी प्रकार,<br>(17 , 87) :- (17) &times; 5 + 2 = 87</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "6",
                    question_en: "<p>43. How many triangles are there in the following figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228524.png\" alt=\"rId52\" width=\"150\"></p>",
                    question_hi: "<p>43. नीचे दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228524.png\" alt=\"rId52\" width=\"150\"></p>",
                    options_en: ["<p>6</p>", "<p>5</p>", 
                                "<p>8</p>", "<p>7</p>"],
                    options_hi: ["<p>6</p>", "<p>5</p>",
                                "<p>8</p>", "<p>7</p>"],
                    solution_en: "<p>43.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228675.png\" alt=\"rId53\" width=\"150\"><br>There are 8 triangle<br>ABC, ACH, HCG, CDG, DGE, EGF , ACG, CGE</p>",
                    solution_hi: "<p>43.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228675.png\" alt=\"rId53\" width=\"150\"><br>8 त्रिभुज हैं<br>ABC, ACH, HCG, CDG, DGE, EGF , ACG, CGE</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "6",
                    question_en: "<p>44. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228776.png\" alt=\"rId54\" width=\"120\"></p>",
                    question_hi: "<p>44. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228776.png\" alt=\"rId54\" width=\"120\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228891.png\" alt=\"rId55\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228985.png\" alt=\"rId56\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229090.png\" alt=\"rId57\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229170.png\" alt=\"rId58\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228891.png\" alt=\"rId55\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228985.png\" alt=\"rId56\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229090.png\" alt=\"rId57\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229170.png\" alt=\"rId58\"></p>"],
                    solution_en: "<p>44.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228985.png\" alt=\"rId56\"></p>",
                    solution_hi: "<p>44.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382228985.png\" alt=\"rId56\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "6",
                    question_en: "<p>45. What should come in place of ? in the given series based on the English alphabetical order ?<br>SLE CXS ? WVU GHI</p>",
                    question_hi: "<p>45. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में &lsquo;?&rsquo; के स्थान पर क्या आना चाहिए?<br>SLE CXS ? WVU GHI</p>",
                    options_en: ["<p>COR</p>", "<p>HAP</p>", 
                                "<p>OWG</p>", "<p>MJG</p>"],
                    options_hi: ["<p>COR</p>", "<p>HAP</p>",
                                "<p>OWG</p>", "<p>MJG</p>"],
                    solution_en: "<p>45.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229304.png\" alt=\"rId59\" width=\"320\"></p>",
                    solution_hi: "<p>45.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229304.png\" alt=\"rId59\" width=\"320\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "6",
                    question_en: "<p>46. Which figure should replace the question mark (?) if the following figure were to be continued ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229451.png\" alt=\"rId60\" width=\"350\" height=\"90\"></p>",
                    question_hi: "<p>46. यदि निम्नलिखित आकृति श्रृंखला को जारी रखना हो तो कौन-सी आकृति प्रश्न चिन्ह (?) के स्थान पर आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229451.png\" alt=\"rId60\" width=\"350\" height=\"90\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229546.png\" alt=\"rId61\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229639.png\" alt=\"rId62\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229724.png\" alt=\"rId63\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229817.png\" alt=\"rId64\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229546.png\" alt=\"rId61\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229639.png\" alt=\"rId62\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229724.png\" alt=\"rId63\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229817.png\" alt=\"rId64\" width=\"90\"></p>"],
                    solution_en: "<p>46.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229546.png\" alt=\"rId61\" width=\"90\"></p>",
                    solution_hi: "<p>46.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229546.png\" alt=\"rId61\" width=\"90\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "6",
                    question_en: "<p>47. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br>Happy : Ecstatic</p>",
                    question_hi: "<p>47. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>प्रसन्न : आनंदित</p>",
                    options_en: ["<p>Polite : Courteous</p>", "<p>Loyal : Betray</p>", 
                                "<p>Honest : Deceptive</p>", "<p>Progress : Stagnation</p>"],
                    options_hi: ["<p>नम्र : भद्र</p>", "<p>वफादार : विश्वासघात</p>",
                                "<p>ईमानदार : भ्रामक</p>", "<p>प्रगति : ठहराव</p>"],
                    solution_en: "<p>47.(a) As happy and ecstatic are synonyms of each other, similarly Polite and Courteous are synonyms of each other.</p>",
                    solution_hi: "<p>47.(a) जिस प्रकार प्रसन्न और आनंदित एक दूसरे के पर्यायवाची हैं, उसी प्रकार नम्र और भद्र एक दूसरे के पर्यायवाची हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "6",
                    question_en: "<p>48. Select the option in which the given figure is embedded (rotation is NOT allowed)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229929.png\" alt=\"rId65\" width=\"80\"></p>",
                    question_hi: "<p>48. उस विकल्प आकृति को चुनिए जिसमें दी गई आकृति निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382229929.png\" alt=\"rId65\" width=\"80\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230018.png\" alt=\"rId66\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230101.png\" alt=\"rId67\" width=\"100\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230190.png\" alt=\"rId68\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230305.png\" alt=\"rId69\" width=\"100\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230018.png\" alt=\"rId66\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230101.png\" alt=\"rId67\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230190.png\" alt=\"rId68\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230305.png\" alt=\"rId69\" width=\"90\"></p>"],
                    solution_en: "<p>48.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230101.png\" alt=\"rId67\" width=\"100\"></p>",
                    solution_hi: "<p>48.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230101.png\" alt=\"rId67\" width=\"100\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "6",
                    question_en: "<p>49. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group? <br>(Note : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>49. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br>(ध्यान दें : असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>BEG</p>", "<p>JMN</p>", 
                                "<p>TWY</p>", "<p>MPR</p>"],
                    options_hi: ["<p>BEG</p>", "<p>JMN</p>",
                                "<p>TWY</p>", "<p>MPR</p>"],
                    solution_en: "<p>49.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230401.png\" alt=\"rId70\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230491.png\" alt=\"rId71\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230627.png\" alt=\"rId72\" width=\"100\"><br>But, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230740.png\" alt=\"rId73\" width=\"100\"></p>",
                    solution_hi: "<p>49.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230401.png\" alt=\"rId70\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230491.png\" alt=\"rId71\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230627.png\" alt=\"rId72\" width=\"100\"><br>लेकिन, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230740.png\" alt=\"rId73\" width=\"100\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. What should come in place of the question mark (?) in the given series ?<br>3, 10, 24, ?, 73, 108, 150, 199</p>",
                    question_hi: "<p>50. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>3, 10, 24, ?, 73, 108, 150, 199</p>",
                    options_en: ["<p>35</p>", "<p>40</p>", 
                                "<p>45</p>", "<p>30</p>"],
                    options_hi: ["<p>35</p>", "<p>40</p>",
                                "<p>45</p>", "<p>30</p>"],
                    solution_en: "<p>50.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230895.png\" alt=\"rId74\" width=\"350\"></p>",
                    solution_hi: "<p>50.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382230895.png\" alt=\"rId74\" width=\"350\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. In an election between two candidates, one got 65% of the total valid votes. 20% of the votes were invalid. If the total number of votes was 7,500, the number of valid votes that the other candidate got was:</p>",
                    question_hi: "<p>51. दो उम्मीदवारों के बीच एक चुनाव में एक उम्मीदवार को कुल वैध मतों के 65% मत प्राप्त हुए। 20% मत अवैध थे। यदि मतों की कुल संख्या 7,500 थी, तो दूसरे उम्मीदवार को मिले वैध मतों की संख्या ______ थी।</p>",
                    options_en: ["<p>4,300</p>", "<p>1,900</p>", 
                                "<p>2,100</p>", "<p>1,700</p>"],
                    options_hi: ["<p>4,300</p>", "<p>1,900</p>",
                                "<p>2,100</p>", "<p>1,700</p>"],
                    solution_en: "<p>51.(c)&nbsp;Total number of votes = 7500<br>Total number of valid votes = 7500 &times; 80% = 6000 <br>Required number of votes = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2100</p>",
                    solution_hi: "<p>51.(c)&nbsp;कुल वोटों की संख्या = 7500<br>वैध मतों की कुल संख्या = 7500 &times; 80% = 6000 <br>आवश्यक वोटों की संख्या = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2100</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. In ∆PQR, PR = 10 cm. Find the length of PT, where ST∥QR. Given that PS = 6 cm and QS = 14 cm.</p>",
                    question_hi: "<p>52. ∆PQR में, PR = 10 cm है। PT की लंबाई ज्ञात कीजिए, जहाँ ST || QR है। दिया गया है कि PS = 6 cm और QS = 14 cm है।</p>",
                    options_en: ["<p>2 cm</p>", "<p>4 cm</p>", 
                                "<p>1.5 cm</p>", "<p>3 cm</p>"],
                    options_hi: ["<p>2 cm</p>", "<p>4 cm</p>",
                                "<p>1.5 cm</p>", "<p>3 cm</p>"],
                    solution_en: "<p>52.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231068.png\" alt=\"rId75\" width=\"150\" height=\"124\"><br><strong>Thales theorem </strong>: - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PS</mi><mi>PQ</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PT</mi><mi>PR</mi></mfrac></math><br>Now, the length of PT<math display=\"inline\"><mo>&#8594;</mo></math><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mrow><mn>14</mn><mo>+</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PT</mi><mn>10</mn></mfrac></math>, PT = 3 cm</p>",
                    solution_hi: "<p>52.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231068.png\" alt=\"rId75\" width=\"150\" height=\"124\"><br><strong>थेल्स प्रमेय: - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PS</mi><mi>PQ</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PT</mi><mi>PR</mi></mfrac></math></strong><br>अब, PT की लंबाई <math display=\"inline\"><mo>&#8594;</mo></math> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mrow><mn>14</mn><mo>+</mo><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PT</mi><mn>10</mn></mfrac></math>, PT = 3 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Simplify the following : <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>25</mn><mn>44</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>33</mn><mn>15</mn></mfrac><mo>=</mo><mo>?</mo><mo>&#160;</mo></math></p>",
                    question_hi: "<p>53. निम्नलिखित व्यंजक को सरल कीजिए।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>25</mn><mn>44</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>33</mn><mn>15</mn></mfrac><mo>=</mo><mo>?</mo><mo>&#160;</mo></math></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>53.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>25</mn><mn>44</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>33</mn><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>33</mn><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>15</mn><mn>33</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>53.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>25</mn><mn>44</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>33</mn><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>5</mn><mn>4</mn></mfrac><mo>)</mo><mo>&#247;</mo><mfrac><mn>33</mn><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&#215;</mo><mfrac><mn>11</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mn>15</mn><mn>33</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>5</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. A, B and C can complete a work in 12, 15 and 20 days, respectively. How many days are required to finish the work if they work together?</p>",
                    question_hi: "<p>54. A, B और C एक काम को क्रमशः 12, 15 और 20 दिनों में पूरा कर सकते हैं। यदि वे एक साथ मिलकर काम करते हैं तो काम को पूरा करने में उन्हे कितने दिन का समय लगेगा?</p>",
                    options_en: ["<p>4 days</p>", "<p>3 days</p>", 
                                "<p>5 days</p>", "<p>6 days</p>"],
                    options_hi: ["<p>4 दिन</p>", "<p>3 दिन</p>",
                                "<p>5 दिन</p>", "<p>6 दिन</p>"],
                    solution_en: "<p>54.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231203.png\" alt=\"rId76\" width=\"250\"><br>Efficiency of A, B and C = 5 + 4 + 3 = 12 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5 days</p>",
                    solution_hi: "<p>54.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231342.png\" alt=\"rId77\" width=\"250\"><br>A, B और C की दक्षता = 5 + 4 + 3 = 12 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The given line graph shows the number of scooters manufactured (in thousands) by companies X and Z, over the years.&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231454.png\" alt=\"rId78\" width=\"400\" height=\"265\"> <br>What is the average number of scooters manufactured by company X over the given period?</p>",
                    question_hi: "<p>55. दिया गया लाइन ग्राफ पिछले वर्षों में कंपनी x और z द्वारा निर्मित स्कूटरों की संख्या (हजार में) दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231544.png\" alt=\"rId79\" width=\"400\"> <br>दी गई अवधि में कंपनी X द्वारा निर्मित स्कूटरों की औसत संख्या कितनी है?</p>",
                    options_en: ["<p>256000</p>", "<p>213000</p>", 
                                "<p>116500</p>", "<p>126500</p>"],
                    options_hi: ["<p>256000</p>", "<p>213000</p>",
                                "<p>116500</p>", "<p>126500</p>"],
                    solution_en: "<p>55.(c)<br>Required average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>128</mn><mo>+</mo><mn>110</mn><mo>+</mo><mn>102</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>109</mn><mo>+</mo><mn>170</mn></mrow><mn>6</mn></mfrac></math> = 116500 (because all values in thousands)</p>",
                    solution_hi: "<p>55.(c)<br>आवश्यक औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>128</mn><mo>+</mo><mn>110</mn><mo>+</mo><mn>102</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>109</mn><mo>+</mo><mn>170</mn></mrow><mn>6</mn></mfrac></math>&nbsp;= 116500 (क्योंकि सभी मान हजारों में हैं)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A sum becomes ₹6,600 in 4 years at simple interest at a yearly interest rate of 5% per annum. What is the sum?</p>",
                    question_hi: "<p>56. एक धनराशि 5% प्रति वर्ष की वार्षिक ब्याज दर पर साधारण ब्याज पर 4 वर्षों में ₹6,600 हो जाती है। वह धनराशि क्या है?</p>",
                    options_en: ["<p>₹6,000</p>", "<p>₹3,300</p>", 
                                "<p>₹5,500</p>", "<p>₹4,400</p>"],
                    options_hi: ["<p>₹6,000</p>", "<p>₹3,300</p>",
                                "<p>₹5,500</p>", "<p>₹4,400</p>"],
                    solution_en: "<p>56.(c)&nbsp;Amount = principal + SI<br>SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br>amount = p + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br>6600 = p + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math><br>6600 = p + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mn>5</mn></mfrac></math><br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi mathvariant=\"normal\">p</mi><mo>+</mo><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>5</mn></mfrac></math><br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi mathvariant=\"normal\">p</mi></mrow><mn>5</mn></mfrac></math><br>p = 5500</p>",
                    solution_hi: "<p>56.(c)&nbsp;राशि = मूलधन + साधारण ब्याज <br>साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>मिश्रधन (राशि) = मूलधन + <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>6600 = मूलधन + <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math><br>6600 = मूलधन + <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mn>5</mn></mfrac></math><br>6600 = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>+</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>5</mn></mfrac></math><br>6600 = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>5</mn></mfrac></math><br>मूलधन = 5500</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If the point of intersection of lines x + y = 2 and 2x - y = 1 lies on y = Kx + 5, what is the value of K ?</p>",
                    question_hi: "<p>57. यदि रेखाओं x + y = 2 और 2x - y = 1 का प्रतिच्छेद बिंदु y = Kx + 5 पर स्थित है, तो K का मान ज्ञात कीजिए</p>",
                    options_en: ["<p>3</p>", "<p>-4</p>", 
                                "<p>4</p>", "<p>-3</p>"],
                    options_hi: ["<p>3</p>", "<p>-4</p>",
                                "<p>4</p>", "<p>-3</p>"],
                    solution_en: "<p>57.(b) <br>x + y = 2&hellip;&hellip;(i)<br>2x - y = 1&hellip;&hellip;.(ii)<br>On solving both equation we get,<br>x = 1, y = 1<br>According to the question,<br>1 = K &times;&nbsp;1 + 5<br>K = 1 - 5 = - 4</p>",
                    solution_hi: "<p>57.(b) <br>x + y = 2&hellip;&hellip;(i)<br>2x - y = 1&hellip;&hellip;.(ii)<br>दोनों समीकरणों को हल करने पर हमें प्राप्त होता है,<br>x = 1, y = 1<br>प्रश्न के अनुसार,<br>1 = K &times;&nbsp;1 + 5<br>K = 1 - 5 = - 4</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Two offers were being made to Swaroop for a watch with a marked price of ₹1,600. Either two successive discounts of 20%, or two discounts of 30% and 10% after each other is offered. If Swaroop opted for the better plan over the other, how much more must he have saved?</p>",
                    question_hi: "<p>58. स्वरूप को ₹1,600 अंकित मूल्य वाली एक घड़ी के लिए दो ऑफर दिए जाते हैं। या तो वह 20% की दो क्रमिक छूट ले सकता है, या एक के बाद एक 30% और 10% की दो छूट ले सकता है। यदि स्वरूप उनमें से बेहतर प्लान का चुनाव करता है, तो वह कितनी अधिक बचत कर सकता है?</p>",
                    options_en: ["<p>₹22</p>", "<p>₹35</p>", 
                                "<p>₹16</p>", "<p>₹30</p>"],
                    options_hi: ["<p>₹22</p>", "<p>₹35</p>",
                                "<p>₹16</p>", "<p>₹30</p>"],
                    solution_en: "<p>58.(c)<br>Two successive discount of 20% = 20 + 20 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math> = 36%<br>Two successive discount of 30% and 10% = 30 + 10 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math> = 37%<br>Required amount = 1600 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>37</mn><mo>-</mo><mn>36</mn></mrow><mn>100</mn></mfrac></math> = ₹ 16</p>",
                    solution_hi: "<p>58.(c)<br>20% की दो क्रमिक छूट = 20 + 20 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= 36%<br>30% और 10% की दो क्रमिक छूट = 30 + 10 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= 37%<br>आवश्यक राशि = 1600 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>37</mn><mo>-</mo><mn>36</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= ₹ 16</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Study the given bar-graph and answer the question that follows.<br>The bar-graph shows the production (in lakh) of kitchen appliances manufactured by three companies A, B, and C over a period of six years from 2012 to 2017.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231640.png\" alt=\"rId80\" width=\"400\" height=\"240\"> <br>80% of the combined average production of companies B and C is what percentage (to the nearest integer) more/less than 90% of the average production of company A. for the given period of 2012 to 2017?</p>",
                    question_hi: "<p>59. दिए गए बार ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br>बार ग्राफ में 2012 से 2017 तक छः वर्षों की अवधि में तीन कंपनियों A, B और C द्वारा निर्मित रसोई उपकरणों के उत्पादन (लाख में) को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231747.png\" alt=\"rId81\" width=\"400\"> <br>वर्ष 2012 से 2017 की दी गई अवधि में कंपनी B और C के संयुक्त औसत उत्पादन का 80%, कंपनी A के औसत उत्पादन के 90% से कितना प्रतिशत (निकटतम पूर्णांक तक) अधिक / कम है?</p>",
                    options_en: ["<p>21% more</p>", "<p>19% more</p>", 
                                "<p>19% less</p>", "<p>21% less</p>"],
                    options_hi: ["<p>21% अधिक</p>", "<p>19% अधिक</p>",
                                "<p>19% कम</p>", "<p>21% कम</p>"],
                    solution_en: "<p>59.(c)<br>Production of company B = 130 + 80 + 70 + 90 + 180 + 80 = 630<br>Production of company C = 70 + 90 + 120 + 80 + 110 + 140 = 610<br>Average production of company B and C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>630</mn><mo>+</mo><mn>610</mn></mrow><mn>12</mn></mfrac></math> = 103.33<br>80% of average production of company B and C = 103.33 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 82.66<br>Average production of company A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>160</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>70</mn><mo>+</mo><mn>110</mn></mrow><mn>6</mn></mfrac></math> = 113.33<br>90% of average production of company A = 113.33 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 102<br>required% = <math display=\"inline\"><mfrac><mrow><mn>102</mn><mo>-</mo><mn>82</mn><mo>.</mo><mn>66</mn></mrow><mrow><mn>102</mn></mrow></mfrac></math> &times; 100 = 18.95 (approx 19% less)</p>",
                    solution_hi: "<p>59.(c)<br>कंपनी B का उत्पादन = 130 + 80 + 70 + 90 + 180 + 80 = 630<br>कंपनी C का उत्पादन = 70 + 90 + 120 + 80 + 110 + 140 = 610<br>कंपनी B और C का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>630</mn><mo>+</mo><mn>610</mn></mrow><mn>12</mn></mfrac></math>&nbsp;= 103.33<br>कंपनी B और C के औसत उत्पादन का 80% = 103.33 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 82.66<br>कंपनी A का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>160</mn><mo>+</mo><mn>150</mn><mo>+</mo><mn>70</mn><mo>+</mo><mn>110</mn></mrow><mn>6</mn></mfrac></math>&nbsp;= 113.33<br>कंपनी A के औसत उत्पादन का 90% = 113.33 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 102<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>102</mn><mo>-</mo><mn>82</mn><mo>.</mo><mn>66</mn></mrow><mrow><mn>102</mn></mrow></mfrac></math> &times; 100 = 18.95 (लगभग 19% कम)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. What is the number of digits required for numbering a book with 428 pages?</p>",
                    question_hi: "<p>60. 428 पृष्ठों वाली एक पुस्तक को क्रमांकित करने के लिए आवश्यक अंकों की संख्या कितनी है?</p>",
                    options_en: ["<p>1500</p>", "<p>2000</p>", 
                                "<p>988</p>", "<p>1176</p>"],
                    options_hi: ["<p>1500</p>", "<p>2000</p>",
                                "<p>988</p>", "<p>1176</p>"],
                    solution_en: "<p>60.(d)&nbsp;According to the question,<br>Number of digits required = (9 &times; 1) + (90 &times; 2) + (329 &times; 3) = 1176</p>",
                    solution_hi: "<p>60.(d)&nbsp;प्रश्न के अनुसार,<br>आवश्यक अंकों की संख्या = (9 &times; 1) + (90 &times; 2) + (329 &times; 3) = 1176</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The outer surface of a sphere having diameter 10 m is painted at the rate of ₹ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> per m&sup2;. What is the cost of painting?</p>",
                    question_hi: "<p>61. 10 m व्यास वाले एक गोले के बाह्य पृष्ठ को ₹ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> प्रति वर्ग मीटर की दर से पेंट किया गया। तो पेंटिंग की लागत कितनी है?</p>",
                    options_en: ["<p>₹8,000</p>", "<p>₹9,000</p>", 
                                "<p>₹4,000</p>", "<p>₹6,000</p>"],
                    options_hi: ["<p>₹8,000</p>", "<p>₹9,000</p>",
                                "<p>₹4,000</p>", "<p>₹6,000</p>"],
                    solution_en: "<p>61.(a)&nbsp;TSA of the sphere = 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup></math><br>Required cost = 4 &times; <math display=\"inline\"><mi>&#960;</mi></math> &times; 5 &times; 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> = ₹ 8000</p>",
                    solution_hi: "<p>61.(a)&nbsp;गोले का सम्पूर्ण पृष्ठीय &nbsp;क्षेत्रफल = 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup></math><br>आवश्यक लागत = 4 &times; <math display=\"inline\"><mi>&#960;</mi></math> &times; 5 &times; 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi mathvariant=\"normal\">&#960;</mi></mfrac></math> = ₹ 8000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Study the given table and answer the question that follows.<br>The table shows the number of students appeared and qualified (in thousands) district-wise in the EMCET examination in a state.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231846.png\" alt=\"rId82\" width=\"550\" height=\"111\"> <br>The total percentage of students who qualified in the year 2016 (correct up to two decimals) is:</p>",
                    question_hi: "<p>62. निम्नलिखित तालिका का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>तालिका में एक राज्य में EMCET परीक्षा में जिलेवार उपस्थित होने वाले और उत्तीर्ण (हजार में) होने वाले विद्यार्थियों की संख्या को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382231986.png\" alt=\"rId83\" width=\"600\"> <br>वर्ष 2016 में उत्तीर्ण होने वाले विद्यार्थियों का कुल प्रतिशत क्या है (केवल दशमलव के दो स्थान तक)?</p>",
                    options_en: ["<p>52.82%</p>", "<p>55.18%</p>", 
                                "<p>44.72%</p>", "<p>54.78%</p>"],
                    options_hi: ["<p>52.82%</p>", "<p>55.18%</p>",
                                "<p>44.72%</p>", "<p>54.78%</p>"],
                    solution_en: "<p>62.(d)&nbsp;Total number of students in 2016 = 55 + 35 + 48 + 50 + 55 + 60 = 303<br>Qualified students = 42 + 22 + 26 + 30 + 27 + 19 = 166<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>166</mn></mrow><mrow><mn>303</mn></mrow></mfrac></math> &times; 100 = 54.78%</p>",
                    solution_hi: "<p>62.(d)&nbsp;2016 में छात्रों की कुल संख्या = 55 + 35 + 48 + 50 + 55 + 60 = 303<br>उत्तीर्ण छात्र = 42 + 22 + 26 + 30 + 27 + 19 = 166<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>166</mn></mrow><mrow><mn>303</mn></mrow></mfrac></math> &times; 100 = 54.78%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. In ∆EFG, XY ∥ FG, area of the quadrilateral XFGY = 44 m<sup>2</sup>. If EX : XF = 2 : 3, then find the area of ∆EXY (in m<sup>2</sup> ).</p>",
                    question_hi: "<p>63. △EFG में, XY || FG है, चतुर्भुज XFGY का क्षेत्रफल = 44 m&sup2; है। यदि EX : XF = 2 : 3 है, तो △EXY का क्षेत्रफल (m&sup2; में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>7.28</p>", "<p>8.10</p>", 
                                "<p>8.38</p>", "<p>9.46</p>"],
                    options_hi: ["<p>7.28</p>", "<p>8.10</p>",
                                "<p>8.38</p>", "<p>9.46</p>"],
                    solution_en: "<p>63.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232119.png\" alt=\"rId84\" width=\"180\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>area</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#916;EXY</mi></mrow><mrow><mi>area</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#916;EFG</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mfrac><mn>4</mn><mn>25</mn></mfrac></math><br>Area of quadrilateral XFGY = 25 - 4 = 21 units<br>21 units = 44 m<sup>2</sup><br>(area of <math display=\"inline\"><mi>&#916;</mi></math>EXY) 2 units = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>21</mn></mfrac></math> &times; 4 = 8.38 m<sup>2</sup></p>",
                    solution_hi: "<p>63.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232119.png\" alt=\"rId84\" width=\"180\"><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;EXY</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;EFG</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>2</mn><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mfrac><mn>4</mn><mn>25</mn></mfrac></math><br>चतुर्भुज XFGY का क्षेत्रफल = 25 - 4 = 21 इकाई <br>21 इकाई = 44 m<sup>2</sup><br>(<math display=\"inline\"><mi>&#916;</mi></math>EXY का क्षेत्रफल) 2 इकाई =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>21</mn></mfrac></math> &times; 4 = 8.38 m<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If <math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>tan&#952;</mi><mo>+</mo><msqrt><mn>13</mn></msqrt><mi>sin&#952;</mi></mrow><mrow><msqrt><mn>13</mn></msqrt><mi>cos&#952;</mi><mo>-</mo><mn>3</mn><mi>tan&#952;</mi><mo>&#160;</mo></mrow></mfrac></math>is :</p>",
                    question_hi: "<p>64. यदि <math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2 है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>tan&#952;</mi><mo>+</mo><msqrt><mn>13</mn></msqrt><mi>sin&#952;</mi></mrow><mrow><msqrt><mn>13</mn></msqrt><mi>cos&#952;</mi><mo>-</mo><mn>3</mn><mi>tan&#952;</mi><mo>&#160;</mo></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>0</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>0</p>", "<p>4</p>"],
                    solution_en: "<p>64.(d)<br><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2<br>sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>13</mn></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">h</mi></mfrac></math><br>b = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 3<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mfrac><mn>3</mn><msqrt><mn>13</mn></msqrt></mfrac><mo>-</mo><mn>3</mn><mo>&#215;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>+</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mn>4</mn></math></p>",
                    solution_hi: "<p>64.(d)<br><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2<br>sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>13</mn></mfrac><mo>=</mo><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">h</mi></mfrac></math><br>b = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 3<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mfrac><mn>3</mn><msqrt><mn>13</mn></msqrt></mfrac><mo>-</mo><mn>3</mn><mo>&#215;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mo>+</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mn>4</mn></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. At the beginning of day 1 of a month, Rajesh has 500 eggs. He sells 20% of the eggs by the end of the day and added b% of the eggs at the beginning of the next day and sold 20% of the eggs at the end of the next day. This pattern continued up to the end of the third day of the month when he is left with 1024 eggs. The value of b is equal to:</p>",
                    question_hi: "<p>65. महीने के पहले दिन की शुरुआत में राजेश के पास 500 अंडे थे। वह दिन के अंत तक 20% अंडे बेच देता है और अगले दिन की शुरुआत में उपलब्ध अंडों में b% अंडे और मिला लेता है और अगले दिन के अंत तक 20% अंडे बेच देता है। यह पैटर्न महीने के तीसरे दिन के अंत तक जारी रहता है और तब उसके पास 1024 अंडे बचते है। निम्न में से b का मान किसके बराबर है?</p>",
                    options_en: ["<p>500</p>", "<p>20</p>", 
                                "<p>100</p>", "<p>10</p>"],
                    options_hi: ["<p>500</p>", "<p>20</p>",
                                "<p>100</p>", "<p>10</p>"],
                    solution_en: "<p>65.(c)&nbsp;According to the question,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>500</mn><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = 1024<br>4 &times; <math display=\"inline\"><mo>(</mo><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>b</mi><mo>)</mo></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = 1024<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1024</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 16 &times; 25 &times; 100<br>100 + b = 4 &times; 5 &times; 10 = 200<br>b = 100</p>",
                    solution_hi: "<p>65.(c)&nbsp;प्रश्न के अनुसार,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>500</mn><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&#215;</mo><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = 1024<br>4 &times; <math display=\"inline\"><mo>(</mo><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>b</mi><mo>)</mo></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = 1024<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1024</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 16 &times; 25 &times; 100<br>100 + b = 4 &times; 5 &times; 10 = 200<br>b = 100</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Divide some money in the ratio Ravi, Reeta and Rahul that 5 (Part of Ravi) = 3 (Part of Reeta) = 11 (Part of Rahul). The money ratio of Ravi : Reeta : Rahul is equal to:</p>",
                    question_hi: "<p>66. कुछ धनराशि को रवि, रीता और राहुल के बीच इस अनुपात में बांटिए कि 5 (रवि का हिस्सा) = 3 (रीता का हिस्सा) = 11 (राहुल का हिस्सा) हो। रवि : रीता : राहुल की धनराशि का अनुपात _________ के बराबर है।</p>",
                    options_en: ["<p>11 : 5 : 3</p>", "<p>11 : 33 : 15</p>", 
                                "<p>5 : 11 : 3</p>", "<p>33 : 55 : 15</p>"],
                    options_hi: ["<p>11 : 5 : 3</p>", "<p>11 : 33 : 15</p>",
                                "<p>5 : 11 : 3</p>", "<p>33 : 55 : 15</p>"],
                    solution_en: "<p>66.(d)<br>5 (Part of Ravi) = 3 (Part of Reeta) = 11 (Part of Rahul)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>ravi</mi><mi>reeta</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>reeta</mi><mi>rahul</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math><br>Ratio - ravi : reeta : rahul<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; &nbsp;:&nbsp; &nbsp; 5&nbsp; &nbsp; :&nbsp; &nbsp;<strong>5</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong> 11</strong>&nbsp; :&nbsp; &nbsp; 11&nbsp; &nbsp;:&nbsp; &nbsp;3<br>-------------------------------------<br>Final - 33&nbsp; &nbsp;:&nbsp; &nbsp;55&nbsp; &nbsp;:&nbsp; 15<br><strong>Short tricks</strong> :- 5 (Part of Ravi) = 3 (Part of Reeta) = 11 (Part of Rahul)<br>LCM of 5, 3, 11 = 5 &times; 3 &times; 11<br>Required ratio = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>11</mn></mfrac></math> = 33 : 55 : 15</p>",
                    solution_hi: "<p>66.(d)&nbsp;5 (रवि का भाग) = 3 (रीता का भाग) = 11 (राहुल का भाग)<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2352;&#2357;&#2367;</mi><mi>&#2352;&#2368;&#2340;&#2366;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>, <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2352;&#2368;&#2340;&#2366;</mi><mi>&#2352;&#2366;&#2361;&#2369;&#2354;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math><br>अनुपात - रवि : रीता : राहुल<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; :&nbsp; &nbsp; 5&nbsp; :&nbsp; &nbsp;<strong>5</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>11</strong> :&nbsp; &nbsp;11 :&nbsp; &nbsp; 3<br>-------------------------------------<br>अंतिम - 33&nbsp; &nbsp;:&nbsp; 55&nbsp; :&nbsp; 15<br><strong>शॉर्ट ट्रिक्स </strong>:- 5 (रवि का हिस्सा) = 3 (रीता का हिस्सा) = 11 (राहुल का हिस्सा)<br>5, 3, 11 का LCM = 5 &times; 3 &times; 11<br>आवश्यक अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>3</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>11</mn></mrow><mn>11</mn></mfrac></math> = 33 : 55 : 15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The difference between the cost price and the selling price of an article is ₹1,800. If there is a profit of 20%, then the cost price of the article is:</p>",
                    question_hi: "<p>67. एक वस्तु के क्रय मूल्य और विक्रय मूल्य के बीच का अंतर ₹1,800 है। यदि 20% का लाभ होता है, तो वस्तु का क्रय मूल्य क्या है?</p>",
                    options_en: ["<p>₹7,000</p>", "<p>₹11,000</p>", 
                                "<p>₹8,000</p>", "<p>₹9,000</p>"],
                    options_hi: ["<p>₹7,000</p>", "<p>₹11,000</p>",
                                "<p>₹8,000</p>", "<p>₹9,000</p>"],
                    solution_en: "<p>67.(d) <br>Ratio - CP : SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; :&nbsp; &nbsp;6<br>Difference = 6 - 5 = 1 unit<br>1 unit = 1800<br>(CP) 5 units = 1800 &times; 5 = ₹ 9000</p>",
                    solution_hi: "<p>67.(d) <br>अनुपात - क्रय मूल्य : विक्रय मूल्य<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 6<br>अंतर = 6 - 5 = 1 इकाई<br>1 इकाई = 1800<br>(क्रय मूल्य) 5 इकाई = 1800 &times; 5 = ₹ 9000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Two trains of lengths 310 m and 330 m, respectively, are 160 m apart. They start moving towards each other on parallel tracks, at speeds 130 km/h and 158 km/h, respectively. In how much time (in seconds) will the trains cross each other?</p>",
                    question_hi: "<p>68. क्रमशः 310 m और 330 m लंबी दो रेलगाड़ियां एक-दूसरे से 160 m की दूरी पर हैं। वे समानांतर पटरियों पर क्रमशः 130 km/h और 158 km/h की चाल से एक दूसरे की ओर बढ़ना शुरू करती हैं। दोनों रेलगाड़ियां एक-दूसरे को कितने समय में (सेकंड में) पार करेंगी?</p>",
                    options_en: ["<p>10</p>", "<p>18</p>", 
                                "<p>8</p>", "<p>12</p>"],
                    options_hi: ["<p>10</p>", "<p>18</p>",
                                "<p>8</p>", "<p>12</p>"],
                    solution_en: "<p>68.(a)&nbsp;Relative speed = 130 + 158 = 288 km/h or 80 m/s<br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>distance</mi><mi>speed</mi></mfrac></math><br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>310</mn><mo>+</mo><mn>330</mn><mo>+</mo><mn>160</mn></mrow><mn>80</mn></mfrac></math> = 10 seconds</p>",
                    solution_hi: "<p>68.(a)&nbsp;सापेक्ष गति = 130 + 158 = 288 km/h या 80 m/s<br>समय = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mi>&#2327;&#2340;&#2367;</mi></mfrac></math><br>समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>310</mn><mo>+</mo><mn>330</mn><mo>+</mo><mn>160</mn></mrow><mn>80</mn></mfrac></math>&nbsp;= 10 सेकंड</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A man spends 35% of his monthly income on food and four-thirteenths of the remaining income on transport. He incurs some other expenses, but also saves Rs.6,300 per month, the latter being equal to 20% of the balance remaining just after spending on food and transport. What is his monthly income (in Rs.)?</p>",
                    question_hi: "<p>69. एक आदमी अपनी मासिक आय का 35% भोजन पर और शेष आय का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>13</mn></mfrac></math> भाग परिवहन पर खर्च करता है। वह कुछ अन्य खर्च भी करता है, लेकिन प्रति माह Rs.6,300 बचाता है, जो कि भोजन और परिवहन पर खर्च करने के बाद शेष राशि के 20% के बराबर है। उसकी मासिक आय (Rs. में) कितनी है?</p>",
                    options_en: ["<p>63000</p>", "<p>72,000</p>", 
                                "<p>67500</p>", "<p>70,000</p>"],
                    options_hi: ["<p>63000</p>", "<p>72,000</p>",
                                "<p>67500</p>", "<p>70,000</p>"],
                    solution_en: "<p>69.(d)&nbsp;Let the total income = 100x<br>E<math display=\"inline\"><mi>x</mi></math>penditure of food = 100x &times; 35% = 35x<br>Remaining amount = 100x&nbsp;- 35x = 65x<br>E<math display=\"inline\"><mi>x</mi></math>penditure of transport = 65x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>13</mn></mfrac></math> = 20x<br>(100x - (35x + 20x)) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 6300<br>100x&nbsp;- 55x = 6300 &times; 5<br>45x&nbsp;= 6300 &times; 5, x = 700<br>So, his monthly income = 100 &times; 700 = 70000</p>",
                    solution_hi: "<p>69.(d)&nbsp;माना कुल आय = 100x<br>भोजन का व्यय = 100x&nbsp;&times; 35% = 35x<br>शेष राशि = 100x&nbsp;- 35x = 65x<br>परिवहन का व्यय = 65x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>13</mn></mfrac></math> = 20x<br>(100x - (35x + 20x)) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 6300<br>100x&nbsp;- 55x = 6300 &times; 5<br>45x&nbsp;= 6300 &times; 5, x = 700<br>तो, उसकी मासिक आय = 100 &times; 700 = 70000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. L and M are the mid points of sides AB and AC of a triangle ABC, respectively, and BC = 18 cm. If LM || BC, then find the length of LM (in cm).</p>",
                    question_hi: "<p>70. L और M क्रमशः त्रिभुज ABC की भुजाओं AB और AC के मध्य बिंदु हैं, और BC = 18 cm है। यदि LM || BC है, तो LM की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>6</p>", "<p>9</p>", 
                                "<p>3</p>", "<p>12</p>"],
                    options_hi: ["<p>6</p>", "<p>9</p>",
                                "<p>3</p>", "<p>12</p>"],
                    solution_en: "<p>70.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232299.png\" alt=\"rId85\" width=\"200\" height=\"180\"><br><strong>Thales theorem</strong> : - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"bold\">AL</mi><mi mathvariant=\"bold\">AB</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>LM</mi><mi>BC</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>LM</mi><mn>18</mn></mfrac></math><br>LM = 9 cm</p>",
                    solution_hi: "<p>70.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232299.png\" alt=\"rId85\" width=\"200\" height=\"180\"><br>थेल्स प्रमेय :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"bold\">AL</mi><mi mathvariant=\"bold\">AB</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>LM</mi><mi>BC</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>LM</mi><mn>18</mn></mfrac></math><br>LM = 9 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. In ∆ABC the straight line parallel to the side BC meets AB and AC at the points P and Q, respectively. If AP = QC, the length of AB is 12 cm and the length of AQ is 2 cm, then the length (in cm) of CQ is:</p>",
                    question_hi: "<p>71. △ABC में भुजा BC के समांतर सीधी रेखा, AB और AC से क्रमशः बिंदु P और Q पर मिलती है। यदि AP = QC है, AB की लंबाई 12 cm है और AQ की लंबाई 2 cm है, तो CQ की लंबाई (cm में) कितनी होगी?</p>",
                    options_en: ["<p>2</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>6</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>71.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232445.png\" alt=\"rId86\" width=\"150\"><br><strong>Thales theorem</strong> : - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"bold\">AP</mi><mi mathvariant=\"bold\">PB</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AQ</mi><mi>QC</mi></mfrac></math> <br>AP = QC = x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mrow><mn>12</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac></math><br>x<sup>2</sup> = 24 - 2x<br>x<sup>2</sup> + 2x - 24 = 0<br>x<sup>2</sup> + 6x - 4x - 24 = 0<br>x(x + 6) - 4(x + 6) = 0<br>(x - 4) (x + 6)<br>x = 4, - 6 (can&rsquo;t take -ve value of x)</p>",
                    solution_hi: "<p>71.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232445.png\" alt=\"rId86\" width=\"150\"><br><strong>थेल्स प्रमेय</strong> :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"bold\">AP</mi><mi mathvariant=\"bold\">PB</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AQ</mi><mi>QC</mi></mfrac></math> <br>AP = QC = x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mrow><mn>12</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi mathvariant=\"normal\">x</mi></mfrac></math><br>x<sup>2</sup> = 24 - 2x<br>x<sup>2</sup> + 2x - 24 = 0<br>x<sup>2</sup> + 6x - 4x - 24 = 0<br>x(x + 6) - 4(x + 6) = 0<br>(x - 4) (x + 6)<br><math display=\"inline\"><mi>x</mi></math> = 4, - 6 ( x का मान नकारात्मक नहीं ले सकते)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The given pie-charts show the distribution of Graduate and Post-Graduate level students in five different colleges A, B, C, D and E.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232577.png\" alt=\"rId87\" width=\"290\" height=\"193\"> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232705.png\" alt=\"rId88\" width=\"300\"> <br>How many students of colleges A and B are studying at Graduate level?</p>",
                    question_hi: "<p>72. दिए गए पाई-चार्ट पांच अलग-अलग महावि&zwnj;द्यालयों A, B, C, D और E में स्नातक और स्नातकोत्तर स्तर के छात्रों के वितरण को दर्शाते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232808.png\" alt=\"rId89\" width=\"300\"> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1733382232942.png\" alt=\"rId90\" width=\"280\"> <br>महाविद्यालय A और B के कितने छात्र स्नातक स्तर पर अध्ययन कर रहे हैं?</p>",
                    options_en: ["<p>5,987</p>", "<p>4,520</p>", 
                                "<p>6,993</p>", "<p>7,052</p>"],
                    options_hi: ["<p>5,987</p>", "<p>4,520</p>",
                                "<p>6,993</p>", "<p>7,052</p>"],
                    solution_en: "<p>72.(c)&nbsp;Students who studying at graduate level in both collage = 18% + 19% = 37%<br>100% = 18900<br>37% = 189 &times; 37 = 6993</p>",
                    solution_hi: "<p>72.(c)&nbsp;दोनों महाविद्यालयों में स्नातक स्तर पर अध्ययन करने वाले छात्र = 18% + 19% = 37%<br>100% = 18900<br>37% = 189 &times; 37 = 6993</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The triangle has sides 3 cm, 4 cm and 5 cm. What is the length of the perpendicular from the opposite vertex to the side whose length is 5 cm?</p>",
                    question_hi: "<p>73. एक त्रिभुज की भुजाएं 3 cm, 4 cm और 5 cm हैं। 5 cm लंबाई वाली भुजा पर विपरीत शीर्ष से डाले गए लंब की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>3.5 cm</p>", "<p>2.4 cm</p>", 
                                "<p>2.2 cm</p>", "<p>1.4 cm</p>"],
                    options_hi: ["<p>3.5 cm</p>", "<p>2.4 cm</p>",
                                "<p>2.2 cm</p>", "<p>1.4 cm</p>"],
                    solution_en: "<p>73.(b)&nbsp;3, 4, 5 are triplet of right angle triangle so,<br>Length of the perpendicular = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>&#215;</mo><mi mathvariant=\"normal\">b</mi></mrow><mi mathvariant=\"normal\">h</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>5</mn></mfrac></math> = 2.4 cm</p>",
                    solution_hi: "<p>73.(b)&nbsp;3, 4, 5 समकोण त्रिभुज के त्रिक हैं इसलिए,<br>लम्ब की लंबाई = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&#215;</mo><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>5</mn></mfrac></math> = 2.4 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. In June, Rohit&rsquo;s bank account balance is ₹5,000 for 25 days, ₹20,000 for 2 days and ₹1,500 for 3 days. What is the average balance (in ₹) in Rohit\'s bank account in June?</p>",
                    question_hi: "<p>74. जून में, रोहित के बैंक खाते का बैलेंस 25 दिनों के लिए ₹5,000, 2 दिनों के लिए ₹20,000 और 3 दिनों के लिए ₹1,500 है। जून में रोहित के बैंक खाते में औसत बैलेंस (₹ में) कितना है?</p>",
                    options_en: ["<p>5650</p>", "<p>5575</p>", 
                                "<p>6000</p>", "<p>5200</p>"],
                    options_hi: ["<p>5650</p>", "<p>5575</p>",
                                "<p>6000</p>", "<p>5200</p>"],
                    solution_en: "<p>74.(a)<br>Average balance = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 5650</p>",
                    solution_hi: "<p>74.(a)<br>औसत बैलेंस = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math>&nbsp;= 5650</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "18",
                    question_en: "<p>75. The radius and height of a cylinder are in the ratio 6 : 7 and its volume is 792 cm<sup>3</sup> . Calculate its curved surface area in cm<sup>2</sup></p>",
                    question_hi: "<p>75. एक बेलन की त्रिज्या और ऊंचाई का अनुपात 6 : 7 है और इसका आयतन 792 cm&sup3; है। इसके वक्र पृष्ठीय क्षेत्रफल की गणना cm&sup2; में कीजिए।</p>",
                    options_en: ["<p>262</p>", "<p>490</p>", 
                                "<p>264</p>", "<p>226</p>"],
                    options_hi: ["<p>262</p>", "<p>490</p>",
                                "<p>264</p>", "<p>226</p>"],
                    solution_en: "<p>75.(c)&nbsp;Volume of the cylinder = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup><mi mathvariant=\"normal\">h</mi></math><br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; (6x)<sup>2</sup>(7x) = 792<br>x<sup>3</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>792</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#215;</mo><mn>36</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> = 1 so x = 1<br>So, the radius and height of the cylinder will be 6 and 7<br>Now, according to the question,<br>CSA of the cylinder = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;rh</mi></math><br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6 &times; 7&nbsp;= 264 cm<sup>2</sup></p>",
                    solution_hi: "<p>75.(c)&nbsp;बेलन का आयतन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup><mi mathvariant=\"normal\">h</mi></math><br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; (6x)<sup>2</sup>(7x) = 792<br>x<sup>3</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>792</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#215;</mo><mn>36</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> = 1 इसलिए x = 1<br>तो, बेलन की त्रिज्या और ऊंचाई 6 और 7 होगी<br>अब, प्रश्न के अनुसार,<br>बेलन का वक्र पृष्ठीय क्षेत्रफल = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;rh</mi></math> = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6 &times; 7&nbsp;= 264 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "18",
                    question_en: "<p>76. When did the first War of Indian Independence (Sepoy Mutiny) start?</p>",
                    question_hi: "<p>76. भारतीय स्वतंत्रता का प्रथम युद्ध (सिपाही विद्रोह) कब शुरू हुआ था?</p>",
                    options_en: ["<p>1839</p>", "<p>1857</p>", 
                                "<p>1887</p>", "<p>1819</p>"],
                    options_hi: ["<p>1839</p>", "<p>1857</p>",
                                "<p>1887</p>", "<p>1819</p>"],
                    solution_en: "<p>76.(b) <strong>1857</strong>. The revolt of 1857 also known as India&rsquo;s First War of Independence, Sepoy Mutiny, etc. The revolt began on May 10, 1857, at Meerut as a sepoy mutiny. It was initiated by sepoys in the Bengal Presidency against the British officers. It occurred during the reign of Governor-General Lord Canning.</p>",
                    solution_hi: "<p>76.(b) <strong>1857</strong>. 1857 के विद्रोह को भारत के प्रथम स्वतंत्रता संग्राम, सिपाही विद्रोह आदि के नाम से भी जाना जाता है। यह विद्रोह 10 मई, 1857 को मेरठ में सिपाही विद्रोह के रूप में शुरू हुआ था। इसकी शुरुआत बंगाल प्रेसीडेंसी के सिपाहियों ने ब्रिटिश अधिकारियों के खिलाफ की थी। यह विद्रोह गवर्नर-जनरल लॉर्ड कैनिंग के शासनकाल के दौरान हुआ था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "18",
                    question_en: "<p>77. In a dihybrid cross between two heterozygous fruit flies with brown bodies and red eyes (BbEe X BbEe), what will be the probability of getting BBEE genotype?</p>",
                    question_hi: "<p>77. भूरे रंग के शरीर और लाल आंखों वाली दो विषम युग्मजी फल मक्खियों के बीच एक द्विसंकर क्रॉस (BbEe X BbEe) में, बीबीईई जीनोटाइप (BBEE genotype) प्राप्त करने की संभावना क्या होगी?</p>",
                    options_en: ["<p>1/8</p>", "<p>1/4</p>", 
                                "<p>1/16</p>", "<p>1/2</p>"],
                    options_hi: ["<p>1/8</p>", "<p>1/4</p>",
                                "<p>1/16</p>", "<p>1/2</p>"],
                    solution_en: "<p>77.(c) <strong>1/16</strong>. Each parent can produce four types of gametes: BE, Be, bE, and be, with equal probability (1/4 each). To obtain the BBEE genotype, the offspring must receive the B and E alleles from both parents. The probability of getting BB from Bb &times; Bb cross is 1/4, and the probability of getting EE from Ee &times; Ee cross is also 1/4. Therefore, the combined probability of getting BBEE is (1/4) * (1/4) = 1/16.</p>",
                    solution_hi: "<p>77.(c) <strong>1/16</strong>. प्रत्येक माता-पिता चार प्रकार के युग्मक उत्पन्न कर सकते हैं: BE, Be, bE, और be, समान संभावना के साथ (प्रत्येक 1/4)। BBEE जीनोटाइप प्राप्त करने के लिए, संतान को दोनों माता-पिता से B और E एलील प्राप्त होना चाहिए। Bb &times; Bb क्रॉस से BB प्राप्त करने की संभावना 1/4 है, और Ee &times; Ee क्रॉस से EE प्राप्त करने की संभावना भी 1/4 है। इसलिए, BBEE प्राप्त करने की संयुक्त संभावना (1/4) * (1/4) = 1/16 है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "18",
                    question_en: "<p>78. Which of the following musical forms in Carnatic Classical is the simplest composition of raga?</p>",
                    question_hi: "<p>78. कर्नाटक शास्त्रीय संगीत में निम्नलिखित में से कौन-सी शैली राग की सबसे सरल रचना है?</p>",
                    options_en: ["<p>Kirtnam</p>", "<p>Varnam</p>", 
                                "<p>Gitam</p>", "<p>Pada</p>"],
                    options_hi: ["<p>कीर्तनम</p>", "<p>वर्णम</p>",
                                "<p>गीतम</p>", "<p>पाड़ा</p>"],
                    solution_en: "<p>78.(c) <strong>Gitam</strong>. The main traditional forms of improvisation in Carnatic music consist of the following: Alapana, Niraval, Pallavi, Ragam, Swarakalpana, Tanam, Tani, Avartanam. Varnam - A type of composition in Carnatic music that captures the essence of a raga, or scale, in a short piece of music. Padas are scholarly compositions in Telegu and Tamil.</p>",
                    solution_hi: "<p>78.(c) <strong>गीतम</strong>। कर्नाटक संगीत में तात्कालिकता के मुख्य पारंपरिक रूप निम्नलिखित हैं: अलापना, नीरावल, पल्लवी, रागम, स्वरकल्पना, तनम, तानी, अवर्तनम। वर्णम - कर्नाटक संगीत में वर्णम, उच्&zwj;च किस्&zwj;म की एक संगीत शिल्&zwj;पकारी की सुन्&zwj;दर रचना है जो उन रागों की सभी विशेषताओं का एक मिश्रण है। वेद तेलुगु और तमिल में विद्वानों द्वारा रचित रचनाएँ हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "18",
                    question_en: "<p>79. According the Census of India 2011, which group of states has the least urban population?</p>",
                    question_hi: "<p>79. भारत की जनगणना 2011 के अनुसार, राज्यों के किस समूह में शहरी जनसंख्या सबसे कम है?</p>",
                    options_en: ["<p>Madhya Pradesh and Bihar</p>", "<p>Himachal Pradesh and Meghalaya</p>", 
                                "<p>Himachal Pradesh and Bihar</p>", "<p>Uttar Pradesh and Bihar</p>"],
                    options_hi: ["<p>मध्य प्रदेश और बिहार</p>", "<p>हिमाचल प्रदेश और मेघालय</p>",
                                "<p>हिमाचल प्रदेश और बिहार</p>", "<p>उत्तर प्रदेश और बिहार</p>"],
                    solution_en: "<p>79.(c) <strong>Himachal Pradesh and Bihar</strong>. According to the 2011 census of India : Urban Population - 31.16%. Rural Population - 69%. Goa is the most urbanized state with 62.17% of the population living in urban areas. The total population percentage of people that lived in urban regions of Himachal Pradesh is 10.03%, Bihar is 11.29%, Odisha is 16.69%, and Rajasthan is 24.87%.</p>",
                    solution_hi: "<p>79.(c) <strong>हिमाचल प्रदेश और बिहार</strong>। भारत की 2011 की जनगणना के अनुसार: शहरी जनसंख्या - 31.16%। ग्रामीण जनसंख्या - 69%। गोवा सबसे अधिक शहरीकृत राज्य है, जिसकी 62.17% आबादी शहरी क्षेत्रों में रहती है। हिमाचल प्रदेश के शहरी क्षेत्रों में रहने वाले लोगों का कुल जनसंख्या प्रतिशत 10.03% है, बिहार में 11.29%, ओडिशा में 16.69% और राजस्थान में 24.87% है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "18",
                    question_en: "<p>80. Which of the following districts of India has the highest literacy rate as per Census 2011?</p>",
                    question_hi: "<p>80. जनगणना 2011 के अनुसार भारत के निम्नलिखित में से किस जिले में साक्षरता दर सबसे अधिक है?</p>",
                    options_en: ["<p>Idukki</p>", "<p>Champhai</p>", 
                                "<p>Ernakulam</p>", "<p>Serchhip</p>"],
                    options_hi: ["<p>इडुक्की (Idukki)</p>", "<p>चम्फाई (Champhai)</p>",
                                "<p>एर्नाकुलम (Ernakulam)</p>", "<p>सेरछिप (Serchhip)</p>"],
                    solution_en: "<p>80.(d) <strong>Serchhip </strong>(Mizoram). A person aged seven and above, who can both read and write with understanding in any language, is treated as literate. As per census data in 2011, the average literacy rate in Serchhip district was 98.23 %. According to the 2011 census: The national literacy rate in the country is 74.04%. The literacy rate among males is 82.14%. The literacy rate among females is 65.46%. Kerala retained the top with a 93.91% literacy rate.</p>",
                    solution_hi: "<p>80.(d) <strong>सेरछिप </strong>(Serchhip) (मिजोरम)। सात वर्ष या उससे अधिक आयु का व्यक्ति जो किसी भी भाषा को समझकर पढ़ और लिख सकता है, उसे साक्षर माना जाता है। 2011 की जनगणना के अनुसार, सेरछिप जिले में औसत साक्षरता दर 98.23% थी। 2011 की जनगणना के अनुसार: देश में राष्ट्रीय साक्षरता दर 74.04% है। पुरुषों में साक्षरता दर 82.14% है। महिलाओं में साक्षरता दर 65.46% है। केरल 93.91% साक्षरता दर के साथ शीर्ष पर बना हुआ है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "18",
                    question_en: "<p>81. Which of the following dynasties&rsquo; genealogy was found in the Bijolia inscription?</p>",
                    question_hi: "<p>81. निम्नलिखित में से किस राजवंश की वंशावली बिजौलिया शिलालेख में पाई गई है?</p>",
                    options_en: ["<p>Gahadavala</p>", "<p>Parmara</p>", 
                                "<p>Chandela</p>", "<p>Chahamana</p>"],
                    options_hi: ["<p>गहड़वाल</p>", "<p>परमार</p>",
                                "<p>चंदेल</p>", "<p>चाहमान</p>"],
                    solution_en: "<p>81.(d) <strong>Chahamana</strong>. Bijoliya Inscription reveals that Chauhans are Vats Gotriya Brahman. This inscription provides information about the names of Jabalipur, Shakmbhari, Srimal. An inscription of Vikram Samvat 1226 Falgun Krishna Tritiya is inscribed on a rock near the northern wall of the ancient Parshvanath temple located in Bijolia.</p>",
                    solution_hi: "<p>81.(d) <strong>चाहमान</strong>। बिजौलिया अभिलेख से पता चलता है कि चौहान वत्स गोत्रीय ब्राह्मण थे। इस अभिलेख से जाबालिपुर, शाकम्भरी, श्रीमाल के नामों की जानकारी मिलती है। बिजौलिया स्थित प्राचीन पार्श्वनाथ मंदिर की उत्तरी दीवार के पास एक चट्टान पर विक्रम संवत 1226 फाल्गुन कृष्ण तृतीया का एक शिलालेख अंकित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "18",
                    question_en: "<p>82. In which judgement did the Supreme Court extend the scope of Article 21 and observed that &lsquo;the Right to education flows directly from the &lsquo;Right to life&rsquo;?</p>",
                    question_hi: "<p>82. किस फैसले में सर्वोच्च न्यायालय ने अनुच्छेद 21 के दायरे का विस्तार किया और कहा कि \'शिक्षा का अधिकार सीधे तौर पर \'जीवन के अधिकार\' से प्राप्त होता है?</p>",
                    options_en: ["<p>Mohini Jain v State of Karnataka</p>", "<p>Neerja Choudhari v State of M. P.</p>", 
                                "<p>Kharak Singh v State of UP</p>", "<p>Malak Singh v State of Punjab</p>"],
                    options_hi: ["<p>मोहिनी जैन बनाम कर्नाटक राज्य</p>", "<p>नीरजा चौधरी बनाम मध्य प्रदेश राज्य</p>",
                                "<p>खड़क सिंह बनाम उत्तर प्रदेश राज्य</p>", "<p>मलक सिंह बनाम पंजाब राज्य</p>"],
                    solution_en: "<p>82.(a)<strong> Mohini Jain v State of Karnataka.</strong> Supreme Court declared that it was impermissible for any educational institution to charge capitation fees as a consideration for admission. Neeraja Chaudhary vs. State of Madhya Pradesh - addressed the issue of bonded labor. Kharak Singh v State of UP - focused on the constitutional review of Uttar Pradesh Police Regulations.</p>",
                    solution_hi: "<p>82.(a) <strong>मोहिनी जैन बनाम कर्नाटक राज्य</strong>। सर्वोच्च न्यायालय ने कहा कि किसी भी शैक्षणिक संस्थान द्वारा प्रवेश के बदले कैपिटेशन फीस लेना अनुचित है। नीरजा चौधरी बनाम मध्य प्रदेश राज्य - बंधुआ मजदूरी के मुद्दे को संबोधित किया। खड़क सिंह बनाम उत्तर प्रदेश राज्य - उत्तर प्रदेश पुलिस विनियमों की संवैधानिक समीक्षा पर केंद्रित था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "18",
                    question_en: "<p>83. Bali Yatra, is a traditional cultural festival of which state?</p>",
                    question_hi: "<p>83. बाली यात्रा, किस राज्य का पारंपरिक सांस्कृतिक उत्सव है?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Bihar</p>", 
                                "<p>West Bengal</p>", "<p>Assam</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>बिहार</p>",
                                "<p>पश्चिम बंगाल</p>", "<p>असम</p>"],
                    solution_en: "<p>83.(a) <strong>Odisha</strong>. Bali Jatra is the major Boita Bandana festival held at Cuttack on Kartik Purnima and lasts for 7 days or more. It is considered to be one of Asia\'s largest open trade fair. Other Festivals of Odisha: Kalipuja, Dola Purnima (Holi), Chandan Yatra, Snana Yatra, Ratha Yatra, Kalinga Mahotsav, Konark Dance Festival, Ekamra Utsav, etc.</p>",
                    solution_hi: "<p>83.(a) <strong>ओडिशा</strong>। बाली जात्रा कार्तिक पूर्णिमा पर कटक में आयोजित होने वाला प्रमुख बोइता बंदना उत्सव है और यह 7 दिनों या उससे अधिक समय तक चलता है। इसे एशिया के सबसे बड़े खुले व्यापार मेलों में से एक माना जाता है। ओडिशा के अन्य त्योहार: कालीपूजा, डोल पूर्णिमा (होली), चंदन यात्रा, स्नान यात्रा, रथ यात्रा, कलिंग महोत्सव, कोणार्क नृत्य महोत्सव, एकाम्र उत्सव, आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "18",
                    question_en: "<p>84.The Bist Doab Canal System and Makhu Canal System are associated with which state of India?</p>",
                    question_hi: "<p>84. बिस्त दोआब (Bist Doab) नहर प्रणाली और मक्खू (Makhu) नहर प्रणाली भारत के किस राज्य से सम्बंधित हैं?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Assam</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Gujarat</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>असम</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>गुजरात</p>"],
                    solution_en: "<p>84.(a) <strong>Punjab</strong>. Bist Doab is situated between the rivers Satluj and Beas. The Makhu Canal System was built in 1950. It has a length of 92 km. It draws water from Sutlej river. Doab system in Punjab : Doab Chaj - Located between the Chenab and Jhelum rivers. Doab Rachna - Located between the Ravi and Chenab rivers. Doab Bari - Located between the Beas and Ravi rivers, and also known as Majha. Suj Doab - Located between the Satluj and the Yamuna rivers, this region forms the historical Malwa region.</p>",
                    solution_hi: "<p>84.(a) <strong>पंजाब</strong>। बिस्त दोआब सतलुज और व्यास नदियों के बीच स्थित है। मखू नहर प्रणाली 1950 में बनाई गई थी। इसकी लंबाई 92 किलोमीटर है। यह सतलुज नदी से पानी लेती है। पंजाब में दोआब प्रणाली: दोआब चाज - चिनाब और झेलम नदियों के बीच स्थित है। दोआब रचना - रावी और चिनाब नदियों के बीच स्थित है। दोआब बारी - ब्यास और रावी नदियों के बीच स्थित है, और माझा के नाम से भी जाना जाता है। सुज दोआब - सतलुज और यमुना नदियों के बीच स्थित है, यह क्षेत्र ऐतिहासिक मालवा क्षेत्र बनाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "18",
                    question_en: "<p>85. ______ has called the Right to Constitutional Remedy as the &lsquo;Soul of the Constitution&rsquo;.</p>",
                    question_hi: "<p>85. ______ ने संवैधानिक उपचार के अधिकार को \'संविधान की आत्मा\' कहा है।</p>",
                    options_en: ["<p>Jawaharlal Nehru</p>", "<p>Lal Lajpat Rai</p>", 
                                "<p>Bal Gangadhar Tilak</p>", "<p>Dr. BR Ambedkar</p>"],
                    options_hi: ["<p>जवाहर लाल नेहरू</p>", "<p>लाला लाजपत राय</p>",
                                "<p>बाल गंगाधर तिलक</p>", "<p>डॉ बी.आर. अम्बेडकर</p>"],
                    solution_en: "<p>85.(d) <strong>Dr. BR Ambedkar </strong>- the chief architect of the Indian Constitution. Article 32 grants every individual the right to move the Supreme Court for the enforcement of their fundamental rights. The Supreme Court has ruled that Article 32 is a basic feature of the Constitution.</p>",
                    solution_hi: "<p>85.(d) <strong>डॉ भीवराव अंबेडकर </strong>को संविधान का प्रमुख निर्माता माना जाता है। अनुच्छेद 32 नागरिकों को उनके मौलिक अधिकारों का उल्लंघन होने पर संवैधानिक उपचार के लिए सर्वोच्च न्यायालय जाने का अधिकार प्रदान करता है। सुप्रीम कोर्ट ने फैसला सुनाया है कि अनुच्छेद 32 संविधान की मूल विशेषता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "18",
                    question_en: "<p>86. In April 2023, India&rsquo;s External Affairs Minister S Jaishankar launched the project for restoration of which of the following ghats?</p>",
                    question_hi: "<p>86. अप्रैल 2023 में, भारत के विदेश मंत्री एस. जयशंकर ने निम्नलिखित में से किस घाट के जीर्णोद्धार के लिए परियोजना की शुरूआत की थी?</p>",
                    options_en: ["<p>Manikarnika Ghat</p>", "<p>Assi Ghat</p>", 
                                "<p>Cheer Ghat</p>", "<p>Tulsi Ghat</p>"],
                    options_hi: ["<p>मणिकर्णिका घाट</p>", "<p>अस्सी घाट</p>",
                                "<p>चीर घाट</p>", "<p>तुलसी घाट</p>"],
                    solution_en: "<p>86.(d) <strong>Tulsi Ghat.</strong> External Affairs Minister S. Jaishankar on April 10 launched the \'Tulsi Ghat Restoration Project\' of Varanasi, during his visit to Uganda\'s Kampala. Other Ghats in Varanasi: Assi Ghat, Dashashwamedh Ghat, Manikarnika Ghat, Raj Ghat, Scindia Ghat, Maan-Mandir Ghat, Lalita Ghat, Jain Ghat or the Bachraj Ghat.</p>",
                    solution_hi: "<p>86.(d) <strong>तुलसी घाट</strong>। विदेश मंत्री एस जयशंकर ने 10 अप्रैल को युगांडा के कंपाला की अपनी यात्रा के दौरान वाराणसी के \'तुलसी घाट जीर्णोद्धार परियोजना\' का शुभारंभ किया। वाराणसी में अन्य घाट: अस्सी घाट, दशाश्वमेध घाट, मणिकर्णिका घाट, राज घाट, सिंधिया घाट, मान-मंदिर घाट, ललिता घाट, जैन घाट या बछराज घाट आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "18",
                    question_en: "<p>87. Lakshmibai National Institute of Physical Education (LNIPE) is located at ______.</p>",
                    question_hi: "<p>87. लक्ष्मीबाई राष्ट्रीय शारीरिक शिक्षा संस्थान (Lakshmibai National Institute of Physical Education - LNIPE) ______ में स्थित है।</p>",
                    options_en: ["<p>Uttar Pradesh</p>", "<p>Haryana</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>उत्तर प्रदेश</p>", "<p>हरियाणा</p>",
                                "<p>मध्यप्रदेश</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>87.(c) <strong>Madhya Pradesh. </strong>LNIPE (located in Gwalior) is a higher education institute deemed-to-be-university, under the aegis of the Ministry of Youth Affairs and Sports. It is committed to excelling in physical education, coaching and sports in the country. Former names - Lakshmibai College of Physical Education. Established - 1957. Campus - Shaktinagar.</p>",
                    solution_hi: "<p>87.(c) <strong>मध्य प्रदेश।</strong> LNIPE (ग्वालियर में स्थित) युवा मामले और खेल मंत्रालय के तत्वावधान में एक उच्च शिक्षण संस्थान है। यह देश में शारीरिक शिक्षा, कोचिंग और खेल में उत्कृष्टता के लिए प्रतिबद्ध है। पूर्व नाम - लक्ष्मीबाई कॉलेज ऑफ फिजिकल एजुकेशन। स्थापना - 1957। परिसर - शक्तिनगर।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "18",
                    question_en: "<p>88. _____ at Market Price refers to the sum total of factor incomes earned by residents of a country during an accounting year including net indirect taxes.</p>",
                    question_hi: "<p>88. किसी लेखांकन वर्ष के दौरान निवल अप्रत्यक्ष करों सहित एक देश के निवासियों द्वारा अर्जित कारक लागत का योगफल बाजार मूल्य पर _____ कहलाता है।</p>",
                    options_en: ["<p>Net National Product (NNP)</p>", "<p>Net Domestic Product (NDP)</p>", 
                                "<p>Gross National Product (GNP)</p>", "<p>Gross Domestic Product (GDP)</p>"],
                    options_hi: ["<p>निवल राष्ट्रीय उत्पाद (NNP)</p>", "<p>निवल घरेलू उत्पाद (NDP)</p>",
                                "<p>सकल राष्ट्रीय उत्पाद (GNP)</p>", "<p>सकल घरेलू उत्पाद (GDP)</p>"],
                    solution_en: "<p>88.(a) <strong>Net National Product (NNP)</strong>. It is at Factor Cost, also known as national income. When calculated at factor cost, the NNP includes the sum of wages, rents, interests, and profits distributed in an economy but excludes indirect taxes while including subsidies. Thus, it reflects the income earned through the productive activities of the country\'s normal residents. Factor Cost + Indirect Tax - Subsidy = Market Price. NNP = GNP &ndash; Depreciation.</p>",
                    solution_hi: "<p>88.(a) <strong>निवल राष्ट्रीय उत्पाद (NNP)</strong>। यह कारक लागत पर है, जिसे राष्ट्रीय आय भी कहा जाता है। कारक लागत पर गणना करने पर, NNP में अर्थव्यवस्था में वितरित मजदूरी, किराए, ब्याज और मुनाफे का योग शामिल होता है, लेकिन सब्सिडी को शामिल करते हुए अप्रत्यक्ष करों को शामिल नहीं किया जाता है। इस प्रकार, यह देश के सामान्य निवासियों की उत्पादक गतिविधियों के माध्यम से अर्जित आय को दर्शाता है। कारक लागत + अप्रत्यक्ष कर - सब्सिडी = बाजार मूल्य। NNP = GNP - मूल्यह्रास।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "18",
                    question_en: "<p>89. Which of the following Articles was amended by The Constitution Fiftieth Amendment Act, 1984?</p>",
                    question_hi: "<p>89. संविधान के पचासवें संशोधन अधिनियम, 1984 द्वारा निम्नलिखित में से किस अनुच्छेद में संशोधन किया गया था?</p>",
                    options_en: ["<p>Article 78</p>", "<p>Article 99</p>", 
                                "<p>Article 33</p>", "<p>Article 56</p>"],
                    options_hi: ["<p>अनुच्छेद 78</p>", "<p>अनुच्छेद 99</p>",
                                "<p>अनुच्छेद 33</p>", "<p>अनुच्छेद 56</p>"],
                    solution_en: "<p>89.(c) <strong>Article 33 -</strong> the Power of Parliament to modify the rights conferred by this Part in their application to Forces, etc. Article 78 - Duties of the Prime Minister as respects the furnishing of information to the President, etc. Article 99 - Oath or affirmation by members of the Parliament. Article 56 - Term of office of President.</p>",
                    solution_hi: "<p>89.(c) <strong>अनुच्छेद 33</strong> - सेनाओं आदि पर लागू होने वाले इस भाग द्वारा प्रदत्त अधिकारों को संशोधित करने की संसद की शक्ति। अनुच्छेद 78 - राष्ट्रपति को सूचना देने आदि के संबंध में प्रधान मंत्री के कर्तव्य। अनुच्छेद 99 - संसद के सदस्यों द्वारा शपथ या प्रतिज्ञान। अनुच्छेद 56 - राष्ट्रपति का कार्यकाल।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "18",
                    question_en: "<p>90. Which type of soil is found on about 40% of the total area of India?</p>",
                    question_hi: "<p>90. भारत के कुल क्षेत्रफल के लगभग 40% भाग पर किस प्रकार की मिट्टी पाई जाती है?</p>",
                    options_en: ["<p>Black soil</p>", "<p>Alluvial soil</p>", 
                                "<p>Laterite soil</p>", "<p>Red and Yellow soil</p>"],
                    options_hi: ["<p>काली मिट्टी</p>", "<p>जलोढ़ मिट्टी</p>",
                                "<p>लैटेराइट मिट्टी</p>", "<p>लाल और पीली मिट्टी</p>"],
                    solution_en: "<p>90.(b) <strong>Alluvial soil </strong>- the soil which is deposited by surface water, commonly found in river valleys and northern plains. It is highly fertile and rich in humus and lime. Crops cultivated on it include sugarcane, rice, maize, wheat, oilseeds, and pulses.</p>",
                    solution_hi: "<p>90.(b) <strong>जलोढ़ मिट्टी</strong> - सतही जल द्वारा एकत्र की गई मिट्टी, आमतौर पर नदी घाटियों और उत्तरी मैदानों में पायी जाती है। यह अत्यधिक उपजाऊ होती है और इसमें ह्यूमस और चूना प्रचुर मात्रा में मौजूद होता है। इस पर उगाई जाने वाली फसलों में गन्ना, चावल, मक्का, गेहूं, तिलहन और दालें आदि शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "18",
                    question_en: "<p>91. In which year did Eric A. Cornell, Wolfgang Ketterle and Carl E. Wieman receive the Nobel Prize in Physics for the achieving \'Bose-Einstein condensation in dilute gases of alkali atoms\'?</p>",
                    question_hi: "<p>91. एरिक ए. कॉर्नेल (Eric A. Cornell), वोल्फगैंग केटरले (Wolfgang Ketterle) और कार्ल ई. वीमन (Carl E. Wieman) को क्षार परमाणुओं की तनु गैसों में \'बोस-आइंस्टीन संघनन (Bose-Einstein condensation)\' प्राप्त करने के लिए भौतिकी का नोबेल पुरस्कार किस वर्ष में मिला था?</p>",
                    options_en: ["<p>2001</p>", "<p>2003</p>", 
                                "<p>2002</p>", "<p>2000</p>"],
                    options_hi: ["<p>2001 में</p>", "<p>2003 में</p>",
                                "<p>2002 में</p>", "<p>2000 में</p>"],
                    solution_en: "<p>91.(a) <strong>2001</strong>. A Bose-Einstein Condensate (BEC) is a state of matter formed when particles known as bosons are cooled to temperatures near absolute zero. This achievement allowed atoms to occupy the same quantum state, forming a new state of matter and advancing quantum phenomena at ultra-low temperatures.</p>",
                    solution_hi: "<p>91.(a) <strong>2001</strong>. बोस-आइंस्टीन कंडेनसेट (BEC) पदार्थ की एक अवस्था है जो तब बनती है जब बोसान नामक कणों को परम शून्य के नजदीक तापमान तक ठंडा किया जाता है। इस उपलब्धि ने परमाणुओं को एक ही क्वांटम अवस्था में रहने की अनुमति दी, जिससे पदार्थ की एक नई अवस्था बनी और अत्यंत कम तापमान पर क्वांटम परिघटनाओं के अध्ययन को आगे बढ़ाया गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "18",
                    question_en: "<p>92.Which of the following was a feature of the Indian economy before the British rule?</p>",
                    question_hi: "<p>92. ब्रिटिश शासन से पहले निम्नलिखित में से कौन-सी भारतीय अर्थव्यवस्था की एक विशेषता थी?</p>",
                    options_en: ["<p>Independent economy</p>", "<p>Prosperous economy</p>", 
                                "<p>Self-reliant economy</p>", "<p>Dependent economy</p>"],
                    options_hi: ["<p>स्वतंत्र अर्थव्यवस्था</p>", "<p>समृद्ध अर्थव्यवस्था</p>",
                                "<p>आत्मनिर्भर अर्थव्यवस्था</p>", "<p>आश्रित अर्थव्यवस्था</p>"],
                    solution_en: "<p>92.(a) <strong>Independent economy.</strong> A prosperous economy is a successful system of trade and industry that usually earns a lot. A self-reliant economy is an economy that is less dependent on other countries and does not require aid from outside the country. Economic dependence in the context of a country\'s economy refers to a situation where a nation heavily relies on another country or countries for its economic growth and development.</p>",
                    solution_hi: "<p>92.(a) <strong>स्वतंत्र अर्थव्यवस्था</strong>। एक समृद्ध अर्थव्यवस्था व्यापार और उद्योग की एक सफल प्रणाली है जो आम तौर पर बहुत अधिक कमाई करती है। एक आत्मनिर्भर अर्थव्यवस्था एक ऐसी अर्थव्यवस्था है जो अन्य देशों पर कम निर्भर होती है और उसे देश के बाहर से सहायता की आवश्यकता नहीं होती है। किसी देश की अर्थव्यवस्था के संदर्भ में आर्थिक निर्भरता एक ऐसी स्थिति को संदर्भित करती है जहाँ एक राष्ट्र अपने आर्थिक विकास और विकास के लिए दूसरे देश या देशों पर बहुत अधिक निर्भर करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "18",
                    question_en: "<p>93. How many countries participated in the first Asian Games held in 1951?</p>",
                    question_hi: "<p>93. 1951 में आयोजित प्रथम एशियाई खेलों (Asian Game) में कितने देशों ने भाग लिया था?</p>",
                    options_en: ["<p>16</p>", "<p>11</p>", 
                                "<p>20</p>", "<p>24</p>"],
                    options_hi: ["<p>16</p>", "<p>11</p>",
                                "<p>20</p>", "<p>24</p>"],
                    solution_en: "<p>93.(b) <strong>11.</strong> The 1951 Asian Games, officially known as the First Asian Games, was a multi-sport event celebrated in New Delhi, India from 4 to 11 March 1951. Motto - Play the game in the spirit of the game. Opened by - Rajendra Prasad (President of India). Main venue - National Stadium (Major Dhyan Chand National Stadium). 1st Rank - Japan (Gold-24, Silver-21, Bronze-15). 2nd Rank - India (Gold-15, Silver-16, Bronze-20).</p>",
                    solution_hi: "<p>93.(b)<strong> 11.</strong>1951 एशियाई खेल, जिन्हें आधिकारिक तौर पर प्रथम एशियाई खेल के रूप में जाना जाता है, 4 से 11 मार्च 1951 तक नई दिल्ली, भारत में आयोजित किया गया एक बहु-खेल आयोजन था। आदर्श वाक्य - खेल को खेल की भावना से खेलें। उद्घाटनकर्ता - राजेंद्र प्रसाद (भारत के राष्ट्रपति)। मुख्य स्थल - राष्ट्रीय स्टेडियम (मेजर ध्यानचंद राष्ट्रीय स्टेडियम)। प्रथम स्थान - जापान (स्वर्ण-24, रजत-21, कांस्य-15)। द्वितीय स्थान - भारत (स्वर्ण-15, रजत-16, कांस्य-20)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "18",
                    question_en: "<p>94. _______ was an Indian dancer and choreographer best known for creating a fusion style of dance adapting European theatrical techniques to Indian classical and folk - dance forms.</p>",
                    question_hi: "<p>94. _______ एक भारतीय नृत्य कलाकार और कोरियोग्राफर थे/थीं, जिन्हें भारतीय शास्त्रीय और लोक-नृत्य शैलियों के लिए यूरोपीय नाट्य तकनीक अपनाकर नृत्य की एक मिश्रित शैली (fusion style) का सृजन करने के लिए जाना जाता था।</p>",
                    options_en: ["<p>Smita Nagdev</p>", "<p>Ravi Shankar</p>", 
                                "<p>Shovana Narayan</p>", "<p>Uday Shankar</p>"],
                    options_hi: ["<p>स्मिता नागदेव</p>", "<p>रवि शंकर</p>",
                                "<p>शोभना नारायण</p>", "<p>उदय शंकर</p>"],
                    solution_en: "<p>94.(d) <strong>Uday Shankar</strong>. Awards - Padma Vibhushan (1971), Sangeet Natak Akademi Fellowship (1962). Shovana Narayan is a recognised Indian Kathak dancer. Her Award - Padma Shri (1992). Pandit Ravi Shankar was an Indian sitarist and composer. Award - Bharat Ratna (1999). Smita Nagdev - Sitar player.</p>",
                    solution_hi: "<p>94.(d) <strong>उदय शंक</strong>र। पुरस्कार - पद्म विभूषण (1971), संगीत नाटक अकादमी फ़ेलोशिप (1962)। शोवना नारायण एक मान्यता प्राप्त भारतीय कथक नृत्यांगना हैं। पुरस्कार - पद्म श्री (1992)। पंडित रविशंकर एक भारतीय सितारवादक और संगीतकार थे। पुरस्कार - भारत रत्न (1999)। स्मिता नागदेव - सितार वादक।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "18",
                    question_en: "<p>95. In Microsoft PowerPoint, what is the shortcut key combination to insert a new slide in a presentation?</p>",
                    question_hi: "<p>95. माइक्रोसॉफ्ट पावर पॉइंट (Microsoft Power Point) में, प्रेजेंटेशन में नई स्लाइड जोड़ने (insert) के लिए शॉर्टकट कुंजी संयोजन क्या है?</p>",
                    options_en: ["<p>Ctrl + I</p>", "<p>Ctrl + N</p>", 
                                "<p>Ctrl + D</p>", "<p>Ctrl + M</p>"],
                    options_hi: ["<p>Ctrl + I</p>", "<p>Ctrl + N</p>",
                                "<p>Ctrl + D</p>", "<p>Ctrl + M</p>"],
                    solution_en: "<p>95.(d) <strong>Ctrl + M.</strong> Microsoft PowerPoint is a presentation program, created by Robert Gaskins, Tom Rudkin and Dennis Austin at a software company named Forethought, Inc. Shortcut keys in Microsoft Powerpoint : Ctrl + I - Add or remove italics to selected text. Ctrl + N - Create a new slide. Ctrl + D - Duplicate the selected object or slide.</p>",
                    solution_hi: "<p>95.(d) <strong>Ctrl + M.</strong> माइक्रोसॉफ्ट पावरपॉइंट एक प्रेजेंटेशन प्रोग्राम है, जिसे रॉबर्ट गैस्किन्स, टॉम रुडकिन और डेनिस ऑस्टिन ने फोरथॉट, इंक. नामक एक सॉफ्टवेयर कंपनी में बनाया है। माइक्रोसॉफ्ट पावरपॉइंट में शॉर्टकट की (keys) : Ctrl + I - सेलेक्ट टेक्स्ट में इटैलिक्स जोड़ें या हटाएँ। Ctrl + N - एक नई स्लाइड बनाएँ। Ctrl + D - चयनित ऑब्जेक्ट या स्लाइड की प्रतिलिपि बनाएँ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "18",
                    question_en: "<p>96. On 9 August 2021, the Parliament had passed the Limited Liability Partnership (Amendment) Bill, 2021. It amended the:</p>",
                    question_hi: "<p>96. 9 अगस्त 2021 को, संसद ने सीमित देयता भागीदारी (संशोधन) विधेयक, 2021 पारित किया था। इसने _______ में संशोधन किया था।</p>",
                    options_en: ["<p>Limited Liability Partnership Act, 2004</p>", "<p>Limited Liability Partnership Act, 2008</p>", 
                                "<p>Limited Liability Partnership Act, 2012</p>", "<p>Limited Liability Partnership Act, 2016</p>"],
                    options_hi: ["<p>सीमित देयता भागीदारी अधिनियम, 2004</p>", "<p>सीमित देयता भागीदारी अधिनियम, 2008</p>",
                                "<p>सीमित देयता भागीदारी अधिनियम, 2012</p>", "<p>सीमित देयता भागीदारी अधिनियम, 2016</p>"],
                    solution_en: "<p>96.(b) <strong>Limited Liability Partnership Act, 2008. </strong>It was published in the official Gazette of India on 9 January 2009 and has been in effect since 31 March 2009. LLP Amendment Bill, 2021 passed on 28th July 2021 by both house of Parliament. 29 amendments were made to LLP Act 2008 by LLP Amendment Bill 2021.</p>",
                    solution_hi: "<p>96.(b) <strong>सीमित देयता भागीदारी अधिनियम, 2008</strong>। यह 9 जनवरी 2009 को भारत के आधिकारिक राजपत्र में प्रकाशित हुआ था और 31 मार्च 2009 से प्रभावी है। एलएलपी (LLP) संशोधन विधेयक, 2021 28 जुलाई 2021 को संसद के दोनों सदनों द्वारा पारित किया गया। एलएलपी (LLP) संशोधन विधेयक 2021 द्वारा एलएलपी (LLP) अधिनियम 2008 में 29वां संशोधन किए गए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "18",
                    question_en: "<p>97. A distinctive cross-shaped constellation best seen in the northern hemisphere during the summer and fall months around September is:</p>",
                    question_hi: "<p>97. सितंबर के आसपास गर्मियों और पतझड़ के महीनों के दौरान उत्तरी गोलार्ध में सबसे स्पष्ट रूप से देखा जा सकने वाला एक विशिष्ट क्रॉस-आकार (cross-shaped) का तारामंडल है:</p>",
                    options_en: ["<p>Cygnus</p>", "<p>Ursa Major</p>", 
                                "<p>Pegasus</p>", "<p>Cassiopeia</p>"],
                    options_hi: ["<p>सिग्नस (Cygnus)</p>", "<p>सप्तर्षिमंडल (Ursa Major)</p>",
                                "<p>पेगसस (Pegasus)</p>", "<p>शर्मिष्ठा (Cassiopeia)</p>"],
                    solution_en: "<p>97.(a) <strong>Cygnus</strong>. It is a northern constellation on the plane of the Milky Way, deriving its name from the Latinized Greek word for swan. Ursa Major, also known as the Great Bear, is a constellation in the northern sky. It is visible throughout the year, but it is best seen from April to September. Pegasus is a constellation in the northern sky which appears in autumn. Cassiopeia is a constellation visible in the northern hemisphere year-round at latitudes above 34&deg;N.</p>",
                    solution_hi: "<p>97.(a) <strong>सिग्नस </strong>(Cygnus)। यह आकाशगंगा में स्थित एक उत्तरी तारामंडल है, जिसका नाम लैटिनकृत ग्रीक शब्द हंस से लिया गया है। उर्सा मेजर, जिसे ग्रेट बियर के नाम से भी जाना जाता है, उत्तरी आकाश में एक तारामंडल है। यह सम्पूर्ण वर्ष दिखाई देता है, लेकिन यह अप्रैल से सितंबर तक स्पष्ट दिखाई देता है। पेगासस उत्तरी आकाश में एक तारामंडल है जो शरद ऋतु में दिखाई देता है। कैसिओपिया एक तारामंडल है जो उत्तरी गोलार्ध में 34&deg;N से ऊपर के अक्षांशों पर साल भर दिखाई देता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "18",
                    question_en: "<p>98. In Kabaddi, a super tackle is when a raider is caught/self out/declared out with or less than how many defenders defending?</p>",
                    question_hi: "<p>98. कबड्डी में, एक सुपर टैकल (super tackle) तब होता है जब एक रेडर _______ या उससे कम डिफेंडर के साथ पकड़ा जाता है/स्वयं आउट हो जाता है/आउट घोषित कर दिया जाता है?</p>",
                    options_en: ["<p>Six</p>", "<p>Five</p>", 
                                "<p>Three</p>", "<p>Four</p>"],
                    options_hi: ["<p>छ:</p>", "<p>पांच</p>",
                                "<p>तीन</p>", "<p>चार</p>"],
                    solution_en: "<p>98.(c) <strong>Three</strong>. Kabaddi is a contact team sport that originated in India and is popular in many South Asian countries. The game is played with two teams of seven players each. Other terminologies in Kabaddi : Chain Tackle - A defensive tactic where the opponents form chains by holding hands in twos or threes. Dubki - An escape technique used by raiders who face a chain tackle. Golden Raid - It happens when there is a tie between two teams at the end of extra time.</p>",
                    solution_hi: "<p>98.(c) <strong>तीन</strong>। कबड्डी एक संपर्क टीम खेल है जिसकी उत्पत्ति भारत में हुई और यह कई दक्षिण एशियाई देशों में लोकप्रिय है। यह खेल सात खिलाड़ियों वाली दो टीमों के साथ खेला जाता है। कबड्डी में अन्य शब्दावली: चेन टैकल - एक रक्षात्मक रणनीति जिसमें विरोधी दो या तीन की संख्या में हाथ पकड़कर चेन बनाते हैं। डुबकी - चेन टैकल का सामना करने वाले रेडर द्वारा इस्तेमाल की जाने वाली भागने की तकनीक। गोल्डन रेड - यह तब होता है जब अतिरिक्त समय के अंत में दो टीमों के बीच बराबरी होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "18",
                    question_en: "<p>99. Who among the following wrote the popular Telugu patriotic song &lsquo;Desamunu preminchumanna, manchi annadi penchumanna&rsquo; meaning &lsquo;Love the nation, grow the goodness&rsquo;?</p>",
                    question_hi: "<p>99. निम्नलिखित में से किसने लोकप्रिय तेलुगु देशभक्ति गीत \'देसामुनु प्रेमिंचुमत्रा, मंची अन्नादी पंचुमत्रा\' लिखा है, जिसका अर्थ है \'राष्ट्र से प्रेम करो, अच्छाई को विकसित करो (Love the nation, grow the goodness)\'?</p>",
                    options_en: ["<p>Gurajada Apparao</p>", "<p>Sri Krishnadevaraya</p>", 
                                "<p>Bammera Potana</p>", "<p>Goparaju Ramachandra Rao</p>"],
                    options_hi: ["<p>गुरजादा अप्पाराव (Gurajada Apparao)</p>", "<p>श्री कृष्णदेवराय (Sri Krishnadevaraya)</p>",
                                "<p>बम्मेरा पोताना (Bammera Potana)</p>", "<p>गोपाराजू रामचंद्र राव (Goparaju Ramachandra Rao)</p>"],
                    solution_en: "<p>99.(a) <strong>Gurajada Apparao</strong>. Gurajada Venkata Apparao was an Indian playwright, dramatist, poet, and writer known for his works in Telugu theatre. Rao wrote the play Kanyasulkam in 1892, which is considered as the greatest play in the Telugu language. He holds the titles Kavisekhara and Abyudaya Kavitha Pithamahudu. In 1910, Rao scripted the widely known Telugu patriotic song \"Desamunu Preminchumanna\".</p>",
                    solution_hi: "<p>99.(a) <strong>गुरजादा अप्पाराव</strong>। गुरजादा वेंकट अप्पाराव एक भारतीय नाटककार, कलाकार, कवि और लेखक थे जो तेलुगु रंगमंच के लिए जाने जाते थे। राव ने 1892 में कन्यासुलकम नामक नाटक लिखा था, जिसे तेलुगु भाषा का सबसे महान नाटक माना जाता है। उन्हें कविशेखर और अब्युदय कविता पिथामहुदु की उपाधियाँ प्राप्त हैं। 1910 में, राव ने व्यापक रूप से प्रसिद्ध तेलुगु देशभक्ति गीत \"देसमुनु प्रेमिंचुमन्ना\" की पटकथा लिखी थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. How can you insert a blank row at the top of a worksheet?</p>",
                    question_hi: "<p>100. आप वर्कशीट के शीर्ष पर एक रिक्त पंक्ति (blank row) कैसे सम्मिलित कर सकते हैं?</p>",
                    options_en: ["<p>Drag the mouse on the row border to select the row and click Insert in the Cells group on the Home tab</p>", "<p>Click on the row number and press the Insert key on the keyboard</p>", 
                                "<p>Click on the row number and press the Delete key on the keyboard.</p>", "<p>Right-click on the row number and select Insert.</p>"],
                    options_hi: ["<p>पंक्ति का चयन करने के लिए माउस को पंक्ति बॉर्डर पर ड्रैग करें और होम टैब पर सेल समूह में इन्सर्ट ((Insert) पर क्लिक करें।</p>", "<p>पंक्ति संख्या पर क्लिक करें और कीबोर्ड पर इन्सर्ट ((Insert) कुंजी (Key) दबाएँ।</p>",
                                "<p>पंक्ति संख्या पर क्लिक करें और कीबोर्ड पर डिलीट (Delete) कुंजी (Key) दबाएं।</p>", "<p>पंक्ति संख्या पर राइट-क्लिक करें और इन्सर्ट (Insert) चुनें।</p>"],
                    solution_en: "<p>100.(a) Microsoft Excel is a spreadsheet editor developed by Microsoft for Windows, macOS, Android, iOS and iPadOS. It features calculation or computation capabilities, graphing tools, pivot tables, and a macro programming language called Visual Basic for Applications (VBA). Excel forms part of the Microsoft 365 suite of software.</p>",
                    solution_hi: "<p>100.(a) Microsoft Excel एक स्प्रेडशीट संपादक है जिसे Microsoft ने Windows, macOS, Android, iOS और iPadOS के लिए विकसित किया है। इसमें गणना या संगणना क्षमताएँ, ग्राफ़िंग टूल, पिवट टेबल और Visual Basic for Applications (VBA) नामक मैक्रो प्रोग्रामिंग भाषा शामिल है। Excel Microsoft 365 सॉफ़्टवेयर के सुइट का हिस्सा है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>