<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: " <p>1. </span><span style=\"font-family:Cambria Math\">What is the size of internet protocol version 6 protocol address?</span></p>",
                    question_hi: " <p>1. </span><span style=\"font-family:Kokila\">इंटरनेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संस्करण</span><span style=\"font-family:Cambria Math\">(version) 6 </span><span style=\"font-family:Kokila\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एड्रेस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">क्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> 128 bits </span></p>", " <p> 34 bits</span></p>", 
                                " <p> 68 bits </span></p>", " <p> 255 bits</span></p>"],
                    options_hi: [" <p> 128 </span><span style=\"font-family:Kokila\">बिट्स</span></p>", " <p> 34 </span><span style=\"font-family:Kokila\">बिट्स</span></p>",
                                " <p> 68 </span><span style=\"font-family:Kokila\">बिट्स</span></p>", " <p> 255 </span><span style=\"font-family:Kokila\">बिट्स</span></p>"],
                    solution_en: " <p>1.(a) </span><span style=\"font-family:Cambria Math\">IPv6 ( internet protocol version 6) uses a 128-bit address space, which has no practical limit on global addressability and provides 3.4 × 10</span><span style=\"font-family:Cambria Math\"> 50 </span><span style=\"font-family:Cambria Math\">unique addresses.</span></p>",
                    solution_hi: " <p>1.(a) </span><span style=\"font-family:Cambria Math\">IPv6 ( internet protocol version 6) 128-</span><span style=\"font-family:Kokila\">बिट</span><span style=\"font-family:Cambria Math\"> address space  </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">जिसकी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वैश्विक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एड्रेसेबिलिटी</span><span style=\"font-family:Cambria Math\"> (global addressability) </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कोई</span><span style=\"font-family:Cambria Math\"> practical limit  </span><span style=\"font-family:Kokila\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">यह</span><span style=\"font-family:Cambria Math\"> 3.4 × 10</span><span style=\"font-family:Cambria Math\"> 50 </span><span style=\"font-family:Cambria Math\">unique addresses </span><span style=\"font-family:Kokila\">प्रदान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: " <p>2. </span><span style=\"font-family:Cambria Math\">In MS Word, if you press the tab key within a table, the cursor moves you to the _______.</span></p>",
                    question_hi: " <p>2. </span><span style=\"font-family:Cambria Math\">MS Word </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">यदि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टेबल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">भीतर</span><span style=\"font-family:Cambria Math\"> tab key </span><span style=\"font-family:Kokila\">दबाते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कर्सर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आपको</span><span style=\"font-family:Cambria Math\"> _______ </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    options_en: [" <p> previous column </span></p>", " <p> next column  </span></p>", 
                                " <p> previous row</span></p>", " <p> next table</span></p>"],
                    options_hi: [" <p> previous column </span></p>", " <p> next column  </span></p>",
                                " <p> previous row</span></p>", " <p> next table</span></p>"],
                    solution_en: " <p>2.(b)</span><span style=\"font-family:Cambria Math\"> In MS Word, if you press the tab key within a table, the cursor moves you to the next column .</span></p>",
                    solution_hi: " <p>2.(b)</span><span style=\"font-family:Cambria Math\"> MS Word </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">यदि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टेबल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">भीतर</span><span style=\"font-family:Cambria Math\"> tab key </span><span style=\"font-family:Kokila\">दबाते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कर्सर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आपको</span><span style=\"font-family:Cambria Math\"> next column  </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: " <p>3. </span><span style=\"font-family:Cambria Math\">In general, which of the following folders is used to store incoming emails?  </span></p>",
                    question_hi: " <p>3. </span><span style=\"font-family:Kokila\">आम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तौर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">आने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ईमेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्टोर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फोल्डर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Outbox </span></p>", " <p> Inbox</span></p>", 
                                " <p> Sent </span></p>", " <p> Spam </span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Kokila\">आउटबॉक्स</span><span style=\"font-family:Cambria Math\"> [outbox]</span></p>", " <p> </span><span style=\"font-family:Kokila\">इनबॉक्स</span><span style=\"font-family:Cambria Math\"> [Inbox ]</span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">सेन्ट</span><span style=\"font-family:Cambria Math\">  [Sent ]</span></p>", " <p> </span><span style=\"font-family:Kokila\">स्पैम</span><span style=\"font-family:Cambria Math\"> [Spam ]</span></p>"],
                    solution_en: " <p>3.(b)</span><span style=\"font-family:Cambria Math\"> Inbox folders are used to store incoming emails. </span></p>",
                    solution_hi: " <p>3.(b)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तौर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">आने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ईमेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्टोर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">इनबॉक्स</span><span style=\"font-family:Cambria Math\"> [Inbox ] </span><span style=\"font-family:Kokila\">फोल्डर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> |</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: " <p>4. </span><span style=\"font-family:Cambria Math\">In MS Word 2007, which of the following keyboard shortcuts can be used to highlight text from the current position to the end of the current line?</span></p>",
                    question_hi: " <p>4. </span><span style=\"font-family:Cambria Math\">MS Word 2007 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">, current position </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> current line </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अंत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तक</span><span style=\"font-family:Cambria Math\"> Text </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हाइलाइट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कीबोर्ड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">शॉर्टकट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Shift + End </span></p>", " <p> Shift + Home</span></p>", 
                                " <p> Ctrl + End </span></p>", " <p> Ctrl + Home </span></p>"],
                    options_hi: [" <p> Shift + End </span></p>", " <p> Shift + Home</span></p>",
                                " <p> Ctrl + End </span></p>", " <p> Ctrl + Home </span></p>"],
                    solution_en: " <p>4.(a) Shift + End </span><span style=\"font-family:Cambria Math\">-  highlights all text from the current position to the end of the line. </span></p> <p><span style=\"font-family:Cambria Math\">Shift + Home</span><span style=\"font-family:Cambria Math\"> - highlights all text from the current position to the start of the line.</span></p> <p><span style=\"font-family:Cambria Math\">Ctrl+End-</span><span style=\"font-family:Cambria Math\"> Move the cursor to the end of the document. </span></p> <p><span style=\"font-family:Cambria Math\">Ctrl+Home</span><span style=\"font-family:Cambria Math\">- Move the cursor to the beginning of the document.</span></p>",
                    solution_hi: " <p>4.(a) Shift + End </span><span style=\"font-family:Cambria Math\">-   </span><span style=\"font-family:Kokila\">इसका</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> current position </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> current line </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अंत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तक</span><span style=\"font-family:Cambria Math\"> Text </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हाइलाइट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Kokila\">है</span></p> <p><span style=\"font-family:Cambria Math\">Shift + Home</span><span style=\"font-family:Cambria Math\"> - </span><span style=\"font-family:Kokila\">इसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> current position </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">शुरुआत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सभी</span><span style=\"font-family:Cambria Math\"> Text </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हाइलाइट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p> <p><span style=\"font-family:Cambria Math\">Ctrl+End-</span><span style=\"font-family:Cambria Math\">   </span><span style=\"font-family:Kokila\">इसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> cursor </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\">  document </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अंत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हे</span><span style=\"font-family:Cambria Math\"> |</span></p> <p><span style=\"font-family:Cambria Math\">Ctrl+Home</span><span style=\"font-family:Cambria Math\">-   </span><span style=\"font-family:Kokila\">इसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> cursor </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\">  document </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">शुरुआत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हे</span><span style=\"font-family:Cambria Math\"> </span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: " <p>5. </span><span style=\"font-family:Cambria Math\">A ________ is an intersection of a row and a column in a worksheet.  </span></p>",
                    question_hi: " <p>5. </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> ________ </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्कशीट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कॉलम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> intersection </span><span style=\"font-family:Kokila\">है।</span></p>",
                    options_en: [" <p> cell </span></p>", " <p> row</span></p>", 
                                " <p> workbook </span></p>", " <p> column</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\">  </span></p>", " <p> </span><span style=\"font-family:Kokila\">रौ</span><span style=\"font-family:Cambria Math\"> </span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">वर्कबुक</span><span style=\"font-family:Cambria Math\">  </span></p>", " <p> </span><span style=\"font-family:Kokila\">कॉलम</span><span style=\"font-family:Cambria Math\"> </span></p>"],
                    solution_en: " <p>5.(a) A cell </span><span style=\"font-family:Cambria Math\">is a rectangular area formed by the intersection of a column and a row. Cells are identified by the Cell Name , which is found by combining the Column Letter with the Row Number.</span></p>",
                    solution_hi: " <p>5.(a </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आयताकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">क्षेत्र</span><span style=\"font-family:Cambria Math\"> ( rectangular area )  </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कॉलम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> intersection </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बनता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नेम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पहचान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\"> Column Letter  </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> Row number  </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">साथ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जोड़कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बनता</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: " <p>6. </span><span style=\"font-family:Cambria Math\">Which protocol is used for communication on the internet?</span></p>",
                    question_hi: " <p>6. </span><span style=\"font-family:Kokila\">इंटरनेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संचार</span><span style=\"font-family:Cambria Math\">( communication )  </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> DNS </span></p>", " <p> TCP/IP</span></p>", 
                                " <p> SNMP </span></p>", " <p> DMZ</span></p>"],
                    options_hi: [" <p> DNS </span></p>", " <p> TCP/IP</span></p>",
                                " <p> SNMP </span></p>", " <p> DMZ</span></p>"],
                    solution_en: " <p>6.(b)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">TCP/IP </span><span style=\"font-family:Cambria Math\">stands for Transmission Control Protocol/Internet Protocol and is a suite of communication protocols used to interconnect network devices on the internet.</span></p> <p><span style=\"font-family:Cambria Math\">The </span><span style=\"font-family:Cambria Math\">domain name system (DNS)</span><span style=\"font-family:Cambria Math\"> is a naming database in which internet domain names are located and translated into Internet Protocol (IP) addresses. </span></p> <p><span style=\"font-family:Cambria Math\">SNMP </span><span style=\"font-family:Cambria Math\">stands for Simple Network Management Protocol  is an Internet-standard protocol for handling devices on IP networks. </span></p> <p><span style=\"font-family:Cambria Math\">In computer security, </span><span style=\"font-family:Cambria Math\">DMZ </span><span style=\"font-family:Cambria Math\">stands for a demilitarized zone and is also known as perimeter network, or screened subnet.</span></p>",
                    solution_hi: " <p>6.(b) </span><span style=\"font-family:Cambria Math\">TCP/IP </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अर्थ</span><span style=\"font-family:Cambria Math\"> Transmission Control Protocol/Internet Protocol  </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">यह</span><span style=\"font-family:Cambria Math\"> communication protocol </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जिसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">इंटरनेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> network devices </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आपस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जोड़ने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p> <p><span style=\"font-family:Cambria Math\">domain name system (DNS)</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> naming database  </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जिसमें</span><span style=\"font-family:Cambria Math\">  internet domain name  </span><span style=\"font-family:Kokila\">स्थित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">इंटरनेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Kokila\">आईपी</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Kokila\">एड्रेस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> translate </span><span style=\"font-family:Kokila\">होते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span></p> <p><span style=\"font-family:Cambria Math\">SNMP </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मतलब</span><span style=\"font-family:Cambria Math\"> Simple Network Management Protocol  </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जो</span><span style=\"font-family:Cambria Math\">  IP networks  </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> devices  </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> handle </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> Internet-standard protocol </span><span style=\"font-family:Kokila\">है।</span></p> <p><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सुरक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">DMZ (</span><span style=\"font-family:Cambria Math\">demilitarized zone </span><span style=\"font-family:Cambria Math\">)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होता</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">इसे</span><span style=\"font-family:Cambria Math\"> perimeter network,  </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्क्रीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गए</span><span style=\"font-family:Cambria Math\"> subnet  </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">भी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: " <p>7. </span><span style=\"font-family:Cambria Math\">In MS Excel 2010, what will be the result of the below function:</span></p> <p><span style=\"font-family:Cambria Math\">=ROUND(1.6666666666666,2)</span></p>",
                    question_hi: " <p>7. </span><span style=\"font-family:Cambria Math\">MS Excel 2010 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">नीचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फ़ंक्शन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">परिणाम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">क्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होगा</span><span style=\"font-family:Cambria Math\">:</span></p> <p><span style=\"font-family:Cambria Math\">=ROUND(1.6666666666666,2)</span></p>",
                    options_en: [" <p> 2.00 </span></p>", " <p> 1.67 </span></p>", 
                                " <p> 1.7</span></p>", " <p> 1.68</span></p>"],
                    options_hi: [" <p> 2.00 </span></p>", " <p> 1.67 </span></p>",
                                " <p> 1.7</span></p>", " <p> 1.68</span></p>"],
                    solution_en: " <p>7.(b) </span><span style=\"font-family:Cambria Math\">The ROUND function rounds a number to a specified number of digits. Value of =ROUND(1.6666666666666,2) is 1.67. </span></p>",
                    solution_hi: " <p>7.(b) </span><span style=\"font-family:Cambria Math\">ROUND </span><span style=\"font-family:Kokila\">फ़ंक्शन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अंकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> specified number </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> Round </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> =ROUND(1.666666666666666,2) </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मान</span><span style=\"font-family:Cambria Math\"> 1.67 </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: " <p>8. </span><span style=\"font-family:Cambria Math\">In MS Excel 2010, in which group of the Home tab, is Format Cell dialogue box available? </span></p>",
                    question_hi: " <p>8. </span><span style=\"font-family:Cambria Math\">MS Excel 2010 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टैब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ग्रुप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फॉर्मेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">डायलॉग</span><span style=\"font-family:Cambria Math\"> ( Format Cell dialogue ) </span><span style=\"font-family:Kokila\">बॉक्स</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपलब्ध</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होता</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Editing </span></p>", " <p> Clipboard</span></p>", 
                                " <p> Font </span></p>", " <p> Language</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Kokila\">एडिटिंग</span><span style=\"font-family:Cambria Math\"> [Editing] </span></p>", " <p> </span><span style=\"font-family:Kokila\">क्लिपबोर्ड</span><span style=\"font-family:Cambria Math\"> [Clipboard]</span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">फॉण्ट</span><span style=\"font-family:Cambria Math\"> [Font]</span></p>", " <p> </span><span style=\"font-family:Kokila\">लैंग्वेज</span><span style=\"font-family:Cambria Math\"> [language]</span></p>"],
                    solution_en: " <p>8.(c) In MS Excel 2010 Home tab contains </span><span style=\"font-family:Cambria Math\">Clipboard , Font (Fonts, Size, Increase/Decrease Font Size, Bold, Italic, Underline, Borders, Fill color, Font color and Options for fonts), Alignment , Number format , Styles, Cells  and Editing .</span></p>",
                    solution_hi: " <p>8.(c) MS EXCEL 2010 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टैब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Clipboard , Font (Fonts, Size, Increase/Decrease Font Size, Bold, Italic, Underline, Borders, Fill color, Font color </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> Options for fonts), Alignment, Number format , Styles, Cells  </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Editing </span><span style=\"font-family:Kokila\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\">.|</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: " <p>9. In Computer terminology, what is a nibble?</span></p>",
                    question_hi: " <p>9. </span><span style=\"font-family:Kokila\">कंप्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">शब्दावली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">निबल</span><span style=\"font-family:Cambria Math\"> ( Nibble )  </span><span style=\"font-family:Kokila\">क्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Half a byte</span></p>", " <p> A kilobyte</span></p>", 
                                " <p> A gigabyte</span></p>", " <p> A terabyte</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Kokila\">आधा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बाइट</span><span style=\"font-family:Cambria Math\"> [ Half a byte]</span><span style=\"font-family:Cambria Math\">         </span></p>", " <p> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किलोबाइट</span><span style=\"font-family:Cambria Math\">[A kilobyte]</span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गीगाबाइट</span><span style=\"font-family:Cambria Math\">  [A gigabyte]</span></p>", " <p> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टेराबाइट</span><span style=\"font-family:Cambria Math\"> [A terabyte]</span></p>"],
                    solution_en: " <p>9.(a) A nibble is a four-bit aggregation or half an octet( byte ). 1 Nibble= 4 bit.</span></p> <p><span style=\"font-family:Cambria Math\">Extra facts :- </span></p> <p><span style=\"font-family:Cambria Math\">1 Kilobyte  :-    1024 bytes (2</span><span style=\"font-family:Cambria Math\">10</span><span style=\"font-family:Cambria Math\">byte)</span></p> <p><span style=\"font-family:Cambria Math\">1 gigabyte (GB) :- 1,024 megabytes (MB)</span></p> <p><span style=\"font-family:Cambria Math\">1 terabyte (TB) :-  1,024 gigabytes.</span></p>",
                    solution_hi: " <p>9.(a) </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> Nibble  </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\">  four-bit aggregation  </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आधा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ऑक्टेट</span><span style=\"font-family:Cambria Math\"> ( byte )  </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> 1 </span><span style=\"font-family:Kokila\">निबल</span><span style=\"font-family:Cambria Math\"> = 4 </span><span style=\"font-family:Kokila\">बिट</span><span style=\"font-family:Cambria Math\"> (bit)</span><span style=\"font-family:Kokila\">।</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">Extra facts :- </span></p> <p><span style=\"font-family:Cambria Math\">1 Kilobyte  :-    1024 bytes (2</span><span style=\"font-family:Cambria Math\">10</span><span style=\"font-family:Cambria Math\">byte)</span></p> <p><span style=\"font-family:Cambria Math\">1 gigabyte (GB) :- 1,024 megabytes (MB)</span></p> <p><span style=\"font-family:Cambria Math\">1 terabyte (TB) :-  1,024 gigabytes.</span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "31",
                    question_en: " <p>10. </span><span style=\"font-family:Cambria Math\">In MS Word 2016, the ‘Insert Footnote’ option appears under the ______ menu item.</span></p>",
                    question_hi: " <p>10. </span><span style=\"font-family:Cambria Math\">MS Word 2016 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">, ______ </span><span style=\"font-family:Kokila\">मेनू</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आइटम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अंतर्गत</span><span style=\"font-family:Cambria Math\">  ‘Insert Footnote’ </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दिखाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">देता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    options_en: [" <p> Design </span></p>", " <p> Mailings</span></p>", 
                                " <p> Insert </span></p>", " <p> References  </span></p>"],
                    options_hi: [" <p> Design </span></p>", " <p> Mailings</span></p>",
                                " <p> Insert </span></p>", " <p> References </span></p>"],
                    solution_en: " <p>10.(d) </span><span style=\"font-family:Cambria Math\">In MS Word 2016, the ‘Insert Footnote’ option appears under the References. </span></p>",
                    solution_hi: " <p>10.(d) </span><span style=\"font-family:Cambria Math\">MS Word 2016 </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\">,  References </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अंतर्गत</span><span style=\"font-family:Cambria Math\">  ‘Insert Footnote’ </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दिखाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">देता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "31",
                    question_en: " <p>11.</span><span style=\"font-family:Cambria Math\"> Which of the following refers to a huge database of internet resources such as webpages, newsgroups, programs, images etc, and helps to locate information on the World Wide Web?</span></p>",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कौन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">webpages, newsgroups, programs, images </span><span style=\"font-family: Kokila;\">आदि</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जैसे</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">इंटरनेट</span><span style=\"font-family: Cambria Math;\"> Resources </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विशाल</span><span style=\"font-family: Cambria Math;\"> (huge) </span><span style=\"font-family: Kokila;\">डेटाबेस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">संदर्भित</span><span style=\"font-family: Cambria Math;\">(refer) </span><span style=\"font-family: Kokila;\">करता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वर्ल्ड</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वाइड</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वेब</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जानकारी</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">का</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लगाने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">मदद</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: [" <p> Search engine</span></p>", " <p> Web Browser</span></p>", 
                                " <p> Website</span></p>", " <p> Web server</span></p>"],
                    options_hi: ["<p>सर्च्र इंजन(Search engine)</p>", "<p>वेब ब्राउज़र(web browser)</p>",
                                "<p>वेबसाइट (website)</p>", "<p>वेब सर्वर(Web server)</p>"],
                    solution_en: " <p>11.(a)</span><span style=\"font-family:Cambria Math\"> A </span><span style=\"font-family:Cambria Math\">search engine</span><span style=\"font-family:Cambria Math\"> is a software system designed to carry out web searches.</span></p>",
                    solution_hi: "<p>11.(a) <span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">search engine</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> software system </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जिसे</span><span style=\"font-family: Cambria Math;\"> web searches </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">डिज़ाइन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">गया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है।</span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "31",
                    question_en: " <p>12.</span><span style=\"font-family:Cambria Math\"> Which of the following options in MS Word is used to insert line numbers and section breaks while formatting text?</span></p>",
                    question_hi: "<p>12. <span style=\"font-family: Kokila;\">एमएस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वर्ड</span><span style=\"font-family: Cambria Math;\"> (MS Word) </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">निम्नलिखित</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">से</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">कौन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सा</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">विकल्प</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">टेक्स्ट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">फॉर्मेट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">करते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">समय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लाइन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">नंबर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">और</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सेक्शन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">ब्रेक</span><span style=\"font-family: Cambria Math;\"> ( section break ) </span><span style=\"font-family: Kokila;\">डालने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिए</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">प्रयोग</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: [" <p> Page Background</span></p>", " <p> Page Setup</span></p>", 
                                " <p> Paragraph</span></p>", " <p> Themes</span></p>"],
                    options_hi: ["<p>पेज बैकग्राउंड (Page Background)</p>", "<p>पेज सेटअप( Page Setup)</p>",
                                "<p>पैराग्राफ (Paragraph)</p>", "<p>थीम्स(Themes)</p>"],
                    solution_en: " <p>12.(b) To insert a line in MS Word : </span><span style=\"font-family:Cambria Math\">On the Page Layout tab, in the Page Setup group, click Line Numbers. </span></p>",
                    solution_hi: "<p>12.(b)<span style=\"font-family: Cambria Math;\">MS Word </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लाइन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">डालने</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">के</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लिए</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Kokila;\">पेज</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">लेआउट</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">टैब</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">पेज</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सेटअप</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">समूह</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">लाइन</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">नंबर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">पर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्लिक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">किया</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जाता</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हे।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "31",
                    question_en: " <p>13.</span><span style=\"font-family:Cambria Math\"> Which of the following statements with reference to email addresses is INCORRECT?</span></p>",
                    question_hi: " <p>13.</span><span style=\"font-family:Cambria Math\"> Email address </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संदर्भ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कथन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गलत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Spaces are not allowed in an email address.</span></p>", " <p> The username and the domain name in an email address are separated by an @ (at) symbol.</span></p>", 
                                " <p> An email is generally of the form username#domain name</span></p>", " <p> Email addresses are not case sensitive.</span></p>"],
                    options_hi: [" <p> Email address </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रिक्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">स्थान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अनुमति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>", " <p> Email address </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> username  </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> domain name </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> @ (at) </span><span style=\"font-family:Kokila\">प्रतीक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अलग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                                " <p> Email address </span><span style=\"font-family:Kokila\">आम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तौर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> username#domain name  </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">होता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span></p>", " <p> Email address  , case sensitive </span><span style=\"font-family:Kokila\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं।</span></p>"],
                    solution_en: " <p>13.(c) option c is incorrect .</span></p> <p><span style=\"font-family:Cambria Math\">The general format of an email address is</span><span style=\"font-family:Cambria Math\"> local-part@domain. </span></p>",
                    solution_hi: " <p>13.(c) </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> (c) </span><span style=\"font-family:Kokila\">गलत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p> <p><span style=\"font-family:Cambria Math\">Email address </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> general format , local-part@domain.  </span><span style=\"font-family:Kokila\">होगा</span><span style=\"font-family:Cambria Math\"> |</span></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "31",
                    question_en: " <p>14.</span><span style=\"font-family:Cambria Math\"> Which of the following is NOT a way to insert a formula in an MS Excel sheet?</span></p>",
                    question_hi: " <p>14.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एमएस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक्सेल</span><span style=\"font-family:Cambria Math\"> (MS Excel) </span><span style=\"font-family:Kokila\">शीट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फॉर्मूला</span><span style=\"font-family:Cambria Math\"> Insert </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तरीका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Selecting a Formula in the \'Formulas\' tab</span></p>", " <p> Using \'Insert function\' from the \'Home\' tab</span></p>", 
                                " <p> Typing a formula inside the cell</span></p>", " <p> Using the \'Insert Function\' option from the \'Formulas\' tab</span></p>"],
                    options_hi: [" <p> \'Formula tab </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फॉर्मूला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\"> |</span></p>", " <p> \'Home\' tab  </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> \'insert function \' </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\"> |</span></p>",
                                " <p> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अंदर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फॉर्मूला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टाइप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\"> |</span></p>", " <p> \'Formula tab </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> \'Insert function\' </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\"> |</span></p>"],
                    solution_en: " <p>14.(b)</span><span style=\"font-family:Cambria Math\"> Using \'Insert function\' from the \'Home\' tab is not the way to insert formula in excel. </span></p>",
                    solution_hi: " <p>14.(b)</span><span style=\"font-family:Cambria Math\"> \'Home\' tab  </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> \'insert function \' </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एमएस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक्सेल</span><span style=\"font-family:Cambria Math\"> (MS Excel) </span><span style=\"font-family:Kokila\">शीट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फॉर्मूला</span><span style=\"font-family:Cambria Math\"> Insert </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">तरीका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "31",
                    question_en: " <p>15.</span><span style=\"font-family:Cambria Math\"> Which of the following notation is used to represent a cell range that includes cells A1, A2, A3, A4 and A5 in an MS Excel sheet?</span></p>",
                    question_hi: " <p>15.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एमएस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक्सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">शीट</span><span style=\"font-family:Cambria Math\"> (MS Excel sheet) </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> A1, A2, A3, A4 </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> A5 </span><span style=\"font-family:Kokila\">सहित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रेंज</span><span style=\"font-family:Cambria Math\"> ( cell range ) </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">दर्शाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">नोटेशन</span><span style=\"font-family:Cambria Math\"> ( notation )</span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> A1 ; A5</span></p>", " <p> $A1 : $A5</span></p>", 
                                " <p> A1$ : A5$</span></p>", " <p> A1 : A5</span></p>"],
                    options_hi: [" <p> A1 ; A5</span></p>", " <p> $A1 : $A5</span></p>",
                                " <p> A1$ : A5$</span></p>", " <p> A1 : A5</span></p>"],
                    solution_en: " <p>15.(d)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Cell range</span><span style=\"font-family:Cambria Math\"> can be notified using the cell addresses of the first and last cells in the cell range, separated by a colon. For A1, A2, A3, A4 and A5 one can write A1 : A5. </span></p>",
                    solution_hi: " <p>15.(d)Cell range </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> colon  </span><span style=\"font-family:Kokila\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अलग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रेंज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पहली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आखिरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">रेंज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करके</span><span style=\"font-family:Cambria Math\"> separate  </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> A1, A2, A3, A4 </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> A5 </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> A1 : A5 </span><span style=\"font-family:Kokila\">लिखा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "31",
                    question_en: " <p>16.</span><span style=\"font-family:Cambria Math\"> Which of the following options in MS Word 2007 allows us to quickly and easily convert data into a table?</span></p>",
                    question_hi: " <p>16.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एमएस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्ड</span><span style=\"font-family:Cambria Math\"> 2007 (MS Word 2007) </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">डेटा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जल्दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आसानी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टेबल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बदलने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अनुमति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">देता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Convert to Data</span></p>", " <p> Convert to Text</span></p>", 
                                " <p> Convert Text to Table</span></p>", " <p> Convert Data to Table</span></p>"],
                    options_hi: [" <p> Convert to Data</span></p>", " <p> Convert to Text</span></p>",
                                " <p> Convert Text to Table</span></p>", " <p> Convert Data to Table</span></p>"],
                    solution_en: " <p>16.(c) </span><span style=\"font-family:Cambria Math\">Convert Text to Table in MS Word 2007 allows us to quickly and easily convert data into a table. </span></p> <p><span style=\"font-family:Cambria Math\">Method for converting Text to Table in word</span><span style=\"font-family:Cambria Math\"> :- Select the text that you want to convert, and then click Insert > Table > Convert Text to Table.</span></p> <p><span style=\"font-family:Cambria Math\">Method for converting Table to Text:- </span><span style=\"font-family:Cambria Math\">Select the rows or table you want to convert to text  then  On the Layout tab, in the Data section > click Convert to Text.</span></p>",
                    solution_hi: " <p>16.(c) </span></p> <p><span style=\"font-family:Kokila\">एमएस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्ड</span><span style=\"font-family:Cambria Math\"> 2007 (MS Word 2007) </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> Convert Text to Table  </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">डेटा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जल्दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आसानी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टेबल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बदलने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अनुमति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">देता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span></p> <p><span style=\"font-family:Cambria Math\">Text </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> Table </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> convert </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> method </span><span style=\"font-family:Cambria Math\"> :- </span><span style=\"font-family:Kokila\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टेक्स्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चुनें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जिसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कन्वर्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चाहते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">फिर</span><span style=\"font-family:Cambria Math\"> Insert > Table > Convert Text to Table. </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">क्लिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करें।</span></p> <p><span style=\"font-family:Cambria Math\">Table  </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> Text </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> convert </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> method </span><span style=\"font-family:Cambria Math\">: - </span><span style=\"font-family:Kokila\">उन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पंक्तियों</span><span style=\"font-family:Cambria Math\"> ( rows )  </span><span style=\"font-family:Kokila\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टेबल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जिन्हें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">टेक्स्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बदलना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">चाहते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">फिर</span><span style=\"font-family:Cambria Math\"> Layout tab  </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\">,  Data section  </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> > Convert to Text. </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">क्लिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करें।</span></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "31",
                    question_en: " <p>17.</span><span style=\"font-family:Cambria Math\"> Which of the following is a communication service provided by the internet ?</span></p>",
                    question_hi: " <p>17.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">इंटरनेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रदान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वाली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संचार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेवा</span><span style=\"font-family:Cambria Math\"> ( Communication service ) </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> ?</span></p>",
                    options_en: [" <p> Email</span></p>", " <p> Gopher</span></p>", 
                                " <p> FTP</span></p>", " <p> Utility computing</span></p>"],
                    options_hi: [" <p> Email</span></p>", " <p> Gopher</span></p>",
                                " <p> FTP</span></p>", " <p> Utility computing</span></p>"],
                    solution_en: " <p>17.(a) e-mail</span><span style=\"font-family:Cambria Math\"> (electronic mail), is a communication service provided by the internet .</span></p> <p><span style=\"font-family:Cambria Math\">Gopher Protocol</span><span style=\"font-family:Cambria Math\"> is a communication protocol designed for distributing, searching, and retrieving documents in Internet Protocol networks. </span></p> <p><span style=\"font-family:Cambria Math\">Utility computing </span><span style=\"font-family:Cambria Math\">is a model in which computing resources are provided to the customer based on specific demand.</span></p>",
                    solution_hi: " <p>17.(a)</span></p> <p><span style=\"font-family:Cambria Math\">e-mail</span><span style=\"font-family:Cambria Math\"> (electronic mail) , </span><span style=\"font-family:Kokila\">इंटरनेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रदान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वाली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संचार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सेवा</span><span style=\"font-family:Cambria Math\"> ( Communication service ) </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">Gopher Protocol </span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> communication protocol </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जिसे</span><span style=\"font-family:Cambria Math\"> Internet Protocol networks. </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> documents  </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> distribute </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Kokila\">खोजने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पुनर्प्राप्त</span><span style=\"font-family:Cambria Math\"> ( retrieve) </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">डिज़ाइन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span></p> <p><span style=\"font-family:Cambria Math\">Utility computing </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ऐसा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मॉडल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जिसमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">विशिष्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">मांग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">आधार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">ग्राहक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कंप्यूटिंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">संसाधन</span><span style=\"font-family:Cambria Math\"> ( computing resources) </span><span style=\"font-family:Kokila\">प्रदान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं।</span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "31",
                    question_en: " <p>18. </span><span style=\"font-family:Cambria Math\">Which of the following displays the first, previous, next and last worksheets in a Workbook and also allows us to move to another worksheet in MS Excel?</span></p>",
                    question_hi: " <p>18. </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> Worksheets </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> first, previous, next </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> last worksheet </span><span style=\"font-family:Kokila\">प्रदर्शित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हमें</span><span style=\"font-family:Cambria Math\"> MS Excel </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अन्य</span><span style=\"font-family:Cambria Math\"> worksheet  </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अनुमति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">भी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">देता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Row headings</span></p>", " <p> Navigation buttons</span></p>", 
                                " <p> Status bar</span></p>", " <p> Column headings</span></p>"],
                    options_hi: [" <p> Row headings</span></p>", " <p> Navigation buttons</span></p>",
                                " <p> Status bar</span></p>", " <p> Column headings</span></p>"],
                    solution_en: " <p>18.(b) Navigation buttons </span><span style=\"font-family:Cambria Math\">allow moving to another worksheet in an Excel workbook. They are used to display the first, previous, next, and last worksheets in the workbook. </span></p>",
                    solution_hi: " <p>18.(b)Navigation buttons </span><span style=\"font-family:Kokila\">किसी</span><span style=\"font-family:Cambria Math\"> Excel worksheet </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अन्य</span><span style=\"font-family:Cambria Math\"> worksheet  </span><span style=\"font-family:Kokila\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">अनुमति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">देते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उनका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> worksheet  </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> first, previous, next </span><span style=\"font-family:Kokila\">और</span><span style=\"font-family:Cambria Math\"> last worksheets  </span><span style=\"font-family:Kokila\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">प्रदर्शित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है।</span><span style=\"font-family:Cambria Math\"> </span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "31",
                    question_en: " <p>19.</span><span style=\"font-family:Cambria Math\"> Which of the following options from the \'Insert\' menu of an MS Word document can be used to insert a built-in calendar?</span></p>",
                    question_hi: " <p>19. </span><span style=\"font-family:Kokila\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">एमएस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्ड</span><span style=\"font-family:Cambria Math\"> (MS Word) document </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> \'</span><span style=\"font-family:Kokila\">इन्सर्ट</span><span style=\"font-family:Cambria Math\">\' </span><span style=\"font-family:Kokila\">मेनू</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बिल्ट</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Kokila\">इन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कैलेंडर</span><span style=\"font-family:Cambria Math\"> ( built-in-calendar) </span><span style=\"font-family:Kokila\">डालने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Quick Tables </span></p>", " <p> Quick Calendar</span></p>", 
                                " <p> Insert Table</span></p>", " <p> Insert Calendar</span></p>"],
                    options_hi: [" <p> Quick Tables</span></p>", " <p> Quick Calendar</span></p>",
                                " <p> Insert Table</span></p>", " <p> Insert Calendar</span></p>"],
                    solution_en: " <p>19.(a) </span><span style=\"font-family:Cambria Math\">Quick Tables option from the \'Insert\' menu of an MS Word document can be used to insert a built-in calendar.</span></p>",
                    solution_hi: " <p>19.(a)</span><span style=\"font-family:Kokila\">एमएस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">वर्ड</span><span style=\"font-family:Cambria Math\"> (MS Word) document </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> \'</span><span style=\"font-family:Kokila\">इन्सर्ट</span><span style=\"font-family:Cambria Math\">\' </span><span style=\"font-family:Kokila\">मेनू</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">में</span><span style=\"font-family:Cambria Math\"> Quick Tables </span><span style=\"font-family:Kokila\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">बिल्ट</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Kokila\">इन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">कैलेंडर</span><span style=\"font-family:Cambria Math\"> ( built-in-calendar) </span><span style=\"font-family:Kokila\">डालने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">जा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">सकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Kokila\">है</span><span style=\"font-family:Cambria Math\">|</span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: " <p>20.</span><span style=\"font-family:Cambria Math\"> What name do we give to the current cell in MS Excel that has a cell boundary around it?</span></p>",
                    question_hi: "<p>20.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एमएस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक्सेल</span><span style=\"font-family: Cambria Math;\"> (MS Excel) </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वर्तमान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सेल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">क्या</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">नाम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">देते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हैं</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">जिसके</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">चारों</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">ओर</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सेल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सीमा</span><span style=\"font-family: Cambria Math;\">(cell boundary) </span><span style=\"font-family: Kokila;\">होती</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">है</span><span style=\"font-family: Cambria Math;\">?</span></p>",
                    options_en: [" <p> Active Cell</span></p>", " <p> New Cell</span></p>", 
                                " <p> Passive Cell</span></p>", " <p> Current Cell</span></p>"],
                    options_hi: ["<p>सक्रिय सेल(Active Cell)</p>", "<p>नई सेल (New Cell)</p>",
                                "<p>निष्क्रिय सेल (Passive cell)</p>", "<p>वर्तमान सेल (Current Cell)</p>"],
                    solution_en: " <p>20.(a) </span></p> <p><span style=\"font-family:Cambria Math\">The </span><span style=\"font-family:Cambria Math\">active cell </span><span style=\"font-family:Cambria Math\">is the name of current cell in ms Excel worksheet.</span></p>",
                    solution_hi: "<p>20.(a)</p>\r\n<p><span style=\"font-family: Kokila;\">एमएस</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">एक्सेल</span><span style=\"font-family: Cambria Math;\"> (MS Excel) </span><span style=\"font-family: Kokila;\">में</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">वर्तमान</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सेल</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">को</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सक्रिय</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">सेल</span><span style=\"font-family: Cambria Math;\">(Active Cell) </span><span style=\"font-family: Kokila;\">नाम</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">देते</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">हैं</span><span style=\"font-family: Cambria Math;\"> |</span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>