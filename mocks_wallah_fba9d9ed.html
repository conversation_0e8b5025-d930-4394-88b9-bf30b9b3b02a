<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. In which of the following cities did Mahatma Gandhi lead the peasant movement against the imposed indigo cultivation by the British planters?</p>",
                    question_hi: "<p>1. निम्नलिखित में से किस शहर में महात्मा गांधी ने ब्रिटिश बागान मालिकों द्वारा थोपी गई नील की खेती के खिलाफ किसान आंदोलन का नेतृत्व किया था?</p>",
                    options_en: ["<p>Kheda</p>", "<p>Gorakhpur</p>", 
                                "<p>Bardoli</p>", "<p>Champaran</p>"],
                    options_hi: ["<p>खेड़ा</p>", "<p>गोरखपुर</p>",
                                "<p>बारदोली</p>", "<p>चंपारण</p>"],
                    solution_en: "<p>1.(d) <strong>The Champaran Satyagraha</strong> of 1917 was the first Satyagraha movement led by Gandhi in India. In 1918, he organised a satyagraha to support the peasants of the Kheda district of Gujarat. In 1918, Mahatma Gandhi went to Ahmedabad to organise a satyagraha movement amongst cotton mill workers. The Bardoli Satyagraha of 1928, in the state of Gujarat was led by Vallabhbhai Patel for the farmers of Bardoli against the unjust raising of taxes.</p>",
                    solution_hi: "<p>1.(d) <strong>चंपारण सत्याग्रह </strong>1917 में भारत में गांधी जी द्वारा नेतृत्व किया गया पहला सत्याग्रह आंदोलन था। 1918 में, उन्होंने गुजरात के खेड़ा जिले के किसानों का समर्थन करने के लिए सत्याग्रह का आयोजन किया। 1918 में, महात्मा गांधी कपास मिल श्रमिकों के बीच सत्याग्रह आंदोलन का आयोजन करने के लिए अहमदाबाद गए। 1928 में गुजरात राज्य में बारदोली सत्याग्रह का नेतृत्व वल्लभभाई पटेल ने करों की अनुचित वृद्धि के खिलाफ बारदोली के किसानों के लिए किया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. According to the Ashrama system of Vedic life, which of the following was the third stage of life?</p>",
                    question_hi: "<p>2. वैदिक जीवन की आश्रम प्रणाली के अनुसार, निम्नलिखित में से जीवन का तीसरा चरण कौन-सा था?</p>",
                    options_en: ["<p>Grihastha</p>", "<p>Sanyasa</p>", 
                                "<p>Brahmacharya</p>", "<p>Vanaprastha</p>"],
                    options_hi: ["<p>गृहस्थ</p>", "<p>सन्यास</p>",
                                "<p>ब्रह्मचर्य</p>", "<p>वानप्रस्थ</p>"],
                    solution_en: "<p>2.(d) <strong>Vanaprastha</strong>, where a person handed over household responsibilities to the next generation, took an advisory role, and gradually withdrew from the world. The four stages of the ashrama system are : Brahmacharya (Student\'s life), Grihastha (household life), Vanaprastha (retired life), Sannyasa (renounced life).</p>",
                    solution_hi: "<p>2.(d) <strong>वानप्रस्थ</strong>, जहाँ व्यक्ति घर की ज़िम्मेदारियाँ अगली पीढ़ी को सौंपता है, सलाहकार की भूमिका निभाता है, और धीरे-धीरे दुनिया से अलग हो जाता है। आश्रम प्रणाली के चार चरण हैं: ब्रह्मचर्य (छात्र जीवन), गृहस्थ (घरेलू जीवन), वानप्रस्थ (सेवानिवृत्त जीवन), संन्यास (त्याग का जीवन)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following events took place in the United Provinces in February, 1922?</p>",
                    question_hi: "<p>3. फरवरी 1922 में, संयुक्त प्रांत में निम्नलिखित में से कौन-सी घटना घटी?</p>",
                    options_en: ["<p>Quit India</p>", "<p>Chauri Chaura</p>", 
                                "<p>Khilafat Movement</p>", "<p>Kakori Incident</p>"],
                    options_hi: ["<p>भारत छोड़ो</p>", "<p>चौरी चौरा</p>",
                                "<p>खिलाफत आंदोलन</p>", "<p>काकोरी कांड</p>"],
                    solution_en: "<p>3.(b) <strong>Chauri Chaura</strong> Incident took place at Chauri Chaura in the Gorakhpur district of United Provinces (now Uttar Pradesh) in British India. The police there fired upon a large group of protesters participating in the non-cooperation movement. In retaliation, the demonstrators attacked and set fire to a police station, killing all of its occupants. The incident led to the deaths of three civilians and 22 policemen. Mahatma Gandhi halted the non-cooperation movement on 12th February 1922 after the Chauri Chaura incident.</p>",
                    solution_hi: "<p>3.(b) <strong>चौरी चौरा</strong> की घटना ब्रिटिश भारत में संयुक्त प्रांत (अब उत्तर प्रदेश) के गोरखपुर जिले के चौरी चौरा में हुई थी। वहां पुलिस ने असहयोग आंदोलन में भाग लेने वाले प्रदर्शनकारियों के एक बड़े समूह पर गोलीबारी की थी। जवाबी कार्रवायी में, प्रदर्शनकारियों ने एक पुलिस स्टेशन पर हमला किया और उसमें आग लगा दी, जिससे उसमें मौजूद सभी लोग मारे गए। इस घटना में तीन नागरिकों और 22 पुलिसकर्मियों की मौत हो गई थी। चौरी- चौरा की घटना के बाद महात्मा गांधी ने 12 फरवरी 1922 को असहयोग आंदोलन रोक दिया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. According to which of the following foreign travellers, people of Delhi used to write letters containing abuses to the Sultan, therefore, in order to punish them Sultan decided to shift the capital?</p>",
                    question_hi: "<p>4. निम्नलिखित में से किस विदेशी यात्री के अनुसार दिल्ली के लोग सुल्तान को अपशब्दों भरे पत्र लिखते थे, जिसके कारण सुल्तान ने उन्हें दंडित करने के लिए राजधानी को स्थानांतरित करने का निर्णय लिया?</p>",
                    options_en: ["<p>Isami</p>", "<p>Hasan Nizami</p>", 
                                "<p>Ibn Batuta</p>", "<p>Al Biruni</p>"],
                    options_hi: ["<p>इसामी</p>", "<p>हसन निज़ामी</p>",
                                "<p>इब्न बतूता</p>", "<p>अल बिरूनी</p>"],
                    solution_en: "<p>4.(c)<strong> Ibn Battuta</strong>, a traveler from Morocco, authored a book of travels called Rihla, which is written in Arabic and provides rich and fascinating details about the social and cultural life in the subcontinent during the 14th century. Muhammad Bin Tughlaq was the ruler of the Delhi sultanate when Ibn Batuta came to India. The Sultan was impressed by his scholarship, and appointed him the qazi or judge of Delhi.</p>",
                    solution_hi: "<p>4.(c) <strong>इब्न बतूत</strong>, मोरक्को यात्री ने रेहला नामक एक यात्रा वृत्तांत अरबी भाषा में लिखा है और यह 14वीं शताब्दी के दौरान उपमहाद्वीप में सामाजिक और सांस्कृतिक जीवन के बारे में समृद्ध और आकर्षक विवरण प्रदान करता है। जब इब्न बतूता भारत आया था, उस समय मुहम्मद बिन तुगलक दिल्ली सल्तनत का शासक था। सुल्तान उनकी विद्वता से प्रभावित हुए और उन्हें दिल्ली का काजी या न्यायाधीश नियुक्त किया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. With reference to Morley-Minto Reforms of 1909, consider the following statements. <br>a) They are also called the Indian Councils Act, 1909. <br>b) They increased the strength of Legislative Councils. <br>c) The right to separate electorate was given to the Muslims. <br>Which of the above statements is/are correct ?</p>",
                    question_hi: "<p>5. 1909 के मॉर्ले-मिंटो सुधारों के संदर्भ में निम्नलिखित कथनों पर विचार करें। <br>a) इन्हें भारतीय परिषद अधिनियम, 1909 भी कहा जाता है। <br>b) उन्होंने विधान परिषदों की ताकत में वृद्धि की। <br>c) मुसलमानों को पृथक निर्वाचन क्षेत्र का अधिकार दिया गया। <br>उपर्युक्त कथनों में से कौन-सा/कौन-से कथन सही है/हैं?</p>",
                    options_en: ["<p>b, c</p>", "<p>a, b, c</p>", 
                                "<p>c, a</p>", "<p>a, b</p>"],
                    options_hi: ["<p>b, c</p>", "<p>a, b, c</p>",
                                "<p>c, a</p>", "<p>a, b</p>"],
                    solution_en: "<p>5.(b) <strong>a, b, c. </strong>The Indian Councils Act of 1909 was introduced by the British government in India as a step toward including Indians in governance. Named after Viceroy Lord Minto and Secretary of State John Morley, the Act considerably increased the size of the legislative councils, both at the Central and provincial levels. The number of members in the Central Legislative Council was raised from 16 to 60.</p>",
                    solution_hi: "<p>5.(b) <strong>a, b, c.</strong> 1909 का भारतीय परिषद अधिनियम भारत में ब्रिटिश सरकार द्वारा भारतीयों को शासन में शामिल करने की दिशा में एक कदम के रूप में पेश किया गया था। वायसराय लॉर्ड मिंटो और राज्य सचिव जॉन मोर्ले के नाम पर, इस अधिनियम ने केंद्रीय और प्रांतीय दोनों स्तरों पर विधान परिषदों के आकार में काफी वृद्धि की। केंद्रीय विधान परिषद में सदस्यों की संख्या 16 से बढ़ाकर 60 कर दी गई।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Vasudeva I who issued coins in gold (dinars and quarter dinars) and copper (single denomination) was a _________king.</p>",
                    question_hi: "<p>6. सोने (दीनार और चौथाई दीनार) और तांबे (एकल मूल्यवर्ग) में सिक्के जारी करने वाला, वासुदेव प्रथम, एक _________शासक था।</p>",
                    options_en: ["<p>Shunga</p>", "<p>Vakataka</p>", 
                                "<p>Shaka</p>", "<p>Kushana</p>"],
                    options_hi: ["<p>शुंग</p>", "<p>वाकाटक</p>",
                                "<p>शक</p>", "<p>कुषाण</p>"],
                    solution_en: "<p>6.(d) <strong>Kushana.</strong> Vasudeva I was the last of the \"Great Kushans.\" He ruled in Northern India and Central Asia, where he minted coins in the city of Balkh. The Kushana dynasty was founded by Kujula Kadphises.</p>",
                    solution_hi: "<p>6.(d) <strong>कुषाण।</strong> वासुदेव प्रथम \"महान कुषाणों\" में से अंतिम शासक थे। उन्होंने उत्तरी भारत और मध्य एशिया में शासन किया था, जहाँ उन्होंने बल्ख (बैक्ट्रिया) शहर में सिक्के ढाले। कुषाण राजवंश की स्थापना कुजुल कडफिसेस ने की थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Match the following institutes with their respective founders of British India.<br><strong>&nbsp; &nbsp; &nbsp; &nbsp; Institutes </strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>Their respective founders</strong><br>a. Asiatic Society of Bengal&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; i. Warren Hastings<br>b. Sanskrit College of Benaras&nbsp; &nbsp; &nbsp; &nbsp;ii. Lord Wellesley<br>c. Fort William College&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;iii. Jonathan Duncan<br>d. Calcutta Madrasa&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;iv. Sir William Jones</p>",
                    question_hi: "<p>7. ब्रिटिश भारत के, निम्नलिखित संस्थानों का उनसे संबंधित संस्थापकों के साथ मिलान कीजिए।<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>संस्थान&nbsp; &nbsp;</strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>उनसे संबंधित संस्थापक</strong><br>a. बंगाल की एशियाटिक सोसायटी&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; i. वारेन हेस्टिंग्स (Warren Hastings)<br>b. बनारस संस्कृत कॉलेज&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ii. लॉर्ड वेलेस्ली (Lord Wellesley)<br>c. फोर्ट विलियम कॉलेज&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; iii. जोनाथन डंकन (Jonathan Duncan)<br>d. कलकत्ता मदरसा&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;iv. सर विलियम जोन्स (Sir William Jones)</p>",
                    options_en: ["<p>a-ii, b-i, c-iv, d-iii</p>", "<p>a-iii, b-iv, c-i, d-ii</p>", 
                                "<p>a-iv, b-iii, c-ii, d-i</p>", "<p>a-i, b-ii, c-iii, d-iv</p>"],
                    options_hi: ["<p>a-ii, b-i, c-iv, d-iii</p>", "<p>a-iii, b-iv, c-i, d-ii</p>",
                                "<p>a-iv, b-iii, c-ii, d-i</p>", "<p>a-i, b-ii, c-iii, d-iv</p>"],
                    solution_en: "<p>7.(c) <strong>A-iv, B-iii, C-ii, D-i.</strong></p>",
                    solution_hi: "<p>7.(c) <strong>A-iv, B-iii, C-ii, D-i.</strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The magnificent Kailasa temple at Ellora was built during the reign of which Rashtrakuta king?</p>",
                    question_hi: "<p>8. एलोरा का भव्य कैलास मंदिर किस राष्ट्रकूट राजा के शासनकाल के दौरान बनाया गया था?</p>",
                    options_en: ["<p>Krishna I</p>", "<p>Indra III</p>", 
                                "<p>Govinda III</p>", "<p>Amoghavarsha</p>"],
                    options_hi: ["<p>कृष्ण प्रथम (Krishna I)</p>", "<p>इंद्र तृतीय (Indra III)</p>",
                                "<p>गोविंद तृतीय (Govinda III)</p>", "<p>अमोघवर्ष (Amoghavarsha)</p>"],
                    solution_en: "<p>8.(a) <strong>Krishna I. </strong>The Kailashnatha Temple in Ellora, Maharashtra, dedicated to Lord Shiva, is renowned for its Dravidian architecture and intricate sculptures, showcasing the engineering and artistic brilliance of ancient India. Famous Heritage Sites in India: Elephanta Caves (Rashtrakuta Kings), Khajurao Temples (Chandela Dynasty), Rani ki Vav (Queen Udayamati).</p>",
                    solution_hi: "<p>8.(a)<strong> कृष्ण प्रथम।</strong> महाराष्ट्र के एलोरा में भगवान शिव को समर्पित कैलाशनाथ मंदिर अपनी द्रविड़ वास्तुकला और जटिल मूर्तियों के लिए प्रसिद्ध है, जो प्राचीन भारत की इंजीनियरिंग और कलात्मक प्रतिभा को दर्शाता है। भारत में प्रसिद्ध विरासत स्थल: एलीफेंटा गुफाएँ (राष्ट्रकूट राजा), खजुराहो मंदिर (चंदेल राजवंश), रानी की वाव (रानी उदयमती)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. With reference to Sepoy Mutiny of 1857, on which of the following dates did the soldiers at Meerut start their journey to Delhi?</p>",
                    question_hi: "<p>9. 1857 के सिपाही विद्रोह के संदर्भ में, निम्नलिखित में से किस तारीख को मेरठ के सैनिकों ने दिल्ली के लिए यात्रा शुरू की थी?</p>",
                    options_en: ["<p>10 May</p>", "<p>19 April</p>", 
                                "<p>2 June</p>", "<p>29 March</p>"],
                    options_hi: ["<p>10 मई</p>", "<p>19 अप्रैल</p>",
                                "<p>2 जून</p>", "<p>29 मार्च</p>"],
                    solution_en: "<p>9.(a) <strong>10 May.</strong> The Revolt of 1857, the first major rebellion against the British East India Company, began in Meerut. The immediate cause of this revolt was the introduction of the Enfield rifle and its greased cartridges. In March 1857, Mangal Pandey, a sepoy in Barrackpore, refused to use the new cartridges because of the rumor that they were greased with pork and beef fat, which violated their religious beliefs, and he attacked his officers, which led to his execution on April 8. On 9th May, 85 soldiers in Meerut refused the new rifles and were imprisoned. Lord Canning was the British viceroy during the revolt.</p>",
                    solution_hi: "<p>9.(a) <strong>10 मई।</strong> 1857 का विद्रोह, ब्रिटिश ईस्ट इंडिया कंपनी के विरुद्ध प्रथम बड़ा विद्रोह, मेरठ में शुरू हुआ था। इस विद्रोह का तात्कालिक कारण एनफील्ड राइफल और उसके चर्बी युक्त कारतूसों का प्रचलन था। मार्च 1857 में, बैरकपुर के एक सिपाही मंगल पांडे ने नए कारतूसों का इस्तेमाल करने से इनकार कर दिया था क्योंकि यह अफवाह थी कि उनमें सूअर और गाय की चर्बी लगी हुई थी, जो उनकी धार्मिक मान्यताओं का उल्लंघन था, और उसने अपने अधिकारियों पर हमला कर दिया, जिसके परिणामस्वरूप 8 अप्रैल को उन्हें फांसी दे दी गई थी। 9 मई को, मेरठ में 85 सैनिकों ने नई राइफलों को लेने से इनकार कर दिया और उन्हें जेल में डाल दिया गया। विद्रोह के दौरान लॉर्ड कैनिंग ब्रिटिश वायसराय थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following plays was NOT written by Harshavardhana ?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन-सा नाटक हर्षवर्द्धन द्वारा नहीं लिखा गया था?</p>",
                    options_en: ["<p>Vikramorvasiyam</p>", "<p>Ratnavali</p>", 
                                "<p>Nagananda</p>", "<p>Priyadarshika</p>"],
                    options_hi: ["<p>विक्रमोर्वशीयम्</p>", "<p>रत्नावली</p>",
                                "<p>नागानंद</p>", "<p>प्रियदर्शिका</p>"],
                    solution_en: "<p>10.(a) <strong>Vikramorvasiyam</strong> is a romantic play written by Kalidasa, based on the legend of King Pururava and an Apsara named Urvashi. Kalidasa\'s works include: Abhijnanasakuntalam, Malavikagnimitram, Kumarasambhavam. Harshavardhana belonged to the Pushyabhuti dynasty, also known as the Vardhana Dynasty, founded by Naravardhana. Harsha\'s reign is documented in Harshacharitra, written by his court poet Banabhatta.</p>",
                    solution_hi: "<p>10.(a) <strong>विक्रमोर्वशीयम् </strong>कालिदास द्वारा लिखित एक प्रेमपूर्ण नाटक है, जो राजा पुरुरवा और उर्वशी नामक एक अप्सरा की कथा पर आधारित है। कालिदास की कृतियों में शामिल हैं: अभिज्ञानशाकुंतलम, मालविकाग्निमित्रम, कुमारसंभवम। हर्षवर्धन पुष्यभूति वंश से संबंधित थे, जिसे वर्धन वंश के नाम से भी जाना जाता है, जिसकी स्थापना नरवर्धन ने की थी। हर्ष के शासनकाल का विवरण उनके दरबारी कवि बाणभट्ट द्वारा लिखित हर्षचरित्र में मिलता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. The Vedangas are Hindu auxiliary disciplines that originated in ancient times and are linked to the study of the Vedas. These are _________ in number.</p>",
                    question_hi: "<p>11. वेदांग हिंदू धर्म के सहायक विषय हैं जिनकी उत्पत्ति प्राचीन काल में हुई थी और वे वेदों के अध्ययन से जुड़े हुए हैं। इनकी संख्या _________ है।</p>",
                    options_en: ["<p>five</p>", "<p>seven</p>", 
                                "<p>four</p>", "<p>six</p>"],
                    options_hi: ["<p>पाँच</p>", "<p>सात</p>",
                                "<p>चार</p>", "<p>छह</p>"],
                    solution_en: "<p>11.(d) <strong>Six.</strong> Vedanga literally means \"limbs of the Vedas,\" and refers to the six disciplines connected with studying the Vedas, the ancient Indian spiritual writings. The six Vedangas are as follows: Shiksha (phonetics), Kalpa (ritual), Vyakarana (grammar), Nirukta (etymology), Chandas (meter), and Jyotisha (astronomy).</p>",
                    solution_hi: "<p>11.(d) <strong>छह।</strong> वेदांग का शाब्दिक अर्थ है \"वेदों के अंग\", और यह वेदों, प्राचीन भारतीय आध्यात्मिक लेखन के अध्ययन से जुड़े छह विषयों को संदर्भित करता है। छह वेदांग इस प्रकार हैं: शिक्षा (phonetics), कल्प (ritual), व्याकरण (grammar), निरुक्त (etymology), छंद (meter), और ज्योतिष (astronomy)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Chand Bardai&rsquo;s famous literary work ________, mentions Prithviraj Chauhan&lsquo;s love story and heroic deeds.</p>",
                    question_hi: "<p>12. चंद बरदाई की प्रसिद्ध साहित्यिक कृति ________ में पृथ्वीराज चौहान की प्रेम कहानी और वीरतापूर्ण कार्यों का उल्लेख है।</p>",
                    options_en: ["<p>Harakeli Nataka</p>", "<p>Abhidhana Chintamani</p>", 
                                "<p>Prithviraj Raso</p>", "<p>Rajamartanda</p>"],
                    options_hi: ["<p>हरकेली नाटक</p>", "<p>अभिधान चिंतामणि</p>",
                                "<p>पृथ्वीराज रासो</p>", "<p>राजमार्तण्ड</p>"],
                    solution_en: "<p>12.(c) <strong>Prithviraj Raso.</strong> It is a Braj language epic poem about the life of Prithviraj Chauhan. Other Ancient Books and Authors : &lsquo;Ain-i-Akbari&rsquo; - Abul Fazl, &lsquo;Mahabharata&rsquo; - Vyasa, &lsquo;Ramayana&rsquo; - Valmiki, &lsquo;Shakuntala&rsquo; - Kalidasa, and &lsquo;Mudrarakshas&rsquo; - Vishakhadatta.</p>",
                    solution_hi: "<p>12.(c)<strong> पृथ्वीराज रासो।</strong> यह पृथ्वीराज चौहान के जीवन के बारे में ब्रज भाषा का महाकाव्य है। अन्य प्राचीन पुस्तकें और लेखक: \'आइन-ए-अकबरी\' - अबुल फजल, \'महाभारत\' - व्यास, \'रामायण\' - वाल्मिकी, \'शकुंतला\' - कालिदास, और \'मुद्राराक्षस\' - विशाखदत्त।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Who among the following freedom fighters is known as &lsquo;Deshbandhu&rsquo;?</p>",
                    question_hi: "<p>13. निम्नलिखित स्वतंत्रता सेनानियों में से किसे देशबंधु के नाम से जाना जाता है?</p>",
                    options_en: ["<p>Gopal Krishna Gokhale</p>", "<p>Bhagat Singh</p>", 
                                "<p>Subhas Chandra Bose</p>", "<p>Chittaranjan Das</p>"],
                    options_hi: ["<p>गोपाल कृष्ण गोखले</p>", "<p>भगत सिंह</p>",
                                "<p>सुभाष चंद्र बोस</p>", "<p>चित्तरंजन दास</p>"],
                    solution_en: "<p>13.(d) <strong>Chittaranjan Das.</strong> He was a politician and leader of the Swaraj (Independence) Party in Bengal under British rule. Other Personalities and Their Titles : Mahatma Gandhi (Father of the Nation), Subhas Chandra Bose (Netaji), Bhagat Singh (Shaheed-e-Azam), Jawaharlal Nehru (Chacha Nehru), and Sardar Vallabhbhai Patel (Iron Man of India).</p>",
                    solution_hi: "<p>13.(d) <strong>चित्तरंजन दास,</strong> ब्रिटिश शासन के तहत बंगाल में स्वराज (स्वतंत्रता) पार्टी के एक राजनीतिज्ञ और नेता थे। अन्य व्यक्तित्व और उनकी उपाधियाँ: महात्मा गांधी (राष्ट्रपिता), सुभाष चंद्र बोस (नेताजी), भगत सिंह (शहीद-ए-आज़म), जवाहरलाल नेहरू (चाचा नेहरू), और सरदार वल्लभभाई पटेल (भारत के लौह पुरुष)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. Who established Shantiniketan and when?",
                    question_hi: "14. शांतिनिकेतन की स्थापना किसने और कब की?",
                    options_en: ["  Mahatma Gandhi, 1910", " Ravindranath Tagore, 1901", 
                                " Ravindranath Tagore, 1905", " Devendranath Tagore, 1915"],
                    options_hi: [" महात्मा गांधी, 1910  में ", " रवींद्रनाथ टैगोर,1901 में ",
                                " रवींद्रनाथ टैगोर, 1905 में ", " देवेंद्रनाथ टैगोर, 1915 में "],
                    solution_en: "14.(b) Ravindranath Tagore, 1901. Shantiniketan Initially founded as an ashram by Debendranath Tagore in 1863 and later expanded by Rabindranath Tagore into a center for learning and culture, it evolved into Visva-Bharati University in 1921. In 2023, Shantiniketan was recognized as India\'s 41st UNESCO World Heritage Site. Other Institutes and Founders : Rishi Valley (1926, Andhra Pradesh) -  J. Krishnamurti; Auroville (1968, Tamil Nadu) -  Sri Aurobindo; Sabarmati Ashram (1915, Gujarat) - Mahatma Gandhi.",
                    solution_hi: "14.(b) रवींद्रनाथ टैगोर,1901 में । शांतिनिकेतन की स्थापना 1863 में देबेन्द्रनाथ टैगोर ने एक आश्रम के रूप में की थी और बाद में रवींद्रनाथ टैगोर ने इसे शिक्षा और संस्कृति के केंद्र के रूप में विस्तारित किया, यह 1921 में विश्वभारती विश्वविद्यालय के रूप में विकसित हुआ। 2023 में, शांतिनिकेतन को भारत के 41वें यूनेस्को विश्व धरोहर स्थल के रूप में मान्यता दी गई। अन्य संस्थान और संस्थापक: ऋषि घाटी (1926, आंध्र प्रदेश) - जे. कृष्णमूर्ति; ऑरोविले (1968, तमिलनाडु) - श्री अरबिंदो; साबरमती आश्रम (1915, गुजरात) - महात्मा गांधी।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Who among the following is primarily connected to the World\'s Parliament of Religions convened in Chicago, 1893?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन मुख्य रूप से 1893 में शिकागो में आयोजित की गई विश्व धर्म संसद से संबंधित है?</p>",
                    options_en: ["<p>Dayanand Saraswati</p>", "<p>Swami Vivekananda</p>", 
                                "<p>Raja Ram Mohan Roy</p>", "<p>Ramakrishna Paramhansa</p>"],
                    options_hi: ["<p>दयानंद सरस्वती</p>", "<p>स्वामी विवेकानंद</p>",
                                "<p>राजा राम मोहन राय</p>", "<p>रामकृष्ण परमहंस</p>"],
                    solution_en: "<p>15.(b) <strong>Swami Vivekananda.</strong> In Chicago (United States) he introduced Hinduism and advocated for religious tolerance. He was the chief disciple of the 19th-century mystic Ramakrishna Paramhansa and established the Ramakrishna Mission in 1897. He established the Belur Math, which became his permanent abode. Dayanand Saraswati founded the Arya Samaj in 1875. Raja Ram Mohan Roy founded the Brahmo Samaj in 1828.</p>",
                    solution_hi: "<p>15.(b) <strong>स्वामी विवेकानंद। </strong>शिकागो (संयुक्त राज्य अमेरिका) में उन्होंने हिंदू धर्म की परिचय दिया और धार्मिक सहिष्णुता का समर्थन किया। वे 19वीं सदी के रहस्यवादी रामकृष्ण परमहंस के मुख्य शिष्य थे और उन्होंने 1897 में रामकृष्ण मिशन की स्थापना की। उन्होंने बेलूर मठ की स्थापना की, जो उनका स्थायी धाम बन गया। दयानंद सरस्वती ने 1875 में आर्य समाज की स्थापना की। राजा राम मोहन राय ने 1828 में ब्रह्म समाज की स्थापना की।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>