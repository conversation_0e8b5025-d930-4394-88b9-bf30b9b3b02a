<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "1.  Sentences of a paragraph are given below in jumbled order. Select the option that arranges the sentences in the correct logical sequence to form a meaningful and coherent paragraph.<br />a. It regulates the operation of all body organs and protects the body and mind from becoming unbalanced as a result of negative circumstances and unhealthy lifestyles.<br />b. It only requires frequent practice of the proper body motions and breathing techniques.<br />c. The physical benefits of yoga can be enjoyed for a lifetime without risk or injury.<br />d. It regulates the relationship between the body, the intellect, and the spirit.",
                    question_hi: "1.  Sentences of a paragraph are given below in jumbled order. Select the option that arranges the sentences in the correct logical sequence to form a meaningful and coherent paragraph.<br />a. It regulates the operation of all body organs and protects the body and mind from becoming unbalanced as a result of negative circumstances and unhealthy lifestyles.<br />b. It only requires frequent practice of the proper body motions and breathing techniques.<br />c. The physical benefits of yoga can be enjoyed for a lifetime without risk or injury.<br />d. It regulates the relationship between the body, the intellect, and the spirit.",
                    options_en: [" d, a, b, c  ", "  c, b, d, a  ", 
                                "  b, a, c, d ", "  a, b, c, d"],
                    options_hi: [" d, a, b, c  ", "  c, b, d, a  ",
                                "  b, a, c, d ", "  a, b, c, d"],
                    solution_en: "1.(b) c, b, d, a<br />Sentence ‘c’ will be the starting line as it introduces the main idea of the parajumble i.e. the physical benefits of yoga. And, Sentence ‘b’ states that yoga only requires frequent practice of the proper body motions and breathing techniques. So, ‘b’ will follow ‘c’. Further, Sentence ‘d’ states that it regulates the relationship between the body, the intellect, and the spirit & Sentence ‘a’ states that it regulates the operation of all body organs. So, ‘a’ will follow ‘d’. Going through the options, option (b) has the correct sequence.",
                    solution_hi: "1.(b) c, b, d, a<br />Sentence  ‘c’ प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार ‘the physical benefits of yoga’ का परिचय देता है। और, Sentence ‘b’ बताता है कि yoga के लिए केवल उचित body motions और breathing techniques का लगातार अभ्यास करने की आवश्यकता होती है। इसलिए, ‘c’ के बाद ‘b’ आएगा। इसके अलावा, Sentence ‘d’ बताता है कि यह शरीर, बुद्धि और आत्मा के बीच relationship को नियंत्रित करता है तथा Sentence ‘a’ बताता है कि यह शरीर के सभी organs के संचालन को नियंत्रित करता है। इसलिए, \'d\' के बाद \'a\' आएगा। अतः options के माध्यम से जाने पर, option ‘b’ में सही sequence है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate meaning of the underlined word.<br>At times, David behaves in a very <span style=\"text-decoration: underline;\">obdurate </span>manner to kids.</p>",
                    question_hi: "<p>2. Select the most appropriate meaning of the underlined word.<br>At times, David behaves in a very <span style=\"text-decoration: underline;\">obdurate </span>manner to kids.</p>",
                    options_en: ["<p>trusting</p>", "<p>amiable</p>", 
                                "<p>obstinate</p>", "<p>envious</p>"],
                    options_hi: ["<p>trusting</p>", "<p>amiable</p>",
                                "<p>obstinate</p>", "<p>envious</p>"],
                    solution_en: "<p>2.(c) Obdurate- obstinate.</p>",
                    solution_hi: "<p>2.(c) Obdurate- obstinate./ जिद्दी</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the most appropriate meaning of the underlined idiom in the following sentence. <br>Rahul won the extempore competition. He is lucky to be blessed with the <span style=\"text-decoration: underline;\">gift of the gab</span>.</p>",
                    question_hi: "<p>3. Select the most appropriate meaning of the underlined idiom in the following sentence. <br>Rahul won the extempore competition. He is lucky to be blessed with the <span style=\"text-decoration: underline;\">gift of the gab</span>.</p>",
                    options_en: ["<p>An honest person</p>", "<p>A big surprise</p>", 
                                "<p>A lucky person</p>", "<p>A talent for speaking</p>"],
                    options_hi: ["<p>An honest person</p>", "<p>A big surprise</p>",
                                "<p>A lucky person</p>", "<p>A talent for speaking</p>"],
                    solution_en: "<p>3.(d) Gift of the gab- a talent for speaking.</p>",
                    solution_hi: "<p>3.(d) Gift of the gab - a talent for speaking./बोलने का कौशल।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Nina has not yet learned how to <span style=\"text-decoration: underline;\">straighten up and fly right</span>.</p>",
                    question_hi: "<p>4. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Nina has not yet learned how to <span style=\"text-decoration: underline;\">straighten up and fly right</span>.</p>",
                    options_en: ["<p>forget the wrongdoings of the others</p>", "<p>improve her absurd behaviour and act seriously</p>", 
                                "<p>be professional in flying the aircraft</p>", "<p>remember the past and prepare for the future</p>"],
                    options_hi: ["<p>forget the wrongdoings of the others</p>", "<p>improve her absurd behaviour and act seriously</p>",
                                "<p>be professional in flying the aircraft</p>", "<p>remember the past and prepare for the future</p>"],
                    solution_en: "<p>4.(b) improve her absurd behaviour and act seriously<br>The phrase &lsquo;straighten up and fly right&rsquo; means to get serious and stop acting absurd anymore. The given sentence states that Nina has not yet learned how to improve her absurd behaviour and act seriously. Hence, \'improve her absurd behaviour and act seriously\' is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(b) improve her absurd behaviour and act seriously<br>Phrase &lsquo;straighten up and fly right&rsquo; का अर्थ है गंभीर होना और बेतुके व्यवहार को बंद करना। दिए गए sentence में कहा गया है कि Nina ने अभी तक यह नहीं सीखा है कि अपने बेतुके व्यवहार (absurd behaviour) को कैसे सुधारा जाए और गंभीरता से काम लिया जाए। अतः, \'improve her absurd behaviour and act seriously\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>I had<span style=\"text-decoration: underline;\"> not only helped her by</span> giving hints but also with providing her links.</p>",
                    question_hi: "<p>5. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>I had <span style=\"text-decoration: underline;\">not only helped her by</span> giving hints but also with providing her links.</p>",
                    options_en: ["<p>helped her not only by</p>", "<p>helped her not by</p>", 
                                "<p>helped her only by</p>", "<p>helped not only her by </p>"],
                    options_hi: ["<p>helped her not only by</p>", "<p>helped her not by</p>",
                                "<p>helped her only by</p>", "<p>helped not only her by</p>"],
                    solution_en: "<p>5.(a) helped her not only by<br>The same part of speech is used after pair conjunctions such as &lsquo;not only&hellip;but also&rsquo;. There is a preposition followed by gerund(providing) after &lsquo;but also&rsquo;. Therefore, &lsquo;not only&rsquo; will be placed before the preposition &lsquo;by&rsquo;. Hence, &lsquo;helped her not only by&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(a) helped her not only by<br>Pair conjunctions जैसे &lsquo;not only&hellip;but also&rsquo; के बाद same part of speech का प्रयोग किया जाता है। &lsquo;but also&rsquo; के बाद एक preposition है जिसके बाद gerund(providing) आया है । इसलिए, &lsquo;not only&rsquo; को preposition &lsquo;by&rsquo; से पहले रखा जाएगा। अतः, &lsquo;helped her not only by&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>Positive self-talk <span style=\"text-decoration: underline;\">have a great affect</span> on one&rsquo;s self-worth.</p>",
                    question_hi: "<p>6. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>Positive self-talk <span style=\"text-decoration: underline;\">have a great affect</span> on one&rsquo;s self-worth.</p>",
                    options_en: ["<p>has a great affect</p>", "<p>have a great effect</p>", 
                                "<p>having a great affect</p>", "<p>has a great effect</p>"],
                    options_hi: ["<p>has a great affect</p>", "<p>have a great effect</p>",
                                "<p>having a great affect</p>", "<p>has a great effect</p>"],
                    solution_en: "<p>6.(d) has a great effect<br>According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, a singular subject always takes a singular verb. In the given sentence, &lsquo;positive self-talk&rsquo; is a singular subject that will take &lsquo;has&rsquo; as a singular verb. Also, the adjective &lsquo;great&rsquo; needs a noun to qualify its meaning. Hence, &lsquo;has a great effect&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(d) has a great effect<br>&ldquo;Subject-Verb Agreement Rule&rdquo; के अनुसार, singular subject के साथ हमेशा singular verb का प्रयोग होता है। दिए गए sentence में, \'positive self-talk,\' singular subject है, जिसके साथ singular verb \'has\' का प्रयोग होगा। इसके अलावा, adjective &lsquo;great&rsquo; को अपने meaning को qualify करने के लिए एक noun (effect) की आवश्यकता है। अतः, \'has a great effect\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "7. Parts of the following sentence have been given as options. Select the option that contains an error. Deepak is studying B.tech. at IIT Delhi to become the engineer.",
                    question_hi: "7. Parts of the following sentence have been given as options. Select the option that contains an error. Deepak is studying B.tech. at IIT Delhi to become the engineer.",
                    options_en: [" B.tech. at IIT Delhi ", "  the engineer ", 
                                "  to become", "  Deepak is studying"],
                    options_hi: [" B.tech. at IIT Delhi ", "  the engineer ",
                                "  to become", "  Deepak is studying"],
                    solution_en: "7.(b) the engineer<br />Indefinite articles (a/an) are used when we refer to something in general. Article ‘an’ will be used here as the word ‘engineer’ begins with a vowel sound. Hence, ‘an engineer’ is the most appropriate answer.",
                    solution_hi: "7.(b) the engineer<br />जब हम किसी चीज़ का सामान्य रूप से उल्लेख करते हैं तब indefinite articles (a/an) का प्रयोग किया जाता है । यहाँ पर Article ‘an’ का प्रयोग किया जाएगा क्योंकि word ‘engineer,’  vowel sound से शुरू होता है। अतः, ‘an engineer’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "8. Select the option that expresses the given sentence in active voice.<br />He was given a reward for his contribution by the Governor.",
                    question_hi: "8. Select the option that expresses the given sentence in active voice.<br />He was given a reward for his contribution by the Governor.",
                    options_en: [" For his contribution, he was rewarded by the governor.  ", " The governor rewarded him.  ", 
                                " The governor gave him a reward for his contribution.  ", " Rewarded by the governor, as he contributed along. "],
                    options_hi: [" For his contribution, he was rewarded by the governor.  ", " The governor rewarded him.  ",
                                " The governor gave him a reward for his contribution.  ", " Rewarded by the governor, as he contributed along. "],
                    solution_en: "8.(c) The governor gave him a reward for his contribution. (Correct)<br />(a) For his contribution, he was rewarded by the governor. (Incorrect Sentence Structure)<br />(b) The governor rewarded him. (Incorrect Sentence Structure)<br />(d) Rewarded by the governor, as he contributed along. (Incorrect Sentence Structure)",
                    solution_hi: "8.(c) The governor gave him a reward for his contribution. (Correct)<br />(a) For his contribution, he was rewarded by the governor. (गलत Sentence Structure)<br />(b) The governor rewarded him. (गलत Sentence Structure)<br />(d) Rewarded by the governor, as he contributed along. (गलत Sentence Structure)",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate ANTONYM of the given word. <br>Hostile</p>",
                    question_hi: "<p>9. Select the most appropriate ANTONYM of the given word. <br>Hostile</p>",
                    options_en: ["<p>Incoherent</p>", "<p>Doubtful</p>", 
                                "<p>Eager</p>", "<p>Friendly</p>"],
                    options_hi: ["<p>Incoherent</p>", "<p>Doubtful</p>",
                                "<p>Eager</p>", "<p>Friendly</p>"],
                    solution_en: "<p>9.(d) <strong>Friendly</strong>- kind and pleasant in behavior towards others.<br><strong>Hostile</strong>- showing or feeling opposition or dislike; unfriendly.<br><strong>Incoherent</strong>- unclear or difficult to understand.<br><strong>Doubtful</strong>- feeling uncertain or lacking confidence.<br><strong>Eager</strong>- keen or enthusiastic about something.</p>",
                    solution_hi: "<p>9.(d) <strong>Friendly </strong>(मैत्रीपूर्ण/मिलनसार)- kind and pleasant in behavior towards others.<br><strong>Hostile </strong>(शत्रुतापूर्ण)- showing or feeling opposition or dislike; unfriendly.<br><strong>Incoherent </strong>(असंगत)- unclear or difficult to understand.<br><strong>Doubtful </strong>(संदेहास्पद)- feeling uncertain or lacking confidence.<br><strong>Eager </strong>(उत्सुक)- keen or enthusiastic about something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the option that can be used as a one-word substitute for the given group of words.<br>Dealing with things in a practical and sensible way</p>",
                    question_hi: "<p>10. Select the option that can be used as a one-word substitute for the given group of words.<br>Dealing with things in a practical and sensible way</p>",
                    options_en: ["<p>Pragmatic</p>", "<p>Sceptic</p>", 
                                "<p>Stoic</p>", "<p>Cynic</p>"],
                    options_hi: ["<p>Pragmatic</p>", "<p>Sceptic</p>",
                                "<p>Stoic</p>", "<p>Cynic</p>"],
                    solution_en: "<p>10.(a) <strong>Pragmatic</strong>- dealing with things in a practical and sensible way.<br><strong>Sceptic</strong>- a person inclined to question or doubt accepted opinions.<br><strong>Stoic</strong>- a person who can endure pain or hardship without showing their feelings or complaining.<br><strong>Cynic</strong>- believing that people are only interested in themselves and are not sincere.</p>",
                    solution_hi: "<p>10.(a) <strong>Pragmatic</strong> (व्यावहारिक)- dealing with things in a practical and sensible way.<br><strong>Sceptic</strong> (संदेहवादी)- a person inclined to question or doubt accepted opinions.<br><strong>Stoic </strong>(सहनशील)- a person who can endure pain or hardship without showing their feelings or complaining.<br><strong>Cynic </strong>(निंदक)- believing that people are only interested in themselves and are not sincere.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "11. Select the correct spelling to fill in the blank.<br />Definitions of _________ are subjective to various interpretations.",
                    question_hi: "11. Select the correct spelling to fill in the blank.<br />Definitions of _________ are subjective to various interpretations.",
                    options_en: [" sovereignty", " suvereignty ", 
                                " sovereinity", " soverenty"],
                    options_hi: ["  sovereignty", " suvereignty ",
                                "  sovereinity", " soverenty"],
                    solution_en: "11.(a) sovereignty<br />\'Sovereignty\' is the correct spelling.",
                    solution_hi: "11.(a) sovereignty<br />\'Sovereignty\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate ANTONYM of the given word.<br>Foment</p>",
                    question_hi: "<p>12. Select the most appropriate ANTONYM of the given word.<br>Foment</p>",
                    options_en: ["<p>Proximity</p>", "<p>Remoteness</p>", 
                                "<p>Separation</p>", "<p>Regulate</p>"],
                    options_hi: ["<p>Proximity</p>", "<p>Remoteness</p>",
                                "<p>Separation</p>", "<p>Regulate</p>"],
                    solution_en: "<p>12.(d) <strong>Regulate</strong>- to control or maintain something according to rules or laws.<br><strong>Foment</strong>- to stir up trouble or rebellion.<br><strong>Proximity</strong>- the state of being near in space or time.<br><strong>Remoteness</strong>- the state of being far away in distance or time.<br><strong>Separation</strong>- the act of moving or being apart from something or someone.</p>",
                    solution_hi: "<p>12.(d) <strong>Regulate </strong>(संयमित रखना)- to control or maintain something according to rules or laws.<br><strong>Foment </strong>(भड़काना)- to stir up trouble or rebellion.<br><strong>Proximity </strong>(समीपता)- the state of being near in space or time.<br><strong>Remoteness </strong>(सुदूरता)- the state of being far away in distance or time.<br><strong>Separation </strong>(पृथक्करण)- the act of moving or being apart from something or someone.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "13. Select the INCORRECTLY spelt word. ",
                    question_hi: "13. Select the INCORRECTLY spelt word. ",
                    options_en: [" Guilty ", " Atempt", 
                                " Admit ", " Agree"],
                    options_hi: [" Guilty ", " Atempt",
                                " Admit ", " Agree"],
                    solution_en: "13.(b) Atempt<br />\'Attempt\' is the correct spelling.",
                    solution_hi: "13.(b) Atempt<br />\'Attempt\' सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the option that can be used as a one-word substitute for the given group of words<br>Anything pertaining to cats</p>",
                    question_hi: "<p>14. Select the option that can be used as a one-word substitute for the given group of words<br>Anything pertaining to cats</p>",
                    options_en: ["<p>Bovine</p>", "<p>Canine</p>", 
                                "<p>Taurine</p>", "<p>Feline</p>"],
                    options_hi: ["<p>Bovine</p>", "<p>Canine</p>",
                                "<p>Taurine</p>", "<p>Feline</p>"],
                    solution_en: "<p>14.(d) <strong>Feline</strong>- anything pertaining to cats.<br><strong>Bovine</strong>- relating to cattle.<br><strong>Canine</strong>- relating to dogs.<br><strong>Taurine</strong>- relating to a bull.</p>",
                    solution_hi: "<p>14.(d) <strong>Feline </strong>(बिल्ली से संबंधित)- anything pertaining to cats.<br><strong>Bovine </strong>(घरेलू पशु से संबंधित)- relating to cattle.<br><strong>Canine </strong>(कुत्ते से संबंधित)- relating to dogs.<br><strong>Taurine </strong>(बैल से संबंधित)- relating to a bull.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "15. Select the most appropriate option to fill in the blank.<br />Sharat is always ______ about showing up for work because he believes that tardiness gets us nowhere.",
                    question_hi: "15. Select the most appropriate option to fill in the blank.<br />Sharat is always ______ about showing up for work because he believes that tardiness gets us nowhere.",
                    options_en: ["  selective", " patient ", 
                                "  drowsy", " punctual"],
                    options_hi: [" selective", " patient ",
                                " drowsy", " punctual"],
                    solution_en: "15.(d) punctual<br />‘Punctual’ means doing something at the agreed or proper time. The given sentence states that Sharat is always punctual about showing up for work because he believes that tardiness gets us nowhere. Hence, \'punctual\' is the most appropriate answer.",
                    solution_hi: "15.(d) punctual<br />‘Punctual’ का अर्थ है किसी काम को तय या उचित समय पर करने वाला। दिए गए sentence में बताया गया है कि Sharat हमेशा काम पर आने के मामले में समय का पाबंद (punctual) रहता है क्योंकि उसका मानना ​​है कि देर से आने से कुछ हासिल नहीं होता। अतः, \'punctual\'  सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate synonym of the underlined word in the following sentence.<br>Sachin Tendulkar was an <span style=\"text-decoration: underline;\">indispensable </span>member of the Indian cricket team in his days.</p>",
                    question_hi: "<p>16. Select the most appropriate synonym of the underlined word in the following sentence.<br>Sachin Tendulkar was an <span style=\"text-decoration: underline;\">indispensable </span>member of the Indian cricket team in his days.</p>",
                    options_en: ["<p>Motivational</p>", "<p>Essential</p>", 
                                "<p>Optional</p>", "<p>Functional</p>"],
                    options_hi: ["<p>Motivational</p>", "<p>Essential</p>",
                                "<p>Optional</p>", "<p>Functional</p>"],
                    solution_en: "<p>16.(b) <strong>Essential</strong>- absolutely necessary or important.<br><strong>Indispensable</strong>- absolutely necessary.<br><strong>Motivational</strong>- providing encouragement or inspiration.<br><strong>Optional</strong>- available to be chosen but not required.<br><strong>Functional</strong>- designed to be practical and useful.</p>",
                    solution_hi: "<p>16.(b) <strong>Essential </strong>(महत्वपूर्ण)- absolutely necessary or important.<br><strong>Indispensable </strong>(अपरिहार्य/अनिवार्य)- absolutely necessary.<br><strong>Motivational </strong>(प्रेरणार्थक)- providing encouragement or inspiration.<br><strong>Optional </strong>(वैकल्पिक)- available to be chosen but not required.<br><strong>Functional </strong>(कार्यात्मक)- designed to be practical and useful.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY.<br />Master Sam / is a heaviest / boy in / grade twelve.",
                    question_hi: "17. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY.<br />Master Sam / is a heaviest / boy in / grade twelve.",
                    options_en: [" boy in", " Master Sam ", 
                                " grade twelve", " is a heaviest"],
                    options_hi: ["  boy in", "  Master Sam ",
                                "  grade twelve", " is a heaviest"],
                    solution_en: "17.(d) is a heaviest<br />‘Heaviest’ is a superlative degree and we always place the definite article ‘the’ before any superlative degree. Hence, ‘is the heaviest’ is the most appropriate answer.",
                    solution_hi: "17.(d) is a heaviest<br />‘Heaviest’  एक superlative degree  है और हम हमेशा किसी भी superlative degree से पहले definite article ‘the’ का प्रयोग करते हैं। अतः, ‘is the heaviest’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the option that expresses the given sentence in passive voice.<br>We have to say little in this matter.</p>",
                    question_hi: "<p>18. Select the option that expresses the given sentence in passive voice.<br>We have to say little in this matter.</p>",
                    options_en: ["<p>Little had to be said in this matter.</p>", "<p>In this matter, little is to be said.</p>", 
                                "<p>In this matter, little must be said.</p>", "<p>Little has to be said in this matter.</p>"],
                    options_hi: ["<p>Little had to be said in this matter.</p>", "<p>In this matter, little is to be said.</p>",
                                "<p>In this matter, little must be said.</p>", "<p>Little has to be said in this matter.</p>"],
                    solution_en: "<p>18.(d) Little has to be said in this matter. (Correct)<br>(a) Little <span style=\"text-decoration: underline;\">had to be</span> said in this matter. (Incorrect Tense)<br>(b) In this matter, little <span style=\"text-decoration: underline;\">is </span>to be said. (Incorrect Verb)<br>(c) In this matter, little <span style=\"text-decoration: underline;\">must </span>be said. (Incorrect use of modal)</p>",
                    solution_hi: "<p>18.(d) Little has to be said in this matter. (Correct)<br>(a) Little <span style=\"text-decoration: underline;\">had to be</span> said in this matter. (गलत Tense)<br>(b) In this matter, little <span style=\"text-decoration: underline;\">is </span>to be said. (गलत Verb)<br>(c) In this matter, little <span style=\"text-decoration: underline;\">must </span>be said. (Modal का गलत प्रयोग)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the option that can be used as a one-word substitute for the given group of words.<br>That which is no longer in existence</p>",
                    question_hi: "<p>19. Select the option that can be used as a one-word substitute for the given group of words.<br>That which is no longer in existence</p>",
                    options_en: ["<p>Extinct</p>", "<p>Removed</p>", 
                                "<p>Vanished</p>", "<p>Destroyed</p>"],
                    options_hi: ["<p>Extinct</p>", "<p>Removed</p>",
                                "<p>Vanished</p>", "<p>Destroyed</p>"],
                    solution_en: "<p>19.(a) <strong>Extinct</strong>- that which is no longer in existence.</p>",
                    solution_hi: "<p>19.(a) <strong>Extinct</strong>- that which is no longer in existence./जो अब अस्तित्व में नहीं है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the most appropriate synonym of the given word.<br>Cascade</p>",
                    question_hi: "<p>20. Select the most appropriate synonym of the given word.<br>Cascade</p>",
                    options_en: ["<p>Garden</p>", "<p>Waterfall</p>", 
                                "<p>Criticise</p>", "<p>Arcade</p>"],
                    options_hi: ["<p>Garden</p>", "<p>Waterfall</p>",
                                "<p>Criticise</p>", "<p>Arcade</p>"],
                    solution_en: "<p>20.(b) <strong>Waterfall</strong>- a flow of water falling from a height.<br><strong>Cascade</strong>- a small waterfall, typically one of several in a series.<br><strong>Garden</strong>- a plot of land used for growing flowers, vegetables, or other plants.<br><strong>Criticise</strong>- to express disapproval or point out faults.<br><strong>Arcade</strong>- a covered passage with arches along one or both sides.</p>",
                    solution_hi: "<p>20.(b) <strong>Waterfall </strong>(झरना)- a flow of water falling from a height.<br><strong>Cascade </strong>(जलप्रपात )- a small waterfall, typically one of several in a series.<br><strong>Garden </strong>(उद्यान)- a plot of land used for growing flowers, vegetables, or other plants.<br><strong>Criticise </strong>(आलोचना करना)- to express disapproval or point out faults.<br><strong>Arcade </strong>(आच्छादित मार्ग/तोरणपथ )- a covered passage with arches along one or both sides.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.<strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>to</p>", "<p>for</p>", 
                                "<p>of</p>", "<p>with</p>"],
                    options_hi: ["<p>to</p>", "<p>for</p>",
                                "<p>of</p>", "<p>with</p>"],
                    solution_en: "<p>21.(a) to<br>&lsquo;To&rsquo; is the correct preposition here. &lsquo;According to something&rsquo; is the correct phrase. Hence, &lsquo;to&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(a) to<br>यहाँ &lsquo;to&rsquo; सही preposition है। &lsquo;According to something&rsquo;, correct phrase है। अतः, &lsquo;to&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 22</p>",
                    question_hi: "<p>22. <strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>criteria</p>", "<p>options</p>", 
                                "<p>display</p>", "<p>aspects</p>"],
                    options_hi: ["<p>criteria</p>", "<p>options</p>",
                                "<p>display</p>", "<p>aspects</p>"],
                    solution_en: "<p>22.(d) aspects<br>&lsquo;Aspect&rsquo; means a particular part or feature of something. The given passage states that the two broad aspects of classical dancing are the tandava and the lasya. Hence, \'aspects\' is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(d) aspects<br>&lsquo;Aspect&rsquo; का अर्थ है किसी चीज़ का कोई विशेष भाग या विशेषता। दिए गए passage में बताया गया है कि classical dancing के दो व्यापक पहलू हैं तांडव और लास्य। अतः, \'aspects\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.<strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23.<strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>renovate</p>", "<p>informed</p>", 
                                "<p>flourished</p>", "<p>associated</p>"],
                    options_hi: ["<p>renovate</p>", "<p>informed</p>",
                                "<p>flourished</p>", "<p>associated</p>"],
                    solution_en: "<p>23..(d) associated<br>&lsquo;Associated&rsquo; means (of a person or thing) connected with something else. The given passage states that tandava is associated with Shiva, and lasya with Parvati. Hence, \'associated\' is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(d) associated<br>&lsquo;Associated&rsquo; का अर्थ है (किसी व्यक्ति या वस्तु का) किसी अन्य चीज से जुड़ा होना। दिए गए passage में कहा गया है कि तांडव Shiva से जुड़ा है, और लास्य Parvati से। अतः, \'associated\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>vegetative</p>", "<p>commodious</p>", 
                                "<p>haphazard</p>", "<p>interpretative</p>"],
                    options_hi: ["<p>vegetative</p>", "<p>commodious</p>",
                                "<p>haphazard</p>", "<p>interpretative</p>"],
                    solution_en: "<p>24.(d) interpretative<br>&lsquo;Interpretative&rsquo; means relating to explaining or understanding the meaning of something. The given passage states that dance which is pure movement is called nritta, and dance which is interpretative in nature is called nritya. Hence, \'interpretative\' is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(d) interpretative<br>&lsquo;Interpretative&rsquo; का अर्थ है किसी चीज़ का meaning समझाने या समझने से संबंधित। दिए गए passage में कहा गया है कि dance जो pure movement है उसे nritta कहा जाता है, और dance जो प्रकृति में व्याख्यात्मक(interpretative) है उसे nritya कहा जाता है। अतः, \'interpretative\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25. <strong>Cloze test:</strong><br>A dance which is created or choreographed and performed according (21)___________ the tenets of the Natya Shastra is called a classical dance. The two broad (22)____________ of classical dancing are the tandava and the lasya. Power and force are typical of the tandava; grace and delicacy of the lasya. Tandava is (23)_______________ with Shiva, and lasya with Parvati. Dance which is pure movement is called nritta, and dance which is (24)_______________ in nature is called nritya. A dancer, in the classical tradition, has to have years of training before he or she can begin to perform on the stage. The four main (25)_____________ of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Bharat Natyam is the oldest and most popular dance-form of India.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>terminologies</p>", "<p>schools</p>", 
                                "<p>characters</p>", "<p>functions</p>"],
                    options_hi: ["<p>terminologies</p>", "<p>schools</p>",
                                "<p>characters</p>", "<p>functions</p>"],
                    solution_en: "<p>25.(b) schools<br>&lsquo;School&rsquo; means a body of creative artists or writers or thinkers linked by a similar style or by similar teachers. The given passage states that the four main schools of classical dancing in India are : Bharat Natyam, Kathakali, Manipuri, Kathak. Hence, &lsquo;schools&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(b) schools<br>&lsquo;School&rsquo; का अर्थ है समान शैली या समान शिक्षकों से जुड़े रचनात्मक कलाकारों या लेखकों या विचारकों का एक समूह। दिए गए passage में बताया गया है कि भारत में शास्त्रीय नृत्य (classical dancing) की चार प्रमुख शैलियाँ (schools) हैं: भरतनाट्यम, कथकली, मणिपुरी, कथक। अतः, &lsquo;schools&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>