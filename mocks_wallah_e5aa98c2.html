<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Kosal, Agna, Kashi and Vajji were all:</p>",
                    question_hi: "<p>1. कोसल, आज्ञा, काशी और वज्जी सभी__________ थे</p>",
                    options_en: ["<p>conquered by King Ajatshatru</p>", "<p>part of 16 Mahajanapadas</p>", 
                                "<p>ruled by King Harshavardhana in 6th century AD</p>", "<p>conquered by King Jaichand</p>"],
                    options_hi: ["<p>राजा अजातशत्रु द्वारा विजय प्राप्त</p>", "<p>16 महाजनपद के भाग</p>",
                                "<p>छठी शताब्दी ईस्वी में राजा हर्षवर्धन द्वारा शासित</p>", "<p>राजा जयचंद द्वारा जीते गये थे</p>"],
                    solution_en: "<p>1.(b) <strong>Part of 16 Mahajanapadas. </strong>16 Mahajanapadas and their Capital: Kasi (Varanasi), Kosala (Shravasti), Anga (Champa), Magadha (Rajgir), Vajji (Vaishali), Malla (Kushinagar and Pava), Chedi (Suktimati), Vatsa (Kaushambi), Kuru (Indraprastha), Panchala (Ahichchhatra), Matsya (Viratnagar), Surasena (Mathura), Assaka (Potali) Avanti (Ujjain and Mahishmati), Gandhara (Taxila) and Kamboja (Rajpur).</p>",
                    solution_hi: "<p>1.(b) <strong>16 महाजनपदों के भाग।</strong> 16 महाजनपद और उनकी राजधानियाँ : काशी (वाराणसी), कोसल (श्रावस्ती), अंग (चम्पा), मगध (राजगीर), वज्जि (वैशाली), मल्ल (कुशीनगर और पावा), चेदि (सुक्तिमती), वत्स (कौशाम्बी), कुरु ( इंद्रप्रस्थ), पांचाल (अहिच्छत्र), मत्स्य (विराटनगर), सुरसेन (मथुरा), अस्सक (पोटाली) अवंती (उज्जैन और महिष्मती), गांधार (तक्षशिला) और कंबोज (राजपुर)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following was the capital of the kingdom Magadh?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन मगध राज्य की राजधानी थी?</p>",
                    options_en: ["<p>Vaishali</p>", "<p>Ujjain</p>", 
                                "<p>Rajgir</p>", "<p>Kaushambi</p>"],
                    options_hi: ["<p>वैशाली</p>", "<p>उज्जैन</p>",
                                "<p>राजगीर</p>", "<p>कौशांबी</p>"],
                    solution_en: "<p>2.(c) <strong>Rajgir </strong>(Rajgriha, \'the home of Royalty\') was the first capital of the kingdom Magadh. Ajatshatru\'s son Udayin moved the capital to Pataliputra. Vaishali was the capital of the Vajji mahajanapada. Ujjain was the capital of the Avanti mahajanapada. Kaushambi was the capital of the Vatsa mahajanapada.</p>",
                    solution_hi: "<p>2.(c) <strong>राजगीर</strong> (राजगृह, \'रॉयल्टी का घर\') मगध राज्य की प्रथम राजधानी थी। अजातशत्रु का पुत्र उदयिन राजधानी को पाटलिपुत्र ले गया। वैशाली वज्जि महाजनपद की राजधानी थी। उज्जैन अवंती महाजनपद की राजधानी थी। कौशांबी वत्स महाजनपद की राजधानी थी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. With reference to the sixteen Mahajanapadas, which among the following was NOT a capital city?</p>",
                    question_hi: "<p>3. सोलह महाजनपदों के संदर्भ में, निम्नलिखित में से कौन सा एक राजधानी शहर नहीं थी ?</p>",
                    options_en: ["<p>Ujjain</p>", "<p>Avanti</p>", 
                                "<p>Sravasti</p>", "<p>Kausambi</p>"],
                    options_hi: ["<p>उज्जैन</p>", "<p>अवंती</p>",
                                "<p>श्रावस्ती</p>", "<p>कौशांबी</p>"],
                    solution_en: "<p>3.(b) <strong>Avanti.</strong> According to the Buddhist texts, the Anguttara Nikaya, Avanti was one of the 16 mahajanapadas of the 6th century BCE. Avanti (Ujjain), Kosala (Sravasti), Vatsa (Kausambi).</p>",
                    solution_hi: "<p>3.(b) <strong>अवंती।</strong> बौद्ध ग्रंथों, अंगुत्तर निकाय के अनुसार, अवंती छठी शताब्दी ईसा पूर्व के 16 महाजनपदों में से एक था। उज्जैन प्राचीन अवंती साम्राज्य की राजधानी थी। अवंती (उज्जैन), कोसल (सरवस्ती), वत्स (कौशाम्बी)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Kosala Mahajanapada (6th century BCE) is a part of modern day:</p>",
                    question_hi: "<p>4. कोसल महाजनपद (छठी शताब्दी ईसा पूर्व) वर्तमान में किसका एक भाग है?</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Haryana</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>हरियाणा</p>",
                                "<p>मध्य प्रदेश</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>4.(d) <strong>Uttar Pradesh</strong>. Modern day names of<strong> other Mahajanapadas:</strong> Munger and Bhagalpur (Anga), Gaya and Patna (Magadha), Banaras (Kashi), Prayagraj (Vatsa), Malwa and Madhya Pradesh (Avanti), Rawalpindi (Gandhara), North Bihar (Vajji), Jaipur (Matsya), Meerut and Haryana (Kuru), Western Uttar Pradesh (Shurasena and Panchala), Bundelkhand region (Chedi), Rajouri, Hajra and North -West Frontier Province (Kamboja), Banks of Godavari (Asmaka or Assaka), Deoria and Uttar Pradesh (Malla).</p>",
                    solution_hi: "<p>4.(d) <strong>उत्तर प्रदेश</strong>। <strong>अन्य महाजनपदों </strong>के आधुनिक नाम: मुंगेर और भागलपुर (अंगा), गया और पटना (मगध), बनारस (काशी), प्रयागराज (वत्स), मालवा और मध्य प्रदेश (अवंती), रावलपिंडी (गांधार), उत्तर बिहार (वज्जि), जयपुर (मत्स्य), मेरठ और हरियाणा (कुरु), पश्चिमी उत्तर प्रदेश (शूरसेन और पंचाल), बुन्देलखण्ड क्षेत्र (चेदि), राजौरी, हाजरा और उत्तर-पश्चिम सीमांत प्रांत (कम्बोज), गोदावरी के तट (अस्माका या अस्सका), देवरिया और उत्तर प्रदेश (मल्ला)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Iron mines in Jharkhand attributed to the rise of which of the following kingdoms in ancient India?</p>",
                    question_hi: "<p>5. झारखंड में पाई जाने वाली लोहे की खदानों ने प्राचीन भारत में निम्नलिखित में से किस साम्राज्य के उदय में सहयोग दिया था?</p>",
                    options_en: ["<p>Kuru</p>", "<p>Magadha</p>", 
                                "<p>Kashi</p>", "<p>Kushan</p>"],
                    options_hi: ["<p>कुरु</p>", "<p>मगध</p>",
                                "<p>काशी</p>", "<p>कुषाण</p>"],
                    solution_en: "<p>5.(b) <strong>Magadha.</strong> Kuru (Meerut and Haryana) - Ruled by an Indo-Aryan clan in the iron age Vedic India (1200 - 900 BC) who ruled the regions between Saraswati and the Ganga river.</p>",
                    solution_hi: "<p>5.(b) <strong>मगध</strong> । कुरु (मेरठ और हरियाणा) - लौह युग वैदिक भारत (1200 - 900 ईसा पूर्व) में एक इंडो-आर्यन कबीले द्वारा शासित, जिन्होंने सरस्वती और गंगा नदी के बीच के क्षेत्रों पर शासन किया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Magadha Mahajanapada was surrounded by the rivers______.",
                    question_hi: "6. मगध महाजनपद _____ नदियों से घिरा हुआ था ।",
                    options_en: [" Ganga and Jhelum", " Ganga and Yamuna", 
                                " Ganga and Son", " Ganga and Ghaghara"],
                    options_hi: [" गंगा और झेलम     ", " गंगा और यमुना ",
                                " गंगा और सोन   ", " गंगा और घाघरा"],
                    solution_en: "<p>6.(c) <strong>Ganga and Son</strong>. Magadha became the most powerful among the mahajanapadas in 6th Century AD due to these rivers and fertility of land. Now Magadha is a part of Bihar region.</p>",
                    solution_hi: "<p>6.(c)&nbsp; <strong>गंगा और सोन</strong> । इन नदियों और भूमि की उर्वरता के कारण छठी शताब्दी ई. में मगध महाजनपदों में सबसे शक्तिशाली बन गया। अब मगध बिहार क्षेत्र का एक भाग है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. In ancient times, the area to the south of the Ganga was known as ______.",
                    question_hi: "7. प्राचीन काल में, गंगा के दक्षिण के क्षेत्र को _______ के रूप में जाना जाता था।",
                    options_en: [" Magadha", " Kosala", 
                                " Anga", " Matsya"],
                    options_hi: [" मगध ", " कोसल   ",
                                " अंग   ", " मत्स्य"],
                    solution_en: "<p>7.(a) <strong>Magadha.</strong> The earliest reference to the Magadha people occurs in the Atharvaveda, where they are found listed along with the Angas, Gandharis and Mujavats. Magadha (Capitals - Rajgriha and Pataliputra), Kosala (Sravasti), Anga (Champa) and Matsya (Viratnagar) were among the 16 Mahajanapadas.</p>",
                    solution_hi: "<p>7.(a) <strong>मगध</strong> । मगध लोगों का सबसे पहला उल्लेख अथर्ववेद में मिलता है, जहाँ उन्हें अंग, गांधारी और मुजावत के साथ सूचीबद्ध पाया जाता है। मगध (राजधानियाँ - राजगृह और पाटलिपुत्र), कोसल (श्रावस्ती), अंग (चंपा) और मत्स्य (विराटनगर) 16 महाजनपदों में से थे।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. ________ in Bihar was the capital of Magadha for several years. Later the capital was shifted to Pataliputra (present-day Patna).</p>",
                    question_hi: "<p>8. बिहार में______कई वर्षों तक मगध की राजधानी थी। बाद में राजधानी को पाटलिपुत्र (वर्तमान पटना) स्थानांतरित कर दिया गया।</p>",
                    options_en: ["<p>Lumbini</p>", "<p>Kalinga</p>", 
                                "<p>Mathura</p>", "<p>Rajagriha</p>"],
                    options_hi: ["<p>लुम्बिनी</p>", "<p>कलिंग</p>",
                                "<p>मथुरा</p>", "<p>राजगृह</p>"],
                    solution_en: "<p>8.(d) <strong>Rajagriha.</strong> Magadha Empire was ruled by following dynasties: Haryanka Dynasty (Bimbisara), Shishunaga Dynasty (Shishunaga), and Nanda Dynasty (Mahapadma Nanda). Alexander invaded North-Western India in 326 BC during the reign of Dhana Nanda (last emperor of Nanda).</p>",
                    solution_hi: "<p>8.(d) <strong>राजगृह </strong>। मगध साम्राज्य पर निम्नलिखित राजवंशों का शासन था: हर्यंक वंश (बिंबिसार), शिशुनाग वंश (शिशुनाग), और नंद वंश (महापद्म नंदा)। सिकंदर ने 326 ईसा पूर्व में धनानन्द (नंद वंश के अंतिम सम्राट) के शासनकाल के दौरान उत्तर-पश्चिमी भारत पर आक्रमण किया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Rulers of Mahajanapadas in ancient India collected taxes on crops at the rate of _____ of what was produced.</p>",
                    question_hi: "<p>9. प्राचीन भारत में महाजनपदों के शासक फसलों की उपज के __ की दर से कर वसूलते थे।</p>",
                    options_en: ["<p>1/3rd</p>", "<p>1/5th</p>", 
                                "<p>1/6th</p>", "<p>1/4th</p>"],
                    options_hi: ["<p>1/3वाँ भाग</p>", "<p>1/5वाँ भाग</p>",
                                "<p>1/6वाँ भाग</p>", "<p>1/4वाँ भाग</p>"],
                    solution_en: "<p>9.(c) <strong>1/6th. </strong>Rulers of mahajanapadas in ancient India collected a tax called \'Bhaga\' from the Farmers in their region.</p>",
                    solution_hi: "<p>9.(c) <strong>1/6वाँ भाग। </strong>प्राचीन भारत में महाजनपदों के शासक अपने क्षेत्र के किसानों से \'भाग\' नामक कर वसूल करते थे।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following pairs of &lsquo;Name of Ruler &ndash; Empire&rsquo; is correctly matched? <br>I. Mahapadma Nanda &ndash; Magadha Empire <br>II. Ajatasatru &ndash; Maurya Empire</p>",
                    question_hi: "<p>10. \'शासक का नाम - साम्राज्य\' की निम्नलिखित में से कौन सी जोड़ी सही सुमेलित है?<br>I. महापद्म नंद - मगध साम्राज्य<br>II. अजातशत्रु &ndash; मौर्य साम्राज्य</p>",
                    options_en: ["<p>Only II</p>", "<p>Neither I nor II</p>", 
                                "<p>Both I and II</p>", "<p>Only I</p>"],
                    options_hi: ["<p>केवल II</p>", "<p>न तो I और न ही II</p>",
                                "<p>I और II दोनों</p>", "<p>केवल I</p>"],
                    solution_en: "<p>10.(d) <strong>Only I. Mahapadma Nanda</strong> established the Nanda dynasty in Eastern Magadha. He established Pataliputra (Patna, Bihar) as his kingdom\'s capital. He is called the &ldquo;first historical emperor of India.&rdquo;.</p>",
                    solution_hi: "<p>10.(d) <strong>केवल I&nbsp; महापद्म नंद</strong> ने पूर्वी मगध में नंद वंश की स्थापना की। उसने अपने राज्य की राजधानी के रूप में पाटलिपुत्र (पटना, बिहार) की स्थापना की। उन्हें \"भारत का पहला ऐतिहासिक सम्राट\" कहा जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. Ajatshatru, a ruler of the Haryanka Dynasty, was the son of _______.",
                    question_hi: "11. हर्यंक वंश का शासक अजातशत्रु ______ का पुत्र था । ",
                    options_en: [" Naga-Dasak", " Udayin ", 
                                " Anurudha ", " Bimbisara "],
                    options_hi: [" नागदशक      ", " उदायिन ",
                                " अनिरुद्ध      ", " बिम्बिसार "],
                    solution_en: "<p>11.(d) <strong>Bimbisara</strong> was the first ruler of Magadha from the Haryanka dynasty. Ajatashatru (492 to 460 BCE or early 4th century BCE) was a king of the Haryanka dynasty of Magadha in East India. He was the son of King Bimbisara and was a contemporary of both Mahavira (Nigantha Nataputta) and Gautama Buddha.</p>",
                    solution_hi: "<p>11.(d) <strong>बिम्बिसार</strong> हर्यक वंश का मगध का पहला शासक था। अजातशत्रु (492 से 460 ईसा पूर्व या चौथी शताब्दी ईसा पूर्व की शुरुआत) पूर्वी भारत में मगध के हर्यंक राजवंश के एक राजा थे। वह राजा बिंबिसार के पुत्र थे और महावीर (निगंता <br>नटपुत्त) और गौतम बुद्ध दोनों के समकालीन थे।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. Who among the following was the last ruler of the Nanda dynasty?",
                    question_hi: "12. नंद वंश का अंतिम शासक निम्न में से कौन था ?    ",
                    options_en: [" Dhanananda", " Panduka ", 
                                " Govishanaka ", " Kaivarta"],
                    options_hi: [" धनानंद     ", " पांडुका ",
                                " गोविशनाका             ", " कैवर्ता "],
                    solution_en: "<p>12.(a) <strong>Dhana Nanda</strong> (died 321 BCE) was the last ruler of the Nanda dynasty. He was the youngest of the eight brothers of the dynasty\'s founder Ugrasena. The Nanda dynasty ended with him in about 321 BCE when Chandragupta Maurya led the foundation of the Mauryan Dynasty.</p>",
                    solution_hi: "<p>12.(a) <strong>धनानंद</strong> (मृत्यु 321 ईसा पूर्व) नंद वंश के अंतिम शासक थे। वह राजवंश के संस्थापक उग्रसेन के आठ भाइयों में सबसे छोटे थे। नंद वंश उनके साथ लगभग 321 ईसा पूर्व में समाप्त हो गया, जब चंद्रगुप्त मौर्य ने मौर्य वंश की नींव रखी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. Which of the following was the capital of Magadh for several years before it was shifted to Pataliputra?",
                    question_hi: "13. पाटलिपुत्र में स्थानांतरित होने से पहले निम्न में से कौन-सा कई वर्षों तक मगध की राजधानी थी?",
                    options_en: [" Patna", " Gaya", 
                                " Nalanda", " Rajagriha"],
                    options_hi: [" पटना ", " गया ",
                                " नालंदा ", " राजगृह"],
                    solution_en: "<p>13.(d) <strong>Rajagriha</strong>. Magadha was the most powerful region of the sixteen Mahajanapadas. The first dynasty ruled over Magadha was the Haryanka dynasty, its rulers Bimbisara (544-492 BC) (founder of this dynasty) made Rajagriha as its capital, Udayin (460-444 BC) (3rd ruler) established the Patliputra (on the confluence of river Ganga and Son) and shifted his capital from Rajagriha to Pataliputra</p>",
                    solution_hi: "<p>13.(d) <strong>राजगृह।</strong> मगध सोलह महाजनपदों में सबसे शक्तिशाली क्षेत्र था। मगध पर शासन करने वाला पहला राजवंश हर्यक राजवंश था, इसके शासकों बिंबिसार (544-492 ईसा पूर्व) (इस राजवंश के संस्थापक) ने राजगृह को अपनी राजधानी बनाया, उदयिन (460-444 ईसा पूर्व) (तीसरे शासक) ने पाटलिपुत्र (गंगा और सोन नदी के संगम पर) की स्थापना की और अपनी राजधानी राजगृह से पाटलिपुत्र स्थानांतरित कर दी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. The capital of Vajji Mahajanapada was ________ .",
                    question_hi: "14. वज्जी महाजनपद की राजधानी ______ थीI",
                    options_en: [" Pataliputra", " Champa", 
                                " Vaishali", " Koshala"],
                    options_hi: [" पाटलिपुत्र  ", " चंपा  ",
                                " वैशाली  ", " कोशल"],
                    solution_en: "<p>14.(c) <strong>Vaishali</strong> (Bihar). Other Mahajanapadas and their capitals: Anga (Champa), Magadha (Rajagriha), Kasi (Varanasi), Vatsa (Kaushambi), Kosala (Shravasti), Shurasena (Mathura), Panchala (Ahichchhatra and Kampilya), Kuru (Indraprastha), Matsya (Viratanagara), Chedi (Sothivati), Avanti (Ujjaini or Mahishmati), Gandhara (Taxila), Kamboja (Poonch), Asmaka (Potali and Poddana), Malla (Kusinara).</p>",
                    solution_hi: "<p>14.(c) <strong>वैशाली</strong> (बिहार)। अन्य महाजनपद और उनकी राजधानियाँ : अंग (चंपा), मगध (राजगृह), काशी (वाराणसी), वत्स (कौशांबी), कोसल (श्रावस्ती), शूरसेन (मथुरा), पांचाल (अहिच्छत्र और कांपिल्य), कुरु (इंद्रप्रस्थ), मत्स्य (विराटनगर), चेदि (सोथिवती), अवंती (उज्जैनी या महिष्मती), गांधार (तक्षशिला), कम्बोज (पुंछ), अस्मक (पोटाली और पोद्दाना), मल्ल (कुशिनारा)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Who was the main ruler associated with the spread of Jainism in Karnataka?</p>",
                    question_hi: "<p>15. कर्नाटक में जैन धर्म के प्रसार से जुड़ा मुख्य शासक कौन था?</p>",
                    options_en: ["<p>Ashoka</p>", "<p>Chandragupta Maurya</p>", 
                                "<p>Bindusara</p>", "<p>Harshavardhana</p>"],
                    options_hi: ["<p>अशोक</p>", "<p>चंद्रगुप्त मौर्य</p>",
                                "<p>बिंदुसार</p>", "<p>हर्षवर्धन</p>"],
                    solution_en: "<p>15.(b) <strong>Chandragupta Maurya</strong>, the founder of the Maurya Empire, later adopted Jainism and is said to have moved to Shravanabelagola in Karnataka. There, he spent the last years of his life as an ascetic, contributing to the spread of Jainism in the region.</p>",
                    solution_hi: "<p>15.(b)<strong> चंद्रगुप्त मौर्य, </strong>मौर्य साम्राज्य के संस्थापक, बाद में जैन धर्म को अपनाया और कहा जाता है कि वे कर्नाटक के श्रवणबेलगोला चले गए थे। वहां उन्होंने अपने जीवन के अंतिम वर्ष एक तपस्वी के रूप में बिताए और क्षेत्र में जैन धर्म के प्रसार में योगदान दिया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>