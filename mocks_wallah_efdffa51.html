<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">The line chart given below shows the sales of two companies J and K in 5 years.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image1.png\" width=\"272\" height=\"201\"><br><span style=\"font-family: Cambria Math;\">P1 - The difference between the average sales of J in years D and E and the average sales of K in years </span><span style=\"font-family: Cambria Math;\">G and H</span><br><span style=\"font-family: Cambria Math;\">P2 = The value of average sales of K in all years.</span><br><span style=\"font-family: Cambria Math;\">What is the value of (P2 - P1)?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> J </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> K </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image1.png\" width=\"289\" height=\"215\"><br><span style=\"font-family: Cambria Math;\">P1 = </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> E </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> J </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> G </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> H </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> K </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><br><span style=\"font-family: Cambria Math;\">P2 = </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> K </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><br><span style=\"font-family: Nirmala UI;\">&nbsp;<span style=\"font-weight: 400;\">(P2 - P1) </span>&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>520</p>\n", "<p>760</p>\n", 
                                "<p>670</p>\n", "<p>610</p>\n"],
                    options_hi: ["<p>520</p>\n", "<p>760</p>\n",
                                "<p>670</p>\n", "<p>610</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d) </span><br><span style=\"font-family: Cambria Math;\">The difference between the average sales of J in years D and E and the average sales of K in years </span><span style=\"font-family: Cambria Math;\">G and H(P1) = 500 - 350 = 150</span><br><span style=\"font-family: Cambria Math;\">And The value of average sales of K in all years(J2) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3800</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>760</mn></math></span><br><span style=\"font-family: Cambria Math;\">Then, the</span><span style=\"font-family: Cambria Math;\"> value of (P2 - P1) = 760 - 150 = 610</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Cambria Math;\"><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span> D <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span> E <span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span> J <span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span> <span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span> <span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span> <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span> <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span> G <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span> H <span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span> K <span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span> <span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span> <span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span> <span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span> <span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span> <span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span> <span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;</span>(P1) = 500 - 350 = 150</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> K </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> (J2) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3800</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>760</mn></math></span><br><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\">, (P2 - P1) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 760 - 150 = 610</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> The length, width and height of a cuboidal box are 18 cm, 12 cm and 10 cm, respectively. What is the total surface area of the box?</span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2344;&#2366;&#2349;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2348;&#2381;&#2348;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;</span><span style=\"font-family: Cambria Math;\">: 18 cm, 12 cm </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10 cm </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2348;&#2381;&#2348;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>1000 cm&sup2;</p>\n", "<p>1048 cm&sup2;</p>\n", 
                                "<p>1032 cm&sup2;</p>\n", "<p>1120 cm&sup2;</p>\n"],
                    options_hi: ["<p>1000 cm&sup2;</p>\n", "<p>1048 cm&sup2;</p>\n",
                                "<p>1032 cm&sup2;</p>\n", "<p>1120 cm&sup2;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>T</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mo>&nbsp;</mo><mi>s</mi><mi>u</mi><mi>r</mi><mi>f</mi><mi>a</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&nbsp;</mo><mi>c</mi><mi>u</mi><mi>b</mi><mi>o</mi><mi>i</mi><mi>d</mi><mi>a</mi><mi>l</mi><mo>&nbsp;</mo><mi>b</mi><mi>o</mi><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mi>h</mi><mo>&nbsp;</mo><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>2</mn><mo>(</mo><mn>18</mn><mo>&times;</mo><mn>12</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><mo>&times;</mo><mn>10</mn><mo>&nbsp;</mo><mo>+</mo><mn>10</mn><mo>&times;</mo><mn>18</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><mn>516</mn><mo>)</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mn>1032</mn><mo>&nbsp;</mo><mi>c</mi><mi>m</mi><mo>&sup2;</mo></math></span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2328;&#2344;&#2366;&#2349;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2337;&#2367;&#2348;&#2381;&#2348;&#2375;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2325;&#2369;&#2354;</mi><mo>&nbsp;</mo><mi>&#2346;&#2371;&#2359;&#2381;&#2336;&#2368;&#2351;</mi><mo>&nbsp;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><mi>l</mi><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>b</mi><mi>h</mi><mo>&nbsp;</mo><mo>+</mo><mi>h</mi><mi>l</mi><mo>)</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>2</mn><mo>(</mo><mn>18</mn><mo>&times;</mo><mn>12</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><mo>&times;</mo><mn>10</mn><mo>&nbsp;</mo><mo>+</mo><mn>10</mn><mo>&times;</mo><mn>18</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>(</mo><mn>516</mn><mo>)</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mn>1032</mn><mo>&nbsp;</mo><mi>&#2360;&#2375;&#2350;&#2368;</mi><mo>&sup2;</mo></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">The speed of a train including stoppages is 75 kmph and excluding stoppages, the speed of the train is 90 kmph. For how many minutes does the train stop per hour?</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> 75 km/h </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> 90 kmph </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>10 minutes</p>\n", "<p>15 minutes</p>\n", 
                                "<p>20 minutes</p>\n", "<p>11 minutes</p>\n"],
                    options_hi: ["<p>10 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span></p>\n", "<p>15 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span></p>\n",
                                "<p>20 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span></p>\n", "<p>11 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">According to the question,</span><br><span style=\"font-family: Cambria Math;\">Due to stoppage the train covers 15 km less distance in 1 hour.</span><br><span style=\"font-family: Cambria Math;\">Therefore, time taken to cover 15km with the speed of 90km/h</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>15</mn><mn>90</mn></mfrac><mo>&times;</mo><mn>60</mn><mo>&nbsp;</mo><mo>=</mo><mn>10</mn><mo>&nbsp;</mo><mi>m</mi><mi>i</mi><mi>n</mi><mi>u</mi><mi>t</mi><mi>e</mi><mi>s</mi><mo>.</mo><mo>&nbsp;</mo></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span><br><span style=\"font-family: Nirmala UI;\">&#2336;&#2361;&#2352;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 15 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><br><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> 90km/h </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 15km </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>15</mn><mn>90</mn></mfrac><mo>&times;</mo><mn>60</mn><mo>&nbsp;</mo><mo>=</mo><mn>10</mn><mo>&nbsp;</mo><mi>&#2350;&#2367;&#2344;&#2335;</mi></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">PQR, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">P=46&deg; and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">R = 64&deg;. If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">PQR is similar to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">ABC and in correspondence, then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">B ?</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">PQR </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">P=46&deg; </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">R = 64&deg;. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">PQR ,</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">ABC </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>70&deg;</p>\n", "<p>90&deg;</p>\n", 
                                "<p>100&deg;</p>\n", "<p>110&deg;</p>\n"],
                    options_hi: ["<p>70&deg;</p>\n", "<p>90&deg;</p>\n",
                                "<p>100&deg;</p>\n", "<p>110&deg;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>PQR is similar to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">ABC and in correspondence</span><br><span style=\"font-family: Cambria Math;\">So, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">P ,</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">Q and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">C = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">R</span><br><span style=\"font-family: Cambria Math;\">According to the question,</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math>Q = 180 - (46 + 64) = 70&deg;</span><br><span style=\"font-family: Cambria Math;\">Therefore ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">B = 70&deg; </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>PQR, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">ABC </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><br><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &ang;A = &ang;</span><span style=\"font-family: Cambria Math;\">P ,</span><span style=\"font-family: Cambria Math;\"> &ang;B = &ang;Q </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &ang;C = &ang;R</span><br><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span><br><span style=\"font-family: Cambria Math;\">&ang;Q = 180 - (46 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>+</mo></math>64) = 70&deg;</span><br><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, &ang;B = 70&deg;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">The length of the base of a triangle is 10 cm and its altitude from the same base is 8 cm. What is the area of the triangle?</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 10cm </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2368;&#2352;&#2381;&#2359;&#2354;&#2306;&#2348;</span><span style=\"font-family: Cambria Math;\"> 8 cm </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>30 cm&sup2;</p>\n", "<p>40 cm&sup2;</p>\n", 
                                "<p>50 cm&sup2;</p>\n", "<p>80 cm&sup2;</p>\n"],
                    options_hi: ["<p>30 cm&sup2;</p>\n", "<p>40 cm&sup2;</p>\n",
                                "<p>50 cm&sup2;</p>\n", "<p>80 cm&sup2;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">Area of the triangle =&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi><mo>&nbsp;</mo><mo>&times;</mo><mi>h</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>10</mn><mo>&times;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>40</mn><mo>&nbsp;</mo><mi>c</mi><mi>m</mi><mo>&sup2;</mo></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> =<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>&times;</mo><mi>&#2314;&#2305;&#2330;&#2366;&#2312;</mi><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>10</mn><mo>&times;</mo><mn>8</mn><mo>=</mo><mn>40</mn><mo>&nbsp;</mo><mi>&#2360;&#2375;&#2350;&#2368;</mi><mo>&sup2;</mo></math></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">The following bar graph shows the sales of tractors by a showroom in different months. What was the approximate percentage decline from August to September?</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image2.png\" width=\"348\" height=\"228\"><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> (Months) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2379;&#2352;&#2370;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2376;&#2325;&#2381;&#2335;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> (sales) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2327;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2340;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2350;&#2366;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> (Percentage) </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2367;&#2352;&#2366;&#2357;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span><br><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image3.png\" width=\"361\" height=\"275\"></p>\n",
                    options_en: ["<p>33<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>51<span style=\"font-family: Cambria Math;\">%</span></p>\n", 
                                "<p>46<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>55<span style=\"font-family: Cambria Math;\">%</span></p>\n"],
                    options_hi: ["<p>33<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>51<span style=\"font-family: Cambria Math;\">%</span></p>\n",
                                "<p>46<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>55<span style=\"font-family: Cambria Math;\">%</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\">Decline from August to September in </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3475</mn><mo>-</mo><mn>1875</mn></mrow><mn>3475</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1600</mn><mn>3475</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>&asymp;</mo><mo>&nbsp;</mo><mn>46</mn><mo>%</mo></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Nirmala UI;\">&#2309;&#2327;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2340;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> % </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2367;&#2352;&#2366;&#2357;&#2335;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3475</mn><mo>-</mo><mn>1875</mn></mrow><mn>3475</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mfrac><mn>1600</mn><mn>3475</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>&asymp;</mo><mo>&nbsp;</mo><mn>46</mn><mo>%</mo></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">What is the expansion of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>64</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>64</mn></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>64</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>64</mn></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>64</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>64</mn></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>64</mn></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>+</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>64</mn></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mrow><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></mrow></msup><mo>-</mo><mo>&nbsp;</mo><mn>64</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><mi>a</mi><mo>&times;</mo><mn>16</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>64</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mi>a</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>-</mo><mo>&nbsp;</mo><mn>64</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&times;</mo><mi>a</mi><mo>&times;</mo><mn>16</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12</mn><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn><mi>a</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>64</mn></math></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> 2 years </span><span style=\"font-family: Cambria Math;\">ago</span><span style=\"font-family: Cambria Math;\"> the average age of husband, wife and child was 28 years, and 5 years ago the average age of husband and child was 21 years. What is the present age of the wife?</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2381;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> 28 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2381;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> 21 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>38 years</p>\n", "<p>40 years</p>\n", 
                                "<p>36 years</p>\n", "<p>42 years</p>\n"],
                    options_hi: ["<p>38 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>40 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                                "<p>36 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>42 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">Let the current ages </span><span style=\"font-family: Cambria Math;\">of </span><span style=\"font-family: Cambria Math;\"> husband</span><span style=\"font-family: Cambria Math;\">, the wife and the child = </span><span style=\"font-family: Cambria Math;\">x,y</span><span style=\"font-family: Cambria Math;\">, and z respectively</span><br><span style=\"font-family: Cambria Math;\">According to the question,</span><br><span style=\"font-family: Cambria Math;\">x +y +z = 90-----------(1)</span><br><span style=\"font-family: Cambria Math;\">And x +z = 52----------(2)</span><br><span style=\"font-family: Cambria Math;\">By eq1st and 2nd</span><br><span style=\"font-family: Cambria Math;\">Present age of </span><span style=\"font-family: Cambria Math;\">wife( y</span><span style=\"font-family: Cambria Math;\">) = 38 years</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2381;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">x,y</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> z </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><br><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span><br><span style=\"font-family: Cambria Math;\">x +y +z = 90-----------(1)</span><br><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> x +z = 52----------(2)</span><br><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;1st </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2nd </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><br><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> y = 38 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">ABC is a </span><span style=\"font-family: Cambria Math;\">right angled</span><span style=\"font-family: Cambria Math;\"> triangle. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">A = </span><span style=\"font-family: Cambria Math;\">90 degree</span><span style=\"font-family: Cambria Math;\">, AB = 4 cm, BC = 5 cm. What is the value of cos B + cot C?</span></p>\n",
                    question_hi: "<p>9. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math><span style=\"font-family: Cambria Math;\">ABC </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> | <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&ang;</mo></math></span><span style=\"font-family: Cambria Math;\">A = 90&deg;, AB = 4 cm, BC = 5 cm </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> | cos B + cot C </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mn>20</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>20</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>31</mn><mn>20</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image4.png\" width=\"170\" height=\"133\"><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>31</mn></mrow><mn>20</mn></mfrac></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image4.png\" width=\"186\" height=\"145\"><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mi>B</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>cos</mi><mo>&nbsp;</mo><mi>B</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mo>&nbsp;</mo><mi>C</mi><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>3</mn></mrow><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>31</mn></mrow><mn>20</mn></mfrac></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> 20 percent of marked price is equal to 50 percent of cost price. If no discount is given, then what will be the profit percentage?</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 50 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2335;&#2381;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>120<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>150<span style=\"font-family: Cambria Math;\">%</span></p>\n", 
                                "<p>125<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>100%</p>\n"],
                    options_hi: ["<p>120<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n", "<p>150<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n",
                                "<p>125<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n", "<p>100 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>M</mi><mi>P</mi><mo>&times;</mo><mfrac><mn>1</mn><mrow><mn>5</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mi>C</mi><mi>P</mi><mo>&times;</mo><mfrac><mn>1</mn><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&nbsp;</span><br><span style=\"font-family: Cambria Math;\">If no discount is given then, profit % =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo></math> </span><span style=\"font-family: Cambria Math;\"> = 150%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&times;</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>=</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&times;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&rArr;</mo><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>M</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>2</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\">&nbsp;</span><br><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">% = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac><mo>&times;</mo><mn>100</mn></math>= 150%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Which of the following is a value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\">, when </span><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mn>0</mn><mo>?</mo></math></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi><mo>-</mo><mn>2</mn><mo>+</mo><mi>cos</mi><mi>&theta;</mi><mo>=</mo><mn>0</mn></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>60<span style=\"font-family: Cambria Math;\">&deg;</span></p>\n", "<p>90<span style=\"font-family: Cambria Math;\">&deg;</span></p>\n", 
                                "<p>30<span style=\"font-family: Cambria Math;\">&deg;</span></p>\n", "<p>0&deg;</p>\n"],
                    options_hi: ["<p>60<span style=\"font-family: Cambria Math;\">&deg;</span></p>\n", "<p>90<span style=\"font-family: Cambria Math;\">&deg;</span></p>\n",
                                "<p>30<span style=\"font-family: Cambria Math;\">&deg;</span></p>\n", "<p>0<span style=\"font-family: Cambria Math;\">&deg;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Cambria Math;\"> If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math> </span><span style=\"font-family: Cambria Math;\">= 0&deg; </span><span style=\"font-family: Cambria Math;\">then ,</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>&theta;</mi><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math> </span><span style=\"font-family: Cambria Math;\">= 0&deg; </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cos</mi><mn>2</mn></msup><mo>&nbsp;</mo><mi>&theta;</mi><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> If </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>+</mo><mfrac><mn>1</mn><mrow><mi>y</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mn>3</mn></math><span style=\"font-family: Cambria Math;\">,</span><span style=\"font-family: Cambria Math;\"> then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>y</mi><mn>3</mn></msup></mfrac><mo>+</mo><msup><mi>y</mi><mn>3</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    question_hi: "<p>12. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>+</mo><mfrac><mn>1</mn><mrow><mi>y</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>3</mn></math><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>y</mi><mn>3</mn></msup></mfrac><mo>+</mo><msup><mi>y</mi><mn>3</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>12</p>\n", "<p>27</p>\n", 
                                "<p>24</p>\n", "<p>18</p>\n"],
                    options_hi: ["<p>12</p>\n", "<p>27</p>\n",
                                "<p>24</p>\n", "<p>18</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>+</mo><mfrac><mn>1</mn><mrow><mi>y</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mfrac><mn>1</mn><msup><mi>y</mi><mn>3</mn></msup></mfrac><mo>+</mo><msup><mi>y</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>&times;</mo><mn>3</mn><mo>&nbsp;</mo><mo>=</mo><mn>18</mn></math></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mo>+</mo><mfrac><mn>1</mn><mrow><mi>y</mi><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mfrac><mn>1</mn><msup><mi>y</mi><mn>3</mn></msup></mfrac><mo>+</mo><msup><mi>y</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>&times;</mo><mn>3</mn><mo>&nbsp;</mo><mo>=</mo><mn>18</mn></math></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">The table given below shows the saving and expenditure of 5 companies.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image5.png\"><br><span style=\"font-family: Cambria Math;\">Income = Expenditure + Savings</span><br><span style=\"font-family: Cambria Math;\">What is the average income of all 5 companies?</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image6.png\"><br><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><br><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>440</p>\n", "<p>360</p>\n", 
                                "<p>480</p>\n", "<p>390</p>\n"],
                    options_hi: ["<p>440</p>\n", "<p>360</p>\n",
                                "<p>480</p>\n", "<p>390</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">average income of all 5 companies =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2200</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 440</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2200</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 440</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> What is the value of (1665 &divide; 37) + (42 &times; 5</span><span style=\"font-family: Cambria Math;\">) ?</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Cambria Math;\"> (1665 &divide; 37) + (42 &times; 5) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>295</p>\n", "<p>270</p>\n", 
                                "<p>255</p>\n", "<p>285</p>\n"],
                    options_hi: ["<p>295</p>\n", "<p>270</p>\n",
                                "<p>255</p>\n", "<p>285</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>1665</mn><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mn>37</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>42</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>5</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>45</mn><mo>+</mo><mn>210</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>255</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>1665</mn><mo>&nbsp;</mo><mo>&divide;</mo><mo>&nbsp;</mo><mn>37</mn><mo>)</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mo>(</mo><mn>42</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>5</mn><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>45</mn><mo>+</mo><mn>210</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>255</mn></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">A trader sells an article at 20 percent profit. If he buys it at half of the cost price and sells at the same selling price as before, then what will be the profit percentage?</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>240 percent</p>\n", "<p>40 percent</p>\n", 
                                "<p>140 percent</p>\n", "<p>60 percent</p>\n"],
                    options_hi: ["<p>240 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>40 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n",
                                "<p>140 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n", "<p>60 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\">Let the CP of the article = 100</span><br><span style=\"font-family: Cambria Math;\">After 20% profit SP of the article = 120</span><br><span style=\"font-family: Cambria Math;\"> If he buys it at half of the cost price and sells at the same selling price as before,</span><br><span style=\"font-family: Cambria Math;\">Then, new CP = 50 and SP = 120</span><br><span style=\"font-family: Cambria Math;\">Therefore, % profit =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>50</mn></mrow><mn>50</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo></math> </span><span style=\"font-family: Cambria Math;\"> = 140%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 100</span><br><span style=\"font-family: Cambria Math;\">20% </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 120</span><br><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><br><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 50 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 120</span><br><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, % </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>50</mn></mrow><mn>50</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> = 140%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">16. </span><span style=\"font-family: Cambria Math;\">Point D, E and F all lie on the circumference of a circle with </span><span style=\"font-family: Cambria Math;\">centre</span><span style=\"font-family: Cambria Math;\"> O. If angle DEF = 48&deg;, then what is a possible value of angle DOF?</span></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> D, E </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> F, O </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> DEF= 48&deg; </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> DOF </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2349;&#2366;&#2357;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>84&deg;</p>\n", "<p>96&deg;</p>\n", 
                                "<p>90&deg;</p>\n", "<p>48&deg;</p>\n"],
                    options_hi: ["<p>84&deg;</p>\n", "<p>96&deg;</p>\n",
                                "<p>90&deg;</p>\n", "<p>48&deg;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image7.png\" width=\"157\" height=\"161\"><br><span style=\"font-family: Cambria Math;\">Angle made by arc at the </span><span style=\"font-family: Cambria Math;\">the</span><span style=\"font-family: Cambria Math;\"> center is twice to the angle made by same arc at circumference</span><br><span style=\"font-family: Cambria Math;\">Therefore, &ang;</span><span style=\"font-family: Cambria Math;\">DOF = 2</span><span style=\"font-family: Cambria Math;\">&ang;</span><span style=\"font-family: Cambria Math;\">DEF = </span><span style=\"font-family: Cambria Math;\">96&deg;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image7.png\" width=\"146\" height=\"150\"><br><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><br><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> &ang;</span><span style=\"font-family: Cambria Math;\">DOF = 2</span><span style=\"font-family: Cambria Math;\">&ang;</span><span style=\"font-family: Cambria Math;\">DEF = </span><span style=\"font-family: Cambria Math;\">96&deg;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">17. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">MNO is similar to <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">STU. Perimeter of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">MNO and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">STU are 80 cm and 200 cm respectively. If ON = 25 cm, then what is the length of TU?</span></p>\n",
                    question_hi: "<p>17. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math><span style=\"font-family: Cambria Math;\">MNO, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">STU </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> | <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">MNO </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">STU </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> 80 cm </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 200</span><span style=\"font-family: Cambria Math;\"> cm </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> | </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> ON = 25 cm, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> TU </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2350;&#2381;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>59<span style=\"font-family: Cambria Math;\"> cm</span></p>\n", "<p>61<span style=\"font-family: Cambria Math;\"> cm</span></p>\n", 
                                "<p>62.5<span style=\"font-family: Cambria Math;\"> cm</span></p>\n", "<p>60.5 cm</p>\n"],
                    options_hi: ["<p>59<span style=\"font-family: Cambria Math;\"> cm</span></p>\n", "<p>61<span style=\"font-family: Cambria Math;\"> cm</span></p>\n",
                                "<p>62.5<span style=\"font-family: Cambria Math;\"> cm</span></p>\n", "<p>60.5 cm</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Cambria Math;\">According to the question,</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>MNO &#8779; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">STU</span><br><span style=\"font-family: Cambria Math;\">so ,</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mo>&#8710;</mo><mi>M</mi><mi>N</mi><mi>O</mi></mrow><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>t</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mo>&#8710;</mo><mi>S</mi><mi>T</mi><mi>U</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>O</mi><mi>N</mi></mrow><mrow><mo>&nbsp;</mo><mi>T</mi><mi>U</mi></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>80</mn><mn>200</mn></mfrac><mo>=</mo><mfrac><mn>25</mn><mrow><mi>T</mi><mi>U</mi><mo>&nbsp;</mo></mrow></mfrac><mo>&rArr;</mo><mo>&nbsp;</mo><mi>T</mi><mi>U</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>25</mn><mo>&times;</mo><mn>200</mn></mrow><mn>80</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>62</mn><mo>.</mo><mn>5</mn><mo>&nbsp;</mo><mi>c</mi><mi>m</mi></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">c)</span><br><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span><br><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>MNO &#8779; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math></span><span style=\"font-family: Cambria Math;\">STU</span><br><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mo>&nbsp;</mo><mo>&#8710;</mo><mi>M</mi><mi>N</mi><mi>O</mi></mrow><mrow><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&#8710;</mo><mi>S</mi><mi>T</mi><mi>U</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>O</mi><mi>N</mi></mrow><mrow><mo>&nbsp;</mo><mi>T</mi><mi>U</mi></mrow></mfrac><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>80</mn><mn>200</mn></mfrac><mo>=</mo><mfrac><mn>25</mn><mrow><mi>T</mi><mi>U</mi><mo>&nbsp;</mo></mrow></mfrac><mo>&rArr;</mo><mo>&nbsp;</mo><mi>T</mi><mi>U</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>25</mn><mo>&times;</mo><mn>200</mn></mrow><mn>80</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>62</mn><mo>.</mo><mn>5</mn><mo>&nbsp;</mo><mi>&#2360;&#2375;&#2350;&#2368;</mi></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">18. </span><span style=\"font-family: Cambria Math;\">The table given below shows the number of girls in 7 schools.</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image8.png\"><br><span style=\"font-family: Cambria Math;\">What is the total number of girls in all the schools?</span></p>\n",
                    question_hi: "<p>18. <span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2370;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><br><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683194011/word/media/image9.png\"><br><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2370;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>3850</p>\n", "<p>3950</p>\n", 
                                "<p>3750</p>\n", "<p>3620</p>\n"],
                    options_hi: ["<p>3850</p>\n", "<p>3950</p>\n",
                                "<p>3750</p>\n", "<p>3620</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">Total number of girls in the school</span><br><span style=\"font-family: Cambria Math;\">= (450 +1050 + 550 + 850 + 650 + 250 + 150) = </span><span style=\"font-family: Cambria Math;\">3950</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2337;&#2364;&#2325;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><br><span style=\"font-family: Cambria Math;\">= (450 +1050 + 550 + 850 + 650 + 250 + 150) = </span><span style=\"font-family: Cambria Math;\">3950</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Cambria Math;\">The population of a town increased from 100000 to 250000 in 5 years. What is the approximate compounded percent increase of population per year?</span></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2348;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 100000 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 250000 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2348;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2350;&#2366;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>20 percent</p>\n", "<p>30 percent</p>\n", 
                                "<p>10 percent</p>\n", "<p>40 percent</p>\n"],
                    options_hi: ["<p>20 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n", "<p>30 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n",
                                "<p>10 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n", "<p>40 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">Percentage increase of population in 5 years = 250000 = 100000&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mi>R</mi><mn>100</mn></mfrac><mo>)</mo></mrow><mn>5</mn></msup></math></span><br><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&rArr;</mo><mfrac><mn>250000</mn><mrow><mn>100000</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>+</mo><mi>R</mi></mrow><mn>100</mn></mfrac><mo>)</mo></mrow><mrow><mn>5</mn><mo>&nbsp;</mo></mrow></msup><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\">20%</span><br><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Short </span><span style=\"font-family: Cambria Math;\">trick :</span></strong><br><span style=\"font-family: Cambria Math;\">Overall increases in % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>250000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>100000</mn></mrow><mn>100000</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&times; 100 = 150%</span><br><span style=\"font-family: Cambria Math;\">In this type of </span><span style=\"font-family: Cambria Math;\">question</span><span style=\"font-family: Cambria Math;\"> we use options. As we know, the total increase is 150%.</span><br><span style=\"font-family: Cambria Math;\">Option (</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">20% = 20 &times;5 = 100% (more than)</span><br><span style=\"font-family: Cambria Math;\">Option (</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">30% = 30&times; 5 = 150% (more than )</span><br><span style=\"font-family: Cambria Math;\">Option (c</span><span style=\"font-family: Cambria Math;\">) 10</span><span style=\"font-family: Cambria Math;\">% = 10 &times; 5 = 50% (more than )</span><br><span style=\"font-family: Cambria Math;\">Option (d</span><span style=\"font-family: Cambria Math;\">) 40</span><span style=\"font-family: Cambria Math;\">% = 40 &times; 5 = 200% (more than)</span><br><span style=\"font-family: Cambria Math;\">So</span><span style=\"font-family: Cambria Math;\"> we can see. Option (a) is correct. </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">69.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">5 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> = 250000 = 100000&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mfrac><mi>R</mi><mn>100</mn></mfrac><mo>)</mo></mrow><mn>5</mn></msup></math></span><br><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&rArr;</mo><mfrac><mn>250000</mn><mrow><mn>100000</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>+</mo><mi>R</mi></mrow><mn>100</mn></mfrac><mo>)</mo></mrow><mrow><mn>5</mn><mo>&nbsp;</mo></mrow></msup><mo>&rArr;</mo><mn>20</mn><mo>%</mo></math><br><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2358;&#2377;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2367;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> :</span></strong><br><span style=\"font-family: Cambria Math;\"> % </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>250000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>100000</mn></mrow><mn>100000</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&times; 100 = 150%</span><br><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2370;&#2305;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;&#2370;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">150% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><br><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Cambria Math;\">20% = 20 &times;5 = 100% (</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2351;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><br><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Cambria Math;\">30% = 30&times; 5 = 150% (</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2351;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> )</span><br><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (c</span><span style=\"font-family: Cambria Math;\">) 10</span><span style=\"font-family: Cambria Math;\">% = 10 &times; 5 = 50% (</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2351;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> )</span><br><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (d</span><span style=\"font-family: Cambria Math;\">) 40</span><span style=\"font-family: Cambria Math;\">% = 40 &times; 5 = 200% (</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2351;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><br><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (a) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Cambria Math;\">What is the value of x?</span><br><span style=\"font-family: Cambria Math;\">If x = 0.05 </span><span style=\"font-family: Cambria Math;\">&times; 0.36</span><span style=\"font-family: Cambria Math;\"> &divide; 0.4 + 0.055 + 1.50 &divide; 0.03</span></p>\n",
                    question_hi: "<p>20. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> x = 0.05 </span><span style=\"font-family: Cambria Math;\">&times; 0.36</span><span style=\"font-family: Cambria Math;\"> &divide; 0.4 + 0.055 + 1.50 &divide; 0.03 , x <span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span> <span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span> <span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span> <span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span> ?</span></p>\n",
                    options_en: ["<p>45.9</p>\n", "<p>50.1</p>\n", 
                                "<p>55.1</p>\n", "<p>49.9</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>45.9</p>\n", "<p>50.1</p>\n",
                                "<p>55.1</p>\n", "<p>49.9</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">x = 0.05 </span><span style=\"font-family: Cambria Math;\">&times; 0.36</span><span style=\"font-family: Cambria Math;\"> &divide; 0.4 + 0.055 + 1.50 &divide; 0.03<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math> </span><span style=\"font-family: Cambria Math;\"> 0.05 &times; 0.9 + 0.055 + 50</span><br><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>0.045 +50.055 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\"> 50.1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><span style=\"font-family: Cambria Math;\">x = 0.05 </span><span style=\"font-family: Cambria Math;\">&times; 0.36</span><span style=\"font-family: Cambria Math;\"> &divide; 0.4 + 0.055 + 1.50 &divide; 0.03<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math> </span><span style=\"font-family: Cambria Math;\"> 0.05 &times; 0.9 + 0.055 + 50</span><br><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>0.045 +50.055 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Cambria Math;\"> 50.1</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21. </span><span style=\"font-family: Cambria Math;\">If </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&alpha;</mi><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>3</mn><mo>&nbsp;</mo></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\">and </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&beta;</mi><mo>=</mo><msqrt><mn>3</mn><mo>&nbsp;</mo></msqrt></math><span style=\"font-family: Cambria Math;\">then what is the value of </span><span style=\"font-family: Cambria Math;\">cos(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&alpha;</mi><mo>+</mo><mi>&beta;</mi></math></span><span style=\"font-family: Cambria Math;\">) ? </span></p>\n",
                    question_hi: "<p>21. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&alpha;</mi><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>3</mn><mo>&nbsp;</mo></msqrt></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>tan</mi><mi>&beta;</mi><mo>=</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>cos</mi><mo>(</mo><mi>&alpha;</mi><mo>+</mo><mi>&beta;</mi><mo>)</mo><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p>0.2</p>\n", "<p>0</p>\n", 
                                "<p>0.5</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>0.2</p>\n", "<p>0</p>\n",
                                "<p>0.5</p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&alpha;</mi><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>3</mn><mo>&nbsp;</mo></msqrt></mfrac><mo>&rArr;</mo><mo>&nbsp;</mo><mi>&alpha;</mi><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>tan</mi><mi>&beta;</mi><mo>=</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>&rArr;</mo><mi>&beta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>f</mi><mi>o</mi><mi>r</mi><mi>e</mi><mo>,</mo><mo>&nbsp;</mo><mi>cos</mi><mo>(</mo><mi>&alpha;</mi><mo>+</mo><mi>&beta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>cos</mi><mo>(</mo><mn>90</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mn>0</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">b)</span><br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>tan</mi><mi>&alpha;</mi><mo>=</mo><mfrac><mn>1</mn><msqrt><mn>3</mn><mo>&nbsp;</mo></msqrt></mfrac><mo>&rArr;</mo><mo>&nbsp;</mo><mi>&alpha;</mi><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&deg;</mo><mo>&nbsp;</mo><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mi>tan</mi><mi>&beta;</mi><mo>=</mo><msqrt><mn>3</mn></msqrt><mo>&nbsp;</mo><mo>&rArr;</mo><mi>&beta;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mo>&deg;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>cos</mi><mo>(</mo><mi>&alpha;</mi><mo>+</mo><mi>&beta;</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>cos</mi><mo>(</mo><mn>90</mn><mo>&deg;</mo><mo>)</mo><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mn>0</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Cambria Math;\">50% of A is equal to 80% of B. What is the ratio of A and B?</span></p>\n",
                    question_hi: "<p>22. A <span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">50</span><span style=\"font-family: Cambria Math;\">% , B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 80% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> | A </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>5<span style=\"font-family: Cambria Math;\"> : 8</span></p>\n", "<p>5<span style=\"font-family: Cambria Math;\"> : 4</span></p>\n", 
                                "<p>4<span style=\"font-family: Cambria Math;\"> : 5</span></p>\n", "<p>8<span style=\"font-family: Cambria Math;\"> : 5</span></p>\n"],
                    options_hi: ["<p>5<span style=\"font-family: Cambria Math;\"> : 8</span></p>\n", "<p>5<span style=\"font-family: Cambria Math;\"> : 4</span></p>\n",
                                "<p>4<span style=\"font-family: Cambria Math;\"> : 5</span></p>\n", "<p>8<span style=\"font-family: Cambria Math;\"> : 5</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn></mrow><mn>5</mn></mfrac><mi>B</mi><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mi>B</mi><mo>=</mo><mn>8</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>5</mn></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mi>A</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn></mrow><mn>5</mn></mfrac><mi>B</mi><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><mi>A</mi><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mi>B</mi><mo>=</mo><mn>8</mn><mo>&nbsp;</mo><mo>:</mo><mo>&nbsp;</mo><mn>5</mn></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23. </span><span style=\"font-family: Cambria Math;\">If <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>=</mo><mn>2</mn></math></span><span style=\"font-family: Cambria Math;\"> and </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><mi>b</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>-</mo><mn>2</mn><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> then what is the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo></math> ?<br></span></p>\n",
                    question_hi: "<p>23. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>=</mo><mn>2</mn><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>b</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>-</mo><mn>2</mn><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup></mfrac><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>0</p>\n", "<p>2</p>\n", 
                                "<p>8</p>\n", "<p>4</p>\n"],
                    options_hi: ["<p>0</p>\n", "<p>2</p>\n",
                                "<p>8</p>\n", "<p>4</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>=</mo><mn>2</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>A</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>b</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>-</mo><mn>2</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mrow><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>T</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mi>f</mi><mi>o</mi><mi>r</mi><mi>e</mi><mo>,</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup></mfrac><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">d)</span><br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>=</mo><mn>2</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mi>b</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mi>b</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>-</mo><mn>2</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mrow><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2354;&#2367;&#2319;</mi><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><msup><mi>a</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>a</mi><mrow><mn>2</mn><mo>&nbsp;</mo></mrow></msup></mfrac><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><msup><mi>b</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>4</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24.</span><span style=\"font-family: Cambria Math;\"> A can do a certain work in 9 days. B is 80% more efficient than A. How many days will B and A together take to do the same work?</span></p>\n",
                    question_hi: "<p>24.<span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> B, A </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 80% </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2358;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>14</mn></mfrac></math>days</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>13</mn></mfrac></math>days</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mn>14</mn></mfrac></math>days</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>13</mn></mfrac></math>days</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>14</mn></mfrac></math> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>54</mn><mn>13</mn></mfrac></math>&#2342;&#2367;&#2344;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>47</mn><mn>14</mn></mfrac></math> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>13</mn></mfrac></math> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">A can complete the work in <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rarr;</mo></math></span><span style=\"font-family: Cambria Math;\"> 9days</span><br><span style=\"font-family: Cambria Math;\">B is 80% more efficient than A</span><br><span style=\"font-family: Cambria Math;\">Then, ratio of efficiency&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mi>A</mi><mi>B</mi></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>9</mn></mfrac></math></span><br><span style=\"font-family: Cambria Math;\">Total work = 9&times;5 = 45 unit</span><br><span style=\"font-family: Cambria Math;\">Therefore, A and B complete the work = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>5</mn><mo>&nbsp;</mo><mo>+</mo><mn>9</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>45</mn></mrow><mn>14</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">days </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">a)</span><br><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><br><span style=\"font-family: Cambria Math;\">B, A </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 80% </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2358;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><br><span style=\"font-family: Nirmala UI;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mfrac><mi>A</mi><mi>B</mi></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>9</mn></mfrac></math></span><br><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> = 9&times;5 = 45 &#2311;&#2325;&#2366;&#2312;&nbsp;</span><br><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, A </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><br><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mrow><mn>5</mn><mo>&nbsp;</mo><mo>+</mo><mn>9</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>45</mn></mrow><mn>14</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-weight: 400;\"> 25. A sum of Rs.2,000 becomes Rs.3,120 in two years at a certain rate of simple interest. What is the rate of the interest per annum?</span></p>\n",
                    question_hi: "<p><span style=\"font-weight: 400;\">25. &#2360;&#2366;&#2343;&#2366;&#2352;&#2339; &#2348;&#2381;&#2351;&#2366;&#2332; &#2325;&#2368; &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2342;&#2352; &#2346;&#2352; &#2342;&#2379; &#2360;&#2366;&#2354; &#2350;&#2375;&#2306; Rs.2,000 &#2325;&#2368; &#2352;&#2366;&#2358;&#2367; Rs. 3,120 &#2361;&#2379; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404; &#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2357;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352; &#2325;&#2367;&#2340;&#2344;&#2368; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">34% per annum</span></p>\n", "<p><span style=\"font-weight: 400;\">33% per annum</span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">30% per annum</span></p>\n", "<p><span style=\"font-weight: 400;\">28% per annum</span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;34%&nbsp;</span><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2340;&#2367; &#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p><span style=\"font-weight: 400;\">33%&nbsp;</span><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2340;&#2367; &#2357;&#2352;&#2381;&#2359;</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;30%&nbsp;</span><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2340;&#2367; &#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p><span style=\"font-weight: 400;\">28%&nbsp;</span><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2340;&#2367; &#2357;&#2352;&#2381;&#2359;</span></p>\n"],
                    solution_en: "<p>25.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>r</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>%</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1120</mn><mo>&times;</mo><mn>100</mn></mrow><mrow><mn>2000</mn><mo>&times;</mo><mn>2</mn></mrow></mfrac><mo>=</mo><mn>28</mn><mo>%</mo></math></p>\n",
                    solution_hi: "<p>25.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2342;&#2352;</mi><mo>&nbsp;</mo><mo>%</mo><mo>=</mo><mfrac><mrow><mn>1120</mn><mo>&times;</mo><mn>100</mn></mrow><mrow><mn>2000</mn><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo></mrow></mfrac><mo>=</mo><mn>28</mn><mo>%</mo></math></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>