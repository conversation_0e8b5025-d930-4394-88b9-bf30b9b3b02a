<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The product of two numbers is 20000. If their LCM is 800, then what is their HCF ?</p>",
                    question_hi: "<p>1. दो संख्याओं का गुणनफल 20000 है। यदि उनका लघुत्तम समापवर्त्य (LCM) 800 है, तो उनका महत्तम समापवर्तक (HCF) क्या है ?</p>",
                    options_en: ["<p>20</p>", "<p>30</p>", 
                                "<p>25</p>", "<p>35</p>"],
                    options_hi: ["<p>20</p>", "<p>30</p>",
                                "<p>25</p>", "<p>35</p>"],
                    solution_en: "<p>1.(c)<br>H.C.F = <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">p</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">d</mi><mi mathvariant=\"bold-italic\">u</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">f</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">u</mi><mi mathvariant=\"bold-italic\">m</mi><mi mathvariant=\"bold-italic\">b</mi><mi mathvariant=\"bold-italic\">e</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">s</mi></mrow><mrow><mi mathvariant=\"bold-italic\">L</mi><mo>.</mo><mi mathvariant=\"bold-italic\">C</mi><mo>.</mo><mi mathvariant=\"bold-italic\">M</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20000</mn><mn>800</mn></mfrac><mo>&#160;</mo></math>= 25</p>",
                    solution_hi: "<p>1.(c)<br>महत्तम समापवर्त्य = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2369;&#2339;&#2344;&#2347;&#2354;</mi></mrow><mrow><mi>&#2354;&#2328;&#2369;&#2340;&#2350;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2366;&#2346;&#2357;&#2352;&#2381;&#2340;&#2381;&#2351;</mi><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20000</mn><mn>800</mn></mfrac><mo>&#160;</mo></math>= 25</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Let the HCF of m and n be \'a\' and let n = ab, LCM of m and n is given by:</p>",
                    question_hi: "<p>2. मान लीजिए कि m और n का HCF \'a\' है और n = ab है, तो m और n का LCM ज्ञात कीजिये I</p>",
                    options_en: ["<p>ab</p>", "<p>am</p>", 
                                "<p>bm</p>", "<p>mn</p>"],
                    options_hi: ["<p>ab</p>", "<p>am</p>",
                                "<p>bm</p>", "<p>mn</p>"],
                    solution_en: "<p>2.(c)<br>H.C.F of m and n = a<br>Given, n = ab<br>L.C.M = <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>r</mi><mi>o</mi><mi>d</mi><mi>u</mi><mi>c</mi><mi>t</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi></mrow><mrow><mi>H</mi><mo>.</mo><mi>C</mi><mo>.</mo><mi>F</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>m</mi><mo>&#215;</mo><mi>a</mi><mi>b</mi></mrow><mi>a</mi></mfrac></math> = mb</p>",
                    solution_hi: "<p>2.(c)<br>m और n का H.C.F = a<br>दिया हुआ है , n = ab<br>L.C.M = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2369;&#2339;&#2344;&#2347;&#2354;</mi><mi>&#160;</mi></mrow><mrow><mi>H</mi><mo>.</mo><mi>C</mi><mo>.</mo><mi>F</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>m</mi><mo>&#215;</mo><mi>a</mi><mi>b</mi></mrow><mi>a</mi></mfrac></math> = mb</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. Two numbers are in the ratio 12 : 7. If their HCF is 25, find the numbers.",
                    question_hi: "<p>3. दो संख्याएं 12 : 7 के अनुपात में हैं। यदि उनका महत्तम समापवर्तक (HCF) 25 है, तो संख्याएं ज्ञात कीजिए ।</p>",
                    options_en: [" 300, 50", "<p>225, 135</p>", 
                                "<p>300, 175</p>", "<p>175, 120</p>"],
                    options_hi: ["<p>300, 50</p>", "<p>225, 135</p>",
                                "<p>300, 175</p>", "<p>175, 120</p>"],
                    solution_en: "<p>3.(c)<br>Ratio of numbers = 12 : 7<br>H.C.F = 25 (given)<br>Numbers = 25 &times; 12 , 7 &times; 25 = 300 and 175</p>",
                    solution_hi: "<p>3.(c)<br>संख्याओं का अनुपात = 12 : 7<br>H.C.F = 25 (दिया गया)<br>संख्याएँ = 25 &times; 12 , 7 &times; 25 = 300 और 175</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The LCM and HCF of two numbers are 1105 and 5. If the LCM is 17 times the first number, then find the two numbers.</p>",
                    question_hi: "<p>4. दो संख्याओं का लघुत्तम समापवर्त्य (LCM) और महत्तम समापवर्तक (HCF) 1105 और 5 है। यदि लघुत्तम समापवर्त्य (LCM) पहली संख्या का 17 गुना है, तो दोनों संख्याएं ज्ञात कीजिए।</p>",
                    options_en: ["<p>55 and 85</p>", "<p>65 and 75</p>", 
                                "<p>60 and 80</p>", "<p>65 and 85</p>"],
                    options_hi: ["<p>55 और 85</p>", "<p>65 और 75</p>",
                                "<p>60 और 80</p>", "<p>65 और 85</p>"],
                    solution_en: "<p>4.(d)<br>According to the question,<br>1105 = 17 &times; 1st number<br>1st number = 65<br>Formula:- 1st no. &times; 2nd no. = LCM &times; HCF<br>65 &times; 2nd no. = 1105 &times; 5<br>2nd no. = 85<br>So the no. will be 65 and 85</p>",
                    solution_hi: "<p>4.(d)<br>प्रश्न के अनुसार,<br>1105 = 17 &times; पहली संख्या<br>पहली संख्या = 65<br>सूत्र :- पहली संख्या &times; दूसरी संख्या = लघुत्तम समापवर्त्य (LCM) &times; महत्तम समापवर्तक (HCF) <br>65 &times; दूसरी संख्या = 1105 &times; 5<br>दूसरी संख्या = 85<br>तो संख्याएँ 65 और 85 होंगी</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. Find the HCF of 240, 280 and 560.",
                    question_hi: "5. 240, 280 और 560 का महत्तम समापवर्तक (HCF) ज्ञात कीजिए।",
                    options_en: [" 40", " 30", 
                                " 20", " 10"],
                    options_hi: [" 40", " 30",
                                " 20", " 10"],
                    solution_en: "5.(a)<br />According to the question,<br />240 = 40 × 6<br />280 = 40 × 7<br />560 = 40 × 14<br />So the HCF of 240, 280 and 560 will be 40",
                    solution_hi: "5.(a)<br />प्रश्न के अनुसार,<br />240 = 40 × 6<br />280 = 40 × 7<br />560 = 40 × 14<br />इसलिए  240, 280 और 560 का HCF 40 होगा",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "6. Which is the smallest natural number that is exactly divisible by each of 96, 108 and 144 ?",
                    question_hi: "6. वह छोटी से छोटी प्राकृत संख्या कौन-सी है जो 96, 108 और 144 में से प्रत्येक से पूरी तरह से विभाज्य है ?",
                    options_en: [" 1728", " 864", 
                                " 1296", " 2592"],
                    options_hi: [" 1728", " 864",
                                " 1296", " 2592"],
                    solution_en: "6.(b)<br />According to the question,<br />L.C.M. of 96, 108, 144 = 864<br />So the required number be 864",
                    solution_hi: "6.(b)<br />प्रश्न के अनुसार,<br />96, 108, 144 का L.C.M = 864<br />अतः अभीष्ट संख्या 864 है",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "7. The HCF of 3888 and 3969 is:",
                    question_hi: "7. 3888 और 3969 का महत्तम समापवर्तक (HCF) ज्ञात कीजिए ।",
                    options_en: [" 81", " 73", 
                                " 83", " 71"],
                    options_hi: [" 81", " 73",
                                " 83", " 71"],
                    solution_en: "7.(a)<br />According to the question,<br />3888 = 81 × 12 × 4<br />3969 = 81 × 7 × 7<br />So the HCF of  3888 and 3969 will be 81",
                    solution_hi: "7.(a)<br />प्रश्न के अनुसार,<br />3888 = 81 × 12 × 4<br />3969 = 81 × 7 × 7<br />इसलिए 3888 और 3969 का  महत्तम समापवर्तक (HCF) 81 होगा",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "8. A person has three iron bars whose lengths are 20, 30 and 40 metres, respectively. He wants to cut pieces of the same length from each of the three bars. What is the least number of total pieces if he cuts without any wastage ?",
                    question_hi: "<p>8. एक व्यक्ति के पास लोहे की तीन छड़ें हैं, जिनकी लंबाइयां क्रमशः 20, 30 और 40 मीटर हैं। वह तीनों छड़ों में से प्रत्येक से समान लंबाई के टुकड़े काटना चाहता है। यदि वह बिना किसी अपव्यय के टुकड़े काटता है, तो कुल टुकड़ों की न्यूनतम संख्या कितनी है ?</p>",
                    options_en: [" 9", "<p>10</p>", 
                                "<p>8</p>", "<p>11</p>"],
                    options_hi: ["<p>9</p>", "<p>10</p>",
                                "<p>8</p>", "<p>11</p>"],
                    solution_en: "<p>8.(a)<br>According to the question,<br>HCF of 20, 30 and 40 will be 10<br>So the required no. of pieces will be = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>10</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>10</mn></mfrac></math>= 9</p>",
                    solution_hi: "<p>8.(a)<br>प्रश्न के अनुसार,<br>20, 30 और 40 का HCF 10 होगा<br>तो टुकड़ों की आवश्यक संख्या होगी = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>10</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>10</mn></mfrac></math>= 9</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "9. The LCM of two numbers is 660 and their HCF is 5. If one of the numbers is 55, find the other.",
                    question_hi: "9. दो संख्याओं का लघुत्तम समापवर्त्य (LCM) 660 है, और महत्तम समापवर्तक (HCF) 5 है। यदि इनमें से एक संख्या 55 है, तो दूसरी संख्या ज्ञात कीजिए।",
                    options_en: [" 12", " 275", 
                                " 110", " 60"],
                    options_hi: [" 12", " 275",
                                " 110", " 60"],
                    solution_en: "9.(d)<br />According to the question,<br />Formula : - LCM × HCF = 1st no. × 2nd no.<br />660 × 5 = 55 × 2nd no.<br />2nd no. = 60",
                    solution_hi: "9.(d)<br />प्रश्न के अनुसार,<br />सूत्र :-  LCM × HCF = पहली संख्या × दूसरी संख्या<br />660 × 5 = 55 × दूसरी संख्या<br />दूसरी संख्या = 60",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10. Which of the following is the greatest four-digit number that is divisible by 15, 25, 40, and 75 ?",
                    question_hi: "10. चार अंकों की वह बड़ी से बड़ी संख्या ज्ञात कीजिए, जो 15, 25, 40 और 75 से विभाज्य है।",
                    options_en: [" 9000", " 9600", 
                                " 9500", " 9200"],
                    options_hi: [" 9000", " 9600",
                                " 9500", " 9200"],
                    solution_en: "10.(b)<br />Greatest 4 digit number = 9999<br />LCM of 15,25,40 and 75 = 600<br /><math display=\"inline\"><mfrac><mrow><mn>9999</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> → Remainder = 399<br />So, largest 4 - digit number divisible by 15, 25, 40, 75 <math display=\"inline\"><mo>→</mo></math> 9999 - 399 = 9600",
                    solution_hi: "10.(b)<br />सबसे बड़ी 4 अंकीय संख्या = 9999<br />15,25,40 और 75 का LCM = 600<br /><math display=\"inline\"><mfrac><mrow><mn>9999</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> → शेष = 399<br />तो, 15, 25, 40, 75 से विभाज्य सबसे बड़ी 4 अंकीय संख्या <math display=\"inline\"><mo>→</mo></math> 9999 - 399 = 9600",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "11. Find the least number which when divided by 4, 9, 12 and 15, leaves the remainder 3 in each case.",
                    question_hi: "11. वह छोटी से छोटी संख्या ज्ञात कीजिए, जिसे 4, 9, 12 और 15 से विभाजित करने पर प्रत्येक स्थिति में शेषफल 3 प्राप्त होता है।",
                    options_en: [" 193", " 183", 
                                " 360", " 180"],
                    options_hi: [" 193", " 183",
                                " 360", " 180"],
                    solution_en: "11.(b)<br />LCM(4,9,12,15) = 180<br />hence , least number = 180 + 3 = 183",
                    solution_hi: "11.(b)<br />LCM(4,9,12,15) = 180<br />अतः, सबसे छोटी संख्या = 180 + 3 = 183",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "12. Determine the LCM of two numbers if their HCF is 12 and their ratio is 13 : 15.",
                    question_hi: "<p>12. यदि दो संख्याओं का महत्तम समापवर्तक (HCF) 12 है, और उनका अनुपात 13 :15 है, तो उन संख्याओं का लघुत्तम समापवर्त्य (LCM) ज्ञात कीजिए।</p>",
                    options_en: [" 2450", " 1780", 
                                " 1890", " 2340"],
                    options_hi: ["<p>2450</p>", "<p>1780</p>",
                                "<p>1890</p>", "<p>2340</p>"],
                    solution_en: "<p>12.(d)<br>Let numbers be 13x&nbsp;and 15x respectively,<br>Then , HCF(13x,15x) = x<br>According to the question, <br><math display=\"inline\"><mi>x</mi></math> = 12<br>So, Numbers = 156 and 180<br>Hence, LCM(156 , 180) = 2340</p>",
                    solution_hi: "<p>12.(d)<br>माना संख्याएँ क्रमशः 13<math display=\"inline\"><mi>x</mi></math> और 15x हैं,<br>फिर, HCF(13<math display=\"inline\"><mi>x</mi></math>,15x) = x<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mi>x</mi></math> = 12<br>तो, संख्याएँ = 156 और 180<br>अतः, ल.स.प(156 , 180) = 2340</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "13. The least common multiple of 12, 18 and 27 is :",
                    question_hi: "13. 12, 18 और 27 का लघुत्तम समापवर्त्य क्या है ?",
                    options_en: [" 216", " 54", 
                                " 27", " 108"],
                    options_hi: [" 216", " 54",
                                " 27", " 108"],
                    solution_en: "13.(d)<br />12 = 2 × 2 × 3<br />18 = 2 × 3 × 3<br />27 = 3 × 3 × 3<br />So, required multiple = LCM(12,18,27) = 108",
                    solution_hi: "13.(d)<br />12 = 2 × 2 × 3<br />18 = 2 × 3 × 3<br />27 = 3 × 3 × 3<br />तो, आवश्यक गुणज = LCM(12,18,27) = 108",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "14. What is the smallest perfect square number which is completely divisible by 4, 6, 9, 12  and 15 ?",
                    question_hi: "<p>14. वह छोटी से छोटी पूर्ण वर्ग संख्या कौन-सी है, जो 4, 6, 9, 12 और 15 से पूर्णतः विभाज्य है?</p>",
                    options_en: ["<p>900</p>", "<p>961</p>", 
                                "<p>784</p>", "<p>841</p>"],
                    options_hi: ["<p>900</p>", "<p>961</p>",
                                "<p>784</p>", "<p>841</p>"],
                    solution_en: "<p>14.(a)<br>LCM(4,6,9,12,15) = 180<br>180 = <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<br>Here, 5 has not its pair <br>So, required number = 180 &times; 5 = 900</p>",
                    solution_hi: "<p>14.(a)<br>LCM(4,6,9,12,15) = 180<br>180 = <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<br>यहाँ, 5 का जोड़ा नहीं है<br>अतः, अभीष्ट संख्या = 180 &times; 5 = 900</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "15. Which of the following numbers leaves the remainder equal to the highest common factor of 6, 8 and 9, when divided by 6, 8 and 9 ?",
                    question_hi: "15. निम्नलिखित में से किस संख्या को 6, 8 और 9 से विभाजित किए जाने पर 6, 8 और 9 के महत्तम समापवर्तक के बराबर शेषफल प्राप्त होता है ?",
                    options_en: [" 506", " 575", 
                                " 291", " 433"],
                    options_hi: [" 506", " 575",
                                " 291", " 433"],
                    solution_en: "15.(d)<br />Remainder = HCF(6,8,9) = 1<br />And LCM(6,8,9) = 72<br />So, number should be = 72k + 1<br />After putting k = 6 we get;<br />Number = 72 × 6 + 1 = 433",
                    solution_hi: "15.(d)<br />शेषफल = HCF(6,8,9) = 1<br />और LCM(6,8,9) = 72<br />तो, संख्या = 72k +1 होनी चाहिए<br />k = 6 रखने पर हमें प्राप्त होती है;<br />संख्या = 72 × 6 + 1 = 433",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>