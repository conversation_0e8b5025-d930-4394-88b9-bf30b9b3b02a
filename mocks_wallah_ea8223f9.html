<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which ruler wanted to build the Alai Minar near the Qutub Minar, but could not complete it?</p>",
                    question_hi: "<p>1. कौन सा शासक कुतुब मीनार के पास अलाई मीनार बनाना चाहता था, लेकिन उसे पूरा नहीं कर सका?</p>",
                    options_en: ["<p>Ala-ud-Din Khilji</p>", "<p>Nasir-ud-Din Mahmud</p>", 
                                "<p>Iltutmish</p>", "<p>Raziya Sultan</p>"],
                    options_hi: ["<p>अलाउद्दीन खिलजी</p>", "<p>नसीर-उद-दीन महमूद</p>",
                                "<p>इल्तुतमिश</p>", "<p>रजिया सुल्ताना</p>"],
                    solution_en: "<p>1.(a) <strong>Ala-ud-Din Khilji</strong>(1296-1316 A.D.). He was the first Sultan who attacked South India (Malik Kafur - General). He introduced a permanent standing army. He was the first sultan of Delhi who ordered the measurement of land to collect land revenue in cash. <strong>Famous monuments</strong> (built by Alauddin Khilji) - Alai Darwaza, Siri Fort, Hauz Khas, and Zamat Khana Masjid in Delhi. Qutub minar was built by three Sultans &ndash; Qutbuddin Aibak, Iltutmish and Firuz Shah Tughluq .</p>",
                    solution_hi: "<p>1.(a) <strong>अलाउद्दीन खिलजी </strong>(1296-1316 ई.)। वह प्रथम सुल्तान था जिसने दक्षिण भारत पर आक्रमण किया (मलिक काफ़ूर - जनरल)। उन्होंने एक स्थायी सेना की शुरुआत की। वह दिल्ली का प्रथम सुल्तान था जिसने नकद में भू-राजस्व एकत्र करने के लिए भूमि की माप का आदेश दिया था। <strong>प्रसिद्ध स्मारक</strong> (अलाउद्दीन खिलजी द्वारा निर्मित) - दिल्ली में अलाई दरवाजा, सिरी का किला, हौज़ खास और ज़मात खाना मस्जिद। कुतुब मीनार का निर्माण तीन सुल्तानों - कुतुबुद्दीन ऐबक, इल्तुतमिश और फ़िरोज़ शाह तुगलक ने करवाया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Who established Sher-i-Adl, an exclusive market in Delhi for manufactured and exported goods to be sold at prices fixed by administration?</p>",
                    question_hi: "<p>2. प्रशासन द्वारा निर्धारित कीमतों पर बेचे जाने वाले निर्मित और निर्यात किए गए सामानों के लिए दिल्ली में एक विशेष बाजार शेर-ए-अदल की स्थापना किसने की?</p>",
                    options_en: ["<p>Qutbuddin Mubarak Shah</p>", "<p>Muhammad Bin Tughlaq</p>", 
                                "<p>Alauddin Khalji</p>", "<p>Shershah Suri</p>"],
                    options_hi: ["<p>कुतुबुद्दीन मुबारक शाह</p>", "<p>मोहम्मद बिन तुगलक</p>",
                                "<p>अलाउद्दीन खिलजी</p>", "<p>शेरशाह सूरी</p>"],
                    solution_en: "<p>2.(c) <strong>Alauddin Khalji . </strong>He is known for the reforms in revenue and price policies. <strong>Administrative Reforms -</strong> He was the first sultan to have a permanent army, introduced the system of Dagh (the branding of the horse) and Chehra (descriptive role of soldiers).<strong> Economic Reforms -</strong> Alauddin controlled the market with many regulations. Fixed the cost of all commodities. He established the market control department under a minister called<strong> Diwan-i-Riya sat.</strong></p>",
                    solution_hi: "<p>2.(c) <strong>अलाउद्दीन खिलजी।</strong> इसे राजस्व एवं मूल्य नीतियों में सुधार के लिए जाना जाता है। <strong>प्रशासनिक सुधार - </strong>यह पहला सुल्तान था जिसके पास स्थायी सेना थी, उसने दाग प्रथा और चेहरा (सैनिकों की वर्णनात्मक भूमिका) की प्रथा की शुरुआत की। <strong>आर्थिक सुधार - </strong>अलाउद्दीन ने अनेक नियमों द्वारा बाजार को नियंत्रित किया। सभी वस्तुओं की कीमत निर्धारित कर दी। उसने दीवान-ए-रियासत नामक, मंत्री के अधीन बाजार नियंत्रण विभाग की स्थापना की।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following was NOT a type of tax imposed by the state during the reign of Alauddin Khilji?</p>",
                    question_hi: "<p>3. अलाउद्दीन खिलजी के शासनकाल में राज्य द्वारा निम्नलिखित में से किस प्रकार का कर नहीं लगाया गया था?</p>",
                    options_en: ["<p>Tax on housing</p>", "<p>Tax on small scale industries</p>", 
                                "<p>Tax on cultivation</p>", "<p>Tax on cattle</p>"],
                    options_hi: ["<p>आवास पर कर</p>", "<p>लघु उद्योगों पर कर</p>",
                                "<p>कृषि पर कर</p>", "<p>मवेशियों पर कर</p>"],
                    solution_en: "<p>3.(b) <strong>Tax on small scale industries. Alauddin Khilji </strong>was a prominent ruler of the <strong>Khilji</strong> dynasty that ruled Delhi Sultanate from 1296 to 1316. He is known for his military prowess, administrative reforms, and his efforts to strengthen the empire\'s economy and centralise power. He imposed <strong>kharaj </strong>(land tax that was collected from farmers), <strong>jizya </strong>(poll tax that was collected from non-Muslims), charai (agriculture tax) and <strong>ghari</strong> (a house tax).</p>",
                    solution_hi: "<p>3.(b) <strong>लघु उद्योगों पर कर I</strong>अलाउद्दीन खिलजी, खिलजी वंश का एक प्रमुख शासक था जिसने 1296 से 1316 तक दिल्ली सल्तनत पर शासन किया था। वह अपनी सैन्य कौशल, प्रशासनिक सुधारों और साम्राज्य की अर्थव्यवस्था को मजबूत करने और सत्ता को केंद्रीकृत करने के अपने प्रयासों के लिए जाना जाता है। उसने <strong>खराज </strong>(किसानों से वसूला जाने वाला भूमि कर), <strong>जजिया </strong>(गैर-मुसलमानों से वसूला जाने वाला चुनाव कर), <strong>चराई </strong>(कृषि कर) और <strong>घरी </strong>(एक गृह कर) लगाया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Who among the following constructed the garrison town Siri for his soldiers?</p>",
                    question_hi: "<p>4. निम्नलिखित में से किसने अपने सैनिकों के लिए सैन्य (गैरीसन) शहर सिरी का निर्माण किया?</p>",
                    options_en: ["<p>Qutb-ud-din-Aibak</p>", "<p>Ghiyas-ud-din Tughlaq</p>", 
                                "<p>Alauddin Khilji</p>", "<p>Mohammad Tughluq</p>"],
                    options_hi: ["<p>कुतुब-उद-दीन-ऐबक</p>", "<p>गयास-उद्-दीन तुगलक</p>",
                                "<p>अलाउद्दीन खिलजी</p>", "<p>मोहम्मद तुगलक</p>"],
                    solution_en: "<p>4.(c) <strong>Alauddin Khilji. </strong>The famous monuments built by him - Alai Darwaza, Siri Fort, Hauz Khas, Alai minar, The Palace of a thousand pillars. <strong>Rulers and Monuments: </strong>Qutb-ud-din-Aibak - Qutub Minar, Quwwat-ul-Islam, Adhai din ka Jhonpra. Iltutmish - Gandhak ki Baoli, Hauz-i-Shamsi. Ghiyas-ud-din Tughlaq - Tughlaqabad Fort. Mohammad bin Tughluq - Jahanpanah, Adilabad Fort.</p>",
                    solution_hi: "<p>4.(c) <strong>अलाउद्दीन खिलजी</strong>। उनके द्वारा बनवाए गए प्रसिद्ध स्मारक - अलाई दरवाजा, सिरी किला, हौज खास, अलाई मीनार, एक हजार स्तंभों का महल। <strong>शासक और स्मारक:</strong> कुतुब-उद-दीन-ऐबक - कुतुब मीनार, कुव्वत-उल-इस्लाम, अढ़ाई दिन का झोंपड़ा। इल्तुतमिश - गंधक की बावली, हौज़-ए-शम्सी। गियास-उद-दीन तुगलक - तुगलकाबाद किला। मोहम्मद बिन तुगलक - जहाँपनाह, आदिलाबाद किला।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. _______ defeated Mahmud Khilji and erected the tower of victory (Vijay Stambha) in Chittorgarh.",
                    question_hi: "5. _________ ने महमूद खिलजी को हराया और चित्तौड़गढ़ में विजय स्तम्भ (विजय स्तम्भ) का निर्माण किया।",
                    options_en: [" Prithviraj Chauhan", " Jai Chand Gadhawak", 
                                " Rana Sangram Singh", " Rana Kumbha"],
                    options_hi: [" पृथ्वीराज चौहान", " जय चंद गढ़वाक",
                                " राणा संग्राम सिंह", " राणा कुंभा"],
                    solution_en: "<p>5.(d) <strong>Rana Kumbha</strong> was the Maharana of Mewar region (1433-1468 CE) and a Rajput from the Sisodia family. The Battle of Sarangpur was fought between Rana Kumbha and Sultan Mahmud Khilji in 1437. The Battle of Mandalgarh and Battle of Banas were two other major battles fought between them. Vijay Stambh is also called Kriti Stambh or Vishnu Stambha located in The Chittorgarh Fort. It is one of the largest forts in India. It was included in the UNESCO World Heritage Site in 2013.</p>",
                    solution_hi: "<p>5.(d) <strong>राणा कुंभा </strong>मेवाड़ क्षेत्र के महाराणा (1433-1468) और सिसोदिया परिवार के एक राजपूत थे। सारंगपुर की लड़ाई 1437 में राणा कुंभा और सुल्तान महमूद खिलजी के बीच लड़ा गया था। मांडलगढ़ की लड़ाई और बनास की लड़ाई उनके बीच लड़ी गई दो अन्य प्रमुख लड़ाईयां थीं। विजय स्तम्भ को चित्तौड़गढ़ किले में स्थित कृति स्तम्भ या विष्णु स्तम्भ भी कहा जाता है। यह भारत के सबसे बड़े किलों में से एक है। इसे 2013 में यूनेस्को की विश्व धरोहर स्थल में शामिल किया गया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Who among the following built the Alai Darwaza in Delhi in 1311?</p>",
                    question_hi: "<p>6. इनमें से किसने 1311 में दिल्ली में अलाई दरवाजा का निर्माण कराया?</p>",
                    options_en: ["<p>Allauddin Khilji</p>", "<p>Muhammad-bin-Tughluq</p>", 
                                "<p>Iltutmish</p>", "<p>Firoz Shah Tughlaq</p>"],
                    options_hi: ["<p>अल्लाउद्दीन खिलज</p>", "<p>----empty----</p>",
                                "<p>मुहम्मद-बिन-तुगलक(hc) इल्तुतमिश</p>", "<p>फिरोजशाह तुगलक</p>"],
                    solution_en: "<p>6.(a) <strong>Alauddin Khilji </strong>(1296 &ndash; 1316) - was the second sultan of the Khilji Dynasty (1290-1320). Founder (Khilji Dynasty) - Jalal-ud-din Firoz Khilji. The Alai Darwaza was added to the already established Qutub Minar complex in Delhi. It is located to the south of the Quwwat-ul-Islam Mosque and is a large, monumental entrance gate. It is built out of red sandstone. He constructed the garrison town Siri for his soldiers. Battles: Ranthambore (1301), Kili, Siege of Warangal (1310), Siege of Chittorgarh (1303), Mongol invasion of India (1303), Gujarat(1299).</p>",
                    solution_hi: "<p>6.(a) <strong>अलाउद्दीन खिलजी</strong> (1296 &ndash; 1316) - खिलजी वंश (1290-1320) का दूसरा सुल्तान था। संस्थापक (खिलजी वंश) - जलाल-उद-दीन फिरोज खिलजी। अलाई दरवाजा दिल्ली में पहले से स्थापित कुतुब मीनार परिसर में जोड़ा गया था। यह कुव्वत-उल-इस्लाम मस्जिद के दक्षिण में स्थित है और एक बड़ा, स्मारकीय प्रवेश द्वार है। इसका निर्माण लाल बलुआ पत्थर से किया गया है। उसने अपने सैनिकों के लिए सैन्य (गैरीसन) शहर सिरी का निर्माण किया। युद्ध: रणथंभौर (1301), किली, वारंगल की घेराबंदी (1310), चित्तौड़गढ़ की घेराबंदी (1303), भारत पर मंगोल आक्रमण (1303), गुजरात (1299)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Who among the following Sultans framed a series of regulations to weaken the power of nobles?</p>",
                    question_hi: "<p>7. निम्नलिखित में से किस सुल्तान ने सामंतो की शक्ति को कमजोर करने के लिए विनियमनों की एक श्रृंखला तैयार की थी?</p>",
                    options_en: ["<p>Muhammad bin Tughluq</p>", "<p>Balban</p>", 
                                "<p>Alauddin Khilji</p>", "<p>Iltutmish</p>"],
                    options_hi: ["<p>मुहम्मद बिन तुगलक</p>", "<p>बलबन</p>",
                                "<p>अलाउद्दीन खिलजी</p>", "<p>इल्तुतमिश</p>"],
                    solution_en: "<p>7.(c) <strong>Alauddin Khilji</strong> (Khilji Dynasty 1290-1320): He made nobles pay land tax for their holdings. This curbed the excess of wealth owned by nobles. The nobles were also ordered not to have social gatherings or intermarriages without his permission. The group of nobles, called \"The Forty\".</p>",
                    solution_hi: "<p>7.(c) <strong>अलाउद्दीन खिलजी</strong> (खिलजी राजवंश 1290-1320): उसने सामंतो से उनकी जोत (जोताई) के लिए भूमि कर का भुगतान कराया। इससे सामंतो के स्वामित्व वाली संपत्ति की अधिकता पर अंकुश लगा। सामंतो को यह भी आदेश दिया गया कि वे उसकी अनुमति के बिना सामाजिक समारोहों या अंतर्विवाह न करें। सामंतो का समूह, जिसे \"द फोर्टी\" कहा जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who among the following classical poets of the Sultanate period in India popularized Qawwali, a form of music which derived its name from the Arabic word &lsquo;qaul&rsquo;, meaning &lsquo;to speak&rsquo; ?</p>",
                    question_hi: "<p>8. भारत में सल्तनत काल के निम्नलिखित शास्त्रीय कवियों में से किसने कव्वाली को लोकप्रिय बनाया था, जो संगीत की एक ऐसी शैली है, जिसका नाम अरबी शब्द \'कौल (qaul)\' से लिया गया है, जिसका अर्थ है \'बोलना\' ?</p>",
                    options_en: ["<p>Jalal al-Din Muhammad Rumi</p>", "<p>Amir Khusrau</p>", 
                                "<p>Nizam al-Din Awliya</p>", "<p>Shams al-Din Muhammad Hafiz</p>"],
                    options_hi: ["<p>जलाल-उद्-दीन मुहम्मद रूमी</p>", "<p>अमीर खुसरो</p>",
                                "<p>निज़ामुद्दीन औलिया</p>", "<p>शम्सुद्दीन मुहम्मद हाफिज</p>"],
                    solution_en: "<p>8.(b) <strong>Amir Khusrau -</strong> He was also known as Tuti-e-Hind (Parrot of India). He was the court musician of Alauddin Khilji. He also lived in courts of different rulers such as Balban, Muhammad, Cakubad, Jalaluddin Khilji, Mubarak Shah Khaliji, and Ghiyasuddin Tughlaq . He was also a disciple of Nizammuddin Auliya.</p>",
                    solution_hi: "<p>8.(b) <strong>अमीर ख़ुसरो - </strong>इन्हें तूती-ए-हिन्द (भारत का तोता) के नाम से भी जाना जाता था। ये अलाउद्दीन खिलजी के दरबारी संगीतकार थे। ये बलबन, मुहम्मद, कैकुबाद, जलालुद्दीन खिलजी, मुबारक शाह खिलजी और गयासुद्दीन तुगलक जैसे अलग-अलग शासकों के दरबार में भी रहे। ये निज़ामुद्दीन औलिया के शिष्य भी थे।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following Sultans of Delhi was appointed as Amir-i-Tuzuk (Master of Ceremonies) before becoming the Sultan?</p>",
                    question_hi: "<p>9. दिल्ली के निम्नलिखित में से किस सुल्तान को सुल्तान बनने से पहले अमीर-ए-तुज़ुक (मास्टर ऑफ सेरेमनीज़) नियुक्त किया गया था?</p>",
                    options_en: ["<p>Alauddin Khalji</p>", "<p>Balban</p>", 
                                "<p>Iltutmish</p>", "<p>Muhammad Bin Tughlaq</p>"],
                    options_hi: ["<p>अलाउद्दीन खिलजी</p>", "<p>बलबन</p>",
                                "<p>इल्तुतमिश</p>", "<p>मुहम्मद बिन तुगलक</p>"],
                    solution_en: "<p>9.(a)<strong> Alauddin Khalji </strong>(1296 - 1316). He was the second ruler of Khilji dynasty after Jalaluddin Khilji (Founder - &lsquo;Khalji Dynasty&rsquo;). He was also appointed Arizi-i-Mumalik (Minister of War) during the reign of Jalaluddin Khilji and laid the foundation of his capital Siri in 1303 A.D.</p>",
                    solution_hi: "<p>9.(a) <strong>अलाउद्दीन खिलजी</strong> (1296 - 1316). वह जलालुद्दीन खिलजी (संस्थापक - खिलजी राजवंश\') के बाद खिलजी राजवंश का दूसरा शासक था। जलालुद्दीन खिलजी के शासनकाल के दौरान उन्हें आरिज-ए-मुमालिक (युद्ध मंत्री) भी नियुक्त किया गया था और उन्होंने 1303 ई. में अपनी राजधानी सिरी की नींव रखी थी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Who was the first to mention the incident of Jauhar in Persian ?</p>",
                    question_hi: "<p>10. जौहर की घटना का उल्लेख फारसी भाषा में सबसे पहले किसने किया था ?</p>",
                    options_en: ["<p>Ibn Batutah</p>", "<p>Amir Khusrau</p>", 
                                "<p>Ziauddin Barani</p>", "<p>Hasan Nizami</p>"],
                    options_hi: ["<p>इब्न बतूता</p>", "<p>अमीर खुसरो</p>",
                                "<p>जियाउद्दीन बरनी</p>", "<p>हसन निज़ामी</p>"],
                    solution_en: "<p>10.(b)&nbsp; <strong>Amir Khusrau. &lsquo;Jauhar&rsquo; - </strong>The collective self-immolation by women in order to escape capture and forced into slavery by invaders in face of imminent defeat. In 1303, Alauddin Khilji laid the size of Chittor.</p>",
                    solution_hi: "<p>10.(b) <strong>अमीर खुसरो। \'जौहर\' - </strong>आसन्न पराजय के सामने आक्रमणकारियों द्वारा गुलामी से बचने के लिए महिलाओं द्वारा सामूहिक आत्मदाह। 1303 में अलाउद्दीन खिलजी ने चित्तौड़ पर अधिकृत किया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Who among the following was the slave-general Alauddin Khilji, who led his army in the battle against Ramachandra of Devagiri?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन अलाउद्दीन खिलजी का गुलाम - सेनापति था, जिसने देवगिरि के रामचन्द्र के खिलाफ युद्ध में अपनी सेना का नेतृत्व किया था?</p>",
                    options_en: ["<p>Nusrat Khan</p>", "<p>Malik Kafur</p>", 
                                "<p>Ulugh Khan</p>", "<p>Zafar Khan</p>"],
                    options_hi: ["<p>नुसरत खान</p>", "<p>मलिक काफूर</p>",
                                "<p>उलुग खान</p>", "<p>जफर खान</p>"],
                    solution_en: "<p>11.(b) <strong>Malik Kafur - </strong>Also known as \'Hazar Dinari&rsquo; Was a slave of the Delhi Sultanate ruler Ala-ud-din Khilji (1296&ndash;1316). Alauddin Khilji - second sultan of Khilji dynasty. Important Military Campaigns - Gujarat (1299), Ranthambore (1301), Chittor (1303), Malwa (1305) and Jalore (1311). Administrative Reforms - implemented the system of \"dagh Pratha ,\" branding horses, and \"Huliya Pratha (a system of ranks)\" keeping records of soldiers.</p>",
                    solution_hi: "<p>11.(b) <strong>मलिक काफ़ूर</strong> - जिसे \'हज़ार दीनारी\' के नाम से भी जाना जाता है, दिल्ली सल्तनत के शासक अला-उद-दीन खिलजी (1296-1316) का गुलाम था। अलाउद्दीन खिलजी - खिलजी वंश का दूसरा सुल्तान। महत्वपूर्ण सैन्य अभियान - गुजरात (1299), रणथंभौर (1301), चित्तौड़ (1303), मालवा (1305) और जालौर (1311)। प्रशासनिक सुधार - सैनिकों का रिकॉर्ड रखने के लिए \"दाग प्रथा \", घोड़ों को दागने और \"हुलिया प्रथा (रैंकों की एक प्रणाली)\" की प्रणाली लागू की गई।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Who was the first Sultan of the Delhi Sultanate to start military expeditions into southern India?</p>",
                    question_hi: "<p>12. दक्षिणी भारत में सैन्य अभियान शुरू करने वाला दिल्ली सल्तनत का पहला सुल्तान कौन था?</p>",
                    options_en: ["<p>Ghiyasuddin Balban</p>", "<p>Alauddin Khalji</p>", 
                                "<p>Muhammad Tughluq</p>", "<p>Shamsuddin Iltutmish</p>"],
                    options_hi: ["<p>गयासुद्दीन बलबन</p>", "<p>अलाउद्दीन खिलजी</p>",
                                "<p>मुहम्मद तुगलक</p>", "<p>शम्सुद्दीन इल्तुतमिश</p>"],
                    solution_en: "<p>12.(b)&nbsp;<strong> Alauddin Khalji </strong>(1296&ndash;1316):- He was the second ruler of the Khilji dynasty of the Delhi Sultanate. <strong>Battles related to Alauddin Khilji :</strong> Gujarat Vijay : (1299) Karna rescued himself and his family to Devagiri. Ranthambore Vijay : (1301) Chauhan King Hammir Dev defeated Sultan Alauddin Khilji, under Nusrat Khan. Chittor Vijay : (1303) Alauddin Khalji defeated Raja Ratan singh.</p>",
                    solution_hi: "<p>12.(b) <strong>अलाउद्दीन खिलजी</strong> (1296-1316):- वह दिल्ली सल्तनत के खिलजी वंश का दूसरा शासक था। <strong>अलाउद्दीन</strong> <strong>खिलजी से संबंधित युद्ध:</strong> गुजरात विजय: (1299) कर्ण पराजित हुआ। रणथंभौर विजय: (1301) चौहान राजा हम्मीर देव ने नुसरत खान के नेतृत्व में सुल्तान अलाउद्दीन खिलजी को पराजित किया । चित्तौड़ विजय: (1303) अलाउद्दीन खिलजी ने राजा रतन सिंह को हराया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Who among the following was the founder of the Khilji Dynasty?</p>",
                    question_hi: "<p>13. निम्नलिखित में से खिलजी वंश के संस्थापक कौन थे?</p>",
                    options_en: ["<p>Jalal-ud-din Firoz Khilji</p>", "<p>Alauddin Khilji</p>", 
                                "<p>Nasiruddin Khusrau Shah</p>", "<p>Qutbuddin Mubarak Shah</p>"],
                    options_hi: ["<p>जलालुद्दीन फिरोज खिलजी</p>", "<p>अलाउद्दीन खिलजी</p>",
                                "<p>नसीरुद्दीन खुसरो शाह</p>", "<p>कुतुबुद्दीन मुबारक शाह</p>"],
                    solution_en: "<p>13.(a) <strong>Jalal-ud-din Firoz Khilji. </strong>He ascended to the throne in 1290 AD after deposing Qutbuddin Mubarak Shah, the last ruler of the Slave Dynasty.</p>",
                    solution_hi: "<p>13.(a) <strong>जलालुद्दीन फ़िरोज़ खिलजी। </strong>वह&nbsp;1290 ई. में गुलाम वंश के अंतिम शासक कुतुबुद्दीन मुबारक शाह को पद से हटाकर सिंहासन पर बैठा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which monument of Khilji dynasty in Delhi has the utilization of red sandstone?</p>",
                    question_hi: "<p>14. दिल्ली में खिलजी वंश के किस स्मारक में लाल बलुआ पत्थर का उपयोग हुआ है?</p>",
                    options_en: ["<p>Moti Masjid</p>", "<p>Victoria memorial</p>", 
                                "<p>Charminar</p>", "<p>Alai Darwaza</p>"],
                    options_hi: ["<p>मोती मस्जिद</p>", "<p>विक्टोरिया मेमोरियल</p>",
                                "<p>चारमीनार</p>", "<p>अलाई-दरवाजा</p>"],
                    solution_en: "<p>14.(d) <strong>Alai Darwaza. </strong>Built by Allaudin Khilji the Sultan of Delhi in 1311 AD. <strong>Moti Masjid -</strong> Aurangzeb (Delhi). <strong>Victoria memorial -</strong> George V (Kolkata). <strong>Charminar </strong>- Mohammed Quli Qutub <br>Shah (Hyderabad). </p>",
                    solution_hi: "<p>14.(d) <strong>अलाई दरवाजा।</strong> 1311 ई. में दिल्ली के सुल्तान अलाउद्दीन खिलजी द्वारा निर्मित है। <strong>मोती मस्जिद - </strong>औरंगजेब (दिल्ली)। विक्टोरिया मेमोरियल - जॉर्ज पंचम (कोलकाता)। <strong>चारमीनार </strong>- मोहम्मद कुली कुतुब शाह (हैदराबाद)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which officer under Alauddin Khilji was required to maintain a register of merchants to ensure an adequate supply of goods?</p>",
                    question_hi: "<p>15. माल की पर्याप्त आपूर्ति सुनिश्चित करने हेतु अलाउद्दीन खिलजी के अधीन किस अधिकारी के लिए व्यापारियों का रजिस्टर बनाए रखना आवश्यक था?</p>",
                    options_en: ["<p>Nazir</p>", "<p>Rais Parwana<strong id=\"docs-internal-guid-21f33528-7fff-4d79-9553-bbfbec7be5f7\"><br></strong></p>", 
                                "<p>Muhtasib</p>", "<p>Shahna-i-Mandi</p>"],
                    options_hi: ["<p>नज़ीर</p>", "<p>रईस परवाना</p>",
                                "<p>मुहतसिब</p>", "<p>शहना-ए-मंडी</p>"],
                    solution_en: "<p>15.(d)<strong> Shahna-i-Mandi. </strong>Alauddin set up three markets at Delhi - First for food-grains, the second for cloth, sugar, ghee, oil, dry fruits and the third for horses, slaves and cattle. <strong>Diwan- e - Riyasat</strong> - Head of Market. The prices of all varieties of grains were fixed by the first regulation or Zabita. <strong>Munhiyans </strong>- Secret agents under Allauddin. <strong>Parwana Rais -</strong> Permit officer under Allauddin.</p>",
                    solution_hi: "<p>15.(d) <strong>शहना-ए-मंडी।</strong> अलाउद्दीन ने दिल्ली में तीन बाजार स्थापित किए - पहला खाद्यान्न के लिए, दूसरा कपड़ा, चीनी, घी, तेल, सूखे मेवों के लिए और तीसरा घोड़ों, दासों और मवेशियों के लिए। <strong>दीवान-ए-रियासत -</strong> बाजार प्रमुख। सभी प्रकार के अनाज की कीमतें पहले विनियमन या ज़बीता द्वारा तय की गईं। <strong>मुंहियान</strong>- अलाउद्दीन के अधीन खुफिया जासूस। <strong>परवाना रईस -</strong> अलाउद्दीन के अधीन परमिट अधिकारी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>