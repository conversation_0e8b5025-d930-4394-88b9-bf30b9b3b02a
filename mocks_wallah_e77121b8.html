<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 50</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">50</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 49,
                end: 49
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Who among the following novelists has written &lsquo; A Suitable Boy\' ?</p>",
                    question_hi: "<p>1. निम्नलिखित में से किस उपन्यासकार ने \'ए सूटेबल बॉय\' (A Suitable Boy) लिखा है?</p>",
                    options_en: ["<p>Aravind Adiga</p>", "<p>Vikram Seth</p>", 
                                "<p>Arundhati Roy</p>", "<p>Chetan Bhagat</p>"],
                    options_hi: ["<p>अरविंद अडिगा</p>", "<p>विक्रम सेठ</p>",
                                "<p>अरुंधति रॉय</p>", "<p>चेतन भगत</p>"],
                    solution_en: "<p>1.(b) <strong>Vikram Seth.</strong> His other books: &lsquo;&lsquo;The Golden Gate&rsquo;&rsquo;, &ldquo;The Frog and the Nightingale&rdquo;, &lsquo;&lsquo;Two Lives&rsquo;&rsquo;. Author and Books: Aravind Adiga - &ldquo;The White Tiger&rdquo; and &ldquo;Selection Day&rdquo;. Arundhati Roy - &ldquo;The God of Small Things&rdquo; and &ldquo;The Ministry of Utmost Happiness&rdquo;. Chetan Bhagat - &ldquo;Five Point Someone&rdquo; and &ldquo;Half Girlfriend&rdquo;.</p>",
                    solution_hi: "<p>1.(b) <strong>विक्रम सेठ। </strong>उनकी अन्य पुस्तकें: \"द गोल्डन गेट\", \"द फ्रॉग एंड द नाइटिंगेल\", \"टू लाइव्स\"। लेखक और पुस्तकें : अरविंद अडिगा - \"द व्हाइट टाइगर\" और \"सिलेक्शन डे\"। अरुंधति रॉय - \"द गॉड ऑफ़ स्मॉल थिंग्स\" और \"द मिनिस्ट्री ऑफ़ अटमोस्ट हैप्पीनेस\"। चेतन भगत - \"फाइव पॉइंट समवन\" और \"हाफ गर्लफ्रेंड\"।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Minati Mishra was conferred the Padma Shri for her contribution to which form of<br>dance ?</p>",
                    question_hi: "<p>2. मिनाती मिश्रा को किस प्रकार के नृत्य में उनके योगदान के लिए पद्म श्री से सम्मानित किया गया था?</p>",
                    options_en: ["<p>Chhau</p>", "<p>Sattriya</p>", 
                                "<p>Bihu</p>", "<p>Odissi</p>"],
                    options_hi: ["<p>छऊ</p>", "<p>सत्त्रिया</p>",
                                "<p>बिहु</p>", "<p>ओडिसी</p>"],
                    solution_en: "<p>2.(d) <strong>Odissi: </strong>Other dancers - Kelucharan Mohapatra, Sujata Mohapatra, Madhavi Mudgal, Deba Prasad Das. Minati Mishra&rsquo;s Awards - Padma Shri (2012), Odisha Sangeet Natak Akademi Award (1975).</p>",
                    solution_hi: "<p>2.(d) <strong>ओडिसी: </strong>अन्य नर्तक - केलुचरण महापात्र, सुजाता महापात्र, माधवी मुद्गल, देबा प्रसाद दास। मिनती मिश्रा के पुरस्कार - पद्म श्री (2012 में), ओडिशा संगीत नाटक अकादमी पुरस्कार (1975 में)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The Lakshmana temple of Khajuraho, dedicated to Lord Vishnu, is an example of<br>which style of temple architecture ?</p>",
                    question_hi: "<p>3. भगवान विष्णु को समर्पित खजुराहो का लक्ष्मण मंदिर किस शैली की मंदिर वास्तुकला का उदाहरण है?</p>",
                    options_en: ["<p>Vesara</p>", "<p>Nagara</p>", 
                                "<p>Dravidian</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>वेसरा</p>", "<p>नागर</p>",
                                "<p>द्रविड़</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>3.(b) <strong>Nagara.</strong> Temple Architecture in India: Nagara style - Kandariya Mahadeva Temple (Madhya Pradesh), and Sun Temple at Modhera (Gujarat). Vesara style - Durga Temple (Karnataka), Kallesvara temple (Karnataka), Lad Khan temple (Karnataka). Dravidian style - Brihadeeswara temple (Thanjavur, Tamil Nadu), Mahabalipuram (Kanchipuram, Tamil Nadu), Annamalaiyar Temple (Tiruvannamalai, Tamil Nadu).</p>",
                    solution_hi: "<p>3.(b)<strong> नागर।</strong> भारत में मंदिर वास्तुकला: नागर शैली - कंदरिया महादेव मंदिर (मध्य प्रदेश), और मोढेरा (गुजरात) में सूर्य मंदिर। वेसर शैली - दुर्गा मंदिर (कर्नाटक), कल्लेश्वर मंदिर (कर्नाटक), लाड खान मंदिर (कर्नाटक)। द्रविड़ शैली - बृहदेश्वर मंदिर (तंजावुर, तमिलनाडु), महाबलीपुरम (कांचीपुरम, तमिलनाडु), अन्नामलाईयार मंदिर (तिरुवन्नामलाई, तमिलनाडु)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Consider the following methods adopted by the government to protect goods<br>produced in India from imports.<br>(i) Heavy taxes were levied on imported goods<br>(ii) The maximum limit on the imports of a commodity by a domestic user was fixed<br>Choose the correct answer.</p>",
                    question_hi: "<p>4. भारत में उत्पादित वस्तुओं को आयात से बचाने के लिए सरकार द्वारा अपनाए गए निम्नलिखित तरीकों पर विचार करें। <br>(i) आयातित वस्तुओं पर भारी टैक्स लगाया जाता था। <br>(ii) देशी उपयोगकर्ता द्वारा किसी वस्तु के आयात की अधिकतम सीमा निर्धारित की गई थी। सही उत्तर का चयन <br>करें।</p>",
                    options_en: ["<p>Both (i) and (ii) are true</p>", "<p>Both (i) and (ii) are false</p>", 
                                "<p>Only (ii) is true</p>", "<p>Only (i) is true</p>"],
                    options_hi: ["<p>(i) और (ii) दोनों सत्य हैं</p>", "<p>(i) और (ii) दोनों असत्य हैं</p>",
                                "<p>केवल (ii) सत्य है</p>", "<p>केवल (i) सत्य है</p>"],
                    solution_en: "<p>4.(a) <strong>Both (i) and (ii) are true.</strong> Import substitution: Trade policy strategy discontinuing foreign imports, promoting domestic production. Trade policy: It refers to a set of goals, rules, standards, and regulations established by governments to govern international trade between countries</p>",
                    solution_hi: "<p>4.(a) <strong>(i) और (ii) दोनों सत्य हैं।</strong> आयात प्रतिस्थापन: व्यापार नीति रणनीति विदेशी आयात को बंद करना, घरेलू उत्पादन को बढ़ावा देना। व्यापार नीति: यह विभिन्न देशों के बीच अंतर्राष्ट्रीय व्यापार को नियंत्रित करने के लिए सरकारों द्वारा स्थापित लक्ष्यों, नियमों, मानकों, और विनियमों के एक समूह को संदर्भित करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following is a popular festival celebrated in the state of Bihar ?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन सा बिहार राज्य में मनाया जाने वाला एक लोकप्रिय त्योहार है?</p>",
                    options_en: ["<p>Me-Dam-Me-Phi</p>", "<p>Sama Chakeva</p>", 
                                "<p>Harela</p>", "<p>Ali-Ai-Ligang</p>"],
                    options_hi: ["<p>मे-दम-मे-फि (Me-Dam-Me-Phi)</p>", "<p>सामा चकेवा (Sama Chakeva)</p>",
                                "<p>हरेला (Harela)</p>", "<p>अली-ऐ-लिगांग (Ali-Ai-Ligang)</p>"],
                    solution_en: "<p>5.(b) <strong>Sama Chakeva. </strong>It is a festival deeply rooted in the Maithili community in India, and Tharu community in Nepal. Festivals in India: Assam - Baishagu, Ali-Ai-Ligang, Baikho, Rongker, Rajini Gabra Harni Gabra, Bohaggiyo Bishu, Ambubashi Mela and Jonbill Mela. Uttarakhand - Harela. Tamil Nadu - Pongal. Kerala - Onam.</p>",
                    solution_hi: "<p>5.(b) <strong>सामा चकेवा (Sama Chakeva)। </strong>यह भारत में मैथिली समुदाय और नेपाल में थारू समुदाय में मूलरूप से निहित एक त्योहार है। भारत में त्यौहार: असम - बैशागु, अली-ऐ-लिगांग, बैखो, रोंगकेर, रजनी गबरा हरनी गबरा, बोहागियो बिशु, अंबुबाची मेला और जोनबिल मेला। उत्तराखंड - हरेला। तमिलनाडु - पोंगल। केरल - ओणम ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Which region of India is known for the unique Shola forests?",
                    question_hi: "<p>6. भारत का कौन-सा क्षेत्र अनोखे शोला वनों के लिए जाना जाता है ?</p>",
                    options_en: ["<p>Eastern Himalayas</p>", "<p>Western Ghats</p>", 
                                "<p>Thar Desert</p>", "<p>Deccan Plateau</p>"],
                    options_hi: ["<p>पूर्वी हिमालय</p>", "<p>पश्चिमी घाट</p>",
                                "<p>थार का रेगिस्तान</p>", "<p>दक्कन का पठार</p>"],
                    solution_en: "<p>6.(b) <strong>Western Ghats. </strong>Shola forests are tropical montane forests found especially in the states of Kerala, Tamil Nadu and Karnataka. Eastern Himalayas span eastern Nepal, Northeast India, Bhutan, Tibet (an autonomous region of China), Yunnan (China), and Northern Myanmar. Thar Desert is located in Asia on the India-Pakistan border.</p>",
                    solution_hi: "<p>6.(b)<strong> पश्चिमी घाट। </strong>शोला वन उष्णकटिबंधीय पर्वतीय वन हैं जो विशेष रूप से केरल, तमिलनाडु और कर्नाटक राज्यों में पाए जाते हैं। पूर्वी हिमालय पूर्वी नेपाल, पूर्वोत्तर भारत, भूटान, तिब्बत (चीन का एक स्वायत्त क्षेत्र), युन्नान (चीन) और उत्तरी म्यांमार तक फैला है। थार मरुस्थल एशिया में भारत-पाकिस्तान सीमा पर स्थित है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. The Ghoomar dance of Rajasthan was originally performed by which tribal<br />community?",
                    question_hi: "<p>7. राजस्थान का घूमर नृत्य मूल रूप से कौन-से आदिवासी समूह द्वारा किया जाता था?</p>",
                    options_en: ["<p>Kathodi</p>", "<p>Mina</p>", 
                                "<p>Bhil</p>", "<p>Kanjar</p>"],
                    options_hi: ["<p>कथौड़ी</p>", "<p>मीणा</p>",
                                "<p>भील</p>", "<p>कंजर</p>"],
                    solution_en: "<p>7.(c) <strong>Bhil.</strong> Other dance forms and tribal community of Rajasthan: Kalbelia dance (Kalbeliya Community), Chari or Pot Dance (Kishangarh and Saini communities), Terah Taal (Kamada tribes), Gair dance (Bhil community), and Fire dance (Banjara community).</p>",
                    solution_hi: "<p>7.(c) <strong>भील।</strong> राजस्थान के अन्य नृत्य रूप और जनजातीय समुदाय: कालबेलिया नृत्य (कालबेलिया समुदाय), चरी या पॉट नृत्य ( किशनगढ़ और सैनी समुदाय), तेरह ताल (कामदा जनजाति), गैर नृत्य (भील समुदाय), अग्नि नृत्य (बंजारा समुदाय)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. Who among the following was appointed as the first minister of the Ministry of<br />Disinvestment during the tenure of the Vajpayee Government?",
                    question_hi: "<p>8. बाजपेयी सरकार के कार्यकाल के दौरान निम्नलिखित में से किसे विनिवेश मंत्रालय के पहले मंत्री के रूप में नियुक्त किया गया था ?</p>",
                    options_en: [" Yashwant Sinha ", " Arun Shourie ", 
                                " George Fernandes ", " Jaswant Singh"],
                    options_hi: ["<p>यशवंत सिन्हा</p>", "<p>अरुण शौरी</p>",
                                "<p>जॉर्ज फर्नांडिस</p>", "<p>जसवंत सिंह</p>"],
                    solution_en: "<p>8.(b)<strong> Arun Shourie.</strong> He was Minister of Communications and Information Technology in the Atal Bihari Vajpayee government (1998&ndash;2004). His Awards - Ramon Magsaysay Award (1982) and the Padma Bhushan (1990).</p>",
                    solution_hi: "<p>8.(b) <strong>अरुण शौरी।</strong> वह अटल बिहारी वाजपेयी सरकार में संचार और सूचना प्रौद्योगिकी मंत्री थे (1998-2004) । पुरस्कार - रेमन मैग्सेसे पुरस्कार (1982) और पद्म भूषण (1990)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following states is related to Gatka, which was inducted into the 37th<br>National Games?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन-सा राज्य 37वें राष्ट्रीय खेलों में शामिल किए गए खेल गतका से संबंधित है?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Uttar Pradesh</p>", 
                                "<p>Haryana</p>", "<p>Uttarakhand</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>उत्तर प्रदेश</p>",
                                "<p>हरियाणा</p>", "<p>उत्तराखंड</p>"],
                    solution_en: "<p>9.(a) <strong>Punjab.</strong> 37th National Games: Host city- Goa, Motto- Get Set Goa. Medal tally- 1st rank (Maharashtra), 2nd rank (Services Sports Control Board), 3rd rank (Haryana). The 38th National Games will be held in Uttarakhand.</p>",
                    solution_hi: "<p>9.(a) <strong>पंजाब।</strong> 37वें राष्ट्रीय खेल: मेजबान शहर- गोवा, आदर्श वाक्य- गेट सेट गोवा। पदक तालिका- पहली रैंक (महाराष्ट्र), दूसरी रैंक (सर्विसेज स्पोर्ट्स कंट्रोल बोर्ड), तीसरी रैंक (हरियाणा)। 38वें राष्ट्रीय खेल उत्तराखंड में होंगे।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following dances was traditionally performed by male monks in<br>monasteries?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन सा नृत्य मठों में परंपरागत रूप से पुरुष भिक्षुओं द्वारा किया जाता था?</p>",
                    options_en: ["<p>Sattriya</p>", "<p>Kathakali</p>", 
                                "<p>Kuchipudi</p>", "<p>Odissi</p>"],
                    options_hi: ["<p>सत्त्रिया</p>", "<p>कथकली</p>",
                                "<p>कुचिपुड़ी</p>", "<p>ओडिसी</p>"],
                    solution_en: "<p>10.(a)<strong> Sattriya. </strong>It originated in the Sattras or Vaishnava monasteries of Assam. Kathakali (Kerala) is associated with the storytelling form and is devoted to Hindu god Krishna. Kuchipudi (Andhra Pradesh) is traditionally performed by both men and women and known for its expressive hand gestures and energetic footwork. Odissi (Odisha) is traditionally performed by both men and women and known for its graceful movements and lyrical quality.</p>",
                    solution_hi: "<p>10.(a) <strong>सत्रिया।</strong> इसकी उत्पत्ति असम के सत्र या वैष्णव मठों में हुई थी। कथकली (केरल), कथा-वाचन रूप के साथ जुड़ी हुई है और यह हिन्दू भगवान कृष्ण को समर्पित है। कुचीपुड़ी (आंध्र प्रदेश) पारंपरिक रूप से पुरुषों और महिलाओं दोनों द्वारा किया जाता है और यह अपने अभिव्यंजक हस्त मुद्राओं और ऊर्जावान पैरों के काम के लिए जाना जाता है। ओडिसी (ओडिशा) पारंपरिक रूप से पुरुषों और महिलाओं दोनों द्वारा किया जाता है और यह अपनी सुंदर गतिविधियों और गीतात्मक गुणवत्ता के लिए जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following Articles of the Constitution of India mentions that Parliament by<br>law can form a new State by separation of territory from any state or by uniting any<br>territory to a part of any State?</p>",
                    question_hi: "<p>11. भारतीय संविधान के निम्नलिखित में से किस अनुच्छेद में यह उल्लेख किया गया है कि संसद कानून द्वारा किसी भी राज्य से क्षेत्र को अलग करके या किसी राज्य के किसी हिस्से में किसी भी क्षेत्र को मिलाकर एक नया राज्य बना सकती है?</p>",
                    options_en: ["<p>Article 9</p>", "<p>Article 5</p>", 
                                "<p>Article 7</p>", "<p>Article 3</p>"],
                    options_hi: ["<p>अनुच्छेद 9</p>", "<p>अनुच्छेद 5</p>",
                                "<p>अनुच्छेद 7</p>", "<p>अनुच्छेद 3</p>"],
                    solution_en: "<p>11.(d) <strong>Article 3 </strong>(in Part I). Under Citizenship (Part II): Article 5 - Citizenship at the commencement of the Constitution. Article 7- Rights of citizenship of certain migrants to Pakistan. Article 9 - Persons voluntarily acquiring citizenship of a foreign State not to be citizens.</p>",
                    solution_hi: "<p>11.(d) <strong>अनुच्छेद 3</strong> (भाग I में)। नागरिकता (भाग II) के तहत: अनुच्छेद 5 - संविधान के प्रारंभ में नागरिकता। अनुच्छेद 7- पाकिस्तान में कुछ प्रवासियों की नागरिकता का अधिकार। अनुच्छेद 9 - स्वेच्छा से किसी विदेशी राज्य की नागरिकता प्राप्त करने वाले व्यक्ति नागरिक नहीं होंगे।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. According to the census of India 2011, what was the literacy rate of Tamil Nadu?</p>",
                    question_hi: "<p>12. भारत की 2011 की जनगणना के अनुसार, तमिलनाडु की साक्षरता दर कितनी थी?</p>",
                    options_en: ["<p>82.80%</p>", "<p>82.34%</p>", 
                                "<p>80.09%</p>", "<p>81.42%</p>"],
                    options_hi: ["<p>82.80%</p>", "<p>82.34%</p>",
                                "<p>80.09%</p>", "<p>81.42%</p>"],
                    solution_en: "<p>12.(c)<strong> 80.09%</strong> (Male - 86.7% and Female - 73.4%). Census of India 2011: Literacy rate (India) - India 74.04% (Male - 82.14% and Female - 65.46%). Literacy rate of other states and UTs: Kerala (94%) - Highest in states; Bihar (61.8%) - Lowest in states; Lakshadweep (91.85%) - Highest in UTs; Dadra and Nagar Haveli (76.24%) - Lowest in UTs; Mizoram (91.33%); Goa (88.7%); Jharkhand (66.41%); and Uttar Pradesh (67.68%).</p>",
                    solution_hi: "<p>12.(c) <strong>80.09%</strong> (पुरुष - 86.8% और महिला - 73.4%)। भारत की जनगणना 2011: साक्षरता दर (भारत) - 74.04% (पुरुष - 82.14% और महिला - 65.46%)। अन्य राज्यों और केंद्रशासित प्रदेशों की साक्षरता दर: केरल (94%) - राज्यों में उच्चतम; बिहार (61.8%) - राज्यों में सबसे कम; लक्षद्वीप (91.85%) - केंद्रशासित प्रदेशों में उच्चतम; दादरा और नगर हवेली (76.24%) - केंद्रशासित प्रदेशों में सबसे कम; मिजोरम (91.33%); गोवा (88.7%); झारखंड (66.41%); और उत्तर प्रदेश (67.68%)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following is NOT one of the several approaches of micro finance delivery<br>mechanisms in India?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-सा भारत में माइक्रो फाइनेंस वितरण-तंत्र के कई दृष्टिकोणों में से एक नहीं है?</p>",
                    options_en: ["<p>Cheap subsidised credit</p>", "<p>Self-help-group - bank linkage programme</p>", 
                                "<p>Micro finance institutions- Credit Lending Models</p>", "<p>Conventional weaker-section lending by banks</p>"],
                    options_hi: ["<p>सस्ता सब्सिडी वाला क्रेडिट</p>", "<p>स्वयं सहायता समूह - बैंक लिंकेज कार्यक्रम</p>",
                                "<p>माइक्रो फाइनेंस संस्थान- क्रेडिट लेंडिंग मॉडल</p>", "<p>बैंकों द्वारा पारंपरिक कमजोर-वर्ग ऋण</p>"],
                    solution_en: "<p>13.(a) <strong>Cheap subsidised credit. </strong>Other Microfinance delivery mechanisms in India: Regional Rural Banks (RRBs) - These are banking institutions that offer microcredit, savings, insurance, and remittance services to rural clients, including small farmers, artisans, and rural entrepreneurs; Non-Banking Financial Companies (NBFCs): NBFCs play a significant role in microfinance by providing credit and other financial services to low-income individuals and microenterprises.</p>",
                    solution_hi: "<p>13.(a) <strong>सस्ता सब्सिडी वाला क्रेडिट।</strong> भारत में अन्य माइक्रोफाइनेंस वितरण तंत्र: क्षेत्रीय ग्रामीण बैंक (RRB) - ये ऐसे बैंकिंग संस्थान हैं जो ग्रामीण ग्राहकों, जिनमें छोटे किसान, कारीगर और ग्रामीण उद्यमी शामिल हैं, को सूक्ष्म ऋण, बचत, बीमा और प्रेषण सेवाएं प्रदान करते हैं। गैर-बैंकिंग वित्तीय कंपनियां (NBFC): NBFC कम आय वाले व्यक्तियों और सूक्ष्म उद्यमों को ऋण और अन्य वित्तीय सेवाएं प्रदान करके सूक्ष्म वित्त में महत्वपूर्ण भूमिका निभाती हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Under which of the following Articles of the Constitution of India does the Governor of<br>Arunachal Pradesh have special responsibility with respect to law and order and in<br>discharge of his functions in relation thereto?</p>",
                    question_hi: "<p>14. भारत के संविधान के निम्नलिखित में से किस अनुच्छेद के तहत अरुणाचल प्रदेश के राज्यपाल के पास कानून और व्यवस्था के संबंध में और अपने कार्यों के प्रभार से हटने से संबंधित विशेष जिम्मेदारी है?</p>",
                    options_en: ["<p>Article 371 D</p>", "<p>Article 371 H</p>", 
                                "<p>Article 371 A</p>", "<p>Article 371 G</p>"],
                    options_hi: ["<p>अनुच्छेद 371 D</p>", "<p>अनुच्छेद 371 H</p>",
                                "<p>अनुच्छेद 371 A</p>", "<p>अनुच्छेद 371 G</p>"],
                    solution_en: "<p>14.(b) <strong>Article 371 H. </strong>Under Temporary, Transitional and Special Provisions (Part XXI): 371 A- Special provision with respect to the State of Nagaland. 371 D - Special provisions with respect to the State of Andhra Pradesh. 371 G - Special provision with respect to the State of Mizoram.</p>",
                    solution_hi: "<p>14.(b) <strong>अनुच्छेद 371 H। </strong>अस्थायी, संक्रमणकालीन और विशेष प्रावधानों के तहत (भाग XXI): 371 A- नागालैंड राज्य के संबंध में विशेष प्रावधान। 371 D - आंध्र प्रदेश राज्य के संबंध में विशेष प्रावधान। 371 G - मिजोरम राज्य के संबंध में विशेष प्रावधान।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Which of the following words is not part of Article 15 of the Indian Constitution as far<br>as grounds of discrimination are concerned?</p>",
                    question_hi: "<p>15. जहाँ तक भेदभाव के आधारों का प्रश्न है, निम्नलिखित में से कौन-सा शब्द भारतीय संविधान के अनुच्छेद 15 का भाग नहीं है?</p>",
                    options_en: ["<p>Race</p>", "<p>Profession</p>", 
                                "<p>Caste</p>", "<p>Religion</p>"],
                    options_hi: ["<p>प्रजाति</p>", "<p>पेशा</p>",
                                "<p>जाति</p>", "<p>धर्म</p>"],
                    solution_en: "<p>15.(b) <strong>Profession</strong>. Under Fundamental Rights (Part III): Article 15 - Prohibition of discrimination on grounds of religion, race, caste, sex or place of birth. Article 16- Equality of opportunity in matters of public employment. Article 17- Abolition of Untouchability.</p>",
                    solution_hi: "<p>15.(b) <strong>पेशा। </strong>मौलिक अधिकारों (भाग III) के अंतर्गत: अनुच्छेद 15 - धर्म, मूलवंश, जाति, लिंग या जन्म स्थान के आधार पर भेदभाव का निषेध। अनुच्छेद 16 - सार्वजनिक रोजगार के मामलों में अवसर की समानता। अनुच्छेद 17 - अस्पृश्यता का अन्त।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Which National Waterway is associated with the Brahmaputra River?</p>",
                    question_hi: "<p>16. कौन-सा राष्ट्रीय जल मार्ग ब्रह्मपुत्र नदी से संबद्ध है?</p>",
                    options_en: ["<p>National Waterway 1</p>", "<p>National Waterway 3</p>", 
                                "<p>National Waterway 2</p>", "<p>National Waterway 5</p>"],
                    options_hi: ["<p>राष्ट्रीय जलमार्ग 1</p>", "<p>राष्ट्रीय जलमार्ग 3</p>",
                                "<p>राष्ट्रीय जलमार्ग 2</p>", "<p>राष्ट्रीय जलमार्ग 5</p>"],
                    solution_en: "<p>16.(c) <strong>National Waterway 2 -</strong> From Dhubri to Sadiya, the waterway extends for a distance of 891Km. National waterways (NW) in India: NW1 (Ganga-Bhagirathi-Hooghly River System), NW16 (Barak River), NW4 (Krishna River), NW27 (Cumberjua River), NW68 (Mandovi River), and NW94 (Sone River).</p>",
                    solution_hi: "<p>16.(c) <strong>राष्ट्रीय जलमार्ग 2 -</strong> धुबरी से सदिया तक जलमार्ग 891 किमी की दूरी तक फैला हुआ है। भारत में राष्ट्रीय जलमार्ग (NW): NW1 (गंगा-भागीरथी-हुगली नदी प्रणाली), NW16 (बराक नदी), NW4 (कृष्णा नदी), NW27 (कंबरजुआ नदी), NW68 (मंडोवी नदी), और NW94 (सोन नदी) ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which of the following channels is NOT a part of six communication mail channels to<br>facilitate quick delivery of mails in large towns and cities of India?</p>",
                    question_hi: "<p>17. निम्नलिखित में से कौन-सा चैनल भारत के बड़े कस्बों और शहरों में मेल (mails) की त्वरित डिलीवरी की सुविधा के लिए छ: संचार मेल चैनलों का भाग नहीं है?</p>",
                    options_en: ["<p>Periodical channel</p>", "<p>Business channel</p>", 
                                "<p>Weekly channel</p>", "<p>Metro channel</p>"],
                    options_hi: ["<p>आवधिक चैनल (Periodical channel)</p>", "<p>बिजनेस चैनल (Business channel)</p>",
                                "<p>साप्ताहिक चैनल (Weekly channel)</p>", "<p>मेट्रो चैनल (Metro channel)</p>"],
                    solution_en: "<p>17.(c) <strong>Weekly channel.</strong> Six Mail Channels: Rajdhani Channel - It uses yellow mailboxes. Metro Channel- It uses blue mailboxes. Green Channel- It uses green mailboxes. Business Channel- It caters to bulk business mail. Bulk Mail Channel- It handles large volumes of mail, often for promotional purposes, at discounted rates. Periodical Channel- It is dedicated to the delivery of newspapers and magazines.</p>",
                    solution_hi: "<p>17.(c) <strong>साप्ताहिक चैनल। </strong>छह मेल चैनल: राजधानी चैनल- यह पीले मेलबॉक्स का उपयोग करता है। मेट्रो चैनल- यह नीले मेलबॉक्स का उपयोग करता है। ग्रीन चैनल- यह हरे मेलबॉक्स का उपयोग करता है। बिजनेस चैनल- यह थोक बिजनेस मेल को पूरा करता है। बल्क मेल चैनल- यह अक्सर प्रचार उद्देश्यों के लिए रियायती दरों पर बड़ी मात्रा में मेल संभालता है। आवधिक चैनल- यह समाचार पत्रों और पत्रिकाओं के वितरण के लिए समर्पित है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which common long-term infection develops when bacteria in the mouth metabolise<br>sugars to produce acids that demineralise the hard tissues of teeth?</p>",
                    question_hi: "<p>18. कौन सा सामान्य दीर्घकालिक संक्रमण तब विकसित होता है जब मुंह में जीवाणु शर्करा का उपापचय करके अम्ल का उत्पादन करता है जो दांतों के कठोर ऊतकों को अखनिजीकृत करता है ?</p>",
                    options_en: ["<p>Dental caries</p>", "<p>Cleft palate</p>", 
                                "<p>Oro-dental trauma</p>", "<p>Oral thrush</p>"],
                    options_hi: ["<p>दंत क्षरण</p>", "<p>खंड तालु</p>",
                                "<p>ओरो-दंत आघात</p>", "<p>मुख व्रण</p>"],
                    solution_en: "<p>18.(a) <strong>Dental caries. </strong>Cleft palate: Congenital birth defect, roof of the mouth doesn\'t fully close during development. Oro-dental trauma: This refers to injuries to the teeth and mouth due to accidents or falls. Oral thrush: Fungal infection by Candida albicans, mainly affects tongue and inner cheeks.</p>",
                    solution_hi: "<p>18.(a)<strong> दंत क्षरण। </strong>खंड तालु (cleft palate): जन्मजात जन्म दोष, विकास के दौरान मुख की तालु पूरी तरह से बंद नहीं होती है। ओरो-दंत आघात: यह दुर्घटनाओं या गिरने के कारण दांतों और मुंह में लगी चोटों को संदर्भित करता है। मुख व्रण (oral thrush): कैंडिडा अल्बिकन्स द्वारा फंगल संक्रमण, मुख्य रूप से जिह्वा और गालों के आंतरिक हिस्से को प्रभावित करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Dehing Patkai festival is the festival to celebrate the rich culture of which of the<br>following Indian states?</p>",
                    question_hi: "<p>19. देहिंग पटकाई त्योहार को निम्नलिखित में से किस भारतीय राज्य की समृद्ध संस्कृति के जश्न के रूप में मनाया जाता है ?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Sikkim</p>", 
                                "<p>Tripura</p>", "<p>Assam</p>"],
                    options_hi: ["<p>नागालैण्ड</p>", "<p>सिक्किम</p>",
                                "<p>त्रिपुरा</p>", "<p>असम</p>"],
                    solution_en: "<p>19.(d) <strong>Assam.</strong> \"Dehing Patkai Festival\" - The Dihing Patkai Festival is a once-a-year festival held at Lekhapani in Tinsukia district of Assam. The festival is named after the majestic Patkai range and the mischievous Dihing River. Other Festivals in Assam - Bihu, Ambubachi Mela, Majuli Festival, and Junbeel Mela.</p>",
                    solution_hi: "<p>19.(d) <strong>असम। </strong>\"देहिंग पटकई महोत्सव\" - दिहिंग पटकई महोत्सव असम के तिनसुकिया जिले के लेखापानी में साल में एक बार आयोजित होने वाला त्योहार है। इस उत्सव का नाम राजसी पटकाई पर्वतमाला और शरारती दिहिंग नदी के नाम पर रखा गया है। असम में अन्य त्यौहार - बिहू, अंबुबाची मेला, माजुली महोत्सव और जुनबील मेला।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. The Sikhs under Guru Hargobind defeated the Mughals four times during the reign of_____</p>",
                    question_hi: "<p>20. गुरु हरगोबिंद के नेतृत्व में सिखों ने ___________ के शासन काल के दौरान मुगलों को चार बार पराजित किया।</p>",
                    options_en: ["<p>Aurangzeb</p>", "<p>Jahangir</p>", 
                                "<p>Akbar</p>", "<p>Shah Jahan</p>"],
                    options_hi: ["<p>औरंगजेब</p>", "<p>जहाँगीर</p>",
                                "<p>अकबर</p>", "<p>शाहजहाँ</p>"],
                    solution_en: "<p>20.(d) <strong>Shah Jahan. </strong>Guru Hargobind, the sixth Sikh Guru, established the Akal Takht and adopted a militaristic stance in response to the execution of his father, Guru Arjan Dev, by Jahangir.</p>",
                    solution_hi: "<p>20.(d) <strong>शाहजहाँ। </strong>छठे सिख गुरु, गुरु हरगोबिंद ने अकाल तख्त की स्थापना की और जहांगीर द्वारा अपने पिता, गुरु अर्जन देव की फांसी के जवाब में सैन्यवादी रुख अपनाया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. In which part of the Constitution of India is the procedure for amendments in the<br>Constitution laid down?</p>",
                    question_hi: "<p>21. भारतीय संविधान के किस भाग में संविधान में संशोधन की प्रक्रिया निर्धारित की गई है?</p>",
                    options_en: ["<p>Part XV</p>", "<p>Part XXI</p>", 
                                "<p>Part XX</p>", "<p>Part X</p>"],
                    options_hi: ["<p>भाग XV</p>", "<p>भाग XXI</p>",
                                "<p>भाग XX</p>", "<p>भाग X</p>"],
                    solution_en: "<p>21.(c) <strong>Part XX.</strong> Article 368 of the Indian Constitution allows two types of amendments: <br>Amendments requiring a special majority of Parliament and Amendments requiring a special majority of Parliament and ratification by the states.</p>",
                    solution_hi: "<p>21.(c)<strong> भाग XX. </strong>भारतीय संविधान का अनुच्छेद 368 दो प्रकार के संशोधनों की अनुमति देता है: संशोधनों के लिए संसद के विशेष बहुमत की आवश्यकता होती है और संशोधनों के लिए संसद के विशेष बहुमत और राज्यों के अनुसमर्थन की आवश्यकता होती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. What is the air temperature at tropopause over the equator?</p>",
                    question_hi: "<p>22. भूमध्य रेखा के ऊपर क्षोभसीमा पर वायु का तापमान कितना होता है?</p>",
                    options_en: ["<p>- 60&deg;C</p>", "<p>- 50&deg;C</p>", 
                                "<p>- 80&deg;C</p>", "<p>- 70&deg;C</p>"],
                    options_hi: ["<p>- 60&deg;C</p>", "<p>- 50&deg;C</p>",
                                "<p>- 80&deg;C</p>", "<p>- 70&deg;C</p>"],
                    solution_en: "<p>22.(c) <strong>- 80&deg;C.</strong> The tropopause is the boundary between the troposphere (the lowest layer of the atmosphere) and the stratosphere (the layer above). Tropopause - It ranges in height from 8 km near the poles up to 18 km above the equator.</p>",
                    solution_hi: "<p>22.(c)<strong> - 80&deg;C</strong>। क्षोभसीमा क्षोभमंडल (वायुमंडल की सबसे निचली परत) और समतापमंडल (ऊपर की परत) के बीच की सीमा है। ट्रोपोपॉज़ - इसकी ऊंचाई ध्रुवों के पास 8 किमी से लेकर भूमध्य रेखा के ऊपर 18 किमी तक होती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Who among the following is called the \'Bhajan Samrat\' of India?</p>",
                    question_hi: "<p>23. निम्नलिखित में से किसे भारत का \'भजन सम्राट\' कहा जाता है?</p>",
                    options_en: ["<p>Anup Jalota</p>", "<p>Dayanand Prajapati</p>", 
                                "<p>Sanjeev Abhyankar</p>", "<p>Vishnu Bharnagar</p>"],
                    options_hi: ["<p>अनूप जलोटा</p>", "<p>दयानन्द प्रजापति</p>",
                                "<p>संजीव अभ्यंकर</p>", "<p>विष्णु भरनगर</p>"],
                    solution_en: "<p>23.(a) <strong>Anup Jalota. </strong>He was born in Nainital, Uttarakhand and received Padma Shri (2012). Sanjeev Abhyankar is a Hindustani classical music vocalist of the Mewati Gharana.</p>",
                    solution_hi: "<p>23.(a) <strong>अनूप जलोटा।</strong> उनका जन्म नैनीताल, उत्तराखंड में हुआ था और उन्हें पद्म श्री (2012) मिला था। संजीव अभ्यंकर मेवाती घराने के हिंदुस्तानी शास्त्रीय संगीत गायक हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. In which of the following States was the 44th Chess Olympiad held?</p>",
                    question_hi: "<p>24. निम्नलिखित में से किस राज्य में 44वाँ शतरंज ओलंपियाड आयोजित किया गया था ?</p>",
                    options_en: ["<p>Tamil Nadu</p>", "<p>Andhra Pradesh</p>", 
                                "<p>Kerala</p>", "<p>Kamataka</p>"],
                    options_hi: ["<p>तमिलनाडु</p>", "<p>आंध्र प्रदेश</p>",
                                "<p>केरल</p>", "<p>कर्नाटक</p>"],
                    solution_en: "<p>24.(a) <strong>Tamil Nadu. </strong>The 44th Chess Olympiad was held in Chennai from July 28 to August 9, 2022. The prestigious competition, which has been organized since 1927, is being hosted in India for the first time and in Asia after 30 years. Winner country - Uzbekistan.</p>",
                    solution_hi: "<p>24.(a) <strong>तमिलनाडु। </strong>44वां शतरंज ओलंपियाड 28 जुलाई से 9 अगस्त, 2022 तक चेन्नई में आयोजित किया गया था। 1927 से आयोजित होने वाली यह प्रतिष्ठित प्रतियोगिता पहली बार भारत में और 30 वर्षों के बाद एशिया में आयोजित की गई थी। विजेता देश - उजबेकिस्तान।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Mame Khan, an Indian folk singer known for his Sufi and folk music performances, is from the state of_______</p>",
                    question_hi: "<p>25. अपने सूफी और लोक संगीत गायन के लिए विख्&zwj;यात भारतीय लोक गायक मामे खान किस राज्य से हैं?</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Punjab</p>", 
                                "<p>Assam</p>", "<p>Gujarat</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>पंजाब</p>",
                                "<p>असम</p>", "<p>गुजरात</p>"],
                    solution_en: "<p>25.(a) <strong>Rajasthan. </strong>Mame Khan received the Global Indian Music Academy Awards (2016), Sangeet Natak Akademi (2023).</p>",
                    solution_hi: "<p>25.(a) <strong>राजस्थान। </strong>मामे खान को ग्लोबल इंडियन म्यूजिक एकेडमी अवार्ड्स (2016), संगीत नाटक अकादमी (2023) मिला।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Globalisation, by connecting countries, shall result in ______",
                    question_hi: "26. देशों को जोड़ने से भूमंडलीकरण का परिणाम ________ होगा।",
                    options_en: [" price enhancement by producers ", " no change in competition among producers ", 
                                " lesser competition among producers ", " greater competition among producers<br /> "],
                    options_hi: [" उत्पादकों द्वारा मूल्य वृद्धि ", " उत्पादकों के बीच प्रतिस्पर्धा में कोई बदलाव नहीं ",
                                " उत्पादकों के बीच कम प्रतिस्पर्धा ", " उत्पादकों के बीच अधिक प्रतिस्पर्धा"],
                    solution_en: "26.(d) Globalization breaks down barriers between economies, allowing producers from different countries to compete for the same customers. This increased competition drives several effects: Lower prices, Higher quality, Greater variety, and Innovation.",
                    solution_hi: "26.(d) भूमंडलीकरण अर्थव्यवस्थाओं के बीच की बाधाओं को तोड़ता है, जिससे विभिन्न देशों के उत्पादकों को समान ग्राहकों के लिए प्रतिस्पर्धा करने की अनुमति मिलती है। यह बढ़ी हुई प्रतिस्पर्धा कई प्रभावों को जन्म देती है: कम कीमतें, उच्च गुणवत्ता, अधिक विविधता, और नवीनता।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which Article of the Indian Constitution mentions that it shall be the duty of every<br>citizen of India to have compassion for living creatures?</p>",
                    question_hi: "<p>27. भारतीय संविधान के किस अनुच्छेद मेंउल्लेख है कि जीवित प्राणियों के प्रति दया रखना भारत के प्रत्येक नागरिक का कर्तव्य होगा?</p>",
                    options_en: ["<p>51 A (h)</p>", "<p>51 A (g)</p>", 
                                "<p>51 A (j)</p>", "<p>51 A (i)</p>"],
                    options_hi: ["<p>51 A (h)</p>", "<p>51 A (g)</p>",
                                "<p>51 A (j)</p>", "<p>51 A (i)</p>"],
                    solution_en: "<p>27.(b) <strong>51 A(g). </strong>Fundamental Duties (Under Part IV A): Article 51 A(a) - Abide by the Constitution and respect national flag and National Anthem, Article 51 A(b) - Follow ideals of the freedom struggle, Article 51 A(h) - Develop scientific temper, Article 51 A(i) - Safeguard public property, 51 A(j) Strive for excellence.</p>",
                    solution_hi: "<p>27.(b) <strong>51 A(g)।</strong> मौलिक कर्तव्य (भाग IV A के तहत): अनुच्छेद 51 A(a) - संविधान का पालन करना और राष्ट्रीय ध्वज और राष्ट्रगान का सम्मान करना, अनुच्छेद 51 A(b) - स्वतंत्रता संग्राम के आदर्शों का पालन करना, अनुच्छेद 51 A(h) - वैज्ञानिक दृष्टिकोण विकसित करना, अनुच्छेद 51 A (i) - सार्वजनिक संपत्ति की रक्षा करना, अनुच्छेद 51 A(j) उत्कृष्टता के लिए प्रयास करना।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following was one of the five states formed by breaking up the Bahmani<br>kingdom?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सा बहमनी साम्राज्य को तोड़कर बनाए गए पाँच राज्यों में से एक था?</p>",
                    options_en: ["<p>Karur</p>", "<p>Golkonda</p>", 
                                "<p>Hyderabad</p>", "<p>Madras</p>"],
                    options_hi: ["<p>करूर</p>", "<p>गोलकोंडा</p>",
                                "<p>हैदराबाद</p>", "<p>मद्रास</p>"],
                    solution_en: "<p>28.(b) <strong>Golkonda.</strong> The Bahmani Kingdom, founded in 1347 by Alauddin Hasan Bahman Shah (Hasan Gangu), emerged through a revolt against the Delhi Sultanate of Muhammad bin Tughlaq. Bahmani kingdom was disintegrated into five Independent states: Nizamshahi of Ahmednagar, Qutb Shahi of Golconda, Barid Shahi of Bidar, Imad Shahi of Berar, and Adil Shahi of Bijapur.</p>",
                    solution_hi: "<p>28.(b)<strong> गोलकुंडा।</strong> 1347 में अलाउद्दीन हसन बहमन शाह (हसन गंगू) द्वारा स्थापित बहमनी साम्राज्य, मुहम्मद बिन तुगलक की दिल्ली सल्तनत के खिलाफ विद्रोह के माध्यम से उभरा। बहमनी साम्राज्य पाँच स्वतंत्र राज्यों में विभाजित हो गया: अहमदनगर के निज़ामशाही, गोलकुंडा के कुतुब शाही, बीदर के बारीद शाही, बरार के इमाद शाही, और बीजापुर के आदिल शाही।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following topped the medals tally, winning 61 gold medals in the National<br>Games-2022?</p>",
                    question_hi: "<p>29. निम्नलिखित में से किसने, राष्ट्रीय खेल-2022 में 61 स्वर्ण पदक जीतकर पदक-तालिका में शीर्ष स्थान प्राप्त किया?</p>",
                    options_en: ["<p>Services Sports Control Board</p>", "<p>Kerala</p>", 
                                "<p>Haryana</p>", "<p>Maharashtra</p>"],
                    options_hi: ["<p>सेवा खेल नियंत्रण बोर्ड</p>", "<p>केरल</p>",
                                "<p>हरियाणा</p>", "<p>महाराष्ट्र</p>"],
                    solution_en: "<p>29.(a) <strong>Services Sports Control Board. </strong>36th National Games 2022: Host - In Gujarat (Ahmedabad, Gandhinagar, Surat, Vadodara, Rajkot and Bhavnagar) and Track cycling event was held in New delhi. Mascot - &ldquo;SAVAJ&rdquo;. 37th National Games 2023 - in Goa and 38th National Games 2024 - Uttarakhand.</p>",
                    solution_hi: "<p>29.(a) <strong>सेवा खेल नियंत्रण बोर्ड।</strong> 36वें राष्ट्रीय खेल 2022: मेजबान- गुजरात (अहमदाबाद, गांधीनगर, सूरत, वडोदरा, राजकोट और भावनगर) में और ट्रैक साइक्लिंग कार्यक्रम नई दिल्ली में आयोजित किया गया था। शुभंकर - सवाज (SAVAJ)। 37वें राष्ट्रीय खेल 2023- गोवा में और 38वें राष्ट्रीय खेल 2024- उत्तराखंड में।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. What is the standard distance for males in a hurdle event?</p>",
                    question_hi: "<p>30. बाधा दौड़ में पुरुषों के लिए मानक दूरी कितनी होती है?</p>",
                    options_en: ["<p>110 m and 410 m</p>", "<p>100 m and 400 m</p>", 
                                "<p>120 m and 410 m</p>", "<p>110 m and 400 m</p>"],
                    options_hi: ["<p>110 m और 410 m</p>", "<p>100 m और 400 m</p>",
                                "<p>120 m और 410 m</p>", "<p>110 m और 400 m</p>"],
                    solution_en: "<p>30.(d)<strong> 110 m and 400 m. </strong>The distance for Women in a hurdle event- 100 meters and 400 meters. World Athletics list of sports: Sprints (100m, 200m, 400m), Middle/Long (800m, 1500m, 5000m, 10000m, 3000m steeplechase), Race Walks (20k race walk), Relays (4x100 relay, 4x400 relay).</p>",
                    solution_hi: "<p>30.(d) <strong>110 m और 400 m।</strong> बाधा दौड़ में महिलाएं- 100 मीटर और 400 मीटर। विश्व एथलेटिक्स खेलों की सूची: स्प्रिंट (100 मीटर, 200 मीटर, 400 मीटर), मध्य/लंबा (800 मीटर, 1500 मीटर, 5000 मीटर, 10000 मीटर, 3000 मीटर स्टीपलचेज़), रेस वॉक (20 किमी रेस वॉक), रिले (4x100 रिले, 4x400 रिले)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following is a book written by Jyotirao Phule in 1873?</p>",
                    question_hi: "<p>31. निम्नलिखित में से कौन-सी पुस्तक 1873 में ज्योतिराव फुले ने लिखी थी?</p>",
                    options_en: ["<p>Tribe Caste and Society</p>", "<p>Last Among Equals</p>", 
                                "<p>Gulamgiri</p>", "<p>Jati ka Vinash</p>"],
                    options_hi: ["<p>ट्राइब कास्ट एंड सोसायटी</p>", "<p>लास्ट अमंग इक्वल्स</p>",
                                "<p>गुलामगिरी</p>", "<p>जाति का विनाश</p>"],
                    solution_en: "<p>31.(c)<strong> Gulamgiri. </strong>The book critiques the caste system, comparing the oppression of lower castes in India to the slavery of black people in America. Jyotiba Phule founded the Satyashodhak Samaj (Truth Seekers Society) in 1873. &ldquo;Caste and Race in India&rdquo; was written by G.S.Ghurye.</p>",
                    solution_hi: "<p>31.(c) <strong>गुलामगिरी। </strong>यह पुस्तक जाति व्यवस्था की आलोचना करती है और भारत में निचली जातियों के उत्पीड़न की तुलना अमेरिका में काले लोगों की गुलामी से करती है। ज्योतिबा फुले ने 1873 में सत्यशोधक समाज (सत्य शोधक समाज) की स्थापना की। \"कास्ट एंड रेस इन इंडिया\" जी.एस.घुर्ये द्वारा लिखी गई थी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Becquerel, which is equal to one disintegration per second (dps) is represented by<br>which symbol?</p>",
                    question_hi: "<p>32. बेकरेल, जो एक विघटन प्रति सेकेंड (dps) के बराबर है, को किस प्रतीक द्वारा दर्शाया जाता है?</p>",
                    options_en: ["<p>Bq</p>", "<p>BI</p>", 
                                "<p>Br</p>", "<p>Bc</p>"],
                    options_hi: ["<p>Bq</p>", "<p>BI</p>",
                                "<p>Br</p>", "<p>Bc</p>"],
                    solution_en: "<p>32.(a) <strong>Bq (becquerel).</strong> It was named after French physicist Henri Becquerel, who discovered radioactivity in 1896. In 1903, he shared the Nobel Prize for Physics with Pierre and Marie Curie.</p>",
                    solution_hi: "<p>32.(a) <strong>Bq (बैकेरल)।</strong> इसका नाम फ्रांसीसी भौतिक विज्ञानी हेनरी बैकेरल के नाम पर रखा गया था, जिन्होंने 1896 में रेडियोधर्मिता की खोज की थी। 1903 में, उन्होंने पियरे और मैरी क्यूरी के साथ भौतिकी के लिए नोबेल पुरस्कार साझा किया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "33. To which genus does the nematode parasite called hookworm belong, which usually<br />spreads through infected soil?",
                    question_hi: "<p>33. अंकुशकृमि (हुकवर्म) नामक सूत्रकृमि परजीवी किस प्रजाति से संबंधित है, जो आमतौर पर संक्रमित मिट्टी से फैलते हैं?</p>",
                    options_en: ["<p>Fasciola</p>", "<p>Ancylostoma</p>", 
                                "<p>Hirudinaria</p>", "<p>Pheretima</p>"],
                    options_hi: ["<p>फेसिओला</p>", "<p>ऐन्किलोस्टोमा</p>",
                                "<p>हिरुडिनेरिया</p>", "<p>फेरेटिमा</p>"],
                    solution_en: "<p>33.(b) <strong>Ancylostoma. Fasciola: </strong>This genus belongs to the flatworm phylum, commonly known as liver flukes. Hirudinaria: This genus encompasses leeches, which are segmented annelids rather than nematodes. Pheretima: This genus includes earthworms, which are beneficial detritivores in the soil ecosystem.</p>",
                    solution_hi: "<p>33.(b) <strong>ऐन्किलोस्टोमा। </strong>फेसिओला: यह प्रजाति फ्लैटवर्म फ़ाइलम से संबंधित है, जिसे आमतौर पर लिवर फ्लूक्स के रूप में जाना जाता है। हिरुडिनेरिया: इस प्रजाति में जोंक शामिल हैं, जो नेमाटोड के बजाय खंडित एनेलिड्स हैं। फेरेटिमा: इस प्रजाति में केंचुए शामिल हैं, जो मृदा पारिस्थितिकी तंत्र में लाभकारी अपघटक पदार्थ हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Under which of the following Acts can supreme court initiate International Commercial<br>Arbitration?</p>",
                    question_hi: "<p>34. निम्नलिखित में से किस अधिनियम के तहत सर्वोच्च न्यायालय अंतरराष्ट्रीय वाणिज्यिक मध्यस्थता शुरू कर सकता है?</p>",
                    options_en: ["<p>International Arbitration Act, 2012</p>", "<p>Arbitration and Cancelations Act, 2004</p>", 
                                "<p>Arbitration and Conciliation Act, 1996</p>", "<p>United Nations Act on justice, 2015</p>"],
                    options_hi: ["<p>अंतरराष्ट्रीय मध्यस्थता अधिनियम, 2012</p>", "<p>मध्यस्थता और रद्दीकरण अधिनियम, 2004</p>",
                                "<p>मध्यस्थता और सुलह अधिनियम, 1996</p>", "<p>न्याय पर संयुक्त राष्ट्र अधिनियम, 2015</p>"],
                    solution_en: "<p>34.(c) <strong>Arbitration and Conciliation Act, 1996. </strong>Indian arbitration is governed and regulated by the Arbitration and Conciliation Act 1996 (which is amended in 2015, 2019 and 2021). Arbitration aims to provide speedy, efficient, and binding resolution of disputes that have arisen between the parties.</p>",
                    solution_hi: "<p>34.(c) <strong>मध्यस्थता और सुलह अधिनियम, 1996। </strong>भारतीय मध्यस्थता, मध्यस्थता और सुलह अधिनियम 1996 (जिसे 2015, 2019 और 2021 में संशोधित किया गया है) द्वारा शासित और विनियमित है। मध्यस्थता का उद्देश्य पक्षों के बीच उत्पन्न विवादों का त्वरित, कुशल और बाध्यकारी समाधान प्रदान करना है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Tax rates on higher income groups have been increased resulting in __________.</p>",
                    question_hi: "<p>35. उच्च आय वर्ग के लोगों पर कर की दरों में वृद्धि की गई है, जिसके परिणाम स्वरूप __________ ।</p>",
                    options_en: ["<p>economic stability</p>", "<p>reducing inequalities of income and wealth</p>", 
                                "<p>economic growth</p>", "<p>reducing regional disparities</p>"],
                    options_hi: ["<p>आर्थिक स्थिरता आई है</p>", "<p>आय और धन की असमानताएं कम हुई हैं</p>",
                                "<p>आर्थिक विकास हुआ है</p>", "<p>क्षेत्रीय विषमताएं कम हुई हैं</p>"],
                    solution_en: "<p>35.(b) <strong>reducing inequalities of income and wealth.</strong> Income inequality is the uneven distribution of income in a population, often accompanied by wealth inequality. Progressive taxation is one of the ways of Redistribution of income and redistribution of wealth in any economy. A progressive tax is a tax in which the tax rate increases as the taxable amount increases (means higher tax rates on higher income groups).</p>",
                    solution_hi: "<p>35.(b) <strong>आय और धन की असमानताएं कम हुई हैं ।</strong> आय असमानता किसी जनसंख्या में आय का असमान वितरण है, जिसके साथ अक्सर धन असमानता भी जुड़ी होती है। प्रगतिशील कराधान किसी भी अर्थव्यवस्था में आय के पुनर्वितरण और धन के पुनर्वितरण के तरीकों में से एक है। प्रगतिशील कर एक ऐसा कर है जिसमें कर योग्य राशि बढ़ने पर कर की दर बढ़ जाती है (अर्थात अधिक आय वर्गों पर अधिक कर दरें)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. According to Census of India 2011, which of the following states has a population of more than 10 crores?</p>",
                    question_hi: "<p>36. भारतीय जनगणना 2011 के अनुसार, निम्नलिखित में से किस राज्य की जनसंख्या 10 करोड़ से अधिक है?</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Rajasthan</p>", 
                                "<p>West Bengal</p>", "<p>Andhra Pradesh</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>राजस्थान</p>",
                                "<p>पश्चिम बंगाल</p>", "<p>आंध्र प्रदेश</p>"],
                    solution_en: "<p>36.(a) <strong>Maharashtra. </strong>Census of India 2011. Total Population (India) - 1.21 billion. Population in States: Uttar Pradesh (19 crores), Maharashtra (11 crores), Bihar (10 crores), West Bengal (9 crores), Andhra Pradesh (8 crore), Sikkim (6 lakh), Lakshadweep (64 thousand).</p>",
                    solution_hi: "<p>36.(a) <strong>महाराष्ट्र। </strong>भारत की जनगणना 2011। कुल जनसंख्या (भारत) - 1.21 अरब । राज्यों में जनसंख्या: उत्तर प्रदेश (19 करोड़), महाराष्ट्र (11 करोड़), बिहार (10 करोड़), पश्चिम बंगाल (9 करोड़), आंध्र प्रदेश (8 करोड़), सिक्किम (6 लाख), लक्षद्वीप (64 हजार)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. In the year 1904, who formed a secret organisation of revolutionaries named Abhinav Bharat?</p>",
                    question_hi: "<p>37. वर्ष 1904 में अभिनव भारत नामक क्रांतिकारियों का एक गुप्त संगठन किस ने बनाया था ?</p>",
                    options_en: ["<p>Lala Har Dayal</p>", "<p>Subhash Chandra Bose</p>", 
                                "<p>Ajit Singh</p>", "<p>Vinayak Damodar Savarkar</p>"],
                    options_hi: ["<p>लाला हर दयाल</p>", "<p>सुभाष चंद्र बोस</p>",
                                "<p>अजीत सिंह</p>", "<p>विनायक दामोदर सावरकर</p>"],
                    solution_en: "<p>37.(d)<strong> Vinayak Damodar Savarkar.</strong> His books: &ldquo;My Transportation For Life&rdquo;, &ldquo;Hindurashtra Darshan&rdquo;, &ldquo;Six Glorious Epochs of Indian History&rdquo;, &ldquo;Hindutva: Who is a Hindu?&rdquo;. Organizations and their founders: Indian Association (Surendranath Banerjee and Ananda Mohan Bose), Servants of Indian Society (Gopal Krishna Gokhle), Swarajya Party (Motilal Nehru and Chittaranjan Das), Sharda Sadan (Rama Bai), Bahishkrit Hitkarini Sabha (B.R. Ambedkar).</p>",
                    solution_hi: "<p>37.(d) <strong>विनायक दामोदर सावरकर।</strong> उनकी पुस्तकें: \"माई ट्रांसपोर्टेशन फॉर लाइफ\", \"हिंदू राष्ट्र दर्शन\", \"सिक्स ग्लोरियस एपोच्स ऑफ इंडियन हिस्ट्री\",\"हिंदुत्व: हिंदू कौन है?\"। संगठन और उनके संस्थापक: इंडियन एसोसिएशन (सुरेंद्रनाथ बनर्जी और आनंद मोहन बोस), सर्वेंट्स ऑफ इंडियन सोसाइटी (गोपाल कृष्ण गोखले), स्वराज्य पार्टी (मोतीलाल नेहरू और चितरंजन दास), शारदा सदन (रमा बाई), स्वतंत्र श्रमिक पार्टी (बी.आर. अम्बेडकर)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Since which year has the Census become a regular ten yearly exercise in India?</p>",
                    question_hi: "<p>38. किस वर्ष से भारत में जनगणना हर दस वर्ष में होने वाली एक नियमित प्रक्रिया बन गई है?</p>",
                    options_en: ["<p>1951</p>", "<p>1881</p>", 
                                "<p>1961</p>", "<p>1851</p>"],
                    options_hi: ["<p>1951</p>", "<p>1881</p>",
                                "<p>1961</p>", "<p>1851</p>"],
                    solution_en: "<p>38.(b) <strong>1881.</strong> The first synchronous census was taken under British rule in 1881 by W.C. Plowden (Census Commissioner of India). First Non-synchronous Census: It was conducted in India in 1872 during the reign of Governor-General Lord Mayo. Census 2011 was the 15th National Census of the country since 1872 and the 7th after Independence. Henry Walter is known as the father of the Indian Census.</p>",
                    solution_hi: "<p>38.(b) <strong>1881.</strong> ब्रिटिश शासन के तहत पहला समकालीन जनगणना 1881 में डब्ल्यू.सी. प्लोवडेन (भारत के जनगणना आयुक्त) द्वारा की गई। पहली गैर-समकालिक जनगणना: यह भारत में 1872 में गवर्नर-जनरल लॉर्ड मेयो के शासनकाल के दौरान आयोजित की गई थी। जनगणना 2011, 1872 के बाद से देश की 15वीं और आज़ादी के बाद 7वीं राष्ट्रीय जनगणना थी। हेनरी वाल्टर को भारतीय जनगणना के जनक के रूप में जाना जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. &lsquo;The Adventures of Huckleberry Finn&rsquo; was written by which of the following authors?</p>",
                    question_hi: "<p>39. \'द एडवेंचर्स ऑफ हकलबेरी फिन\' निम्नलिखित में से किस लेखक ने लिखा था?</p>",
                    options_en: ["<p>Mark Twain</p>", "<p>Robert Louis Stevenson</p>", 
                                "<p>Jane Austen</p>", "<p>Jerome K Jerome</p>"],
                    options_hi: ["<p>मार्क ट्वेन</p>", "<p>रॉबर्ट लुई स्टीवेन्सन</p>",
                                "<p>जेन ऑस्टेन</p>", "<p>जेरोम के. जेरोम</p>"],
                    solution_en: "<p>39.(a) <strong>Mark Twain. </strong>His Other Books - &ldquo;The Adventures of Tom Sawyer&rdquo;, &ldquo;Pudd\'nhead Wilson&rdquo;, &ldquo;Roughing It&rdquo;, &ldquo;A Horse\'s Tale&rdquo;. Authors and Books: Robert Louis Stevenson - &ldquo;Treasure Island&rdquo; and &ldquo;Kidnapped&rdquo;, Jane Austen - &ldquo;Pride and Prejudice&rdquo; and &ldquo;Sense and Sensibility&rdquo;, Jerome K Jerome - &ldquo;Three Men in a Boat&rdquo; and &ldquo;Three Men on the Bummel&rdquo;.</p>",
                    solution_hi: "<p>39.(a)<strong> मार्क ट्वेन।</strong> उनकी अन्य पुस्तकें - \"द एडवेंचर्स ऑफ टॉम सॉयर\", \"पुडनहेड विल्सन\", \"रफिंग इट\", \"ए हॉर्स टेल\"। लेखक और पुस्तकें: रॉबर्ट लुई स्टीवेन्सन - \"ट्रेजर आइलैंड\" और \"किडनैप्ड\", जेन ऑस्टेन - \"प्राइड एंड प्रेजुडिस\" और \"सेंस एंड सेंसिबिलिटी\", जेरोम के जेरोम - \"थ्री मेन इन ए बोट\" और \"थ्री मेन ऑन द बुमेल\" &rdquo;।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Simuka was the founder of which of the following dynasties of the post Mauryan era?</p>",
                    question_hi: "<p>40. सिमुक, मौर्योत्तर काल के निम्नलिखित में से किस राजवंश का संस्थापक था ?</p>",
                    options_en: ["<p>Kushana</p>", "<p>Parthians</p>", 
                                "<p>Kanva</p>", "<p>Satavahana</p>"],
                    options_hi: ["<p>कुषाण</p>", "<p>पार्थियन</p>",
                                "<p>कण्व</p>", "<p>सातवाहन</p>"],
                    solution_en: "<p>40.(d) <strong>Satavahana.</strong> Indian Dynasties and their Founders: Haryanka Dynasty (Bimbisara), Nanda Dynasty (Mahapadma Nanda), Kanva Dynasty (Vasudeva Kanva), Gahadwal dynasty (Chandradev), Sayyid dynasty (Khizr Khan), Kushan Dynasty (Kujala Kadphises), Chandela Dynasty (Nannuk).</p>",
                    solution_hi: "<p>40.(d) <strong>सातवाहन। </strong>भारतीय राजवंश और उनके संस्थापक: हर्यंक राजवंश (बिम्बिसार), नंद राजवंश (महापद्म नंद), कण्व राजवंश (वासुदेव कण्व), गहड़वाल राजवंश (चंद्रदेव), सैय्यद राजवंश (खिज्र खान), कुषाण राजवंश (कुजल कडफिसेस), चंदेल राजवंश (नन्नुक)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "41. Green Revolution is an example of: ",
                    question_hi: "41. हरित क्रांति किसका उदाहरण है?",
                    options_en: [" how the service sector can bring about a revolution in India  ", " how international trade ban can bring changes in the agriculture produce  ", 
                                " how green GDP can be increased with the agricultural revolution  ", " how technology can bring revolutionary changes in agricultural output"],
                    options_hi: [" सेवा क्षेत्र भारत में कैसे क्रांति ला सकता है ", " अंतरराष्ट्रीय व्यापार प्रतिबंध कृषि उपज में कैसे बदलाव ला सकता है ",
                                " कृषि क्रांति से हरित जीडीपी (GDP) को कैसे बढ़ाया जा सकता है ", " प्रौद्योगिकी कृषि उत्पादन में क्रांतिकारी परिवर्तन कैसे ला सकती है"],
                    solution_en: "41.(d) The Green Revolution was a period that began in the 1960s and it involved the adoption of high-yielding varieties (HYVs) of wheat and rice to boost food crop production, particularly in India. Father of green revolution in india- M.S.Swaminathan. Father of the green revolution in the world - Norman Borlaug. First person to use the word Green Revolution - William S. Gaud. ",
                    solution_hi: "41.(d) हरित क्रांति एक ऐसी अवधि थी जो 1960 के दशक में शुरू हुई थी और इसमें विशेष रूप से भारत में खाद्य फसलों की उत्पादन बढ़ाने के लिए गेहूं और चावल के उच्च उत्पादक वैशिष्ट्यों (HYVs) को अपनाना शामिल था। भारत में हरित क्रांति के जनक - एम.एस.स्वामीनाथन। हरित क्रांति के जनक (विश्व में) - नॉर्मन बोरलॉग। हरित क्रांति शब्द का प्रयोग करने वाले प्रथम व्यक्ति - विलियम एस गौड। ",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The Red Fort was commissioned by which of these emperors?</p>",
                    question_hi: "<p>42. लाल किले का निर्माण निम्न में से किस सम्राट ने करवाया था?</p>",
                    options_en: ["<p>Qutb-ud-din Bakhtiyar Khilji</p>", "<p>Shah Jahan</p>", 
                                "<p>Jahangir</p>", "<p>Mohammad Bin Tughlaq</p>"],
                    options_hi: ["<p>कुतुबुद्दीन बख्तियार खिलजी</p>", "<p>शाहजहाँ</p>",
                                "<p>जहांगीर</p>", "<p>मोहम्मद बिन तुगलक</p>"],
                    solution_en: "<p>42.(b) <strong>Shah Jahan.</strong> He was the fifth Mughal Emperor (1627-1658). His architectural Achievements - Taj Mahal (Agra), Red Fort (Delhi), Jama Masjid (Delhi), Shalimar Gardens ( Lahore, Pakistan). Mughal architecture - Humayun&rsquo;s Tomb (Bega Begum or Haji Begum), Agra Fort and Fatehpur Sikri (Akbar), Bibi Ka Maqbara (Aurangzeb), Kabuli Bagh Mosque in Panipat (Babur), Zeenat-ul-Masjid in Delhi (Zeenat-un-Nissa Begum).</p>",
                    solution_hi: "<p>42.(b) <strong>शाहजहाँ। </strong>वह पांचवें मुगल सम्राट (1627-1658) थे। उनकी स्थापत्य उपलब्धियाँ: ताज महल (आगरा), लाल किला (दिल्ली), जामा मस्जिद (दिल्ली), शालीमार गार्डन (लाहौर, पाकिस्तान)। मुगल सम्राटों द्वारा निर्मित: हुमायूँ का मकबरा (बेगा बेगम या हाजी बेगम), आगरा का किला और फ़तेहपुर सीकरी (अकबर), बीबी का मकबरा (औरंगजेब), पानीपत में काबुली बाग मस्जिद (बाबर), दिल्ली में ज़ीनत-उल-मस्जिद (ज़ीनत-उन-निसा बेगम)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which of the following is involved in the movement of lava over or towards the surface of the earth?</p>",
                    question_hi: "<p>43. निम्नलिखित में से कौन-सा विकल्प पृथ्वी की सतह के ऊपर या उसकी ओर लावा के संचलन में शामिल है?</p>",
                    options_en: ["<p>Weathering</p>", "<p>Exogenic process</p>", 
                                "<p>Diastrophism</p>", "<p>Volcanism</p>"],
                    options_hi: ["<p>अपक्षयण</p>", "<p>बहिर्जनिक प्रक्रिया</p>",
                                "<p>पटलविरूपण</p>", "<p>ज्वालामुखीय घटना</p>"],
                    solution_en: "<p>43.(d) <strong>Volcanism. </strong>Weathering: Breakdown of rocks at Earth\'s surface through physical, chemical, and biological processes. Exogenic processes: External forces like weathering, erosion, and deposition that modify the Earth\'s surface. Diastrophism: Large-scale deformation of the Earth\'s crust caused by internal forces such as plate tectonics, mountain building, and earthquakes.</p>",
                    solution_hi: "<p>43.(d) <strong>ज्वालामुखीय घटना। </strong>अपक्षयण: भौतिक, रासायनिक और जैविक प्रक्रियाओं के माध्यम से पृथ्वी की सतह पर चट्टानों का टूटना। बहिर्जनिक प्रक्रियाएँ: अपक्षय, अपरदन और निक्षेपण जैसी बाहरी शक्तियाँ जो पृथ्वी की सतह को संशोधित करती हैं। पटलविरूपण: प्लेट टेक्टोनिक्स, पर्वत निर्माण और भूकंप जैसी आंतरिक शक्तियों के कारण भू-पर्पटी का बड़े पैमाने पर विरूपण।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following schemes aims to provide generic medicines at reasonable prices to the citizen of the country?</p>",
                    question_hi: "<p>44. निम्नलिखित में से किस योजना का उद्देश्य देश के नागरिकों को उचित मूल्य पर जेनेरिक दवाएं उपलब्ध कराना है ?</p>",
                    options_en: ["<p>Pradhan Mantri Bharatiya Janaushadhi Pariyojana</p>", "<p>Mission Indradhanush</p>", 
                                "<p>Janani Shishu Suraksha Karyakaram</p>", "<p>Universal Immunisation Programme</p>"],
                    options_hi: ["<p>प्रधान मंत्री भारतीय जन औषधि परियोजना</p>", "<p>मिशन इंद्रधनुष</p>",
                                "<p>जननी शिशु सुरक्षा कार्यक्रम</p>", "<p>सार्वभौमिक टीकाकरण कार्यक्रम</p>"],
                    solution_en: "<p>44.(a)<strong> Pradhan Mantri Bharatiya Janaushadhi Pariyojana (PMBJP) </strong>is a campaign launched by the Department of Pharmaceuticals to provide quality medicines at affordable prices to the masses. PMBJP stores have been set up to provide generic drugs, which are available at lesser prices but are equivalent in quality and efficacy as expensive branded drugs.</p>",
                    solution_hi: "<p>44.(a) <strong>प्रधान मंत्री भारतीय जन औषधि परियोजना (PMBJP),</strong> जनता को सस्ती कीमत पर गुणवत्तापूर्ण दवाएं उपलब्ध कराने के लिए फार्मास्यूटिकल्स विभाग द्वारा शुरू किया गया एक अभियान है। जेनेरिक दवाएं उपलब्ध कराने के लिए PMBJP स्टोर स्थापित किए गए हैं, जो कम कीमत पर उपलब्ध हैं लेकिन गुणवत्ता और प्रभावकारिता में महंगी ब्रांडेड दवाओं के बराबर हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Devadasi National Award is given at the dance festival held at the pilgrim centre in the state of _________.</p>",
                    question_hi: "<p>45.देवदासी राष्ट्रीय पुरस्कार, _________ राज्य में तीर्थ केंद्र में आयोजित नृत्य उत्सव में दिया जाता है।</p>",
                    options_en: ["<p>Kerala</p>", "<p>West Bengal</p>", 
                                "<p>Tamil Nadu</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>केरल</p>", "<p>पश्चिम बंगाल</p>",
                                "<p>तमिलनाडु</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>45.(d) <strong>Odisha. </strong>Devadasi national award aimed at recognizing young and talented artists who have a promising career ahead.</p>",
                    solution_hi: "<p>45.(d) <strong>ओडिशा।</strong> देवदासी राष्ट्रीय पुरस्कार का उद्देश्य उन युवा और प्रतिभाशाली कलाकारों को पहचानना है जिनका आगे एक सुनहरा भविष्य है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who among the following emphasised and propagated that &ldquo;the Vedanta is the religion of all and not of the Hindus alone&rdquo;?</p>",
                    question_hi: "<p>46. निम्नलिखित में से किस ने इस बात पर बल दिया और प्रचारित किया कि \"वेदांत सभी का धर्म है न कि केवल हिंदुओं का\"?</p>",
                    options_en: ["<p>Raja Ram Mohan Roy</p>", "<p>Ishwar Chandra Vidyasagar</p>", 
                                "<p>Swami Vivekanand</p>", "<p>Swami Dayanand Saraswati</p>"],
                    options_hi: ["<p>राजा राम मोहन राय</p>", "<p>ईश्वर चंद्र विद्यासागर</p>",
                                "<p>स्वामी विवेकानंद</p>", "<p>स्वामी दयानंद सरस्वती</p>"],
                    solution_en: "<p>46.(c) <strong>Swami Vivekanand.</strong> He was born as Narendranath Dutt in 1863. His Associated Organizations: Ramakrishna Mission (1897), Advaita Ashrama, Vedanta Society. Raja Ram Mohan Roy established Atmiya Sabha (1815), Brahmo Sabha (1828). Ishwarchandra Vidyasagar initiated the concept of widow remarriage. Swami Dayanand Saraswati established Arya Samaj (1875).</p>",
                    solution_hi: "<p>46.(c) <strong>स्वामी विवेकानन्द। </strong>उनका जन्म 1863 में नरेंद्रनाथ दत्त के रूप में हुआ था । संबंधित संगठन: रामकृष्ण मिशन (1897), अद्वैत आश्रम, वेदांत सोसायटी। राजा राममोहन राय ने आत्मीय सभा (1815), ब्रह्म सभा (1828) की स्थापना की। ईश्वरचंद्र विद्यासागर ने विधवा पुनर्विवाह अवधारणा की शुरूआत की। स्वामी दयानंद सरस्वती ने आर्य समाज (1875) की स्थापना की।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. What is the spread of living pteridophytes to narrow geographical regions?</p>",
                    question_hi: "<p>47. सजीव टेरिडोफाइटा का संकीर्ण भौगोलिक क्षेत्रों में किस प्रकार का फैलाव होता है ?</p>",
                    options_en: ["<p>Limited and restricted</p>", "<p>Limited and unrestricted</p>", 
                                "<p>Unlimited and restricted</p>", "<p>Unlimited and unrestricted</p>"],
                    options_hi: ["<p>सीमित और प्रतिबंधित</p>", "<p>सीमित और अप्रतिबंधित</p>",
                                "<p>असीमित और प्रतिबंधित</p>", "<p>असीमित और अप्रतिबंधित</p>"],
                    solution_en: "<p>47.(a) <strong>Limited and restricted. </strong>Pteridophytes are the earliest terrestrial plants with vascular tissues (xylem and phloem). Example - Selaginella, Equisetum, Fern, and Salvinia. They\'re used for medicine, soil binding, and often cultivated as ornamentals.</p>",
                    solution_hi: "<p>47.(a) <strong>सीमित और प्रतिबंधित। </strong>टेरिडोफाइट्स, सबसे शुरुआती स्थलीय पौधे हैं जिनमें संवहनी ऊतक ( जाइलम और फ्लोएम) पाए जाते हैं। उदाहरण - सेलाजिनेला, इक्विसेटम, फर्न, और साल्विनिया। इनका उपयोग दवा, मिट्टी के कटाव को रोकने और अक्सर सजावटी पौधों के रूप में किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. The Rigveda Samhita comprises of _____ books.</p>",
                    question_hi: "<p>48. ऋग्वेद संहिता में _____ पुस्तकें शामिल हैंl</p>",
                    options_en: ["<p>13</p>", "<p>11</p>", 
                                "<p>7</p>", "<p>10</p>"],
                    options_hi: ["<p>13</p>", "<p>11</p>",
                                "<p>7</p>", "<p>10</p>"],
                    solution_en: "<p>48.(d) <strong>10 Mandalas.</strong> In Rigveda, First and tenth Mandalas - youngest and the longest. 3rd Mandal - Gayatri mantra. 9th Mandala - Completely devoted to Soma. 10th mandala- Purusha Sukta which explains the 4 Varnas that were born from the mouth, arms, thighs, and feet of the Brahma or Purusha.</p>",
                    solution_hi: "<p>48.(d) <strong>10 मंडल। </strong>ऋग्वेद में पहला और दसवां मंडल - सबसे छोटा और सबसे लंबा। तीसरा मंडल-गायत्री मंत्र। 9वां मंडल - पूर्णतः सोम को समर्पित। 10वां मंडल - पुरुष सूक्त जो ब्रह्मा या पुरुष के मुख , भुजाओं, जंघों और पैरों से उत्पन्न हुए 4 वर्णों की व्याख्या करता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following states had the least decadal growth in literacy rate, as per census <br>2011?</p>",
                    question_hi: "<p>49. 2011 की जनगणना के अनुसार निम्नलिखित में से किस राज्य में साक्षरता दर में सबसे कम दशकीय वृद्धि थी?</p>",
                    options_en: ["<p>Mizoram</p>", "<p>Tripura</p>", 
                                "<p>Kerala</p>", "<p>Goa</p>"],
                    options_hi: ["<p>मिजोरम</p>", "<p>त्रिपुरा</p>",
                                "<p>केरल</p>", "<p>गोवा</p>"],
                    solution_en: "<p>49.(a) <strong>Mizoram (2.53%).</strong> Census 2011 in India: literacy rate - 74.04% (Male- 82.14%, Female- 65.46%). Percentage increase in literacy rate as per census 2011: Dadra and Nagar Haveli (18.61%), Bihar (14.8%), Tripura (14.03%), Jharkhand(12.85%), Uttar Pradesh (11.41%), Kerala (3.14%),Chandigarh (4.11%).</p>",
                    solution_hi: "<p>49.(a) <strong>मिजोरम (2.53%)।</strong> भारत में जनगणना 2011: साक्षरता दर- 74.04 (पुरुष- 82.14, महिला- 65.46 और % परिवर्तन- 8.66)। 2011 की जनगणना के अनुसार साक्षरता दर में प्रतिशत वृद्धि: दादरा और नगर हवेली (18.61%), बिहार (14.8%), त्रिपुरा (14.03%), झारखंड (12.85%), उत्तर प्रदेश (11.41%), केरल (3.14%), चंडीगढ़ (4.11%)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "50",
                    section: "misc",
                    question_en: "<p>50. Who advises the Governor with respect to proroguing of the session of the State Legislature?</p>",
                    question_hi: "<p>50. राज्य विधानमंडल के सत्र को स्थगित करने के संबंध में राज्यपाल को सलाह कौन देता है ?</p>",
                    options_en: ["<p>Speaker</p>", "<p>Chief Minister</p>", 
                                "<p>Deputy Speaker</p>", "<p>Senior Member of the House</p>"],
                    options_hi: ["<p>विधानसभाध्यक्ष</p>", "<p>मुख्यमंत्री</p>",
                                "<p>विधानसभा-उपाध्यक्ष</p>", "<p>सदन के वरिष्ठ सदस्य</p>"],
                    solution_en: "<p>50.(b) <strong>Chief Minister. </strong>Governor acts on Chief Minister&rsquo;s advice for legislative decisions, aligning with responsible government principles for executive accountability to the legislature. Articles 153 to 167 in Part VI of the Constitution deal with the state executive.</p>",
                    solution_hi: "<p>50.(b) <strong>मुख्यमंत्री।</strong> राज्यपाल विधायी निर्णयों के लिए मुख्यमंत्री की सलाह पर कार्य करते हैं, विधायिका के प्रति कार्यकारी जवाबदेही के लिए जिम्मेदार सरकारी सिद्धांतों के अनुरूप होते हैं। संविधान के भाग VI में अनुच्छेद 153 से 167 राज्य कार्यपालिका से संबंधित हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>