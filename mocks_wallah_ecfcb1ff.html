<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 16</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">16</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 15,
                end: 15
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. With which state is the Nabakalebara festival associated ?</p>",
                    question_hi: "<p>1. नवकलेबारा उत्सव किस राज्य से संबंधित है?</p>",
                    options_en: ["<p>West Bengal</p>", "<p>Odisha</p>", 
                                "<p>Assam</p>", "<p>Sikkim</p>"],
                    options_hi: ["<p>पश्चिम बंगाल</p>", "<p>उड़ीसा</p>",
                                "<p>असम</p>", "<p>सिक्किम</p>"],
                    solution_en: "<p>1.(b) <strong>Odisha. Nabakalebara:</strong> It is an important festival in the Hindu Odia calendar, observed in the Jagannath Temple, Puri. It was first organized in 1575 A.D by Yaduvanshi King Ramachandra Deva.<strong> Other Festivals of Odisha - </strong>Rath Yatra, Durga Puja, Konark Dance Festival, Puri Beach Festival, Kalinga Mahotsav, Ekamra Utsav, etc.</p>",
                    solution_hi: "<p>1.(b) <strong>उड़ीसा। नवकलेबारा: </strong>यह हिंदू ओडिया कैलेंडर में एक महत्वपूर्ण त्योहार है, जो पुरी के जगन्नाथ मंदिर में मनाया जाता है। इसका आयोजन पहली बार 1575 ई. में यदुवंशी राजा रामचन्द्र देव द्वारा किया गया था। <strong>उड़ीसा के अन्य त्यौहार -</strong> रथ यात्रा, दुर्गा पूजा, कोणार्क नृत्य महोत्सव, पुरी बीच महोत्सव, कलिंग महोत्सव, एकाम्र उत्सव, आदि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. What is the Tamil New Year also known as?</p>",
                    question_hi: "<p>2. तमिल नव वर्ष को और किस नाम से जाना जाता है?</p>",
                    options_en: ["<p>Varusha Pirappu</p>", "<p>Ugadi</p>", 
                                "<p>Bestu Varas</p>", "<p>Vishu</p>"],
                    options_hi: ["<p>वरुषा पिरप्पु</p>", "<p>उगादी</p>",
                                "<p>बेस्टु वरास</p>", "<p>विशु</p>"],
                    solution_en: "<p>2.(a) <strong>Varusha Pirappu </strong>(Puthandu):- It is celebrated on the first day of the Chithirai month. <strong>Ugadi or Yugadi </strong>(Samvatsarādi) - New Year\'s Day for the states of Andhra Pradesh, Telangana, and Karnataka in India. <strong>Vishu -</strong> A Hindu festival celebrated in Kerala. <strong>Bestu Varas</strong> (Varsha-Pratipada or Padwa):- Gujarati&rsquo;s New Year celebrated a day after Diwali.</p>",
                    solution_hi: "<p>2.(a) <strong>वरुषा पिरप्पु</strong> (पुथंडु):- यह चिथिराई महीने के पहले दिन मनाया जाता है। <strong>उगादि या युगादि</strong> (संवत्सरादि) - भारत में आंध्र प्रदेश, तेलंगाना और कर्नाटक राज्यों के लिए नव वर्ष का दिन है । <strong>विशु -</strong> केरल में मनाया जाने वाला एक हिंदू त्योहार। <strong>बेस्तु वरस </strong>(वर्ष-प्रतिपदा या पड़वा): - गुजरातियो का नव वर्ष दिवाली के एक दिन बाद मनाया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Famous &ldquo;Sengal Festival &rdquo; is an annual cultural festival organised by which state of North- India?</p>",
                    question_hi: "<p>3. प्रसिद्ध \"सेंगल उत्सव\" उत्तर-भारत के किस राज्य द्वारा आयोजित एक वार्षिक सांस्कृतिक उत्सव है?</p>",
                    options_en: ["<p>Tripura</p>", "<p>Meghalaya</p>", 
                                "<p>Nagaland</p>", "<p>Manipur</p>"],
                    options_hi: ["<p>त्रिपुरा</p>", "<p>मेघालय</p>",
                                "<p>नगालैंड</p>", "<p>मणिपुर</p>"],
                    solution_en: "<p>3.(d) <strong>Manipur. Other festivals of Manipur:</strong> Yaosang, Cheiraoba (The Manipuri New Year), Heikru Hidongba, Kang (Ratha Yatra of Manipur). <strong>Festival and state:</strong> Losoong (Sikkim), Sekrenyi and Hornbill (Nagaland), Bihu (Assam), Kharchi Puja (Tripura), Wangala (Meghalaya), Majuli Festival (Assam), Lui - Ngai - Ni (Nagaland), Dree (Arunachal Pradesh), Moatsu (Nagaland).</p>",
                    solution_hi: "<p>3.(d) <strong>मणिपुर। मणिपुर के अन्य त्योहार:</strong> याओसांग, चेइराओबा (मणिपुरी नव वर्ष), हेइक्रू हिडोंगबा, कांग (मणिपुर की रथ यात्रा)। <strong>त्योहार और राज्य: </strong>लोसूंग (सिक्किम), सेक्रेनी और हॉर्नबिल (नागालैंड), बिहू (असम), खारची पूजा (त्रिपुरा), वांगला (मेघालय), माजुली महोत्सव (असम), लुई - नगाई - नी (नागालैंड), ड्री (अरुणाचल) प्रदेश), मोत्सु (नागालैंड)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The famous Hornbill Festival is unique to which Northeast state of India?</p>",
                    question_hi: "<p>4. प्रसिद्ध हॉर्नबिल महोत्सव भारत के किस पूर्वोत्तर राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Mizoram</p>", "<p>Nagaland</p>", 
                                "<p>Meghalaya</p>", "<p>Assam</p>"],
                    options_hi: ["<p>मिजोरम</p>", "<p>नागालैंड</p>",
                                "<p>मेघालय</p>", "<p>असम</p>"],
                    solution_en: "<p>4.(b) <strong>Nagaland. Hornbill Festival </strong>is celebrated every year in the first week of December. <strong>Other festivals of Nagaland -</strong> Sukrunyi, Moatsu, Naknyulem, Bushu Jiba. <strong>Festivals of Mizoram -</strong> Chapchar Kut, Lyuva Khutla, Anthurium Festival. <strong>Festivals of Meghalaya -</strong> Shad Suk Mynsiem, Nongkrem, Behdienkhlam, Shad Sukra, Wangala. <strong>Festivals of Assam</strong> <strong>- </strong>Me-Dum-Me-Phi, Baishagu, Ambubachi Mela.</p>",
                    solution_hi: "<p>4.(b) <strong>नागालैंड। हॉर्नबिल महोत्सव</strong> प्रत्येक वर्ष दिसंबर के पहले सप्ताह में मनाया जाता है। <strong>नागालैंड के अन्य त्यौहार -</strong> सुकरुनी, मोत्सु, नाकन्यूलेम, बुशू जिबा। <strong>मिजोरम के त्यौहार - </strong>चापचार कुट, ल्युवा खुटला, एंथुरियम महोत्सव। <strong>मेघालय के त्यौहार - </strong>शाद सुक माइन्सिएम, नोंगक्रेम, बेहदीएनखलम, शाद सुक्रा, वांगला। <strong>असम के त्यौहार -</strong> मी-दम-मी-फी, बैशागु, अंबुबाची मेला।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which city of India celebrates the famous Elephant Festival annually on the day of Holi?</p>",
                    question_hi: "<p>5. भारत का कौन सा शहर हर साल होली के दिन प्रसिद्ध हाथी उत्सव मनाता है?</p>",
                    options_en: ["<p>Ajmer</p>", "<p>Jaisalmer</p>", 
                                "<p>Jodhpur</p>", "<p>Jaipur</p>"],
                    options_hi: ["<p>अजमेर</p>", "<p>जैसलमेर</p>",
                                "<p>जोधपुर</p>", "<p>जयपुर</p>"],
                    solution_en: "<p>5.(d) <strong>Jaipur</strong> (Pink City of India). Camel Festival - Bikaner, Hornbill festival - Nagaland, Rath Yatra - Puri, Odisha. Holi is a Hindu festival celebrated in the spring season, usually in the month of March. It signifies the victory of good over evil, the arrival of spring, and the celebration of colors.</p>",
                    solution_hi: "<p>5.(d) <strong>जयपुर</strong> (भारत का गुलाबी शहर)। ऊँट महोत्सव - बीकानेर, हॉर्नबिल महोत्सव - नागालैंड, रथ यात्रा - पुरी, ओडिशा। होली एक हिंदू त्योहार है जो वसंत ऋतु में, आमतौर पर मार्च के महीने में मनाया जाता है। यह बुराई पर अच्छाई की जीत, वसंत के आगमन और रंगों के उत्सव का प्रतीक है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. In which part of India is the festival &lsquo;<strong>Moatsu</strong>&rsquo; celebrated?</p>",
                    question_hi: "<p>6. भारत के किस भाग में \'<strong>मोत्सु</strong>\' उत्सव मनाया जाता है?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Rajasthan</p>", 
                                "<p>Maharashtra</p>", "<p>Goa</p>"],
                    options_hi: ["<p>नागालैंड</p>", "<p>राजस्थान</p>",
                                "<p>महाराष्ट्र</p>", "<p>गोआ</p>"],
                    solution_en: "<p>6.(a)<strong> Nagaland. Moatsu -</strong> A festival celebrated by the <strong>Ao Tribe.</strong> This festival is celebrated after seeds have been sown in the field. <strong>List of Indian Festivals - </strong>Nagaland (Hornbill festival), Mizoram (Chapcharkut Festival), Manipur (Yaoshang, Chavang Kut), Meghalaya (Nongkrem festival, Khasis festival, Wangla), Assam (Ambubachi, Bhogali Bihu, Baishagu, Dehing Patkai).</p>",
                    solution_hi: "<p>6.(a) <strong>नागालैंड। मोत्सु - आओ </strong>(Ao) <strong>जनजाति </strong>द्वारा मनाया जाने वाला एक त्योहार है । यह त्यौहार खेत में बीज बोने के बाद मनाया जाता है। <strong>भारतीय त्योहारों की सूची -</strong> नागालैंड (हॉर्नबिल त्योहार), मिजोरम (चापचारकुट त्योहार), मणिपुर (याओशांग, चवांग कुट), मेघालय (नोंगक्रेम त्योहार, खासी त्योहार, वांगला), असम (अम्बुबाची, भोगाली बिहू, बैशागु, देहिंग पटकाई)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following union territories celebrates the Tarpa festival?</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन सा केंद्र शासित प्रदेश तर्पा उत्सव मनाता है?</p>",
                    options_en: ["<p>Lakshadweep</p>", "<p>Andman and Nicobar Islands</p>", 
                                "<p>Dadra and Nagar Haveli</p>", "<p>Ladakh</p>"],
                    options_hi: ["<p>लक्षद्वीप</p>", "<p>अंडमान व नोकोबार द्वीप समूह</p>",
                                "<p>दादरा और नगर हवेली</p>", "<p>लद्दाख</p>"],
                    solution_en: "<p>7.(c) <strong>Dadra and Nagar Haveli. Tarpa dance: </strong>Harvest dance celebrated by Varli, Kokna and Koli tribes. <strong>Other Festivals:</strong> Divasol, Akhatrij, Nariyeli Purnima, and Monsoon Magic Festival. <strong>Lakshadweep:</strong> Eid, Muharram. <strong>Andman and Nicobar Islands:</strong> Ossuary Feast (Nicobarese tribe), Villakku pooja, Island Tourism Festival.<strong> Ladakh: </strong>Hemis, Thiksey, Losar, Tak - Tok.</p>",
                    solution_hi: "<p>7.(c) <strong>दादरा और नगर हवेली। तारपा नृत्य:</strong> वर्ली, कोकना और कोली जनजातियों द्वारा मनाया जाने वाला फसल नृत्य है। <strong>अन्य त्यौहार: </strong>दिवासोल, अखत्रिज, नारियाली पूर्णिमा, और मानसून जादू महोत्सव। <strong>लक्षद्वीप:</strong> ईद, मुहर्रम। <strong>अंडमान और निकोबार द्वीप समूह:</strong> ओस्युअरी पर्व (निकोबारी जनजाति), विलाक्कू पूजा, द्वीप पर्यटन महोत्सव। <strong>लद्दाख: </strong>हेमिस, थिकसे, लोसर, टक - टोक।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The Hornbill festival is a famous tribal festival. It is celebrated in which of the following North-eastern states of India?</p>",
                    question_hi: "<p>8. हॉर्नबिल उत्सव एक प्रसिद्ध आदिवासी त्योहार है। यह भारत के निम्नलिखित में से किस उत्तर-पूर्वी राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Assam</p>", "<p>Mizoram</p>", 
                                "<p>Arunachal Pradesh</p>", "<p>Nagaland</p>"],
                    options_hi: ["<p>असम</p>", "<p>मिजोरम</p>",
                                "<p>अरुणाचल प्रदेश</p>", "<p>नागालैंड</p>"],
                    solution_en: "<p>8.(d) <strong>Nagaland.</strong> The Hornbill festival, which is called the &lsquo;Festivals of Festivals&rsquo; is a 10-day (from December 1 to December 10) annual cultural fest of Nagaland that showcases the diverse and rich Naga ethnicity. The start of this festival (December 1) marks the Nagaland Statehood day. <strong>Tribal festivals of India -</strong> Sume-Gelirak (Odisha), Sekrenyi Festival (Nagaland), Madai (Chhattisgarh), Bhagoria (Madhya Pradesh), Puttari (Karnataka), Sarhul (Jharkhand).</p>",
                    solution_hi: "<p>8.(d) <strong>नागालैंड। </strong>हॉर्नबिल उत्सव, जिसे \'त्योहारों का त्योहार\' कहा जाता है, नागालैंड का 10 दिवसीय (1 दिसंबर से 10 दिसंबर तक) वार्षिक सांस्कृतिक उत्सव है जो विविध और समृद्ध नागा जातीयता को प्रदर्शित करता है। इस त्यौहार की शुरुआत (1 दिसंबर) नागालैंड राज्य दिवस के रूप में होती है। <strong>भारत के जनजातीय त्योहार -</strong> सुमे-गेलिरक (ओडिशा), सेक्रेनी महोत्सव (नागालैंड), मड़ई (छत्तीसगढ़), भगोरिया (मध्य प्रदेश), पुट्टारी (कर्नाटक), सरहुल (झारखंड)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Where is the Red Panda Winter Carnival celebrated?</p>",
                    question_hi: "<p>9. रेड पांडा विंटर कार्निवल कहाँ मनाया जाता है?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Rajasthan</p>", 
                                "<p>Tamil Nadu</p>", "<p>Sikkim</p>"],
                    options_hi: ["<p>नगालैंड</p>", "<p>राजस्थान</p>",
                                "<p>तमिलनाडु</p>", "<p>सिक्किम</p>"],
                    solution_en: "<p>9.(d) <strong>Sikkim.</strong> The Red Panda Winter Festival was previously celebrated under the name of Sikkim Winter Carnival and was aimed at promoting tourism in the state during the winter months. It is celebrated during December-January months. It is organized and announced by the Sikkim Tourism board. It was started in 2016. <strong>Other festivals of Sikkim :</strong> Losoong Festival, Losar Festival, Bhumchu Festival, Saga Dawa, Lhabab Duchen Festival, Hee Bermiok Festival, International Flower Festival, etc.</p>",
                    solution_hi: "<p>9.(d) <strong>सिक्किम।</strong> रेड पांडा विंटर फेस्टिवल पहले सिक्किम विंटर कार्निवल के नाम से मनाया जाता था और इसका उद्देश्य सर्दियों के महीनों के दौरान राज्य में पर्यटन को बढ़ावा देना था। यह दिसंबर-जनवरी महीनों के दौरान मनाया जाता है। इसका आयोजन और घोषणा सिक्किम पर्यटन बोर्ड द्वारा की जाती है। इसकी शुरुआत 2016 में हुई थी। <strong>सिक्किम के अन्य त्यौहार: </strong>लोसूंग महोत्सव, लोसर महोत्सव, भुमचू महोत्सव, सागा दावा, ल्हाबाब डुचेन महोत्सव, ही बरमिओक महोत्सव, अंतर्राष्ट्रीय फूल महोत्सव, आदि।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. What is &lsquo;Jallikattu&rsquo;?</p>",
                    question_hi: "<p>10. \'जल्लीकट्टू\' क्या है?</p>",
                    options_en: ["<p>The traditional name for &lsquo;People&rsquo;s Leader&rsquo; in Tamil</p>", "<p>A popular watersport enjoyed by the Marina beach, Chennai</p>", 
                                "<p>A bull worship festival at Pashupatinath temple, Nepal</p>", "<p>A traditional bull taming sport popular in Tamil Nadu</p>"],
                    options_hi: ["<p>तमिल में \'पीपुल्स लीडर\' का पारंपरिक नाम</p>", "<p>मरीना बीच, चेन्नई द्वारा आनंदित एक लोकप्रिय वाटरस्पोर्ट</p>",
                                "<p>पशुपतिनाथ मंदिर, नेपाल में एक बैल पूजा उत्सव</p>", "<p>तमिलनाडु में लोकप्रिय बैल को वश में करने का एक पारंपरिक खेल</p>"],
                    solution_en: "<p>10.(d) Jallikattu (celebration of nature, and thanks giving for a bountiful harvest, of which cattle-worship is part) is a part of the festival of Mattu Pongal. Other names of Jallikattu: Yeru Thazhuvuthal, Madu Pidithal, Pollerudhu Pidithal.</p>",
                    solution_hi: "<p>10.(d) जल्लीकट्टू (प्रकृति का उत्सव, और भरपूर फसल के लिए धन्यवाद देना, जिसमें मवेशियों की पूजा भी शामिल है) मट्टू पोंगल के त्योहार का एक हिस्सा है। जल्लीकट्टू के अन्य नाम: येरु थझुवुथल, मदु पिडिथल, पोलेरुधु पिडिथल।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. In which state is Mamallapuram Utsav celebrated?</p>",
                    question_hi: "<p>11. मामल्लापुरम उत्सव किस राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Karnataka</p>", "<p>Kerala</p>", 
                                "<p>Andhra Pradesh</p>", "<p>Tamil Nadu</p>"],
                    options_hi: ["<p>कर्नाटक</p>", "<p>केरल</p>",
                                "<p>आंध्र प्रदेश</p>", "<p>तमिलनाडु</p>"],
                    solution_en: "<p>11.(d) <strong>Tamil Nadu. Mamallapuram Utsav:</strong> Organized by the Department of Tourism, (Tamil Nadu), a 30-day festival held annually between December and January. Every year, Mamallapuram hosts the &ldquo;Indian Dance Festival&rdquo; - A Festival of Our Culture and Tradition. The venue for this Utsav - Front lawn of the Shore Temple.</p>",
                    solution_hi: "<p>11.(d) <strong>तमिलनाडु। मामल्लापुरम उत्सव: </strong>पर्यटन विभाग, (तमिलनाडु) द्वारा आयोजित, 30-दिवसीय उत्सव जो हर साल दिसंबर और जनवरी के बीच आयोजित किया जाता है। हर साल, मामल्लपुरम \"भारतीय नृत्य महोत्सव\" का आयोजन करता है - हमारी संस्कृति और परंपरा का एक महोत्सव। इस उत्सव का स्थान - शोर मंदिर के सामने का घास का मैदान (लॉन)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which state government declared &ldquo;Pakke Paga Hornbill Festival&rdquo; as a state festival ?</p>",
                    question_hi: "<p>12. किस राज्य सरकार ने \"पक्के पागा हॉर्नबिल महोत्सव\" को राज्य उत्सव के रूप में घोषित किया?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Meghalaya</p>", 
                                "<p>Arunachal Pradesh</p>", "<p>Andhra Pradesh</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>मेघालय</p>",
                                "<p>अरुणाचल प्रदेश</p>", "<p>आंध्र प्रदेश</p>"],
                    solution_en: "<p>12.(c) <strong>Arunachal Pradesh</strong> (Land of the Rising Sun). <strong>Pakke Paga Hornbill Festival -</strong> An annual festival celebrated in the Pakke Paga Wildlife Sanctuary (located in the East Kameng district). <strong>Other festivals of Arunachal Pradesh -</strong> Losar Festival, Nyokum Festival, Sangken Festival. <strong>Meghalaya - </strong>Nongkrem Dance Festival, Wangala Festival, Autumn Festival. <strong>Odisha - </strong>Rath Yatra, Konark Dance Festival, Bali Yatra. <strong>Andhra Pradesh - </strong>Lepakshi Utsav.</p>",
                    solution_hi: "<p>12.(c) <strong>अरुणाचल प्रदेश</strong> (उगते सूरज की भूमि)। <strong>पक्के पागा हॉर्नबिल महोत्सव -</strong> पक्के पागा वन्यजीव अभयारण्य (पूर्वी कामेंग जिले में स्थित) में मनाया जाने वाला एक वार्षिक उत्सव है। <strong>अरुणाचल प्रदेश के अन्य त्यौहार -</strong> लोसर महोत्सव, न्योकुम महोत्सव, सांगकेन महोत्सव। <strong>मेघालय - </strong>नोंगक्रेम नृत्य महोत्सव, वांगला महोत्सव, शरद महोत्सव। <strong>ओडिशा -</strong> रथ यात्रा, कोणार्क नृत्य महोत्सव, बाली यात्रा। <strong>आंध्र प्रदेश -</strong> लेपाक्षी उत्सव।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following festivals is associated with harvest?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन सा त्योहार फसल से जुड़ा है?</p>",
                    options_en: ["<p>Guru Purnima</p>", "<p>Pongal</p>", 
                                "<p>Eid-Ul- Fitr</p>", "<p>Easter</p>"],
                    options_hi: ["<p>गुरु पूर्णिमा</p>", "<p>पोंगल</p>",
                                "<p>ईद-उल-फितर</p>", "<p>ईस्टर</p>"],
                    solution_en: "<p>13.(b) <strong>Pongal</strong> (Tamil Nadu) festival spans four days: Bhogi, Surya Pongal, Mattu Pongal, and Kaanum Pongal. Other Festivals - <strong>Guru Purnima</strong> (Hindu festival dedicated to spiritual and academic teachers), <strong>Eid-Ul-Fitr</strong> (Islamic festival that marks the end of Ramadan, the month of fasting), <strong>Easter</strong> (Christian festival that celebrates the resurrection of Jesus Christ).</p>",
                    solution_hi: "<p>13.(b) <strong>पोंगल</strong> (तमिलनाडु) त्योहार चार दिनों तक मनाया जाता है: भोगी, सूर्य पोंगल, मट्टू पोंगल और कानुम पोंगल। अन्य त्यौहार - <strong>गुरु पूर्णिमा </strong>(आध्यात्मिक और शैक्षणिक शिक्षकों को समर्पित हिंदू त्योहार), <strong>ईद-उल-फितर </strong>(इस्लामी त्योहार जो उपवास का महीना रमजान के अंत का प्रतीक है), <strong>ईस्टर</strong> (ईसाई त्योहार जो यीशु मसीह के पुनर्जन्म का जश्न मनाते है)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. &lsquo;Ugadi&rsquo; is a festival celebrated in the India state of:</p>",
                    question_hi: "<p>14. उगादी उत्सव भारत के किस राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Assam</p>", "<p>Andhra Pradesh</p>", 
                                "<p>Goa</p>", "<p>Haryana</p>"],
                    options_hi: ["<p>असम</p>", "<p>आंध्र प्रदेश</p>",
                                "<p>गोवा</p>", "<p>हरियाणा</p>"],
                    solution_en: "<p>14.(b) <strong>Andhra Pradesh. Ugadi </strong>(Yugadi): It marks the beginning of the Hindu lunar calendar and is considered the New Year\'s Day in Telangana and Karnataka also. It usually falls in the months of March or April. <strong>Other festivals of Andhra Pradesh -</strong> Pongal, Lumbini Festival and Lepakshi Utsav.</p>",
                    solution_hi: "<p>14.(b) <strong>आंध्र प्रदेश। उगादि</strong> (युगादि): यह हिंदू चंद्र कैलेंडर की शुरुआत का प्रतीक है और इसे तेलंगाना और कर्नाटक में भी नए साल का दिन माना जाता है। यह आमतौर पर मार्च या अप्रैल के महीने में पड़ता है। <strong>आंध्र प्रदेश के अन्य त्यौहार -</strong> पोंगल, लुंबिनी महोत्सव और लेपाक्षी उत्सव।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. The &lsquo;Sangai&rsquo; festival is celebrated in which of the following states of India?</p>",
                    question_hi: "<p>15. \'संगाई (Sangai)&rsquo; त्योहार भारत के निम्नलिखित में से किस राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Mizoram</p>", "<p>Nagaland</p>", 
                                "<p>Tripura</p>", "<p>Manipur</p>"],
                    options_hi: ["<p>मिजोरम</p>", "<p>नागालैंड</p>",
                                "<p>त्रिपुरा</p>", "<p>मणिपुर</p>"],
                    solution_en: "<p>15.(d) <strong>Manipur.</strong> Other festival of <strong>Manipur</strong> - Gang-Ngai, Lui-Ngai-Ni, Yaosang, Cheiraoba (The Manipuri New Year), Kang (The Ratha Yatra of Manipur), Heikru Hitongba, Ningol Chak-kouba, Kut (Festival of Kuki- Chin- Mizo), Chumpha (Festival of Tangkhul Nagas). <strong>Mizoram -</strong> Chapchar Kut, Mim Kut, Pawl Kut, Lyuva Khutla. <strong>Nagaland -</strong> Sekrenyi Festival, Tsukheneye. <strong>Tripura - </strong>Garia Puja, Pilak Festival, Kharchi Festival.</p>",
                    solution_hi: "<p>15.(d) <strong>मणिपुर। मणिपुर</strong> के अन्य त्योहार - गैंग-नगाई, लुई-नगाई-नी, याओसांग, चेइराओबा (मणिपुरी नव वर्ष), कांग (मणिपुर की रथ यात्रा), हेइक्रू हितोंगबा, निंगोल चक-कौबा, कुट (कुकी-चिन-मिज़ो का त्योहार), चुम्फा (तांगखुल नागाओं का त्योहार)। <strong>मिजोरम - </strong>चपचार कुट, मीम कुट, पावल कुट, ल्यूवा खुटला। <strong>नागालैंड -</strong> सेक्रेनी महोत्सव, त्सुखेनेय। <strong>त्रिपुरा -</strong> गरिया पूजा, पिलक महोत्सव, खारची महोत्सव।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "misc",
                    question_en: "<p>16. Famous &lsquo;Sangai Festival&rsquo; is an annual cultural festival organized by which state of North-East India?</p>",
                    question_hi: "<p>16. प्रसिद्ध \'संगाई महोत्सव\' पूर्वोत्तर भारत के किस राज्य द्वारा आयोजित एक वार्षिक-सांस्कृतिक उत्सव है?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Manipur</p>", 
                                "<p>Tripura</p>", "<p>Meghalaya</p>"],
                    options_hi: ["<p>नागालैंड</p>", "<p>मणिपुर</p>",
                                "<p>त्रिपुरा</p>", "<p>मेघालय</p>"],
                    solution_en: "<p>16.(b) <strong>Manipur.</strong> Sangai Utsav is an annual cultural festival organized by the Manipur Tourism Department every year in November. The festival is named after the State animal, Sangai, the brow-antlered deer found only in Manipur. List of Festivals of Indian States - Manipur (Yaoshang, Kang), Nagaland (Hornbill festival, Moatsu Festival), Tripura (Kharchi Puja, Garia puja), Meghalaya (Nongkrem festival, Khasi&rsquo;s Shad Suk), Mizoram (Pawl Kut, Lyuva Khutla).</p>",
                    solution_hi: "<p>16.(b) <strong>मणिपुर।</strong> संगाई उत्सव प्रत्येक वर्ष नवंबर में मणिपुर पर्यटन विभाग द्वारा आयोजित एक वार्षिक सांस्कृतिक महोत्सव है। इस त्यौहार का नाम राज्य पशु संगाई के नाम पर रखा गया है, जो केवल मणिपुर में पाया जाने वाला हिरण है। भारतीय राज्यों के त्योहारों की सूची - मणिपुर (याओशांग, कांग), नागालैंड (हॉर्नबिल त्योहार, मोत्सु महोत्सव), त्रिपुरा (खार्ची पूजा, गरिया पूजा), मेघालय (नोंगक्रेम त्योहार, खासी का शाद सुक), मिजोरम (पावल कुट, ल्यूवा खुटला)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>