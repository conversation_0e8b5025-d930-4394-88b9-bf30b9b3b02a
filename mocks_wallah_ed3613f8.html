<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">There is a ratio of </span><span style=\"font-family: Cambria Math;\">10 :</span><span style=\"font-family: Cambria Math;\"> 13 between two numbers. If 80 percent of the first is 16</span><span style=\"font-family: Cambria Math;\">, then</span><span style=\"font-family: Cambria Math;\"> 50% of the second number is</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">10 :</span><span style=\"font-family: Cambria Math;\"> 13 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 80 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> 16 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 50% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>17</p>\n", "<p>13</p>\n", 
                                "<p>25</p>\n", "<p>14</p>\n"],
                    options_hi: ["<p>17</p>\n", "<p>13</p>\n",
                                "<p>25</p>\n", "<p>14</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let first number = 10x</span><span style=\"font-family: Cambria Math;\">, and IInd = 13x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10x</span><span style=\"font-family: Cambria Math;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 16&rArr; x </span><span style=\"font-family: Cambria Math;\">= 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">50% of </span><span style=\"font-family: Cambria Math;\">IInd</span><span style=\"font-family: Cambria Math;\"> number &rArr;</span><span style=\"font-family: Cambria Math;\"> 26 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> =</span><span style=\"font-family: Cambria Math;\"> 13</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 10x </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> = 13x </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10x</span><span style=\"font-family: Cambria Math;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 16&rArr; x </span><span style=\"font-family: Cambria Math;\">= 2</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 50% &rArr;</span><span style=\"font-family: Cambria Math;\"> 26 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 13</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> The line chart given below shows the production of refrigerator by 6 companies. </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image1.png\" width=\"350\" height=\"273\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">What is the ratio of production of refrigerator by company C1 to the production of refrigerator by company C6? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2347;&#2381;&#2352;&#2367;&#2332;&#2352;&#2375;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image1.png\" width=\"350\" height=\"273\"></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> C1 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2347;&#2381;&#2352;&#2367;&#2332;&#2352;&#2375;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> C6 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2347;&#2381;&#2352;&#2367;&#2332;&#2352;&#2375;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 1 </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 1</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">1 :</span><span style=\"font-family: Cambria Math;\"> 3</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">1 :</span><span style=\"font-family: Cambria Math;\"> 2</span></p>\n"],
                    options_hi: ["<p>4 : 1</p>\n", "<p>2 : 1</p>\n",
                                "<p>1 : 3</p>\n", "<p>1 : 2</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio of company C1 and C6 = </span><span style=\"font-family: Cambria Math;\">100 :</span><span style=\"font-family: Cambria Math;\"> 50</span><span style=\"font-family: Cambria Math;\"> &rarr; 2 : 1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> C1 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C6 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2340;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 100 : 50</span><span style=\"font-family: Cambria Math;\"> &rarr; 2 : 1</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">The table given below shows the cost price and selling price of four articles.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/3eCapture.PNG\" width=\"257\" height=\"101\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">The total cost price of D, E and F is what percent of the total selling price of E, F and G? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><img src=\"https://ssccglpinnacle.com/images/mceu_95832333811683362766926.png\"></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D, E </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> F </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">, E, F </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> G </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>118.37 percent</p>\n", "<p>105.37 percent</p>\n", 
                                "<p>101.33 percent</p>\n", "<p>108.33 percent</p>\n"],
                    options_hi: ["<p>118.37 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>105.37 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>101.33 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>108.33 <span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">total</span><span style=\"font-family: Cambria Math;\"> cost price of D, E and F = 550 + 750 + 650 = 1950</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">total</span><span style=\"font-family: Cambria Math;\"> selling price of E, F and G = 700 + 450 + 650 = 1800</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Percentage </span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1950</mn><mn>1800</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = 108.33</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D, E </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> F </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 550 + 750 + 650 = 1950</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">E, F </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> G </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 700 + 450 + 650 = 1800</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1950</mn><mn>1800</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = 108.33</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Find the value of </span><span style=\"font-family: Cambria Math;\">sec&theta;</span><span style=\"font-family: Cambria Math;\"> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo></msqrt></math> </span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">sec&theta;</span><span style=\"font-family: Cambria Math;\"> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo></msqrt></math> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> |</span></p>\n",
                    options_en: ["<p>0</p>\n", "<p>2</p>\n", 
                                "<p>-1</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>0</p>\n", "<p>2</p>\n",
                                "<p>-1</p>\n", "<p>1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sec&theta; </span><span style=\"font-family: Cambria Math;\">&times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo></msqrt></math>&nbsp; &nbsp; &nbsp; &nbsp;[we know that <span style=\"font-weight: 400;\">(1 &minus; </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">&theta;)</span><span style=\"font-weight: 400;\">&nbsp; = </span><span style=\"font-weight: 400;\">cos&sup2;</span><span style=\"font-weight: 400;\">&theta;]</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sec&theta;</span><span style=\"font-family: Cambria Math;\"> &times;</span><span style=\"font-family: Cambria Math;\"> cos&theta; = 1</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sec&theta; </span><span style=\"font-family: Cambria Math;\">&times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>)</mo></msqrt></math>&nbsp; &nbsp; &nbsp;[ &#2361;&#2350; &#2332;&#2366;&#2344;&#2340;&#2375; &#2361;&#2376;&#2306; <span style=\"font-weight: 400;\">(1 &minus; </span><span style=\"font-weight: 400;\">sin&sup2;</span><span style=\"font-weight: 400;\">&theta;)</span><span style=\"font-weight: 400;\">&nbsp; = </span><span style=\"font-weight: 400;\">cos&sup2;</span><span style=\"font-weight: 400;\">&theta;]</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sec&theta;</span><span style=\"font-family: Cambria Math;\"> &times;</span><span style=\"font-family: Cambria Math;\"> cos&theta; = 1</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">If 3 </span><span style=\"font-family: Cambria Math;\">tan&theta;</span><span style=\"font-family: Cambria Math;\"> = 5 </span><span style=\"font-family: Cambria Math;\">sin&theta;</span><span style=\"font-family: Cambria Math;\">, then what is the value of </span><span style=\"font-family: Cambria Math;\">cos&theta;</span><span style=\"font-family: Cambria Math;\"> ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> 3</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">tan&theta;</span><span style=\"font-family: Cambria Math;\"> = 5 </span><span style=\"font-family: Cambria Math;\">sin&theta;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">cos&theta;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Cambria Math;\">tan&theta;</span><span style=\"font-family: Cambria Math;\"> = 5 </span><span style=\"font-family: Cambria Math;\">sin&theta;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mi>&theta;</mi></mrow><mrow><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>3</mn></mfrac><mo>,</mo></math> (&nbsp; tan<span style=\"font-family: Cambria Math;\">&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mrow><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo></mrow></mfrac></math> )</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>&theta;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>3</mn></mfrac></math>&nbsp; , <span style=\"font-weight: 400;\">cos</span><span style=\"font-weight: 400;\">&theta; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>&nbsp;&nbsp;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Cambria Math;\">tan&theta;</span><span style=\"font-family: Cambria Math;\"> = 5 </span><span style=\"font-family: Cambria Math;\">sin&theta;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mi>&theta;</mi></mrow><mrow><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>3</mn></mfrac><mo>,</mo><mo>&nbsp;</mo></math>(&nbsp; tan<span style=\"font-family: Cambria Math;\">&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mrow><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo></mrow></mfrac></math>&nbsp;</span> )</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mi>&theta;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>3</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">, <span style=\"font-weight: 400;\">cos</span><span style=\"font-weight: 400;\">&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> The average age of </span><span style=\"font-family: Cambria Math;\">Abhay</span><span style=\"font-family: Cambria Math;\">, his wife and their child 6 years ago was 42 years and that of his wife and their child 8 years ago was 30 years. Find the present age of </span><span style=\"font-family: Cambria Math;\">Abhay</span><span style=\"font-family: Cambria Math;\">. </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2351;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;</span><span style=\"font-family: Nirmala UI;\">&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2381;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> 6 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> 42 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2381;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>68 years</p>\n", "<p>63 years</p>\n", 
                                "<p>64 years</p>\n", "<p>66 years</p>\n"],
                    options_hi: ["<p>68 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>63 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                                "<p>64 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>66 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">6 years before sum of family&rsquo;s a</span><span style=\"font-family: Cambria Math;\">ge = 42 &times; 3 = </span><span style=\"font-family: Cambria Math;\">126</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Family&rsquo;s present age </span><span style=\"font-family: Cambria Math;\">= 126</span><span style=\"font-family: Cambria Math;\"> + 18 = 144</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Age of wife and children before 8 years = 30 &times; 2 = </span><span style=\"font-family: Cambria Math;\">60</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Wife and children&rsquo;s present age = 60 + </span><span style=\"font-family: Cambria Math;\">16 = 76</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Present age of </span><span style=\"font-family: Cambria Math;\">abhay</span><span style=\"font-family: Cambria Math;\"> = 144 &minus;</span><span style=\"font-family: Cambria Math;\"> 76 = 68 years</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">6 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;</span><span style=\"font-family: Nirmala UI;\">&#2327;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 42 &times; 3 = </span><span style=\"font-family: Cambria Math;\">126</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Nirmala UI;\">&#2369;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 126 + 18 = 144</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2381;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> = 30 &times; 2 =</span><span style=\"font-family: Cambria Math;\"> 60</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2381;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> = 60 + 16 = 76</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> = 144 - 76 = 68 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> If 73A215 is divisible by 11 and 56B26 is divisible by 9, then what is the value of A + </span><span style=\"font-family: Cambria Math;\">B ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">73A215 ,</span><span style=\"font-family: Cambria Math;\"> 11 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 56B26, 9 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> A + B </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>10</p>\n", "<p>9<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>11</p>\n", "<p>7</p>\n"],
                    options_hi: ["<p>10</p>\n", "<p>9</p>\n",
                                "<p>11</p>\n", "<p><span style=\"font-family: Cambria Math;\">7</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Divisibility of </span><span style=\"font-family: Cambria Math;\">11 :</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> When the difference between the sum of odd and even place digits is equal to 0 or multiple of 11</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">73A215 = 7 + A + 1 = 3 + 2 + 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 + A = 10&nbsp; &nbsp; (A = 2) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">if</span><span style=\"font-family: Cambria Math;\"> the sum of digits of number is divided by 9 then the number </span><span style=\"font-family: Cambria Math;\">is itself is divisible by 9.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">56B26 &rarr; </span><span style=\"font-family: Cambria Math;\">5 + 6 + B + 2 + 6 = 19 + B, (B = </span><span style=\"font-family: Cambria Math;\">8</span><span style=\"font-family: Cambria Math;\">) (</span><span style=\"font-family: Cambria Math;\">19 + 8 = 27 is divisible by 9)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">so</span><span style=\"font-family: Cambria Math;\"> the value of A + B = 2 + 8 = 10</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">11 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">- </span></strong><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> 0 </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 11 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2339;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">73A215 = 7 + A + 1 = 3 + 2 + 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 + A = 10&nbsp; &nbsp;(A = 2) </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2351;&#2306;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">56B26 &rarr;</span><span style=\"font-family: Cambria Math;\"> 5 + 6 + B + 2 + 6 = 19 + B, (B &rarr;</span><span style=\"font-family: Cambria Math;\"> 8</span><span style=\"font-family: Cambria Math;\">) (</span><span style=\"font-family: Cambria Math;\">19 + 8 = 27, 9 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Cambria Math;\">+ B</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 2 + 8 = 10</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> If (p + q) = 5 and </span><span style=\"font-family: Cambria Math;\">pq</span><span style=\"font-family: Cambria Math;\"> = 18, then what is the value of p&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;+ </span><span style=\"font-family: Cambria Math;\">q&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">p + q) = 5 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">pq</span><span style=\"font-family: Cambria Math;\"> = 18, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> p&sup3;&nbsp;+ q&sup3;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p>-270</p>\n", "<p>125</p>\n", 
                                "<p>-145</p>\n", "<p>135</p>\n"],
                    options_hi: ["<p>-270</p>\n", "<p>125</p>\n",
                                "<p>-145</p>\n", "<p>135</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><strong>Formula :-</strong><span style=\"font-weight: 400;\"> (</span><span style=\"font-weight: 400;\">a + b)<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">a<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">b<span style=\"font-family: Cambria Math;\">&sup3; </span></span><span style=\"font-weight: 400;\">+ 3ab(a + b)</span></p>\r\n<p><span style=\"font-weight: 400;\">According to the question,</span></p>\r\n<p><span style=\"font-weight: 400;\">(p + q)<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">p<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">q<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;+ 3pq(p + q)</span></p>\r\n<p><span style=\"font-weight: 400;\">(5)<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">p<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">q<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\"> + 3 &times; 18 &times; 5</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr; p<span style=\"font-family: Cambria Math;\">&sup3;</span>&nbsp;+ q<span style=\"font-family: Cambria Math;\">&sup3;</span> = 125 </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">270 = </span><span style=\"font-weight: 400;\">-145</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-weight: 400;\"><strong>&#2360;&#2370;&#2340;&#2381;&#2352; :-&nbsp;</strong> (</span><span style=\"font-weight: 400;\">a + b)<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">a<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">b<span style=\"font-family: Cambria Math;\">&sup3; </span></span><span style=\"font-weight: 400;\">+ 3ab(a + b)</span></p>\r\n<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-weight: 400;\">(p + q)<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">p<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">q<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;+ 3pq(p + q)</span></p>\r\n<p><span style=\"font-weight: 400;\">(5)<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;= </span><span style=\"font-weight: 400;\">p<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\">&nbsp;+ </span><span style=\"font-weight: 400;\">q<span style=\"font-family: Cambria Math;\">&sup3;</span></span><span style=\"font-weight: 400;\"> + 3 &times; 18 &times; 5</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr; p<span style=\"font-family: Cambria Math;\">&sup3;</span>&nbsp;+ q<span style=\"font-family: Cambria Math;\">&sup3;</span> = 125 </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">270 = </span><span style=\"font-weight: 400;\">-145</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">Simplify: 60% of 870 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1444</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">+ 18 &times; </span><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">60% of 870 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1444</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">+ 18 &times; </span><span style=\"font-family: Cambria Math;\">19 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> |</span></p>\n",
                    options_en: ["<p>826</p>\n", "<p>842</p>\n", 
                                "<p>786</p>\n", "<p>700</p>\n"],
                    options_hi: ["<p>826</p>\n", "<p>842</p>\n",
                                "<p>786</p>\n", "<p>700</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">60% of 870 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1444</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">+ 18 &times; </span><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-weight: 400;\">522 - </span><span style=\"font-weight: 400;\">38</span><span style=\"font-weight: 400;\">&nbsp; + 342 =</span><span style=\"font-weight: 400;\"> 826</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">60% of 870 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1444</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">+ 18 &times; </span><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-weight: 400;\">522 - </span><span style=\"font-weight: 400;\">38</span><span style=\"font-weight: 400;\">&nbsp; + 342 =</span><span style=\"font-weight: 400;\"> 826</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> If 70% </span><span style=\"font-family: Cambria Math;\">of (</span><span style=\"font-family: Cambria Math;\">x - y) = 30% of (x + y), then what percent of x is y ? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;(x - y) <span style=\"font-weight: 400;\">&#2325;&#2366;</span> 70% = (x + y) <span style=\"font-weight: 400;\">&#2325;&#2366;</span> 30%, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p>20%</p>\n", "<p>50%</p>\n", 
                                "<p>40%</p>\n", "<p>30%</p>\n"],
                    options_hi: ["<p>20%</p>\n", "<p>50%</p>\n",
                                "<p>40%</p>\n", "<p>30%</p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">70% </span><span style=\"font-family: Cambria Math;\">of (</span><span style=\"font-family: Cambria Math;\">x - y) = 30% of (x + y)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>y</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>y</mi><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>7</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-weight: 400;\">3</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> + 3</span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\"> = 7</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> 7</span><span style=\"font-weight: 400;\">y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> =10</span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\">,</span> </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>y</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Percentage =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = </span><span style=\"font-family: Cambria Math;\">40%</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">(x - y) <span style=\"font-weight: 400;\">&#2325;&#2366;</span> 70% = (x + y) <span style=\"font-weight: 400;\">&#2325;&#2366;</span> 30%</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>y</mi><mo>)</mo></mrow><mrow><mo>(</mo><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>y</mi><mo>)</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>3</mn><mn>7</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-weight: 400;\">3</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> + 3</span><span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\"> = 7</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> 7</span><span style=\"font-weight: 400;\">y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4x</span><span style=\"font-family: Cambria Math;\"> = 10y</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>y</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>5</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = </span><span style=\"font-family: Cambria Math;\">40%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If radius of a circle is increased by 10%.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">then</span><span style=\"font-family: Cambria Math;\"> its area would increase by: </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2338;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">:</span></p>\n",
                    options_en: ["<p>100%</p>\n", "<p>20<span style=\"font-family: Cambria Math;\">%</span></p>\n", 
                                "<p>21<span style=\"font-family: Cambria Math;\">%</span></p>\n", "<p>19%</p>\n"],
                    options_hi: ["<p>100%</p>\n", "<p>20%</p>\n",
                                "<p>21%</p>\n", "<p>19%</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Fraction value of 10% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of circle = <span style=\"font-weight: 400;\">&pi;</span><span style=\"font-weight: 400;\">r&sup2;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Old area&nbsp; &nbsp;:&nbsp; &nbsp;new area</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; 100&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;121 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Percentage increase in area =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = </span><span style=\"font-family: Cambria Math;\">21%</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2367;&#2344;&#2381;&#2344;&#2366;&#2340;&#2381;&#2350;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = <span style=\"font-weight: 400;\">&pi;</span><span style=\"font-weight: 400;\">r&sup2;</span></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2352;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span></span><span style=\"font-family: Cambria Math;\">&nbsp; :&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;121 </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = </span><span style=\"font-family: Cambria Math;\">21%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If (a + b</span><span style=\"font-family: Cambria Math;\">)&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;= 100 and </span><span style=\"font-family: Cambria Math;\">ab</span><span style=\"font-family: Cambria Math;\"> = 25 then find the value of (a - b).</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">a + b)<span style=\"font-weight: 400;\">&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 100 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ab</span><span style=\"font-family: Cambria Math;\"> = 25 </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> (a - b) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> |</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>20</p>\n", "<p>25</p>\n", 
                                "<p>0</p>\n", "<p>10</p>\n"],
                    options_hi: ["<p>20</p>\n", "<p>25</p>\n",
                                "<p>0</p>\n", "<p>10</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let A = B = 5</span></p>\r\n<p><span style=\"font-weight: 400;\">(a + b)<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-weight: 400;\"> = 100, (5 + 5)<span style=\"font-family: Cambria Math;\">&sup2; </span></span><span style=\"font-weight: 400;\">= 100, 100 = 100 (satisfied)</span></p>\r\n<p><span style=\"font-weight: 400;\">ab = 25</span></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;</span> 5 &times; 5 = 25</p>\r\n<p><span style=\"font-weight: 400;\">&rArr;</span> 25 = 25 (satisfied)</p>\r\n<p><span style=\"font-weight: 400;\">According to the question,</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;(a - b) = 5 </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\"> = 0</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> A = B = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">(a + b)&sup2;</span><span style=\"font-weight: 400;\"> = 100, (5 + 5)&sup2; </span><span style=\"font-weight: 400;\">= 100, 100 = 100 </span>(</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">ab = 25</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr; 5 &times; 5 = 25</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr; 25 = 25 </span>(</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;</span><span style=\"font-family: Nirmala UI;\">&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-weight: 400;\">(a - b) = 5 </span><span style=\"font-weight: 400;\">- </span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\"> = 0</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The following line graph shows the production of wheat in five different states in the year 2020. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">What is the average production of wheat in these five states in the year </span><span style=\"font-family: Cambria Math;\">2020 ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image3.png\" width=\"400\" height=\"298\"></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2020 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2361;&#2370;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2020 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;</span><span style=\"font-family: Nirmala UI;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image4.png\" width=\"400\" height=\"299\"></p>\n",
                    options_en: ["<p>60 <span style=\"font-family: Cambria Math;\">tonnes</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>70 <span style=\"font-family: Cambria Math;\">tonnes</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>57 <span style=\"font-family: Cambria Math;\">tonnes</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>50 <span style=\"font-family: Cambria Math;\">tonnes</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>60 <span style=\"font-family: Nirmala UI;\">&#2335;&#2344;</span></p>\n", "<p>70 <span style=\"font-family: Nirmala UI;\">&#2335;&#2344;</span></p>\n",
                                "<p>57 <span style=\"font-family: Nirmala UI;\">&#2335;&#2344;</span></p>\n", "<p>50 <span style=\"font-family: Nirmala UI;\">&#2335;&#2344;</span></p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Average production of wheat = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>62</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>50</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn></mrow><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>300</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">60 tonnes</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2375;&#2361;&#2370;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; =</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>62</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>50</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>48</mn></mrow><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>300</mn><mn>5</mn></mfrac></math> = </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> 60 </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2344;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">If x = -2 and y = 1, then what is the value of (</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>x</mi></mfrac></math> + y) (</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>4</mn><mi>y</mi></mrow><mi>x</mi></mfrac></math> <span style=\"font-family: Cambria Math;\">) ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">. . </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> x</span><span style=\"font-family: Cambria Math;\"> = -2 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> y = 1, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mi>x</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + y) (</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>4</mn><mi>y</mi></mrow><mi>x</mi></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>-5</p>\n", "<p>7<span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>-7</p>\n", "<p>5</p>\n"],
                    options_hi: ["<p>-5</p>\n", "<p>7</p>\n",
                                "<p>-7</p>\n", "<p>5</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = -</span><span style=\"font-family: Cambria Math;\">2 ,</span><span style=\"font-family: Cambria Math;\"> y = 1,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>4</mn><mi>x</mi></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>y</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>16</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>4</mn><mi>y</mi></mrow><mi>x</mi></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mo>-</mo><mn>2</mn></mrow></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>16</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mo>-</mo><mn>2</mn></mrow></mfrac><mo>)</mo></math></p>\r\n<p><span style=\"font-weight: 400;\">&rArr; (</span><span style=\"font-weight: 400;\">-2</span><span style=\"font-weight: 400;\"> + 1) (</span><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\"> + 1 + 2) = </span><span style=\"font-weight: 400;\">-7</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = -</span><span style=\"font-family: Cambria Math;\">2 ,</span><span style=\"font-family: Cambria Math;\"> y = 1,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>4</mn><mi>x</mi></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>y</mi><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>16</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>y</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>4</mn><mi>y</mi></mrow><mi>x</mi></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>(</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mo>-</mo><mn>2</mn></mrow></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>)</mo><mo>&nbsp;</mo><mo>(</mo><mfrac><mn>16</mn><mn>4</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>4</mn></mrow><mrow><mo>-</mo><mn>2</mn></mrow></mfrac><mo>)</mo></math></p>\r\n<p><span style=\"font-weight: 400;\">&rArr; (</span><span style=\"font-weight: 400;\">-2</span><span style=\"font-weight: 400;\"> + 1) (</span><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\"> + 1 + 2) = </span><span style=\"font-weight: 400;\">-7</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">In a right-angled triangle if the longest side is 26 cm and one of the other sides is 24 cm, find the length of the third side.</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> 26 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> 24 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>20 cm</p>\n", "<p>18<span style=\"font-family: Cambria Math;\"> cm</span></p>\n", 
                                "<p>12<span style=\"font-family: Cambria Math;\"> cm</span></p>\n", "<p>10<span style=\"font-family: Cambria Math;\"> cm</span></p>\n"],
                    options_hi: ["<p>20 cm</p>\n", "<p>18 cm</p>\n",
                                "<p>12 cm</p>\n", "<p>10 cm</p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image5.png\" width=\"151\" height=\"174\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&Delta;ABC follows the triplets = (10, 24, </span><span style=\"font-family: Cambria Math;\">26</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So the value of BC = 10 cm</span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image5.png\" width=\"151\" height=\"174\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&Delta;ABC </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;</span><span style=\"font-family: Nirmala UI;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> (10, 24, 26)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> BC </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 10 cm </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">16</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Angle subtended by a chord PQ on the circumference is 90 degree. If radius of this circle is 15 cm, then what is the length of PQ? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">16</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">PQ </span><span style=\"font-family: Nirmala UI;\">&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> 90 </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 15 cm </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> PQ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>35 cm</p>\n", "<p>40 cm</p>\n", 
                                "<p>7.5 cm</p>\n", "<p>30 cm</p>\n"],
                    options_hi: ["<p>35 cm</p>\n", "<p>40 cm</p>\n",
                                "<p>7.5 cm</p>\n", "<p>30 cm</p>\n"],
                    solution_en: "<p>16<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image6.png\" width=\"150\" height=\"138\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to question, Chord is a Diameter when it makes the 90&deg; angle on the circum</span><span style=\"font-family: Cambria Math;\">ference of the </span><span style=\"font-family: Cambria Math;\">circle .</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So the length of </span><span style=\"font-family: Cambria Math;\">chord(</span><span style=\"font-family: Cambria Math;\">PQ) = 2 &times; radius = 15 &times; 2 = 30 cm</span></p>\n",
                    solution_hi: "<p>16<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image6.png\" width=\"150\" height=\"138\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;</span><span style=\"font-family: Nirmala UI;\">&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 90&deg; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> (PQ) = 2 &times; </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 15 &times; 2 = 30 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">17</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A cell phone was bought for Rs.18</span><span style=\"font-family: Cambria Math;\">,400</span><span style=\"font-family: Cambria Math;\"> after getting a discount of 8% on its marked price. What is the marked price of the cell </span><span style=\"font-family: Cambria Math;\">phone ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">17</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;</span><span style=\"font-family: Nirmala UI;\">&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> 18,400 </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs.20<span style=\"font-family: Cambria Math;\">,500</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Rs.19<span style=\"font-family: Cambria Math;\">,000</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>Rs.19<span style=\"font-family: Cambria Math;\">,500</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Rs.20<span style=\"font-family: Cambria Math;\">,000</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Rs.20<span style=\"font-family: Cambria Math;\">,500</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Rs.19<span style=\"font-family: Cambria Math;\">,000</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>Rs.19<span style=\"font-family: Cambria Math;\">,500</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Rs.20<span style=\"font-family: Cambria Math;\">,000</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>17<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">M.P &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>25</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= &#8377;18400</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">M.P </span>= &#8377;20000</span></p>\n",
                    solution_hi: "<p>17<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>25</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\">= &#8377;18400</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=</span><span style=\"font-family: Cambria Math;\"> &#8377;20000</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The pie chart below shows total number of cars sold by a company during five months, Total number of cars sold in these 5 months is 5400.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image7.png\" width=\"300\" height=\"253\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total number of cars sold in January and April is how many more than the number of cars sold in </span><span style=\"font-family: Cambria Math;\">May ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2305;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 5400 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1683360919/word/media/image8.png\" width=\"299\" height=\"252\"></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2357;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2376;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>2258</p>\n", "<p>2160</p>\n", 
                                "<p>2200</p>\n", "<p>2100</p>\n"],
                    options_hi: ["<p>2258</p>\n", "<p>2160</p>\n",
                                "<p>2200</p>\n", "<p>2100</p>\n"],
                    solution_en: "<p>18<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cars</span><span style=\"font-family: Cambria Math;\"> sold in January and April = 5400 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 2808</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">cars</span><span style=\"font-family: Cambria Math;\"> sold in May = 5400 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 648</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Diffe</span><span style=\"font-family: Cambria Math;\">rence between the cars = 2808 &minus; </span><span style=\"font-family: Cambria Math;\">648 = 2160</span></p>\n",
                    solution_hi: "<p>18<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2357;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2376;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> = 5400 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>52</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 2808</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> = 5400 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">= 648</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 2808 &minus; </span><span style=\"font-family: Cambria Math;\">648 = 2160</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A sum of Rs.4</span><span style=\"font-family: Cambria Math;\">,000</span><span style=\"font-family: Cambria Math;\"> becomes Rs.4,960 in 4 years at simple interest. If the rate of interest becomes 1.5 times of itself, then how much this sum will become after 4 years? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> Rs.4,000 </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> Rs.4,960 </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2351;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 1.5 </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Rs.5<span style=\"font-family: Cambria Math;\">,440</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Rs.5<span style=\"font-family: Cambria Math;\">,680</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>Rs.5<span style=\"font-family: Cambria Math;\">,280</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Rs.4<span style=\"font-family: Cambria Math;\">,890</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Rs.5<span style=\"font-family: Cambria Math;\">,440</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Rs.5<span style=\"font-family: Cambria Math;\">,680</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>Rs.5<span style=\"font-family: Cambria Math;\">,280</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Rs.4<span style=\"font-family: Cambria Math;\">,890</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>19<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Simple interest</span><span style=\"font-family: Cambria Math;\"> = amount &minus; </span><span style=\"font-family: Cambria Math;\">principle</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Simple interest = 4960 &minus;</span><span style=\"font-family: Cambria Math;\"> 4000 = </span><span style=\"font-family: Cambria Math;\">960</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Simple </span><span style=\"font-family: Cambria Math;\">interest =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mi>r</mi><mi>i</mi><mi>n</mi><mi>c</mi><mi>i</mi><mi>p</mi><mi>l</mi><mi>e</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">960 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Rate = 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New rate = 6 &times; 1.5 = </span><span style=\"font-family: Cambria Math;\">9</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Simple interest =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math> = </span><span style=\"font-family: Cambria Math;\">&#8377;1440</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New amount = 4000 + 1440 = </span><span style=\"font-family: Cambria Math;\">&#8377;5440</span></p>\n",
                    solution_hi: "<p>19<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2332;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2358;&#2367; <span style=\"font-family: Cambria Math;\">&minus; </span></span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2343;&#2344;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> = 4960 &minus;</span><span style=\"font-family: Cambria Math;\"> 4000 = </span><span style=\"font-family: Cambria Math;\">&#8377;960</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2332;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>&#2342;&#2352;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>&#2360;&#2350;&#2351;</mi><mo>&nbsp;</mo></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">960 =&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mi>&#2342;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 6</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 6 &times; 1.5 = </span><span style=\"font-family: Cambria Math;\">9</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math> = </span><span style=\"font-family: Cambria Math;\">&#8377;1440</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2358;</span><span style=\"font-family: Nirmala UI;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 4000 + 1440 = </span><span style=\"font-family: Cambria Math;\">&#8377;5440</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">20</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A</span><span style=\"font-family: Cambria Math;\"> alone can complete a work in 20 days with full efficiency. </span><span style=\"font-family: Cambria Math;\">If the efficiency of A decreases by 20 percent.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">then</span><span style=\"font-family: Cambria Math;\"> A alone can do the same work in how many days? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">20</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2375;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;</span><span style=\"font-family: Nirmala UI;\">&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2375;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>30</p>\n", "<p>20</p>\n", 
                                "<p>25</p>\n", "<p>36<span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>30</p>\n", "<p>20</p>\n",
                                "<p>25</p>\n", "<p>36</p>\n"],
                    solution_en: "<p>20<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Fraction value of 20% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Efficiency of A &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">before&nbsp; :</span><span style=\"font-family: Cambria Math;\">&nbsp; after</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;4</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total work = 20 &times; 5 &rarr; </span><span style=\"font-family: Cambria Math;\">100 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, A will do the all work =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>4</mn></mfrac></math> = </span><span style=\"font-family: Cambria Math;\">25 days</span></p>\n",
                    solution_hi: "<p>20<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">20% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2367;&#2344;&#2381;&#2344;&#2366;&#2340;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;</span><span style=\"font-family: Nirmala UI;\">&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2340;&#2366;&nbsp; <span style=\"font-family: Cambria Math;\">&rarr;&nbsp; </span></span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;</span><span style=\"font-family: Nirmala UI;\">&#2375;&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> :&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 4</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> = 20 &times; 5 = </span><span style=\"font-family: Cambria Math;\">100 &#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, A </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>4</mn></mfrac></math> = </span><span style=\"font-family: Cambria Math;\">25 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">What is the time taken by Mohan to cover one round of a circular park of diameter 420 m. if he walks at a speed of 9 km/</span><span style=\"font-family: Cambria Math;\">h ?</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">420 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2368;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2340;&#2381;&#2340;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2325;&#2381;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2379;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>8.8 minutes</p>\n", "<p>4.4 minutes</p>\n", 
                                "<p>6.6 minutes</p>\n", "<p>3.3 minutes</p>\n"],
                    options_hi: ["<p>8.8 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span></p>\n", "<p>4.4 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span></p>\n",
                                "<p>6.6 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span></p>\n", "<p>3.3 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span></p>\n"],
                    solution_en: "<p>21<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Circumference of circle = 2&pi;</span><span style=\"font-family: Cambria Math;\">r</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 210 = 1320</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr; 9 km/h = 9 &times;</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math> = 2.5m/s</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>d</mi><mi>i</mi><mi>s</mi><mi>tan</mi><mi>c</mi><mi>e</mi></mrow><mrow><mo>&nbsp;</mo><mi>s</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1320</mn><mrow><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = </span><span style=\"font-family: Cambria Math;\">528 sec = </span><span style=\"font-family: Cambria Math;\">8.8 minutus</span></p>\n",
                    solution_hi: "<p>21<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2357;&#2371;&#2340;&#2381;&#2340; &#2325;&#2368; &#2346;&#2352;&#2367;&#2343;&#2367; </span>&nbsp;= 2&pi;</span><span style=\"font-family: Cambria Math;\">r</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; 2 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 210 = 1320</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;&rArr; 9 km/h = 9 &times;</span><span style=\"font-family: Cambria Math;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math> = 2.5m/s</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2350;&#2351;</span> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#2327;&#2340;&#2367;</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2360;&#2350;&#2351;</span> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1320</mn><mrow><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = </span><span style=\"font-family: Cambria Math;\">528 <span style=\"font-weight: 400;\">&#2360;&#2375;.</span> = </span><span style=\"font-family: Cambria Math;\">8.8 <span style=\"font-weight: 400;\">&#2350;&#2367;&#2344;&#2335;</span></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The area of a square Is 1156 </span><span style=\"font-family: Cambria Math;\">cm&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;.</span><span style=\"font-family: Cambria Math;\"> What is the respective ratio between the length and the breadth of a rectangle whose length is twice the side of the square and whose breadth is 14 cm less than the side of the square? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">22</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> 1156 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;<span style=\"font-family: Cambria Math;\">&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2358;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 14 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">16 :</span><span style=\"font-family: Cambria Math;\"> 7 </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">19 :</span><span style=\"font-family: Cambria Math;\"> 5</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">17 :</span><span style=\"font-family: Cambria Math;\"> 5</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">21 :</span><span style=\"font-family: Cambria Math;\"> 19</span></p>\n"],
                    options_hi: ["<p>16 : 7</p>\n", "<p>19 : 5</p>\n",
                                "<p>17 : 5</p>\n", "<p>21 : 19</p>\n"],
                    solution_en: "<p>22<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of square = side&sup2;&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1156 </span><span style=\"font-family: Cambria Math;\">= side&sup2;</span><span style=\"font-family: Cambria Math;\"> , side = 34 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So the length of rectangle = 34 &times; 2</span><span style=\"font-family: Cambria Math;\"> = 68 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Breadth of rectangle = 34 &ndash;</span><span style=\"font-family: Cambria Math;\"> 14 = </span><span style=\"font-family: Cambria Math;\">20 cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio of length and breadth of rectangle </span><span style=\"font-family: Cambria Math;\">= 68</span><span style=\"font-family: Cambria Math;\"> : 20 &rArr; </span><span style=\"font-family: Cambria Math;\">17 : 5</span></p>\n",
                    solution_hi: "<p>22<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> = <span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;<span style=\"font-family: Cambria Math;\">&sup2;</span></span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1156 </span><span style=\"font-family: Cambria Math;\">= <span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;<span style=\"font-family: Cambria Math;\">&sup2;</span></span></span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\">&nbsp;= 34 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2350;&#2381;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 34 &times; 2</span><span style=\"font-family: Cambria Math;\"> = 68 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2312;</span><span style=\"font-family: Cambria Math;\"> = 34 &ndash; 14 = 20</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2350;&#2368;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2337;&#2364;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 68</span><span style=\"font-family: Cambria Math;\"> : 20 &rArr;</span><span style=\"font-family: Cambria Math;\">&nbsp;17 : 5</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A man can cover a certain distance in 2 hours and 10 minutes if he walks at the rate of 12 km/hr. If he covers the same distance on cycle at the rate of 13 km/</span><span style=\"font-family: Cambria Math;\">hr</span><span style=\"font-family: Cambria Math;\">, then find the time taken by him. </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">23. </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2311;&#2325;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 13 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>90 minutes</p>\n", "<p>110 minutes</p>\n", 
                                "<p>120 minutes</p>\n", "<p>11<span style=\"font-family: Cambria Math;\">5 minutes </span></p>\n"],
                    options_hi: ["<p>90 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>110 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>120 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>115 <span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>23<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Distance = speed &times; time</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Distance = 12 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac></math> = </span><span style=\"font-family: Cambria Math;\">26 km</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, time taken by man,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>d</mi><mi>i</mi><mi>s</mi><mi>tan</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>s</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&nbsp;&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>13</mn></mfrac></math> = 2 hours or 120 minutes</span></p>\n",
                    solution_hi: "<p>23<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> = 12 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac></math> = </span><span style=\"font-family: Cambria Math;\">26 km</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">,</span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2350;</span><span style=\"font-family: Nirmala UI;\">&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#2327;&#2340;&#2367;</mi></mfrac><mo>&nbsp;</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>26</mn><mn>13</mn></mfrac></math> = 2 </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 120 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2344;&#2335;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> What is the value of sec60&deg; + tan45&deg; &minus; sin&sup2;</span><span style=\"font-family: Cambria Math;\">90</span><span style=\"font-family: Cambria Math;\">&deg; ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> sec60&deg; + tan45&deg; &minus; sin&sup2;</span><span style=\"font-family: Cambria Math;\">90</span><span style=\"font-family: Cambria Math;\">&deg; </span><span style=\"font-family: Nirmala UI;\">&#2325;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ? </span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>1</p>\n", "<p>2</p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>1</p>\n", "<p>2</p>\n"],
                    solution_en: "<p>24<span style=\"font-family: Cambria Math;\">(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> sec60&deg; + tan45&deg; &minus; sin&sup2;</span><span style=\"font-family: Cambria Math;\">90</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; 2 + 1 &minus;</span><span style=\"font-family: Cambria Math;\"> 1 = 2</span></p>\n",
                    solution_hi: "<p>24<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">sec60&deg; + tan45&deg; &minus; sin&sup2;</span><span style=\"font-family: Cambria Math;\">90</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; 2 + 1 &minus;</span><span style=\"font-family: Cambria Math;\"> 1 = 2</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">25</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Amar buys an old washing machine for Rs.4</span><span style=\"font-family: Cambria Math;\">,500</span><span style=\"font-family: Cambria Math;\"> and spends Rs.500 on its repair. If he </span><span style=\"font-family: Cambria Math;\">sells</span><span style=\"font-family: Cambria Math;\"> the washing machine for Rs.4,500, then what is h</span><span style=\"font-family: Cambria Math;\">is loss percent ? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">25</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2350;&#2352;</span><span style=\"font-family: Cambria Math;\"> 4,500 </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2352;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2358;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2352;&#2350;&#2381;&#2350;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 500 </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2358;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 4,500 </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>6%</p>\n", "<p>8%</p>\n", 
                                "<p>10%</p>\n", "<p>12%</p>\n"],
                    options_hi: ["<p>6%</p>\n", "<p>8%</p>\n",
                                "<p>10%</p>\n", "<p>12%</p>\n"],
                    solution_en: "<p>25<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cost price of machine = 4500 + 500 = &#8377;5000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling price = 4500</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Loss percentage =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4500</mn></mrow><mn>5000</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = </span><span style=\"font-family: Cambria Math;\">10%</span></p>\n",
                    solution_hi: "<p>25<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2358;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 4500 + 500 = &#8377;5000</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;</span><span style=\"font-family: Nirmala UI;\">&#2351;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 4500</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Nirmala UI;\">&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4500</mn></mrow><mn>5000</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; 100 = </span><span style=\"font-family: Cambria Math;\">10%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>