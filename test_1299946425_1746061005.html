<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the figure from among the given options that can logically replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744330.png\" alt=\"rId4\" width=\"334\" height=\"60\"></p>",
                    question_hi: "<p>1. दिए गए विकल्पों में से उस आकृति का चयन कीजिए, जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को तार्किक रूप से प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744330.png\" alt=\"rId4\" width=\"334\" height=\"60\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744487.png\" alt=\"rId5\" width=\"84\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744614.png\" alt=\"rId6\" width=\"84\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744820.png\" alt=\"rId7\" width=\"82\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744912.png\" alt=\"rId8\" width=\"78\" height=\"70\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744487.png\" alt=\"rId5\" width=\"85\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744614.png\" alt=\"rId6\" width=\"84\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744820.png\" alt=\"rId7\" width=\"81\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744912.png\" alt=\"rId8\" width=\"78\" height=\"70\"></p>"
                    ],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744820.png\" alt=\"rId7\" width=\"81\" height=\"70\"></p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650744820.png\" alt=\"rId7\" width=\"83\" height=\"71\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the number from among the given options that can replace the question mark (?) in the following series.<br>300, 152, 80, 48, 40, 52, ?</p>",
                    question_hi: "<p>2. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>300, 152, 80, 48, 40, 52, ?</p>",
                    options_en: [
                        "<p>60</p>",
                        "<p>80</p>",
                        "<p>90</p>",
                        "<p>100</p>"
                    ],
                    options_hi: [
                        "<p>60</p>",
                        "<p>80</p>",
                        "<p>90</p>",
                        "<p>100</p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745035.png\" alt=\"rId9\" width=\"274\" height=\"40\"></p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745035.png\" alt=\"rId9\" width=\"274\" height=\"40\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. 4 is related to 125 following a certain logic. Following the same logic, 8 is related to 729. Which of the following numbers is related to 1728 using the same logic ?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>3. एक निश्चित तर्क का अनुसरण करते हुए 4, 125 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 8, 729 से संबंधित है। उसी तर्क का उपयोग करते हुए निम्नलिखित में से कौन सी संख्या 1728 से संबंधित है ?&nbsp;<br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना संक्रियाएँ केवल पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए - 13 पर की जाने वाली संक्रियाएँ, जैसे 13 में जोड़ना / घटाना / गुणा करना इत्यादि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>9</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>9</p>"
                    ],
                    solution_en: "<p>3.(a)<br><strong>Logic:- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>o</mi><mi>n</mi><mi>d</mi><mo>&#160;</mo><mi>n</mi><mi>o</mi><mo>.</mo></mrow><mn>3</mn></mroot></math></strong>&nbsp;- 1 = first&nbsp;no.<br>(4, 125):- <math display=\"inline\"><mroot><mrow><mn>125</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> - 1 &rarr; 5 - 1 = 4<br>(8, 729):- <math display=\"inline\"><mroot><mrow><mn>729</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> - 1 &rarr; 9 - 1 = 8<br>Similarly <br>(?, 1728):- <math display=\"inline\"><mroot><mrow><mn>1728</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> - 1 &rarr; 12 - 1 = 11</p>",
                    solution_hi: "<p>3.(a)<br><strong>तर्क :-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mroot><mrow><mi>&#2342;&#2370;&#2360;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>3</mn></mroot></math> - 1 = पहली संख्या <br>(4, 125):- <math display=\"inline\"><mroot><mrow><mn>125</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> - 1 &rarr; 5 - 1 = 4<br>(8, 729):- <math display=\"inline\"><mroot><mrow><mn>729</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> - 1 &rarr; 9 - 1 = 8<br>इसी प्रकार <br>(?, 1728):- <math display=\"inline\"><mroot><mrow><mn>1728</mn></mrow><mrow><mn>3</mn></mrow></mroot></math> - 1 &rarr; 12 - 1 = 11</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. If in a code language, REPORT is coded as 3 and CALM is coded as 2, what will be the code for TOGETHER?</p>",
                    question_hi: "<p>4. यदि एक कूट भाषा में REPORT को 3 के रूप में और CALM को 2 के रूप में कूटबद्ध किया जाता है, तो TOGETHER के लिए कूट क्या होगा?</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>10</p>",
                        "<p>5</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>10</p>",
                        "<p>5</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>4.(d)<br><strong>Logic :-</strong> (Total number of letters) <math display=\"inline\"><mo>&#247;</mo></math> 2<br>REPORT :- (6 Letter) <math display=\"inline\"><mo>&#247;</mo></math> 2 = 3<br>CALM :- (4 Letter) <math display=\"inline\"><mo>&#247;</mo></math> 2 = 2<br>Similarly,<br>TOGETHER :- (8 Letter) <math display=\"inline\"><mo>&#247;</mo></math> 2 = 4</p>",
                    solution_hi: "<p>4.(d)<br><strong>तर्क :-</strong> (कुल अक्षरों की संख्या) <math display=\"inline\"><mo>&#247;</mo></math> 2<br>REPORT :- (6 अक्षर) <math display=\"inline\"><mo>&#247;</mo></math> 2 = 3<br>CALM :- (4 अक्षर) <math display=\"inline\"><mo>&#247;</mo></math> 2 = 2<br>इसी प्रकार,<br>TOGETHER :- (8 अक्षर) <math display=\"inline\"><mo>&#247;</mo></math> 2 = 4</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?&nbsp;<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>5. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है?&nbsp;<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>360 &ndash; 12 &ndash; 6</p>",
                        "<p>82 &ndash; 8 &ndash; 2</p>",
                        "<p>63 &ndash; 6 &ndash; 3</p>",
                        "<p>164 &ndash; 10 &ndash; 4</p>"
                    ],
                    options_hi: [
                        "<p>360 &ndash; 12 &ndash; 6</p>",
                        "<p>82 &ndash; 8 &ndash; 2</p>",
                        "<p>63 &ndash; 6 &ndash; 3</p>",
                        "<p>164 &ndash; 10 &ndash; 4</p>"
                    ],
                    solution_en: "<p>5.(b) <strong>Logic: </strong>(2nd number)&sup2; + (3rd number)&sup3; = 1st number<br>360 &ndash; 12 &ndash; 6 :- 12&sup2; + 6&sup3; = 144 + 216 = 360<br>63 &ndash; 6 &ndash; 3 :- 6&sup2; + 3&sup3; = 36 + 27 = 63<br>164 &ndash; 10 &ndash; 4 :- 10&sup2; + 4&sup3; = 100 + 64 = 164<br>But<br>82 &ndash; 8 &ndash; 2 :- 8&sup2; + 2&sup3; = 64 +8 = 72 (<math display=\"inline\"><mo>&#8800;</mo></math> 82)</p>",
                    solution_hi: "<p>5.(b) <strong>तर्क:</strong> (दूसरी संख्या)&sup2; + (तीसरी संख्या)&sup3; = पहली संख्या<br>360 &ndash; 12 &ndash; 6 :- 12&sup2; + 6&sup3; = 144 + 216 = 360<br>63 &ndash; 6 &ndash; 3 :- 6&sup2; + 3&sup3; = 36 + 27 = 63<br>164 &ndash; 10 &ndash; 4 :- 10&sup2; + 4&sup3; = 100 + 64 = 164<br>लेकिन<br>82 &ndash; 8 &ndash; 2 :- 8&sup2; + 2&sup3; = 64 +8 = 72 (<math display=\"inline\"><mo>&#8800;</mo></math>&nbsp;82)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the option that represents the letters that when placed from left to right in the blanks below will complete the letter series. <br>L_P_QS_K_P_S_KP_Q_L_ _PQS</p>",
                    question_hi: "<p>6. उस विकल्प का चयन कीजिए जो उन अक्षरों को निरूपित करता है जिन्हें नीचे रिक्त स्थानों में बाएँ से दाएँ रखने पर अक्षर श्रृंखला पूरी हो जाएगी। <br>L_P_QS_K_P_S_KP_Q_L_ _PQS</p>",
                    options_en: [
                        "<p>KPLPQLPSKP</p>",
                        "<p>KPLPLQLSPK</p>",
                        "<p>KPLPQQLPSK</p>",
                        "<p>PKLPQPLSKP</p>"
                    ],
                    options_hi: [
                        "<p>KPLPQLPSKP</p>",
                        "<p>KPLPLQLSPK</p>",
                        "<p>KPLPQQLPSK</p>",
                        "<p>PKLPQPLSKP</p>"
                    ],
                    solution_en: "<p>6.(a) L<span style=\"text-decoration: underline;\"><strong>K</strong></span>P<span style=\"text-decoration: underline;\"><strong>P</strong></span>QS/ <span style=\"text-decoration: underline;\"><strong>L</strong></span>K<strong><span style=\"text-decoration: underline;\">P</span></strong>P<span style=\"text-decoration: underline;\"><strong>Q</strong></span>S/ <span style=\"text-decoration: underline;\"><strong>L</strong></span>KP<span style=\"text-decoration: underline;\"><strong>P</strong></span>Q<span style=\"text-decoration: underline;\"><strong>S</strong></span> / L<span style=\"text-decoration: underline;\"><strong>KP</strong></span>PQS</p>",
                    solution_hi: "<p>6.(a) L<span style=\"text-decoration: underline;\"><strong>K</strong></span>P<span style=\"text-decoration: underline;\"><strong>P</strong></span>QS/ <span style=\"text-decoration: underline;\"><strong>L</strong></span>K<strong><span style=\"text-decoration: underline;\">P</span></strong>P<span style=\"text-decoration: underline;\"><strong>Q</strong></span>S/ <span style=\"text-decoration: underline;\"><strong>L</strong></span>KP<span style=\"text-decoration: underline;\"><strong>P</strong></span>Q<span style=\"text-decoration: underline;\"><strong>S</strong></span> / L<span style=\"text-decoration: underline;\"><strong>KP</strong></span>PQS</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In this question, three statements are given, followed by two conclusions numbered I&nbsp;and II. Assuming the statements to be true, even if they seem to be at variance with&nbsp;commonly known facts, decide which of the conclusions logically follows/follow from&nbsp;the statements.<br><strong>Statements :</strong><br>I. Some tables are chairs.<br>II. All desks are chairs.<br>III. No chair is a fan.<br><strong>Conclusions :</strong><br>I. Some desks are tables. <br>II. Some fans are desks.</p>",
                    question_hi: "<p>7. इस प्रश्न में, तीन कथन और उसके बाद दो निष्कर्ष I और II दिए गए हैं। कथनों को सत्य मानते हुए, भले&nbsp;ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्णय लें कि कौन-सा/से निष्कर्ष तार्किक रूप से कथनों का अनुसरण करता/करते है/हैं।<br><strong>कथन :</strong> <br>I. कुछ टेबल, कुर्सियाँ हैं।<br>II. सभी डेस्क, कुर्सियाँ हैं।<br>III. कोई कुर्सी, पंखा नहीं है।<br><strong>निष्कर्ष :</strong><br>I. कुछ डेस्क, टेबल हैं। <br>II. कुछ पंखे, डेस्क हैं।</p>",
                    options_en: [
                        "<p>Only conclusion I follows</p>",
                        "<p>Both conclusions I and II follow</p>",
                        "<p>Only conclusion II follows</p>",
                        "<p>Neither conclusion I nor II follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>",
                        "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>न तो निष्कर्ष I और न ही II अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>7.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745170.png\" alt=\"rId10\" width=\"356\" height=\"71\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745331.png\" alt=\"rId11\" width=\"338\" height=\"70\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option figure in which the given figure is embedded as its part (rotation is NOT allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745475.png\" alt=\"rId12\" width=\"118\" height=\"80\"></p>",
                    question_hi: "<p>8. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति उसके भाग के रूप में निहित है (घूर्णन की अनुमति नहींहै)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745475.png\" alt=\"rId12\" width=\"118\" height=\"80\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745646.png\" alt=\"rId13\" width=\"73\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745749.png\" alt=\"rId14\" width=\"81\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745848.png\" alt=\"rId15\" width=\"74\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745945.png\" alt=\"rId16\" width=\"76\" height=\"75\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745646.png\" alt=\"rId13\" width=\"73\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745749.png\" alt=\"rId14\" width=\"81\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745848.png\" alt=\"rId15\" width=\"74\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650745945.png\" alt=\"rId16\" width=\"76\" height=\"75\"></p>"
                    ],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746050.png\" alt=\"rId17\" width=\"98\" height=\"97\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746050.png\" alt=\"rId17\" width=\"98\" height=\"97\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. AZ 1 is related to CX 3 in a certain way based on the English alpha-numerical order. In the same way, EV 5 is related to GT 7. To which of the following is IR 9 related, following the same logic ?</p>",
                    question_hi: "<p>9. अंग्रेजी के अक्षरांकीय क्रम के अनुसार AZ1 एक निश्चित तरीके के आधार पर CX3 से संबंधित है। EV5 इसी प्रकार से GT7 से संबंधित है। तो, इसी तर्क के अनुसार IR9 इनमें से किससे संबंधित होगा ?</p>",
                    options_en: [
                        "<p>KP 11</p>",
                        "<p>KQ 11</p>",
                        "<p>PK 11</p>",
                        "<p>MN 13</p>"
                    ],
                    options_hi: [
                        "<p>KP 11</p>",
                        "<p>KQ 11</p>",
                        "<p>PK 11</p>",
                        "<p>MN 13</p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746186.png\" alt=\"rId18\" width=\"101\" height=\"80\">&nbsp; &nbsp;and&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746477.png\" alt=\"rId19\" width=\"102\" height=\"80\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746679.png\" alt=\"rId20\" width=\"102\" height=\"80\"></p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746186.png\" alt=\"rId18\" width=\"101\" height=\"80\"> &nbsp; और &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746477.png\" alt=\"rId19\" width=\"102\" height=\"80\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746679.png\" alt=\"rId20\" width=\"102\" height=\"80\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language, \'SPIKE\' is coded as \'82467\' and \'LIKES\' is coded as \'47298\'. What is the code for \'P\' in the given code language?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में, \'SPIKE\' को \'82467\' लिखा जाता है और \'LIKES\' को \'47298\' लिखा जाता है। उसी कूट भाषा में \'P\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>10.(b) SPIKE :- 82467&hellip;&hellip;&hellip;(i)<br>LIKES :- 47298&hellip;&hellip;..(ii)<br>From (i) and (ii) &lsquo;IKES&rsquo; and &lsquo;2478&rsquo; are common. The code of &lsquo;P&rsquo; = &lsquo;6&rsquo;.</p>",
                    solution_hi: "<p>10.(b) SPIKE :- 82467&hellip;&hellip;&hellip;(i)<br>LIKES :- 47298&hellip;&hellip;..(ii)<br>(i) और (ii) से &lsquo;IKES&rsquo; और &lsquo;2478&rsquo; उभयनिष्ठ हैं। &lsquo;P&rsquo; का कोड = &lsquo;6&rsquo; है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746806.png\" alt=\"rId21\" width=\"118\" height=\"100\"></p>",
                    question_hi: "<p>11. दर्पण को MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746806.png\" alt=\"rId21\" width=\"118\" height=\"100\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746906.png\" alt=\"rId22\" width=\"126\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747045.png\" alt=\"rId23\" width=\"129\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747206.png\" alt=\"rId24\" width=\"121\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747310.png\" alt=\"rId25\" width=\"125\" height=\"20\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650746906.png\" alt=\"rId22\" width=\"126\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747045.png\" alt=\"rId23\" width=\"129\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747206.png\" alt=\"rId24\" width=\"121\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747310.png\" alt=\"rId25\" width=\"125\" height=\"20\"></p>"
                    ],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747045.png\" alt=\"rId23\" width=\"136\" height=\"21\"></p>",
                    solution_hi: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747045.png\" alt=\"rId23\" width=\"129\" height=\"20\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Which letter-cluster will replace the question mark (?) to complete the given series?<br>BKUZ, ENWB, ?, KTAF, NWCH</p>",
                    question_hi: "<p>12. दी गई शृंखला को पूरा करने के लिए कौन-सा अक्षर-समूह प्रश्न-चिह्न (?) को प्रतिस्थापित करेगा?<br>BKUZ, ENWB, ?, KTAF, NWCH</p>",
                    options_en: [
                        "<p>HQYD</p>",
                        "<p>HRYE</p>",
                        "<p>HRZD</p>",
                        "<p>HRYD</p>"
                    ],
                    options_hi: [
                        "<p>HQYD</p>",
                        "<p>HRYE</p>",
                        "<p>HRZD</p>",
                        "<p>HRYD</p>"
                    ],
                    solution_en: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747413.png\" alt=\"rId26\" width=\"342\" height=\"100\"></p>",
                    solution_hi: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747413.png\" alt=\"rId26\" width=\"342\" height=\"100\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Identify the figure given in the options that when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747521.png\" alt=\"rId27\" width=\"294\" height=\"67\"></p>",
                    question_hi: "<p>13. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747521.png\" alt=\"rId27\" width=\"294\" height=\"67\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747630.png\" alt=\"rId28\" width=\"82\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747763.png\" alt=\"rId29\" width=\"84\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747868.png\" alt=\"rId30\" width=\"86\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747987.png\" alt=\"rId31\" width=\"88\" height=\"86\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747630.png\" alt=\"rId28\" width=\"82\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747763.png\" alt=\"rId29\" width=\"83\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747868.png\" alt=\"rId30\" width=\"86\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747987.png\" alt=\"rId31\" width=\"88\" height=\"86\"></p>"
                    ],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747630.png\" alt=\"rId28\" width=\"86\" height=\"84\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650747630.png\" alt=\"rId28\" width=\"86\" height=\"84\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. If &lsquo;A&rsquo; stands for &lsquo;+&rsquo;, &lsquo;B&rsquo; stands for &lsquo;&ndash;&rsquo;, &lsquo;C&rsquo; stands for &lsquo;&times;&rsquo; and &lsquo;D&rsquo; stands for &lsquo;&divide;&rsquo;, what will come in place of the question mark (?) in the following equation ?<br>69 D 3 A 4 C 7 B 10 = ?</p>",
                    question_hi: "<p>14. यदि \'A\' का अर्थ \'+\' है, \'B\' का अर्थ \'<math display=\"inline\"><mo>-</mo></math>\' है, \'C\' का अर्थ \'&times;\' है और \'D\' का अर्थ \'&divide;\' है, तो निम्नलिखित समीकरण में प्रश्न-चिह्न (?) के स्थान पर क्या आएगा ?<br>69 D 3 A 4 C 7 B 10 = ?</p>",
                    options_en: [
                        "<p>37</p>",
                        "<p>41</p>",
                        "<p>35</p>",
                        "<p>39</p>"
                    ],
                    options_hi: [
                        "<p>37</p>",
                        "<p>41</p>",
                        "<p>35</p>",
                        "<p>39</p>"
                    ],
                    solution_en: "<p>14.(b)<strong> Given :-</strong> 69 D 3 A 4 C 7 B 10<br>As per given instruction after interchanging the letter with sign we get,<br>69 &divide;&nbsp;3 + 4 &times; 7 - 10<br>23 + 28 - 10<br>51 - 10 = 41</p>",
                    solution_hi: "<p>14.(b) <strong>दिया गया :- </strong>69 D 3 A 4 C 7 B 10<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने के बाद हमें प्राप्त होता है,<br>69 &divide;&nbsp;3 + 4 &times; 7 - 10<br>23 + 28 - 10<br>51 - 10 = 41</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the option that indicates the correct arrangement of the given words in the order in which they appear in an English dictionary.<br>1- Elate<br>2- Election<br>3- Elbow<br>4- Elastic<br>5- Electoral</p>",
                    question_hi: "<p>15. उस विकल्प का चयन कीजिए जो दिए गए शब्दों को उस सही क्रम को दर्शाता है जिस क्रम में वे अंग्रेजी शब्दकोश में दिखाई देते हैं।<br>1- Elate<br>2- Election<br>3- Elbow<br>4- Elastic<br>5- Electoral</p>",
                    options_en: [
                        "<p>1, 4, 5, 2, 3</p>",
                        "<p>4, 1, 3, 2, 5</p>",
                        "<p>4, 3, 2, 1, 5</p>",
                        "<p>1, 3, 4, 2, 5</p>"
                    ],
                    options_hi: [
                        "<p>1, 4, 5, 2, 3</p>",
                        "<p>4, 1, 3, 2, 5</p>",
                        "<p>4, 3, 2, 1, 5</p>",
                        "<p>1, 3, 4, 2, 5</p>"
                    ],
                    solution_en: "<p>15.(b) <strong>The correct order is :-</strong> <br>Elastic(4) &rarr; Elate(1) &rarr; Elbow(3) &rarr; Election(2) &rarr; Electoral(5)</p>",
                    solution_hi: "<p>15.(b) <strong>सही क्रम है:-</strong><br>Elastic(4) &rarr; Elate(1) &rarr; Elbow(3) &rarr; Election(2) &rarr; Electoral(5)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the option that is related to the third number in the same way as the second number is related to the first number and the sixth number is related to the fifth number.<br>16 : 85 :: 23 : ? :: 28 : 145<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>16. उस विकल्प का चयन कीजिए जो तीसरी संख्या से उसी प्रकार संबंधित है जिस प्रकार दूसरी संख्या पहली संख्या से संबंधित है और छठी संख्या पांचवीं संख्या से संबंधित है।<br>16 : 85 :: 23 : ? :: 28 : 145<br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना संक्रियाएँ केवल पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए -13 पर की जाने वाली संक्रियाएँ, जैसे 13 में जोड़ना / घटाना / गुणा करना इत्यादि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>135</p>",
                        "<p>105</p>",
                        "<p>120</p>",
                        "<p>150</p>"
                    ],
                    options_hi: [
                        "<p>135</p>",
                        "<p>105</p>",
                        "<p>120</p>",
                        "<p>150</p>"
                    ],
                    solution_en: "<p>16.(c)<br><strong>Logic:- </strong><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi>st</mi></msup></math>no. &times; 5 + 5 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mi>nd</mi></msup></math>no.<br>(16 : 85):- 16 <math display=\"inline\"><mo>&#215;</mo></math> 5 + 5 &rarr; 80 + 5 = 85<br>(28 : 145):- 28 <math display=\"inline\"><mo>&#215;</mo></math> 5 + 5 &rarr; 140 + 5 = 145<br>Similarly<br>(23 : ?):- 23 <math display=\"inline\"><mo>&#215;</mo></math> 5 + 5 &rarr; 115 + 5 = 120</p>",
                    solution_hi: "<p>16.(c)<br><strong>तर्क :-</strong> पहली संख्या <math display=\"inline\"><mo>&#215;</mo></math> 5 + 5 = दूसरी संख्या <br>(16 : 85):- 16 <math display=\"inline\"><mo>&#215;</mo></math> 5 + 5 &rarr; 80 + 5 = 85<br>(28 : 145):- 28 <math display=\"inline\"><mo>&#215;</mo></math> 5 + 5 &rarr; 140 + 5 = 145<br>इसी प्रकार <br>(23 : ?):- 23 <math display=\"inline\"><mo>&#215;</mo></math> 5 + 5 &rarr; 115 + 5 = 120</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748102.png\" alt=\"rId32\" width=\"86\" height=\"92\"></p>",
                    question_hi: "<p>17. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748102.png\" alt=\"rId32\" width=\"86\" height=\"92\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748314.png\" alt=\"rId33\" width=\"101\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748413.png\" alt=\"rId34\" width=\"100\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748565.png\" alt=\"rId35\" width=\"104\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748680.png\" alt=\"rId36\" width=\"103\" height=\"24\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748314.png\" alt=\"rId33\" width=\"101\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748413.png\" alt=\"rId34\" width=\"100\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748565.png\" alt=\"rId35\" width=\"104\" height=\"20\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748680.png\" alt=\"rId36\" width=\"103\" height=\"24\"></p>"
                    ],
                    solution_en: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748565.png\" alt=\"rId35\" width=\"104\" height=\"20\"></p>",
                    solution_hi: "<p>17.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748565.png\" alt=\"rId35\" width=\"104\" height=\"20\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;;<br>&lsquo;A &minus; B&rsquo; means &lsquo;A is the brother of B&rsquo;;<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo; and<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the father of B&rsquo;.<br>Based on the above, how is 4 related to 2 if &lsquo;4 + 3 &divide; 2 &divide; 1 &minus; 5&rsquo; ?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है \'A, B की माता है\';<br>\'A &minus; B\' का अर्थ है \'A, B का भाई है\';<br>\'A &times; B\' का अर्थ है \'A, B की पत्नी है\' और<br>\'A &divide; B\' का अर्थ है \'A, B का पिता है\'।<br>उपर्युक्त के आधार पर, यदि \'4 + 3 &divide; 2 &divide; 1 &minus; 5\' है, तो 4, 2 से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Father&rsquo;s father</p>",
                        "<p>Father&rsquo;s mother</p>",
                        "<p>Mother&rsquo;s father</p>",
                        "<p>Mother&rsquo;s mother</p>"
                    ],
                    options_hi: [
                        "<p>पिता का पिता</p>",
                        "<p>पिता की माता</p>",
                        "<p>माता का पिता</p>",
                        "<p>माता की माता</p>"
                    ],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748813.png\" alt=\"rId37\" width=\"99\" height=\"159\"><br>4 is the mother of 2&rsquo;s father.</p>",
                    solution_hi: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650748813.png\" alt=\"rId37\" width=\"99\" height=\"159\"><br>4, 2 के पिता की माँ है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter cluster that is different.&nbsp;<br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster</p>",
                    question_hi: "<p>19. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एकसमान हैं और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए।&nbsp;<br>नोट: अक्षर समूह में, असंगत व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: [
                        "<p>ORTU</p>",
                        "<p>XZCH</p>",
                        "<p>MORW</p>",
                        "<p>PRUZ</p>"
                    ],
                    options_hi: [
                        "<p>ORTU</p>",
                        "<p>XZCH</p>",
                        "<p>MORW</p>",
                        "<p>PRUZ</p>"
                    ],
                    solution_en: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749009.png\" alt=\"rId38\" width=\"109\" height=\"50\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749243.png\" alt=\"rId39\" width=\"112\" height=\"50\">&nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749386.png\" alt=\"rId40\" width=\"110\" height=\"50\"><br>but,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749646.png\" alt=\"rId41\" width=\"109\" height=\"50\"></p>",
                    solution_hi: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749009.png\" alt=\"rId38\" width=\"109\" height=\"50\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749243.png\" alt=\"rId39\" width=\"112\" height=\"50\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749386.png\" alt=\"rId40\" width=\"121\" height=\"55\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749646.png\" alt=\"rId41\" width=\"109\" height=\"50\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Panther, Bird, Eagle</p>",
                    question_hi: "<p>20. उस वेन आरेख का चयन कीजिये जो निम्नलिखित वर्गों के बीच के संबंध को सर्वश्रेष्ठ रूप से दर्शाता है।<br>चीता, पक्षी, चील</p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749842.png\" alt=\"rId42\" width=\"127\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749939.png\" alt=\"rId43\" width=\"81\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750010.png\" alt=\"rId44\" width=\"109\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750207.png\" alt=\"rId45\" width=\"70\" height=\"70\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749842.png\" alt=\"rId42\" width=\"127\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650749939.png\" alt=\"rId43\" width=\"81\" height=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750010.png\" alt=\"rId44\" width=\"109\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750207.png\" alt=\"rId45\" width=\"70\" height=\"70\"></p>"
                    ],
                    solution_en: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750323.png\" alt=\"rId46\" width=\"243\" height=\"82\"></p>",
                    solution_hi: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750487.png\" alt=\"rId47\" width=\"214\" height=\"89\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the set in which the numbers are related in the same way as are the numbers of the followingsets.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(7, 11, 33)<br>(14, 18, 54)</p>",
                    question_hi: "<p>21. उस सेट का चयन कीजिए, जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित सेटों की संख्याएं संबंधित हैं।&nbsp;<br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 को लीजिए 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>(7, 11, 33)<br>(14, 18, 54)</p>",
                    options_en: [
                        "<p>(16, 20, 40)</p>",
                        "<p>(5, 9, 36)</p>",
                        "<p>(12, 16, 48)</p>",
                        "<p>(18, 21, 63)</p>"
                    ],
                    options_hi: [
                        "<p>(16, 20, 40)</p>",
                        "<p>(5, 9, 36)</p>",
                        "<p>(12, 16, 48)</p>",
                        "<p>(18, 21, 63)</p>"
                    ],
                    solution_en: "<p>21.(c) <strong>Logic:- </strong>(2nd number) = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>st</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi>rd</mi><mi mathvariant=\"normal\">&#160;</mi><mi>number</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn></mrow><mn>4</mn></mfrac></math>)<br>(11) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>+</mo><mn>33</mn><mo>+</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>44</mn><mn>4</mn></mfrac></mstyle></math><br>(18) = <math display=\"inline\"><mfrac><mrow><mn>14</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>54</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>72</mn><mn>4</mn></mfrac></mstyle></math><br>Similarly,<br>(16) = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>48</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><mn>4</mn></mfrac></mstyle></math></p>",
                    solution_hi: "<p>21.(c) <strong>तर्क:- </strong>(दूसरी संख्या) = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2340;&#2368;&#2360;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn></mrow><mn>4</mn></mfrac></math>)<br>(11) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>33</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>44</mn><mn>4</mn></mfrac></mstyle></math><br>(18) = <math display=\"inline\"><mfrac><mrow><mn>14</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>54</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>72</mn><mn>4</mn></mfrac></mstyle></math><br>इसी प्रकार,<br>(16) = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>48</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><mn>4</mn></mfrac></mstyle></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Which of the four options will replace the question mark (?) in the following series? <br>XF 11 , YI 15, ZL 19 , AO 23, ?</p>",
                    question_hi: "<p>22. चार विकल्पों में से कौन सा विकल्प निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आएगा?<br>XF 11 , YI 15, ZL 19 , AO 23, ?</p>",
                    options_en: [
                        "<p>CQ 25</p>",
                        "<p>BP 26</p>",
                        "<p>CR 25</p>",
                        "<p>BR 27</p>"
                    ],
                    options_hi: [
                        "<p>CQ 25</p>",
                        "<p>BP 26</p>",
                        "<p>CR 25</p>",
                        "<p>BR 27</p>"
                    ],
                    solution_en: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750654.png\" alt=\"rId48\" width=\"361\" height=\"132\"></p>",
                    solution_hi: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750654.png\" alt=\"rId48\" width=\"361\" height=\"132\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In this question, three statements are given, followed by two conclusions numbered I and II. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follows/follow from the statements.<br><strong>Statements :</strong><br>I. Some lotuses are daisies.<br>Il. No daisy is a rose.<br>III. All thorns are roses.<br><strong>Conclusions :</strong><br>I. No thorn is a daisy.<br>Il. Some roses are lotuses.</p>",
                    question_hi: "<p>23. इस प्रश्न में, तीन कथन और उसके बाद दो निष्कर्ष। और॥ दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्णय लें कि कौन-सा/से निष्कर्ष तार्किक रूप से कथनों का अनुसरण करता/करते है/हैं।<br><strong>कथन :</strong><br>I. कुछ कमल, गुलबहार हैं।<br>II. कोई गुलबहार, गुलाब नहीं है।<br>III. सभी कांटे, गुलाब हैं।<br><strong>निष्कर्ष :</strong><br>I. कोई कांटा, गुलबहार नहीं है।<br>II. कुछ गुलाब, कमल हैं।</p>",
                    options_en: [
                        "<p>Only conclusion I follows</p>",
                        "<p>Only conclusion II follows</p>",
                        "<p>Both conclusions I and II follow</p>",
                        "<p>Neither conclusion I nor II follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष। अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>दोनों निष्कर्ष। और II अनुसरण करते हैं</p>",
                        "<p>न तो निष्कर्ष। और न ही II अनुसरण करता</p>"
                    ],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750758.png\" alt=\"rId49\"><br>Only conclusion I follows.</p>",
                    solution_hi: "<p>23.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750867.png\" alt=\"rId50\"><br>केवल निष्कर्ष I अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. In a certain language, GATE is coded as FZSD, and PANT is coded as OZMS. How will BANK be coded in the same language ?</p>",
                    question_hi: "<p>24. एक निश्चित भाषा में GATE को FZSD और PANT को OZMS के रूप में कूटबद्ध किया जाता है। उस भाषा में BANK को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: [
                        "<p>CBOK</p>",
                        "<p>ABKN</p>",
                        "<p>KNAB</p>",
                        "<p>AZMJ</p>"
                    ],
                    options_hi: [
                        "<p>CBOK</p>",
                        "<p>ABKN</p>",
                        "<p>KNAB</p>",
                        "<p>AZMJ</p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750990.png\" alt=\"rId51\" width=\"120\" height=\"82\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751172.png\" alt=\"rId52\" width=\"120\" height=\"80\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751286.png\" alt=\"rId53\" width=\"120\" height=\"80\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650750990.png\" alt=\"rId51\" width=\"120\" height=\"82\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751172.png\" alt=\"rId52\" width=\"120\" height=\"80\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751286.png\" alt=\"rId53\" width=\"120\" height=\"80\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. What will come in place of the question mark (?) in the following equation, if &lsquo;&times;&rsquo; is replaced by &lsquo;&divide;&rsquo;, &lsquo;&divide;&rsquo; is replaced by &lsquo;+&rsquo;, &lsquo;+&rsquo; is replaced by &lsquo;&times;&rsquo; and &lsquo;&minus;&rsquo; remains unchanged? <br>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?</p>",
                    question_hi: "<p>25. यदि \'&times;\' को \'&divide;\' से बदल दिया जाए, \'&divide;\' को \'+\' से बदल दिया जाए, \'+\' को \'&times;\' से बदल दिया जाए और \'&minus;\' अपरिवर्तित रहे, तो निम्नलिखित समीकरण में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आएगा ?&nbsp;<br>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?</p>",
                    options_en: [
                        "<p>23</p>",
                        "<p>33</p>",
                        "<p>29</p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>23</p>",
                        "<p>33</p>",
                        "<p>29</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>25.(b) <strong>Given:</strong> 42 &times; 3 &divide; 4 + 9 &minus; 17 = ?<br>As per the instructions after interchanging the symbol , we get.<br>42 &divide; 3 + 4 &times; 9 &minus; 17 = ? <br>14 + 4 &times; 9 - 17 <br>14 + 36 - 17<br>50 - 17 = 33</p>",
                    solution_hi: "<p>25.(b) <strong>दिया गया है: </strong>42 &times; 3 &divide; 4 + 9 &minus; 17 = ?<br>निर्देशों के अनुसार, चिन्हों को बदलने के बाद हमें प्राप्त होता है।<br>42 &divide; 3 + 4 &times; 9 &minus; 17 = ? <br>14 + 4 &times; 9 - 17 <br>14 + 36 - 17<br>50 - 17 = 33</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. In general, deficit is considered as a ________ concept.</p>",
                    question_hi: "<p>26. सामान्यतः, घाटे को एक __________ संकल्पना माना जाता है।</p>",
                    options_en: [
                        "<p>surplus</p>",
                        "<p>stock</p>",
                        "<p>margin</p>",
                        "<p>flow</p>"
                    ],
                    options_hi: [
                        "<p>अधिशेष (surplus)</p>",
                        "<p>स्टॉक (stock)</p>",
                        "<p>सार्थक (margin)</p>",
                        "<p>प्रवाह (flow)</p>"
                    ],
                    solution_en: "<p>26.(d) <strong>flow.</strong> A deficit occurs when expenses exceed revenues, imports exceed exports, or liabilities exceed assets. Federal budget deficits add to the national debt.</p>",
                    solution_hi: "<p>26.(d) <strong>प्रवाह। </strong>घाटा तब होता है जब व्यय राजस्व से अधिक हो, आयात निर्यात से अधिक हो, या देनदारियाँ परिसंपत्तियों से अधिक हो। संघीय बजट घाटा राष्ट्रीय ऋण में वृद्धि करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Where was the Durand Cup Tournament started in India in 1888?</p>",
                    question_hi: "<p>27. 1888 में भारत में डूरंड कप टूर्नामेंट कहाँ शुरू किया गया था?</p>",
                    options_en: [
                        "<p>Mumbai</p>",
                        "<p>Calcutta</p>",
                        "<p>Shimla</p>",
                        "<p>Delhi</p>"
                    ],
                    options_hi: [
                        "<p>मुंबई</p>",
                        "<p>कलकत्ता</p>",
                        "<p>शिमला</p>",
                        "<p>दिल्ली</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>Shimla.</strong> Durand Cup is named after Sir Mortimer Durand who was then, the Foreign Secretary to Govt of India. It is the oldest football tournament in India as well as in Asia. The winning team receives three trophies: the Durand Cup, the Shimla Trophy (first awarded in 1904), and the President\'s Cup (first presented by Dr. Rajendra Prasad in 1956).</p>",
                    solution_hi: "<p>27.(c) <strong>शिमला। </strong>डूरंड कप का नाम सर मोर्टिमर डूरंड के नाम पर रखा गया है, जो उस समय भारत सरकार के विदेश सचिव थे। यह भारत के साथ-साथ एशिया का सबसे पुराना फुटबॉल टूर्नामेंट है। जीतने वाली टीम को तीन ट्रॉफी मिलती हैं: डूरंड कप, शिमला ट्रॉफी (पहली बार 1904 में प्रदान की गई), और प्रेसिडेंट कप (पहली बार 1956 में डॉ. राजेंद्र प्रसाद द्वारा प्रदान की गई)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Consider the following statements and choose the correct statement(s) from the following:<br>1. The UDAN scheme has completed five years since its implementation in 2017.<br>2. It is the flagship scheme of the Ministry of Science and Technology.<br>3. The Airports Authority of India is the implementing agency of this scheme.</p>",
                    question_hi: "<p>28. निम्नलिखित कथनों पर विचार कीजिए और निम्नलिखित में से सही कथन/कथनों का चयन कीजिए:<br>1. उड़ान (UDAN) योजना ने 2017 में लागू होने के पाँच साल पूरे कर लिए हैं।<br>2. यह विज्ञान और प्रौद्योगिकी मंत्रालय की प्रमुख योजना है।<br>3. भारतीय विमानपत्तन प्राधिकरण इस योजना की कार्यान्वयन एजेंसी है।</p>",
                    options_en: [
                        "<p>Only statement 3</p>",
                        "<p>Only statement 2</p>",
                        "<p>Only statements 2 and 3</p>",
                        "<p>Only statements 1 and 3</p>"
                    ],
                    options_hi: [
                        "<p>केवल कथन 3</p>",
                        "<p>केवल कथन 2</p>",
                        "<p>केवल कथन 2 और 3</p>",
                        "<p>केवल कथन 1 और 3</p>"
                    ],
                    solution_en: "<p>28.(d) <strong>Only statements 1 and 3.</strong> The UDAN (Ude Desh ka Aam Nagrik) scheme is an initiative by the Indian government aimed at enhancing connectivity to remote and regional areas while making air travel more affordable. The scheme was introduced in 2016 as part of the National Civil Aviation Policy. First Flight : The first flight under the UDAN scheme, from Shimla to Delhi, was flagged off by Prime Minister Narendra Modi in 2017. Nodal Ministry: Ministry of Civil Aviation.</p>",
                    solution_hi: "<p>28.(d) <strong>केवल कथन 1 और 3 ।</strong> उड़ान (उड़े देश का आम नागरिक) योजना भारत सरकार की एक पहल है जिसका उद्देश्य हवाई यात्रा को और अधिक किफायती बनाते हुए दूरदराज और क्षेत्रीय क्षेत्रों में कनेक्टिविटी बढ़ाना है। इस योजना को 2016 में राष्ट्रीय नागरिक उड्डयन नीति के भाग के रूप में शुरू किया गया था। पहली उड़ान: उड़ान योजना के तहत शिमला से दिल्ली के लिए पहली उड़ान को प्रधानमंत्री नरेंद्र मोदी ने 2017 में हरी झंडी दिखाई थी। नोडल मंत्रालय: नागरिक उड्डयन मंत्रालय।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Non-polar molecules possess _______.</p>",
                    question_hi: "<p>29. गैर-ध्रुवीय (Non-polar) अणुओं में _______ होता है।</p>",
                    options_en: [
                        "<p>permanent dipole moment</p>",
                        "<p>partial charge</p>",
                        "<p>no dipole moment</p>",
                        "<p>instantaneous dipole moment</p>"
                    ],
                    options_hi: [
                        "<p>स्थायी द्विध्रुव आघूर्ण</p>",
                        "<p>आंशिक आवेश</p>",
                        "<p>कोई भी द्विध्रुव आघूर्ण नहीं</p>",
                        "<p>तात्क्षणिक द्विध्रुव आघूर्ण</p>"
                    ],
                    solution_en: "<p>29.(d) <strong>instantaneous dipole moment. </strong>Non-polar molecules are molecules in which the centre of positive and negative charge coincide with one another. Since the centre of positive and negative charges coincides with one another, thus these molecules have zero dipole moment.</p>",
                    solution_hi: "<p>29.(d) <strong>तात्क्षणिक द्विध्रुव आघूर्ण। </strong>अध्रुवीय अणु वे अणु होते हैं जिनमें धनात्मक और ऋणात्मक आवेश का केंद्र एक दूसरे के साथ संपाती होता है। चूँकि धनात्मक और ऋणात्मक आवेश का केंद्र एक दूसरे के साथ संपाती होता है, इसलिए इन अणुओं का द्विध्रुव आघूर्ण शून्य होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Below are two chemical equations:<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Zn</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>)</mo><mo>+</mo><msub><mi>CuSO</mi><mn>4</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>&#8594;</mo><msub><mi>ZnSO</mi><mn>4</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><mi>Cu</mi><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Pb</mi><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>)</mo><mo>+</mo><msub><mi>CuCl</mi><mn>2</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>&#8594;</mo><msub><mi>PbCl</mi><mn>2</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><mi>Cu</mi><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>)</mo></math><br>Why does copper get separated from its compound form in both cases?</p>",
                    question_hi: "<p>30. नीचे दो रासायनिक समीकरण दिए गए हैं:<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Zn</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>)</mo><mo>+</mo><msub><mi>CuSO</mi><mn>4</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>&#8594;</mo><msub><mi>ZnSO</mi><mn>4</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><mi>Cu</mi><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>)</mo></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Pb</mi><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>)</mo><mo>+</mo><msub><mi>CuCl</mi><mn>2</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>&#8594;</mo><msub><mi>PbCl</mi><mn>2</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><mi>Cu</mi><mo>(</mo><mi mathvariant=\"normal\">s</mi><mo>)</mo></math><br>दोनों ही स्थितियों में कॉपर (copper) अपने यौगिक रूप से पृथक क्यों हो जाता है?</p>",
                    options_en: [
                        "<p>Zinc and Lead are unstable in element form.</p>",
                        "<p>Copper compounds break down under high pressure.</p>",
                        "<p>Zinc and Lead are more reactive than Copper.</p>",
                        "<p>Copper is more reactive than Zinc and Lead.</p>"
                    ],
                    options_hi: [
                        "<p>जिंक (Zinc) और लेड (Lead) तत्व रूप में अस्थायी होते हैं।</p>",
                        "<p>कॉपर (Copper) के यौगिक उच्च दाब पर अपघटित हो जाते हैं।</p>",
                        "<p>जिंक (Zinc) और लेड (Lead), कॉपर (Copper) की तुलना में अधिक अभिक्रियाशील होते हैं।</p>",
                        "<p>कॉपर (Copper), जिंक (Zinc) और लेड (Lead) की तुलना मेंअधिक अभिक्रियाशील होता है।</p>"
                    ],
                    solution_en: "<p>30.(c)<strong> Zinc and Lead are more reactive than Copper. </strong>In both the given equations, copper gets separated from its compound form because zinc and lead are more reactive than copper. This means that zinc and lead have a greater tendency to lose electrons and form ions, which allows them to displace copper from its compounds. This is an example of a single displacement reaction, where a more reactive element displaces a less reactive element from its compound.</p>",
                    solution_hi: "<p>30.(c) <strong>जिंक (Zinc) और लेड (Lead), कॉपर (Copper) की तुलना में अधिक अभिक्रियाशील होते हैं।</strong> दिए गए दोनों समीकरणों में, कॉपर अपने यौगिक रूप से पृथक हो जाता है क्योंकि जिंक और लेड कॉपर से अधिक अभिक्रियाशील होते हैं। इसका अर्थ यह है कि जिंक और लेड में इलेक्ट्रॉन त्यागने और आयन बनाने की अधिक प्रवृत्ति होती है, जो उन्हें कॉपर को उसके यौगिकों से विस्थापित करने की अनुमति देता है। यह एकल विस्थापन अभिक्रिया का एक उदाहरण है, जहाँ अधिक अभिक्रियाशील तत्व अपने यौगिक से कम अभिक्रियाशील तत्व को विस्थापित करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Ortho-sulphobenzimide is one of the artificial sweeteners. It is also known as ________.</p>",
                    question_hi: "<p>31. ऑर्थो-सल्फोबेंज़िमाइड (Ortho-sulphobenzimide) कृत्रिम मधुरकों में से एक है। इसे ________ के नाम से भी जाना जाता है।</p>",
                    options_en: [
                        "<p>sucralose</p>",
                        "<p>aspartame</p>",
                        "<p>saccharin</p>",
                        "<p>alitame</p>"
                    ],
                    options_hi: [
                        "<p>सुक्रालोज़ (sucralose)</p>",
                        "<p>एस्पार्टेम (aspartame)</p>",
                        "<p>सैकरीन (saccharin)</p>",
                        "<p>एलिटेम (alitame)</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>Saccharin</strong> is the first popular artificial sweetening agent. It has been used as a sweetening agent ever since it was discovered in 1879. It is about 550 times as sweet as cane sugar. It is considered safe and inert when consumed.</p>",
                    solution_hi: "<p>31.(c) <strong>सैकरीन</strong> सबसे पहला कृत्रिम मीठा (मधुरक) घटक है। 1879 में इसकी खोज के बाद से ही इसे मीठा घटक के रूप में प्रयोग किया जाता रहा है। यह गन्ने की चीनी से लगभग 550 गुना अधिक मीठा होता है। इसे सेवन करने पर सुरक्षित और निष्क्रिय माना जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following diseases is detected by the Widal test?</p>",
                    question_hi: "<p>32. विडाल परीक्षण (Widal test) द्वारा निम्नलिखित में से किस रोग का पता लगाया जाता है?</p>",
                    options_en: [
                        "<p>Pneumonia</p>",
                        "<p>Typhoid</p>",
                        "<p>Diphtheria</p>",
                        "<p>Influenza</p>"
                    ],
                    options_hi: [
                        "<p>न्यूमोनिया (Pneumonia)</p>",
                        "<p>आंत्र ज्वर (Typhoid)</p>",
                        "<p>डिप्थीरिया (Diphtheria)</p>",
                        "<p>इंफ्लुएंजा (Influenza)</p>"
                    ],
                    solution_en: "<p>32.(b) <strong>Typhoid. </strong>Widal test is an advanced way to check for antibodies that your body makes against the salmonella bacteria that causes typhoid fever. It was developed in 1896. Diseases and diagnostic tests: Tuberculosis (TB) - Mantoux test (Tuberculin skin test), Sputum smear, Chest X-ray. HIV/AIDS - ELISA (Enzyme-Linked Immunosorbent Assay). COVID-19 - RT-PCR, Rapid antigen test.</p>",
                    solution_hi: "<p>32.(b) <strong>आंत्र ज्वर (Typhoid)।</strong> विडाल टेस्ट एक उन्नत तरीका है जो हमारे शरीर द्वारा टाइफाइड बुखार उत्पन्न करने वाले साल्मोनेला बैक्टीरिया के खिलाफ बनाई गई एंटीबॉडी की जांच करता है। इसे 1896 में विकसित किया गया था। रोग और नैदानिक परीक्षण: तपेदिक (TB.) - मैंटू टेस्ट (ट्यूबरकुलिन स्किन टेस्ट), कफ की जांच, छाती का एक्स-रे। HIV/AIDS - ELISA (एंजाइम-लिंक्ड इम्यूनोसोरबेंट एसे)। कोविड-19 - RT-PCR, रैपिड एंटीजन टेस्ट।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Name the new ant species that has been discovered in June 2024 in Arunachal Pradesh&rsquo;s Siang Valley?</p>",
                    question_hi: "<p>33. जून 2024 में अरुणाचल प्रदेश के सियांग घाटी में खोजी गई नई चींटी प्रजाति का नाम बताएं।</p>",
                    options_en: [
                        "<p>Batillipes Chandrayaani</p>",
                        "<p>Ablennes gracali</p>",
                        "<p>Paraparatrechina neela</p>",
                        "<p>Peucetia chhaparajnirvin</p>"
                    ],
                    options_hi: [
                        "<p>Batillipes Chandrayaani</p>",
                        "<p>Ablennes gracali</p>",
                        "<p>Paraparatrechina neela</p>",
                        "<p>Peucetia chhaparajnirvin</p>"
                    ],
                    solution_en: "<p>33.(c)<strong> Paraparatrechina neela. </strong>The name neela, which signifies the colour blue in most Indian languages, was derived from its unique blue colouration. It is easily distinguishable from all known species of Paraparatrechina by its metallic-blue body.</p>",
                    solution_hi: "<p>33.(c) <strong>Paraparatrechina neela। </strong>इसका नाम &lsquo;नीला&rsquo; इसके अद्वितीय नीले रंग के कारण रखा गया है। यह metallic-blue शरीर वाली प्रजाति अन्य सभी ज्ञात Paraparatrechina प्रजातियों से अलग है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Balban&rsquo;s rule as an absolute despot led to which of the following events ?</p>",
                    question_hi: "<p>34. एक पूर्ण निरंकुश (absolute despot) शासक के रूप में बलबन के शासन के कारण निम्नलिखित में से कौन सी घटना घटित हुई?</p>",
                    options_en: [
                        "<p>Respected common man</p>",
                        "<p>Granting Jagirs to Nobles</p>",
                        "<p>Appointed Hindus in administration</p>",
                        "<p>Destruction of the Forty</p>"
                    ],
                    options_hi: [
                        "<p>आदरणीय आम आदमी (Respected common man)</p>",
                        "<p>रईसों को जागीर देना (Granting Jagirs to Nobles)</p>",
                        "<p>प्रशासन में हिन्दुओं का नियुक्त किया जाना (Appointed Hindus in administration)</p>",
                        "<p>चालीसा का विनाश (Destruction of the Forty)</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Destruction of the Forty.</strong> Balban (ruled from 1266 to 1287), also known as Ghiyas ud-Din Balban, was the ninth Sultan of the Delhi Sultanate. His rule is associated with the downfall of a group known as \"the Forty,\" a group of influential nobles. His rule was characterized by a military approach, which was referred to as the \"policy of blood and iron. He faced numerous Mongol invasions. He established the office of the Diwan-i-Ariz (military department).</p>",
                    solution_hi: "<p>34.(d) <strong>चालीसा का विनाश। </strong>बलबन (शासन - 1266 से 1287 तक), जिसे गियास उद-दीन बलबन के नाम से भी जाना जाता है, दिल्ली सल्तनत का नौवां सुल्तान था। उसका शासन \"चालीसा\" नामक समूह के पतन से जुड़ा है, जो प्रभावशाली रईसों का समूह था। उनके शासन की विशेषता एक सैन्य दृष्टिकोण (military approach) थी, जिसे \"रक्त और लौह नीति\" के रूप में संदर्भित किया गया था। उन्होंने कई मंगोल आक्रमणों का सामना किया। उन्होंने दीवान-ए-आरिज़ (सैन्य विभाग) का दफ़तर स्थापित किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. In March 2024, which country became the world&rsquo;s first to enshrine the right to an abortion in the Constitution?</p>",
                    question_hi: "<p>35. मार्च 2024 में, कौन सा देश गर्भपात के अधिकार को संविधान में शामिल करने वाला दुनिया का पहला देश बना?</p>",
                    options_en: [
                        "<p>France</p>",
                        "<p>Russia</p>",
                        "<p>United Kingdom</p>",
                        "<p>Canada</p>"
                    ],
                    options_hi: [
                        "<p>फ्रांस</p>",
                        "<p>रूस</p>",
                        "<p>यूनाइटेड किंगडम</p>",
                        "<p>कनाडा</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>France </strong>lawmakers approved a&nbsp;bill that will enshrine the right to an abortion in the Constitution of France in a joint session of Parliament at the Palace of Versailles. The move made France the first country in the world to offer explicit protection for terminating a pregnancy in its basic law.</p>",
                    solution_hi: "<p>35.(a) <strong>फ्रांस। </strong>फ्रांस के सांसदों ने वर्साय के महल में संसद के संयुक्त सत्र में एक विधेयक पारित किया, जो फ्रांस के संविधान में गर्भपात के अधिकार को शामिल करता है। यह कदम फ्रांस को गर्भावस्था समाप्ति के लिए स्पष्ट सुरक्षा प्रदान करने वाला पहला देश बनाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following is NOT one of the main pillars of the &lsquo;Namami Gange&nbsp;Programme&rsquo;, launched by the Government of India in 2014?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन-सा, वर्ष 2014 में भारत सरकार द्वारा शुरू किए गए \'नमामि गंगे कार्यक्रम\' के&nbsp;मुख्य स्तंभों में से एक नहीं है?</p>",
                    options_en: [
                        "<p>Sewerage Treatment Infrastructure</p>",
                        "<p>Industrial Effluent Monitoring</p>",
                        "<p>River Linkage Development</p>",
                        "<p>River-Front Development</p>"
                    ],
                    options_hi: [
                        "<p>मलजल उपचार अवसंरचना (Sewerage Treatment Infrastructure)</p>",
                        "<p>औद्योगिक अपशिष्ट निगरानी (Industrial Effluent Monitoring)</p>",
                        "<p>नदी जोड़ो विकास (River Linkage Development)</p>",
                        "<p>रिवरफ्रंट विकास (River-Front Development)</p>"
                    ],
                    solution_en: "<p>36.(c) <strong>River Linkage Development.</strong> The \'Namami Gange Programme\' is an integrated conservation mission approved as a flagship program by the Union Government in June 2014. It has a budget outlay of Rs. 20,000 crore and aims to achieve the twin objectives of effective pollution abatement and the conservation and rejuvenation of the National River Ganga. Main pillars of the Namami Gange Programme River-Surface Cleaning, Afforestation, Ganga Gram, and Bio-Diversity.</p>",
                    solution_hi: "<p>36.(c) <strong>नदी जोड़ो विकास</strong> (River Linkage Development)। \'नमामि गंगे कार्यक्रम\' एक एकीकृत संरक्षण मिशन है जिसे जून 2014 में केंद्र सरकार द्वारा एक प्रमुख कार्यक्रम के रूप में स्वीकृत किया गया था। इसका बजट परिव्यय रु. 20,000 करोड़ रुपये और इसका लक्ष्य प्रभावी प्रदूषण उन्मूलन और राष्ट्रीय नदी गंगा के संरक्षण और कायाकल्प के दोहरे उद्देश्यों को प्राप्त करना है। नमामि गंगे कार्यक्रम के मुख्य स्तंभ नदी सतह की सफाई, वनरोपण, गंगा ग्राम और जैव-विविधता हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Who received the prestigious &lsquo;Gender Diversity&rsquo; National Award at the Indian Steel Association (ISA) Steel Conclave 2024?</p>",
                    question_hi: "<p>37. भारतीय इस्पात संघ (ISA) स्टील कॉन्क्लेव 2024 में &lsquo;जेंडर डायवर्सिटी&rsquo; राष्ट्रीय पुरस्कार किसे प्रदान किया गया?</p>",
                    options_en: [
                        "<p>Dasari Radhika</p>",
                        "<p>Sri Nagendra Nath Sinha</p>",
                        "<p>Atul Bhatt</p>",
                        "<p>Rahul Kejriwal</p>"
                    ],
                    options_hi: [
                        "<p>दासरी राधिका</p>",
                        "<p>श्री नागेंद्र नाथ सिन्हा</p>",
                        "<p>अतुल भट्ट</p>",
                        "<p>राहुल केजरीवाल</p>"
                    ],
                    solution_en: "<p>37.(a) <strong>Dasari Radhika.</strong> The Indian Steel Association (ISA) presented the prestigious &lsquo;Gender Diversity&rsquo; National award to Dr. Dasari Radhika, Deputy General Manager (DGM) Human Resource (HR), Rashtriya Ispat Nigam Limited (RINL) at the ISA Steel Conclave 2024 in New Delhi, Delhi.</p>",
                    solution_hi: "<p>37.(a) <strong>दासरी राधिका। </strong>भारतीय इस्पात संघ (ISA) ने &lsquo;जेंडर डायवर्सिटी&rsquo; राष्ट्रीय पुरस्कार डॉ. दासरी राधिका, उप महाप्रबंधक (DGM), मानव संसाधन (HR), राष्ट्रीया इस्पात निगम लिमिटेड (RINL) को नई दिल्ली में आयोजित ISA स्टील कॉन्क्लेव 2024 में प्रदान किया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Where was the first Jallikattu event of 2025 held in Tamil Nadu ?</p>",
                    question_hi: "<p>38. तमिलनाडु में 2025 के पहले जल्लीकट्टू कार्यक्रम का आयोजन किस स्थान पर हुआ?</p>",
                    options_en: [
                        "<p>Avaniapuram</p>",
                        "<p>Palamedu</p>",
                        "<p>Thatchankurichi</p>",
                        "<p>Alanganallur</p>"
                    ],
                    options_hi: [
                        "<p>अवनीपुरम</p>",
                        "<p>पालामेडु</p>",
                        "<p>थाचनकुरिची</p>",
                        "<p>अलंगनल्लूर</p>"
                    ],
                    solution_en: "<p>38.(c) <strong>Thatchankurichi. </strong>The first Jallikattu event of 2025 in Tamil Nadu was held on January 4 at Thatchankurichi village in the Gandarvakottai taluk of Pudukkottai district. This traditional bull-taming event featured over 600 bulls and 350 participants, marking the commencement of the Pongal celebrations and the Jallikattu season in the state.</p>",
                    solution_hi: "<p>38.(c)<strong> थाचनकुरिची । </strong>तमिलनाडु में 2025 का पहला जल्लीकट्टू कार्यक्रम 4 जनवरी को पुदुकोट्टई जिले के गंदरवकोट्टई तालुक के थाचनकुरिची गांव में आयोजित किया गया। इस पारंपरिक सांड-परंपरा कार्यक्रम में 600 से अधिक सांडों और 350 सांड-परंपरा प्रतिभागियों ने भाग लिया, जो पोंगल उत्सव और राज्य में जल्लीकट्टू सीजन की शुरुआत का प्रतीक है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. In a Parliamentary system, who among the following is known as the head of the Government?</p>",
                    question_hi: "<p>39. संसदीय प्रणाली में, निम्नलिखित में से किसे सरकार के प्रमुख के रूप में जाना जाता है?</p>",
                    options_en: [
                        "<p>Prime Minister</p>",
                        "<p>Chief Minister</p>",
                        "<p>Chief Justice</p>",
                        "<p>President</p>"
                    ],
                    options_hi: [
                        "<p>प्रधान मंत्री</p>",
                        "<p>मुख्यमंत्री</p>",
                        "<p>मुख्य न्यायधीश</p>",
                        "<p>राष्ट्रपति</p>"
                    ],
                    solution_en: "<p>39.(a) <strong>Prime Minister: </strong>Article 75(1) - The Prime Minister shall be appointed by the President and the other Ministers shall be appointed by the President on the advice of the Prime Minister (head of the Council of Ministers). Article 75(3) - The Council of Ministers shall be collectively responsible to the Lok Sabha. Article 84(2) - Must be a citizen of India and a member of either the Lok Sabha or the Rajya Sabha.</p>",
                    solution_hi: "<p>39.(a)<strong> प्रधानमंत्री:</strong> अनुच्छेद 75(1) - प्रधानमंत्री की नियुक्ति राष्ट्रपति द्वारा की जाएगी और अन्य मंत्रियों की नियुक्ति राष्ट्रपति द्वारा प्रधानमंत्री (मंत्रिपरिषद के प्रमुख) की सलाह पर की जाएगी। अनुच्छेद 75(3) - मंत्रिपरिषद सामूहिक रूप से लोकसभा के प्रति उत्तरदायी होगी। अनुच्छेद 84(2) - भारत का नागरिक होना चाहिए और लोकसभा या राज्यसभा का सदस्य होना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The &lsquo;Rule of Law&rsquo; is a key feature of the Indian Constitution which is a doctrine ascribed to ______.</p>",
                    question_hi: "<p>40. \'विधि का शासन\' भारतीय संविधान की एक प्रमुख विशेषता है, जिस सिद्धांत का श्रेय ______ को दिया जाता है।</p>",
                    options_en: [
                        "<p>Plato</p>",
                        "<p>Adam Smith</p>",
                        "<p>Albert Venn Dicey</p>",
                        "<p>Karl Marx</p>"
                    ],
                    options_hi: [
                        "<p>प्लेटो (Plato)</p>",
                        "<p>एडम स्मिथ (Adam Smith)</p>",
                        "<p>अल्बर्ट वेन डाइसी (Albert Venn Dicey)</p>",
                        "<p>काल मार्क्स (Karl Marx)</p>"
                    ],
                    solution_en: "<p>40.(c) <strong>Albert Venn Dicey </strong>was a British Whig jurist and constitutional theorist. Adam Smith is most famous for his 1776 book, \"The Wealth of Nations.\" Karl Marx is best known for his theories that led to the development of Marxism. His books, \"Das Kapital\" and \"The Communist Manifesto,\" formed the basis of Marxism. Plato was an ancient Greek philosopher.</p>",
                    solution_hi: "<p>40.(c) <strong>अल्बर्ट वेन डाइसी</strong> एक ब्रिटिश व्हिग विधिवेत्ता और संवैधानिक सिद्धांतकार थे। एडम स्मिथ को 1776 ईस्वी में अपनी प्रकाशित पुस्तक \"द वेल्थ ऑफ नेशन्स\" से सबसे अधिक प्रसिद्धि मिली थी। कार्ल मार्क्स मार्क्सवाद के विकास में योगदान देने वाले अपने सिद्धांतों के लिए सबसे अधिक जाने जाते हैं। उनकी प्रमुख पुस्तकें \"दास कैपिटल\" और \"द कम्युनिस्ट मैनिफेस्टो\" है। प्लेटो एक प्राचीन यूनानी दार्शनिक थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. What is the main objective of zero-based budgeting ?</p>",
                    question_hi: "<p>41. शून्य-आधारित बजट (zero-based budgeting) का मुख्य उद्देश्य क्या है?</p>",
                    options_en: [
                        "<p>To assess the effectiveness of each programme and activity with the output</p>",
                        "<p>Establishing a relationship between inputs and outputs</p>",
                        "<p>Examining the objectives for which funds are provided</p>",
                        "<p>Re-evaluation of all the activities every year when budget provisions are made</p>"
                    ],
                    options_hi: [
                        "<p>आउटपुट के साथ प्रत्येक कार्यक्रम और गतिविधि की प्रभावशीलता का आकलन करना</p>",
                        "<p>इनपुट और आउटपुट के बीच संबंध स्थापित करना</p>",
                        "<p>उन उद्देश्यों की जांच करना जिनके लिए धन प्रदान किया जाता है</p>",
                        "<p>बजट प्रावधान किए जाने पर प्रत्येक वर्ष सभी गतिविधियों का पुनर्मूल्यांकन किया जाना</p>"
                    ],
                    solution_en: "<p>41.(d) Zero Base Budgeting (ZBB) in India first experimented in April 1987. The three types of budgets in India are: Balanced - When the estimated revenue for a fiscal year is equal to the estimated spending for that year. Surplus - When the estimated revenue exceeds the estimated expenditure in a fiscal year. Deficit - when the estimated expenditure exceeds the estimated revenue in a fiscal year.</p>",
                    solution_hi: "<p>41.(d) भारत में शून्य आधारित बजट (ZBB) का पहली बार प्रयोग अप्रैल 1987 में किया गया था। भारत में बजट तीन प्रकार के होते हैं : संतुलित - जब किसी वित्तीय वर्ष के लिए अनुमानित राजस्व उस वर्ष के अनुमानित व्यय के बराबर होता है।अधिशेष बजट (Surplus Budget): जब एक वित्तीय वर्ष में अनुमानित राजस्व अनुमानित व्यय से अधिक होता है। घाटा बजट (Deficit Budget): जब एक वित्तीय वर्ष में अनुमानित व्यय अनुमानित राजस्व से अधिक होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which state hosts the Nehru trophy boat race ?</p>",
                    question_hi: "<p>42. नेहरू ट्रॉफी बोट रेस (नौका दौड़) की मेजबानी कौन-सा राज्य करता है?</p>",
                    options_en: [
                        "<p>Kerala</p>",
                        "<p>Goa</p>",
                        "<p>Karnataka</p>",
                        "<p>Tamil Nadu</p>"
                    ],
                    options_hi: [
                        "<p>केरल</p>",
                        "<p>गोवा</p>",
                        "<p>कर्नाटक</p>",
                        "<p>तमिलनाडु</p>"
                    ],
                    solution_en: "<p>42.(a) <strong>Kerala. </strong>The Nehru Trophy Boat Race is an annual event held in the Punnamada Lake in Alappuzha district of Kerala. The race was first held in 1952 to commemorate a visit by India\'s first Prime Minister, Jawaharlal Nehru, to the state. Other festivals of Kerala: Onam, Vishu, Attukal Pongala, and Thrissur Pooram.</p>",
                    solution_hi: "<p>42.(a) <strong>केरल।</strong> नेहरू ट्रॉफी बोट रेस केरल के अलपुझा जिले में पुन्नमदा झील में आयोजित होने वाला एक वार्षिक आयोजन है। यह रेस पहली बार 1952 में भारत के पहले प्रधानमंत्री जवाहरलाल नेहरू की राज्य यात्रा की याद में आयोजित की गई थी। केरल के अन्य त्योहार: ओणम, विशु, अट्टुकल पोंगल और त्रिशूर पूरम।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. If labour is employed in a job but is not actually utilised for the production of goods and services, it is known as:</p>",
                    question_hi: "<p>43. यदि श्रम (labour) को किसी कार्य में लगाया जाए, लेकिन उसका उपयोग वास्तव में वस्तुओं और सेवाओं के उत्पादन के लिए नहीं किया जाए, तो यह ____________ कहलाएगी।</p>",
                    options_en: [
                        "<p>seasonal unemployment</p>",
                        "<p>educated unemployment</p>",
                        "<p>disguised unemployment</p>",
                        "<p>urban unemployment</p>"
                    ],
                    options_hi: [
                        "<p>मौसमी बेरोजगारी (seasonal unemployment)</p>",
                        "<p>शिक्षित बेरोजगारी (educated unemployment)</p>",
                        "<p>प्रच्छन्न बेरोजगारी (disguised unemployment)</p>",
                        "<p>शहरी बेरोजगारी (urban unemployment)</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>disguised unemployment.</strong> Seasonal unemployment happens when people are not able to find jobs during some months of the year. People dependent upon agriculture usually face this kind of problem. Educated unemployment occurs when individuals with formal education cannot secure jobs. Urban unemployment refers to joblessness in urban regions.</p>",
                    solution_hi: "<p>43.(c) <strong>प्रच्छन्न बेरोजगारी </strong>(Disguised Unemployment)। मौसमी बेरोजगारी तब होती है जब लोग वर्ष के कुछ महीनों में रोजगार प्राप्त करने में असमर्थ होते हैं। कृषि पर निर्भर लोग सामान्य तौर पर इस प्रकार की समस्या का सामना करते हैं। शिक्षित बेरोजगारी तब होती है जब औपचारिक शिक्षा प्राप्त व्यक्ति अपनी योग्यता के अनुरूप रोजगार प्राप्त करने में असमर्थ होते हैं। शहरी बेरोजगारी (Urban Unemployment) शहरी क्षेत्रों में बेरोजगारी को संदर्भित करती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Identify the INCORRECT pair of cropping season and related crop from the following.</p>",
                    question_hi: "<p>44. निम्नलिखित में से फसल ऋतू और संबंधित फसल के गलत युग्म की पहचान कीजिए।</p>",
                    options_en: [
                        "<p>Rabi - Wheat</p>",
                        "<p>Kharif - Maize</p>",
                        "<p>Rabi - Paddy</p>",
                        "<p>Kharif - Soyabean</p>"
                    ],
                    options_hi: [
                        "<p>रबी- गेहूं (Wheat)</p>",
                        "<p>ख़रीफ़ -ज्वार (Maize)</p>",
                        "<p>रबी- धान (Paddy)</p>",
                        "<p>ख़रीफ़ -सोयाबीन (Soyabean)</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Rabi - Paddy.</strong> Paddy (Kharif crop). Rabi crops are sown in winter (October-December) and harvested in summer (April-June), including wheat, barley, peas, gram, and mustard. Kharif crops are grown with the monsoon and harvested in September-October, including maize, jowar, bajra, moong, urad, cotton, jute, groundnut, and soybean.</p>",
                    solution_hi: "<p>44.(c) <strong>रबी - धान।</strong> धान (ख़रीफ़ फसल)। रबी की फसलें सर्दियों (अक्टूबर - दिसंबर) में बोई जाती हैं और गर्मियों (अप्रैल-जून) में काटी जाती हैं, जिनमें गेहूं, जौ, मटर, चना और सरसों शामिल हैं। खरीफ की फसलें मानसून के साथ उगाई जाती हैं और सितंबर-अक्टूबर में काटी जाती हैं, जिनमें मक्का, ज्वार, बाजरा, मूंग, उड़द, कपास, जूट, मूंगफली और सोयाबीन शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Bundu Khan was a _____ player of Delhi gharana.</p>",
                    question_hi: "<p>45. बुंदू खान दिल्ली घराने के एक_____ वादक थे।</p>",
                    options_en: [
                        "<p>santoor</p>",
                        "<p>flute</p>",
                        "<p>sarod</p>",
                        "<p>sarangi</p>"
                    ],
                    options_hi: [
                        "<p>संतूर</p>",
                        "<p>बांसुरी</p>",
                        "<p>सरोद</p>",
                        "<p>सारंगी</p>"
                    ],
                    solution_en: "<p>45.(d) <strong>sarangi.</strong> Bundu Khan was trained by his grandfather Saungi Khan. Some sarangi players from the Delhi gharana: Shahnawaz Khan, Nabeel Khan, Sabri Khan, Abdul Latif Khan. Other Musical Instruments and their Exponents : Santoor - Shivkumar Sharma, Rahul Sharma, Sedat Anar. Flute - Pandit Hariprasad Chaurasia, Pannalal Ghosh, Rajendra Prasad. Sarod - Ustad Amjad Ali Khan, Soumik Datta, Shyamal Sinha.</p>",
                    solution_hi: "<p>45.(d) <strong>सारंगी।</strong> बुंदू खान को उनके दादा सौंगी खान ने प्रशिक्षित किया था। दिल्ली घराने के कुछ सारंगी वादक: शाहनवाज खान, नबील खान, साबरी खान, अब्दुल लतीफ खान। अन्य संगीत वाद्ययंत्र एवं उनके प्रतिपादक: संतूर - शिवकुमार शर्मा, राहुल शर्मा, सेदत अनार। बांसुरी - पंडित हरिप्रसाद चौरसिया, पन्नालाल घोष, राजेंद्र प्रसाद। सरोद - उस्ताद अमजद अली खान, सौमिक दत्ता, श्यामल सिन्हा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. When a river reaches a low lying plain, it continues erosion of soil as well as deposition and as a result of both these factors another river channel develops and later it is detached from the main river forming a U-shaped lake; this is called:</p>",
                    question_hi: "<p>46. जब कोई नदी निचले मैदान में पहुँचती है, तो यह मिट्टी का कटाव और जमाव जारी रखती है और इन दोनों कारकों के परिणामस्वरूप एक और नदी - मार्ग विकसित हो जाता है और बाद में यह U-आकार की झील बनाते हुए मुख्य नदी से अलग हो जाता है; इसे एक ______ कहा जाता है।</p>",
                    options_en: [
                        "<p>a canal</p>",
                        "<p>an estuary</p>",
                        "<p>a gulf</p>",
                        "<p>an oxbow lake</p>"
                    ],
                    options_hi: [
                        "<p>नहर (canal)</p>",
                        "<p>मुहाना (estuary)</p>",
                        "<p>खाड़ी (gulf)</p>",
                        "<p>गोखुर झील (oxbow lake)</p>"
                    ],
                    solution_en: "<p>46.(d) <strong>an oxbow lake. </strong>An oxbow lake is a U-shaped lake that forms when a river is cut off from its main course, often due to erosion and deposition. An estuary is a partially enclosed coastal body of water, where freshwater from rivers and streams mixes with saltwater from the ocean. A gulf is a large body of water that is partially enclosed by land, but is still connected to the ocean.</p>",
                    solution_hi: "<p>46.(d) <strong>गोखुर झील (oxbow lake)। </strong>गोखुर झील एक U-आकृति की झील होती है जो तब बनती है जब नदी अपने मुख्य मार्ग से कट जाती है, जो अक्सर कटाव और अवसादन के कारण होता है। मुहाना जल का आंशिक रूप से संलग्न तटीय निकाय है, जहाँ नदियों और झरनों का ताज़ा जल समुद्र के खारे जल के साथ मिल जाता है। खाड़ी एक बड़ी जल निकाय होती है जो आंशिक रूप से भूमि से घिरी होती है, लेकिन यह महासागर से जुड़ी होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following statement/statements is/are true regarding the light year?<br>1.Light year is a unit of distance.<br>2.Light is a unit of time.<br>3.A light year is the distance that light travels in one Earth year.<br>4.Light year is the measurement of light intensity.</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन-सा/से कथन प्रकाश वर्ष के संदर्भ में सत्य है/हैं?<br>1. प्रकाश वर्ष, दूरी की इकाई है।<br>2. प्रकाश, समय की इकाई है।<br>3. प्रकाश वर्ष वह दूरी है, जो प्रकाश, एक पृथ्वी वर्ष में तय करता है।<br>4. प्रकाश वर्ष प्रकाश की तीव्रता का माप है।</p>",
                    options_en: [
                        "<p>Only 1 is correct</p>",
                        "<p>Only 1 and 3 are correct</p>",
                        "<p>Only 2 is correct</p>",
                        "<p>Only 1 and 2 are correct</p>"
                    ],
                    options_hi: [
                        "<p>केवल 1 सही है</p>",
                        "<p>केवल 1 और 3 सही हैं</p>",
                        "<p>केवल 2 सही है</p>",
                        "<p>केवल 1 और 2 सही हैं</p>"
                    ],
                    solution_en: "<p>47.(b) <strong>Only 1 and 3 are correct. </strong>1 light year = 9.46 &times; 10<sup>15</sup> m = 9.46 &times; 10<sup>12</sup> km = 9.46 &times; 10<sup>17</sup> cm. Relative Speed of light: Air &gt; Water &gt; Glass &gt; Diamond. In air (3 &times; 10<sup>8</sup> m/s), in water (2.26 &times; 10<sup>8</sup> m/s), in glass (2 &times; 10<sup>8</sup> m/s), in diamond (1.25 &times;10<sup>8</sup> m/s). Light radiation travels at a speed of approximately 299,792,458 (m/s) or 3 &times; 10<sup>8</sup> m/s in a vacuum. Relative Speed of light : Solid ＜ Liquid ＜ Gas ＜Vaccum.</p>",
                    solution_hi: "<p>47.(b) <strong>केवल 1 और 3 सही हैं। </strong>1 प्रकाश वर्ष = 9.46 &times; 10<sup>15</sup> मीटर = 9.46 &times; 10<sup>12</sup> किमी = 9.46 &times; 10<sup>17</sup> सेमी। प्रकाश की सापेक्ष गति: वायु &gt; जल &gt; कांच &gt; हीरा। वायु में (3 &times; 108 मीटर/सेकेंड), जल में (2.26 &times; 108 मीटर/सेकेंड), कांच में (2 &times; 10<sup>8</sup> मीटर/सेकेंड), हीरे में (1.25 &times; 10<sup>8</sup> मीटर/सेकेंड)। प्रकाश विकिरण निर्वात में लगभग 299,792,458 (मी/सेकेंड) या 3 &times; 10<sup>8</sup> मीटर/सेकेंड की गति से गमन करता है। प्रकाश की सापेक्ष गति: ठोस ＜ द्रव ＜ गैस ＜ निर्वात।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Who has written the book &lsquo;Fearless Governance&rsquo;, published in January 2021, describing her stint as Governor?</p>",
                    question_hi: "<p>48. राज्यपाल के रूप में अपने कार्यकाल का वर्णन करते हुए जनवरी 2021 में प्रकाशित पुस्तक \'फियरलेस गवर्नेंस\' (&lsquo;Fearless Governance&rsquo;) किसने लिखी है?</p>",
                    options_en: [
                        "<p>Draupadi Murmu</p>",
                        "<p>Baby Rani Maurya</p>",
                        "<p>Kiran Bedi</p>",
                        "<p>Anandi ben Patel</p>"
                    ],
                    options_hi: [
                        "<p>द्रौपदी मुर्मू</p>",
                        "<p>बेबी रानी मौर्य</p>",
                        "<p>किरण बेदी</p>",
                        "<p>आनंदी बेन पटेल</p>"
                    ],
                    solution_en: "<p>48.(c) <strong>Kiran Bedi.</strong> She is the first woman in India to join the officer ranks of the Indian Police Service (IPS) in 1972. Her other books: &lsquo;It\'s always possible&rsquo;, &lsquo;Himmat Hai&rsquo;, &lsquo;Dare to Do! For the New Generation&rsquo;.</p>",
                    solution_hi: "<p>48.(c) <strong>किरण बेदी</strong> 1972 में भारतीय पुलिस सेवा (IPS) के अधिकारी रैंक में शामिल होने वाली भारत की पहली महिला हैं। उनकी अन्य पुस्तकें: \'इट्स ऑलवेज पॉसिबल\', \'हिम्मत है\', \'डेयर टू डू! फॉर द न्यू जेनरेशन\'।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Chandrayaan-3 leaves Earth&rsquo;s surface by burning a huge amount of fuel. The gases released then push the rocket upwards against gravity. Which law of motion is applied here?</p>",
                    question_hi: "<p>49. चंद्रयान-3, अत्यधिक मात्रा में ईंधन जलाकर पृथ्वी की सतह से प्रस्थान करता है। फिर निकलने वाली गैसें रॉकेट को गुरुत्वाकर्षण के विरुद्ध ऊपर की ओर धकेलती हैं। यहां गति का कौन-सा नियम लागू होता है?</p>",
                    options_en: [
                        "<p>Newton&rsquo;s 1<sup>st</sup> law of motion</p>",
                        "<p>Newton&rsquo;s law of gravitation</p>",
                        "<p>Newton&rsquo;s 3<sup>rd</sup> law of motion</p>",
                        "<p>Newton&rsquo;s 2<sup>nd</sup> law of motion</p>"
                    ],
                    options_hi: [
                        "<p>न्यूटन का गति का पहला नियम</p>",
                        "<p>न्यूटन का गुरुत्वाकर्षण नियम</p>",
                        "<p>न्यूटन का गति का तीसरा नियम</p>",
                        "<p>न्यूटन का गति का दूसरा नियम</p>"
                    ],
                    solution_en: "<p>49.(c) The third law of motion states that the force exerted by a first object on a second object is equal and opposite in direction to the force exerted by the second object on the first object. The first law of motion is stated as follows: An object remains at rest or in uniform motion in a straight line unless an attempt is made to change its position by an external force.</p>",
                    solution_hi: "<p>49.(c) गति का तीसरा नियम कहता है कि किसी पहली वस्तु द्वारा दूसरी वस्तु पर लगाया गया बल, दूसरी वस्तु द्वारा पहली वस्तु पर लगाए गए बल के बराबर और विपरीत दिशा में होता है। गति का पहला नियम इस प्रकार बताया गया है: कोई वस्तु तब तक विराम की स्थिति में या एक सीधी रेखा में एकसमान गति में रहती है जब तक कि किसी बाह्य बल द्वारा उसकी स्थिति में परिवर्तन करने का प्रयास न किया जाए ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which of the following was the first subcontinental empire?</p>",
                    question_hi: "<p>50. निम्नलिखित में से प्रथम उपमहाद्वीपीय साम्राज्य कौन सा था?</p>",
                    options_en: [
                        "<p>Satavahana Empire</p>",
                        "<p>Mauryan Empire</p>",
                        "<p>Kushan Empire</p>",
                        "<p>Gupta Empire</p>"
                    ],
                    options_hi: [
                        "<p>सातवाहन साम्राज्य</p>",
                        "<p>मौर्य साम्राज्य</p>",
                        "<p>कुषाण साम्राज्य</p>",
                        "<p>गुप्त साम्राज्य</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>Mauryan Empire.</strong> Chandragupta Maurya established the Mauryan Empire by defeating the Nandas in 322 BCE. Pataliputra was the capital of the Mauryan empire. Simuka was the founder of the Satavahana Dynasty. The founder of the Kushan dynasty was Kujala Kadphises.</p>",
                    solution_hi: "<p>50.(b) <strong>मौर्य साम्राज्य।</strong> चंद्रगुप्त मौर्य ने 322 ईसा पूर्व में नंदों को पराजित कर मौर्य साम्राज्य की स्थापना की। पाटलिपुत्र मौर्य साम्राज्य की राजधानी थी। सिमुक सातवाहन राजवंश के संस्थापक थे। कुषाण वंश के संस्थापक कुजुल कडफिसेस थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If 7 cosA = 6, then the numerical value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cotA</mi></mrow><mrow><mi>cosecA</mi><mo>-</mo><mi>cotA</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>51. यदि 7 cosA = 6 है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cotA</mi></mrow><mrow><mi>cosecA</mi><mo>-</mo><mi>cotA</mi></mrow></mfrac></math> का संख्&zwj;यात्&zwj;मक मान कितना होगा ?</p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>13</p>",
                        "<p>-13</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>13</p>",
                        "<p>-13</p>"
                    ],
                    solution_en: "<p>51.(c)<strong> Given,</strong> 7 cosA = 6 <br>Cos A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math><br>Here B = 6 , H = 7 , P = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>-</mo><mn>36</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math><br>Now , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cotA</mi></mrow><mrow><mi>cosecA</mi><mo>-</mo><mi>cotA</mi></mrow></mfrac></math> =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac><mo>+</mo><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mrow><mrow><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac><mo>-</mo><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mrow></mfrac></mstyle></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>13</mn><mn>1</mn></mfrac></mstyle></math> = 13</p>",
                    solution_hi: "<p>51.(c) <strong>दिया गया है, </strong>7 cosA = 6 <br>Cos A = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math><br>यहां , B = 6 , H = 7 , P = <math display=\"inline\"><msqrt><mn>49</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>36</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math><br>अब, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosecA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cotA</mi></mrow><mrow><mi>cosecA</mi><mo>-</mo><mi>cotA</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac><mo>+</mo><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mrow><mrow><mfrac><mn>7</mn><msqrt><mn>13</mn></msqrt></mfrac><mo>-</mo><mfrac><mn>6</mn><msqrt><mn>13</mn></msqrt></mfrac></mrow></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>13</mn><mn>1</mn></mfrac></mstyle></math> = 13</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Find the largest number which should replace P in the 7- digit number 87893P4 to make the number divisible by 4.</p>",
                    question_hi: "<p>52. वह सबसे बड़ा अंक ज्ञात कीजिए जिसे 7 अंकीय संख्या 87893P4 में P के स्थान पर रखे जाने पर दी गई संख्या 4 से विभाज्य हो जाए।</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>9</p>",
                        "<p>2</p>",
                        "<p>0</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>9</p>",
                        "<p>2</p>",
                        "<p>0</p>"
                    ],
                    solution_en: "<p>52.(a)<br><strong>Divisibility rule of 4,</strong><br>For any no to be divisible by 4, its last 2 digit should be divisible by 4<br>Then, For 87893P4, <strong>P4</strong> should be divisible by 4<br>For this , P can be 2, 4, 6 or 8<br>But for the maximum value of P, it must be 8.</p>",
                    solution_hi: "<p>52.(a)<br><strong>4 का विभाज्यता नियम,</strong><br>किसी भी संख्या को 4 से विभाज्य करने के लिए उसके अंतिम 2 अंक 4 से विभाज्य होने चाहिए<br>तब , 87893P4,के लिए, <strong>P4</strong> को 4 से विभाज्य होना चाहिए<br>इसके लिए, P का मान 2, 4, 6 या 8 हो सकता है<br>लेकिन P के अधिकतम मान के लिए यह 8 होना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The length of a side of an equilateral triangle is 18 cm. The area (in cm&sup2;) of the region lying between the circumcircle and the incircle of the triangle is [ use &pi; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle></math>]</p>",
                    question_hi: "<p>53. एक समबाहु त्रिभुज की एक भुजा की लंबाई 18 cm है। त्रिभुज के परिवृत्त और अंतःवृत्त के बीच स्थित क्षेत्र का क्षेत्रफल (cm&sup2; में) कितना है? [<math display=\"inline\"><mi>&#960;</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle></math> का उपयोग कीजिए]</p>",
                    options_en: [
                        "<p>254<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>254<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>254<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>254<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>254<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>254<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>254<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>254<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>53.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751413.png\" alt=\"rId54\" width=\"193\" height=\"170\"><br>Area of equilateral triangle = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac><mo>&#215;</mo><msup><mi>a</mi><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></mstyle></math> &times; 18 &times; 18 = 81<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm&sup2;<br>Relation between area of circumcircle, equilateral triangle and incircle <br>Circumcircle : equilateral triangle : incircle = 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math> : 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> : &pi;<br>Area of circumcircle - area of incircle = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mi mathvariant=\"normal\">&#960;</mi></math> - &pi; = 3&pi;<br><math display=\"inline\"><mn>3</mn><msqrt><mn>3</mn></msqrt></math> unit = 81<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm&sup2;<br>1 unit = 27 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>cm</mi><mn>2</mn></msup></math><br>3<math display=\"inline\"><mi>&#960;</mi></math> unit =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>27</mn><mn>1</mn></mfrac></mstyle></math> &times; 3 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle></math> = 254<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>7</mn></mfrac></mstyle></math>cm&sup2;</p>",
                    solution_hi: "<p>53.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751413.png\" alt=\"rId54\" width=\"193\" height=\"170\"><br>समबाहु त्रिभुज का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac><mo>&#215;</mo><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></mstyle></math>&times; 18 &times; 18 = 81<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm&sup2;<br>परिवृत्त, समबाहु त्रिभुज और अंतःवृत्त के क्षेत्रफल के बीच संबंध<br>परिवृत्त : समबाहु त्रिभुज : अंतःवृत्त = 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math> : 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> : &pi;<br>परिवृत्त का क्षेत्रफल - अंतःवृत्त का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><mi mathvariant=\"normal\">&#960;</mi></math> - &pi; = 3&pi;<br><math display=\"inline\"><mn>3</mn><msqrt><mn>3</mn></msqrt></math> इकाई = 81<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm&sup2;<br>1 इकाई = 27 cm&sup2;<br>3<math display=\"inline\"><mi>&#960;</mi></math> इकाई =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>27</mn><mn>1</mn></mfrac></mstyle></math> &times; 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>22</mn><mn>7</mn></mfrac></mstyle></math> = 254<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mn>7</mn></mfrac></mstyle></math> cm&sup2;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi><mo>&#179;</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow><mrow><mi>sin&#952;</mi><mo>-</mo><mn>3</mn><mi>sin</mi><mo>&#179;</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> is equal to:</p>",
                    question_hi: "<p>54. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#179;</mo><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>3</mn><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#179;</mo><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>tan&sup2; &theta;</p>",
                        "<p>cot&sup2; &theta;</p>",
                        "<p>t&alpha;n &theta;</p>",
                        "<p>cot &theta;</p>"
                    ],
                    options_hi: [
                        "<p>tan&sup2; &theta;</p>",
                        "<p>cot&sup2; &theta;</p>",
                        "<p>t&alpha;n &theta;</p>",
                        "<p>cot &theta;</p>"
                    ],
                    solution_en: "<p>54.(d) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>cos</mi><mo>&#179;</mo><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow><mrow><mi>sin&#952;</mi><mo>-</mo><mn>3</mn><mi>sin</mi><mo>&#179;</mo><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos&#952;</mi><mo>(</mo><mn>3</mn><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mrow><mi>sin&#952;</mi><mo>(</mo><mn>1</mn><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos&#952;</mi><mo>(</mo><mn>3</mn><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mrow><mi>sin&#952;</mi><mo>(</mo><mn>1</mn><mo>-</mo><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>{</mo><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>}</mo><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>(</mo><mn>3</mn><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>(</mo><mn>3</mn><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></mstyle></math>= cot &theta;</p>",
                    solution_hi: "<p>54.(d) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>cos</mi><mo>&#179;</mo><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mn>2</mn><mi>cos&#952;</mi></mrow><mrow><mi>sin&#952;</mi><mo>-</mo><mn>3</mn><mi>sin</mi><mo>&#179;</mo><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos&#952;</mi><mo>(</mo><mn>3</mn><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mrow><mi>sin&#952;</mi><mo>(</mo><mn>1</mn><mo>-</mo><mn>3</mn><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos&#952;</mi><mo>(</mo><mn>3</mn><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mrow><mi>sin&#952;</mi><mo>(</mo><mn>1</mn><mo>-</mo><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>{</mo><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">&#952;</mi><mo>}</mo><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi><mo>(</mo><mn>3</mn><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi><mo>(</mo><mn>3</mn><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow></mfrac></mstyle></math>= cot &theta;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. In fractions <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac><mo>,</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>2</mn><mn>15</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mi>and</mi><mfrac><mn>2</mn><mn>3</mn></mfrac></math> largest and smallest fractions are respectively</p>",
                    question_hi: "<p>55. भिन्नों <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac><mo>,</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>2</mn><mn>15</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#160;</mo><mi>&#2324;&#2352;</mi><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math> में, सबसे बड़ी और सबसे छोटी भिन्न क्रमशः हैं</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>15</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>15</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math></p>"
                    ],
                    solution_en: "<p>55.(a) According to the question,<br>LCM of denominator of fractions (10, 9, 15, 5, 3) = 90<br>Now,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac><mo>,</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>2</mn><mn>15</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mi>and</mi><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>90</mn></mfrac><mo>,</mo><mfrac><mn>40</mn><mn>90</mn></mfrac><mo>,</mo><mfrac><mn>12</mn><mn>90</mn></mfrac><mo>,</mo><mfrac><mn>54</mn><mn>90</mn></mfrac><mo>,</mo><mfrac><mn>60</mn><mn>90</mn></mfrac></math><br>So, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>15</mn></mfrac><mo>&#60;</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#60;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#60;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#60;</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math><br>Hence required numbers <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>15</mn></mfrac></math></p>",
                    solution_hi: "<p>55.(a) प्रश्न के अनुसार,<br>भिन्नों के हर (10, 9, 15, 5, 3) का LCM = 90<br>अब,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac><mo>,</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>,</mo><mfrac><mn>2</mn><mn>15</mn></mfrac><mo>,</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>,</mo><mi>&#2324;&#2352;</mi><mfrac><mn>2</mn><mrow><mn>3</mn><mo>&#160;</mo></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>90</mn></mfrac><mo>,</mo><mfrac><mn>40</mn><mn>90</mn></mfrac><mo>,</mo><mfrac><mn>12</mn><mn>90</mn></mfrac><mo>,</mo><mfrac><mn>54</mn><mn>90</mn></mfrac><mo>,</mo><mfrac><mn>60</mn><mn>90</mn></mfrac></math><br>तो, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>15</mn></mfrac><mo>&#60;</mo><mfrac><mn>4</mn><mn>9</mn></mfrac><mo>&#60;</mo><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>&#60;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#60;</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math><br>इसलिए आवश्यक संख्याएँ <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>15</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Simplify the following expression.<br>(4.2 of 2.5 &divide; 3.5) - (3.6 &divide; 2.4 + 2.5 of 3 &divide; 7.5) + (6 - 3 of 2 + 4 of 3 &divide; 6)</p>",
                    question_hi: "<p>56. निम्नलिखित व्यंजक को सरल कीजिए।<br>(4.2 का 2.5 &divide; 3.5) - (3.6 &divide; 2.4 + 2.5 का 3 &divide; 7.5) + (6 - 3 का 2 + 4 का 3 &divide; 6)</p>",
                    options_en: [
                        "<p>2.5</p>",
                        "<p>4.5</p>",
                        "<p>3.5</p>",
                        "<p>1.5</p>"
                    ],
                    options_hi: [
                        "<p>2.5</p>",
                        "<p>4.5</p>",
                        "<p>3.5</p>",
                        "<p>1.5</p>"
                    ],
                    solution_en: "<p>56.(a)<br>(4.2 of 2.5 &divide;&nbsp; 3.5) - (3.6 &divide; 2.4 + 2.5 of 3 &divide; 7.5) + (6 - 3 of 2 + 4 of 3 &divide; 6)<br><math display=\"inline\"><mo>&#8658;</mo></math> (10.5 &divide; 3.5) - (1.5 + 7.5 &divide; 7.5) + (6 - 6 + 12 &divide; 6)<br><math display=\"inline\"><mo>&#8658;</mo></math> (3) - (1.5 + 1) + (2)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 - 2.5 + 2 = 2.5</p>",
                    solution_hi: "<p>56.(a) <br>(4.2 का 2.5 &divide; 3.5) - (3.6 &divide; 2.4 + 2.5 का 3 &divide; 7.5) + (6 - 3 का 2 + 4 का 3 &divide; 6)<br><math display=\"inline\"><mo>&#8658;</mo></math> (10.5 &divide; 3.5) - (1.5 + 7.5 &divide; 7.5) + (6 - 6 + 12 &divide; 6)<br><math display=\"inline\"><mo>&#8658;</mo></math> (3) - (1.5 + 1) + (2)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 - 2.5 + 2 = 2.5</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Two numbers are, respectively, 30% and 40% more than the third number. The ratio of the second number to the first number is:</p>",
                    question_hi: "<p>57. दो संख्याएँ तीसरी संख्या से क्रमशः 30% और 40% अधिक हैं। दूसरी संख्या का पहली संख्या से अनुपात _____ है।</p>",
                    options_en: [
                        "<p>39 : 42</p>",
                        "<p>14 : 13</p>",
                        "<p>26 : 28</p>",
                        "<p>13 : 14</p>"
                    ],
                    options_hi: [
                        "<p>39 : 42</p>",
                        "<p>14 : 13</p>",
                        "<p>26 : 28</p>",
                        "<p>13 : 14</p>"
                    ],
                    solution_en: "<p>57.(b) <br>Let the third number be 100<br>Then, first no. = 100 &times; 130% = 130<br>and second no. = 100 &times; 140% = 140<br>Required ratio = 140 : 130 = 14 : 13</p>",
                    solution_hi: "<p>57.(b) <br>माना कि तीसरी संख्या 100 है<br>तो, पहली संख्या = 100 &times; 130% = 130<br>और दूसरी संख्या = 100 &times; 140% = 140<br>आवश्यक अनुपात = 140 : 130 = 14 : 13</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Arvind works 4 times as fast as Suresh. If Suresh can complete a work in 20 days independently, then find the number of days in which Arvind and Suresh can together finish the work.</p>",
                    question_hi: "<p>58. अरविंद, सुरेश से 4 गुना तेजी से काम करता है। यदि सुरेश स्वतंत्र रूप से किसी कार्य को 20 दिन में पूरा कर सकता है, तो अरविंद और सुरेश मिलकर उस कार्य को कितने दिन में पूरा कर सकते हैं?</p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>3</p>",
                        "<p>5</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>58.(d)&nbsp;Efficiency of Arvind and Suresh = 4 : 1<br>Total work = 20 &times; 1 = 20 unit<br>So, the time taken by them to complete the whole work = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><mn>4</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 4 days</p>",
                    solution_hi: "<p>58.(d)&nbsp;अरविंद और सुरेश की दक्षता = 4 : 1<br>कुल कार्य = 20 &times; 1 = 20 इकाई<br>अतः, पूरा कार्य पूरा करने में उनके द्वारा लिया गया समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mrow><mn>4</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math>&nbsp;= 4 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Anwesha\'s marks in a class text of Sanskrit were incorrectly entered as 99 instead of 66. If there are 55 students in the class, then what is the increment in the average marks of the class in Sanskrit because of this typing error ?</p>",
                    question_hi: "<p>59. संस्कृत के एक कक्षा पाठ में अन्वेषा के अंक गलती से 66 के बजाय 99 दर्ज कर दिए गए थे। यदि कक्षा में 55 छात्र हैं, तो इस टाइपिंग त्रुटि के कारण संस्कृत में कक्षा के औसत अंकों में कितनी वृद्धि हुई है?</p>",
                    options_en: [
                        "<p>0.2</p>",
                        "<p>0.8</p>",
                        "<p>0.4</p>",
                        "<p>0.6</p>"
                    ],
                    options_hi: [
                        "<p>0.2</p>",
                        "<p>0.8</p>",
                        "<p>0.4</p>",
                        "<p>0.6</p>"
                    ],
                    solution_en: "<p>59.(d)<br>Increment in average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>99</mn><mo>-</mo><mn>66</mn></mrow><mn>55</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>55</mn></mfrac></math> = 0.6</p>",
                    solution_hi: "<p>59.(d)<br>औसत वृद्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>99</mn><mo>-</mo><mn>66</mn></mrow><mn>55</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>55</mn></mfrac></math> = 0.6</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Shopkeeper A marks up his price for an item at 25% and offers a discount of 15%. The same item is marked at 20% by another shopkeeper B and sold at a discount of 12%. Who gets a better deal in terms of % profit and by what profit percentage he sells that item ?</p>",
                    question_hi: "<p>60. दुकानदार A एक वस्तु का मूल्य 25% अधिक अंकित करता है और 15% की छूट देता है। उसी वस्तु पर एक दूसरे दुकानदार B द्वारा 20% अधिक मूल्य अंकित किया जाता है और 12% की छूट पर बेचा जाता है। % लाभ के मामले में किसे एक बेहतर डील मिलती है और वह उस वस्तु को कितने प्रतिशत लाभ में बेचता है ?</p>",
                    options_en: [
                        "<p>A by 0.55%</p>",
                        "<p>B by 0.65%</p>",
                        "<p>B by 5.6%</p>",
                        "<p>A by 6.25%</p>"
                    ],
                    options_hi: [
                        "<p>A द्वारा 0.55%</p>",
                        "<p>B द्वारा 0.65%</p>",
                        "<p>B द्वारा 5.6%</p>",
                        "<p>A द्वारा 6.25%</p>"
                    ],
                    solution_en: "<p>60.(d) <strong>For shopkeeper A :-</strong> <br>C.P. : M.P. = 4 : 5<br>After giving 15% discount<br>S.P. = 5 - 5 &times; 15% = 5 - 0.75 = 4.25<br>Profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>25</mn><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow><mn>4</mn></mfrac></math> &times; 100 = 6.25%<br><strong>For shopkeeper B :-</strong><br>C.P. : M.P. = 5 : 6<br>After giving 12% discount<br>S.P. = 6 - 6 &times; 12% = 6 - 0.72 = 5.28<br>Profit % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>.</mo><mn>28</mn><mo>-</mo><mn>5</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>28</mn></mrow><mn>5</mn></mfrac></math> &times; 100 = 5.60%<br>Clearly, A gets the better deal with 6.25% profit.</p>",
                    solution_hi: "<p>60.(d) <strong>दुकानदार A के लिए :-</strong> <br>क्रय मूल्य : अंकित मूल्य = 4 : 5<br>15% छूट देने के बाद<br>विक्रय मूल्य = 5 - 5 &times; 15% = 5 - 0.75 = 4.25<br>लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>25</mn><mo>-</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow><mn>4</mn></mfrac></math>&nbsp;&times; 100 = 6.25%<br><strong>दुकानदार B के लिए:-</strong><br>क्रय मूल्य : अंकित मूल्य = 5 : 6<br>12% छूट देने के बाद<br>विक्रय मूल्य = 6 - 6 &times; 12% = 6 - 0.72 = 5.28<br>लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>.</mo><mn>28</mn><mo>-</mo><mn>5</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>28</mn></mrow><mn>5</mn></mfrac></math>&nbsp;&times; 100 = 5.60%<br>स्पष्टतः, A को 6.25% लाभ के साथ बेहतर सौदा मिलता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A right angled triangle of area 600 m&sup2; is to be made such that the hypotenuse is double the base. For the triangle, the hypotenuse is _______ more than the base.</p>",
                    question_hi: "<p>61. 600m&sup2; क्षेत्रफल वाले एक समकोण त्रिभुज को इस प्रकार बनाया जाना है कि कर्ण, आधार से दोगुना हो। त्रिभुज का कर्ण, उसके आधार से कितना अधिक है?</p>",
                    options_en: [
                        "<p>26.32 m</p>",
                        "<p>22.32 m</p>",
                        "<p>24.36 m</p>",
                        "<p>20.36 m</p>"
                    ],
                    options_hi: [
                        "<p>26.32 m</p>",
                        "<p>22.32 m</p>",
                        "<p>24.36 m</p>",
                        "<p>20.36 m</p>"
                    ],
                    solution_en: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751533.png\" alt=\"rId55\" width=\"150\" height=\"171\"><br>Using pythagoras theorem <br>PQ<sup>2</sup> = PR<sup>2</sup> - QR<sup>2</sup><br>= 4x<sup>2</sup>&nbsp;- x<sup>2 </sup>= 3x<sup>2</sup><br>PQ = x<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>Area of triangle = 600 <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; QR &times; PQ = 600<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; x &times; x<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 600 <br>x<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1200</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>x<sup>2</sup> = 400<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>x<sup>2</sup> = 400 &times; 1.73 <br>x<sup>2</sup> = 692<br>x = 26.30<br>Difference between hypotenuse and base ( 2x&nbsp;- x = x) = 26.30</p>",
                    solution_hi: "<p>61.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751533.png\" alt=\"rId55\" width=\"149\" height=\"169\"><br>पाइथागोरस प्रमेय का उपयोग करने पर <br>PQ<sup>2</sup> = PR<sup>2</sup> - QR<sup>2</sup><br>= 4x<sup>2</sup>&nbsp;- x<sup>2 </sup>= 3x<sup>2</sup><br>PQ = x<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>त्रिभुज का क्षेत्रफल = 600 <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; QR &times; PQ = 600<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; x &times; x<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 600 <br>x<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1200</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>x<sup>2</sup> = 400<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>x<sup>2</sup> = 400 &times; 1.73 <br>x<sup>2</sup> = 692<br>x = 26.30<br>कर्ण और आधार के बीच अंतर ( 2x&nbsp;- x = x) = 26.30</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The savings and expenditure of a family in a month on different heads is shown in the given pie chart. The family saves ₹8,000 per month.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751639.png\" alt=\"rId56\" width=\"350\" height=\"212\"> <br>Find the expenditure (in ₹) on education.</p>",
                    question_hi: "<p>62. एक परिवार की एक महीने में विभिन्न मदों में की गई बचत और व्यय को दिए गए पाई चार्ट में दर्शाया गया है। यह परिवार प्रति माह ₹8,000 की बचत करता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751754.png\" alt=\"rId57\" width=\"350\"><br>शिक्षा पर किया गया व्यय (₹ में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>4000</p>",
                        "<p>3000</p>",
                        "<p>2500</p>",
                        "<p>3500</p>"
                    ],
                    options_hi: [
                        "<p>4000</p>",
                        "<p>3000</p>",
                        "<p>2500</p>",
                        "<p>3500</p>"
                    ],
                    solution_en: "<p>62.(a)<br>Expenditure on education = <math display=\"inline\"><mfrac><mrow><mn>8000</mn></mrow><mrow><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 30&deg; = ₹4000</p>",
                    solution_hi: "<p>62.(a)<br>शिक्षा पर व्यय = <math display=\"inline\"><mfrac><mrow><mn>8000</mn></mrow><mrow><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 30&deg; = ₹4000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Find a single discount equivalent to the successive discount of 12%, 20%, 24% and 32%. (Correct to two places of decimals)</p>",
                    question_hi: "<p>63. 12%, 20%, 24% और 32% की क्रमिक छूटों के बराबर एक एकल छूट ज्ञात कीजिए। (दशमलव के दो<br>स्थानों तक शुद्ध)</p>",
                    options_en: [
                        "<p>43.41%</p>",
                        "<p>73.71%</p>",
                        "<p>63.62%</p>",
                        "<p>53.51%</p>"
                    ],
                    options_hi: [
                        "<p>43.41%</p>",
                        "<p>73.71%</p>",
                        "<p>63.62%</p>",
                        "<p>53.51%</p>"
                    ],
                    solution_en: "<p>63.(c)<br>Single discount of 12% &amp; 20% = (12 + 20 &ndash; <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32 - 2.4 = 29.6% &asymp; 30%<br>Single discount of 24% &amp; 32% = (24 + 32 - <math display=\"inline\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>32</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 56 - 7.68 = 48.32% &asymp; 48%<br>Now, single equivalent discount of 30% &amp; 48% = (30 + 48 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>48</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 78 - 14.4 = 63.6% &asymp; 63.62%</p>",
                    solution_hi: "<p>63.(c) <br>12% और 20% की एकल छूट = (12 + 20 &ndash; <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32 - 2.4 = 29.6% &asymp; 30%<br>24% और 32% की एकल छूट = (24 + 32 - <math display=\"inline\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>32</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 56 - 7.68 = 48.32% &asymp; 48%<br>अब, 30% और 48% की एकल समकक्ष छूट = (30 + 48 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>48</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 78 - 14.4 = 63.6% &asymp; 63.62%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Rice worth ₹43/kg and ₹67/kg are mixed with a third variety in the ratio 2 : 1 : 5. If the mixture is worth ₹96/kg, the price (in ₹) of the third variety of rice per kg will be:</p>",
                    question_hi: "<p>64. ₹43/kg और ₹67/kg मूल्य वाले चावलों को तीसरी किस्म के चावल के साथ 2 : 1 : 5 के अनुपात में मिलाया जाता है। यदि मिश्रण का मूल्य ₹96/kg है, तो तीसरी किस्म के चावल का प्रति kg मूल्य (₹ में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>131</p>",
                        "<p>159</p>",
                        "<p>123</p>",
                        "<p>142</p>"
                    ],
                    options_hi: [
                        "<p>131</p>",
                        "<p>159</p>",
                        "<p>123</p>",
                        "<p>142</p>"
                    ],
                    solution_en: "<p>64.(c)&nbsp;According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>43</mn><mo>&#160;</mo><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>67</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = 96<br>86 + 67 + 5x&nbsp;= 96 &times; 8<br>5x&nbsp;= 768 - 153 = 615<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>615</mn><mn>5</mn></mfrac></math> = 123<br>So, per kg price of third variety (<math display=\"inline\"><mi>x</mi></math>) = ₹123</p>",
                    solution_hi: "<p>64.(c)&nbsp;प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>43</mn><mo>&#160;</mo><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>67</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = 96<br>86 + 67 + 5x&nbsp;= 96 &times; 8<br>5x&nbsp;= 768 - 153 = 615<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>615</mn><mn>5</mn></mfrac></math> = 123<br>तो, तीसरी किस्म की प्रति किलोग्राम कीमत (x) = ₹123</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. In an election between two candidates, one candidate gets 72% of the total votes cast. If the total votes are 1000, by how many votes does the winner win the election?</p>",
                    question_hi: "<p>65. दो उम्मीदवारों के बीच एक चुनाव में एक उम्मीदवार को डाले गए कुल मतों का 72% प्राप्त होता है। यदि कुल मत 1000 हैं, तो विजेता कितने मतों से चुनाव जीतता है?</p>",
                    options_en: [
                        "<p>360</p>",
                        "<p>720</p>",
                        "<p>250</p>",
                        "<p>440</p>"
                    ],
                    options_hi: [
                        "<p>360</p>",
                        "<p>720</p>",
                        "<p>250</p>",
                        "<p>440</p>"
                    ],
                    solution_en: "<p>65.(d)&nbsp;Votes got by looser candidate = 28%<br>Votes got by winner candidate = 72%<br>Difference = 72% - 28% = 44%<br>(total number of votes) 100% = 1000 votes<br>(no. of votes by which winner won the election) 44% = 440 votes</p>",
                    solution_hi: "<p>65.(d)&nbsp;हारे हुए उम्मीदवार को मिले वोट = 28%<br>विजेता उम्मीदवार को मिले वोट = 72%<br>अंतर = 72% - 28% = 44%<br>(मतों की कुल संख्या) 100% = 1000 मत<br>(वोटों की संख्या जिनसे विजेता ने चुनाव जीता) 44% = 440 वोट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Pipe A can fill a cistern in 4 hours and Pipe B can fill the same cistern in 5 hours. Pipe C can empty a full cistern in 3 hours. If all three pipes are opened together, then the time (in minutes) taken to fill the tank is: (round to the nearest minute)</p>",
                    question_hi: "<p>66. पाइप A एक टंकी को 4 घंटे में भर सकता है और पाइप B उसी टंकी को 5 घंटे में भर सकता है। पाइप C पूरी टंकी को 3 घंटे में खाली कर सकता है। यदि सभी तीन पाइप एक साथ खोले जाते हैं, तो टंकी को भरने में कितना समय (मिनट में) लगेगा? (निकटतम मिनट तक पूर्णांकित)</p>",
                    options_en: [
                        "<p>625</p>",
                        "<p>445</p>",
                        "<p>800</p>",
                        "<p>514</p>"
                    ],
                    options_hi: [
                        "<p>625</p>",
                        "<p>445</p>",
                        "<p>800</p>",
                        "<p>514</p>"
                    ],
                    solution_en: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650751973.png\" alt=\"rId58\" width=\"280\" height=\"131\"><br>Efficiency of all pipe together = 15 + 12 - 20 = 7 unit<br>Time taken to fill tank together = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo></math> 60 minutes = 514 .28 &asymp; 514 minute</p>",
                    solution_hi: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650752259.png\" alt=\"rId59\" width=\"280\" height=\"145\"><br>सभी पाइपों की कुल क्षमता= 15 + 12 - 20 = 7 इकाई<br>टंकी को एक साथ भरने में लगा समय =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>&#215;</mo></math> 60 मिनट = 514 .28 &asymp; 514 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Find the amount to be paid at the end if ₹4,500 is invested for a period of 4 years at the rate of 8% simple interest per annum.</p>",
                    question_hi: "<p>67. यदि ₹4,500 की धनराशि का 8% वार्षिक साधारण ब्याज की दर से 4 वर्ष की अवधि के लिए निवेश किया जाता है, तो अवधि के अंत में भुगतान की जाने वाली धनराशि ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹6,080</p>",
                        "<p>₹5,940</p>",
                        "<p>₹6,300</p>",
                        "<p>₹5,490</p>"
                    ],
                    options_hi: [
                        "<p>₹6,080</p>",
                        "<p>₹5,940</p>",
                        "<p>₹6,300</p>",
                        "<p>₹5,490</p>"
                    ],
                    solution_en: "<p>67.(b) let the principal = 100<br>Amount after 4 year with rate of 8% = 132<br>Now,<br>100 unit = ₹ 4500<br>132 unit = 45 &times; 132 = ₹ 5940</p>",
                    solution_hi: "<p>67.(b) माना मूलधन = 100<br>4 वर्ष के बाद 8% की दर से राशि = 132<br>अब,<br>100 इकाई = ₹ 4500<br>132 इकाई = 45 &times; 132 = ₹ 5940</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. One laddoo with a radius of 5 cm belongs to a shopkeeper. How many laddoos of radius 2.5 cm may be created from the same one laddoo ?</p>",
                    question_hi: "<p>68. 5 cm त्रिज्या वाला एक लड्डू एक दुकानदार का है। उसी एक ही लड्डू से 2.5 cm त्रिज्या के कितने लड्डू बनाए जा सकते हैं?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>6</p>",
                        "<p>4</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>68.(d) Volume of laddoo of radius 5 cm = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;R<sup>3</sup>&nbsp;<br>Volume of laddoo of radius 2.5 cm = &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;r<sup>3</sup><br>No. of laddoos created = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle><msup><mi>&#960;R</mi><mn>3</mn></msup></mrow><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><msup><mi>&#960;r</mi><mn>3</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 8 laddoos</p>",
                    solution_hi: "<p>68.(d) 5 सेमी त्रिज्या वाले लड्डू का आयतन = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;R<sup>3</sup>&nbsp;<br>2.5 सेमी त्रिज्या वाले लड्डू का आयतन = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;r<sup>3</sup><br>बनाए गए लड्डुओं की संख्या =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle><msup><mi>&#960;R</mi><mn>3</mn></msup></mrow><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><msup><mi>&#960;r</mi><mn>3</mn></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 8 लड्डू&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A shopkeeper marks up his goods 32% above the cost price and gives a discount of 21% on the marked price. Find his gain percentage on the goods.</p>",
                    question_hi: "<p>69. एक दुकानदार अपने माल पर क्रय मूल्य से 32% अधिक मूल्य अंकित करता है और अंकित मूल्य पर 21% की छूट देता है। माल पर उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>8.24%</p>",
                        "<p>8.42%</p>",
                        "<p>4.28%</p>",
                        "<p>4.82%</p>"
                    ],
                    options_hi: [
                        "<p>8.24%</p>",
                        "<p>8.42%</p>",
                        "<p>4.28%</p>",
                        "<p>4.82%</p>"
                    ],
                    solution_en: "<p>69.(c)&nbsp;According to the question,<br>Let the CP of the goods is 100 units<br>MP of the goods = 100 &times; 132% = 132 units<br>SP of the goods = 132 &times; 79% = 104.28 units<br>profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>104</mn><mo>.</mo><mn>28</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math> &times; 100 = 4.28%</p>",
                    solution_hi: "<p>69.(c)&nbsp;प्रश्न के अनुसार,<br>माना माल का क्रय मूल्य 100 इकाई है<br>माल का अंकित मूल्य = 100 &times; 132% = 132 इकाई <br>माल का विक्रय मूल्य = 132 &times; 79% = 104.28 इकाई <br>लाभ% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>104</mn><mo>.</mo><mn>28</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math> &times; 100 = 4.28%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The below table shows the number of trees of 3 different fruits, planted during 2018-2022. Study the table carefully and answer the question based on it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650752429.png\" alt=\"rId60\" width=\"320\" height=\"117\"> <br>What was the percentage increase in the number of mango trees planted in 2020 compared to the number of mango trees planted in 2018?</p>",
                    question_hi: "<p>70. नीचे दी गई तालिका 2018-2022 के दौरान लगाए गए 3 अलग-अलग फलों के पेड़ों की संख्या दर्शाती है। तालिका का ध्यानपूर्वक अध्ययन कीजिए और उसके आधार पर दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650752572.png\" alt=\"rId61\" width=\"300\" height=\"129\"> <br>2018 में लगाए गए आम के पेड़ों की संख्या की तुलना में 2020 में लगाए गए आम के पेड़ों की संख्या में कितने प्रतिशत की वृद्धि हुई?</p>",
                    options_en: [
                        "<p>15%</p>",
                        "<p>17.5%</p>",
                        "<p>30%</p>",
                        "<p>23.08%</p>"
                    ],
                    options_hi: [
                        "<p>15%</p>",
                        "<p>17.5%</p>",
                        "<p>30%</p>",
                        "<p>23.08%</p>"
                    ],
                    solution_en: "<p>70.(c) <br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>52000</mn><mo>-</mo><mn>40000</mn><mo>)</mo></mrow><mn>40000</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12000</mn><mrow><mn>40000</mn><mo>&#160;</mo></mrow></mfrac></math>&times; 100 = 30%</p>",
                    solution_hi: "<p>70.(c) <br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>52000</mn><mo>-</mo><mn>40000</mn><mo>)</mo></mrow><mn>40000</mn></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12000</mn><mrow><mn>40000</mn><mo>&#160;</mo></mrow></mfrac></math>&times; 100 = 30%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A class of 30 students appeared in a test. The average score of 12 students is 62, and that of the rest is 78. What is the average score of the class ?</p>",
                    question_hi: "<p>71. 30 छात्रों वाली एक कक्षा के सभी छात्रों ने एक टेस्ट दिया। 12 छात्रों का औसत स्कोर 62 है और बाकी छात्रों का औसत स्कोर 78 है। कक्षा का औसत स्कोर ज्ञात करें।</p>",
                    options_en: [
                        "<p>72.6</p>",
                        "<p>69.6</p>",
                        "<p>70.6</p>",
                        "<p>71.6</p>"
                    ],
                    options_hi: [
                        "<p>72.6</p>",
                        "<p>69.6</p>",
                        "<p>70.6</p>",
                        "<p>71.6</p>"
                    ],
                    solution_en: "<p>71.(d)&nbsp;Total students = 30 <br>Average score of class = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>62</mn><mo>+</mo><mn>18</mn><mo>&#215;</mo><mn>78</mn></mrow><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>62</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>78</mn></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>248</mn><mo>+</mo><mn>468</mn></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>716</mn><mn>10</mn></mfrac></math> = 71.6</p>",
                    solution_hi: "<p>71.(d)&nbsp;कुल छात्र = 30 <br>कक्षा का औसत अंक = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>62</mn><mo>+</mo><mn>18</mn><mo>&#215;</mo><mn>78</mn></mrow><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>62</mn><mo>+</mo><mn>6</mn><mo>&#215;</mo><mn>78</mn></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>248</mn><mo>+</mo><mn>468</mn></mrow><mn>10</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>716</mn><mn>10</mn></mfrac></math> = 71.6</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If the system of the following equations has the value of the variables as three consecutive integers, then the value of a is ____.<br>x - y + z = 2a<br>x + 4y - 2z = 3(4 - a)<br>2x - 3y + 4z = 6 - 2a</p>",
                    question_hi: "<p>72. यदि निम्नलिखित समीकरणों की प्रणाली में चरों का मान लगातार तीन पूर्णांकों के रूप में है, तो a का मान ____ है।<br>x - y + z = 2a<br>x + 4y - 2z = 3(4 - a)<br>2x - 3y + 4z = 6 - 2a</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>1</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>72.(c) Let the three consecutive numbers x, y, and z be 3 , 2 and 1 respectively,<br>x - y + z = 2a &hellip;. (i)<br>Put the value x, y and z, we get<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 - 2 + 1 = 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>x + 4y - 2z = 3(4 - a) &hellip;. (ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 8 - 2 = 3(4 - a)<br><math display=\"inline\"><mo>&#8658;</mo></math> 9 = 3(4 - a)<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>2x - 3y + 4z = 6 - 2a &hellip;. (iii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 6 - 6 + 4 = 6 - 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 = 6 - 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>It is clear form in all above equations satisfies the value of x, y and z, so the value of a = 1</p>",
                    solution_hi: "<p>72.(c) माना , तीन क्रमागत संख्याएँ x, y, और z क्रमशः 3, 2 और 1 हैं<br>x - y + z = 2a &hellip;. (i)<br>x, y और z का मान रखने पर <br><math display=\"inline\"><mo>&#8658;</mo></math> 3 - 2 + 1 = 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>x + 4y - 2z = 3(4 - a) &hellip;. (ii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 8 - 2 = 3(4 - a)<br><math display=\"inline\"><mo>&#8658;</mo></math> 9 = 3(4 - a)<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>2x - 3y + 4z = 6 - 2a &hellip;. (iii)<br><math display=\"inline\"><mo>&#8658;</mo></math> 6 - 6 + 4 = 6 - 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 = 6 - 2a<br><math display=\"inline\"><mo>&#8658;</mo></math> a = 1<br>यह स्पष्ट है कि उपरोक्त सभी समीकरणों में x, y और z का मान संतुष्ट होता है, अतः a का मान = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A teacher travelled a distance of 22 km in 7 hours. He travelled partly on foot at the speed of 2 km/h and partly on bicycle at the rate of 4 km/h. The distance travelled on foot is:</p>",
                    question_hi: "<p>73. एक शिक्षक ने 7 घंटे में 22 km की दूरी तय की। उन्होंने आंशिक रूप से 2 km/h की चाल से पैदल यात्रा की और आंशिक रूप से 4 km/h की चाल से साइकिल से यात्रा की। पैदल तय की गई दूरी कितनी है?</p>",
                    options_en: [
                        "<p>10 km</p>",
                        "<p>3 km</p>",
                        "<p>6 km</p>",
                        "<p>8 km</p>"
                    ],
                    options_hi: [
                        "<p>10 km</p>",
                        "<p>3 km</p>",
                        "<p>6 km</p>",
                        "<p>8 km</p>"
                    ],
                    solution_en: "<p>73.(c)&nbsp;If he travelled all the time on foot = 2 &times;&nbsp;7 = 14 km<br>And if he travelled all the time by bicycle = 4 &times;&nbsp;7 = 28 km<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650752758.png\" alt=\"rId62\" width=\"180\" height=\"154\"><br>Hence, time taken = 6 + 8 = 14 unit = 7 hrs<br><math display=\"inline\"><mo>&#8658;</mo></math> time taken by foot = 3 hrs <br>And time taken by bicycle = 4 hrs<br>Now, distance travelled on foot = 3 &times;&nbsp;2 = 6 km.</p>",
                    solution_hi: "<p>73.(c)&nbsp;यदि पूरे समय पैदल यात्रा करने से यात्रा करे तो तय की गई दूरी = 2 &times;&nbsp;7 = 14 km<br>और यदि वह पूरे समय साइकिल से यात्रा करे तो तय की गई दूरी = 4 &times;&nbsp;7 = 28 km<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743650753068.png\" alt=\"rId63\" width=\"180\" height=\"161\"><br>अतः, लिया गया समय = 6 + 8 = 14 इकाई = 7 घंटे<br><math display=\"inline\"><mo>&#8658;</mo></math> पैदल चलने में लगा समय (6 इकाई ) = 3 घंटे&nbsp;<br>तथा साइकिल द्वारा लिया गया समय (8 इकाई ) = 4 घंटे<br>अब, पैदल तय की गई दूरी = 3 &times; 2 = 6 किमी</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The average of five integers is 48. The average of the first four integers is 50% of the 5th integer. The 5th integer is:</p>",
                    question_hi: "<p>74. पाँच पूर्णांकों का औसत 48 है। प्रथम चार पूर्णांकों का औसत, पाँचवें पूर्णांक का 50% है। पाँचवाँ पूर्णांक ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>74</p>",
                        "<p>80</p>",
                        "<p>82</p>",
                        "<p>78</p>"
                    ],
                    options_hi: [
                        "<p>74</p>",
                        "<p>80</p>",
                        "<p>82</p>",
                        "<p>78</p>"
                    ],
                    solution_en: "<p>74.(b) Let five integers be a, b, c, d and e<br>Average of five integers = 48<br>Then, (a + b + c + d + e) = 48 &times; 5 = 240 &hellip;&hellip;(i)<br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi><mo>+</mo><mi mathvariant=\"normal\">d</mi></mrow><mn>4</mn></mfrac></math> = e &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> a + b + c + d = 2e <br>Now put the v<math display=\"inline\"><mi>a</mi></math>lue of (a + b + c + d) in eqn. (i)<br><math display=\"inline\"><mo>&#8658;</mo></math> (2e + e) = 240<br><math display=\"inline\"><mo>&#8658;</mo></math> e = 80<br>Hence, 5th integer will be 80</p>",
                    solution_hi: "<p>74.(b) माना पाँच पूर्णांक a, b, c, d और e है।<br>पाँच पूर्णांकों का औसत = 48<br>तो, (a + b + c + d + e) = 48 &times; 5 = 240 &hellip;&hellip;(i)<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>+</mo><mi mathvariant=\"normal\">b</mi><mo>+</mo><mi mathvariant=\"normal\">c</mi><mo>+</mo><mi mathvariant=\"normal\">d</mi></mrow><mn>4</mn></mfrac></math> = e &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> a + b + c + d = 2e <br>(a&nbsp;+ b + c + d) का मान समी. (i) में रखने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math> (2e + e) = 240<br><math display=\"inline\"><mo>&#8658;</mo></math> e = 80<br>अतः पाँचवाँ पूर्णांक 80 होगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The value of a machine depreciates every year by 5%. If the present value of the&nbsp;machine is ₹80,000, what will be its value after 2 years?</p>",
                    question_hi: "<p>75 एक मशीन का मूल्य हर वर्ष 5% घट जाता है। यदि मशीन का वर्तमान मूल्य ₹80,000 है, तो 2 वर्ष बाद इसका मूल्य क्या होगा?</p>",
                    options_en: [
                        "<p>₹72,000</p>",
                        "<p>₹72,200</p>",
                        "<p>₹74,000</p>",
                        "<p>₹74,400</p>"
                    ],
                    options_hi: [
                        "<p>₹72,000</p>",
                        "<p>₹72,200</p>",
                        "<p>₹74,000</p>",
                        "<p>₹74,400</p>"
                    ],
                    solution_en: "<p>75.(b) <br>Value after 2 years = 80000 &times; <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>= ₹72,200</p>",
                    solution_hi: "<p>75.(b) <br>2 वर्ष बाद मूल्य = 80000 &times; <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = ₹72,200</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>Some people like to do some social work even though it lacks resources.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>Some people like to do some social work even though it lacks resources.</p>",
                    options_en: [
                        "<p>Some people like</p>",
                        "<p>to do some social</p>",
                        "<p>it lacks resources.</p>",
                        "<p>work even though</p>"
                    ],
                    options_hi: [
                        "<p>Some people like</p>",
                        "<p>to do some social</p>",
                        "<p>it lacks resources.</p>",
                        "<p>work even though</p>"
                    ],
                    solution_en: "<p>76.(c) it lacks resources<br>The pronoun in the phrase &lsquo;it lacks&rsquo; will be replaced by &lsquo;They&rsquo; for the noun &lsquo;some people&rsquo; and the verb &lsquo;Lack&rsquo; will be substituted as per the Pronoun. Hence, &lsquo;They lack resources&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(c) it lacks resources<br>Phrase में pronoun &lsquo;it&rsquo; को \'they\' द्वारा replace किया जाएगा । इसलिए, \'They lack resources\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Find the correctly spelt word:</p>",
                    question_hi: "<p>77. Find the correctly spelt word:</p>",
                    options_en: [
                        "<p>commencement</p>",
                        "<p>estableshment</p>",
                        "<p>announcment</p>",
                        "<p>Committment</p>"
                    ],
                    options_hi: [
                        "<p>commencement</p>",
                        "<p>estableshment</p>",
                        "<p>announcment</p>",
                        "<p>Committment</p>"
                    ],
                    solution_en: "<p>77.(a) commencement</p>",
                    solution_hi: "<p>77.(a) commencement</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "78. Identify the error in the following sentence.<br />Brr, the city view from the Eiffel Tower is amazing!",
                    question_hi: "78. Identify the error in the following sentence.<br />Brr, the city view from the Eiffel Tower is amazing!",
                    options_en: [
                        " amazing",
                        " from",
                        " view",
                        " Brr"
                    ],
                    options_hi: [
                        " amazing",
                        " from",
                        " view",
                        " Brr"
                    ],
                    solution_en: "78.(d) Brr<br />An interjection is a word or phrase used to express a sudden feeling. ‘Brr’ is used to express the sensation of cold. ‘Wow’ is used to express a sudden feeling of amazement. Hence, ‘Wow’ is the most appropriate answer.",
                    solution_hi: "78.(d) Brr<br />Interjection एक word या phrase होता है जिसका प्रयोग अचानक महसूस होने वाली feeling को express करने के लिए किया जाता है। ‘Brr’ का use ठंड की अनुभूति को व्यक्त करने के लिए किया जाता है। ‘Wow’ का use आश्चर्य की भावना को व्यक्त करने के लिए किया जाता है। अतः, ‘Wow’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>In the olden days, many <span style=\"text-decoration: underline;\"><strong>people use to think</strong></span> that the earth was flat.</p>",
                    question_hi: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>In the olden days, many <span style=\"text-decoration: underline;\"><strong>people use to think</strong></span> that the earth was flat.</p>",
                    options_en: [
                        "<p>people used to think</p>",
                        "<p>people were used to think</p>",
                        "<p>people were thinking</p>",
                        "<p>No substitution required</p>"
                    ],
                    options_hi: [
                        "<p>people used to think</p>",
                        "<p>people were used to think</p>",
                        "<p>people were thinking</p>",
                        "<p>No substitution required</p>"
                    ],
                    solution_en: "<p>79.(a) people used to think<br>The given sentence is in the past tense so the verb must be used in its past form(used) and not in the present form(use). Hence, &lsquo;people used to think&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(a) people used to think<br>दिया गया वाक्य past tense में है इसलिए verb का उपयोग उसके past form (used) में किया जाना चाहिए न कि present form (use) में। इसलिए, \'people used to think\' सबसे उपयुक्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate synonym of the given word.<br>Lax</p>",
                    question_hi: "<p>80. Select the most appropriate synonym of the given word.<br>Lax</p>",
                    options_en: [
                        "<p>Needy</p>",
                        "<p>Negligent</p>",
                        "<p>Careful</p>",
                        "<p>Deprived</p>"
                    ],
                    options_hi: [
                        "<p>Needy</p>",
                        "<p>Negligent</p>",
                        "<p>Careful</p>",
                        "<p>Deprived</p>"
                    ],
                    solution_en: "<p>80.(b) Negligent <br><strong>Lax</strong>- not having high standards<br><strong>Negligent</strong>- not being careful or giving enough attention to people or things that are your responsibility<br><strong>Needy</strong>- not having enough money, food, clothes, etc.<br><strong>Careful</strong>- thinking about what you are doing so that you do not have an accident or make mistakes, etc.<br><strong>Deprived</strong>- not having enough of the basic things in life, such as food, money, etc.</p>",
                    solution_hi: "<p>80.(b) Negligent <br><strong>Lax</strong>- not having high standards (उच्च मानक न होना।)<br><strong>Negligent</strong>- not being careful or giving enough attention to people or things that are your responsibility (सावधान न होना या उन लोगों या चीजों पर पर्याप्त ध्यान न देना जो आपकी जिम्मेदारी हैं।)&nbsp;<br><strong>Needy</strong>- not having enough money, food, clothes, etc. (पर्याप्त धन, भोजन, वस्त्र आदि न होना।)<br><strong>Careful</strong>- thinking about what you are doing so that you do not have an accident or make mistakes, etc. (इस बारे में सोचना कि आप क्या कर रहे हैं ताकि आपके साथ कोई दुर्घटना न हो या गलतियाँ न हों, आदि।)<br><strong>Deprived</strong>- not having enough of the basic things in life, such as food, money, etc. (जीवन में पर्याप्त बुनियादी चीजें जैसे भोजन, पैसा आदि न होना।)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>Will Martin be helped by us ?</p>",
                    question_hi: "<p>81. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>Will Martin be helped by us?</p>",
                    options_en: [
                        "<p>Will Martin help us ?</p>",
                        "<p>Shall we help Martin ?</p>",
                        "<p>Shall Martin be helped ?</p>",
                        "<p>We shall helped Martin ?</p>"
                    ],
                    options_hi: [
                        "<p>Will Martin help us ?</p>",
                        "<p>Shall we help Martin ?</p>",
                        "<p>Shall Martin be helped ?</p>",
                        "<p>We shall helped Martin ?</p>"
                    ],
                    solution_en: "<p>81.(b) Shall we help Martin ?<br>(a) Will Martin help us ? (Meaning has changed)<br>(c) Shall Martin be helped? (Doer is missing)<br>(d) We <strong>shall helped</strong> Martin ? (Incorrect structure)</p>",
                    solution_hi: "<p>81.(b) Shall we help Martin ?<br>(a). Will Martin help us ? (अर्थ बदल गया)<br>(c). Shall Martin be helped? (कर्ता नहीं है)<br>(d). We <strong>shall</strong> <strong>helped </strong>Martin ? (गलत structure)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) When the oxygen in the air combines with carbon and hydrogen in a fuel, a chemical reaction takes place.<br />(B) Energy in the form of heat and light is released in this process what we call fire.<br />(C) But we now know that fire is the result of a chemical reaction.<br />(D) Fire may have puzzled early man.",
                    question_hi: "82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) When the oxygen in the air combines with carbon and hydrogen in a fuel, a chemical reaction takes place.<br />(B)Energy in the form of heat and light is released in this process what we call fire.<br />(C) But we now know that fire is the result of a chemical reaction.<br />(D)Fire may have puzzled early man.",
                    options_en: [
                        " DACB",
                        " ABCD",
                        " DCAB",
                        " ADBC"
                    ],
                    options_hi: [
                        " DACB",
                        " ABCD",
                        " DCAB",
                        " ADBC"
                    ],
                    solution_en: " 82.(c) DCAB<br />Sentence D will be the starting line as it contains the main idea of the parajumble i.e. Fire may have puzzled early men because they didn’t have knowledge about it. Sentence C states that but now we know it is the result of a chemical reaction  . So, C will follow D. Further, Sentence A states the process by which the reaction takes place  and Sentence B states that the energy which comes out in this process we call it fire. So, B will follow A.  Going through the options, option (c ) DCAB has the correct sequence.",
                    solution_hi: " 82.(c) DCAB<br />Sentence D प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है यानी आग ने शुरुआती लोगों को भ्रमित किया होगा क्योंकि उन्हें इसके बारे में ज्ञान नहीं था। Sentence C बताता है कि लेकिन अब हम जानते हैं कि यह एक रासायनिक प्रतिक्रिया का परिणाम है। तो, D के बाद C आएगा। आगे, sentence A उस प्रक्रिया को बताता है जिसके द्वारा प्रतिक्रिया होती है और sentence B कहता है कि इस प्रक्रिया में जो ऊर्जा निकलती है उसे हम आग कहते हैं। इसलिए, A  के बाद B आएगा। Option (c) DCAB में सही क्रम है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Pick a word opposite in meaning to<br>Though generous and <span style=\"text-decoration: underline;\"><strong>benevolent</strong></span>, he did not meet with success in his career.</p>",
                    question_hi: "<p>83. Pick a word opposite in meaning to<br>Though generous and <span style=\"text-decoration: underline;\"><strong>benevolent</strong></span>, he did not meet with success in his career.</p>",
                    options_en: [
                        "<p>beneficent</p>",
                        "<p>malevolent</p>",
                        "<p>goodwill</p>",
                        "<p>liberal</p>"
                    ],
                    options_hi: [
                        "<p>beneficent</p>",
                        "<p>malevolent</p>",
                        "<p>goodwill</p>",
                        "<p>liberal</p>"
                    ],
                    solution_en: "<p>83.(b) Malevolent<br><strong>Benevolent </strong>- well-meaning and kindly.<br><strong>Beneficent </strong>- (of a person) generous or doing good.<br><strong>Malevolent </strong>- having or showing a wish to do evil to others.<br><strong>Goodwill </strong>- friendly, helpful, or cooperative feelings or attitude.<br><strong>Liberal </strong>- willing to respect or accept behaviour or opinions different from one\'s own; open to new ideas.</p>",
                    solution_hi: "<p>83.(b) Malevolent<br><strong>Benevolent </strong>- दयालू<br><strong>Beneficent </strong>- परोपकारी<br><strong>Malevolent </strong>- द्रोही<br><strong>Goodwill </strong>- ख्याति<br><strong>Liberal </strong>- उदारवादी</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "84. Find the correctly spelt word",
                    question_hi: "84. Find the correctly spelt word",
                    options_en: [
                        " consensus",
                        " concensus",
                        " consencus          ",
                        " consenssus"
                    ],
                    options_hi: [
                        " consensus",
                        " concensus",
                        " consencus          ",
                        " consenssus"
                    ],
                    solution_en: "84.(a) ‘Consensus’ is the correct spelling.",
                    solution_hi: "84.(a) ‘Consensus’ सही spelling है।  ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate meaning of the given idiom.<br>Sacred cow</p>",
                    question_hi: "<p>85. Select the most appropriate meaning of the given idiom.<br>Sacred cow</p>",
                    options_en: [
                        "<p>Unquestionable</p>",
                        "<p>Interrogate</p>",
                        "<p>Hateful</p>",
                        "<p>Harmful</p>"
                    ],
                    options_hi: [
                        "<p>Unquestionable</p>",
                        "<p>Interrogate</p>",
                        "<p>Hateful</p>",
                        "<p>Harmful</p>"
                    ],
                    solution_en: "<p>85.(a) <strong>Sacred cow</strong> - unquestionable.<br>E.g.- In their family, the old traditions are treated like a sacred cow.</p>",
                    solution_hi: "<p>85.(a) <strong>Sacred cow</strong> - unquestionable./असंदिग्ध<br>E.g.- In their family, the old traditions are treated like a sacred cow.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that expresses the given sentence in active voice. <br>He will be made President by the people.</p>",
                    question_hi: "<p>86. Select the option that expresses the given sentence in active voice. <br>He will be made President by the people.</p>",
                    options_en: [
                        "<p>People will be making him the President</p>",
                        "<p>The people will make him President</p>",
                        "<p>By the people, he shall be made the President.</p>",
                        "<p>He will be made President by the people.</p>"
                    ],
                    options_hi: [
                        "<p>People will be making him the President</p>",
                        "<p>The people will make him President</p>",
                        "<p>By the people, he shall be made the President.</p>",
                        "<p>He will be made President by the people.</p>"
                    ],
                    solution_en: "<p>86.(b) The people will make him President. (Correct)<br>(a) People <span style=\"text-decoration: underline;\">will be making</span> him the President. (Incorrect Verb)<br>(c) By the people, he shall be made the President. (Incorrect Sentence Structure)<br>(d) He will be made President by the people. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>86.(b) The people will make him President. (Correct)<br>(a) People <span style=\"text-decoration: underline;\">will be making</span> him the President. (गलत Verb)<br>(c) By the people, he shall be made the President. (गलत Sentence Structure)<br>(d) He will be made President by the people. (गलत Sentence Structure)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the idiom (in the context).<br>He decided to <strong><span style=\"text-decoration: underline;\">bury the hatchet</span></strong>.</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the idiom (in the context).<br>He decided to <span style=\"text-decoration: underline;\"><strong>bury the hatchet</strong></span>.</p>",
                    options_en: [
                        "<p>to keep a secret</p>",
                        "<p>to make peace</p>",
                        "<p>to fool someone</p>",
                        "<p>to bury the wealth</p>"
                    ],
                    options_hi: [
                        "<p>to keep a secret</p>",
                        "<p>to make peace</p>",
                        "<p>to fool someone</p>",
                        "<p>to bury the wealth</p>"
                    ],
                    solution_en: "<p>87.(b) Idiom <strong>bury the hatchet/bury your difference</strong> means : to stop being unfriendly and become friends again ( Hatchet is a small weapon)</p>",
                    solution_hi: "<p>87.(b) Idiom <strong>bury the hatchet/bury your difference</strong> means : to stop being unfriendly and become friends again ( hatchet is a small weapon) / मुहावरा <strong>bury the hatchet/bury your difference</strong> का अर्थ है: फिर से दोस्त बनना (hatchet एक छोटा हथियार है)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) During this time she went around the earth 252 times, travelling 10.45 million kilometres!<br />(B) Kalpana’s first space mission in the space shuttle, Columbia, was 15 days, 16 hours and 34 minutes long. <br />(C )The crew included a Japanese and a Ukranian astronaut.<br />(D)The crew performed experiments such as pollinating plants to observe food growth in space, - all for a price tag of about 56 million dollars.",
                    question_hi: "88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br />(A) During this time she went around the earth 252 times, travelling 10.45 million kilometres!<br />(B) Kalpana’s first space mission in the space shuttle, Columbia, was 15 days, 16 hours and 34 minutes long. <br />(C )The crew included a Japanese and a Ukranian astronaut.<br />(D)The crew performed experiments such as pollinating plants to observe food growth in space, - all for a price tag of about 56 million dollars.",
                    options_en: [
                        " DCBA",
                        " BCDA",
                        " BACD",
                        " DBCA"
                    ],
                    options_hi: [
                        " DCBA",
                        " BCDA",
                        " BACD",
                        " DBCA"
                    ],
                    solution_en: "88.(c) BACD<br />Sentence B will be the starting line as it contains the main idea of the parajumble i.e. time taken in Kalpana’s first space mission in space. Sentence A states that she went around the earth 252 times in this time period . So, A will follow B. Further, Sentence C states that there were a Japanese and Ukrainian astronaut in the crew and Sentence D states that the crew performed many  experiments in space . So, D will follow C . Going through the options, option (c ) BACD has the correct sequence.",
                    solution_hi: "88.(c) BACD<br />Sentence B प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है यानी अंतरिक्ष में कल्पना के पहले अंतरिक्ष मिशन में लगने वाला समय। Sentence A कहता है कि वह इस समय अवधि में 252 बार पृथ्वी की परिक्रमा कर चुकी है। तो B के बाद A आएगा।  आगे, sentence C कहता है कि चालक दल में एक Japanese और Ukrainian अंतरिक्ष यात्री थे और sentence D कहता है कि चालक दल ने अंतरिक्ष में कई प्रयोग किए। अतः C  के बाद D आएगा।   Options को देखते हुए, option (c) BACD में सही क्रम है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the word which means the same as the group of words given.<br>A group of wolves</p>",
                    question_hi: "<p>89. Select the word which means the same as the group of words given.<br>A group of wolves</p>",
                    options_en: [
                        "<p>Pack</p>",
                        "<p>Flock</p>",
                        "<p>Troop</p>",
                        "<p>Tribe</p>"
                    ],
                    options_hi: [
                        "<p>Pack</p>",
                        "<p>Flock</p>",
                        "<p>Troop</p>",
                        "<p>Tribe</p>"
                    ],
                    solution_en: "<p>89.(a) Pack<br><strong>Pack</strong>- A group of wolves <br><strong>Flock</strong>- a group of sheep or birds<br><strong>Troop</strong>- a large group of people or animals <br><strong>Tribe</strong>- a group of people that have the same language and customs and that have a leader</p>",
                    solution_hi: "<p>89.(a) Pack<br><strong>Pack( झुण्ड)</strong>- A group of wolves <br><strong>Flock(झुंड)-</strong> a group of sheep or birds<br><strong>Troop(सेना)-</strong> a large group of people or animals <br><strong>Tribe(जनजाति)</strong>- a group of people that have the same language and customs and that have a leader</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br />Belief that war and violence are unjustified",
                    question_hi: "90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br />Belief that war and violence are unjustified",
                    options_en: [
                        " Neutralisation   ",
                        " Pessimism",
                        " Naturalisation   ",
                        " Pacifism"
                    ],
                    options_hi: [
                        " Neutralisation   ",
                        " Pessimism",
                        " Naturalisation   ",
                        " Pacifism"
                    ],
                    solution_en: "90.(d) Pacifism- the belief that war and violence are unjustifiable and that all disputes should be settled by peaceful means.<br />Neutralization- make (something) ineffective by applying an opposite force or effect.<br />Pessimism- a tendency to see the worst aspect of things or believe that the worst will happen<br />Naturalization- the act of causing something to appear natural.",
                    solution_hi: "90.(d) Pacifism - यह विश्वास कि युद्ध और हिंसा अनुचित हैं और यह कि सभी विवादों को शांतिपूर्ण तरीकों से सुलझाया जाना चाहिए। <br />Neutralization - विपरीत बल या प्रभाव को लागू करके (कुछ) अप्रभावी बनाना। <br />Pessimism- चीजों के सबसे बुरे पहलू को देखने की प्रवृत्ति या उस पर विश्वास करना सबसे बुरा होगा। <br />Naturalization - किसी चीज़ को स्वाभाविक दिखाने की क्रिया।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "91. Select the most appropriate option to fill in the blank.<br />It is the ____________ to reach Delhi.",
                    question_hi: "91. Select the most appropriate option to fill in the blank.<br />It is the ____________ to reach Delhi.",
                    options_en: [
                        " quickest route ",
                        " fastest route ",
                        " strongest route ",
                        " lowest route"
                    ],
                    options_hi: [
                        " quickest route ",
                        " fastest route ",
                        " strongest route ",
                        " lowest route"
                    ],
                    solution_en: "91.(b) fastest route<br />‘Fastest route’ means the route that allows you to get to your destination in the shortest amount of time. The given sentence states that it is the fastest route to reach Delhi. Hence, ‘fastest route’ is the most appropriate answer.",
                    solution_hi: "91.(b) fastest route<br />‘Fastest route’ का अर्थ है वह मार्ग जो आपको कम से कम समय में अपने गंतव्य (destination) तक पहुँचने की अनुमति देता है। दिए गए sentence में कहा गया है कि यह दिल्ली पहुँचने का सबसे तेज़ मार्ग (fastest route) है। अतः, ‘fastest route’ सबसे उपयुक्त उत्तर है।                        ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>You may <span style=\"text-decoration: underline;\"><strong>sign on this paper.</strong></span></p>",
                    question_hi: "<p>92. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>You may <span style=\"text-decoration: underline;\"><strong>sign on this paper</strong></span>.</p>",
                    options_en: [
                        "<p>sign at this paper</p>",
                        "<p>sign this paper</p>",
                        "<p>sign upon this paper</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>sign at this paper</p>",
                        "<p>sign this paper</p>",
                        "<p>sign upon this paper</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>92.(b) sign this paper<br>&lsquo;Sign&rsquo; is a verb that means to put one&rsquo;s signature on something. With &lsquo;sign&rsquo; no preposition is&nbsp;used. Hence, the preposition &lsquo;on&lsquo; must be removed from the given sentence &amp; &lsquo;sign this paper&rsquo; <br>will become the most appropriate answer.</p>",
                    solution_hi: "<p>92.(b) sign this paper<br>&lsquo;Sign&rsquo; एक verb है जिसका अर्थ है किसी चीज पर अपना हस्ताक्षर करना। &lsquo;sign&rsquo; के साथ किसी preposition का प्रयोग नहीं किया जाता है। इसलिए, preposition &lsquo;on&lsquo; को दिए गए वाक्य से हटा दिया जाना चाहिए और &lsquo;sign this paper&rsquo; सबसे उपयुक्त उत्तर होगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate antonym of the given word.<br>Ransack</p>",
                    question_hi: "<p>93. Select the most appropriate antonym of the given word.<br>Ransack</p>",
                    options_en: [
                        "<p>Seize</p>",
                        "<p>Raid</p>",
                        "<p>Protect</p>",
                        "<p>Pilfer</p>"
                    ],
                    options_hi: [
                        "<p>Seize</p>",
                        "<p>Raid</p>",
                        "<p>Protect</p>",
                        "<p>Pilfer</p>"
                    ],
                    solution_en: "<p>93.(c) Protect <br><strong>Ransack</strong>- to search a place, making it untidy and causing damage<br><strong>Protect</strong>- to keep somebody/something safe<br><strong>Seize</strong>- to take hold of something suddenly and firmly<br><strong>Raid</strong>- a surprise visit by the police looking for criminals or illegal goods<br><strong>Pilfer</strong>- to steal something in small quantity or of little value</p>",
                    solution_hi: "<p>93.(c) Protect <br><strong>Ransack </strong>- सामान को हानि पहुँचाते हुए किसी स्&zwj;थान पर वस्&zwj;तु विशेष की खोजबीन करना। <br><strong>Protect</strong>- सुरक्षित रखना । <br><strong>Seize</strong>- किसी चीज को अचानक और मजबूती से पकड़ लेना। <br><strong>Raid</strong>- अपराधियों या अवैध सामानों की तलाश में पुलिस का अचानक निरीक्षण । <br><strong>Pilfer</strong>- कम मात्रा या कम मूल्य की कोई वस्तु चुराना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>My servant _______ with all my money.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>My servant _______ with all my money.</p>",
                    options_en: [
                        "<p>have escaped</p>",
                        "<p>was run away</p>",
                        "<p>has run off</p>",
                        "<p>running away</p>"
                    ],
                    options_hi: [
                        "<p>have escaped</p>",
                        "<p>was run away</p>",
                        "<p>has run off</p>",
                        "<p>running away</p>"
                    ],
                    solution_en: "<p>94.(c) <strong>Run off with something</strong> - It means to steal something and take it away.</p>",
                    solution_hi: "<p>94.(c) <strong>Run off with something</strong> - इसका अर्थ है किसी वस्तु को चुराकर ले जाना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate Synonym of the given word.<br>Paranoid</p>",
                    question_hi: "<p>95. Select the most appropriate Synonym of the given word.<br>Paranoid</p>",
                    options_en: [
                        "<p>Impeccable</p>",
                        "<p>Suspicious</p>",
                        "<p>Insignificant</p>",
                        "<p>muddled</p>"
                    ],
                    options_hi: [
                        "<p>Impeccable</p>",
                        "<p>Suspicious</p>",
                        "<p>Insignificant</p>",
                        "<p>muddled</p>"
                    ],
                    solution_en: "<p>95.(b) Suspicious<br><strong>Paranoid</strong>- feeling extremely nervous and worried because you believe that other people do not like you or are trying to harm you<br><strong>Suspicious</strong>- feeling that somebody has done something wrong, dishonest or illegal.<br><strong>Impeccable</strong>- without any mistakes or faults, perfect. <br><strong>Insignificant</strong>- of little value or importance. <br><strong>Muddled</strong>- Confused</p>",
                    solution_hi: "<p>95.(b) Suspicious<br><strong>Paranoid</strong>- अत्यधिक घबराहट और चिंता महसूस करना क्योंकि आप मानते हैं कि अन्य लोग आपको पसंद नहीं करते हैं या&nbsp;आपको नुकसान पहुँचाने की कोशिश कर रहे हैं<br><strong>Suspicious</strong>- यह महसूस करना कि किसी ने कुछ गलत, अवैध या बेईमानी वाला काम किया है।<br><strong>Impeccable</strong>- बिना किसी गलती या दोष के<br><strong>Insignificant</strong>- जिसका मूल्य या महत्व कम हो ।<br><strong>Muddled</strong>- अव्यवस्थित/दुविधा में</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-</strong><br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-</strong><br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: [
                        "<p>population</p>",
                        "<p>clan</p>",
                        "<p>natives</p>",
                        "<p>inhabitants</p>"
                    ],
                    options_hi: [
                        "<p>population</p>",
                        "<p>clan</p>",
                        "<p>natives</p>",
                        "<p>inhabitants</p>"
                    ],
                    solution_en: "<p>96.(a) Population. <br>The given passage talks about the population of Irrawaddy dolphins. Hence, &lsquo;population&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) Population. <br>दिया गया passage Irrawaddy dolphins की आबादी (population) के बारे में बात करता है। इसलिए, \'population\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-</strong><br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:-</strong><br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: [
                        "<p>unwarranted</p>",
                        "<p>illegal</p>",
                        "<p>illegitimate</p>",
                        "<p>unconstitutional</p>"
                    ],
                    options_hi: [
                        "<p>unwarranted</p>",
                        "<p>illegal</p>",
                        "<p>illegitimate</p>",
                        "<p>unconstitutional</p>"
                    ],
                    solution_en: "<p>97.(b) Illegal.<br>&lsquo;Illegal&rsquo; means not allowed by the law. The given passage states that the rise in the Irrawaddy dolphin can be attributed to the eviction of fish enclosures which are not allowed by the law. Hence, &lsquo;illegal&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) Illegal.<br>\'Illegal\' का अर्थ- गैरकानूनी है। दिए गए passage में कहा गया है कि इरावदी डॉल्फ़िन की आबादी में वृद्धि को मछली के बाड़ों की बेदखली के लिए जिम्मेदार ठहराया जा सकता है जिसकी कानून द्वारा अनुमति नहीं है। इसलिए, \'Illegal\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-</strong><br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-</strong><br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: [
                        "<p>trespass</p>",
                        "<p>confiscation</p>",
                        "<p>intervention</p>",
                        "<p>encroachment</p>"
                    ],
                    options_hi: [
                        "<p>trespass</p>",
                        "<p>confiscation</p>",
                        "<p>intervention</p>",
                        "<p>encroachment</p>"
                    ],
                    solution_en: "<p>98.(d) Encroachment.<br>&lsquo;Encroachment&rsquo; means intrusion on a person\'s territory, rights, etc. The given passage states that thousands of hectares of Chilika lake were made encroachment free so that the dolphins can move freely. Hence, &lsquo;encroachment&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) Encroachment.<br>\'Encroachment\' का अर्थ है किसी व्यक्ति के क्षेत्र, अधिकारों आदि पर घुसपैठ। दिए गए passage में कहा गया है कि हजारों hectares चिल्का झील को अतिक्रमण मुक्त कर दिया गया ताकि dolphins स्वतंत्र रूप से रह सकें। इसलिए, \'encroachment\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-</strong><br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. Cloze Test:-<br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: [
                        "<p>Whereas</p>",
                        "<p>Moreover</p>",
                        "<p>Nevertheless</p>",
                        "<p>However</p>"
                    ],
                    options_hi: [
                        "<p>Whereas</p>",
                        "<p>Moreover</p>",
                        "<p>Nevertheless</p>",
                        "<p>However</p>"
                    ],
                    solution_en: "<p>99.(b) Moreover.<br>&lsquo;Moreover&rsquo; is used for adding another fact &amp; it will perfectly fit in the given blank because it is used for adding another fact about COVID-19 lockdown in the given sentence. Hence, &lsquo;Moreover&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) Moreover.<br>\'Moreover\' का उपयोग एक और तथ्य को जोड़ने के लिए किया जाता है और यह दिए गए रिक्त स्थान में पूरी तरह से फिट होगा क्योंकि इसका उपयोग दिए गए वाक्य में COVID-19 lockdown के बारे में एक और तथ्य जोड़ने के लिए किया जाता है। इसलिए, \'moreover\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-</strong><br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-</strong><br>The rise in the Irrawaddy dolphin (96) ______ in Chilika can be attributed to the eviction of (97)______ fish enclosures. After thousands of hectares of Chilika lake were made (98) ______ free, Irrawaddy dolphins found unobstructed area for movement. (99) ______, due to the COVID-19 lockdown last year, there were comparatively fewer tourist boats on Chilika lake, which made it (100) ______ for dolphins to move from one part of the lake to another.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: [
                        "<p>conducive</p>",
                        "<p>hurtful</p>",
                        "<p>detrimental</p>",
                        "<p>disturbing</p>"
                    ],
                    options_hi: [
                        "<p>conducive</p>",
                        "<p>hurtful</p>",
                        "<p>detrimental</p>",
                        "<p>disturbing</p>"
                    ],
                    solution_en: "<p>100.(a) Conducive. <br>&lsquo;Conducive&rsquo; means helping or making something happen. The given passage states that fewer tourist boats on Chilika lake made it conducive for dolphins to move from one part of the lake to another. Hence, &lsquo;conducive&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(a) Conducive. <br>\'Conducive\' का अर्थ होता है- किसी काम को करने में मदद करना। दिए गए passage में कहा गया है कि चिल्का झील पर कम पर्यटक नौकाओं ने dolphins को झील के एक हिस्से से दूसरे हिस्से में जाने के लिए अनुकूल (conducive) बना दिया। इसलिए, \'conducive\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>