<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The maximum value of (2 sin <math display=\"inline\"><mi>&#952;</mi></math> + 3 cos &theta;) is:</p>",
                    question_hi: "<p>1. (2 sin <math display=\"inline\"><mi>&#952;</mi></math> + 3 cos &theta;) का अधिकतम मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>17</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>11</mn></msqrt></math></p>", 
                                "<p>9</p>", "<p><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>17</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>11</mn></msqrt></math></p>",
                                "<p>9</p>", "<p><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math></p>"],
                    solution_en: "<p>1.(d) Maximum value of a sin x + b cos x = <math display=\"inline\"><msqrt><mo>(</mo><msup><mrow><mi mathvariant=\"bold-italic\">a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">b</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo></msqrt></math> <br>maximum value of (2 sin <math display=\"inline\"><mi>&#952;</mi></math> + 3 cos &theta;) =<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mo>+</mo><mn>9</mn></msqrt></math> =&nbsp;<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>",
                    solution_hi: "<p>51.(d) a sin x + b cos x का अधिकतम मान = <math display=\"inline\"><msqrt><mo>(</mo><msup><mrow><mi mathvariant=\"bold-italic\">a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">b</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo></msqrt></math> <br>(2 sin <math display=\"inline\"><mi>&#952;</mi></math> + 3 cos &theta;) का अधिकतम मान = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mo>+</mo><mn>9</mn></msqrt></math> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The radius of the circle is 10 cm and the perpendicular distance from the chord to the centre is 6 cm. The length of the chord is:</p>",
                    question_hi: "<p>2. वृत्त की त्रिज्या 10 cm है और जीवा से केंद्र की लंबवत दूरी 6 cm है। जीवा की लंबाई कितनी है?</p>",
                    options_en: ["<p>16 cm</p>", "<p>12 cm</p>", 
                                "<p>14 cm</p>", "<p>18 cm</p>"],
                    options_hi: ["<p>16 cm</p>", "<p>12 cm</p>",
                                "<p>14 cm</p>", "<p>18 cm</p>"],
                    solution_en: "<p>2.(a)</p>\n<p><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfqehkReezllWT-3mauOjaOYbfSx1Qvc7KRDRdrILPjhAqkcFp3KTz1GWjWad-VsrifJzIS9iF9bCoNJArsi6eRKC2SvgGWn8uJjkW1Ag-3814Ft6oUuWa6gZPtZ0fWmB2095Id6A?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"174\" height=\"168\"><br>By using triplet (6, 8 , 10) , we can say this PB = 8<br>Length of chord AB = 2 &times; 8 = 16 cm</p>",
                    solution_hi: "<p>2.(a) <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfqehkReezllWT-3mauOjaOYbfSx1Qvc7KRDRdrILPjhAqkcFp3KTz1GWjWad-VsrifJzIS9iF9bCoNJArsi6eRKC2SvgGWn8uJjkW1Ag-3814Ft6oUuWa6gZPtZ0fWmB2095Id6A?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"174\" height=\"168\"><br>त्रिक (6, 8, 10) का प्रयोग करके हम यह कह सकते हैं कि PB = 8<br>जीवा AB की लंबाई = 2 &times; 8 = 16 सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. Simplify the following expression<br />25 <math display=\"inline\"><mo>÷</mo></math> 5 + 3 of 2 - 4 + 6 of 3 ÷ 2 - 8 + 4",
                    question_hi: "3. निम्नलिखित व्यंजक का मान ज्ञात कीजिए।<br />25 <math display=\"inline\"><mo>÷</mo></math> 5 + 3 का 2 - 4 + 6 का 3 ÷ 2 - 8 + 4",
                    options_en: [" 16", " 4", 
                                " 8", " 12"],
                    options_hi: [" 16", " 4",
                                " 8", " 12"],
                    solution_en: "3.(d) 25 <math display=\"inline\"><mo>÷</mo></math> 5 + 3 of 2 - 4 + 6 of 3 ÷ 2 - 8 + 4<br />             = 5 + 6 - 4 + 18 <math display=\"inline\"><mo>÷</mo></math> 2 -  8 + 4 <br />             = 7 +  9 - 8 + 4 <br />             = 12",
                    solution_hi: "3.(d) 25 <math display=\"inline\"><mo>÷</mo></math> 5 + 3 का 2 - 4 + 6 का 3 ÷ 2 - 8 + 4<br />             = 5 + 6 - 4 + 18 <math display=\"inline\"><mo>÷</mo></math> 2 -  8 + 4 <br />             = 7 +  9 - 8 + 4 <br />             = 12",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If (x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = 7, x &gt; 0. The positive value of (x -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>) is :</p>",
                    question_hi: "<p>4. यदि (x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = 7, x &gt; 0 है। (x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>) का धनात्मक मान कितना है?</p>",
                    options_en: ["<p>5<math display=\"inline\"><msqrt><mn>4</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>5</mn><msqrt><mn>5</mn></msqrt></math></p>", 
                                "<p>3<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p>4<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    options_hi: ["<p>5<math display=\"inline\"><msqrt><mn>4</mn></msqrt></math></p>", "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>",
                                "<p>3<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p>4<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    solution_en: "<p>4.(c) (x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = 7<br>(x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>-</mo><mn>4</mn></msqrt></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>45</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msqrt><mn>5</mn></msqrt></math></p>",
                    solution_hi: "<p>4.(c) (x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = 7<br>(x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn><mo>-</mo><mn>4</mn></msqrt></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>45</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msqrt><mn>5</mn></msqrt></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A shopkeeper sells rice at the rate of ₹44 per kg whose cost price is ₹40 per kg. Not satisfied with this, he tries to increase his profit by removing 200 grams of rice from each packet. What is the shopkeeper&rsquo;s overall gain percentage?</p>",
                    question_hi: "<p>5. एक दुकानदार ₹44 प्रति kg की दर से चावल बेचता है, जिसका क्रय मूल्य ₹40 प्रति kg है। इससे संतुष्ट नहीं होने पर वह हर पैकेट से 200 ग्राम चावल निकालकर अपना लाभ बढ़ाने की कोशिश करता है। दुकानदार के कुल लाभ प्रतिशत की गणना करें।</p>",
                    options_en: ["<p>35%</p>", "<p>37.5%</p>", 
                                "<p>37%</p>", "<p>35.5%</p>"],
                    options_hi: ["<p>35%</p>", "<p>37.5%</p>",
                                "<p>37%</p>", "<p>35.5%</p>"],
                    solution_en: "<p>5.(b) <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">S</mi><mi mathvariant=\"bold-italic\">P</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow><mrow><mi mathvariant=\"bold-italic\">C</mi><mi mathvariant=\"bold-italic\">P</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>800</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>32</mn></mfrac></math><br>overall gain percentage = <math display=\"inline\"><mfrac><mrow><mn>44</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> &times; 100 = 37.5%</p>",
                    solution_hi: "<p>5.(b) <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">S</mi><mi mathvariant=\"bold-italic\">P</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow><mrow><mi mathvariant=\"bold-italic\">C</mi><mi mathvariant=\"bold-italic\">P</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>800</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>32</mn></mfrac></math><br>कुल लाभ प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>44</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> &times; 100 = 37.5%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.The following bar graph shows the sales of a company (in₹ crore) in different years. Study the graph and answer the question. <br><strong id=\"docs-internal-guid-cefdc575-7fff-2a22-804e-4dc24027d612\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcXhbGC15_OqUOshiw5b8eDOgnNbGCSMNEtRsAZW5mTbvKYGU5BsBs6AxPaLhbVv-D6uxWnZE-ZuIhlv-Bx6Xh89c78RlbCdicFDcIanD_1AnkPa4DDCRmlSl2A24rxqpusnq1R?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"222\" height=\"147\"></strong><br>The mean of the highest and lowest sales (in₹ crore) is:</p>",
                    question_hi: "<p>6. निम्नलिखित बार ग्राफ दिए गए वर्षों में एक कंपनी द्वारा की गई बिक्री (₹ करोड़ में) को दर्शाता है। ग्राफ का अध्ययन करें और प्रश्न का उत्तर दें। <br><strong id=\"docs-internal-guid-e3d9fb09-7fff-8ef5-53c2-d222af47cebc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd3pMLE6mq3oV6tvwAe-U6L9cqCjbKEzukPwKh2nBIXUqQ66ylVs7givezIe_H24JuhdD90U53RTvuyW6gjdKmt6PF2tiT5URlrAtkRTM4kNguqsjO-I3EVLQ-B9tvsAy9kyMTEzA?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"246\" height=\"163\"></strong><br>अधिकतम और न्यूनतम बिक्री का माध्य (₹ करोड़ में) कितना है?</p>",
                    options_en: ["<p>326.7</p>", "<p>394.5</p>", 
                                "<p>484.2</p>", "<p>412.3</p>"],
                    options_hi: ["<p>326.7</p>", "<p>394.5</p>",
                                "<p>484.2</p>", "<p>412.3</p>"],
                    solution_en: "<p>6.(b) Mean of the highest and lowest sales = <math display=\"inline\"><mfrac><mrow><mn>584</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>205</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 394.5</p>",
                    solution_hi: "<p>6.(b) उच्चतम और न्यूनतम बिक्री का माध्य = <math display=\"inline\"><mfrac><mrow><mn>584</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>205</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 394.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "7. Three years ago, the age of a person was six times the age of his younger brother. Eight years later, the age of the person will be three years less than twice the age of his younger brother. Find the present ages of the person and his younger brother. ",
                    question_hi: "7. तीन वर्ष पहले, एक व्यक्ति की आयु उसके छोटे भाई की आयु की छह गुनी थी। आठ वर्ष बाद, व्यक्ति की आयु उसके छोटे भाई की आयु के दोगुने से तीन वर्ष कम होगी। व्यक्ति और उसके छोटे भाई की वर्तमान आयु ज्ञात करें।",
                    options_en: [" 15 years and 5 years ", " 5 years and 15 years ", 
                                " 12 years and 4 years  ", " 4 years and 12 years"],
                    options_hi: [" 15 वर्ष और 5 वर्ष  ", " 5 वर्ष और 15 वर्ष ",
                                " 12 वर्ष और 4 वर्ष  ", " 4 वर्ष और 12 वर्ष"],
                    solution_en: "7.(a)  Let present age of person = x year<br />                          Present age of his younger brother = y year<br />                   According to question , Three year ago<br />                   <math display=\"inline\"><mo>⇒</mo><mo>(</mo><mi>&nbsp;</mi><mi>x</mi><mo>-</mo><mi>&nbsp;</mi><mn>3</mn><mo>)</mo></math> = 6 (y  - 3 )<br />                  <math display=\"inline\"><mo>⇒</mo></math> x - 6y = -15 …. (i)<br />                  Eight year later , <br />                 <math display=\"inline\"><mo>⇒</mo></math> (x + 8 ) = 2 (y + 8) - 3<br />                 <math display=\"inline\"><mo>⇒</mo></math>x - 2y = 5 ….(ii)<br />                  After solving (i) and (ii) we get , <br />                  x = 15 years  and y = 5 years",
                    solution_hi: "7.(a)  <br />माना व्यक्ति की वर्तमान आयु = x वर्ष <br />उसके छोटे भाई की वर्तमान आयु = y वर्ष <br />प्रश्न के अनुसार, <br />तीन वर्ष पहले<br /><math display=\"inline\"><mo>⇒</mo><mo>(</mo><mi>&nbsp;</mi><mi>x</mi><mo>-</mo><mi>&nbsp;</mi><mn>3</mn><mo>)</mo></math> = 6 (y  - 3 )<br /><math display=\"inline\"><mo>⇒</mo></math> x - 6y = -15 …. (i)<br />  आठ साल बाद,<br /><math display=\"inline\"><mo>⇒</mo></math> (x + 8 ) = 2 (y + 8) - 3<br /><math display=\"inline\"><mo>⇒</mo></math>x - 2y = 5 ….(ii)<br />  (i) और (ii) को हल करने के बाद, <br />x = 15 वर्ष और y = 5 वर्ष",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. An article marked for ₹275 is sold at 5% discount. The seller still gains 4.5%. What did he pay for it?</p>",
                    question_hi: "<p>8. ₹275 अंकित मूल्य वाली एक वस्तु को 5% छूट पर बेचा जाता है। विक्रेता अभी भी 4.5% लाभ प्राप्त करता है। उसने इसके लिए कितना भुगतान किया था?</p>",
                    options_en: ["<p>₹250</p>", "<p>₹125</p>", 
                                "<p>₹200</p>", "<p>₹225</p>"],
                    options_hi: ["<p>₹250</p>", "<p>₹125</p>",
                                "<p>₹200</p>", "<p>₹225</p>"],
                    solution_en: "<p>8.(a) let the C.P. of article be 100 unit<br>104.5 unit = 275 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>1 unit = 275 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>104</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math><br>100 unit = 275 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>104</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 = ₹250</p>",
                    solution_hi: "<p>8.(a) माना वस्तु का क्रय मूल्य 100 इकाई है। <br>104.5 इकाई = 275 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>1 इकाई = 275 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>104</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math><br>100 इकाई = 275 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>104</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 = ₹250</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. PQ and RS are common tangents to two circles intersecting at A and B. A and B, when produced on both the sides, meet the tangents PQ and RS at X and Y, respectively. If AB = 3 cm and XY = 5 cm, then PQ is ________.</p>",
                    question_hi: "<p>9. PQ और RS, A और B पर प्रतिच्छेद करने वाली दो वृत्तों की उभयनिष्ठ स्पर्श रेखाएं हैं। A और B, दोनों ओर बढ़ाए जाने पर, स्पर्श रेखा PQ और RS से क्रमशः X और Y पर मिलते हैं। यदि AB = 3 cm और XY = 5 cm है, तो PQ _________ है।</p>",
                    options_en: ["<p>5 cm</p>", "<p>3 cm</p>", 
                                "<p>6 cm</p>", "<p>4 cm</p>"],
                    options_hi: ["<p>5 cm</p>", "<p>3 cm</p>",
                                "<p>6 cm</p>", "<p>4 cm</p>"],
                    solution_en: "<p>9.(d) <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfUk9UX47Fv4AAvn-VEqeokU3YtoZFYX315PJTcALtJTE5-FFlFS-n0AlFDcU1nnpG5SM3IFgdh2VgYKL4z6aqcF3KXZ2piFvMhaVjKaoeYdEacjchijbXKT4QxRm6y-Gudp8mbQg?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"161\" height=\"132\"><br>Given , AB = 3cm , and XY = 5 cm <br>We know that XA = XB <br>XY = XA + AB + BY <br>5 = 2XA + 3 <br>2XA = 2 , XA = 1 <br>XB = 1 + 3 = 4<br>We know that , <br><math display=\"inline\"><msup><mrow><mi>P</mi><mi>X</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = XA &times; XB<br><math display=\"inline\"><msup><mrow><mi>P</mi><mi>X</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 1 &times; 4 <br>PX = 2<br>Similarly XQ = 2 cm <br>PQ = PX + XQ = 2 + 2 = 4<br><strong>Short method</strong>:- <br>Given, AB = 3 cm, XY = 5 cm <br>We know,<br><math display=\"inline\"><msup><mrow><mi>P</mi><mi>Q</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = XY<sup>2 </sup>- AB<sup>2 </sup>= 5<sup>2</sup> - 3<sup>2</sup> = 25 - 9 = 16<br>PQ = 4 cm</p>",
                    solution_hi: "<p>9.(d) <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfUk9UX47Fv4AAvn-VEqeokU3YtoZFYX315PJTcALtJTE5-FFlFS-n0AlFDcU1nnpG5SM3IFgdh2VgYKL4z6aqcF3KXZ2piFvMhaVjKaoeYdEacjchijbXKT4QxRm6y-Gudp8mbQg?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"161\" height=\"132\"><br>दिया गया है, AB = 3cm , और XY = 5 cm <br>हम जानते हैं कि XA = XB <br>XY = XA + AB + BY <br>5 = 2XA + 3 <br>2XA = 2 , XA = 1 <br>XB = 1 + 3 = 4<br>हम जानते हैं कि , <br><math display=\"inline\"><msup><mrow><mi>P</mi><mi>X</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = XA &times; XB<br><math display=\"inline\"><msup><mrow><mi>P</mi><mi>X</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 1 &times; 4 <br>PX = 2<br>इसी प्रकार XQ = 2 cm <br>PQ = PX + XQ = 2 + 2 = 4<br><strong>शार्ट विधि</strong> :- <br>दिया है, AB = 3 cm, XY = 5 cm <br>हम जानते है,<br><math display=\"inline\"><msup><mrow><mi>P</mi><mi>Q</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = XY<sup>2</sup> - AB<sup>2 </sup>= 5<sup>2</sup> - 3<sup>2</sup> = 25 - 9 = 16<br>PQ = 4 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. At a school building, there is an overhead tank. To fill this tank 50 buckets of water are required. Assume that the capacity of the bucket is reduced to two-fifth of the present. How many buckets of water are required to fill the same tank?</p>",
                    question_hi: "<p>10. एक स्कूल बिल्डिंग में एक ओवरहेड टैंक है। इस टैंक को भरने के लिए 50 बाल्टी पानी की आवश्यकता होती है। मान लीजिए कि बाल्टी की क्षमता इसकी वर्तमान क्षमता का दो-पाँचवां भाग कर दी जाती है। तो उसी टैंक को भरने के लिए कितनी बाल्टी पानी की आवश्यकता होगी?</p>",
                    options_en: ["<p>62.5</p>", "<p>20</p>", 
                                "<p>125</p>", "<p>60</p>"],
                    options_hi: ["<p>62.5</p>", "<p>20</p>",
                                "<p>125</p>", "<p>60</p>"],
                    solution_en: "<p>10.(c) Let the initial capacity of bucket = 5 <br>After reducing two-fifth of the initial , the capacity of bucket = 2 <br>Total capacity of the tank = 5 &times; 50 = 250 <br>After reduction , required no of bucket to fill the same tank = <math display=\"inline\"><mfrac><mrow><mn>250</mn><mi>&#160;</mi></mrow><mrow><mi>&#160;</mi><mn>2</mn></mrow></mfrac></math> = 125</p>",
                    solution_hi: "<p>10.(c) <br>माना बाल्टी की प्रारंभिक क्षमता = 5 <br>प्रारंभिक का दो-पाँचवाँ हिस्सा कम करने के बाद, बाल्टी की क्षमता = 2 <br>टैंक की कुल क्षमता = 5 &times; 50 = 250 <br>कमी के बाद, उसी टंकी को भरने के लिए आवश्यक बाल्टी की संख्या = <math display=\"inline\"><mfrac><mrow><mn>250</mn><mi>&#160;</mi></mrow><mrow><mi>&#160;</mi><mn>2</mn></mrow></mfrac></math> = 125</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The bar graph given below shows the sales of ball pens (in thousand numbers) from six shops S1, S2, S3, S4, S5, and S6, during two consecutive years, 2011 and 2012.<br><strong id=\"docs-internal-guid-6989326d-7fff-0da3-5189-8862a310e1d8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdQRnRIgKQLt0kTcVrTbZwtJ57NBdk_tMSi6tWtLkbm4g66gHKmUGYwiZbxVXq1hjSL5bkwm5MjGF2G5eidWqknq51s9BxhovlFyaQHfqJn5Yw7HCljPJWXnE_8luiqZvHcKiNa?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"242\" height=\"156\"></strong><br>What\'s is the ratio of the total sales of shop S2 for both years to the total sales of Shop S5 for both years?</p>",
                    question_hi: "<p>11. नीचे दिया गया दंड आलेख दो क्रमिक वर्षों, 2011 और 2012 के दौरान छः दुकानों S1, S2, S3, S4, S5 ओर S6 से बॉल पेनों की बिक्री (हजार संख्या में) को दर्शाता है।<br><strong id=\"docs-internal-guid-7b0a34e4-7fff-6b9d-3ba5-7fdbb80265a5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcLXao4_FDlDqhzir6fkrlM-gqBXyBZXkJV2L0Y-5gmnziGw8s-iJagLwbjC121fbIuR-01Jth4yr513KYIrr50MrrvG7HaKe4tjBp_Lxky7XlEE4Nt_LERfiLHkoM99_5jkZZp?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"238\" height=\"154\"></strong><br>Sales of ball pens (in thousand numbers) = बॉल पेनों की बिक्री (हजार संख्या में)<br>Shops = दुकान<br>दोनों वर्षों में दुकान S2 की कुल बिक्री और दोनों वर्षों में दुकान S5 की कुल बिक्री का अनुपात क्या है?</p>",
                    options_en: ["<p>8 : 7</p>", "<p>7 : 5</p>", 
                                "<p>5 : 8</p>", "<p>3 : 7</p>"],
                    options_hi: ["<p>8 : 7</p>", "<p>7 : 5</p>",
                                "<p>5 : 8</p>", "<p>3 : 7</p>"],
                    solution_en: "<p>11.(a) Total sales of shop S2 for both the years = 70 + 90 = 160<br>Total sales of shop S5 for both the years = 80 + 60 = 140 <br>Required ratio : 160 : 140 = 8 : 7</p>",
                    solution_hi: "<p>11.(a) दोनों वर्षों के लिए दुकान S2 की कुल बिक्री = 70 + 90 = 160<br>दोनों वर्षों में दुकान S5 की कुल बिक्री = 80 + 60 = 140 <br>आवश्यक अनुपात : 160 : 140 = 8 : 7</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A and B can do a work in 12 days and 16 days, respectively. If they work together for 6 days, then the fraction of the work left is _________.</p>",
                    question_hi: "<p>12. A और B एक काम को क्रमशः 12 दिन और 16 दिन में कर सकते हैं। यदि वे 6 दिन तक एक साथ काम करते हैं, तो कितना काम शेष रहेगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdNgIcEGSR18eL2Pk9s_5KsuRyNpd98HsNlTDT7srePqiREBfbITGU0pvMILK9nHb9umMxRW03gXB38TjZOXT2N0gbKz338zzVpKFlrTEoDlCfgSr18Fi6WvG4s_X0jh1O1vFSvSg?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"148\" height=\"129\"><br>working together, work done in 6 days = 7 &times; 6 = 42 unit <br>Remaining work = <math display=\"inline\"><mfrac><mrow><mn>48</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>42</mn><mi>&#160;</mi></mrow><mrow><mn>48</mn></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math></p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXejRE0KkncAOXEWlw-rdSdzJJsnbmOeRyC6Z1WMelOEEP95OFas0FTwtgED0Ub4J-NfXkntTo6rbmtye6cmbQWgGXfHq_TRwvO7XiLx5m0fKqJsagK__Ejpx_SYvHT35GXABEiZ?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"129\" height=\"116\"><br>एक साथ कार्य करने पर, 6 दिनों में किया गया कार्य = 7 &times; 6 = 42 इकाई <br>शेष कार्य = <math display=\"inline\"><mfrac><mrow><mn>48</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>42</mn><mi>&#160;</mi></mrow><mrow><mn>48</mn></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A quadrilateral PQRS is inscribed in a circle of centre O, such that PQ is a diameter and &lt;PSR = 120&deg;. Find the value of &lt;QPR.&nbsp;</p>\n<p>&nbsp;</p>",
                    question_hi: "<p>13. एक चतुर्भुज PQRS, O केंद्र वाले वृत्त में इस प्रकार खींचा गया है कि PQ, व्यास है और &ang;PSR = 120&deg; है। &ang;QPR का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>30&deg;</p>", "<p>60&deg;</p>", 
                                "<p>40&deg;</p>", "<p>50&deg;</p>"],
                    options_hi: ["<p>30&deg;</p>", "<p>60&deg;</p>",
                                "<p>40&deg;</p>", "<p>50&deg;</p>"],
                    solution_en: "<p>13.(a)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdVU0wJh0t-U9Sq5p35nTSuiEe7O7EB0JfNsrcz7HzwZaRm3UdKQmViy-yxpRKM9YtSlKLCBXagZx_OYt_dOSR_R0juZOsg8_KQf_wO18-ER0wTkVeKudrnDornd7Uxrw5ESUro?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"137\" height=\"106\"><br>We know that , &ang;PRQ = 90&deg;<br>Since , PQRS is cyclic quadrilateral , so &ang;PSR + &ang; RQP = 180&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> &ang; RQP = 180&deg; - 120&deg; = 60&deg;<br>In <math display=\"inline\"><mi>&#916;</mi></math> PRQ <br>&ang;P + &ang;Q + &ang;R = 180&deg;<br>&ang;P = 180&deg; - 90&deg; -60&deg;<br>&ang;P = 30&deg;<br>So, &ang;QPR = 30&deg;</p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdVU0wJh0t-U9Sq5p35nTSuiEe7O7EB0JfNsrcz7HzwZaRm3UdKQmViy-yxpRKM9YtSlKLCBXagZx_OYt_dOSR_R0juZOsg8_KQf_wO18-ER0wTkVeKudrnDornd7Uxrw5ESUro?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"137\" height=\"106\"><br>हम जानते हैं कि, &ang;PRQ = 90&deg;<br>चूँकि, PQRS चक्रीय चतुर्भुज है, इसलिए<br>&ang;PSR + &ang; RQP = 180&deg; <br><math display=\"inline\"><mo>&#8658;</mo></math> &ang; RQP = 180&deg; - 120&deg; = 60&deg;<br><math display=\"inline\"><mi>&#916;</mi></math> PRQ में <br>&ang;P + &ang;Q + &ang;R = 180&deg;<br>&ang;P = 180&deg; - 90&deg; -60&deg;<br>&ang;P = 30&deg;<br>So, &ang;QPR = 30&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The base radius of a circular cylinder is 10.5 cm. If the area of its curved surface is 792 cm<sup>2</sup> , then what is the volume of the cylinder?</p>",
                    question_hi: "<p>14. एक वृत्ताकार बेलन की आधार त्रिज्या 10.5 cm है। यदि इसके वक्र पृष्ठ का क्षेत्रफल 792 cm<sup>2</sup> है, तो बेलन का आयतन क्या है?</p>",
                    options_en: ["<p>4185 cm<sup>3</sup></p>", "<p>4518 cm<sup>3</sup></p>", 
                                "<p>4152 cm<sup>3</sup></p>", "<p>4158 cm<sup>3</sup></p>"],
                    options_hi: ["<p>4185 cm<sup>3</sup></p>", "<p>4518 cm<sup>3</sup></p>",
                                "<p>4152 cm<sup>3</sup></p>", "<p>4158 cm<sup>3</sup></p>"],
                    solution_en: "<p>14.(d) Base radius = 10.5cm <br>Curved surface area of cylinder (2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>h</mi><mo>)</mo></math> = 792cm<sup>2</sup><br>h = <math display=\"inline\"><mfrac><mrow><mn>792</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>22</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>105</mn></mrow></mfrac></math> = 12cm<br>volume of the cylinder = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math><br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mi>&#160;</mi><mo>&#215;</mo><mn>10</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>12</mn></math><br>= 4158cm<sup>3</sup></p>",
                    solution_hi: "<p>14.(d) आधार की त्रिज्या = 10.5cm <br>बेलन का वक्र पृष्ठीय क्षेत्रफल (2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>h</mi><mo>)</mo></math> = 792 सेमी<sup>2</sup><br>h = <math display=\"inline\"><mfrac><mrow><mn>792</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>22</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>105</mn></mrow></mfrac></math> = 12 सेमी<br>बेलन का आयतन = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math></p>\n<p>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times;10.5 &times; 10.5 &times; 12<br>= 4158cm<sup>3</sup></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. The value of the expression <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mn>2</mn><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mn>2</mn><mi>t</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math>&nbsp; is</p>",
                    question_hi: "<p>15. व्यंजक <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mn>2</mn><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>+</mo><mi>s</mi><mi>i</mi><mi>n</mi><mo>(</mo><mn>2</mn><mi>t</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math>&nbsp; का मान ___________ है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math></p>"],
                    solution_en: "<p>15.(b)<br><strong>Identity used:</strong><br><math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></math> = 2sin A cosA and sin<sup>2</sup>A + cos<sup>2</sup>A = 1<br>Now,<br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi></mrow><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi></mrow></mfrac></math> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math>&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mo>&#160;</mo><mi>cos</mi><mo>&#160;</mo><mi>t</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mo>&#160;</mo><mi>t</mi><mo>&#160;</mo><mo>)</mo></mrow><mrow><mo>&#160;</mo><mo>(</mo><mi>cos</mi><mo>&#160;</mo><mi>t</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>&#160;</mo><mi>t</mi><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo></mrow></mfrac></math><br>On dividing numerator and denominator by <math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi></math> we get,<br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mn>1</mn><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math></p>",
                    solution_hi: "<p>15.(b)<br><strong>प्रयुक्त सूत्र :</strong><br><math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mn>2</mn><mi>A</mi></math> = 2sin A cosA and sin<sup>2</sup>A + cos<sup>2</sup>A = 1<br>अब <br>= <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi></mrow><mrow><mi>c</mi><mi>o</mi><msup><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>t</mi><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow><mrow><mi>cos</mi><mo>(</mo><mi>t</mi><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mo>(</mo><mi>t</mi><mo>)</mo></mrow></mfrac></math>) =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mo>&#160;</mo><mi>cos</mi><mo>&#160;</mo><mi>t</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>sin</mi><mo>&#160;</mo><mi>t</mi><mo>&#160;</mo><mo>)</mo></mrow><mrow><mo>&#160;</mo><mo>(</mo><mi>cos</mi><mo>&#160;</mo><mi>t</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>sin</mi><mo>&#160;</mo><mi>t</mi><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo></mrow></mfrac></math><br>अंश और हर को <math display=\"inline\"><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>t</mi></math> से विभाजित करने पर हमें प्राप्त होता है,<br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mn>1</mn><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>t</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "16. The sum of the ages of 6 children born at the interval of two years each is 90 years. What is the age (in years) of the eldest child? ",
                    question_hi: "16. दो वर्षों के अंतराल पर पैदा हुए 6 बच्चों की आयु का योग 90 वर्ष है। सबसे बड़े बच्चे की आयु (वर्ष में) क्या है? ",
                    options_en: [" 14", " 20", 
                                " 16", " 10"],
                    options_hi: [" 14", " 20",
                                " 16", " 10"],
                    solution_en: "16.(b) Let the ages of six children are , (x - 4 ) , (x  - 2 ) , x , (x + 2 ) , (x + 4 ) , ( x + 6 )<br />                  According to question , <br />                 <math display=\"inline\"><mo>⇒</mo></math> x - 4  +  x  - 2  + x + x + 2  + x + 4 +  x + 6 = 90<br />                <math display=\"inline\"><mo>⇒</mo></math>  6x + 6 = 90 <br />                <math display=\"inline\"><mo>⇒</mo></math>  6x = 90 - 6 = 84 <br />                <math display=\"inline\"><mo>⇒</mo></math>   x = 14<br />               Age of the eldest child = 14 + 6 = 20",
                    solution_hi: "16.(b) माना छह बच्चों की उम्र क्रमश: =  (x - 4 ) , (x  - 2 ) , x , (x + 2 ) , (x + 4 ) , ( x + 6 )<br />                  प्रश्न के अनुसार, <br />               <math display=\"inline\"><mo>⇒</mo></math> x - 4  +  x  - 2  + x + x + 2  + x + 4 +  x + 6 = 90<br />                <math display=\"inline\"><mo>⇒</mo></math>  6x + 6 = 90 <br />                <math display=\"inline\"><mo>⇒</mo></math>  6x = 90 - 6 = 84 <br />                <math display=\"inline\"><mo>⇒</mo></math>   x = 14<br />               सबसे बड़े बच्चे की आयु = 14 + 6 = 20",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. A sum of ₹1,456, when invested for ten years, amounts to ₹2,366 on maturity. Find the rate of simple interest per annum that was to be paid on the sum invested.</p>",
                    question_hi: "<p>17. ₹1,456 की राशि, जब दस वर्षों के लिए निवेश की जाती है, तो यह परिपक्वता पर ₹2,366 हो जाती है। साधारण ब्याज की वार्षिक दर ज्ञात कीजिए जिसे निवेश की गई राशि पर भुगतान किया जाना था।</p>",
                    options_en: ["<p>5.75%</p>", "<p>6.5%</p>", 
                                "<p>6.25%</p>", "<p>6.15%</p>"],
                    options_hi: ["<p>5.75%</p>", "<p>6.5%</p>",
                                "<p>6.25%</p>", "<p>6.15%</p>"],
                    solution_en: "<p>17.(c) <br><strong>S.I.</strong> = 2366 - 1456 = 910<br><strong>S.I.</strong> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>&#215;</mo><mi>r</mi><mo>&#215;</mo><mi>t</mi></mrow><mn>100</mn></mfrac></math><br>Rate = <math display=\"inline\"><mfrac><mrow><mn>910</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>1456</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow></mfrac></math><br>Rate = 6.25%</p>",
                    solution_hi: "<p>17.(c) <br><strong>साधारण ब्याज</strong> = 2366 - 1456 = 910<br><strong>साधारण ब्याज</strong> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mo>&#160;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi><mo mathvariant=\"bold\">&#160;</mo></mrow><mn>100</mn></mfrac></math><br>दर = <math display=\"inline\"><mfrac><mrow><mn>910</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>1456</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow></mfrac></math><br>दर = 6.25%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. Study the given bar graph and answer the question that follows.<br>The following bar graph shows the quarterly profit (in lakhs) of a departmental store from 1997 to 2000.<br><strong id=\"docs-internal-guid-7db5f2c7-7fff-e483-8066-088364419940\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfbEr4F0-de4fEQ2IkRIQcvq1Mpfo9pdsdfnTZ-KVrI1R7FcRiIqrvDtU4Wl8jumIpzJXJDpCNuF0TmGEK1DaRwmbvocEvmhXDd7LlmDksmvn22a6793-PkEEfQ0PC1sXN35rh09g?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"254\" height=\"153\"></strong><br>What was the percentage increase in annual profit of the departmental store from 1997 to 2000?</p>",
                    question_hi: "<p>18. दिए गए दंड आलेख का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>नीचे दिए गए दंडआलेख में एक डिपार्टमेंटल स्टोर के वर्ष 1997 से 2000 तक के तिमाही लाभ (लाख में) को दिखाया गया है।<br><strong id=\"docs-internal-guid-62cbc46d-7fff-54e6-d684-1c66550f7c24\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeeESjI6J4mWC05v6bnrkpsOSol4muthn-C3fYL5EBCFjpkAHdMOw41qCPcoFI-MWjl8SDQZ697QS-XNCxHu7vOu5skKfqHdjLSlMAFnro2j_VB5OL7b0mu-ZdY-9PFI7hWFvj2sw?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"261\" height=\"157\"></strong><br>वर्ष 1997 से 2000 तक डिपार्टमेंटल स्टोर के वार्षिक लाभ में कितने प्रतिशत की वृद्धि हुई?</p>",
                    options_en: ["<p>85<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>", "<p>84<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>", 
                                "<p>87<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>", "<p>83<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>85<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>", "<p>84<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>",
                                "<p>87<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>", "<p>83<math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>18.(c) Annual profit in 1997 = 135 + 115 + 90 + 70 = 410 <br>Annual profit in 2000 = 150 + 180 + 210 + 230 = 770<br>percentage increase in annual profit of the departmental store from 1997 to 2000 <br>= <math display=\"inline\"><mfrac><mrow><mn>770</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>410</mn></mrow><mrow><mn>410</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>3600</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>87</mn><mfrac><mn>33</mn><mn>41</mn></mfrac></math>%</p>",
                    solution_hi: "<p>18.(c) 1997 में वार्षिक लाभ = 135 + 115 + 90 + 70 = 410 <br>2000 में वार्षिक लाभ = 150 + 180 + 210 + 230 = 770<br>1997 से 2000 तक डिपार्टमेंटल स्टोर के वार्षिक लाभ में प्रतिशत वृद्धि<br>= <math display=\"inline\"><mfrac><mrow><mn>770</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>410</mn></mrow><mrow><mn>410</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>3600</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>87</mn><mfrac><mn>33</mn><mn>41</mn></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. Simplify [ 1- sin<sup>2</sup>32&deg; + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math> ].</p>",
                    question_hi: "<p>19. [ 1- sin<sup>2</sup>32&deg; + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math> ].को सरल कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>sin32&deg;</p>", 
                                "<p>1</p>", "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>sin32&deg;</p>",
                                "<p>1</p>", "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>19.(c) [ 1- sin<sup>2</sup>32&deg; + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math> ]<br>= <math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>32</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math> [ ∵ 1 - sin<sup>2</sup>A = cos<sup>2</sup>A, 1 + tan<sup>2</sup>A = sec<sup>2</sup>A]<br>= <math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>32</mn><mo>&#176;</mo></math> + cos <sup>2</sup>58&deg; <br>= <math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>32</mn><mo>&#176;</mo></math> + Sin <sup>2</sup>32&deg; <br>= 1</p>",
                    solution_hi: "<p>19.(c) [ 1- sin232&deg; + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math> ]<br>= <math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>32</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>s</mi><mi>e</mi><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>58</mn><mo>&#176;</mo></mrow></mfrac></math> [ ∵ 1 - sin<sup>2</sup>A = cos<sup>2</sup>A, 1 + tan<sup>2</sup>A = sec<sup>2</sup>A]<br>= <math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>32</mn><mo>&#176;</mo></math> + cos<sup> 2</sup>58&deg; <br>= <math display=\"inline\"><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>32</mn><mo>&#176;</mo></math> + Sin <sup>2</sup>32&deg; <br>= 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "20. The average score of a batsman in twenty matches is 15 and in twenty-five other matches is 24. Find the average score in all the forty-five matches.",
                    question_hi: "20. एक बल्लेबाज का बीस मैचों में औसत स्कोर 15 है और पच्चीस अन्य मैचों में औसत स्कोर 24 है। सभी पैंतालीस मैचों में औसत स्कोर ज्ञात कीजिए।",
                    options_en: [" 21 ", " 20", 
                                " 19.5", " 22"],
                    options_hi: [" 21 ", " 20",
                                " 19.5", " 22"],
                    solution_en: "20.(b) Total Score in twenty matches = 15 × 20 = 300<br />                    Total Score in twenty - five other matches = 24 × 25 = 600<br />                   average score in all the forty-five matches = <math display=\"inline\"><mfrac><mrow><mn>300</mn><mi>&nbsp;</mi><mo>+</mo><mi>&nbsp;</mi><mn>600</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 20",
                    solution_hi: "20.(b) बीस मैचों में कुल स्कोर = 15 × 20 = 300<br />                   पच्चीस अन्य मैचों में कुल स्कोर = 24 × 25 = 600<br />                   सभी पैंतालीस मैचों में औसत स्कोर = <math display=\"inline\"><mfrac><mrow><mn>300</mn><mi>&nbsp;</mi><mo>+</mo><mi>&nbsp;</mi><mn>600</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 20",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. The given pie chart shows the number of students admitted in to different classes of a school in the academic year 2022-23.<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcj9uSV4fOW2W-kzZl3NHtoYar1oXWgF3aRn8u0Kvs_OP2miMA-wXQmBoFLPy1A7Ny8Mg2MFBOG5volexNEdvr6z9RVUS-3G7aWoV-d8onq0xyA0BrdYqUMjYf9d5ghJyZtBJNNnQ?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"145\" height=\"148\"><br>How many students were more in Class 10 than in Class 12 if 600 students were admitted in to Class 8?</p>",
                    question_hi: "<p>21. दिया गया पाई चार्ट शैक्षणिक वर्ष 2022-23 में एक स्कूल की विभिन्न कक्षाओं में प्रवेश पाने वाले विद्यार्थियों की संख्या को दर्शाता है।<br><strong id=\"docs-internal-guid-5eba89b5-7fff-435c-8731-6a36eb1bf108\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeNN9vkPC8XSlA3US5eal3wtblHFB4B1Uqd-H-iE2AHtsoOp8UmqNqmsI4iJAlT6_jDKI6lOLkSBhQKYbO52qgfQ26LDlyObpWgt3k724UJi4pkoTG86EcGbtHwAIBlB-OWKoBezg?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"128\" height=\"131\"></strong><br>यदि कक्षा 8 में 600 विद्यार्थियों को प्रवेश दिया गया तो कक्षा 12 <br>की तुलना में कक्षा 10 में कितने विद्यार्थी अधिक थे?</p>",
                    options_en: ["<p>280</p>", "<p>298</p>", 
                                "<p>240</p>", "<p>295</p>"],
                    options_hi: ["<p>280</p>", "<p>298</p>",
                                "<p>240</p>", "<p>295</p>"],
                    solution_en: "<p>21.(a) According to question , <br><math display=\"inline\"><mo>&#8658;</mo></math> 15% = 600 <br><math display=\"inline\"><mo>&#8658;</mo></math> 1 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo><mo>(</mo><mn>25</mn><mi>%</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>18</mn><mi>%</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mn>7</mn><mi>%</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac></math>&times; 7 = 280</p>",
                    solution_hi: "<p>21.(a) प्रश्न के अनुसार, <br><math display=\"inline\"><mo>&#8658;</mo></math> 15% = 600 <br><math display=\"inline\"><mo>&#8658;</mo></math> 1 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo><mo>(</mo><mn>25</mn><mi>%</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>18</mn><mi>%</mi><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mn>7</mn><mi>%</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>15</mn></mfrac></math> &times; 7 = 280</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "22. A thief is spotted by a policeman from a distance of 200 metres. When the policeman starts the chase, the thief also starts running. If the speed of the thief is 9 km/h and that of the policeman 10 km/h, how far will the thief have to run before he is overtaken? ",
                    question_hi: "22. एक चोर को एक पुलिसकर्मी 200 मीटर की दूरी से देखता है। जैसे ही पुलिस कर्मी पीछा करना शुरू करता है, तो चोर भी भागने लगता है। यदि चोर की चाल 9 km/h हो और पुलिसकर्मी की चाल 10 km/h हो, तो चोर पकड़े जाने से पहले कितनी दूर भागेगा?",
                    options_en: [" 1.2 km ", " 1.4 km ", 
                                " 1.8 km ", " 1.6 km"],
                    options_hi: [" 1.2 km ", " 1.4 km ",
                                " 1.8 km ", " 1.6 km"],
                    solution_en: "22.(c) <br />Relative speed = 10 - 9 = 1 km/h = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> m/s<br />Time taken by policeman to catch the thief = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></mrow></mfrac></math> = 720 second<br />Distance travel by thief in 720 seconds = 720  × 9 <math display=\"inline\"><mo>×</mo><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> m =1800 m or 1.8 km",
                    solution_hi: "22.(c) <br />सापेक्ष गति = 10 - 9 = 1 किमी/घंटा = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> मीटर/सेकेंड<br />पुलिसकर्मी द्वारा चोर को पकड़ने में लगा समय  = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></mrow></mfrac></math> = 720 सेकंड<br />चोर द्वारा 720 सेकंड में तय की गई दूरी = 720  × 9 <math display=\"inline\"><mo>×</mo><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> मीटर  =1800 मीटर  or 1.8 किमी",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. From the given numbers A, B, C and D, which number is NOT divisible by 11? <br>A = 712712 <br>B = 177210 <br>C = 64614 <br>D = 756148</p>",
                    question_hi: "<p>23. दी गई संख्याओं A, B, C ओं और D में से कौन-सी संख्या 11 से विभाज्य नहीं है?<br>A = 712712 <br>B = 177210 <br>C = 64614 <br>D = 756148</p>",
                    options_en: ["<p>C</p>", "<p>D</p>", 
                                "<p>B</p>", "<p>A</p>"],
                    options_hi: ["<p>C</p>", "<p>D</p>",
                                "<p>B</p>", "<p>A</p>"],
                    solution_en: "<p>23.(b) Divisibility rule of 11 : Difference between sum of digit odd places and sum of digits at even places is either 0 or multiple of 11 <br>By checking all options one by one option (b) does not satisfy this condition <br>756148 = (7 + 6 + 4) - (5 + 1 + 8 ) <br>= 17 - 14 = 3 <math display=\"inline\"><mo>&#8800;</mo><mi>&#160;</mi><mn>0</mn><mi>&#160;</mi><mo>,</mo><mi>&#160;</mi><mi>M</mi><mi>u</mi><mi>l</mi><mi>t</mi><mi>i</mi><mi>p</mi><mi>l</mi><mi>e</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>11</mn><mi>&#160;</mi></math><br>&there4; Option (b) is not divisible by 11</p>",
                    solution_hi: "<p>23.(b) 11 का विभाज्यता नियम: विषम स्थानों के अंकों के योग और सम स्थानों के अंकों के योग के बीच का अंतर या तो 0 है या 11 का गुणक है <br>सभी विकल्पों को एक-एक करके जांचने पर विकल्प (b) इस शर्त को पूरा नहीं करता है <br>756148 = (7 + 6 + 4) - (5 + 1 + 8 ) <br>= 17 - 14 = 3 &ne; 0, 11 से विभाज्य<br>&there4; विकल्प (b) 11 से विभाज्य नहीं है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. The price of an item is increased twice successively. The final price is ₹2,295 and initial price was ₹1,000. The percentage increment the second time was twice the percentage increment the first time. By what percentage was the price increased the second time?</p>",
                    question_hi: "<p>24. किसी वस्तु की कीमत लगातार दो बार बढ़ाई जाती है। अंतिम कीमत ₹2,295 है और प्रारंभिक कीमत ₹1,000 थी। दूसरी बार प्रतिशत वृद्धि, पहली बार प्रतिशत वृद्धि से दोगुनी थी। दूसरी बार कीमत में कितने प्रतिशत की वृद्धि की गई?</p>",
                    options_en: ["<p>35%</p>", "<p>65%</p>", 
                                "<p>70%</p>", "<p>25%</p>"],
                    options_hi: ["<p>35%</p>", "<p>65%</p>",
                                "<p>70%</p>", "<p>25%</p>"],
                    solution_en: "<p>24.(c) <br>As we know , percentage increment the second time was twice the percentage increment the first time<br>By using hit and trial method , let first increment = 35%<br><math display=\"inline\"><mo>&#8658;</mo></math> 1000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>170</mn><mn>100</mn></mfrac></math> = 2295<br><math display=\"inline\"><mo>&#8658;</mo></math> 2295 = 2295 <br>&there4; This satisfy the condition , hence price increased in the second time = 70%</p>",
                    solution_hi: "<p>24.(c) <br>जैसा कि हम जानते हैं, दूसरी बार प्रतिशत वृद्धि पहली बार प्रतिशत वृद्धि से दोगुनी थी<br>हिट और ट्रायल विधि का उपयोग करके, मान लीजिए कि पहली वृद्धि = 35%<br><math display=\"inline\"><mo>&#8658;</mo></math> 1000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>170</mn><mn>100</mn></mfrac></math> = 2295<br><math display=\"inline\"><mo>&#8658;</mo></math> 2295 = 2295 <br>&there4; यह शर्त को पूरा करता है, इसलिए दूसरी बार कीमत में वृद्धि = 70%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. &Delta;ABC and &Delta;DEF are two triangles such that &Delta;ABC&cong;&Delta;FDE. If AB=17 cm, &ang;B=52&deg; and &ang;A=95&deg;, then which of the following options is true?</p>",
                    question_hi: "<p>25. &Delta;ABC और &Delta;DEF दो त्रिभुज इस प्रकार हैं कि &Delta;ABC&cong;&Delta;FDE है। यदि AB = 17 cm, &ang;B = 52&deg; और &ang;A = 95&deg; है, तो निम्नलिखित में से कौन-सा विकल्प सत्य है?</p>",
                    options_en: ["<p>DE=17 cm,&ang;F=33&deg;</p>", "<p>DF=17 cm,&ang;E=33&deg;</p>", 
                                "<p>DE=17 cm,&ang;E=33&deg;</p>", "<p>DF=17 cm,&ang;D=33&deg;</p>"],
                    options_hi: ["<p>DE=17 cm,&ang;F=33&deg;</p>", "<p>DF=17 cm,&ang;E=33&deg;</p>",
                                "<p>DE=17 cm,&ang;E=33&deg;</p>", "<p>DF=17 cm,&ang;D=33&deg;</p>"],
                    solution_en: "<p>25.(b) &Delta;ABC&cong;&Delta;FDE &there4; there corresponding angle and side are equa<strong id=\"docs-internal-guid-85c375a3-7fff-c511-5a0d-02931899f42c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcnaIf2dWze4RRj2okJoio4djzIcgaGfaZQpzUpRbkHXx6wfab_ipYQFBvLPrr1zGDG2P_JfDOuGejV0bN6btGSduBV05O6iTf0Bcvz6xEf1e19N9koTh-vLLyKm9SEddsAmkDb?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"232\" height=\"123\"></strong><br>&there4;&ang; C = 180&deg; - 95&deg; - 52&deg; = 33&deg;<br>Hence , Option (b) DF=17 cm,&ang;E=33&deg; is true .</p>",
                    solution_hi: "<p>25.(b) &Delta;ABC&cong;&Delta;FDE &there4; संगत कोण और भुजा बराबर हैं <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcnaIf2dWze4RRj2okJoio4djzIcgaGfaZQpzUpRbkHXx6wfab_ipYQFBvLPrr1zGDG2P_JfDOuGejV0bN6btGSduBV05O6iTf0Bcvz6xEf1e19N9koTh-vLLyKm9SEddsAmkDb?key=KpYzTKj1T7uNwqcyvOUHAwlX\" width=\"232\" height=\"123\"><br>&there4;&ang; C = 180&deg; - 95&deg; - 52&deg; = 33&deg;<br>इसलिए, विकल्प (b) DF=17 सेमी , &ang;E=33&deg; सत्य है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>