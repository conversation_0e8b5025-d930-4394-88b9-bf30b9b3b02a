<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">In the following number-pairs, the second number is obtained by applying certain mathematical operations to the first number. Select the set in which the numbers are related in the same way as are the numbers of the following sets.</span></p>\r\n<p><span style=\"font-weight: 400;\">(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(42, 370)</span></p>\r\n<p><span style=\"font-weight: 400;\">(57, 505)</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\"><span style=\"font-family: Cambria Math;\">1.&nbsp; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;-&#2351;&#2369;&#2327;&#2381;&#2350;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2346;&#2352; &#2325;&#2369;&#2331; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2354;&#2366;&#2327;&#2370; &#2325;&#2352;&#2325;&#2375; &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404; &#2313;&#2360; &#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2306; &#2332;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2350;&#2369;&#2330;&#2381;&#2330;&#2351;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2306;&#2404;</span></span></p>\r\n<p><span style=\"font-weight: 400;\">(&#2343;&#2381;&#2351;&#2366;&#2344; &#2342;&#2375;&#2306;: &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2379; &#2313;&#2344;&#2325;&#2375; &#2328;&#2335;&#2325; &#2309;&#2306;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306;&nbsp; &#2309;&#2354;&#2327; - &#2309;&#2354;&#2327; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366; &#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2306; &#2325;&#2368; &#2332;&#2366;&#2344;&#2368; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; 13 - &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 13 &#2346;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2306; &#2332;&#2376;&#2360;&#2375; &#2325;&#2367; 13 &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2344;&#2366;/&#2328;&#2335;&#2366;&#2344;&#2366;/&#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2344;&#2366; &#2310;&#2342;&#2367; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; 13 &#2325;&#2379; 1 &#2324;&#2352; 3 &#2350;&#2375;&#2306; &#2309;&#2354;&#2327; - &#2309;&#2354;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2324;&#2352; &#2347;&#2367;&#2352; 1 &#2324;&#2352; 3 &#2346;&#2352; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;)</span></p>\r\n<p><span style=\"font-weight: 400;\">(42, 370)</span></p>\r\n<p><span style=\"font-weight: 400;\">(57, 505)</span></p>\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">(29, 253)</span></p>\n", "<p><span style=\"font-weight: 400;\">(31, 288)</span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">(49, 449)</span></p>\n", "<p><span style=\"font-weight: 400;\">(84, 747)</span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(29, 253)</span></p>\n", "<p><span style=\"font-weight: 400;\">(31, 288)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">(49, 449)</span></p>\n", "<p><span style=\"font-weight: 400;\">(84, 747)</span></p>\n"],
                    solution_en: "<p>1.(a)<strong>Logic:</strong> (<span style=\"font-family: Cambria Math;\">1st num &times; 9) - 8 = 2nd num</span></p>\r\n<p>(42 , 370)&rarr; (42 &times; 9) - 8 &rArr; 378 - 8 = 370</p>\r\n<p>(57 , 505)&rarr; (57 &times; 9) - 8 &rArr; 513 - 8 = 505</p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, (29, 253) &rarr;(29 &times; 9) - 8 &rArr; 261 - 8 = 253</span></p>\n",
                    solution_hi: "<p>1.(a)<strong>&#2340;&#2352;&#2381;&#2325;:</strong> <span style=\"font-family: Cambria Math;\">(&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &times; 9) - 8 = &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span></p>\r\n<p>(42 , 370)&rarr; (42 &times; 9) - 8 &rArr; 378 - 8 = 370</p>\r\n<p>(57 , 505)&rarr; (57 &times; 9) - 8 &rArr; 513 - 8 = 505</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;, (29, 253) &rarr;(29 &times; 9) - 8 &rArr; 261 - 8 = 253</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: " <p>2. </span><span style=\"font-family:Cambria Math\">JOSV is related to LQUX in a certain way based on the English alphabetical order. In the same way, EJNQ is related to GLPS. Which of the following is DIMP related to, following the same logic ?</span></p>",
                    question_hi: " <p>2. </span><span style=\"font-family:Cambria Math\">अंग्रेजी वर्णमाला क्रम के आधार पर JOSV, LQUX से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, EJNQ, GLPS से संबंधित है। समान तर्क का अनुसरण करते हुए, DIMP निम्नलिखित में से किससे संबंधित है?</span></p>",
                    options_en: [" <p> FKOR</span></p>", " <p> FJMO</span></p>", 
                                " <p> FJNP</span></p>", " <p> HMQT </span></p>"],
                    options_hi: [" <p> FKOR</span></p>", " <p> FJMO</span></p>",
                                " <p> FJNP</span></p>", " <p> HMQT </span></p>"],
                    solution_en: " <p>2.(a)</span></p> <p><span style=\"font-family:Cambria Math\">              </span></p> <p><span style=\"font-family:Cambria Math\"> </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image1.png\"/></p> <p><span style=\"font-family:Cambria Math\">Similarly,</span></p> <p><span style=\"font-family:Cambria Math\">   </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image6.png\"/></p>",
                    solution_hi: " <p>2.(a)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image1.png\"/><span style=\"font-family:Cambria Math\">             </span></p> <p><span style=\"font-family:Cambria Math\">इसी प्रकार</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image6.png\"/></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">AHL is related to ELP in a certain way based on the English alphabetical order. In the same way, GMP is related to KQT. To which of the following is FQS related following the same logic?</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> &#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2357;&#2352;&#2381;&#2339;&#2350;&#2366;&#2354;&#2366; &#2325;&#2381;&#2352;&#2350; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; AHL, ELP &#2360;&#2375; &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;, GMP, KQT &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; &#2325;&#2366; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319;, FQS &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>JUW</p>\n", "<p>SFQ</p>\n", 
                                "<p>SQF</p>\n", "<p>ITV</p>\n"],
                    options_hi: ["<p>JUW</p>\n", "<p>SFQ</p>\n",
                                "<p>SQF</p>\n", "<p>ITV</p>\n"],
                    solution_en: "<p>3.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image2.png\" width=\"91\" height=\"102\">&nbsp; ,&nbsp; <img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image13.png\" width=\"86\" height=\"103\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image11.png\" width=\"79\" height=\"91\"></p>\n",
                    solution_hi: "<p>3.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image2.png\" width=\"87\" height=\"98\"> &nbsp;<span style=\"font-family: Cambria Math;\">,&nbsp; </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image13.png\" width=\"87\" height=\"105\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;,</span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image11.png\" width=\"79\" height=\"91\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: " <p>4. </span><span style=\"font-family:Cambria Math\">Select the triad from the below options that has the same analogy as the following triads.</span></p> <p><span style=\"font-family:Cambria Math\">6 : 36 : 216</span></p> <p><span style=\"font-family:Cambria Math\">17 : 289 : 4913</span></p>",
                    question_hi: " <p>4.</span><span style=\"font-family:Cambria Math\"> नीचे दिए गए विकल्पों में से उस त्रय को चुनिए जिसमें वही समानता है जो समानता निम्नलिखित त्रयों में है।</span></p> <p><span style=\"font-family:Cambria Math\">6 : 36 : 216 </span></p> <p><span style=\"font-family:Cambria Math\">17 : 289 : 4913</span></p>",
                    options_en: [" <p> 22 : 440 : 9000</span></p>", " <p> 21 : 400 : 9200</span></p>", 
                                " <p> 21 : 441 : 9261</span></p>", " <p> 22 : 400 : 9999</span></p>"],
                    options_hi: [" <p> 22 : 440 : 9000</span></p>", " <p> 21 : 400 : 9200</span></p>",
                                " <p> 21 : 441 : 9261</span></p>", " <p> 22 : 400 :  9999</span></p>"],
                    solution_en: " <p>4.(c)</span></p> <p><span style=\"font-family:Cambria Math\">Logic :- (1st number × 2nd number) = 3rd number</span></p> <p><span style=\"font-family:Cambria Math\">6 : 36 : 216 :- (6 × 36) = 216</span></p> <p><span style=\"font-family:Cambria Math\">17 : 289 : 4913 :-  (17 × 289) = 4913</span></p> <p><span style=\"font-family:Cambria Math\">Similarly,</span></p> <p><span style=\"font-family:Cambria Math\">21 : 441 : 9261 :- (21 × 441) = 9261</span></p>",
                    solution_hi: " <p>4.(c)</span></p> <p><span style=\"font-family:Cambria Math\">तर्क :- (पहली संख्या× दूसरी संख्या) = तीसरी संख्या</span></p> <p><span style=\"font-family:Cambria Math\">6 : 36 : 216 :- (6 × 36) = 216</span></p> <p><span style=\"font-family:Cambria Math\">17 : 289 : 4913 :-  (17 × 289) = 4913</span></p> <p><span style=\"font-family:Cambria Math\">इसी प्रकार,</span></p> <p><span style=\"font-family:Cambria Math\">21 : 441 : 9261 :- (21 × 441) = 9261</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: " <p>5. </span><span style=\"font-family:Cambria Math\">75 is related to 25 in a certain way. Following the same logic, 69 is related to 23. To which of the following is 93 related, following the same logic ?</span></p>",
                    question_hi: " <p>5. </span><span style=\"font-family:Cambria Math\">एक निश्चित तर्क के अनुसार 75, 25 से संबंधित है। समान तर्क के अनुसार, 69, 23 से संबंधित है। समान तर्क का अनुसरण करते हुए 93 निम्नलिखित में से किससे संबंधित है ? </span></p>",
                    options_en: [" <p> 31</span></p>", " <p> 39</span></p>", 
                                " <p> 30</span></p>", " <p> 38</span></p>"],
                    options_hi: [" <p> 31</span></p>", " <p> 39</span></p>",
                                " <p> 30</span></p>", " <p> 38</span></p>"],
                    solution_en: " <p>5.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Logic : </span><span style=\"font-family:Cambria Math\">1st number  ÷ 3 = 2nd number </span></p> <p><span style=\"font-family:Cambria Math\">75 </span><span style=\"font-family:Cambria Math\">÷ 3</span><span style=\"font-family:Cambria Math\"> = 25</span></p> <p><span style=\"font-family:Cambria Math\">69 </span><span style=\"font-family:Cambria Math\">÷ 3 </span><span style=\"font-family:Cambria Math\">= 23</span></p> <p><span style=\"font-family:Cambria Math\">similarly</span></p> <p><span style=\"font-family:Cambria Math\">93 </span><span style=\"font-family:Cambria Math\">÷ 3</span><span style=\"font-family:Cambria Math\"> = 31</span></p>",
                    solution_hi: " <p>5.(a)</span></p> <p><span style=\"font-family:Cambria Math\">तर्क : </span><span style=\"font-family:Cambria Math\">पहली संख्या  ÷ 3 = दूसरी संख्या </span></p> <p><span style=\"font-family:Cambria Math\">75 </span><span style=\"font-family:Cambria Math\">÷ 3</span><span style=\"font-family:Cambria Math\"> = 25</span></p> <p><span style=\"font-family:Cambria Math\">69 </span><span style=\"font-family:Cambria Math\">÷ 3 </span><span style=\"font-family:Cambria Math\">= 23</span></p> <p><span style=\"font-family:Cambria Math\">इसी प्रकार </span></p> <p><span style=\"font-family:Cambria Math\">93 </span><span style=\"font-family:Cambria Math\">÷ 3</span><span style=\"font-family:Cambria Math\"> = 31</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">DFCB is related to LNKJ in a certain way based on the English alphabetical order. In the same way, OQNM is related to WYVU. To which of the following is JLIH related to, following the same logic?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2357;&#2352;&#2381;&#2339;&#2350;&#2366;&#2354;&#2366; &#2325;&#2381;&#2352;&#2350; &#2350;&#2375;&#2306; &#2342;&#2367;&#2319; &#2327;&#2319; &#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2360;&#2306;&#2348;&#2306;&#2343; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; DFCB, LNKJ &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;, OQNM, WYVU &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319; JLIH &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p>RTSQ</p>\n", "<p>RTQP</p>\n", 
                                "<p>RQPT</p>\n", "<p>PRON</p>\n"],
                    options_hi: ["<p>RTSQ</p>\n", "<p>RTQP</p>\n",
                                "<p>RQPT</p>\n", "<p>PRON</p>\n"],
                    solution_en: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image4.png\" width=\"83\" height=\"76\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image3.png\" width=\"84\" height=\"76\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image7.png\" width=\"89\" height=\"81\"></p>\n",
                    solution_hi: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image4.png\" width=\"96\" height=\"88\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image3.png\" width=\"97\" height=\"87\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image7.png\" width=\"95\" height=\"87\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">EGDH is related to KMJN in a certain way based on the English alphabetical order. In the same way, QSPT is related to WYVZ. To which of the following is CEBF related to, following the same logic ?</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2357;&#2352;&#2381;&#2339;&#2350;&#2366;&#2354;&#2366; &#2325;&#2381;&#2352;&#2350; &#2350;&#2375;&#2306; &#2342;&#2367;&#2319; &#2327;&#2319; &#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2360;&#2306;&#2348;&#2306;&#2343; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; EGDH, KMJN &#2360;&#2375; &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;, QSPT, WYVZ &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319; CEBF &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>IKHL</p>\n", "<p>HKIL</p>\n", 
                                "<p>KMJN</p>\n", "<p>HGJK</p>\n"],
                    options_hi: ["<p>IKHL</p>\n", "<p>HKIL</p>\n",
                                "<p>KMJN</p>\n", "<p>HGJK</p>\n"],
                    solution_en: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image20.png\" width=\"91\" height=\"84\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image12.png\" width=\"96\" height=\"87\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image17.png\" width=\"92\" height=\"83\"></p>\n",
                    solution_hi: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image20.png\" width=\"106\" height=\"97\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image12.png\" width=\"102\" height=\"92\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image17.png\" width=\"106\" height=\"96\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: " <p>8. </span><span style=\"font-family:Cambria Math\">Select the triad in which the numbers are related in the same way as are the numbers of the following triads.</span></p> <p><span style=\"font-family:Cambria Math\">4-200-5</span></p> <p><span style=\"font-family:Cambria Math\">6-420-7</span></p>",
                    question_hi: " <p>8. </span><span style=\"font-family:Cambria Math\">उस त्रय को चुनिए जिसमें संख्याएँ ठीक उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित त्रयों की संख्याएँ संबंधित हैं।</span></p> <p><span style=\"font-family:Cambria Math\">4-200-5</span></p> <p><span style=\"font-family:Cambria Math\">6-420-7</span></p>",
                    options_en: [" <p> 3-660-9</span></p>", " <p> 7-780-8</span></p>", 
                                " <p> 8-720-9</span></p>", " <p> 4-400-7</span></p>"],
                    options_hi: [" <p> 3-660-9</span></p>", " <p> 7-780-8</span></p>",
                                " <p> 8-720-9</span></p>", " <p> 4-400-7</span></p>"],
                    solution_en: " <p>8.(c)</span></p> <p><span style=\"font-family:Cambria Math\">Logic : </span><span style=\"font-family:Cambria Math\">1st number  × 3rd number × 10 = 2nd number </span></p> <p><span style=\"font-family:Cambria Math\">4 × 5 × 10 = 200</span></p> <p><span style=\"font-family:Cambria Math\">6 × 7 × 10 = 420</span></p> <p><span style=\"font-family:Cambria Math\">similarly</span></p> <p><span style=\"font-family:Cambria Math\">8 × 9 × 10 = 720</span></p>",
                    solution_hi: " <p>8.(c)</span></p> <p><span style=\"font-family:Cambria Math\">तर्क : </span><span style=\"font-family:Cambria Math\">पहली संख्या  × तीसरी संख्या  × 10 = दूसरी संख्या </span></p> <p><span style=\"font-family:Cambria Math\">4 × 5 × 10 = 200</span></p> <p><span style=\"font-family:Cambria Math\">6 × 7 × 10 = 420</span></p> <p><span style=\"font-family:Cambria Math\">इसी प्रकार </span></p> <p><span style=\"font-family:Cambria Math\">8 × 9 × 10 = 720</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> DM 12 is related to HQ 17 in a certain way. In the same way, TS 19 is related to XW 24. To which of the following is KG 17 related following the same logic?</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">DM 12, HQ 17 &#2360;&#2375; &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;, TS 19, XW 24 &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; &#2325;&#2366; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319; KG 17 &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>MK 20</p>\n", "<p>OK 22</p>\n", 
                                "<p>NK 22</p>\n", "<p>NJ 21</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>MK 20</p>\n", "<p>OK 22</p>\n",
                                "<p>NK 22</p>\n", "<p>NJ 21</p>\n"],
                    solution_en: "<p>9.(b)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_98681152711698482852995.png\" width=\"192\" height=\"84\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image16.png\" width=\"116\" height=\"106\"></p>\n",
                    solution_hi: "<p>9.(b)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_98681152711698482852995.png\" width=\"192\" height=\"84\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; ,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image16.png\" width=\"116\" height=\"106\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">34 is related to 70 in a certain way. Following the same logic, 56 is related to 114. To which of the following is 23 related, following the same logic?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10.</span><span style=\"font-family: Cambria Math;\"> &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2340;&#2352;&#2381;&#2325; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; 34, 70 &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;, 56, 114 &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; &#2325;&#2366; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319; 23 &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>39</p>\n", "<p>52</p>\n", 
                                "<p>48</p>\n", "<p>47</p>\n"],
                    options_hi: ["<p>39</p>\n", "<p>52</p>\n",
                                "<p>48</p>\n", "<p>47</p>\n"],
                    solution_en: "<p>10.(c) Logic: (<span style=\"font-family: Cambria Math;\">1st num &times; 2) + 2 = 2nd num </span></p>\r\n<p>(34 - 70)&rarr; (34 &times; 2) + 2 &rArr; 68 + 2 = 70</p>\r\n<p>(56 - 112)&rarr;(56 &times; 2) + 2 &rArr; 112 + 2 = 114</p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, (23 - ?) &rarr;(23 &times; 2) + 2 &rArr; 46 + 2 = </span><span style=\"font-family: Cambria Math;\">48</span></p>\n",
                    solution_hi: "<p>10.(c)&#2340;&#2352;&#2381;&#2325;: <span style=\"font-family: Cambria Math;\">(&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &times; 2) + 2 = &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span></p>\r\n<p>(34 - 70)&rarr; (34 &times; 2) + 2 &rArr; 68 + 2 = 70</p>\r\n<p>(56 - 112)&rarr;(56 &times; 2) + 2 &rArr; 112 + 2 = 114</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;, (23 - ?) &rarr;(23 &times; 2) + 2 &rArr; 46 + 2 = </span><span style=\"font-family: Cambria Math;\">48</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Select the option in which the letter-clusters share the same relationship as that shared by the given pair of letter-clusters.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">DKG : HSN</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">VLM : ZTT</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2309;&#2325;&#2381;&#2359;&#2352;-&#2360;&#2350;&#2370;&#2361; &#2357;&#2361;&#2368; &#2360;&#2306;&#2348;&#2306;&#2343; &#2360;&#2366;&#2333;&#2366; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2379; &#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2309;&#2325;&#2381;&#2359;&#2352;-&#2360;&#2350;&#2370;&#2361;&#2379;&#2306; &#2325;&#2375; &#2351;&#2369;&#2327;&#2381;&#2350; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2360;&#2366;&#2333;&#2366; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">DKG : HSN</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">VLM : ZTT</span></p>\n",
                    options_en: ["<p>RBI : VYG</p>\n", "<p>SPY: VXB</p>\n", 
                                "<p>KTY : OBF</p>\n", "<p>PBN : SLM</p>\n"],
                    options_hi: ["<p>RBI : VYG</p>\n", "<p>SPY: VXB</p>\n",
                                "<p>KTY : OBF</p>\n", "<p>PBN : SLM</p>\n"],
                    solution_en: "<p>11.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image14.png\" width=\"297\" height=\"90\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image10.png\" width=\"135\" height=\"88\"></p>\n",
                    solution_hi: "<p>11.(c)</p>\r\n<p><span style=\"text-decoration: underline;\"><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image14.png\" width=\"251\" height=\"76\"></span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image10.png\" width=\"126\" height=\"82\"></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">Select the number pair in which the numbers are related in the same way as are the numbers of the given number pairs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 : 50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 : 288</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> &#2313;&#2360; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;-&#2351;&#2369;&#2327;&#2381;&#2350; &#2325;&#2379; &#2330;&#2369;&#2344;&#2367;&#2319; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2336;&#2368;&#2325; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2306; &#2332;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;-&#2351;&#2369;&#2327;&#2381;&#2350;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2306;&#2404; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 : 50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 : 288</span></p>\n",
                    options_en: ["<p>4 : 36</p>\n", "<p>8 : 128</p>\n", 
                                "<p>9 : 161</p>\n", "<p>6 : 70</p>\n"],
                    options_hi: ["<p>4 : 36</p>\n", "<p>8 :128</p>\n",
                                "<p>9 : 161</p>\n", "<p>6 : 70</p>\n"],
                    solution_en: "<p>12.(b)Logic: (<span style=\"font-family: Cambria Math;\">1st num)</span><span style=\"font-family: Cambria Math;\">&sup2; </span><span style=\"font-family: Cambria Math;\">&times; 2 = 2nd num </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 5 : 50 &rarr; (5</span><span style=\"font-family: Cambria Math;\">)&sup2; &times; 2 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&rArr; 25 &times; 2 = 50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 : 288 &rarr; (12</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\">) &times; 2 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&rArr; 144 &times; 2 = 288</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 : 128 &rarr; (8</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\">) &times; 2 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&rArr; 64 &times; 2 = 128</span></p>\n",
                    solution_hi: "<p>12.(b)&#2340;&#2352;&#2381;&#2325;: (<span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;)</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\"> &times; 2 = &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 5 : 50 &rarr; (5</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\">) &times; 2 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&rArr; 25 &times; 2 = 50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 : 288 &rarr; (12</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\">) &times; 2 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&rArr; 144 &times; 2 = 288</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 8 : 128 &rarr; (8</span><span style=\"font-family: Cambria Math;\">&sup2;</span><span style=\"font-family: Cambria Math;\">) &times; 2 </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&rArr; 64 &times; 2 = 128</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">34 is related to 15 following a certain logic. Following the same logic, 44 is related to 20. To which of the following is 52 related to, following the same logic? </span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2340;&#2352;&#2381;&#2325; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; 34, 15 &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;, 44, 20 &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; &#2360;&#2350;&#2366;&#2344; &#2340;&#2352;&#2381;&#2325; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319;, 52 &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2367;&#2360;&#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>32</p>\n", "<p>54</p>\n", 
                                "<p>20</p>\n", "<p>24</p>\n"],
                    options_hi: ["<p>32</p>\n", "<p>54</p>\n",
                                "<p>20</p>\n", "<p>24</p>\n"],
                    solution_en: "<p>13.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :- (2nd number &times; 2) + 4 = 1st number</span></p>\r\n<p>(34 ,15):- (15 &times; 2) + 4 = 34</p>\r\n<p>(44 , 20):- (20 &times; 2) + 4 = 44</p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\r\n<p>(52 , ?):- (24 &times; 2) + 4 = 52</p>\n",
                    solution_hi: "<p>13.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2352;&#2381;&#2325; :- (&#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &times; 2) + 4 = &#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span></p>\r\n<p><span style=\"font-weight: 400;\">(34, 15)</span>:- (15 &times; 2) + 4 = 34</p>\r\n<p><span style=\"font-weight: 400;\">(44, 20)</span>:- (20 &times; 2) + 4 = 44</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-weight: 400;\">(52, ?)</span>:- (24 &times; 2) + 4 = 52</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: " <p>14. </span><span style=\"font-family:Cambria Math\">AJK is related to BKL in a certain way based on the English alphabetical order. In the same way, DOT is related to EPU. To which of the following is PJO related following the same logic?</span></p>",
                    question_hi: " <p>14. </span><span style=\"font-family:Cambria Math\">अंग्रेजी वर्णमाला क्रम के आधार पर AJK, BKL से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, DOT, EPU से संबंधित है। समान तर्क का अनुसरण करते हुए, PJO निम्नलिखित में से किससे संबंधित है?</span></p>",
                    options_en: [" <p> QKP</span></p>", " <p> PKQ</span></p>", 
                                " <p> OJP </span></p>", " <p> RLQ </span></p>"],
                    options_hi: [" <p> QKP</span></p>", " <p> PKQ</span></p>",
                                " <p> OJP </span></p>", " <p> RLQ </span></p>"],
                    solution_en: " <p>14.(a)</span></p> <p><span style=\"font-family:Cambria Math\"> ,</span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image15.png\"/><span style=\"font-family:Cambria Math\">  </span></p> <p><span style=\"font-family:Cambria Math\">,</span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image19.png\"/><span style=\"font-family:Cambria Math\">   </span></p> <p><span style=\"font-family:Cambria Math\">Similarly,</span></p> <p><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image9.png\"/></p>",
                    solution_hi: " <p>14.(a)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image15.png\"/><span style=\"font-family:Cambria Math\">, </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image19.png\"/></p> <p><span style=\"font-family:Cambria Math\">इसी प्रकार,</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1698052371/word/media/image9.png\"/></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the triad in which the numbers are related in the same way as are the numbers of the following triads. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14 - 15 - 145</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 - 27 - 195</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">&#2313;&#2360; &#2340;&#2381;&#2352;&#2351; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2336;&#2368;&#2325; &#2313;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376; &#2332;&#2367;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2340;&#2381;&#2352;&#2351; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14 - 15 - 145</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 - 27 - 195</span></p>\n",
                    options_en: ["<p>25 - 21 - 670</p>\n", "<p>17 - 31 - 240</p>\n", 
                                "<p>11 - 19 - 230</p>\n", "<p>12 - 52 - 450</p>\n"],
                    options_hi: ["<p>25 - 21 - 670</p>\n", "<p>17 - 31 - 240</p>\n",
                                "<p>11 - 19 - 230</p>\n", "<p>12 - 52 - 450</p>\n"],
                    solution_en: "<p>15.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :-</span><span style=\"font-family: Cambria Math;\"> (1st number + 2nd number) &times; 5 =3rd number</span></p>\r\n<p><span style=\"font-weight: 400;\">(14 - 15 - 145) </span>:- (14 + 15) &times; 5 &rArr; (29) &times; 5 = 145</p>\r\n<p><span style=\"font-weight: 400;\">(12 -27- 195) </span>:- (12 + 27) &times; 5 &rArr; (39) &times; 5 = 195</p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\r\n<p><span style=\"font-weight: 400;\">(17- 31 -240) </span>:- (17 + 31) &times; 5 &rArr; (48) &times; 5 = 240</p>\n",
                    solution_hi: "<p>15.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2352;&#2381;&#2325; :</span><span style=\"font-family: Cambria Math;\">- (&#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; + &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;) &times; 5 = &#2340;&#2368;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span></p>\r\n<p><span style=\"font-weight: 400;\">(14 - 15 - 145) </span>:- (14 + 15) &times; 5 &rArr; (29) &times; 5= 145</p>\r\n<p><span style=\"font-weight: 400;\">(12 -27- 195) </span>:- (12 + 27) &times; 5 &rArr; (39) &times; 5 = 195</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-weight: 400;\">(17- 31 -240) </span>:- (17 + 31) &times; 5 &rArr; (48) &times; 5 = 240</p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>