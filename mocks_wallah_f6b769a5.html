<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. 242 is related to 22 following a certain logic. Following the same logic, 385 is related to 35. To which of the following is 572 related following the same logic ?<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>1. एक निश्चित तर्क का अनुसरण करते हुए 242, 22 से संबंधित है। उसी तर्क का अनुसरण करते हुए 385, 35 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 572 निम्नलिखित में से किससे संबंधित है ?<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>50</p>", "<p>52</p>", 
                                "<p>54</p>", "<p>56</p>"],
                    options_hi: ["<p>50</p>", "<p>52</p>",
                                "<p>54</p>", "<p>56</p>"],
                    solution_en: "<p>1.(b) <strong>Logic :- </strong>(2nd number &times; 11) = 1st number<br>(242 , 22) :- (22 &times; 11) = 242<br>(385, 35) :- (35 &times; 11) = 385<br>Similarly,<br>(572, 52) :- (52 &times; 11) = 572</p>",
                    solution_hi: "<p>1.(b)<strong> तर्क :- </strong>(दूसरी संख्या &times; 11) = पहली संख्या<br>(242 , 22) :- (22 &times; 11) = 242<br>(385, 35) :- (35 &times; 11) = 385<br>इसी प्रकार,<br>(572, 52) :- (52 &times; 11) = 572</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the set in which the numbers are related in the same way as are the numbers of&nbsp;the following sets.<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3&nbsp;and then performing mathematical operations on 1 and 3 is not allowed.)<br>(6, 12, 72)<br>(8, 16, 96)</p>",
                    question_hi: "<p>2. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए<br>समुचयों की संख्याएं संबंधित हैं।<br>(<strong>ध्यान दें: </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की&nbsp;जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा&nbsp;करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर&nbsp;गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(6, 12, 72)<br>(8, 16, 96)</p>",
                    options_en: ["<p>(7, 28, 144)</p>", "<p>(12, 24, 140)</p>", 
                                "<p>(12, 24, 144)</p>", "<p>(11, 24, 144)</p>"],
                    options_hi: ["<p>(7, 28, 144)</p>", "<p>(12, 24, 140)</p>",
                                "<p>(12, 24, 144)</p>", "<p>(11, 24, 144)</p>"],
                    solution_en: "<p>2.(c) <strong>Logic :-</strong> (1st number + 2nd number) &times; 4 = 3rd number<br>(6, 12, 72) :- (6 + 12) &times; 4 <br>&rArr; (18) &times; 4 = 72<br>(8, 16, 96) :- (8 + 16) &times; 4 <br>&rArr; (24) &times; 4 = 96<br>Similarly,<br>(12, 24, 144) :- (12 + 24) &times; 4 <br>&rArr; (36) &times; 4 = 144</p>",
                    solution_hi: "<p>2.(c) <strong>तर्क:- </strong>(पहली संख्या + दूसरी संख्या) &times; 4 = तीसरी संख्या<br>(6, 12, 72) :- (6 + 12) &times; 4 <br>&rArr; (18) &times; 4 = 72<br>(8, 16, 96) :- (8 + 16) &times; 4 <br>&rArr; (24) &times; 4 = 96&nbsp;<br>इसी प्रकार,<br>(12, 24, 144) :- (12 + 24) &times; 4 <br>&rArr; (36) &times; 4 = 144</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)<br>Referee : Field</p>",
                    question_hi: "<p>3. उस शब्द -युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या /व्यंजन/&nbsp;स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>रेफरी : मैदान</p>",
                    options_en: ["<p>Judge : Courtroom</p>", "<p>Gladiator : Ring</p>", 
                                "<p>Experimenter : Result</p>", "<p>Arbitrator : Deadlock</p>"],
                    options_hi: ["<p>जज : कोर्टरूम</p>", "<p>पैशेवर लड़ाका : गोल अखाड़ा</p>",
                                "<p>प्रयोगकर्ता : परिणाम</p>", "<p>मध्यस्थ : गतिरोध</p>"],
                    solution_en: "<p>3.(a) As the Referee is responsible for maintaining rules during matches on field. Similarly the Judge is responsible for maintaining rules in the courtroom.</p>",
                    solution_hi: "<p>3.(a) जैसे मैदान पर मैच के दौरान नियमों को बनाए रखने की जिम्मेदारी रेफरी की होती है। इसी प्रकार जज कोर्टरूम में नियमों को बनाए रखने के लिए जिम्मेदार होता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "4. DFIK is related to EGJL in a certain way based on the English alphabetical order. In the same way, GILN is related to HJMO. To which of the following is MORT related to, following the same logic ?",
                    question_hi: "<p>4. अंग्रेजी वर्णमाला क्रम के आधार पर DFIK, EGJL से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, GILN, HJMO से संबंधित है। समान तर्क का अनुसरण करते हुए, MORT निम्नलिखित में से किससे संबंधित है ?</p>",
                    options_en: [" NPVS ", " NQSU  ", 
                                " NPSU", " NPSV"],
                    options_hi: [" NPVS ", "<p>NQSU</p>",
                                "<p>NPSU</p>", "<p>NPSV</p>"],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842286637.png\" alt=\"rId4\" width=\"112\" height=\"87\">&nbsp; &nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842286770.png\" alt=\"rId5\" width=\"111\" height=\"87\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842286929.png\" alt=\"rId6\" width=\"110\" height=\"87\"></p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842286637.png\" alt=\"rId4\" width=\"112\" height=\"87\">&nbsp; &nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842286770.png\" alt=\"rId5\" width=\"111\" height=\"87\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842286929.png\" alt=\"rId6\" width=\"110\" height=\"87\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. 33 is related to 726 by certain logic. Following the same logic, 43 is related to 946. To which of the following is 53 related, following the same logic ?<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>5. 33 का संबंध एक निश्चित तर्क से 726 से है। उसी तर्क का अनुसरण करते हुए, 43 का संबंध 946 से है।&nbsp;उसी तर्क का अनुसरण करते हुए, 53 का संबंध निम्नलिखित में से किससे है ?<br>(<strong>ध्यान दें: </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की&nbsp;जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा&nbsp;करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर&nbsp;गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>846</p>", "<p>980</p>", 
                                "<p>1060</p>", "<p>1166</p>"],
                    options_hi: ["<p>846</p>", "<p>980</p>",
                                "<p>1060</p>", "<p>1166</p>"],
                    solution_en: "<p>5.(d) <strong>Logic :-</strong> (1st number &times; 22) = 2nd number<br>(33 , 726) :- (33 &times; 22) = 726<br>(43, 946) :- (43 &times; 22) = 946<br>Similarly,<br>(53, 1166) :- (53 &times; 22) = 1166</p>",
                    solution_hi: "<p>5.(d)<strong> तर्क :-</strong> (पहली संख्या &times; 22) = दूसरी संख्या<br>(33 , 726) :- (33 &times; 22) = 726<br>(43, 946) :- (43 &times; 22) = 946<br>इसी प्रकार,<br>(53, 1166) :- (53 &times; 22) = 1166</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the letter-cluster pair that best represents a similar relationship to the one&nbsp;expressed in the pairs of letter-clusters given below.<br>VPN : ZTR<br>SMK : WQO</p>",
                    question_hi: "<p>6. उस अक्षर-समूह का चयन कीजिए जो नीचे दिए गए अक्षर-समूह में व्यक्त किए गए समान&nbsp;संबंध का सबसे अच्छा निरूपण करता है।<br>VPN : ZTR<br>SMK : WQO</p>",
                    options_en: ["<p>PMK : TOL</p>", "<p>HDB : MIG</p>", 
                                "<p>KFD : OJK</p>", "<p>LFD : PJH</p>"],
                    options_hi: ["<p>PMK : TOL</p>", "<p>HDB : MIG</p>",
                                "<p>KFD : OJK</p>", "<p>LFD : PJH</p>"],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287062.png\" alt=\"rId7\" width=\"140\" height=\"130\">&nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287176.png\" alt=\"rId8\" width=\"139\" height=\"129\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287283.png\" alt=\"rId9\" width=\"139\" height=\"134\"></p>",
                    solution_hi: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287062.png\" alt=\"rId7\" width=\"140\" height=\"130\">&nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287176.png\" alt=\"rId8\" width=\"139\" height=\"129\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287283.png\" alt=\"rId9\" width=\"139\" height=\"134\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. 92 is related to 23 following a certain logic. Following the same logic, 136 is related to 34. To which of the following is 224 related, following the same logic ?<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking&nbsp;down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as&nbsp;adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>7. एक निश्चित तर्क का अनुसरण करते हुए 92, 23 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 136, 34 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 224 निम्नलिखित में से किससे संबंधित है ?<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>54</p>", "<p>52</p>", 
                                "<p>56</p>", "<p>58</p>"],
                    options_hi: ["<p>54</p>", "<p>52</p>",
                                "<p>56</p>", "<p>58</p>"],
                    solution_en: "<p>7.(c) <strong>Logic :- </strong>(2nd number &times; 4) = 1st number<br>(92, 23) :- (23 &times; 4) = 92<br>(136, 34) :- (34 &times; 4) = 136<br>Similarly,<br>(224, 56) :- (56 &times; 4) = 224</p>",
                    solution_hi: "<p>7.(c) <strong>तर्क :-</strong> (दूसरी संख्या &times; 4) = पहली संख्या<br>(92, 23) :- (23 &times; 4) = 92<br>(136, 34) :- (34 &times; 4) = 136<br>इसी प्रकार,<br>(224, 56) :- (56 &times; 4) = 224</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "8. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.) <br />Flower-Petal",
                    question_hi: "8. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है। (शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या व्यंजन स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।) <br />फूल - पंखुड़ी",
                    options_en: [" Horse- Mare ", " Fruit-Seed ", 
                                " Tiny-Large ", " Clarify - Explain"],
                    options_hi: [" घोड़ा - घोड़ी ", " फल - बीज  ",
                                " छोटा - बड़ा ", " स्पष्टीकरण - व्याख्या "],
                    solution_en: "8.(b) As Petal is the part of Flower similarly Seed is the part of Fruit.",
                    solution_hi: "8.(b) जैसे पंखुड़ी फूल का हिस्सा है उसी तरह बीज फल का हिस्सा है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the set in which the numbers are related in the same way as are the numbers of&nbsp;the following sets.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking&nbsp;down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as&nbsp;adding / subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1&nbsp;and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(5, 4, 41)<br>(7, 2, 53)</p>",
                    question_hi: "<p>9. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए&nbsp;समुचयों की संख्याएं संबंधित हैं।<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की&nbsp;जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा&nbsp;करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर&nbsp;गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(5, 4, 41)<br>(7, 2, 53)</p>",
                    options_en: ["<p>(10, 5, 130)</p>", "<p>(9, 3, 90)</p>", 
                                "<p>(13, 6, 208)</p>", "<p>(11, 9, 204)</p>"],
                    options_hi: ["<p>(10, 5, 130)</p>", "<p>(9, 3, 90)</p>",
                                "<p>(13, 6, 208)</p>", "<p>(11, 9, 204)</p>"],
                    solution_en: "<p>9.(b) <strong>Logic :- (</strong>1st number)<sup>2</sup> + (2nd number)<sup>2</sup> = 3rd number<br>(5, 4 ,41) :- (5)<sup>2</sup> + (4)<sup>2</sup> = 41<br>(7, 2, 53):- (7)<sup>2</sup> + (2)<sup>2</sup> = 53<br>Similarly,<br>(9 , 3 ,90) :- (9)<sup>2</sup> + (3)<sup>2</sup> = 90</p>",
                    solution_hi: "<p>9.(b) <strong>तर्क :- </strong>(पहली संख्या)<sup>2 </sup>+ (दूसरी संख्या)<sup>2</sup> = तीसरी संख्या<br>(5, 4 ,41) :- (5)<sup>2</sup> + (4)<sup>2</sup> = 41<br>(7, 2, 53):- (7)<sup>2</sup> + (2)<sup>2</sup> = 53<br>इसी प्रकार,<br>(9 , 3 ,90) :- (9)<sup>2</sup> + (3)<sup>2</sup> = 90</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br />(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br />Strong : Robust",
                    question_hi: "10. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br />(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br />मज़बूत : ठोस",
                    options_en: [" Kind : Generous", " Knowledge : Ignorance ", 
                                " Create : Destroy ", " Brave : Fearful<br /> "],
                    options_hi: [" दयालु : उदार ", " ज्ञान : अज्ञान  ",
                                " निर्माण करना : नष्ट करना   ", " बहादुर : भयभीत"],
                    solution_en: "10.(a) As the Strong and Robust are synonym of each other similarly Kind and Generous are synonym of each other.",
                    solution_hi: "10.(a) जिस प्रकार मज़बूत और ठोस एक दूसरे के पर्याय हैं उसी प्रकार दयालु और उदार एक दूसरे के पर्याय हैं।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br />(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)<br />Dawn - Sunrise",
                    question_hi: "11.उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br />(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br />भोर - सूर्योदय",
                    options_en: [" Touch - Toes", " Old - New ", 
                                " Joy - Sad", " Easy - Simple"],
                    options_hi: [" स्पर्श-अंगूठा", " पुराना-नया",
                                " ख़ुशी-दुःख ", " आसान-सरल"],
                    solution_en: "11.(d) As the meaning of Dawn and Sunrise is the same, similarly the meaning of Easy and Simple is same.",
                    solution_hi: "11.(d) जैसे भोर और सूर्योदय का अर्थ एक ही है, वैसे ही आसान और सरल का अर्थ भी एक ही है। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the set in which the numbers are related in the same way as are the numbers of&nbsp;the following sets.<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking&nbsp;down the numbers into its constituent digits. E.g., 13 - Operations on 13 such as&nbsp;adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3&nbsp;and then performing mathematical operations on 1 and 3 is not allowed.)<br>(6, 12, 48)<br>(8, 16, 64)</p>",
                    question_hi: "<p>12. उस समुच्चय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्चयों की संख्याएं संबंधित हैं।<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13- संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(6, 12, 48)<br>(8, 16, 64)</p>",
                    options_en: ["<p>(14, 28, 112)</p>", "<p>(12, 28, 112)</p>", 
                                "<p>(14, 28, 110)</p>", "<p>(14, 24, 112)</p>"],
                    options_hi: ["<p>(14, 28, 112)</p>", "<p>(12, 28, 112)</p>",
                                "<p>(14, 28, 110)</p>", "<p>(14, 24, 112)</p>"],
                    solution_en: "<p>12.(a) <strong>Logic :-</strong> <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup></math>no. &times; 2 = 2<sup>nd</sup>no., 2<sup>nd</sup>no. &times; 4 = 3<sup>rd</sup>no.<br>(6, 12, 48) :- 6 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 12, 12 &times; 4 = 48<br>(8, 16, 64) :- 8 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 16, 16 &times; 4 = 64<br>Similarly,<br>(14, 28, 112) :- 14 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 28, 28 &times; 4 = 112</p>",
                    solution_hi: "<p>12.(a) <strong>तर्क :- </strong>पहली संख्या <math display=\"inline\"><mo>&#215;</mo></math> 2 = दूसरी संख्या, दूसरी संख्या &times; 4 = तीसरी संख्या<br>(6, 12, 48) :- 6 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 12, 12 &times; 4 = 48<br>(8, 16, 64) :- 8 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 16, 16 &times; 4 = 64<br>इसी प्रकार,<br>(14, 28, 112) :- 14 <math display=\"inline\"><mo>&#215;</mo></math> 2 = 28, 28 &times; 4 = 112</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. 72 is related to 8 following a certain logic. Following the same logic, 198 is related to 22. To which of the following is 351 related following the same logic ?<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking&nbsp;down the numbers into its constituent digits. E.g. 13- Operations on 13 such as&nbsp;adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and&nbsp;3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>13. एक निश्चित तर्क का अनुसरण करते हुए 72, 8 से संबंधित है। उसी तर्क का अनुसरण करते हुए 198, 22 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 351 निम्नलिखित में से किससे संबंधित है ?<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13-संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>43</p>", "<p>39</p>", 
                                "<p>41</p>", "<p>37</p>"],
                    options_hi: ["<p>43</p>", "<p>39</p>",
                                "<p>41</p>", "<p>37</p>"],
                    solution_en: "<p>13.(b) Logic:- <math display=\"inline\"><mfrac><mrow><msup><mrow><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">t</mi></mrow></msup><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">o</mi><mo>.</mo></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 2<sup>nd</sup>no.<br>(72, 8) :- <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 8<br>(198, 22) :- <math display=\"inline\"><mfrac><mrow><mn>198</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 22<br>Similarly,<br>(351, ?) :- <math display=\"inline\"><mfrac><mrow><mn>351</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 39</p>",
                    solution_hi: "<p>13.(b)<strong> तर्क:-</strong> <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mn>9</mn></mfrac></math> = दूसरी संख्या<br>(72, 8) :- <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 8<br>(198, 22) :- <math display=\"inline\"><mfrac><mrow><mn>198</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 22<br>इसी प्रकार,<br>(351, ?) :- <math display=\"inline\"><mfrac><mrow><mn>351</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 39</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. \'RENT\' is related to \"IVMG\' in a certain way based on the English alphabetical order. In the same way, &lsquo;PAYABLE\' is related to &lsquo;KZBZYOV&rsquo;. To which of the following is \'BARGUANDY&rsquo; related, following the same logic ?</p>",
                    question_hi: "<p>14. अंग्रेजी वर्णमाला क्रम के आधार पर \'RENT\', \'IVMG\' से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, \'PAYABLE\', \'KZBZYOV\' से संबंधित है। समान तर्क का अनुसरण करते हुए, \'BARGUANDY\' निम्नलिखित में से किससे संबंधित है ?</p>",
                    options_en: ["<p>YZITFAMWB</p>", "<p>YZITVZMWB</p>", 
                                "<p>YZITFZMYB</p>", "<p>YZITFZMWB</p>"],
                    options_hi: ["<p>YZITFAMWB</p>", "<p>YZITVZMWB</p>",
                                "<p>YZITFZMYB</p>", "<p>YZITFZMWB</p>"],
                    solution_en: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287389.png\" alt=\"rId10\" width=\"81\" height=\"111\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287554.png\" alt=\"rId11\" width=\"163\" height=\"120\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287641.png\" alt=\"rId12\" width=\"200\" height=\"111\"></p>",
                    solution_hi: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287763.png\" alt=\"rId13\" width=\"88\" height=\"124\">&nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287890.png\" alt=\"rId14\" width=\"163\" height=\"126\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736842287991.png\" alt=\"rId15\" width=\"205\" height=\"119\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. 15 is related to 195 following a certain logic. Following the same logic, 19 is related to&nbsp;247. To which of the following is 24 related following the same logic ?<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>15. एक निश्चित तर्क का अनुसरण करते हुए 15, 195 से संबंधित है। उसी तर्क का अनुसरण करते हुए 19,&nbsp;247 से संबंधित है। समान तर्क का अनुसरण करते हुए, 24 निम्नलिखित में से किससे संबंधित है ?<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>312</p>", "<p>300</p>", 
                                "<p>322</p>", "<p>332</p>"],
                    options_hi: ["<p>312</p>", "<p>300</p>",
                                "<p>322</p>", "<p>332</p>"],
                    solution_en: "<p>15.(a)<strong> Logic :- </strong>(1st number &times; 13) = 2nd number<br>(15, 195) :- (15 &times; 13) = 195<br>(19, 247) :- (19 &times; 13) = 247<br>Similarly,<br>(24, ?) :- (24 &times; 13) = 312</p>",
                    solution_hi: "<p>15.(a)<strong> तर्क :-</strong> (पहली संख्या &times; 13) = दूसरी संख्या<br>(15, 195) :- (15 &times; 13) = 195<br>(19, 247) :- (19 &times; 13) = 247<br>इसी प्रकार,<br>(24, ?) :- (24 &times; 13) = 312</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>