<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1.</span><span style=\"font-family:Cambria Math\"> Select the correct homonym from the given options to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Nadia had an apple and </span><span style=\"font-family:Cambria Math\">a________for</span><span style=\"font-family:Cambria Math\"> breakfast.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Morning)</span></p>",
                    question_hi: " <p>1.</span><span style=\"font-family:Cambria Math\"> Select the correct homonym from the given options to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Nadia had an apple and </span><span style=\"font-family:Cambria Math\">a________for</span><span style=\"font-family:Cambria Math\"> breakfast.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Morning)</span></p>",
                    options_en: [" <p>  pear</span></p>", " <p>  prier</span></p>", 
                                " <p>  pair</span></p>", " <p>  pare</span></p>"],
                    options_hi: [" <p>  pear</span></p>", " <p>  prier</span></p>",
                                " <p>  pair</span></p>", " <p>  pare</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">1.(</span><span style=\"font-family:Cambria Math\">a)</span></p> <p><span style=\"font-family:Cambria Math\">Pear is a fruit. The sentence mentions about something being eaten so pear is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">1.(</span><span style=\"font-family:Cambria Math\">a)</span></p> <p><span style=\"font-family:Cambria Math\">Pear is a fruit. The sentence mentions about something being eaten so pear is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p>2. </span><span style=\"font-family:Cambria Math\">Select the correct homonym from the given options to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">I wonder if the committee members </span><span style=\"font-family:Cambria Math\">changed________plans</span><span style=\"font-family:Cambria Math\">.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Morning)</span></p>",
                    question_hi: " <p>2. </span><span style=\"font-family:Cambria Math\">Select the correct homonym from the given options to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">I wonder if the committee members </span><span style=\"font-family:Cambria Math\">changed________plans</span><span style=\"font-family:Cambria Math\">.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Morning)</span></p>",
                    options_en: [" <p>  they’re</span></p>", " <p>  their</span></p>", 
                                " <p>  dare</span></p>", " <p>  there</span></p>"],
                    options_hi: [" <p>  they’re</span></p>", " <p>  their</span></p>",
                                " <p>  dare</span></p>", " <p>  there</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">2.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Cambria Math\">The pronoun </span><span style=\"font-family:Cambria Math\">their</span><span style=\"font-family:Cambria Math\"> will be used. “Their” is used with the people or things previously mentioned or easily identified (committee members</span><span style=\"font-family:Cambria Math\">).So</span><span style=\"font-family:Cambria Math\"> b is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">2.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Cambria Math\">The pronoun </span><span style=\"font-family:Cambria Math\">their</span><span style=\"font-family:Cambria Math\"> will be used. “Their” is used with the people or things previously mentioned or easily identified (committee members</span><span style=\"font-family:Cambria Math\">).So</span><span style=\"font-family:Cambria Math\"> b is the most appropriate answer.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p>3.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The news of the robbery spread faster than ________.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Morning)</span></p>",
                    question_hi: " <p>3.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The news of the robbery spread faster than ________.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Morning)</span></p>",
                    options_en: [" <p>  fire</span></p>", " <p>  wind</span></p>", 
                                " <p>  water</span></p>", " <p>  air</span></p>"],
                    options_hi: [" <p>  fire</span></p>", " <p>  wind</span></p>",
                                " <p>  water</span></p>", " <p>  air</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">3.(</span><span style=\"font-family:Cambria Math\">a)</span></p> <p><span style=\"font-family:Cambria Math\">Spread like wildfire is an idiom which means to spread with great speed. </span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> a is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">3.(</span><span style=\"font-family:Cambria Math\">a)</span></p> <p><span style=\"font-family:Cambria Math\">Spread like wildfire is an idiom which means to spread with great speed. </span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> a is the most appropriate answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\">rumours</span><span style=\"font-family: Cambria Math;\"> usually _________ like wild fire.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\">rumours</span><span style=\"font-family: Cambria Math;\"> usually _________ like wild fire.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>spreading</p>\n", "<p>spread</p>\n", 
                                "<p>spreads</p>\n", "<p>are spreading</p>\n"],
                    options_hi: ["<p>spreading</p>\n", "<p>spread</p>\n",
                                "<p>spreads</p>\n", "<p>are spreading</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The word usually in the sentence shows that present indefinite should be </span><span style=\"font-family: Cambria Math;\">used.So</span><span style=\"font-family: Cambria Math;\"> spread will be used as the subject is &ldquo;the </span><span style=\"font-family: Cambria Math;\">rumours</span><span style=\"font-family: Cambria Math;\">.&rdquo;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The word usually in the sentence shows that present indefinite should be </span><span style=\"font-family: Cambria Math;\">used.So</span><span style=\"font-family: Cambria Math;\"> spread will be used as the subject is &ldquo;the </span><span style=\"font-family: Cambria Math;\">rumours</span><span style=\"font-family: Cambria Math;\">.&rdquo;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: " <p>5.</span><span style=\"font-family:Cambria Math\"> Fill in the blank with the correctly spelled word.</span></p> <p><span style=\"font-family:Cambria Math\">There was a feeling of __________ in his eyes.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Afternoon)</span></p>",
                    question_hi: " <p>5.</span><span style=\"font-family:Cambria Math\"> Fill in the blank with the correctly spelled word.</span></p> <p><span style=\"font-family:Cambria Math\">There was a feeling of __________ in his eyes.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Afternoon)</span></p>",
                    options_en: [" <p> </span><span style=\"font-family:Cambria Math\">vengaence</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">vengense</span></p>", 
                                " <p> </span><span style=\"font-family:Cambria Math\">vengence</span></p>", " <p> vengeance</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">vengaence</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">vengense</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">vengence</span></p>", " <p> vengeance</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">5.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">Vengeance is the correct spelling.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">5.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">Vengeance is the correct spelling.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He always ____ a tantrum about something or the other.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He always ____ a tantrum about something or the other.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>through</p>\n", "<p>threw</p>\n", 
                                "<p>tough</p>\n", "<p>true</p>\n"],
                    options_hi: ["<p>through</p>\n", "<p>threw</p>\n",
                                "<p>tough</p>\n", "<p>true</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">To throw a tantrum is an </span><span style=\"font-family: Cambria Math;\">idiom.It</span><span style=\"font-family: Cambria Math;\"> is used when a child has an angry outburst or someone who is not a child gets very angry and upset and behaves like a child.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Pinnacle note</strong>- </span><span style=\"font-family: Cambria Math;\">With tantrum we use the verb &ldquo;throw&rdquo; only.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">To throw a tantrum is an </span><span style=\"font-family: Cambria Math;\">idiom.It</span><span style=\"font-family: Cambria Math;\"> is used when a child has an angry outburst or someone who is not a child gets very angry and upset and behaves like a child.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Pinnacle note</strong>- </span><span style=\"font-family: Cambria Math;\">With tantrum we use the verb &ldquo;throw&rdquo; only.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully </span><span style=\"font-family: Cambria Math;\">and select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The app gets Larry&rsquo;s parents involved in the learning and improvement process, </span><span style=\"font-family: Cambria Math;\">sending them advice about Larry&rsquo;s </span><span style=\"font-family: Cambria Math;\">behaviours</span><span style=\"font-family: Cambria Math;\"> and progress, and recommending </span><span style=\"font-family: Cambria Math;\">motivational _______ they might undertake that are _______ to Larry&rsquo;s situation and </span><span style=\"font-family: Cambria Math;\">the program&rsquo;s _________ about his attitudes and </span><span style=\"font-family: Cambria Math;\">behaviours</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 (Evening)</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">In the following passage, some words have been deleted. Read the passage carefully </span><span style=\"font-family: Cambria Math;\">and select the most appropriate option to fill in each blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The app gets Larry&rsquo;s parents involved in the learning and improvement process, </span><span style=\"font-family: Cambria Math;\">sending them advice about Larry&rsquo;s </span><span style=\"font-family: Cambria Math;\">behaviours</span><span style=\"font-family: Cambria Math;\"> and progress, and recommending </span><span style=\"font-family: Cambria Math;\">motivational _______ they might undertake that are _______ to Larry&rsquo;s situation and </span><span style=\"font-family: Cambria Math;\">the program&rsquo;s _________ about his attitudes and </span><span style=\"font-family: Cambria Math;\">behaviours</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 07/06/2022 (Evening)</span></p>\n",
                    options_en: ["<p>intermissions<span style=\"font-family: Cambria Math;\">, directed, injunctions</span></p>\n", "<p>interpolations<span style=\"font-family: Cambria Math;\">, disrupted, inventions</span></p>\n", 
                                "<p>interventions<span style=\"font-family: Cambria Math;\">, targeted, inferences</span></p>\n", "<p>interruptions<span style=\"font-family: Cambria Math;\">, disillusioned, invasions</span></p>\n"],
                    options_hi: ["<p>intermissions<span style=\"font-family: Cambria Math;\">, directed, injunctions</span></p>\n", "<p>interpolations<span style=\"font-family: Cambria Math;\">, disrupted, inventions</span></p>\n",
                                "<p>interventions<span style=\"font-family: Cambria Math;\">, targeted, inferences</span></p>\n", "<p>interruptions<span style=\"font-family: Cambria Math;\">, disillusioned, invasions</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Interventions means the action of becoming intentionally involved in a difficult situation, in order to improve it or prevent it from getting </span><span style=\"font-family: Cambria Math;\">worse.And</span><span style=\"font-family: Cambria Math;\"> intervention is available in option c only so c is the correct answer.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Interventions means the action of becoming intentionally involved in a difficult situation, in order to improve it or prevent it from getting </span><span style=\"font-family: Cambria Math;\">worse.And</span><span style=\"font-family: Cambria Math;\"> intervention is available in option c only so c is the correct answer.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: " <p>8.</span><span style=\"font-family:Cambria Math\"> Select the correct homonym from the given options to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The athlete’s________ was badly injured due to a fall on the track.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Evening)</span></p>",
                    question_hi: " <p>8.</span><span style=\"font-family:Cambria Math\"> Select the correct homonym from the given options to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The athlete’s________ was badly injured due to a fall on the track.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Evening)</span></p>",
                    options_en: [" <p>  toe</span></p>", " <p>  toad</span></p>", 
                                " <p>  tow</span></p>", " <p>  to</span></p>"],
                    options_hi: [" <p>  toe</span></p>", " <p>  toad</span></p>",
                                " <p>  tow</span></p>", " <p>  to</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">a)</span></p> <p><span style=\"font-family:Cambria Math\">Toe is a part of the human body in the feet. Toad is a small animal that is an </span><span style=\"font-family:Cambria Math\">amphibian.And</span><span style=\"font-family:Cambria Math\"> tow- is used in case of a motor vehicle or boat) pull (another vehicle or boat) along with a rope, chain, or tow bar. Hence option a is the right answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">a)</span></p> <p><span style=\"font-family:Cambria Math\">Toe is a part of the human body in the feet. Toad is a small animal that is an </span><span style=\"font-family:Cambria Math\">amphibian.And</span><span style=\"font-family:Cambria Math\"> tow- is used in case of a motor vehicle or boat) pull (another vehicle or boat) along with a rope, chain, or tow bar. Hence option a is the right answer.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p>9. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">She swallowed ______ her breakfast in a hurry.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Evening)</span></p>",
                    question_hi: " <p>9. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">She swallowed ______ her breakfast in a hurry.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 07/06/2022 (Evening)</span></p>",
                    options_en: [" <p>  in</span></p>", " <p>  up</span></p>", 
                                " <p>  into</span></p>", " <p>  down</span></p>"],
                    options_hi: [" <p>  in</span></p>", " <p>  up</span></p>",
                                " <p>  into</span></p>", " <p>  down</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">9.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">Swallow down- To eat or drink something, especially very quickly. </span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> d is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">9.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">Swallow down- To eat or drink something, especially very quickly. </span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> d is the most appropriate answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: " <p>10. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Funny stories made me and my brother_________.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 08/06/2022 (Morning)</span></p>",
                    question_hi: " <p>10. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Funny stories made me and my brother_________.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 08/06/2022 (Morning)</span></p>",
                    options_en: [" <p> conscious</span></p>", " <p> determined</span></p>", 
                                " <p> giggly</span></p>", " <p> gritty</span></p>"],
                    options_hi: [" <p> conscious</span></p>", " <p> determined</span></p>",
                                " <p> giggly</span></p>", " <p> gritty</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">10.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Giggly is </span><span style=\"font-family:Cambria Math\">having a tendency to laugh in an excited, nervous, or silly way. The stories are funny so the word giggly will be </span><span style=\"font-family:Cambria Math\">used.</span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> option c is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">10.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Giggly is </span><span style=\"font-family:Cambria Math\">having a tendency to laugh in an excited, nervous, or silly way. The stories are funny so the word giggly will be </span><span style=\"font-family:Cambria Math\">used.</span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> option c is the most appropriate answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: " <p>11.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Settlers ______ a new colony in the early 18th century.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 08/06/2022 (Morning)</span></p>",
                    question_hi: " <p>11.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">Settlers ______ a new colony in the early 18th century.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 08/06/2022 (Morning)</span></p>",
                    options_en: [" <p> fixed</span></p>", " <p> extended</span></p>", 
                                " <p> established</span></p>", " <p> entrenched</span></p>"],
                    options_hi: [" <p> fixed</span></p>", " <p> extended</span></p>",
                                " <p> established</span></p>", " <p> entrenched</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">11.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">When a new colony is set up the verb establish is </span><span style=\"font-family:Cambria Math\">used.Settlers</span><span style=\"font-family:Cambria Math\"> establish new colonies.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">11.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">When a new colony is set up the verb establish is </span><span style=\"font-family:Cambria Math\">used.Settlers</span><span style=\"font-family:Cambria Math\"> establish new colonies.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">We\'re asking them to ______ the deadline.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 08/06/2022 (Afternoon)       </span></p>",
                    question_hi: " <p>12.</span><span style=\"font-family:Cambria Math\"> Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">We\'re asking them to ______ the deadline.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 08/06/2022 (Afternoon)       </span></p>",
                    options_en: [" <p>  increase</span></p>", " <p>  enlarge</span></p>", 
                                " <p>  broaden</span></p>", " <p>  extend</span><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p>  increase</span></p>", " <p>  enlarge</span></p>",
                                " <p>  broaden</span></p>", " <p>  extend</span><span style=\"font-family:Cambria Math\"> </span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">12.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">The verb \"extend the deadline\" means \"make a period last </span><span style=\"font-family:Cambria Math\">longer</span><span style=\"font-family:Cambria Math\">\".A</span><span style=\"font-family:Cambria Math\"> deadline is a time or date before which a particular task must be finished. Deadlines are not increased, enlarged or broadened but deadlines are extended. </span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> option d is the most appropriate answer.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">12.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">The verb \"extend the deadline\" means \"make a period last </span><span style=\"font-family:Cambria Math\">longer</span><span style=\"font-family:Cambria Math\">\".A</span><span style=\"font-family:Cambria Math\"> deadline is a time or date before which a particular task must be finished. Deadlines are not increased, enlarged or broadened but deadlines are extended. </span><span style=\"font-family:Cambria Math\">So</span><span style=\"font-family:Cambria Math\"> option d is the most appropriate answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p>13. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The world is constantly living in risk, uncertainty and fear of _________.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 09/06/2022 (Morning)</span></p>",
                    question_hi: " <p>13. </span><span style=\"font-family:Cambria Math\">Select the most appropriate option to fill in the blank.</span></p> <p><span style=\"font-family:Cambria Math\">The world is constantly living in risk, uncertainty and fear of _________.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 09/06/2022 (Morning)</span></p>",
                    options_en: [" <p>  life</span></p>", " <p>  body</span></p>", 
                                " <p>  nature</span></p>", " <p>  death</span></p>"],
                    options_hi: [" <p>  life</span></p>", " <p>  body</span></p>",
                                " <p>  nature</span></p>", " <p>  death</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">13.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">The word to be used has to be paired with uncertainty which is a negative word. When two words are joined by and they should be similar. So the other word should also be </span><span style=\"font-family:Cambria Math\">negative.So</span><span style=\"font-family:Cambria Math\"> death is the most appropriate answer. </span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">13.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">The word to be used has to be paired with uncertainty which is a negative word. When two words are joined by and they should be similar. So the other word should also be </span><span style=\"font-family:Cambria Math\">negative.So</span><span style=\"font-family:Cambria Math\"> death is the most appropriate answer. </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14. </span><span style=\"font-family:Cambria Math\">Complete the collocation.</span></p> <p><span style=\"font-family:Cambria Math\">When we met first it was very difficult to ______ the ice.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 09/06/2022 (Afternoon)</span></p>",
                    question_hi: " <p>14. </span><span style=\"font-family:Cambria Math\">Complete the collocation.</span></p> <p><span style=\"font-family:Cambria Math\">When we met first it was very difficult to ______ the ice.</span></p> <p><span style=\"font-family:Cambria Math\">SSC CHSL 09/06/2022 (Afternoon)</span></p>",
                    options_en: [" <p> smash</span></p>", " <p> burst</span></p>", 
                                " <p> break</span></p>", " <p> shatter</span></p>"],
                    options_hi: [" <p> smash</span></p>", " <p> burst</span></p>",
                                " <p> break</span></p>", " <p> shatter</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Break the ice is an idiom. It means to do something to make people feel relaxed and comfortable</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">Break the ice is an idiom. It means to do something to make people feel relaxed and comfortable</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. Select the most appropriate option to fill in the blank</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tagore and Gandhi were undoubtedly the two outstanding and _________ figures of </span><span style=\"font-family: Cambria Math;\">India in the first half of twentieth century.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 09/06/2022 (Afternoon)</span></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">. Select the most appropriate option to fill in the blank</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Tagore and Gandhi were undoubtedly the two outstanding and _________ figures of </span><span style=\"font-family: Cambria Math;\">India in the first half of twentieth century.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SSC CHSL 09/06/2022 (Afternoon)</span></p>\n",
                    options_en: ["<p>dominating</p>\n", "<p>activating</p>\n", 
                                "<p>resuming</p>\n", "<p>appreciating</p>\n"],
                    options_hi: ["<p>dominating</p>\n", "<p>activating</p>\n",
                                "<p>resuming</p>\n", "<p>appreciating</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Dominating means having power or influence over.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Dominating means having power or influence over.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>