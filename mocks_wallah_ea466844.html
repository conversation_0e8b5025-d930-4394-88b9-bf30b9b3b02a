<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The average marks scored by the students of a class is 75. If the average marks scored by the girls in the same class is 90 and that of boys is 70, what is the percentage of boys in the class?</p>",
                    question_hi: "<p>1. एक कक्षा के विद्यार्थियों द्वारा प्राप्त औसत अंक 75 है। यदि उसी कक्षा में लड़कियों द्वारा प्राप्त औसत अंक 90 और लड़कों द्वारा प्राप्त औसत अंक 70 हैं, तो कक्षा में लड़कों का प्रतिशत क्या है?</p>",
                    options_en: ["<p>60%</p>", "<p>70%</p>", 
                                "<p>65%</p>", "<p>75%</p>"],
                    options_hi: ["<p>60%</p>", "<p>70%</p>",
                                "<p>65%</p>", "<p>75%</p>"],
                    solution_en: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736829871787.png\" alt=\"rId4\" width=\"215\" height=\"117\"><br>So the percentage of boys = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 75%</p>",
                    solution_hi: "<p>1.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736829871920.png\" alt=\"rId5\" width=\"186\" height=\"115\"><br>अतः लड़कों का प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = 75%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. In an election there were only two candidates. One of the candidates secured 35% of votes and is defeated by the other candidate by 381 votes. The total number of votes polled is:</p>",
                    question_hi: "<p>2. एक चुनाव में केवल दो उम्मीदवार थे। एक उम्मीदवार को 35% मत मिले और वह दूसरे उम्मीदवार से 381 मतों से हार गया। डाले गए मतों की कुल संख्या कितनी है?</p>",
                    options_en: ["<p>1028</p>", "<p>1270</p>", 
                                "<p>635</p>", "<p>514</p>"],
                    options_hi: ["<p>1028</p>", "<p>1270</p>",
                                "<p>635</p>", "<p>514</p>"],
                    solution_en: "<p>2.(b) <br>Difference = 65 - 35 = 30%<br>(Difference) 30% = 381<br>(Total votes) 100% = <math display=\"inline\"><mfrac><mrow><mn>381</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 1270</p>",
                    solution_hi: "<p>2.(b) <br>अंतर = 65 - 35 = 30%<br>(अंतर) 30% = 381<br>(कुल वोट) 100% = <math display=\"inline\"><mfrac><mrow><mn>381</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = 1270</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. If a person\'s salary increases from ₹200 per day to ₹234 per day, then the percentage increase in the person\'s salary is:</p>",
                    question_hi: "<p>3. यदि किसी व्यक्ति का वेतन ₹200 प्रति दिन से बढ़कर ₹234 प्रति दिन हो जाता है, तो व्यक्ति के वेतन में __________ प्रतिशत वृद्धि है।</p>",
                    options_en: ["<p>14%</p>", "<p>16%</p>", 
                                "<p>15%</p>", "<p>17%</p>"],
                    options_hi: ["<p>14%</p>", "<p>16%</p>",
                                "<p>15%</p>", "<p>17%</p>"],
                    solution_en: "<p>3.(d) <br>Percentage increase = <math display=\"inline\"><mfrac><mrow><mn>234</mn><mo>-</mo><mn>200</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> &times; 100 <br>= 17%</p>",
                    solution_hi: "<p>3.(d) <br>प्रतिशत वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>234</mn><mo>-</mo><mn>200</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> &times; 100 <br>= 17%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If 20% of 35% of a number is 240.8, then 18% of that number (rounded off to 1 decimal place) is:</p>",
                    question_hi: "<p>4. यदि किसी संख्या के 35% का 20%, 240.8 है तो उस संख्या का 18% (1 दशमलव स्थान तक पूर्णांकित) ज्ञात कीजिए।</p>",
                    options_en: ["<p>621.2</p>", "<p>619.2</p>", 
                                "<p>618.2</p>", "<p>623.2</p>"],
                    options_hi: ["<p>621.2</p>", "<p>619.2</p>",
                                "<p>618.2</p>", "<p>623.2</p>"],
                    solution_en: "<p>4.(b) <br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 240.8<br>x = 240.8 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>7</mn></mfrac></math> &times; 5<br>Number (x) = 3440<br>Now, 18% of number = 3440 &times; <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= 619.2</p>",
                    solution_hi: "<p>4.(b) <br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>20</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 240.8<br>x = 240.8 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>7</mn></mfrac></math> &times; 5<br>संख्या (x) = 3440<br>अब, संख्या का 18% = 3440 &times; <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= 619.2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The price of an electronic watch is first reduced by 25% and then increased by 50%. If the resulting price of the electronic watch is ₹7,650, then what was its original price (in ₹)?</p>",
                    question_hi: "<p>5. एक इलेक्ट्रॉनिक घड़ी का मूल्य पहले 25% कम किया जाता है और फिर 50% बढ़ा दिया जाता है। यदि इलेक्ट्रॉनिक घड़ी का परिणामी मूल्य ₹7,650 है, तो इसका मूल मूल्य (₹ में) क्या था?</p>",
                    options_en: ["<p>6,600</p>", "<p>6,800</p>", 
                                "<p>6,900</p>", "<p>6,500</p>"],
                    options_hi: ["<p>6,600</p>", "<p>6,800</p>",
                                "<p>6,900</p>", "<p>6,500</p>"],
                    solution_en: "<p>5.(b) <br>Let initial price = Rs. x<br>According to the question,<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> = 7650<br>x = 7650 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> <br>= Rs. 6800</p>",
                    solution_hi: "<p>5.(b) <br>माना प्रारंभिक कीमत = रु.x<br>प्रश्न के अनुसार,<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> = 7650<br>x = 7650 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> <br>= रु. 6800</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. After decreasing 30%, an article costs Rs.3,500. Find its actual cost.</p>",
                    question_hi: "<p>6. 30% की कमी करने के बाद, किसी वस्तु का मूल्य Rs.3,500 होता है। इसका वास्तविक मूल्य ज्ञात कीजिए।</p>",
                    options_en: ["<p>Rs.5,000</p>", "<p>Rs.6,500</p>", 
                                "<p>Rs.5,500</p>", "<p>Rs.4,500</p>"],
                    options_hi: ["<p>Rs.5,000</p>", "<p>Rs.6,500</p>",
                                "<p>Rs.5,500</p>", "<p>Rs.4,500</p>"],
                    solution_en: "<p>6.(a)<br>Actual cost = <math display=\"inline\"><mfrac><mrow><mn>3500</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 100 = Rs.5,000</p>",
                    solution_hi: "<p>6.(a)<br>वास्तविक लागत = <math display=\"inline\"><mfrac><mrow><mn>3500</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 100 = Rs.5,000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Ram spends 30% of his salary on food, 10% on house rent, 7% on entertainment, and&nbsp;6% on conveyance. If at the end of a month his savings are ₹1,880, then his monthly&nbsp;salary is:</p>",
                    question_hi: "<p>7. राम अपने वेतन का 30% भोजन पर, 10% घर के किराए पर, 7% मनोरंजन पर और 6% परिवहन पर&nbsp;खर्च करता है। यदि एक महीने के अंत में उसकी बचत ₹1,880 है, तो उसके मासिक वेतन की गणना करें।</p>",
                    options_en: ["<p>₹4,000</p>", "<p>₹4,500</p>", 
                                "<p>₹5,000</p>", "<p>₹5,500</p>"],
                    options_hi: ["<p>₹4,000</p>", "<p>₹4,500</p>",
                                "<p>₹5,000</p>", "<p>₹5,500</p>"],
                    solution_en: "<p>7.(a) <br>Let total salary = 100 %<br>Total expenditure = 30% + 10% + 7% + 6% = 53%<br>Saving = 100% - 53% = 47%<br>47% = 1880<br>100% (total salary) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1880</mn><mn>47</mn></mfrac></math> &times; 100% <br>= 4,000</p>",
                    solution_hi: "<p>7.(a) <br>माना कुल वेतन = 100 %<br>कुल व्यय = 30% + 10% + 7% + 6% = 53%<br>बचत = 100% - 53% = 47%<br>47% = 1880<br>100% (कुल वेतन) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1880</mn><mn>47</mn></mfrac></math> &times; 100% <br>= 4,000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. In an election between two candidates, A gets 63% of the total valid votes. If the total&nbsp;votes polled were 8,750, what is the number of valid votes that the other candidate B&nbsp;gets, if 20% of the total votes were declared invalid?</p>",
                    question_hi: "<p>8. दो उम्मीदवारोंके बीच एक चुनाव में, A को कुल वैध मतों का 63% प्राप्त होता है। यदि डाले गए कुल मत 8,750 थे, यदि कुल मतों का 20% अवैध घोषित किया गया था तो अन्य उम्मीदवार B को मिले वैध मतों की संख्या क्या है?</p>",
                    options_en: ["<p>4,410</p>", "<p>3,560</p>", 
                                "<p>6,450</p>", "<p>2,590</p>"],
                    options_hi: ["<p>4,410</p>", "<p>3,560</p>",
                                "<p>6,450</p>", "<p>2,590</p>"],
                    solution_en: "<p>8.(d) <br>Total votes polled = 8750<br>Valid votes = <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 8750 = 7000<br>Valid votes get by B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>100</mn></mfrac></math> &times; 7000 = 2590</p>",
                    solution_hi: "<p>8.(d) <br>कुल वोट पड़े = 8750<br>वैध वोट = <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 8750 = 7000<br>B को मिले वैध वोट = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>100</mn></mfrac></math> &times; 7000 = 2590</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. In an election there were three candidates. The first candidate got 40% votes and&nbsp;second candidate got 35% votes. If the total votes in the election are 50,000, find the&nbsp;number of votes got by the third candidate.</p>",
                    question_hi: "<p>9. एक चुनाव में तीन उम्मीदवार थे। पहले उम्मीदवार को 40% मत मिले और दूसरे उम्मीदवार को 35% मत मिले। यदि चुनाव में कुल मत 50,000 हैं, तो तीसरे उम्मीदवार को मिले मतों की संख्या ज्ञात करें।</p>",
                    options_en: ["<p>13,500</p>", "<p>12500</p>", 
                                "<p>12000</p>", "<p>1250</p>"],
                    options_hi: ["<p>13,500</p>", "<p>12500</p>",
                                "<p>12000</p>", "<p>1250</p>"],
                    solution_en: "<p>9.(b)<br>Total votes (100%) = 50000<br>No. of vote got third candidate = 100 - (40 + 35) = 25%<br>Hence, votes gote by third candidate = <math display=\"inline\"><mfrac><mrow><mn>50000</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 25 = 12500</p>",
                    solution_hi: "<p>9.(b)<br>कुल वोट (100%) = 50000<br>तीसरे उम्मीदवार को मिले वोटों की संख्या = 100 - (40 + 35) = 25%<br>अतः, तीसरे उम्मीदवार को मिले वोट = <math display=\"inline\"><mfrac><mrow><mn>50000</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 25 = 12500</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. After an increase of 9% in salary, a person received ₹6,300. What was his salary before&nbsp;the increase (consider integral part only)?</p>",
                    question_hi: "<p>10. वेतन में 9% की वृद्धि के बाद, एक व्यक्ति को ₹6,300 प्राप्त हुए। वृद्धि से पहले उसका वेतन कितना था (केवल पूर्णांक मान मानिए)?</p>",
                    options_en: ["<p>₹5,000</p>", "<p>₹3,879</p>", 
                                "<p>₹5,779</p>", "<p>₹4,000</p>"],
                    options_hi: ["<p>₹5,000</p>", "<p>₹3,879</p>",
                                "<p>₹5,779</p>", "<p>₹4,000</p>"],
                    solution_en: "<p>10.(c)<br>Initial salary = <math display=\"inline\"><mfrac><mrow><mn>6300</mn></mrow><mrow><mn>109</mn></mrow></mfrac></math> &times; 100 = ₹5,779 (approx)</p>",
                    solution_hi: "<p>10.(c)<br>प्रारंभिक वेतन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6300</mn><mn>109</mn></mfrac></math> &times; 100 = ₹5,779 (लगभग)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. When 12 is subtracted from a number, it reduces to its 40%. Two-fifth of that number&nbsp;is:</p>",
                    question_hi: "<p>11. किसी संख्या में से 12 घटाने पर वह संख्या घटकर संख्या का 40% रह जाती है। उस संख्या का दो पांचवा (Two-fifth) भाग ज्ञात कीजिए।</p>",
                    options_en: ["<p>20</p>", "<p>50</p>", 
                                "<p>12</p>", "<p>8</p>"],
                    options_hi: ["<p>20</p>", "<p>50</p>",
                                "<p>12</p>", "<p>8</p>"],
                    solution_en: "<p>11.(d) <br>Let number = x<br>According to the question,<br>x - 12 = x &times; 25<br>&rArr; 5x&nbsp;- 60 = 2x<br>&rArr; 3x&nbsp;= 60 <br>&rArr; x = 20<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>th of number = 20 &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> <br>= 8</p>",
                    solution_hi: "<p>11.(d) <br>माना संख्या = <math display=\"inline\"><mi>x</mi></math><br>प्रश्न के अनुसार,<br>x - 12 = x &times; 25<br>&rArr; 5x&nbsp;- 60 = 2x<br>&rArr; 3x&nbsp;= 60 <br>&rArr; x = 20<br>संख्या का <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> वाँ भाग = 20 &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> <br>= 8</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. In an election, a candidate secures 40% of the votes, but is defeated by the only other&nbsp;candidate with a majority of 298 votes. Find the total number of votes recorded.</p>",
                    question_hi: "<p>12. किसी चुनाव में, एक उम्मीदवार को 40% मत प्राप्त होते हैं, लेकिन वह अन्य उम्मीदवार से केवल 298 मतों के बहुमत से पराजित हो जाता है। दर्ज मतों की कुल संख्या ज्ञात करें।</p>",
                    options_en: ["<p>1,490</p>", "<p>1,290</p>", 
                                "<p>1,270</p>", "<p>1,470</p>"],
                    options_hi: ["<p>1,490</p>", "<p>1,290</p>",
                                "<p>1,270</p>", "<p>1,470</p>"],
                    solution_en: "<p>12.(a)<br>Let the total no. of votes be x<br>No. of votes received by 1<sup>st</sup>&nbsp;candidate = 40%<br>No. of votes received by 2<sup>nd</sup>&nbsp;candidate = (100 - 40)% = 60%<br>according to the question,<br>x &times; (60 - 40)% = 298<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>100</mn></mfrac></math> = 298<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 298<br>x = 298 &times; 5 <br>= 1490</p>",
                    solution_hi: "<p>12.(a)<br>माना वोटों की कुल संख्या x&nbsp;है<br>प्रथम उम्मीदवार को प्राप्त मतों की संख्या = 40%<br>दूसरे उम्मीदवार को प्राप्त वोटों की संख्या = (100 - 40)% = 60%<br>प्रश्न के अनुसार<br>x &times; (60 - 40)% = 298<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>100</mn></mfrac></math> = 298<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 298<br>x = 298 &times; 5 <br>= 1490</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Due to decrease in manpower, the production in a factory decreases by 25%. By what percent should the working hours be increased to restore the original production?</p>",
                    question_hi: "<p>13. कामगारों की कमी के कारण किसी कारखाने का उत्पादन 25% घट जाता है। मूल उत्पादन को फिर से प्राप्त करने के लिए काम के घंटों में कितने प्रतिशत की वृद्धि की जानी चाहिए?</p>",
                    options_en: ["<p>50%</p>", "<p>43<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>", 
                                "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>25%</p>"],
                    options_hi: ["<p>50%</p>", "<p>43<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>",
                                "<p>33<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>25%</p>"],
                    solution_en: "<p>13.(c) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Original :&nbsp; &nbsp;New<br>Production&nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;3<br>Working hr&nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;4<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>3</mn></mfrac></math> = 33<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</p>",
                    solution_hi: "<p>13.(c) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; मूल&nbsp; :&nbsp; नया<br>उत्पादन&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; -&nbsp; 4&nbsp; &nbsp;:&nbsp; &nbsp; 3<br>काम करने के घंटे -&nbsp; 3&nbsp; &nbsp;:&nbsp; &nbsp; 4<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>3</mn></mfrac></math> = 33<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Two numbers are respectively 61% and 35% less than a third number. The first number is what percentage of the second number ?</p>",
                    question_hi: "<p>14. दो संख्याएं एक तीसरी संख्या से क्रमशः 61% और 35% कम हैं। पहली संख्या दूसरी संख्या का कितना प्रतिशत है?</p>",
                    options_en: ["<p>48%</p>", "<p>24%</p>", 
                                "<p>60%</p>", "<p>30%</p>"],
                    options_hi: ["<p>48%</p>", "<p>24%</p>",
                                "<p>60%</p>", "<p>30%</p>"],
                    solution_en: "<p>14.(c)<br>Let the third no. be 100<br>Then, first no. = 100 &times; 39% = 39<br>and second no. = 100 &times; 65% = 65<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>65</mn></mrow></mfrac></math> &times; 100 = 60%</p>",
                    solution_hi: "<p>14.(c)<br>माना कि तीसरी संख्या 100 है<br>तो, पहली संख्या = 100 &times; 39% = 39<br>और दूसरी संख्या = 100 &times; 65% = 65<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>65</mn></mrow></mfrac></math> &times; 100 = 60%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In an election between two candidates, 15% of the votes were invalid and one candidate got 52% of the total valid votes. If the total number of votes was 8000, what was the number of valid votes that the other candidate got ?</p>",
                    question_hi: "<p>15. दो उम्मीदवारों के बीच एक चुनाव में, 15% मत अवैध थे और एक उम्मीदवार को कुल वैध मतों का 52% मत मिले। यदि मतों की कुल संख्या 8000 थी, तो दूसरे उम्मीदवार को मिले वैध मतों की संख्या क्या थी?</p>",
                    options_en: ["<p>3264</p>", "<p>6800</p>", 
                                "<p>3840</p>", "<p>3536</p>"],
                    options_hi: ["<p>3264</p>", "<p>6800</p>",
                                "<p>3840</p>", "<p>3536</p>"],
                    solution_en: "<p>15.(a) <br>Number of valid votes that the other candidate got <br>= 8000 &times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>15</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>52</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math><br>= 8000 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>100</mn></mfrac></math><br>= 3,264</p>",
                    solution_hi: "<p>15.(a) <br>दूसरे उम्मीदवार को मिले वैध वोटों की ,संख्या <br>= 8000 &times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>15</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mn>52</mn><mo>)</mo></mrow><mn>100</mn></mfrac></math><br>= 8000 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>100</mn></mfrac></math><br>= 3,264</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>