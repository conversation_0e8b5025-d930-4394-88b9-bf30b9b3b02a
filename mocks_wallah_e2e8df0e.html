<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the option that can be used as a one-word substitute for the given group of words. Morbid compulsion to consume alcohol continuously</p>",
                    question_hi: "<p>1. Select the option that can be used as a one-word substitute for the given group of words. Morbid compulsion to consume alcohol continuously</p>",
                    options_en: ["<p>Satyromania</p>", "<p>Maniac</p>", 
                                "<p>Kleptomania</p>", "<p>Dipsomania</p>"],
                    options_hi: ["<p>Satyromania</p>", "<p>Maniac</p>",
                                "<p>Kleptomania</p>", "<p>Dipsomania</p>"],
                    solution_en: "<p>1.(d) <strong>Dipsomania-</strong> morbid compulsion to consume alcohol continuously.<br><strong>Satyromania-</strong> a man\'s excessive and often uncontrollable sexual desire and behavior.<br><strong>Maniac-</strong> a person exhibiting extremely wild or violent behaviour.<br><strong>Kleptomania-</strong> a recurrent urge to steal, typically without regard for need or profit.</p>",
                    solution_hi: "<p>1.(d) <strong>Dipsomania</strong> (शराब की लत)- morbid compulsion to consume alcohol continuously.<br><strong>Satyromania</strong> (अनितंत्रित यौन इच्छा)- a man\'s excessive and often uncontrollable sexual desire and behavior.<br><strong>Maniac</strong> (पागल)- a person exhibiting extremely wild or violent behaviour.<br><strong>Kleptomania</strong> (चोरी करने की लत)- a recurrent urge to steal, typically without regard for need or profit.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the option that expresses the given sentence in active voice. <br>The painting was admired by art enthusiasts from around the world.</p>",
                    question_hi: "<p>2. Select the option that expresses the given sentence in active voice. <br>The painting was admired by art enthusiasts from around the world.</p>",
                    options_en: ["<p>The painting was being admired by art enthusiasts from around the world.</p>", "<p>Art enthusiasts from around the world admired the painting.</p>", 
                                "<p>Art enthusiasts from around the world have admired the painting.</p>", "<p>The painting has been admired by art enthusiasts from around the world.</p>"],
                    options_hi: ["<p>The painting was being admired by art enthusiasts from around the world.</p>", "<p>Art enthusiasts from around the world admired the painting.</p>",
                                "<p>Art enthusiasts from around the world have admired the painting.</p>", "<p>The painting has been admired by art enthusiasts from around the world.</p>"],
                    solution_en: "<p>2.(b) Art enthusiasts from around the world admired the painting. (Correct)<br>(a) The painting was being admired by art enthusiasts from around the world. (Incorrect Sentence Structure)<br>(c) Art enthusiasts from around the world <span style=\"text-decoration: underline;\">have admired</span> the painting. (Incorrect Tense)<br>(d) The painting has been admired by art enthusiasts from around the world. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>2.(b) Art enthusiasts from around the world admired the painting. (Correct)<br>(a) The painting was being admired by art enthusiasts from around the world. (गलत Sentence Structure)<br>(c) Art enthusiasts from around the world <span style=\"text-decoration: underline;\">have admired</span> the painting. (गलत Tense)<br>(d) The painting has been admired by art enthusiasts from around the world. (गलत Sentence Structure)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3.  Select the word segment that can substitute the bracketed word segment correctly and complete the sentence meaningfully. <br />(Each participant have) performed well. ",
                    question_hi: "3.  Select the word segment that can substitute the bracketed word segment correctly and complete the sentence meaningfully. <br />(Each participant have) performed well. ",
                    options_en: [" Each participant was", " Each participant has ", 
                                " Each participant have been", " Each participant were"],
                    options_hi: [" Each participant was", " Each participant has ",
                                " Each participant have been", " Each participant were"],
                    solution_en: "3.(b) Each participant has<br />‘Everyone/each/ every/ nothing/ everything/ anything/ either/ neither’ represents a singular noun. Therefore, singular verbs will be used with them. Hence, ‘Each participant has’ is the most appropriate answer.",
                    solution_hi: "3.(b) Each participant has<br />‘Everyone/each/ every/ nothing/ everything/ anything/ either/ neither’ singular noun को दर्शाता है। इसलिए, इनके साथ singular verbs का उपयोग किया जाएगा। अतः , ‘Each participant has’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate meaning of the underlined idiom. <br>Fatima was <span style=\"text-decoration: underline;\">at her wits&rsquo; end</span> trying to decide on a serene place for spending the holidays in.</p>",
                    question_hi: "<p>4. Select the most appropriate meaning of the underlined idiom. <br>Fatima was <span style=\"text-decoration: underline;\">at her wits&rsquo; end</span> trying to decide on a serene place for spending the holidays in.</p>",
                    options_en: ["<p>So worried, confused or annoyed that she does not know what to do next</p>", "<p>So worried, confused or annoyed that she became unconscious</p>", 
                                "<p>So calm and composed that she knows what to do next</p>", "<p>So worried, confused or annoyed that she insulted others for no reason</p>"],
                    options_hi: ["<p>So worried, confused or annoyed that she does not know what to do next</p>", "<p>So worried, confused or annoyed that she became unconscious</p>",
                                "<p>So calm and composed that she knows what to do next</p>", "<p>So worried, confused or annoyed that she insulted others for no reason</p>"],
                    solution_en: "<p>4.(a) <strong>At her wits&rsquo; end</strong>- so worried, confused or annoyed that she does not know what to do next.</p>",
                    solution_hi: "<p>4.(a) <strong>At her wits&rsquo; end-</strong> so worried, confused or annoyed that she does not know what to do next./इतना चिंतित होना की कोई निर्णय लेने में असमर्थ हो।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br>P. having the opportunity of observing people&rsquo;s <br>Q. we respect and appreciate their point of views <br>R. the advantage of travelling different places and <br>S. culture, custom and lifestyle, is that</p>",
                    question_hi: "<p>5. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br>P. having the opportunity of observing people&rsquo;s <br>Q. we respect and appreciate their point of views <br>R. the advantage of travelling different places and <br>S. culture, custom and lifestyle, is that</p>",
                    options_en: ["<p>RQPS</p>", "<p>PSQR</p>", 
                                "<p>RPSQ</p>", "<p>SQPR</p>"],
                    options_hi: ["<p>RQPS</p>", "<p>PSQR</p>",
                                "<p>RPSQ</p>", "<p>SQPR </p>"],
                    solution_en: "<p>5.(c) <strong>RPSQ</strong><br>The given sentence starts with Part R as it introduces the main idea of the sentence, i.e. the advantages of travelling. Part R will be followed by Part P as they are joined by the conjunction &lsquo;and&rsquo;. Further, Part S completes Part P by providing the nouns for the possessive <span style=\"text-decoration: underline;\">people&rsquo;s</span> &amp; Part Q states the advantage that we respect and appreciate their point of views. So, Q will follow S. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>5.(c) <strong>RPSQ</strong><br>दिया गया sentence, Part R से प्रारंभ होगा क्योंकि इसमे sentence का मुख्य विचार, &lsquo;the advantages of travelling&rsquo; शामिल है। Part R के बाद Part P आएगा क्योंकि ये conjunction &lsquo;and&rsquo; से जुड़े हुए हैं। इसके अलावा, Part S, possessive <span style=\"text-decoration: underline;\">people&rsquo;s</span> के लिए nouns प्रदान करके Part P को पूरा करता है और Part Q में यह advantage बताया गया है कि हम उनके विचारों का सम्मान करते हैं और उनकी सराहना करते हैं। इसलिए, S के बाद Q आएगा। Options के माध्यम से जाने पर, option \'c\' में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "6. Select the INCORRECTLY spelt word. ",
                    question_hi: "6. Select the INCORRECTLY spelt word. ",
                    options_en: [" Accommodate ", " Mischievious  ", 
                                " Millennium ", " Privilege"],
                    options_hi: [" Accommodate ", " Mischievious  ",
                                " Millennium ", " Privilege"],
                    solution_en: "6.(b) Mischievious<br />\'Mischievous\' is the correct spelling.",
                    solution_hi: "6.(b) Mischievious<br />\'Mischievous\' सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate synonym of the given word. <br>Taut</p>",
                    question_hi: "<p>7. Select the most appropriate synonym of the given word. <br>Taut</p>",
                    options_en: ["<p>Slack</p>", "<p>Tight</p>", 
                                "<p>Torn</p>", "<p>Flaccid</p>"],
                    options_hi: ["<p>Slack</p>", "<p>Tight</p>",
                                "<p>Torn</p>", "<p>Flaccid</p>"],
                    solution_en: "<p>7.(b) <strong>Tight-</strong> not loose.<br><strong>Taut-</strong> stretched or pulled tight.<br><strong>Slack-</strong> not tight or taut.<br><strong>Torn-</strong> having been pulled apart.<br><strong>Flaccid-</strong> lacking firmness or stiffness.</p>",
                    solution_hi: "<p>7.(b) <strong>Tight</strong> (कसा हुआ)- not loose.<br><strong>Taut</strong> (खींचा हुआ)- stretched or pulled tight.<br><strong>Slack</strong> (ढीला)- not tight or taut.<br><strong>Torn</strong> (फटा हुआ)- having been pulled apart.<br><strong>Flaccid</strong> (शिथिल/ढीला)- lacking firmness or stiffness</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the option that expresses the given sentence in active voice. <br>He will be made President by the people.</p>",
                    question_hi: "<p>8. Select the option that expresses the given sentence in active voice. <br>He will be made President by the people.</p>",
                    options_en: ["<p>People will be making him the President</p>", "<p>The people will make him President</p>", 
                                "<p>By the people, he shall be made the President.</p>", "<p>He will be made President by the people.</p>"],
                    options_hi: ["<p>People will be making him the President</p>", "<p>The people will make him President</p>",
                                "<p>By the people, he shall be made the President.</p>", "<p>He will be made President by the people. </p>"],
                    solution_en: "<p>8.(b) The people will make him President. (Correct)<br>(a) People <span style=\"text-decoration: underline;\">will be making</span> him the President. (Incorrect Verb)<br>(c) By the people, he shall be made the President. (Incorrect Sentence Structure)<br>(d) He will be made President by the people. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>8.(b) The people will make him President. (Correct)<br>(a) People <span style=\"text-decoration: underline;\">will be making</span> him the President. (गलत Verb)<br>(c) By the people, he shall be made the President. (गलत Sentence Structure)<br>(d) He will be made President by the people. (गलत Sentence Structure)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Which of the following idioms means &lsquo;to steal from a shop&rsquo;?</p>",
                    question_hi: "<p>9. Which of the following idioms means &lsquo;to steal from a shop&rsquo;?</p>",
                    options_en: ["<p>Golden opportunity</p>", "<p>Green thumb</p>", 
                                "<p>Five-finger discount</p>", "<p>Black sheep</p>"],
                    options_hi: ["<p>Golden opportunity</p>", "<p>Green thumb</p>",
                                "<p>Five-finger discount</p>", "<p>Black sheep</p>"],
                    solution_en: "<p>9.(c) <strong>Five-finger discount</strong>- to steal from a shop.<br>E.g.- He got caught trying to use a five-finger discount at the electronics store.</p>",
                    solution_hi: "<p>9.(c) <strong>Five-finger discount- </strong>to steal from a shop./ किसी दुकान से चोरी करना। <br>E.g.- He got caught trying to use a five-finger discount at the electronics store.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Parts of the following sentence have been given as options. Select the option that contains an error. <br />I are dancing with my husband at my sister’s wedding. ",
                    question_hi: "10. Parts of the following sentence have been given as options. Select the option that contains an error. <br />I are dancing with my husband at my sister’s wedding. ",
                    options_en: [" husband at my ", " sister’s wedding ", 
                                " with my  ", " I are dancing"],
                    options_hi: [" husband at my ", " sister’s wedding ",
                                " with my  ", " I are dancing"],
                    solution_en: "10.(d) I are dancing<br />‘Am’ is the correct helping verb to use with ‘I’ in present continuous tense. Hence, ‘I am dancing’ is the most appropriate answer.",
                    solution_hi: "10.(d) I are dancing<br />Present continuous tense में  ‘I’ के साथ प्रयोग करने के लिए सही helping verb ‘am’ होती है। अतः, ‘I am dancing’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "11. Parts of the following sentence have been given as options. Select the option that contains an error. <br />The tea at this café is best than at the Costa Café in our office.",
                    question_hi: "11. Parts of the following sentence have been given as options. Select the option that contains an error. <br />The tea at this café is best than at the Costa Café in our office.",
                    options_en: [" at the Costa Café", " The tea at this café  ", 
                                " is best than ", " in our office  "],
                    options_hi: [" at the Costa Café", " The tea at this café  ",
                                " is best than ", " in our office  "],
                    solution_en: "11.(c) is best than<br />‘Best’ must be replaced with ‘better’ as we use the comparative degree with ‘than’ when there is a comparison between two people or things. Hence, \'is better than\' is the most appropriate answer. ",
                    solution_hi: "11.(c) is best than<br />दिए गए sentence में ‘best’ के स्थान पर ‘better’ का प्रयोग होगा क्योंकि जब दो लोगों (people) या वस्तुओं (things) के बीच तुलना की जाती है तो ‘than’ के साथ comparative degree का प्रयोग किया जाता हैं। अतः, \'is better than\' सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate ANTONYM of the given word. <br>Miserable</p>",
                    question_hi: "<p>12. Select the most appropriate ANTONYM of the given word. <br>Miserable</p>",
                    options_en: ["<p>Melodious</p>", "<p>Settled</p>", 
                                "<p>Restrained</p>", "<p>Cheerful</p>"],
                    options_hi: ["<p>Melodious</p>", "<p>Settled</p>",
                                "<p>Restrained</p>", "<p>Cheerful</p>"],
                    solution_en: "<p>12.(d) <strong>Cheerful-</strong> noticeably happy and optimistic.<br><strong>Miserable-</strong> extremely unhappy or uncomfortable.<br><strong>Melodious-</strong> having a pleasant tune; harmonious.<br><strong>Settled-</strong> established in a particular place or condition.<br><strong>Restrained-</strong> kept under control.</p>",
                    solution_hi: "<p>12.(d) <strong>Cheerful</strong> (प्रसन्नचित्त)- noticeably happy and optimistic.<br><strong>Miserable</strong> (अत्यंत दुखी)- extremely unhappy or uncomfortable.<br><strong>Melodious</strong> (मधुर)- having a pleasant tune; harmonious.<br><strong>Settled</strong> (स्थिर)- established in a particular place or condition.<br><strong>Restrained</strong> (संयमित)- kept under control.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate synonym of the given word. <br>Confiscate</p>",
                    question_hi: "<p>13. Select the most appropriate synonym of the given word. <br>Confiscate</p>",
                    options_en: ["<p>Restock</p>", "<p>Destroy</p>", 
                                "<p>Seize</p>", "<p>Titivate</p>"],
                    options_hi: ["<p>Restock</p>", "<p>Destroy</p>",
                                "<p>Seize</p>", "<p>Titivate</p>"],
                    solution_en: "<p>13.(c) <strong>Seize-</strong> to take hold of suddenly and forcibly.<br><strong>Confiscate-</strong> to take or seize someone\'s property with authority.<br><strong>Restock-</strong> to replenish the supply of something.<br><strong>Destroy-</strong> to put an end to the existence of something.<br><strong>Titivate-</strong> to enhance appearance of.</p>",
                    solution_hi: "<p>13.(c) <strong>Seize</strong> (जब्त करना)- to take hold of suddenly and forcibly.<br><strong>Confiscate</strong> (जब्त करना)- to take or seize someone\'s property with authority.<br><strong>Restock</strong> (पुनःभंडार करना)- to replenish the supply of something.<br><strong>Destroy</strong> (नष्ट करना)- to put an end to the existence of something.<br><strong>Titivate</strong> (सजाना)- to enhance appearance of.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate meaning of the given word. <br>Edible</p>",
                    question_hi: "<p>14. Select the most appropriate meaning of the given word. <br>Edible</p>",
                    options_en: ["<p>noisome</p>", "<p>mortal</p>", 
                                "<p>noxious</p>", "<p>nutritive</p>"],
                    options_hi: ["<p>noisome</p>", "<p>mortal</p>",
                                "<p>noxious</p>", "<p>nutritive</p>"],
                    solution_en: "<p>14.(d) <strong>Nutritive-</strong> providing nutrition.<br><strong>Edible-</strong> fit or suitable to be eaten.<br><strong>Noisome-</strong> having an unpleasant smell.<br><strong>Mortal-</strong> subject to death.<br><strong>Noxious-</strong> harmful, poisonous, or very unpleasant.</p>",
                    solution_hi: "<p>14.(d) <strong>Nutritive</strong> (पौष्टिक)- providing nutrition.<br><strong>Edible</strong> (खाने योग्य)- fit or suitable to be eaten.<br><strong>Noisome</strong> (दुर्गंधयुक्त)- having an unpleasant smell.<br><strong>Mortal</strong> (नश्वर)- subject to death.<br><strong>Noxious</strong> (विषैला/हानिकारक)- harmful, poisonous, or very unpleasant.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>There are many factories in the <span style=\"text-decoration: underline;\">operative</span> area of the city.</p>",
                    question_hi: "<p>15. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>There are many factories in the <span style=\"text-decoration: underline;\">operative</span> area of the city.</p>",
                    options_en: ["<p>industrial</p>", "<p>serviceable</p>", 
                                "<p>industrious</p>", "<p>mechanical</p>"],
                    options_hi: ["<p>industrial</p>", "<p>serviceable</p>",
                                "<p>industrious</p>", "<p>mechanical</p>"],
                    solution_en: "<p>15.(a) industrial<br>&lsquo;Industrial&rsquo; means having a lot of factories. The given sentence states that there are many factories in the industrial area of the city. Hence, \'industrial\' is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(a) industrial<br>&lsquo;Industrial&rsquo; का अर्थ है बहुत सारे कारखाने होना। दिए गए sentence में बताया गया है कि शहर के औद्योगिक क्षेत्र (industrial area) में कई कारखाने हैं। अतः , \'industrial\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank. <br>The elaborate syllabi had to be ________(summarised) to make a presentation in the UGC assessment meeting.</p>",
                    question_hi: "<p>16. Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank. <br>The elaborate syllabi had to be ________(summarised) to make a presentation in the UGC assessment meeting.</p>",
                    options_en: ["<p>expanded</p>", "<p>recapitulated</p>", 
                                "<p>prolonged</p>", "<p>lengthened</p>"],
                    options_hi: ["<p>expanded</p>", "<p>recapitulated</p>",
                                "<p>prolonged</p>", "<p>lengthened</p>"],
                    solution_en: "<p>16.(b) <strong>Recapitulated-</strong> restated or summarized the main points.<br><strong>Summarised-</strong> presented in a concise form.<br><strong>Expanded-</strong> increased in size, volume, or scope.<br><strong>Prolonged-</strong> extended in duration or time.<br><strong>Lengthened-</strong> made longer in time or space.</p>",
                    solution_hi: "<p>16.(b) <strong>Recapitulated</strong> (पुनरावलोकन)- restated or summarized the main points.<br><strong>Summarised</strong> (सारांशित)- presented in a concise form.<br><strong>Expanded</strong> (विस्तारित)- increased in size, volume, or scope.<br><strong>Prolonged</strong> (दीर्घकालीन)- extended in duration or time.<br><strong>Lengthened</strong> (लंबा करना)- made longer in time or space.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. Identify the INCORRECTLY spelt word in the following sentence and select its correct spelling from the given options.<br />I receieved a parcel in the mail today. ",
                    question_hi: "17. Identify the INCORRECTLY spelt word in the following sentence and select its correct spelling from the given options.<br />I receieved a parcel in the mail today. ",
                    options_en: [" received", " recived", 
                                " recieved ", " receaved"],
                    options_hi: [" received", " recived",
                                " recieved ", " receaved"],
                    solution_en: "17.(a) received<br />\'Received\' is the correct spelling.",
                    solution_hi: "17.(a) received<br />\'Received\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the option that can be used as a one-word substitute for the underlined group of words. <br>The question papers are placed in the lockers of the school in a way that they <span style=\"text-decoration: underline;\">can&rsquo;t be reached</span>.</p>",
                    question_hi: "<p>18. Select the option that can be used as a one-word substitute for the underlined group of words. <br>The question papers are placed in the lockers of the school in a way that they <span style=\"text-decoration: underline;\">can&rsquo;t be reached</span>.</p>",
                    options_en: ["<p>are irrepressible</p>", "<p>are inaccessible</p>", 
                                "<p>are affordable</p>", "<p>are affable</p>"],
                    options_hi: ["<p>are irrepressible</p>", "<p>are inaccessible</p>",
                                "<p>are affordable</p>", "<p>are affable</p>"],
                    solution_en: "<p>18.(b)<strong> are inaccessible-</strong> can&rsquo;t be reached.<br><strong>are irrepressible</strong>- not able to be controlled or restrained.<br><strong>are affordable-</strong> reasonably priced.<br><strong>are affable- </strong>friendly, good-natured, or easy to talk to.</p>",
                    solution_hi: "<p>18.(b) <strong>are inaccessible (</strong>दुर्गम)- can&rsquo;t be reached.<br><strong>are irrepressible</strong> (अदम्य )- not able to be controlled or restrained.<br><strong>are affordable </strong>(किफायती)- reasonably priced.<br><strong>are affable</strong> (मैत्रीपूर्ण)- friendly, good-natured, or easy to talk to.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "19. Select the most appropriate homophone to fill in the blank. <br />He has the ________ to express his thoughts.",
                    question_hi: "19. Select the most appropriate homophone to fill in the blank. <br />He has the ________ to express his thoughts.",
                    options_en: [" right ", " ride", 
                                " write", " rite"],
                    options_hi: [" right ", " ride",
                                " write", " rite"],
                    solution_en: "19.(a) right<br />The given sentence states that he has the right to express his thoughts. Hence, ‘right’ is the most appropriate answer.",
                    solution_hi: "19.(a) right<br />दिए गए sentence में कहा गया है कि उसे अपने विचार व्यक्त करने का अधिकार है। अतः, ‘right’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the option that can be used as a one-word substitute for the given group of words. An order of law requiring people to remain indoors</p>",
                    question_hi: "<p>20. Select the option that can be used as a one-word substitute for the given group of words. An order of law requiring people to remain indoors</p>",
                    options_en: ["<p>Restriction</p>", "<p>Curfew</p>", 
                                "<p>Limit</p>", "<p>Check in</p>"],
                    options_hi: ["<p>Restriction</p>", "<p>Curfew</p>",
                                "<p>Limit</p>", "<p>Check in</p>"],
                    solution_en: "<p>20.(b) <strong>Curfew-</strong> an order of law requiring people to remain indoors.<br><strong>Restriction-</strong> an official limit or control on what people or companies are allowed to do, or on what can happen.<br><strong>Limit-</strong> a point or level beyond which something does not or may not extend or pass.<br><strong>Check in-</strong> arrive and register at a hotel or airport.</p>",
                    solution_hi: "<p>20.(b) <strong>Curfew</strong> (कर्फ़्यू/निषेधाज्ञा)- an order of law requiring people to remain indoors.<br><strong>Restriction</strong> (प्रतिबंध)- an official limit or control on what people or companies are allowed to do, or on what can happen.<br><strong>Limit</strong> (सीमा)- a point or level beyond which something does not or may not extend or pass.<br><strong>Check in</strong> (आगमन)- arrive and register at a hotel or airport.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.<strong> Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend. <br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21.<strong> Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>market</p>", "<p>area</p>", 
                                "<p>zone</p>", "<p>field</p>"],
                    options_hi: ["<p>market</p>", "<p>area</p>",
                                "<p>zone</p>", "<p>field</p>"],
                    solution_en: "<p>21.(a) market<br>&lsquo;Market&rsquo; means places in which things are bought and sold. The given passage states that tea prices in the domestic market continue to rule high in the current year. Hence, \'market\' is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(a) market<br>&lsquo;Market&rsquo; का मतलब है वह स्थान जहाँ चीज़ें खरीदी और बेची जाती हैं। दिए गए passage में कहा गया है कि घरेलू बाजार में चाय की कीमतें वर्तमान वर्ष (current year) में भी उच्च स्तर पर बनी हुई हैं। अतः, \'market\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22.<strong> Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>aligning</p>", "<p>favouring</p>", 
                                "<p>countering</p>", "<p>encountering</p>"],
                    options_hi: ["<p>aligning</p>", "<p>favouring</p>",
                                "<p>countering</p>", "<p>encountering</p>"],
                    solution_en: "<p>22.(a) aligning<br>&lsquo;Aligning&rsquo; means to be the same or similar to each other. The given passage states that during the past three months, tea prices have been aligning with the prices of other companies. Hence, \'aligning\' is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) aligning<br>&lsquo;Aligning&rsquo; का अर्थ है एक दूसरे के समान या अनुरूप होना। दिए गए passage में कहा गया है कि पिछले तीन महीनों के दौरान, चाय की कीमतें अन्य कंपनियों की कीमतों के अनुरूप रही हैं। अतः , \'aligning\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend. <br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23.<strong> Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>an inflow</p>", "<p>an innate</p>", 
                                "<p>a reduction</p>", "<p>an uptrend</p>"],
                    options_hi: ["<p>an inflow</p>", "<p>an innate</p>",
                                "<p>a reduction</p>", "<p>an uptrend</p>"],
                    solution_en: "<p>23.(d) an uptrend<br>&lsquo;Uptrend&rsquo; means a gradual increase in the price or value of something. The given passage states that unlike last year, tea prices have generally shown an uptrend. Hence, \'an uptrend\' is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(d) an uptrend<br>&lsquo;Uptrend&rsquo; का अर्थ है किसी चीज़ की कीमत या मूल्य में क्रमिक वृद्धि। दिए गए passage में कहा गया है कि पिछले वर्ष के विपरीत, चाय की कीमतों में आम तौर पर बढ़ोत्तरी देखी गई है।अतः \'an uptrend\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>stabilised</p>", "<p>moderated</p>", 
                                "<p>equated</p>", "<p>abated</p>"],
                    options_hi: ["<p>stabilised</p>", "<p>moderated</p>",
                                "<p>equated</p>", "<p>abated</p>"],
                    solution_en: "<p>24.(a) stabilised<br>&lsquo;Stabilised&rsquo; means become fixed or stopped changing. The given passage states that this year, prices seem to have stabilised at a rather high level. Hence, \'stabilised\' is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(a) stabilised<br>&lsquo;Stabilised&rsquo; का अर्थ है स्थिर हो जाना। दिए गए passage में कहा गया है कि इस वर्ष, कीमतें काफी उच्च स्तर पर स्थिर हो गई हैं। अतः , \'stabilised\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25. <strong>Cloze Test:</strong> <br>Tea prices in the domestic (21)__________ continue to rule high in the current year despite the expectation of a higher production as compared to the previous year. During the past three months, tea prices have been (22)__________ with the prices of other companies. Unlike last year, tea prices have generally shown (23) __________ , when tea prices rose dramatically, this year, prices seem to have (24) __________ at a rather high level. In the subsequent four months, the (25)__________ average price showed a downtrend.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>monthly</p>", "<p>half yearly</p>", 
                                "<p>weekly</p>", "<p>yearly</p>"],
                    options_hi: ["<p>monthly</p>", "<p>half yearly</p>",
                                "<p>weekly</p>", "<p>yearly</p>"],
                    solution_en: "<p>25.(a) monthly<br>&lsquo;Monthly&rsquo; means calculated over a month. The given passage states that the monthly average price showed a downtrend. Hence, \'monthly\' is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(a) monthly<br>&lsquo;Monthly&rsquo; का अर्थ है एक महीने की गणना। दिए गए passage में बताया गया है कि मासिक औसत मूल्य में गिरावट देखी गई। अतः , \'monthly\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>