<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The distribution of total 1500 students in a school for different games is shown in the&nbsp;following figure. Study the given figure carefully and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834520599.png\" alt=\"rId4\" width=\"228\" height=\"236\"> <br>Find the total number of students selected for cricket and tennis.</p>",
                    question_hi: "<p>1. एक विद्यालय में विभिन्न खेलों के लिए कुल 1500 छात्रों का वितरण निम्नलिखित आकृति में दर्शाया गया&nbsp;है। दी गई आकृति का ध्यानपूर्वक अध्ययन कीजिए और आगे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834520753.png\" alt=\"rId5\" width=\"207\" height=\"236\"> <br>क्रिकेट और टेनिस के लिए चयनित छात्रों की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>825</p>", "<p>625</p>", 
                                "<p>525</p>", "<p>415</p>"],
                    options_hi: ["<p>825</p>", "<p>625</p>",
                                "<p>525</p>", "<p>415</p>"],
                    solution_en: "<p>1.(a) <br>Number of student selected for cricket and tennis = 1500 &times; (35 + 20)%<br>= 1500 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 825</p>",
                    solution_hi: "<p>1.(a) <br>क्रिकेट और टेनिस के लिए चयनित छात्रों की संख्या = 1500 &times; (35 + 20)%<br>= 1500 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 825</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Study the given graph and answer the question that follows.<br>The graph shows the exports from three companies (A, B and C) over the years (in ₹ crore).<br><strong id=\"docs-internal-guid-24978736-7fff-ef9b-5de0-510740bac5ad\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf_AfgAR5LeiYJLt_ErqkbBHVKGP5oycdX7nbjZum1STJmHX1YIR7_LzBfGi6zygguuhMMQES7VRC86xzIWrVjGZBhwChucaEvMk9sUKkvFU3l9qKIdPALZecWqG1q-b9tstVba?key=OXejARi3rfDReVD9iicPZiml\" width=\"434\" height=\"260\"></strong><br>In which year was the difference between the exports from companies A and B the minimum?</p>",
                    question_hi: "<p>2. दिए गए ग्राफ का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। <br>ग्राफ तीन कंपनियों (A, B और C) का कुछ वर्षों में (₹ करोड़ में) निर्यात दर्शाता है।<br><strong id=\"docs-internal-guid-24978736-7fff-ef9b-5de0-510740bac5ad\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf_AfgAR5LeiYJLt_ErqkbBHVKGP5oycdX7nbjZum1STJmHX1YIR7_LzBfGi6zygguuhMMQES7VRC86xzIWrVjGZBhwChucaEvMk9sUKkvFU3l9qKIdPALZecWqG1q-b9tstVba?key=OXejARi3rfDReVD9iicPZiml\" width=\"434\" height=\"260\"></strong><br>किस वर्ष कंपनी A और B से निर्यात के बीच का अंतर न्यूनतम था?</p>",
                    options_en: ["<p>1994</p>", "<p>1998</p>", 
                                "<p>1995</p>", "<p>1997</p>"],
                    options_hi: ["<p>1994</p>", "<p>1998</p>",
                                "<p>1995</p>", "<p>1997</p>"],
                    solution_en: "<p>2.(b) <br>By checking options<br>Difference between the exports from company A and B <br>In 1994 :- do not have data<br>In 1998 :- (152 - 95) = 57 crore<br>In 1995 :- (125 - 60) = 65 crore<br>In 1997 :- (145 - 80) = 65 crore<br>In 1998 required difference is minimum</p>",
                    solution_hi: "<p>2.(b) <br>विकल्पों की जाँच करने पर <br>कंपनी A और B के निर्यात के बीच अंतर <br>1994 में :- डेटा नहीं है<br>1998 में :- (152 - 95) = 57 करोड़<br>1995 में :- (125 - 60) = 65 करोड़<br>1997 में :- (145 - 80) = 65 करोड़<br>1998 में आवश्यक अंतर न्यूनतम है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The given bar graph shows the number of accidents in a city during the first 6 months of a year. Examine the bar graph and answer the question.<br><strong id=\"docs-internal-guid-95f28278-7fff-1474-c5d8-232e7f50cd59\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXedmhbHCS6ult_bj5QcUrDeKjv-YfRaTI5leUAZ2s_lXiQxBF7RcPO3SJUKFQMf2IbGXl8FzM_H8tIWanICdwWXI3nr-Gx1pEhO183c09ViqEqi_oe-pZ1mlprzHFMgtQSqz7QH?key=OXejARi3rfDReVD9iicPZiml\" width=\"335\" height=\"275\"></strong><br>What is the percentage of accidents in the month of April with respect to the total accidents in the city during six months ? (Correct to nearest integer)</p>",
                    question_hi: "<p>3. दिया गया बार ग्राफ एक वर्ष के पहले 6 महीनों के दौरान एक शहर में होने वाली दुर्घटनाओं की संख्या को दर्शाता है। बार ग्राफ का अवलोकन कीजिए और उसके बाद दिए गए प्रश्न का उत्तर दीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834521152.png\" alt=\"rId8\" width=\"315\" height=\"258\"> <br>शहर में छ: माह के दौरान हुई कुल दुर्घटनाओं के सापेक्ष में अप्रैल माह में हुई दुर्घटनाओं का प्रतिशत कितना है? (निकटतम पूर्णांक तक पूर्णांकित)</p>",
                    options_en: ["<p>22%</p>", "<p>15%</p>", 
                                "<p>20%</p>", "<p>24%</p>"],
                    options_hi: ["<p>22%</p>", "<p>15%</p>",
                                "<p>20%</p>", "<p>24%</p>"],
                    solution_en: "<p>3.(d)<br>Total accidents in the city during 6 months = 25 + 19 + 30 + 43 + 35 + 27 = 179<br>Accidents in month of April = 43<br>required % = <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>179</mn></mrow></mfrac></math> &times; 100 = 24.02 (approx 24%)</p>",
                    solution_hi: "<p>3.(d)<br>6 माह के दौरान शहर में कुल दुर्घटनाएँ = 25 + 19 + 30 + 43 + 35 + 27 = 179<br>अप्रैल माह की दुर्घटनाएँ = 43<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>179</mn></mrow></mfrac></math> &times; 100 = 24.02 (लगभग 24%)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The following bar graph represents the population of a city (in lakhs) as per the census across different years<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834521262.png\" alt=\"rId9\" width=\"316\" height=\"237\"> <br>What is the percent increase in population from 1951 to 2001?</p>",
                    question_hi: "<p>4. निम्नलिखित बार ग्राफ विभिन्न वर्षों में जनगणना के अनुसार किसी शहर की जनसंख्या (लाख में) का प्रतिनिधित्व करता है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834521262.png\" alt=\"rId9\" width=\"316\" height=\"237\"><br>1951 से 2001 तक जनसंख्या में कितने प्रतिशत की वृद्धि हुई?</p>",
                    options_en: ["<p>35%</p>", "<p>22%</p>", 
                                "<p>144%</p>", "<p>120%</p>"],
                    options_hi: ["<p>35%</p>", "<p>22%</p>",
                                "<p>144%</p>", "<p>120%</p>"],
                    solution_en: "<p>4.(c) <br>required% = <math display=\"inline\"><mfrac><mrow><mn>61</mn><mo>-</mo><mn>25</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 144%</p>",
                    solution_hi: "<p>4.(c) <br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>61</mn><mo>-</mo><mn>25</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 144%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The pie chart, given here, shows the various expenses of a publisher in the production of a magazine.<br><strong id=\"docs-internal-guid-7ea6267a-7fff-5f4f-f06a-83aa7006dbb1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfnirLzWhDZ3bjtprbLf5Xz_XhuDMKRZnwRsPTXnKWL1cxmGJNtNoNkYZ1OSsl6Cyys-eb18ZDvaSWxaFltjz8cn1SsLl2i12kiwnJbhU0rprFMZEc4PQTlj_wNL2IVSHMXClXciw?key=OXejARi3rfDReVD9iicPZiml\" width=\"324\" height=\"242\"></strong><br>What is the difference between the measures of the central angles of sectors for binding charges and paper cost ?</p>",
                    question_hi: "<p>5. दिया गया वृत्त आलेख एक पत्रिका को तैयार करने में प्रकाशक के विभिन्न व्ययों को दर्शाता है।<br><strong id=\"docs-internal-guid-b6981d32-7fff-70b8-90ed-8d62150fd2bc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfnrlgej8f9e0Gt4pznCIEDqbQgHPVwjkbhmZMg8SKCHz-CP0hpCn3g-cjr7kAOz3Ek5tfaWNhTA_RX_4bN3CS2tS0P4XqZO8Z4OM4aP-0DNCNeTZqzQZlpBxQz8R4-ae5_Wd6N?key=OXejARi3rfDReVD9iicPZiml\" width=\"289\" height=\"255\"></strong><br>बाइंडिंग चार्ज और कागज की लागत के लिए त्रिज्यखंडों (sectors) के केंद्रीय कोणों की माप के बीच कितना अंतर है?</p>",
                    options_en: ["<p>27.2&deg;</p>", "<p>28.6&deg;</p>", 
                                "<p>27.6&deg;</p>", "<p>28.8&deg;</p>"],
                    options_hi: ["<p>27.2&deg;</p>", "<p>28.6&deg;</p>",
                                "<p>27.6&deg;</p>", "<p>28.8&deg;</p>"],
                    solution_en: "<p>5.(d) <br>difference between the measures of the central angles of sectors for binding charges and paper cost = 18 - 10 = 8%<br>(total) 100% = 360&deg;<br>(difference) 8% = <math display=\"inline\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 8 = 28.8&deg;</p>",
                    solution_hi: "<p>5.(d) <br>बाइंडिंग चार्ज और कागज की लागत के लिए सेक्टरों के केंद्रीय कोणों के माप के बीच अंतर = 18 - 10 = 8%<br>(कुल) 100% = 360&deg;<br>(अंतर) 8% = <math display=\"inline\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 8 = 28.8&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Study the given table and select the most appropriate option to fill in the blanks.<br>Consider the data of sales of products, A, B and C, by three companies, X, Y and Z, in the table given below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834521669.png\" alt=\"rId12\" width=\"272\" height=\"120\"> <br>The highest average sale per company is for the product_____ and the lowest average sale per product is for the company_____.</p>",
                    question_hi: "<p>6. दी गई तालिका का अध्ययन कीजिए और रिक्त स्थान को भरने के लिए सबसे उपयुक्त विकल्प का चयन कीजिए।<br>नीचे दी गई तालिका में तीन कंपनियों, X, Y और Z द्वारा उत्पादों, A, B और C की बिक्री के आँकड़ों पर विचार कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834521875.png\" alt=\"rId13\" width=\"265\" height=\"126\"> <br>प्रति कंपनी उच्चतम औसत बिक्री उत्पाद_____ के लिए है और प्रति उत्पाद न्यूनतम औसत बिक्री कंपनी _____के लिए है।</p>",
                    options_en: ["<p>B; Z</p>", "<p>B; Y</p>", 
                                "<p>A; Y</p>", "<p>A; X</p>"],
                    options_hi: ["<p>B; Z</p>", "<p>B; Y</p>",
                                "<p>A; Y</p>", "<p>A; X</p>"],
                    solution_en: "<p>6.(b)<br>average sale of the product A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2500</mn><mo>+</mo><mi>&#160;</mi><mn>3500</mn></mrow><mn>3</mn></mfrac></math> = 2916.66<br>Average sale of the product B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3250</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3000</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3150</mn></mrow><mn>3</mn></mfrac></math> = 3133.33<br>Average sale of the product C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2250</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2400</mn></mrow><mn>3</mn></mfrac></math> = 2133.33<br>We can clearly see that the product B has the highest selling product.<br>average sale of the company X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3250</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2250</mn></mrow><mn>3</mn></mfrac></math> = 2750<br>Average sale of the company Y = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3000</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1750</mn></mrow><mn>3</mn></mfrac></math> = 2416.66<br>Average sale of the company Z = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3500</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2400</mn></mrow><mn>3</mn></mfrac></math> = 3016.66<br>We can clearly see that company Y has the lowest sales.</p>",
                    solution_hi: "<p>6.(b)<br>उत्पाद A की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>2750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2500</mn><mo>+</mo><mi>&#160;</mi><mn>3500</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 2916.66<br>उत्पाद B की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>3250</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3000</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3150</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 3133.33<br>उत्पाद C की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>2250</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2400</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 2133.33<br>हम स्पष्ट रूप से देख सकते हैं कि उत्पाद B सबसे अधिक बिकने वाला उत्पाद है।<br>कंपनी X की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>2750</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3250</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2250</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 2750<br>कंपनी Y की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>2500</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3000</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1750</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 2416.66<br>कंपनी Z की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>3500</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2400</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 3016.66<br>हम स्पष्ट रूप से देख सकते हैं कि कंपनी Y की बिक्री सबसे कम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <br><strong id=\"docs-internal-guid-33bcc44b-7fff-22aa-8434-6ce856cc8f0e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeaIbjEqJJwaaeDlC36bnZB1SO0maygdO-7gN9yK_j4fmNHpVz9oTRJPXN4_lAEa2fMxU49fW6GioIC39Hb0flnUYZQyhZPsPV9ussh5T28f61Lqu4oG6dIOI8k-IKci3s-OZBj?key=OXejARi3rfDReVD9iicPZiml\" width=\"291\" height=\"166\"></strong><br>A company starts constructing buildings for a college. The constructions have 4 stages.<br>1.buildings proposals<br>2. proposals approval<br>3.constructions implemented<br>4.buildings completed<br>The above 3D-bar graph indicates the status of constructions for 4 years 2015 to 2018.<br>The X-axis scale 0,2,4,6,8,10 indicates 0, 20%, 40%, 60%, 80%, 100%.<br>Study the bar graph carefully and answer the following question.<br>Find the total percentage of buildings completed during the total period, 2015 to 2018.</p>",
                    question_hi: "<p>7.<br><strong id=\"docs-internal-guid-1c9fe042-7fff-5e6e-2ee4-9f4bebe1f51f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUIUbq2GnTdK17H4IDGiQLVbF19SifT88uuwHRA87fdg_gYxJ7F-TJBDUI6hh8IuJNc86GwLv2dShW9SHrYfToBwGaEgE40I5Iqgn40KgOHijp1_dqjpQtjGduuhXWGcqHYJqxBQ?key=OXejARi3rfDReVD9iicPZiml\" width=\"299\" height=\"183\"></strong><br>एक कंपनी एक कॉलेज के लिए भवनों का निर्माण शुरू करती है। निर्माण के 4 चरण हैं।<br>1. भवनों के प्रस्ताव 2. प्रस्तावों की स्वीकृति 3. निर्माण कार्यान्वित 4. भवन पूर्ण<br>उपरोक्त 3D-दंड आलेख 4 वर्ष, 2015 से 2018 के लिए निर्माण की स्थिति को दर्शाता है।<br>X-अक्ष पर पैमाना 0, 2, 4, 6, 8, 10 क्रमशः 0, 20%, 40%, 60%, 80%, 100% को इंगित करता है।<br>दंड आलेख का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>2015 से 2018 तक की कुल अवधि के दौरान भवनों के पूर्ण होने का कुल प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>50%</p>", "<p>30%</p>", 
                                "<p>40%</p>", "<p>60%</p>"],
                    options_hi: ["<p>50%</p>", "<p>30%</p>",
                                "<p>40%</p>", "<p>60%</p>"],
                    solution_en: "<p>7.(a)<br>total percentage of buildings completed during the total period, 2015 to 2018 is 50%</p>",
                    solution_hi: "<p>7.(a)<br>2015 से 2018 की कुल अवधि के दौरान पूर्ण की गई इमारतों का कुल प्रतिशत 50% है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The given table shows the number of candidates who have appeared, qualified and been selected in an examination from two states A and B over the years 2001 to 2005.<br><strong id=\"docs-internal-guid-74167b6a-7fff-75c1-abc6-a8f1c76ffbb7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcxjtTqyF616L5OCRkV6Ju0WF0RpGDvCAUkvAa77MIwgQs6TAUla5Vb0kLmVAKmOwtdcNJOZ9gDcbaLgzWCoDsUXTlyiUP3lugLWRz-3TLx2ncuwb1kuvzMkKZjAJmf-IoWPVjKlg?key=OXejARi3rfDReVD9iicPZiml\" width=\"451\" height=\"154\"></strong><br>In the year 2001, the percentage of candidates who were selected over the candidates who applied in State A is:</p>",
                    question_hi: "<p>8. दी गई तालिका वर्ष 2001 से 2005 के दौरान दो राज्यों A और B से एक परीक्षा में उपस्थित, उत्तीर्ण और चयनित होने वाले उम्मीदवारों की संख्या को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834522641.png\" alt=\"rId17\" width=\"363\" height=\"148\"> <br>वर्ष 2001 में राज्य A में उपस्थित होने वाले उम्मीदवारों की तुलना में चयनित उम्मीदवारों का प्रतिशत है :</p>",
                    options_en: ["<p>1.175%</p>", "<p>1.185%</p>", 
                                "<p>1.155%</p>", "<p>1.165%</p>"],
                    options_hi: ["<p>1.175%</p>", "<p>1.185%</p>",
                                "<p>1.155%</p>", "<p>1.165%</p>"],
                    solution_en: "<p>8.(a)<br>required% = <math display=\"inline\"><mfrac><mrow><mn>94</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 1.175%</p>",
                    solution_hi: "<p>8.(a)<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>94</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 1.175%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Study the given bar-graph and answer the question that follows.<br>The bar-graph shows the production of refrigerators (in thousand) by five different companies A, B, C, D and E during 2004 to 2007.<br><strong id=\"docs-internal-guid-1e14d7ad-7fff-ce7f-a05c-e7618d86b270\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcfqLJgBn6wHT47Ax3cfzIgb6jyVvrm9pf2Pp7PkiYwgj-U5-Ifz7fyVuohVtCxQYwkCanKG4BE61ZOgWYTKJjLkofzGO3ABWN927AgZ1NFQwU4D6SOu93LHFldi7Az5hGudJVd?key=OXejARi3rfDReVD9iicPZiml\" width=\"399\" height=\"274\"></strong><br>What is the ratio of the average production of the refrigerators by companies A, B and E taken together for the year 2004-2005 to the average production of the refrigerators by companies C and D taken together for the year 2005-2006?</p>",
                    question_hi: "<p>9. दिए गए बार ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br>बार ग्राफ में 2004 से 2007 के दौरान पाँच अलग-अलग कंपनियों A, B, C, D और E द्वारा रेफ्रिजरेटर के उत्पादन (हजार में) को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834522938.png\" alt=\"rId19\" width=\"372\" height=\"254\"> <br>कंपनी A, B और E द्वारा मिलाकर वर्ष 2004-2005 में रेफ्रिजरेटर के औसत उत्पादन तथा वर्ष 2005-2006 में कंपनी C और D द्वारा मिलाकर रेफ्रिजरेटर के औसत उत्पादन का अनुपात कितना है?</p>",
                    options_en: ["<p>43 : 19</p>", "<p>41 : 19</p>", 
                                "<p>19 : 41</p>", "<p>19 : 43</p>"],
                    options_hi: ["<p>43 : 19</p>", "<p>41 : 19</p>",
                                "<p>19 : 41</p>", "<p>19 : 43</p>"],
                    solution_en: "<p>9.(d) <br>Average production of the refrigerators by companies A, B and E together for the year <br>2004 - 2005 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>110</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>160</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>300</mn></mrow><mn>3</mn></mfrac></math> = 190<br>Average production of the refrigerators by companies C and D together for the year <br>2005 - 2006 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>370</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>490</mn></mrow><mn>3</mn></mfrac></math> = 430<br>Required ratio = 190 : 430 = 19 : 43</p>",
                    solution_hi: "<p>9.(d) <br>वर्ष 2004 - 2005 के लिए कंपनियों A, B और E द्वारा रेफ्रिजरेटर का औसत उत्पादन<br>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>110</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>160</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>300</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 190<br>वर्ष 2005 - 2006 के लिए कंपनी C और D द्वारा रेफ्रिजरेटर का औसत उत्पादन<br>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>370</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>490</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 430<br>आवश्यक अनुपात = 190 : 430 = 19 : 43</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The given line graph shows the number of scooters manufactured (in thousands) by companies X and Z, over the years. <br><strong id=\"docs-internal-guid-2818680c-7fff-d9ef-1339-98905302294f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfhRCoopn__leUCRSbaL8Vjlm6rH_Xm-mItl4QuWAySP5zm8k1_NdENwRNPa8SW2wd7XH7v8HA6T-hy3ot9kg2imenmm-rPJ10pbW4y8d_tededYecrx2_FnYrxlQjWqqjmxI5QAQ?key=OXejARi3rfDReVD9iicPZiml\" width=\"402\" height=\"263\"></strong><br>In which of the following years was the difference between the production by companies X and Z the maximum among the given years ?</p>",
                    question_hi: "<p>10. दिया गया लाइन ग्राफ पिछले वर्षों में कंपनी X और Z द्वारा निर्मित स्कूटरों की संख्या (हजार में) को दर्शाता है। <br><strong id=\"docs-internal-guid-01f95d86-7fff-f687-9300-51a6f65d736b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfcqef9N95cVybQs2TX1I1Ty2D1f5QnHKLE_fa4MtA5cZjF5vS7oY2L9drllh3CDgEUl9mFR_QKJwqDmMExRRxCkuTCsQW9X2Q9qf9agnXYg3550m0TqYbArBdNVO1ClrvWEl8NXg?key=OXejARi3rfDReVD9iicPZiml\" width=\"385\" height=\"251\"></strong><br>दिए गए वर्षों में से निम्नलिखित में से किस वर्ष में कंपनियों X और Z के उत्पादन के बीच का अंतर अधिकतम था?</p>",
                    options_en: ["<p>2019</p>", "<p>2020</p>", 
                                "<p>2017</p>", "<p>2018</p>"],
                    options_hi: ["<p>2019</p>", "<p>2020</p>",
                                "<p>2017</p>", "<p>2018</p>"],
                    solution_en: "<p>10.(c)<br>By checking options<br>Difference between the production by companies X and Z <br>In 2019:- 130 - 80 = 50 <br>In 2020:- 122 - 109 = 13 <br>In 2017:- 192 - 110 = 82 <br>In 2018:- 145 - 102 = 43 <br>In 2017 required difference is maximum (i.e. 82 thousands)</p>",
                    solution_hi: "<p>10.(c)<br>विकल्पों की जाँच करके<br>कंपनी X और Z द्वारा उत्पादन के बीच अंतर <br>2019 में:- 130 - 80 = 50 <br>2020 में:- 122 - 109 = 13 <br>2017 में:- 192 - 110 = 82 <br>2018 में:- 145 - 102 = 43 <br>2017 में आवश्यक अंतर अधिकतम (यानि 82 हजार) हैI</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Read the given information and answer the question that follows.<br>The following table gives the percentage of marks obtained by seven students in six different subjects in an examination.<br>The number in the brackets gives the maximum marks in each subject.<br><strong id=\"docs-internal-guid-4dc9486a-7fff-f519-ca84-067ec72c4674\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUH5vnK5FP_cHGwroPZ_auC14NwUokGqOJSyeg4hI8Ee4GKpzuY-pL95G26pFyfhRZ_rBiSAgUJ1NX33HzqR1p4y6YxFNyXTdLqGjAch_MFEUjXbTgsMZCWDdjmOAOrtfZ1BfTLA?key=OXejARi3rfDReVD9iicPZiml\" width=\"450\" height=\"163\"></strong><br>How many students secured a raw score of at least 30 in computer science ?</p>",
                    question_hi: "<p>11. दी गई जानकारी को ध्यानपूर्वक पढ़ें और आगे दिए गए प्रश्न का उत्तर दें।<br>नीचे दी गई तालिका में एक परीक्षा के छह अलग-अलग विषयों में सात वि&zwnj;द्यार्थियों द्वारा प्राप्त अंकों का प्रतिशत दर्शाया गया है।<br>कोष्ठक में दी गई संख्या प्रत्येक विषय के अधिकतम अंक को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834523480.png\" alt=\"rId23\" width=\"389\" height=\"195\"> <br>कितने विद्यार्थियों ने कंप्यूटर साइंस में कम से कम 30 का यथा प्राप्त समंक (Raw Score) हासिल किया?</p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>3</p>", "<p>5</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>3</p>", "<p>5</p>"],
                    solution_en: "<p>11.(c)<br>Ayush get the marks in CS = 40 &times; 80% = 32<br>Aman get the marks in CS = 40 &times; 70% = 28<br>Sajal get the marks in CS = 40 &times; 70% = 28<br>Rohit get the marks in CS = 40 &times; 60% = 24<br>Muskan get the marks in CS = 40 &times; 90% = 36<br>Tanvi get the marks in CS = 40 &times; 60% = 24<br>Tarun get the marks in CS = 40 &times; 80% = 32<br>So, 3 students scored at least 30 marks in CS.</p>",
                    solution_hi: "<p>11.(c)<br>आयुष को कंप्यूटर साइंस में अंक मिले = 40 &times; 80% = 32<br>अमन को कंप्यूटर साइंस में अंक प्राप्त हुए = 40 &times; 70% = 28<br>सजल को कंप्यूटर साइंस में अंक मिले = 40 &times; 70% = 28<br>रोहित को कंप्यूटर साइंस में अंक प्राप्त हुए = 40 &times; 60% = 24<br>मुस्कान को कंप्यूटर साइंस में अंक मिले = 40 &times; 90% = 36<br>तन्वी को कंप्यूटर साइंस में अंक मिले = 40 &times; 60% = 24<br>टैरिन को कंप्यूटर साइंस में अंक मिले = 40 &times; 80% = 32<br>तो, 3 छात्रों ने कंप्यूटर साइंस में कम से कम 30 अंक प्राप्त किए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Study the given graph and answer the question that follows. <br>Ratio of imports to exports from two companies over the years<br><strong id=\"docs-internal-guid-79508f71-7fff-7f8a-2fe5-2082b119ad00\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe5uEuk1BD9dVzjx26uXSs0ETpPKNrL5SUkTSghEK_Ivdk_BqEhSkkSJ5-MDVVLUg2YAabSXAwUBQIFjC3D-gfZRdQX5bm4Wk-LIXp7eTKo3kTxjrb8c5D_N_7O5tNuJFWZNUgx?key=OXejARi3rfDReVD9iicPZiml\" width=\"333\" height=\"191\"></strong><br>In how many of the given years were the imports more than the exports of Company N ?</p>",
                    question_hi: "<p>12. दिए गए ग्राफ का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। <br>पिछले वर्षों के दौरान दो कंपनियों से आयात का अनुपात।<br><strong id=\"docs-internal-guid-79508f71-7fff-7f8a-2fe5-2082b119ad00\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe5uEuk1BD9dVzjx26uXSs0ETpPKNrL5SUkTSghEK_Ivdk_BqEhSkkSJ5-MDVVLUg2YAabSXAwUBQIFjC3D-gfZRdQX5bm4Wk-LIXp7eTKo3kTxjrb8c5D_N_7O5tNuJFWZNUgx?key=OXejARi3rfDReVD9iicPZiml\" width=\"333\" height=\"191\"></strong><br>दिए गए वर्षों में से कितने वर्ष कंपनी N के निर्यात की तुलना में आयात अधिक थे?</p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>12.(c) <br>Ratio of imports to exports greater than 1 indicates that import were more than export<br>Now, by the given graph data import to export ratio for company N:<br>In 2005 :- ratio equal to 1<br>In 2006 :- ratio greater than 1<br>In 2007 :- ratio greater than 1<br>In 2008 :- ratio greater than 1<br>In 2009 :- ratio greater than 1<br>In 2010 :- ratio equal to 1<br>Hence, required years = 4 years</p>",
                    solution_hi: "<p>12.(c) <br>आयात और निर्यात का अनुपात 1 से अधिक होना यह दर्शाता है कि आयात निर्यात से अधिक था<br>अब, दिए गए ग्राफ़ डेटा के अनुसार कंपनी N के लिए आयात-निर्यात अनुपात:<br>2005 में :- अनुपात 1 के बराबर<br>2006 में:- अनुपात 1 से अधिक<br>2007 में:- अनुपात 1 से अधिक<br>2008 में:- अनुपात 1 से अधिक<br>2009 में:- अनुपात 1 से अधिक<br>2010 में :- अनुपात 1 के बराबर<br>अतः, आवश्यक वर्ष = 4 वर्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The profit (in Rupees) earned by four companies in the different quarters of the year 2020 are shown in the chart. The data shown is in crores.<br><strong id=\"docs-internal-guid-12233c49-7fff-6237-c43a-8882d5be6266\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcH6sdo2i0oBNkg3lQUMhYQ834_SAWiYBEeTCfhG3hdiqfPsN8jX9QZ2q_y2yq8xUij3ky6ljKDXZzX2XcU80_d_Z1JYf8ztLkVSRp7PcF1gXn2HSuufP8K4lXUG1xY9oEZRLSc?key=OXejARi3rfDReVD9iicPZiml\" width=\"363\" height=\"218\"></strong><br>How much is the average profit earned by ITC in the year 2020?</p>",
                    question_hi: "<p>13. वर्ष 2020 की विभिन्न तिमाहियों में चार कंपनियों द्वारा अर्जित लाभ (रुपये में) चार्ट में दिखाया गया है। दिखाया गया डेटा करोड़ों में है।<br><strong id=\"docs-internal-guid-6f83c8b2-7fff-7d6b-a72f-60ee19c73db0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcKSM2HX_XwCewPMoFaktohQRzxemExarTUgXRaZQAbkN-d7wYR0pw8W2b6e7n9oKRRddvdGo4Ot9OQkQhAip6D_pyaLFbkO-PI6eN-lPZl7oNWUxaBzmW3Bd954xpU1C3e4OdG-A?key=OXejARi3rfDReVD9iicPZiml\" width=\"420\" height=\"252\"></strong><br>वर्ष 2020 में ITC द्वारा अर्जित औसत लाभ कितना है?</p>",
                    options_en: ["<p>₹1350 crore</p>", "<p>₹1225 crore</p>", 
                                "<p>₹1325 crore</p>", "<p>₹1400 crore</p>"],
                    options_hi: ["<p>₹1350 करोड़</p>", "<p>₹1225 करोड़</p>",
                                "<p>₹1325 करोड़</p>", "<p>₹1400 करोड़</p>"],
                    solution_en: "<p>13.(c) <br>Required average = <math display=\"inline\"><mfrac><mrow><mn>1100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1400</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1500</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1300</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>5300</mn><mo>&#160;</mo></mrow><mn>4</mn></mfrac></math> = ₹1325 crore</p>",
                    solution_hi: "<p>13.(c) <br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>1100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1400</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1500</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1300</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>5300</mn><mo>&#160;</mo></mrow><mn>4</mn></mfrac></math> = ₹1325 करोड़</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The following table shows the production of food grains in a country over the five years.<br><strong id=\"docs-internal-guid-cd2eb61b-7fff-17dd-2c8f-f922f88a13bd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXesTOOIgLuh9pDO5DPAdo521cAHmO9RouOlbprpi4eogd7u9oK8u-6pSn13jOBlbOyPvo-4X8zV5Y5bYI1y_BtlX4UOdf9Sg_Pnsdo8deuKFC45Y1MYFKgp_6yNRcFbI6gW-2tPLg?key=OXejARi3rfDReVD9iicPZiml\" width=\"341\" height=\"163\"></strong><br>The difference between the average production (in ten lakh tonnes) of wheat and maize over the years is:</p>",
                    question_hi: "<p>14. निम्नलिखित तालिका किसी देश में पाँच वर्षों में खा&zwnj;द्यान्नों के उत्पादन को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736834524518.png\" alt=\"rId28\" width=\"286\" height=\"157\"> <br>इन वर्षों में गेहूँ और मक्के के औसत उत्पादन (दस लाख टन में) के बीच अंतर ज्ञात कीजिए।</p>",
                    options_en: ["<p>224</p>", "<p>450</p>", 
                                "<p>310</p>", "<p>140</p>"],
                    options_hi: ["<p>224</p>", "<p>450</p>",
                                "<p>310</p>", "<p>140</p>"],
                    solution_en: "<p>14.(b)<br>Average production of wheat = <math display=\"inline\"><mfrac><mrow><mn>650</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>800</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>680</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>700</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>640</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 694<br>Average production of maize = <math display=\"inline\"><mfrac><mrow><mn>200</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>240</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>220</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>260</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>300</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 244<br>Required difference = 694 - 244 = 450</p>",
                    solution_hi: "<p>14.(b)<br>गेहूँ का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>650</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>800</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>680</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>700</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>640</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 694<br>मक्के का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>200</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>240</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>220</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>260</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>300</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 244<br>आवश्यक अंतर = 694 - 244 = 450</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Study the given graph and answer the question that follows.<br>The graph shows the marks obtained by Riya and Rida in six subjects in the CBSE 12th exam. The maximum marks in Maths, Physics and Chemistry are 140, and that in English. IP, Biology are 180<br><strong id=\"docs-internal-guid-e2a29a1c-7fff-edae-5bf0-747906e654c4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeqEHpCITa-QQX64HMvNVLHD_FC2lWGEJVn8YsUHg47mhEJD4ZMkVPDhQ2QIBbZ_XfVMil6k0SDLS8kDQnDlIsOptmJ8ZUQv89Ik8Aa37a-O2ox8PiSLKBGG7niFrhhXLrQMr_U4Q?key=OXejARi3rfDReVD9iicPZiml\" width=\"384\" height=\"212\"></strong><br>What is the percentage of marks obtained by Riya in Chemistry ?</p>",
                    question_hi: "<p>15. निम्नलिखित ग्राफ का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>ग्राफ में सीबीएसई (CBSE) 12वीं की परीक्षा में छह विषयों में रिया और रिदा द्वारा प्राप्त अंकों को दर्शाया गया है। गणित, भौतिकी और रसायन विज्ञान में अधिकतम अंक 140 और अंग्रेजी, आईपी और जीवविज्ञान में अधिकतम अंक 180 है।<br><strong id=\"docs-internal-guid-f4d3d36e-7fff-2204-2833-f447209df819\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe_xEqWpEYs5sTx42pnO4qsPuoMQomSHkaZhrVAMoDsWVegc08722zSMRM9mhmDBOeYuWyzy6T3Q43HmIxTExm4q_8Ckq-R_ul-fnTTfI3w0nNlwN8W1ryB_SZif3LKutL8Ixou?key=OXejARi3rfDReVD9iicPZiml\" width=\"345\" height=\"189\"></strong><br>रसायन विज्ञान में रिया द्वारा प्राप्त अंकों का प्रतिशत कितना है ?</p>",
                    options_en: ["<p>92.86%</p>", "<p>92.68%</p>", 
                                "<p>99.38%</p>", "<p>95.87%</p>"],
                    options_hi: ["<p>92.86%</p>", "<p>92.68%</p>",
                                "<p>99.38%</p>", "<p>95.87%</p>"],
                    solution_en: "<p>15.(a)<br>required% = <math display=\"inline\"><mfrac><mrow><mn>130</mn></mrow><mrow><mn>140</mn></mrow></mfrac></math> &times; 100 = 92.86%</p>",
                    solution_hi: "<p>15.(a)<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>130</mn></mrow><mrow><mn>140</mn></mrow></mfrac></math> &times; 100 = 92.86%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>