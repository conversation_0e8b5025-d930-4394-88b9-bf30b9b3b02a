<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A company has five plants for manufacturing spare parts. Each plant manufactures&nbsp;local quality and export quality. What is the ratio of the production of local quality and&nbsp;export quality units of the highest manufacturing plant ?<br><strong id=\"docs-internal-guid-a52c1e62-7fff-6d7b-878a-5cdd43b37ebb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcZo9f7i8e8urtaFlt1vumH3Ff1m4iuSrt51st5ECgo4ENS0rGEP1SCjNwl7XeTYuoVXws_PSAxvJtBkEcAQ5FFfoywO_K_sfc1o0idhYyCDJB3iv9DPpJyioPthLC6KVbNjXRn-w?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"435\" height=\"201\"></strong></p>",
                    question_hi: "<p>1. किसी कंपनी का स्पेयर पार्ट विनिर्माण के लिए पाँच संयंत्र हैं। प्रत्येक संयंत्र स्थानीय गुणवत्ता और निर्यात गुणवत्ता का विनिर्माण करता है। उच्चतम विनिर्माण संयंत्र की स्थानीय गुणवत्ता और निर्यात गुणवत्ता इकाइयों के उत्पादन का अनुपात क्या है?<br><strong id=\"docs-internal-guid-a1363d45-7fff-9f1e-3b03-c744870ce084\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd04THg7Sx_Z8JXww5xaBtkJVyG39iGMnaetGijsS7tpU3n8zko4jpy6BE43-Zmw63MTh3Stb0a87OkR5C5plCKZbqTpXgTRIWHhlx04rtMIjPxPDIpnQqdosxZSFxHVv97-zz1Fw?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"427\" height=\"190\"></strong></p>",
                    options_en: ["<p>4 : 1</p>", "<p>3 : 1</p>", 
                                "<p>2 : 11</p>", "<p>7 : 3</p>"],
                    options_hi: ["<p>4 : 1</p>", "<p>3 : 1</p>",
                                "<p>2 : 11</p>", "<p>7 : 3</p>"],
                    solution_en: "<p>1.(b)<br>Total production of Plant A = 420 + 140 = 560<br>Total production of Plant B = 350 + 150 = 500<br>Total production of Plant C = 360 + 120 = 480<br>Total production of Plant D = 440 + 80 = 520<br>Total production of Plant E = 440 + 110 = 550<br>We can see that the highest manufacturing plant is A<br>So Required ratio = 420: 140 = 3 : 1</p>",
                    solution_hi: "<p>1.(b)<br>संयंत्र A का कुल उत्पादन = 420 + 140 = 560<br>संयंत्र B का कुल उत्पादन = 350 + 150 = 500<br>संयंत्र C का कुल उत्पादन = 360 + 120 = 480<br>संयंत्र D का कुल उत्पादन = 440 + 80 = 520<br>संयंत्र E का कुल उत्पादन = 440 + 110 = 550<br>हम देख सकते हैं कि उच्चतम विनिर्माण संयंत्र A है<br>तो अभीष्ट अनुपात = 420: 140 = 3 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The percentage of students enrolled in different activities in a school which are displayed in the left side pie chart. The total number of students is 5000.<br>In the right-side pie chart, the total number of girls is 1550 and their percentage breakup enrolled in these activities.<br><strong id=\"docs-internal-guid-703e09cb-7fff-13ae-2c05-285f39ba79b5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeiKW5-z-lwpkSfB1pUiIoI564AZhVLzHRM_Hg22PKs3z4f8tL32SvlThizCS7SZ3_einFJGzqNY5xA371tB01LS0yIvHJ2yvZdNf3fwCCyLGXALLbgo_J3nXwQh_FKW6-8uosjnw?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"254\" height=\"247\">&nbsp; &nbsp; &nbsp; </strong><strong id=\"docs-internal-guid-b4ee5473-7fff-1b08-9438-b845dd9b8153\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXerJlPDZaT2dfYZa4rFRbHu_xZXF3Z3y5BrInpapFOpoG-n4X32hRykIYuScAX2woFKqfWBavj15tWlgT1KI8GxJRmlDgumx7o0vLvZNKPTwR04dmw894-SiGTJnCBvWFywQtRT2g?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"250\" height=\"252\"></strong><br>The number of girls enrolled in Craft forms what percent of the total number of&nbsp;students in the school?</p>",
                    question_hi: "<p>2. एक स्कूल में विभिन्न गतिविधियों में नामांकित विद्यार्थियों का प्रतिशत जो बाईं ओर पाई चार्ट में प्रदर्शित किया गया है। विद्यार्थियों की कुल संख्या 5000 है।<br>दायीं ओर के पाई चार्ट में, लड़कियों की कुल संख्या 1550 है और इन गतिविधियों में नामांकित उनका ब्रेकअप प्रतिशत है।<br><strong id=\"docs-internal-guid-0451549f-7fff-944e-98c9-8432faa1ceb9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfgFN7x1ertrfMujFbeW6yHWTTDrTRVLBJz7Kw9kgxGEcT54Ojtm7Ii9bu1MRdAccBoFJGrBw0vUTDTME8-Z1J-IX5q3U0OIere3ENvfBPLf30ASUHRogBf_aDWyWa2cNDwAKnvRg?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"235\" height=\"223\">&nbsp; &nbsp; &nbsp;</strong><strong id=\"docs-internal-guid-b84fe534-7fff-ad09-b6ca-5eea478283c9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFe8n6t3LHivi36eMx3EVk0kdb6-bwEkkEtkIhizShFi859ZakBTNaMS0Apauec3ycBC9VdP7kU7a9i1xDAsRS7l8Z8wqfYuFhTb-cY4-9QE69LAxHVhuDIkZGaz_rMpL3OZSTmg?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"204\" height=\"218\"></strong><br>क्राफ्ट में नामांकित लड़कियों की संख्या, स्कूल में विद्यार्थियों की कुल संख्या का कितना प्रतिशत है?</p>",
                    options_en: ["<p>6.82%</p>", "<p>7.11%</p>", 
                                "<p>5.82%</p>", "<p>5.81%</p>"],
                    options_hi: ["<p>6.82%</p>", "<p>7.11%</p>",
                                "<p>5.82%</p>", "<p>5.81%</p>"],
                    solution_en: "<p>2.(a)<br>Girls enrolled in craft = 1550 &times; 22% = 341<br>required% = <math display=\"inline\"><mfrac><mrow><mn>341</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 = 6.82</p>",
                    solution_hi: "<p>2.(a)<br>क्राफ्ट में नामांकित लड़कियाँ = 1550 &times; 22% = 341<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>341</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 = 6.82</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The given table shows the percentage of marks obtained by 5 students in different subjects. Study the table and answer the question that follows. (Maximum marks are given beside subject)<br><strong id=\"docs-internal-guid-2d9d6620-7fff-49e2-7d16-ffd5d9d13a19\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeQcIveeT3J-VfMdKARIqLBPgvfbunSeJsCXgy6lJ-BtTbjqHkY8PCBaSXoDQQUfTe4RQXLBv0POPvg06zYRbHjy0qVXaE96MxZcDBTW_k3AD-qSMeqotJDC6wvUbhJsbVOb2jj?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"328\" height=\"138\"></strong><br>What are the marks (in percentage) obtained by Mohan?</p>",
                    question_hi: "<p>3. दी गई तालिका 5 छात्रों द्वारा विभिन्न विषयों में प्राप्त अंकों का प्रतिशत दर्शाती है। तालिका का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें। (अधिकतम अंक विषय के नीचे दिए गए हैं)<br><strong id=\"docs-internal-guid-7b78e14a-7fff-8b69-c6da-40394e7730cc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXckgjaitXljag0WTjMB5SO0MPjN0V4KLImD06NG-W7-iqVv6k_1W3NGrcBoAp6Cbu61cq-Y1dqWwwU05mmP0V3BuALt7WeKKr34TIYfIu8Hh3gmimv71g1JBuzJW--hLfr0C4KvGg?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"358\" height=\"150\"></strong><br>मोहन द्वारा प्राप्त अंक (प्रतिशत में) कितने हैं?</p>",
                    options_en: ["<p>76.5%</p>", "<p>77.5%</p>", 
                                "<p>75.5%</p>", "<p>74.5%</p>"],
                    options_hi: ["<p>76.5%</p>", "<p>77.5%</p>",
                                "<p>75.5%</p>", "<p>74.5%</p>"],
                    solution_en: "<p>3.(d)&nbsp;Total marks of mohan <br>= 300 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + 300 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math> + 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math> + 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>95</mn><mn>100</mn></mfrac></math> + 200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math><br>= 240 + 180 + 70 + 95 + 160<br>= 745<br>Maximum marks = 300 + 300 + 100 + 100 + 200 = 1000<br>required% = <math display=\"inline\"><mfrac><mrow><mn>745</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 100 = 74.5</p>",
                    solution_hi: "<p>3.(d) <br>मोहन के कुल अंक <br>= 300 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + 300 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math> + 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math> + 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>95</mn><mn>100</mn></mfrac></math> + 200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math><br>= 240 + 180 + 70 + 95 + 160<br>= 745<br>अधिकतम अंक = 300 + 300 + 100 + 100 + 200 = 1000<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>745</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 100 = 74.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Study the given pie-charts and answer the question that follows.<br>The pie-charts show the data of students\' progress after graduation.<br><strong id=\"docs-internal-guid-1ae980c2-7fff-6239-b210-62d41c9ea507\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdM7fVBLA9iEwFLthzFypJwMpSQRwn6tdrmyweUk0kTBk1l1Jd9Ar7ajHnZaE8ZzbouGDiBO0tHkfAFvvd9StJN2jaJVfwiUM0kEiW_7i_3mWUaml7cSzXesqqK5mgycRtzwZjJ?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"333\" height=\"227\"></strong><strong id=\"docs-internal-guid-ca999cf0-7fff-762c-26d3-012ed0a5e10f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfhaZWKC53B1eXqE-LCS8ZTiII9mvNzSAn5DUDmGbjfyHlspRix7iOFoYqLi97A8pTlYWYQM90tXlE9q2-O0-MMtwLQJgZbUFH9E1dX4OyELqHcFp26Jxv12lgjfY_KGZGRHVxIJg?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"240\" height=\"215\"></strong><br>Which two combined have the same number of students as the other?</p>",
                    question_hi: "<p>4. दिए गए पाई-चार्ट का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>पाई-चार्ट स्नातक स्तर की पढ़ाई के बाद विद्यार्थियों की प्रगति के आंकड़े दर्शाते हैं।<br><strong id=\"docs-internal-guid-61b07f86-7fff-d40e-aefc-ae4a25ca0440\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeWLg_wLHbfW9QyyMK_XAy6c5OstoQiiwjlYBk_By4-zJo3AfuVSObhPDigaWbXDyNzak9bWzCcgsEBHkDyNgWy75BEkKTc8s5OuJqsBQNfcWN6mZwwih32xUJDnq4S1kE0W0yqPA?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"293\" height=\"192\">&nbsp; &nbsp; &nbsp;</strong><strong id=\"docs-internal-guid-644c74b6-7fff-dc42-ae75-83e2db2ef44e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcbk7pYx2ixy1gMNwTfwM0eTV5Hkc14puoVxAz3UGLIP3g6uG98Ugb2YK7FglTTN773SIgEoJ2R0hqbrQjP0yRy7XpyrGXiH18hdfzqhu5g79G0P-qB6RaDd8nZjljbKkkEUoP-ug?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"206\" height=\"196\"></strong><br>किन दो को मिलाकर विद्यार्थियों की संख्या अन्य (Other) के समान है?</p>",
                    options_en: ["<p>P.G. + M.Phil.</p>", "<p>P.G. + Ph.D.</p>", 
                                "<p>Entrepreneur + Ph.D.</p>", "<p>Entrepreneur + P.G.</p>"],
                    options_hi: ["<p>पी.जी. + एम.फिल.</p>", "<p>पी.जी.+ पीएच.डी</p>",
                                "<p>उद्यमी + पीएच.डी</p>", "<p>उद्यमी + पी.जी.</p>"],
                    solution_en: "<p>4.(b)&nbsp;By checking option one by one option (b) satisfied the given condition<br>P.G. + Ph.D. = other<br>39% + 9% = 48%<br>48% = 48%<br>RHS = LHS</p>",
                    solution_hi: "<p>4.(b)&nbsp;एक-एक करके विकल्प की जाँच करके विकल्प (b) दी गई शर्त को पूरा करता है<br>पी.जी.+ पीएच.डी = अन्य <br>39% + 9% = 48%<br>48% = 48%<br>RHS = LHS</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The following bar graph shows the sales (in thousands) of magazines from six branches (A, B, C, D, E and F) of a publishing company during two consecutive years 2021 and 2022.<br><strong id=\"docs-internal-guid-f409b18f-7fff-d2ca-e5cc-e60082e1a644\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4cGqqRmVcQPWg5hFV6ibZyK0HzCFCYe0xYdnRzMl3Zl2tq2b1MqupTp5c4sTS5VSIrftNM1XqxWG-_F73oAkgXfk9NV0-1HHFfHAAOxTsiiHczdQ6WX0XcALfijV_Ax47snHEDQ?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"372\" height=\"246\"></strong><br>What percent of the average sales of branches A, B and C in 2022 is the average sales of branches A, C and F in 2021 ?</p>",
                    question_hi: "<p>5. निम्नलिखित दंड आरेख दो क्रमागत वर्षों 2021 और 2022 के दौरान एक प्रकाशन कंपनी की छह शाखाओं (A, B, C, D, E और F) से पत्रिकाओं की बिक्री (हजारों में) को दर्शाता है।<br><strong id=\"docs-internal-guid-46b0d0fb-7fff-0981-cd35-b8024cab262e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4cGqqRmVcQPWg5hFV6ibZyK0HzCFCYe0xYdnRzMl3Zl2tq2b1MqupTp5c4sTS5VSIrftNM1XqxWG-_F73oAkgXfk9NV0-1HHFfHAAOxTsiiHczdQ6WX0XcALfijV_Ax47snHEDQ?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"398\" height=\"263\"></strong><br>वर्ष 2021 में शाखाओं A, C और F की औसत बिक्री, वर्ष 2022 में शाखाओं A, B और C की औसत बिक्री का कितना प्रतिशत है?</p>",
                    options_en: ["<p>87.5%</p>", "<p>86.2%</p>", 
                                "<p>82.3%</p>", "<p>88.4%</p>"],
                    options_hi: ["<p>87.5%</p>", "<p>86.2%</p>",
                                "<p>82.3%</p>", "<p>88.4%</p>"],
                    solution_en: "<p>5.(a)<br>Average sales of branches A, B and C in 2022 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>105</mn><mo>+</mo><mn>65</mn><mo>+</mo><mn>110</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>280</mn><mn>3</mn></mfrac></math><br>Average sales of branches A, C and F in 2021 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>+</mo><mn>95</mn><mo>+</mo><mn>70</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>245</mn><mn>3</mn></mfrac></math><br>So, required % = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>245</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>280</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>245</mn><mn>3</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>280</mn></mfrac></math> &times; 100 = 87.5%</p>",
                    solution_hi: "<p>5.(a)<br>2022 में शाखाओं A, B और C की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>105</mn><mo>+</mo><mn>65</mn><mo>+</mo><mn>110</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>280</mn><mn>3</mn></mfrac></math><br>2021 में शाखाओं A , C और F की औसत बिक्री = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>+</mo><mn>95</mn><mo>+</mo><mn>70</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>245</mn><mn>3</mn></mfrac></math><br>तो, आवश्यक % = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>245</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>280</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>245</mn><mn>3</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>280</mn></mfrac></math> &times; 100 = 87.5%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The following graph shows the production (in thousands) of two types (A and B) of cars by a company over the years 2016 to 2021.<br><strong id=\"docs-internal-guid-5aaa131a-7fff-e0bd-9b13-bc8a2add3d9e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc-SyRK5kTiJX9iRcLlBQWw1oUU14jTsA4HgJhumf16V1Domns-z1Fqn2z81otmBrwYSIiyof4UNsr7Oybqgznml2eN-JcypbDYyu9SePPHxOKFbU3gHSC7qRpzDIc1qx3uJjBi?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"376\" height=\"249\"></strong><br>In how many of the given years was the production of type A cars more than the average production of this type car in the given years?</p>",
                    question_hi: "<p>6. निम्नलिखित आरेख वर्ष 2016 से 2021 के दौरान एक कंपनी &zwnj;द्वारा दो प्रकार (A और B) की कार्य के उत्पादन (हजार में) को दशर्शाता है।<br><strong id=\"docs-internal-guid-a7ab0ceb-7fff-13c4-e01c-0b856f81f77b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdTLjGHXyc7u-b2wWY0RJWo7MfJ89js0QEwlEQd9sfLwSqZ94hMmX8ZMyW_UXZorUTojeWbOGVlYIbn7yNuQzdlxTN_jsraprO_unY4qt3qMTuBO7SD2c-6gbCV30QRDQU7JIvt?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"374\" height=\"233\"></strong><br>दिए गए कितने वर्षों में A प्रकार की कारों का उत्पादन दिए गए वर्षों में इस प्रकार की कारों के औसत उत्पादन से अधिक था?</p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>1</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>1</p>", "<p>4</p>"],
                    solution_en: "<p>6.(a)<br>Average production of type A cars = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>125</mn><mo>+</mo><mn>200</mn><mo>+</mo><mn>250</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>400</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1375</mn><mn>6</mn></mfrac></math> = 229 (approx)<br>So, required years = 3 years (2019 , 2020 , 2021)</p>",
                    solution_hi: "<p>6.(a)<br>टाइप A कारों का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>125</mn><mo>+</mo><mn>200</mn><mo>+</mo><mn>250</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>400</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1375</mn><mn>6</mn></mfrac></math> = 229 (लगभग)<br>अतः, आवश्यक वर्ष = 3 वर्ष (2019 , 2020 , 2021)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Study the given three-dimensional chart and answer the question that follows.<br>The chart details the sale of fruits in different months.<br>,<strong id=\"docs-internal-guid-7120ff26-7fff-4aab-c8e0-dd13c74228af\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdveHh1fbWtwc8bLONQE9lHh4toKpu8e8nD9pax8kAc7eyhzVChmK2hgdN3YYAOS7ie5S0Eyjo-BRAW-tLnOT5SfUJLi3AJFNmCCfs-FKpIviEXhuproDt3SCoIqCOb-851V-7xlQ?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"323\" height=\"256\"></strong><br>In which month was the sale of Melons the lowest and by how much was it more than the sale of oranges in that month ?</p>",
                    question_hi: "<p>7 दिए गए त्रि-विमीय चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्नों का उत्तर दीजिए। चार्ट विभिन्न महीनों में फलों की बिक्री का विवरण देता है।<br><strong id=\"docs-internal-guid-02771cc8-7fff-85fc-6165-089aef302817\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_X6yeqmn0kiXwCl62FW8Sskqb_7MprpH-Xk2SvHVYzYTkdE9T_9-sIAVqusisEPnDvNTOfp_J7VeKZH3AidjdGzKZQakUn83s9MzXLr-eOr5BkwrrrqM7aIMpzJd1XrIIo68g5Q?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"339\" height=\"268\"></strong><br>किस महीने में खरबूजे की बिक्री सबसे कम थी और यह उस महीने में संतरे की बिक्री से कितनी अधिक थी?</p>",
                    options_en: ["<p>April, 3</p>", "<p>May, 3</p>", 
                                "<p>June, 3</p>", "<p>June, 4</p>"],
                    options_hi: ["<p>अप्रैल, 3</p>", "<p>मई, 3</p>",
                                "<p>जून, 3</p>", "<p>जून, 4</p>"],
                    solution_en: "<p>7.(c) <br>In the month of june sales of melons are the lowest (20)<br>Sales of oranges in june = 17<br>So, sales of melons was 3 more than the sales of oranges in the month of june.</p>",
                    solution_hi: "<p>7.(c) <br>जून माह में खरबूजे की बिक्री सबसे कम (20) रही। <br>जून में संतरे की बिक्री = 17<br>तो, जून के महीने में खरबूजे की बिक्री संतरे की बिक्री से 3 अधिक थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The given table shows the marks obtained by 100 students out of 50 marks in Mathematics and Physics in an examination.<br><strong id=\"docs-internal-guid-a8384ab1-7fff-1080-ea41-c81a97b3340f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf4WTSlOuH-Djdg-XiSwLQn-4x4rtm0JZcy_zoiPczbvMwrvTEb5FWFyE_gbpwnRetUnYoP35TVdR1hnhPws6dzfhHysGd2Zdw5-PM4gBiR_eSZLdjRMk8csNjbNUGAp9hLVzx7DQ?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"369\" height=\"107\"></strong><br>If at least 60% marks in Physics are required for pursuing higher studies in Physics, how many students will be eligible to pursue higher studies in Physics?</p>",
                    question_hi: "<p>8. दी गई तालिका 100 छात्रों द्वारा एक परीक्षा में गणित और भौतिकी में 50 अंकों में से प्राप्त अंकों को दर्शाती है।<br><strong id=\"docs-internal-guid-46f7f57b-7fff-2fad-1b1e-68594aff069b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXepJZS4ifEMH3yYxHmVZPHjKdQn2DY3EdbmNp5dcMXY0ptvTWhUO36Ulj-epqGCL7Iwta-_nPD_U3AXWeScvtnOlOWdJUjVRCQR1yEX3p6-HtKvuVN1iCkDckMY5sS7hsTineR2yA?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"312\" height=\"116\"></strong><br>यदि भौतिकी में उच्च शिक्षा प्राप्त करने के लिए भौतिकी में कम से कम 60% अंक आवश्यक हैं, तो कितने छात्र भौतिकी में उच्च शिक्षा के लिए पात्र होंगे?</p>",
                    options_en: ["<p>66</p>", "<p>21</p>", 
                                "<p>81</p>", "<p>27</p>"],
                    options_hi: ["<p>66</p>", "<p>21</p>",
                                "<p>81</p>", "<p>27</p>"],
                    solution_en: "<p>8.(b)<br>Minimum required number to pursue higher studies in Physics = 50 &times; 60% = 30<br>So, Number of student who score 30 or more than 30 marks in Physics = 21 student</p>",
                    solution_hi: "<p>8.(b)<br>भौतिकी में उच्च अध्ययन करने के लिए न्यूनतम आवश्यक संख्या = 50 &times; 60% = 30<br>तो, भौतिकी में 30 या 30 से अधिक अंक प्राप्त करने वाले छात्रों की संख्या = 21 छात्र</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The given pie-charts show the distribution of Graduate and Post-graduate level students in five different colleges A, B, C, D and E.<br><strong id=\"docs-internal-guid-ab41b2e5-7fff-b7a7-a092-84eb5fd605b4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeRIXItvhre3n-3fWyl33_NrM_GBIwfrMN9hYiEqphJ44pENTxBOd0IEHGMaCSNQwN8TDrAs11T_fW9DFfx2c20DXjBsTLg7mBjPP6V3sgcH9pphFGZM0W1JXklEA4SAwiDhVftig?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"311\" height=\"206\"></strong><strong id=\"docs-internal-guid-34893094-7fff-4ea7-734e-7c3ec8277268\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdUaCI7ceoOhHemITg402tMe2b6jvIi84RuNK3nWZawufLZ55pc_OMHnFS_Pq9YGOjjcoTWMlRkpOhS3qfMezENwEvRyG_rwLXVdj1pRx24UuTJjbowufnXSu38Wk0qjzWZlOdbyw?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"349\" height=\"219\"></strong><br><br>What is the ratio between the number of students studying at Post-Graduate and Graduate levels, respectively, from college E?</p>",
                    question_hi: "<p>9. दिए गए पाई-चार्ट पांच अलग-अलग महाविद्यालयों A, B, C, D और E में स्नातक और स्नातकोत्तर स्तर के छात्रों के वितरण को दर्शाते हैं।<br><strong id=\"docs-internal-guid-15f249f1-7fff-f925-fbb6-02aa1a807f1f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdSwvNFrPxO0QmVYn8gPfvgIsqmu4EYEYWDS3h2j4YYcKTtthFmz9j-wHXyciJiUssOG4bJoqoTa9HA3tei6xb-o68uiAC9Rj2_3CXZpGjMJE5GsWo6OuxLyHe3WjgSsY-Eo2nk?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"339\" height=\"198\"></strong><strong id=\"docs-internal-guid-e348b9db-7fff-02d2-05c8-91099d834f10\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcvYN_UJ51zJo1-T-FjmjaybV70MOpfH2Fztll3VHVcmiDJ-pwHeWpKkKhrm7Xxu0qXxi1ThuksbLA_YHEXzbFSYA5uMN2NZYknQOZ9cjOi-sW_NG2NIwYsUgeLPPbJP7VlEI-Lhw?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"314\" height=\"207\"></strong><br>महाविद्यालय E से क्रमशः स्नातकोत्तर और स्नातक स्तर पर अध्ययन करने वाले छात्रों की संख्या के बीच अनुपात कितना है?</p>",
                    options_en: ["<p>48 : 37</p>", "<p>32 : 43</p>", 
                                "<p>16 : 17</p>", "<p>44 : 45</p>"],
                    options_hi: ["<p>48 : 37</p>", "<p>32 : 43</p>",
                                "<p>16 : 17</p>", "<p>44 : 45</p>"],
                    solution_en: "<p>9.(d)<br>Number of students in post graduate from college E = 16800 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>Number of students in graduate from college E= 18900 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>Required ratio = 16800 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> : 18900 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>100</mn></mfrac></math> = 168 &times; 22 : 189 &times; 20 = 44 : 45</p>",
                    solution_hi: "<p>9.(d)<br>कॉलेज E से स्नातकोत्तर में छात्रों की संख्या = 16800 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>कॉलेज E से स्नातक में छात्रों की संख्या = 18900 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>आवश्यक अनुपात = 16800 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> : 18900 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>100</mn></mfrac></math> = 168 &times; 22 : 189 &times; 20 = 44 : 45</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Following Pie chart presents monthly expenditure by Sushma and Preeti in different heads. Sushma and Preeti make equal expenditure every month.<br><strong id=\"docs-internal-guid-c39c340c-7fff-ab34-3987-e2db9eaa4a3b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfwqJG5_PjR16-4bou9SSKOZCNAPi8C7xVtFH_TMbz7k_msX2mQcgwkHsipSNylfqWjl1t60mgvQ1gFWcUmtxvhLb-l7Qa3_1ctTsVTynBPF_jbHrW1lWQNOSymgOVQzBNf0j7adA?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"276\" height=\"236\">&nbsp; &nbsp; &nbsp;</strong><strong id=\"docs-internal-guid-64d44a31-7fff-b6f8-77aa-4c46497af941\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdCEaxidnNWPdZMbo7DjWzci_UDH_eZAPSnjiEgfrVLdesWgnV2a6U0rsgQWlmI85Ehah0jwIpiPiRucetjADBYeSSub4TL-G8-nsF6ioxD4c0Q13FW1vVh-pkvpYZBWr3Ekd9iBQ?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"244\" height=\"233\"></strong><br><br>What is the difference between the expenditure (in ₹) done on Food by Sushma and Preeti?</p>",
                    question_hi: "<p>10. निम्नलिखित पाई-चार्ट में सुषमा और प्रीति द्वारा विभिन्न मदों में किए गए मासिक व्यय को प्रस्तुत किया गया है। सुषमा और प्रीति हर महीने एक बराबर राशि खर्च करती हैं।<br><strong id=\"docs-internal-guid-e16045d7-7fff-02ce-8d2e-06d24b22ce29\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXczaPfhPCLTQjcvSqfcbINZ3MccDp8Py3n469yDspB3m_uy7ZHJyMPC9APzBqgQ9xogzt0hCpItOgVDYXdOCSaUf4x1FB7hnFCOdOdcMf-y9ojh74oIopr5VfahlMvpPgcYyG8E9A?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"232\" height=\"215\">&nbsp; &nbsp;</strong><strong id=\"docs-internal-guid-721352ff-7fff-19b5-cf2b-332bcb745299\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeu9rdpfhwN__kdTZOWrl1RPdrIPjDygwp8heNr4zb6kPwvzhyijNKpTv-WSZ-iNO6i6k6ZFna2-RIqmn8xiMM0dFqWXKTz1ULAdHrBr6sLNCXRCYi_Z9fZ_mUSd5_2nF3B3Ldbhg?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"213\" height=\"214\"></strong><br>सुषमा और प्रीति द्वारा भोजन पर किए गए व्यय (₹ में) के बीच का अंतर कितना है?</p>",
                    options_en: ["<p>1500</p>", "<p>500</p>", 
                                "<p>1000</p>", "<p>750</p>"],
                    options_hi: ["<p>1500</p>", "<p>500</p>",
                                "<p>1000</p>", "<p>750</p>"],
                    solution_en: "<p>10.(a) <br>Expenditure of sushma = expenditure of preeti<br>Total expenditure of sushma = 1800 + 5000 + 6000 + 7200 + 2500 + 7500 = 30000<br>100% = 30000 <br>(preeti&rsquo;s expenditure on food) 15% = 300 &times; 15 = 4500<br>Expenditure of sushma on food = 6000<br>Required difference = 6000 - 4500 = 1500</p>",
                    solution_hi: "<p>10.(a) <br>सुषमा का खर्च = प्रीति का खर्च<br>सुषमा का कुल खर्च = 1800 + 5000 + 6000 + 7200 + 2500 + 7500 = 30000<br>100% = 30000 <br>(प्रीति का भोजन पर खर्च) 15% = 300 &times; 15 = 4500<br>सुषमा का भोजन पर खर्च = 6000<br>आवश्यक अंतर = 6000 - 4500 = 1500</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The bar graph represents the production of different spare parts of computer in&nbsp;different months.<br><strong id=\"docs-internal-guid-e7eff935-7fff-ba1c-f34c-3fa8ee6d0b3b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfY3ggKlf8MwPjDa3vPA7mCTjHDE8vYuiIdjmAAlhPOYIeyXWvSZAocEzeQO4mFObcqsrFLZqK6caE9PYTa7J-upvr-NgfuamLOInKocrhjgxTbWuqVsvgitNTVHt96FtjQ9GW0kw?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"373\" height=\"271\"></strong><br>The ratio between the number of pen drives produced in the month of Jan, March and&nbsp;April is:</p>",
                    question_hi: "<p>11. दंड आरेख अलग-अलग महीनों में कंप्यूटर के विभिन्न स्पेयर पार्ट्स के उत्पादन को निरूपित करता है।<br><strong id=\"docs-internal-guid-759b06de-7fff-b7bf-44ec-8ad4c997d679\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfzq6qBmSDcXD_vEh990P4cMO5FE9IcKypMikxAK8Dw8iDmo8z2yNEI8nJNMgyxW925MPbbCbX0A44WjNav6qVsIwQYuGOXfuW6kPyua4XWW9lzaL92xhLW2VFOYYzUYuNKVDL4Ow?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"355\" height=\"255\"></strong><br>जनवरी, मार्च और अप्रैल के महीने में उत्पादित पेन ड्राइव की संख्या के बीच का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>1 : 1 : 1</p>", "<p>1 : 1 : 3</p>", 
                                "<p>1 : 1 : 2</p>", "<p>3 : 1 : 2</p>"],
                    options_hi: ["<p>1 : 1 : 1</p>", "<p>1 : 1 : 3</p>",
                                "<p>1 : 1 : 2</p>", "<p>3 : 1 : 2</p>"],
                    solution_en: "<p>11.(c) <br>Production of pen drives in jan. = 1500<br>Production of pen drives in march = 1500<br>Production of pen drives in april = 3000<br>Required ratio = 1500 : 1500 : 3000 = 1 : 1 : 2</p>",
                    solution_hi: "<p>11.(c) <br>जनवरी में पेन ड्राइव का उत्पादन = 1500<br>मार्च में पेन ड्राइव का उत्पादन = 1500<br>अप्रैल में पेन ड्राइव का उत्पादन = 3000<br>आवश्यक अनुपात = 1500 : 1500 : 3000 = 1 : 1 : 2</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Study the given table and answer the question that follows.<br>The table shows the results of half-yearly and annual examinations of three sections A, B, C of class X students in a school<br><strong id=\"docs-internal-guid-e89d78e4-7fff-53ba-aecc-e254234cc8ca\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXezFNvN2ubDtBrGDZKXfH0ZbYIX_BoUXfixJ6kmgKQEzwbCjeN2fNH7sNgJOS1omD_K6Ys8xLNn7Vz3QXvZYFZ2-NmFnEOcyNUmN-_leGjMnMnnU8P8SoEAlygO1Ro9NWQ9Nnk1?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"437\" height=\"182\"></strong><br>Find the failed percentage of section C in at least one of the two examinations.</p>",
                    question_hi: "<p>12. निम्नलिखित तालिका का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>तालिका में एक स्कूल में X कक्षा के विद्यार्थियों के तीन वर्गों A, B, C की अर्धवार्षिक और वार्षिक परीक्षाओं के परिणाम को दर्शाया गया है।<br><strong id=\"docs-internal-guid-37cbfcb3-7fff-7eaf-c2aa-4660a9560ab6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc-oyq4T3PgoPeTDXYS_nyYTc8ooeEU-CFNCZmVck5iWTzhwalftCenthlPGVQcIv4MDJSkQWZblbcOrgP4WbJXadGlJQvsv3sL0s-a8SAMsacUylFvv2JxJIxcpetvRk4aPvrD3w?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"384\" height=\"179\"></strong><br>वर्ग C में दोनों परीक्षाओं में से न्यूनतम एक परीक्षा में अनुत्तीर्ण होने वाले विद्यार्थियों का प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>30.81%</p>", "<p>42.62%</p>", 
                                "<p>10.42%</p>", "<p>38.23%</p>"],
                    options_hi: ["<p>30.81%</p>", "<p>42.62%</p>",
                                "<p>10.42%</p>", "<p>38.23%</p>"],
                    solution_en: "<p>12.(b)<br>Student that are failed in at least one of the two examinations of Section C <br>= (12 + 6 + 8) = 26<br>Number of candidate of section C = (12 + 6 + 8 + 35) = 61<br>Hence, required % = <math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>61</mn></mrow></mfrac></math> &times; 100 = 42.62%</p>",
                    solution_hi: "<p>12.(b)<br>वह छात्र जो सेक्सन C की दो परीक्षाओं में से कम से कम एक में अनुत्तीर्ण हुए है<br>= (12 + 6 + 8) = 26<br>अनुभाग C के अभ्यर्थियों की संख्या = (12 + 6 + 8 + 35) = 61<br>अतः, आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>61</mn></mrow></mfrac></math> &times; 100 = 42.62%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The following graph shows the production (in thousands) of two types (A and B) of cars by a company over the years 2016 to 2021.<br><strong id=\"docs-internal-guid-cc3556a4-7fff-1a32-d041-d14bfa66bcc7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdrW5yf_p-IPKR5sb7Q9G5yM_sNLgJizu0QI52i5Oc2Cqc_1_kQkz84yFleJkrrEHwxEmvWVIJAtuONf2XrNuJ5L4JRygK3AOrqAu4AeHcn_pvuQVGMXzmhEQ0Y-R_vmnMXAbn04Q?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"377\" height=\"239\"></strong><br>The ratio of the total production of type A cars to type B cars over the years is:</p>",
                    question_hi: "<p>13. निम्नलिखित आरेख वर्ष 2016 से 2021 के दौरान एक कंपनी द्वारा दो प्रकार (A और B) की कारों के उत्पादन (हजार में) को दर्शाता है।<br><strong id=\"docs-internal-guid-7214761e-7fff-4bab-a219-92e4717ff761\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4qoBc99V_43YL5BWaOFy_tIXMbYEsB0Z06boNh5Mh0FJjqShKXA4eYfK2Jc1i3-1ytt4pIk5SrDWEpiafRwbNXRdeLanPnDHb57RvZxMqg5ZDHESSaIx-BEtmFUUB3xsp_-O1Xw?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"370\" height=\"244\"></strong><br>इन वर्षों के दौरान A प्रकार की कारों के कुल उत्पादन और B प्रकार की कारों के कुल उत्पादन का अनुपात क्या था?</p>",
                    options_en: ["<p>11 : 10</p>", "<p>10 : 21</p>", 
                                "<p>10 : 11</p>", "<p>21 : 10</p>"],
                    options_hi: ["<p>11 : 10</p>", "<p>10 : 21</p>",
                                "<p>10 : 11</p>", "<p>21 : 10</p>"],
                    solution_en: "<p>13.(a)<br>Total production of type A cars = (100 + 125 + 200 + 250 + 300 + 400) = 1375<br>Total production of type B cars = (150 + 200 + 150 + 200 + 250 + 300) = 1250<br>Hence, required ratio = 1375 : 1250 = 11 : 10</p>",
                    solution_hi: "<p>13.(a)<br>A प्रकार की कारों का कुल उत्पादन = (100 + 125 + 200 + 250 + 300 + 400) = 1375<br>B प्रकार की कारों का कुल उत्पादन = (150 + 200 + 150 + 200 + 250 + 300) = 1250<br>अतः, आवश्यक अनुपात = 1375 : 1250 = 11 : 10</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The following graph shows the production of milk in 4 states in 2010 and 2011.<br><strong id=\"docs-internal-guid-0c5b6d7e-7fff-ca29-99ba-98e7783b4a44\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd2H2hTae5Jr7O0rqK3x0n0UygfGWEAvegtrLYwd7JqwFFRtnXazLudpHtXfvGVIMfuDOgkpjBEuXK1IPRIG5z7AaH0U8Nyk1DdBL51kJtgRxjaLpdJMzDb7-qEdWECtuxab85f?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"382\" height=\"267\"></strong><br>The difference between the average production (in thousand tonnes) for all state in&nbsp;both years is:</p>",
                    question_hi: "<p>14. निम्नांकित आरेख 2010 और 2011 में 4 राज्यों में दूध के उत्पादन को दर्शाता है।<br><strong id=\"docs-internal-guid-6335fc4c-7fff-0410-0917-da58fb5ef741\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJg9TLQgZ2BmzuXwYbdM1KeA00ZXLocFIuatIv5_oPnhxEne5S9oafOqsGp48qdeE_euVN_ucoxEDA-MLkD5i0Q3IvZjleI3S0wdp1b9S-1uxSrTRNdCqYscJ-sYI79NLUyuVR?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"375\" height=\"266\"></strong><br>दोनों वर्षों में सभी राज्यों के औसत उत्पादन (हजार टन में) के बीच का अंतर ज्ञात कीजिए।</p>",
                    options_en: ["<p>7.25</p>", "<p>6.25</p>", 
                                "<p>6.75</p>", "<p>7.75</p>"],
                    options_hi: ["<p>7.25</p>", "<p>6.25</p>",
                                "<p>6.75</p>", "<p>7.75</p>"],
                    solution_en: "<p>14.(b) <br>Average production of milk in 4 states in 2010 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>+</mo><mn>25</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>55</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>160</mn><mn>4</mn></mfrac></math> = 40<br>Average production of milk in 4 states in 2011 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mi>&#160;</mi><mo>+</mo><mn>50</mn><mo>+</mo><mn>45</mn><mo>+</mo><mn>50</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>4</mn></mfrac></math> = 46.25<br>Required difference = 46.25 - 40 = 6.25</p>",
                    solution_hi: "<p>14.(b) <br>2010 में 4 राज्यों में दूध का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>+</mo><mn>25</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>55</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>160</mn><mn>4</mn></mfrac></math> = 40<br>2011 में 4 राज्यों में दूध का औसत उत्पादन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mi>&#160;</mi><mo>+</mo><mn>50</mn><mo>+</mo><mn>45</mn><mo>+</mo><mn>50</mn></mrow><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>185</mn><mn>4</mn></mfrac></math> = 46.25<br>आवश्यक अंतर = 46.25 - 40 = 6.25</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Answer the following question on the basis of the bar graph given<br><strong id=\"docs-internal-guid-51823244-7fff-6dc3-e543-3cab4143fb5b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdzC98M9AiFQ79wzhzrGy584Incy15Pa_gJinbDcCkphcCLa6N2Y3gLz9owC2UAGBK_c-e9PE_DLmcn2NizKszdypEdk-F_XvmVcr2Z2zzpSBaDk1YKxrPfqe3UfpKPvBw4_4XGSg?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"385\" height=\"258\"></strong><br>Which year shows the highest change in the revenue obtained from magazines?</p>",
                    question_hi: "<p>15. दिए गए बार ग्राफ के आधार पर निम्नलिखित प्रश्न का उत्तर दें।<br><strong id=\"docs-internal-guid-db0ecd9e-7fff-95fd-4607-a8454cb58479\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfH6C__9riRz4GpS2Pg5PvCIxPEW_g5EIJBAU9fOK9659bpJsKBhm5b7BX0LOVGp9v931b8IlpJFM7nxI7c42NsyhjvrSp78h32KAP8ikj2cDljL6G6cbJ2fiQ91DztjVc-0YScmg?key=37inV95BS1sVLQN9XQ_E9RPV\" width=\"423\" height=\"274\"></strong><br>कौन सा वर्ष मैगजीन से प्राप्त आमदनी में सर्वाधिक परिवर्तन दर्शाता है ?</p>",
                    options_en: ["<p>1989</p>", "<p>1990</p>", 
                                "<p>1991</p>", "<p>1992</p>"],
                    options_hi: ["<p>1989</p>", "<p>1990</p>",
                                "<p>1991</p>", "<p>1992</p>"],
                    solution_en: "<p>15.(b)<br>By checking options one by one Change in the revenue<br>In 1989 :- cannot find out<br>In 1990 :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>39</mn><mo>-</mo><mn>31</mn></mrow><mn>31</mn></mfrac></math> &times; 100 = 25.80%<br>In 1991 :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>-</mo><mn>39</mn></mrow><mn>39</mn></mfrac></math> &times; 100 = 15.38%<br>In 1992 :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>45</mn></mrow><mn>45</mn></mfrac></math> &times; 100 = 11.11%<br>Hence, required year = 1990</p>",
                    solution_hi: "<p>15.(b)<br>एक-एक करके विकल्पों की जाँच करने से राजस्व में परिवर्तन होता है<br>1989 में :- बता नहीं सकते <br>1990 में :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>39</mn><mo>-</mo><mn>31</mn></mrow><mn>31</mn></mfrac></math> &times; 100 = 25.80%<br>1991 में :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>-</mo><mn>39</mn></mrow><mn>39</mn></mfrac></math> &times; 100 = 15.38%<br>1992 में :- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>45</mn></mrow><mn>45</mn></mfrac></math> &times; 100 = 11.11%<br>अतः, आवश्यक वर्ष = 1990</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>