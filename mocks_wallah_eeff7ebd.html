<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Simplify<br><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>15</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>75</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>125</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>25</mn></mrow></mfrac></math>(x-5)</p>",
                    question_hi: "<p>1. सरल कीजिए :<br><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>15</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>75</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>125</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>25</mn></mrow></mfrac></math>(x-5)</p>",
                    options_en: ["<p><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+25</p>", "<p><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+5x + 25</p>", 
                                "<p><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 10x + 25</p>", "<p><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>- 25</p>"],
                    options_hi: ["<p><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ 25</p>", "<p><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+ 5x + 25</p>",
                                "<p><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 10x + 25</p>", "<p><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math>- 25</p>"],
                    solution_en: "<p>1.(c) <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>15</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>75</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>125</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>25</mn><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><msup><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math><br>= <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 25 + 10x or x<sup>2 </sup>+ 10x + 25</p>",
                    solution_hi: "<p>1.(c) <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>15</mn><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>75</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>125</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>25</mn><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>x</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math> &times; (x - 5)<br>= <math display=\"inline\"><msup><mrow><mo>(</mo><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math><br>= <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 25 + 10x or x<sup>2 </sup>+ 10x + 25</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Which of the following can be the value of k, if <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>88</mn><mi>&#160;</mi><mo>&#247;</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>k</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>k</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo></mrow></mfrac></math> = 1 ?</p>",
                    question_hi: "<p>2. यदि <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>88</mn><mi>&#160;</mi><mo>&#247;</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>k</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>k</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>)</mo></mrow></mfrac></math> = 1 है तो निम्नलिखित में से k का मान क्या हो सकता है?</p>",
                    options_en: ["<p>1,10</p>", "<p>4,7</p>", 
                                "<p>3,10</p>", "<p>2,7</p>"],
                    options_hi: ["<p>1,10</p>", "<p>4,7</p>",
                                "<p>3,10</p>", "<p>2,7</p>"],
                    solution_en: "<p>2.(a) <math display=\"inline\"><mfrac><mrow><mn>88</mn><mi>&#160;</mi><mo>&#247;</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>k</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>k</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = 1<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>9</mn></mrow><mrow><mn>36</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>35</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>k</mi><mn>2</mn></msup></mrow></mfrac></math> = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> 11k - 9 = 1 + k<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> k<sup>2</sup> - 11k + 10 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k<sup>2</sup> - 10k - k + 10 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k(k - 10) - 1(k - 10) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (k - 10)(k - 1) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k = 10 and 1</p>",
                    solution_hi: "<p>2.(a) <math display=\"inline\"><mfrac><mrow><mn>88</mn><mi>&#160;</mi><mo>&#247;</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>k</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn></mrow><mrow><msup><mrow><mn>6</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>k</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>9</mn></mrow><mrow><mn>36</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>35</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>k</mi><mn>2</mn></msup></mrow></mfrac></math> = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> 11k - 9 = 1 + k<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> k<sup>2</sup> - 11k + 10 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k<sup>2 </sup>- 10k - k + 10 = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k(k - 10) - 1(k - 10) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> (k - 10)(k - 1) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> k = 10 and 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. If the equations 4x + (k &minus; 2)y + 3 = 0 and (k &minus; 2)x + 9y &minus; 5 = 0 (k &gt; 0) are parallel, then find the value of k<sup>2</sup> + 6.</p>",
                    question_hi: "<p>3. यदि समीकरण 4x + (k &minus; 2)y + 3 = 0 और (k &minus; 2)x + 9y &minus; 5 = 0 (k &gt; 0) समांतर हैं, तो k<sup>2 </sup>+ 6 का मान ज्ञात करें।</p>",
                    options_en: ["<p>68</p>", "<p>70</p>", 
                                "<p>72</p>", "<p>64</p>"],
                    options_hi: ["<p>68</p>", "<p>70</p>",
                                "<p>72</p>", "<p>64</p>"],
                    solution_en: "<p>3.(b)<br>For the two parallel lines:- <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math><br>According to the question,<br><math display=\"inline\"><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = 4 , a<sub>2</sub> = (k - 2) , b<sub>1</sub> = (k - 2) , b<sub>2</sub> = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><mo>&#160;</mo><mo>(</mo><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>)</mo></mrow><mn>9</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 36 = (k - 2)<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 6 = k - 2 (as k &gt; 0)<br><math display=\"inline\"><mo>&#8658;</mo></math> k = 8<br>Now, k<sup>2</sup> + 6 = 64 + 6 = 70</p>",
                    solution_hi: "<p>3.(b)<br>दो समानांतर रेखाओं के लिए :- <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\"><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = 4 , a<sub>2</sub> = (k - 2) , b<sub>1</sub> = (k - 2) , b<sub>2</sub> = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><mo>&#160;</mo><mo>(</mo><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>)</mo><mo>&#160;</mo></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>)</mo></mrow><mn>9</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 36 = (k - 2)<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 6 = k - 2 <strong>(जैसा कि K &gt; 0)</strong><br><math display=\"inline\"><mo>&#8658;</mo></math> k = 8<br>अब, k<sup>2</sup> + 6 = 64 + 6 = 70</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Let a + b = 2c, then which of the following expressions is true?</p>",
                    question_hi: "<p>4. मान लीजिए a + b = 2c है, तो निम्नलिखित में से कौन सा व्यंजक सही है?</p>",
                    options_en: ["<p>a&sup2; + 2ac = b&sup2; + 2bc</p>", "<p>a<sup>2</sup> - 2bc = b&sup2; - 2ac</p>", 
                                "<p>a&sup2; + 2bc = b&sup2; + 2ac</p>", "<p>a&sup2; - ac = b&sup2; - bc</p>"],
                    options_hi: ["<p>a&sup2; + 2ac = b&sup2; + 2bc</p>", "<p>a<sup>2</sup> - 2bc = b&sup2; - 2ac</p>",
                                "<p>a&sup2; + 2bc = b&sup2; + 2ac</p>", "<p>a&sup2; - ac = b&sup2; - bc</p>"],
                    solution_en: "<p>4.(c) <br>Given: a + b = 2c<br><math display=\"inline\"><msup><mrow><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>b</mi></mrow><mrow></mrow></msup></math> = c+c<br><math display=\"inline\"><msup><mrow><mi>a</mi><mi>&#160;</mi><mo>-</mo><mi>c</mi></mrow><mrow></mrow></msup></math> = c-b<br>On squaring both side<br><math display=\"inline\"><msup><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>2</mn><mi>a</mi><mi>c</mi></mrow><mrow></mrow></msup></math> = c<sup>2</sup>+b<sup>2</sup>-2bc<br><math display=\"inline\"><msup><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mn>2</mn><mi>b</mi><mi>c</mi></mrow><mrow></mrow></msup></math> = b<sup>2 </sup>+2ac</p>",
                    solution_hi: "<p>4.(c) <br>दिया गया है , a + b = 2c<br><math display=\"inline\"><msup><mrow><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>b</mi></mrow><mrow></mrow></msup></math> = c+c<br><math display=\"inline\"><msup><mrow><mi>a</mi><mi>&#160;</mi><mo>-</mo><mi>c</mi></mrow><mrow></mrow></msup></math> = c-b<br>दोनों तरफ वर्ग करने पर<br><math display=\"inline\"><msup><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><msup><mrow><mi>c</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>2</mn><mi>a</mi><mi>c</mi></mrow><mrow></mrow></msup></math> = c<sup>2</sup>+b<sup>2</sup>-2bc<br><math display=\"inline\"><msup><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mn>2</mn><mi>b</mi><mi>c</mi></mrow><mrow></mrow></msup></math> = b<sup>2</sup> +2ac</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If x, y and z are positive numbers and x + y + z = 1, then the least value of <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>y</mi></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>z</mi></mfrac></math>is :</p>",
                    question_hi: "<p>5. यदि x, y और z धनात्मक संख्याएँ हैं और x + y + z = 1 है, तो <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>y</mi></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>z</mi></mfrac></math>&nbsp;का न्यूनतम मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>7</p>", "<p>5</p>", 
                                "<p>9</p>", "<p>11</p>"],
                    options_hi: ["<p>7</p>", "<p>5</p>",
                                "<p>9</p>", "<p>11</p>"],
                    solution_en: "<p>5.(c) Let: x = y = z = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>Then, <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>y</mi></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>z</mi></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></mfrac><mo>+</mo><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></mfrac><mo>+</mo><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></mfrac></math><br>= 3 + 3 + 3 = 9</p>",
                    solution_hi: "<p>5.(c) माना x = y = z = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>तब ,<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>y</mi></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>z</mi></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></mfrac><mo>+</mo><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></mfrac><mo>+</mo><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></mfrac></math><br>= 3 + 3 + 3 = 9</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math> = 12, then find the value of a<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math>.</p>",
                    question_hi: "<p>6. यदि a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math> = 12 है, तो a<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math>का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>144</p>", "<p>146</p>", 
                                "<p>142</p>", "<p>140</p>"],
                    options_hi: ["<p>144</p>", "<p>146</p>",
                                "<p>142</p>", "<p>140</p>"],
                    solution_en: "<p>6.(c) <br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math>= (a+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math>)<sup>2</sup> - 2&nbsp;<br>= <math display=\"inline\"><mn>1</mn><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> - 2 = 142</p>",
                    solution_hi: "<p>6.(c) <br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>2</mn></msup></mfrac></math>= (a+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>a</mi></mfrac></math>)<sup>2</sup> - 2 <br>= <math display=\"inline\"><mn>1</mn><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> - 2 = 142</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. If the sum of three numbers is 18 and the sum of their squares is 36, then find the difference between the sum of their cubes and three times of their product.</p>",
                    question_hi: "<p>7. यदि तीन संख्याओं का योग 18 है और उनके वर्गों का योग 36 है, तो उनके घनों के योग और उनके गुणनफल के तीन गुना के बीच अंतर ज्ञात कीजिए।</p>",
                    options_en: ["<p>1449</p>", "<p>&minus;1944</p>", 
                                "<p>&minus;1494</p>", "<p>4149</p>"],
                    options_hi: ["<p>1449</p>", "<p>&minus;1944</p>",
                                "<p>&minus;1494</p>", "<p>4149</p>"],
                    solution_en: "<p>7.(b) <br>Given that, a+b+c = 18 &amp; <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+b<sup>2</sup>+c<sup>2</sup> = 36<br>Then, <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math>+b<sup>3</sup>+c<sup>3</sup> - 3abc = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>(a+b+c) [3(a<sup>2</sup>+b<sup>2</sup>+c<sup>2</sup>)-(a+b+c)<sup>2</sup>]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&times;18[3&times;36 - 18<sup>2</sup>]<br>= 9[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>108</mn><mo>-</mo><mn>324</mn></math>] = 9&times;(-216) = -1944</p>",
                    solution_hi: "<p>7.(b) <br>मान लें कि, a+b+c = 18 &amp; <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math>+b<sup>2</sup>+c<sup>2</sup> = 36<br>तब,<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math>+b<sup>3</sup>+c<sup>3</sup> - 3abc = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>(a+b+c) [3(a<sup>2</sup>+b<sup>2</sup>+c<sup>2</sup>)-(a+b+c)<sup>2</sup>]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&times;18[3&times;36 - 18<sup>2</sup>]<br>= 9[180-324] = 9&times;(-216) = -1944</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Find the values of x, y and z, so as to satisfy the equations given below:<br>x + y + z = 12; x + y -z = 6; x - y + z = 4</p>",
                    question_hi: "<p>8. x,y और z के वे मान ज्ञात करें, जिससे नीचे दिए गए समीकरणों को संतुष्ट किया जा सके:<br>x + y + z = 12; x + y -z = 6; x - y + z = 4</p>",
                    options_en: ["<p>x =5, y = 4, z =- 3</p>", "<p>x =5, y =- 4, z = 3</p>", 
                                "<p>x=5, y = 4, z = 3</p>", "<p>x =5, y =- 4, z =- 3</p>"],
                    options_hi: ["<p>x =5, y = 4, z =- 3</p>", "<p>x =5, y =- 4, z = 3</p>",
                                "<p>x=5, y = 4, z = 3</p>", "<p>x =5, y =- 4, z =- 3</p>"],
                    solution_en: "<p>8.(c) After checking all the options one by one, only option (c) satisfied.<br>x=5,y=4,z=3<br><math display=\"inline\"><mo>&#8658;</mo></math> x + y + z = 12; <br>LHS = 5 + 4 + 3 = 12 = RHS<br><math display=\"inline\"><mo>&#8658;</mo></math> x + y -z = 6;<br>LHS = 5 + 4 - 3 = 6 = RHS <br><math display=\"inline\"><mo>&#8658;</mo></math> x - y + z = 4<br>LHS = 5 - 4 + 3 = 4 = RHS</p>",
                    solution_hi: "<p>8.(c) सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (c) संतुष्ट है।<br>x = 5 , y = 4 , z = 3 <br><math display=\"inline\"><mo>&#8658;</mo></math> x + y + z = 12; <br>बायाँ पक्ष = 5 + 4 + 3 = 12 = दायाँ पक्ष<br><math display=\"inline\"><mo>&#8658;</mo></math> x + y -z = 6;<br>बायाँ पक्ष = 5 + 4 - 3 = 6 = दायाँ पक्ष<br><math display=\"inline\"><mo>&#8658;</mo></math> x - y + z = 4<br>बायाँ पक्ष = 5 - 4 + 3 = 4 = दायाँ पक्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. If <math display=\"inline\"><msqrt><mi>x</mi></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math> = 7, then the value of x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> x is equal to:</p>",
                    question_hi: "<p>9. यदि <math display=\"inline\"><msqrt><mi>x</mi></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math>= 7 है, तो x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> का मान निम्न में से किसके बराबर होगा?</p>",
                    options_en: ["<p>47</p>", "<p>49</p>", 
                                "<p>51</p>", "<p>45</p>"],
                    options_hi: ["<p>47</p>", "<p>49</p>",
                                "<p>51</p>", "<p>45</p>"],
                    solution_en: "<p>9.(a) <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>x</mi></msqrt></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math>&nbsp;= 7<br>On squaring both side, <br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>+ 2 = 49<br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>= 47</p>",
                    solution_hi: "<p>9.(a) <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>x</mi></msqrt></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi>x</mi></msqrt></mfrac></math> = 7<br>दोनो पक्षो का वर्ग करने पर, <br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>+ 2 = 49<br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>= 47</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If <math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; = 280 and xy = 120, then find the value of<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>y</mi></mrow><mrow><mi>x</mi><mo>+</mo><mi>y</mi><mo>&#160;</mo></mrow></mfrac></math>&nbsp;</p>",
                    question_hi: "<p>10. यदि <math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; = 280 और xy = 120 है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>y</mi></mrow><mrow><mo>&#160;</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>&#160;</mo></mrow></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>13</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mi>&#160;</mi><msqrt><mn>15</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\"><mi>&#160;</mi><msqrt><mn>13</mn></msqrt></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>15</mn></msqrt></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>13</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mi>&#160;</mi><msqrt><mn>15</mn></msqrt></math></p>",
                                "<p><math display=\"inline\"><mi>&#160;</mi><msqrt><mn>13</mn></msqrt></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>15</mn></msqrt></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(a)<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; = 280 and xy = 120<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; - 2xy = 280 - 2 &times; 120 = 40 &hellip;&hellip;&hellip;&hellip;&hellip;&hellip;..(i)<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; + 2xy = 280 + 2 &times; 120 = 520 &hellip;&hellip;&hellip;&hellip;&hellip;.(ii)<br>Now, on dividing (i) by (ii):<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi>xy</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>520</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>13</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>13</mn></msqrt></mfrac></math></p>",
                    solution_hi: "<p>10.(a)<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; = 280 and xy = 120<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; - 2xy = 280 - 2 &times; 120 = 40 &hellip;&hellip;&hellip;&hellip;&hellip;&hellip;..(i)<br><math display=\"inline\"><mi>x</mi></math>&sup2; + y&sup2; + 2xy = 280 + 2 &times; 120 = 520 &hellip;&hellip;&hellip;&hellip;&hellip;.(ii)<br>अब, (i) को (ii) से विभाजित करने पर:<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi>xy</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>520</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>13</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mrow><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>y</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>13</mn></msqrt></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. For what value of k will the lines 2<math display=\"inline\"><mi>x</mi></math> + 7ky &ndash; 8 = 0 and x + y &ndash; 9 = 0 have no solution ?</p>",
                    question_hi: "<p>11. k के कौन-से मान के लिए समीकरण 2<math display=\"inline\"><mi>x</mi></math> + 7ky &ndash; 8 = 0 और x + y &ndash; 9 = 0 का कोई हल नहीं होगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>11.(a)<br>If the lines have no solution<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math>,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>k</mi></mrow><mn>1</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> k = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math></p>",
                    solution_hi: "<p>11.(a)<br>यदि रेखाओं का कोई हल नहीं है,<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math>,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>k</mi></mrow><mn>1</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> k = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math> , where x &gt; 1, then the value of (x<sup>3</sup>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>) is:</p>",
                    question_hi: "<p>12. यदि x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math> है, जहाँ x &gt; 1 है, तो (x<sup>3</sup>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>221</p>", "<p>234</p>", 
                                "<p>198</p>", "<p>216</p>"],
                    options_hi: ["<p>221</p>", "<p>234</p>",
                                "<p>198</p>", "<p>216</p>"],
                    solution_en: "<p>12.(b)<br>x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math><br>x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>10</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mo>&#160;</mo></msqrt></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>40</mn><mo>-</mo><mn>4</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>&#160;</mo><mn>36</mn><mo>&#160;</mo></msqrt></math> = 6 (as x &gt; 1)<br>So, x<sup>3</sup> - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math> = (6)<sup>3</sup>+3&times;6 = 234</p>",
                    solution_hi: "<p>12.(b)<br>x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math><br>x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>10</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mo>&#160;</mo></msqrt></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>40</mn><mo>-</mo><mn>4</mn></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>&#160;</mo><mn>36</mn><mo>&#160;</mo></msqrt></math> = 6 (as x &gt; 1)<br>अतः, x<sup>3</sup> - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math> = (6)<sup>3</sup>+3&times;6 = 234</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Solve the given set of three equations to get the value of the unknowns:<br>x + 2z = 3<br>x + 2y + 3z = 5<br>3x - 5z = -13</p>",
                    question_hi: "<p>13. अज्ञात मान प्राप्त करने के लिए दिए गए तीन समीकरणों के समूह को हल कीजिए:<br>x + 2z = 3<br>x + 2y + 3z = 5<br>3x - 5z = -13</p>",
                    options_en: ["<p>x = 0,y = -1,z = 2</p>", "<p>x = -1,y = 0,z = 2</p>", 
                                "<p>x = -1,y = 2,z = 0</p>", "<p>x = 2,y = 0,z = -1</p>"],
                    options_hi: ["<p>x = 0,y = -1,z = 2</p>", "<p>x = -1,y = 0,z = 2</p>",
                                "<p>x = -1,y = 2,z = 0</p>", "<p>x = 2,y = 0,z = -1</p>"],
                    solution_en: "<p>13.(b) <br>Given :- x + 2z = 3<br>x + 2y + 3z = 5<br>3x - 5z = -13<br>By checking all options one by one, only option (b) satisfies,<br>hence, x = -1, y = 0, z = 2</p>",
                    solution_hi: "<p>13.(b) <br>दिया गया है :- x + 2z = 3<br>x + 2y + 3z = 5<br>3x - 5z = -13<br>सभी विकल्पों को एक-एक करके जांचने पर, केवल विकल्प (b) संतुष्ट होता है,<br>इसलिए, x = -1, y = 0, z = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. If 0.4x + 0.16y = 1.7 and 0.3x + 0.12y = 3.4, then which of the following is correct?</p>",
                    question_hi: "<p>14. यदि 0.4x + 0.16y = 1.7 और 0.3x + 0.12y = 3.4 है, तो निम्नलिखित में से कौन सा कथन सही है?</p>",
                    options_en: ["<p>The system has finitely many solutions but not unique.</p>", "<p>The system has infinitely many solutions.</p>", 
                                "<p>The system has no solution.</p>", "<p>The system has unique solution.</p>"],
                    options_hi: ["<p>निकाय में परिमित रूप से अनेक हल हैं लेकिन अद्वितीय नहीं हैं।</p>", "<p>निकाय के अपरिमित रूप से अनेक हल हैं।</p>",
                                "<p>निकाय का कोई हल नहीं है।</p>", "<p>निकाय का अद्वितीय हल है।</p>"],
                    solution_en: "<p>14.(c) <br>4x + 0.16y - 1.7 = 0<br>0.3x + 0.12y - 3.4 = 0<br>Comparing these two equations with a<sub>1</sub>x+b<sub>1</sub>y+C<sub>1</sub> = 0 &amp; a<sub>2</sub>x+b<sub>2</sub>y+C<sub>2</sub> = 0. We have ;<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>3</mn></mrow></mfrac><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>,</mo><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>16</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>12</mn></mrow></mfrac><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>,</mo><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>1</mn><mo>.</mo><mn>7</mn></mrow><mrow><mo>-</mo><mn>3</mn><mo>.</mo><mn>4</mn></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>a</mi><mn>1</mn></msub><msub><mi>a</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math><strong id=\"docs-internal-guid-af545356-7fff-e3fe-d1fa-5220f4172b47\">&ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></strong><br>Clearly, we can see that the equations has no solution.</p>",
                    solution_hi: "<p>14.(c) <br>4x + 0.16y - 1.7 = 0<br>0.3x + 0.12y - 3.4 = 0<br>इन दोनो समीकरणों की तुलना a<sub>1</sub>x+b<sub>1</sub>y+C<sub>1</sub> = 0 &amp; a<sub>2</sub>x+b<sub>2</sub>y+C<sub>2</sub> = 0 से करने पर,&nbsp;<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>3</mn></mrow></mfrac><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>,</mo><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><mrow><mn>0</mn><mo>.</mo><mn>16</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>12</mn></mrow></mfrac><mo>=</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>,</mo><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>1</mn><mo>.</mo><mn>7</mn></mrow><mrow><mo>-</mo><mn>3</mn><mo>.</mo><mn>4</mn></mrow></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>a</mi><mn>1</mn></msub><msub><mi>a</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math><strong id=\"docs-internal-guid-af545356-7fff-e3fe-d1fa-5220f4172b47\">&ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></strong><br>स्पष्ट रुप से देख सकते है इन दोनो समीकरणो का कोई हल नही है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#160;</mi><mn>9</mn><mi>x</mi></mrow></mfrac></math> = 3, then the value of 9x&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>15. यदि x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#160;</mi><mn>9</mn><mi>x</mi></mrow></mfrac></math> = 3 हो, तो 9x&sup2; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math>का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>87</p>", "<p>81</p>", 
                                "<p>77</p>", "<p>79</p>"],
                    options_hi: ["<p>87</p>", "<p>81</p>",
                                "<p>77</p>", "<p>79</p>"],
                    solution_en: "<p>15.(d)<br>x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#160;</mi><mn>9</mn><mi>x</mi></mrow></mfrac></math> = 3<br>Multiplying both side by 3, we have ;<br>3x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>x</mi></mrow></mfrac></math> = 9<br>Then, 9x&sup2; + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mn>9</mn><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math>= 9<sup>2</sup> - 2 = 81 - 2 = 79</p>",
                    solution_hi: "<p>15.(d)<br>x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#160;</mi><mn>9</mn><mi>x</mi></mrow></mfrac></math> = 3<br>दोनो तरफ 3 से गुणा करने , हमे प्राप्त होता है ;<br>3x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>x</mi></mrow></mfrac></math> = 9<br>तब, 9x&sup2; + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mn>9</mn><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math>= 9<sup>2</sup> - 2 = 81 - 2 = 79</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>