<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Select the correctly spelt word.</span></p>\\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Select the correctly spelt word.</span></p>\\n",
                    options_en: ["<p>Business</p>\\n", "<p>Buisnesc</p>\\n", 
                                "<p>Buisness</p>\\n", "<p>Busines</p>\\n"],
                    options_hi: ["<p>Business</p>\\n", "<p>Buisnesc</p>\\n",
                                "<p>Buisness</p>\\n", "<p>Busines</p>\\n"],
                    solution_en: "<p>1.(a) <span style=\"font-family: Cambria Math;\">Business</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Business&rsquo; is the correct spelling.</span></p>\\n",
                    solution_hi: "<p>1.(a) <span style=\"font-family: Cambria Math;\">Business</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Business&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> spelling </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the word &lsquo;Keen&rsquo; from the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">I am eager and happy to visit the beautiful city of Darjeeling next month with my family.</span></p>\\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the word &lsquo;Keen&rsquo; from the given sentence. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">I am eager and happy to visit the beautiful city of Darjeeling next month with my family.</span></p>\\n",
                    options_en: ["<p>beautiful</p>\\n", "<p>visit</p>\\n", 
                                "<p>eager</p>\\n", "<p>happy</p>\\n"],
                    options_hi: ["<p>beautiful</p>\\n", "<p>visit</p>\\n",
                                "<p>eager</p>\\n", "<p>happy</p>\\n"],
                    solution_en: "<p>2.(c)&nbsp;<strong>Eager- </strong><span style=\"font-weight: 400;\">very enthusiastic or excited.</span></p>\\r\\n<p><strong>Keen- </strong><span style=\"font-weight: 400;\">very interested or enthusiastic.</span></p>\\r\\n<p><strong>Beautiful- </strong><span style=\"font-weight: 400;\">attractive or pleasing to the senses.</span></p>\\r\\n<p><strong>Visit- </strong><span style=\"font-weight: 400;\">go somewhere to see someone or spend time.</span></p>\\r\\n<p><strong>Happy- </strong><span style=\"font-weight: 400;\">feeling or showing pleasure.</span></p>\\n",
                    solution_hi: "<p>2.(c)&nbsp;<strong>Eager </strong><span style=\"font-weight: 400;\">(&#2313;&#2340;&#2381;&#2360;&#2369;&#2325;) - very enthusiastic or excited.</span></p>\\r\\n<p><strong>Keen </strong><span style=\"font-weight: 400;\">(&#2311;&#2330;&#2381;&#2331;&#2369;&#2325;) -</span><strong> </strong><span style=\"font-weight: 400;\">very interested or enthusiastic.</span></p>\\r\\n<p><strong>Beautiful </strong><span style=\"font-weight: 400;\">(&#2360;&#2369;&#2306;&#2342;&#2352;) - attractive or pleasing to the senses.</span></p>\\r\\n<p><strong>Visit</strong><span style=\"font-weight: 400;\"> (&#2351;&#2366;&#2340;&#2381;&#2352;&#2366; &#2325;&#2352;&#2344;&#2366;)- go somewhere to see someone or spend time.</span></p>\\r\\n<p><strong>Happy </strong><span style=\"font-weight: 400;\">(&#2346;&#2381;&#2352;&#2360;&#2344;&#2381;&#2344;) -</span><strong> </strong><span style=\"font-weight: 400;\">feeling or showing pleasure.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM for &lsquo;scarcity&rsquo; in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">In spite of having ice glaciers across the globe, people don&rsquo;t have excess of fresh drinking water.</span></p>\\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM for &lsquo;scarcity&rsquo; in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">In spite of having ice glaciers across the globe, people don&rsquo;t have excess of fresh drinking water.</span></p>\\n",
                    options_en: ["<p>Excess</p>\\n", "<p>Water</p>\\n", 
                                "<p>Glaciers</p>\\n", "<p>Fresh</p>\\n"],
                    options_hi: ["<p>Excess</p>\\n", "<p>Water</p>\\n",
                                "<p>Glaciers</p>\\n", "<p>Fresh</p>\\n"],
                    solution_en: "<p>3.(a)&nbsp;<strong>Excess- </strong><span style=\"font-weight: 400;\">too much of something.</span></p>\\r\\n<p><strong>Scarcity- </strong><span style=\"font-weight: 400;\">not enough of something.</span></p>\\r\\n<p><strong>Glaciers- </strong><span style=\"font-weight: 400;\">large ice masses.</span></p>\\r\\n<p><strong>Fresh- </strong><span style=\"font-weight: 400;\">clean and not spoiled.</span></p>\\n",
                    solution_hi: "<p>3.(a)&nbsp;<strong>Excess (</strong><span style=\"font-weight: 400;\">&#2309;&#2343;&#2367;&#2325;&#2340;&#2366;) -</span><strong> </strong><span style=\"font-weight: 400;\">too much of something.</span></p>\\r\\n<p><strong>Scarcity </strong><span style=\"font-weight: 400;\">(&#2309;&#2349;&#2366;&#2357;) -</span><strong> </strong><span style=\"font-weight: 400;\">not enough of something.</span></p>\\r\\n<p><strong>Glaciers </strong><span style=\"font-weight: 400;\">(&#2361;&#2367;&#2350;&#2344;&#2342;) -</span><strong> </strong><span style=\"font-weight: 400;\">large ice masses.</span></p>\\r\\n<p><strong>Fresh </strong><span style=\"font-weight: 400;\">(&#2340;&#2366;&#2332;&#2366;) -</span><strong> </strong><span style=\"font-weight: 400;\">clean and not spoiled.</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in passive voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A sudden noise frightened the horse.</span></p>\\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in passive voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A sudden noise frightened the horse.</span></p>\\n",
                    options_en: ["<p>The horse has been frightened by a sudden noise.</p>\\n", "<p>The horse was frightened by a sudden noise.</p>\\n", 
                                "<p>Th<span style=\"font-family: Cambria Math;\">e horse had frightened by a sudden noise.</span></p>\\n", "<p>The horse got frightened by a sudden noise.</p>\\n"],
                    options_hi: ["<p>The horse has been frightened by a sudden noise.</p>\\n", "<p>The horse was frightened by a sudden noise.</p>\\n",
                                "<p>The horse had frightened by a sudden noise.</p>\\n", "<p>The horse got frightened by a sudden noise.</p>\\n"],
                    solution_en: "<p>4.(b) <span style=\"font-family: Cambria Math;\">The horse was frightened by a sudde</span><span style=\"font-family: Cambria Math;\">n noise.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) The horse </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">has been</span></span><span style=\"font-weight: 400;\"> frightened by a sudden noise.(Incorrect Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(c) The horse </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">had</span></span><span style=\"font-weight: 400;\"> frightened by a sudden noise.(Incorrect Helping Verb)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) The horse </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">got</span></span><span style=\"font-weight: 400;\"> frightened by a sudden noise.(Incorrect Verb)</span></p>\\n",
                    solution_hi: "<p>4.(b)&nbsp;<span style=\"font-weight: 400;\">The horse was frightened by a sudden noise.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(a) The horse </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">has been</span></span><span style=\"font-weight: 400;\"> frightened by a sudden noise.(&#2327;&#2354;&#2340; Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(c) The horse </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">had</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>frightened by a sudden noise.(&#2327;&#2354;&#2340; Helping Verb)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) The horse </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">got</span></span><span style=\"font-weight: 400;\"> frightened by a sudden noise.(&#2327;&#2354;&#2340; Verb)</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">In today&rsquo;s world, technology </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">must be used for feed</span></span><span style=\"font-family: Cambria Math;\"> the changing forces in society.</span></p>\\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">In today&rsquo;s world, technology </span><span style=\"font-family: Cambria Math;\">must be used for feed</span><span style=\"font-family: Cambria Math;\"> the changing forces in society.</span></p>\\n",
                    options_en: ["<p>must had been used to feed</p>\\n", "<p>must have been used to feed</p>\\n", 
                                "<p>mu<span style=\"font-family: Cambria Math;\">st used to feed</span></p>\\n", "<p>must be used to feed</p>\\n"],
                    options_hi: ["<p>must had been used<span style=\"font-family: Cambria Math;\"> to feed</span></p>\\n", "<p>must have been used to feed</p>\\n",
                                "<p>must used to feed</p>\\n", "<p>must be used to feed</p>\\n"],
                    solution_en: "<p>5.(d)&nbsp;<span style=\"font-weight: 400;\">must be used to feed</span></p>\\r\\n<p><span style=\"font-weight: 400;\">We generally use an infinitive</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub><mo>)</mo></math><span style=\"font-weight: 400;\"> with the preposition &lsquo;to&rsquo;. Hence, &lsquo;must be used to feed</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub><mo>)</mo></math><span style=\"font-weight: 400;\">&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>5.(d)&nbsp;<span style=\"font-weight: 400;\">must be used to feed</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&#2361;&#2350; &#2310;&#2350;&#2340;&#2380;&#2352; &#2346;&#2352; preposition &lsquo;to&rsquo; &#2325;&#2375; &#2360;&#2366;&#2341; infinitive</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msub><mi>V</mi><mn>1</mn></msub><mo>)</mo></math><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, &lsquo;must be used to feed</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msub><mi>V</mi><mn>1</mn></msub><mo>)</mo></math><span style=\"font-weight: 400;\">)&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Select the option with the correct spellings to repl</span><span style=\"font-family: Cambria Math;\">ace the underlined words in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">enchanteng scenary</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"> </span>of the beautiful lake was mesmerising.</span></p>\\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">Select the option with the correct spellings to replace the underlined words in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\">enchanteng scenary</span><span style=\"font-family: Cambria Math;\"> of the beautiful lake was mesmerising.</span></p>\\n",
                    options_en: ["<p>enchanting; scenery</p>\\n", "<p>encanting; scenery</p>\\n", 
                                "<p>enchenting; seenary</p>\\n", "<p>enchantine; senery</p>\\n"],
                    options_hi: ["<p>enchanting; scenery</p>\\n", "<p>encanting; scenery</p>\\n",
                                "<p>enchenting; seenary</p>\\n", "<p>enchantine; sen<span style=\"font-family: Cambria Math;\">ery</span></p>\\n"],
                    solution_en: "<p>6.(a) <span style=\"font-family: Cambria Math;\">Enchanting; Scenery</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Enchanting&rsquo;, &lsquo;Scenery&rsquo; are the correct spellings.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>6.(a)&nbsp;<span style=\"font-weight: 400;\">Enchanting; Scenery</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&lsquo;Enchanting&rsquo;, &lsquo;Scenery&rsquo; &#2360;&#2361;&#2368; spellings &#2361;&#2376;&#2404;&nbsp;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Select the option that expresses the given sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The matches are being shown live on Sports TV.</span></p>\\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">Select the op</span><span style=\"font-family: Cambria Math;\">tion that expresses the given sentence in active voice.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The matches are being shown live on Sports TV.</span></p>\\n",
                    options_en: ["<p>Sports TV is showing the matches live.</p>\\n", "<p>Sports TV may be showing the matches live.</p>\\n", 
                                "<p>Sports TV will be showing the matches live.</p>\\n", "<p>Sports TV can be showing the matches live.</p>\\n"],
                    options_hi: ["<p>Sports TV is showing the matches live.</p>\\n", "<p>Sports TV may be showing the matches live.</p>\\n",
                                "<p>Sports TV will be showing the matches live.</p>\\n", "<p>Sports <span style=\"font-family: Cambria Math;\">TV can be showing the matches live.</span></p>\\n"],
                    solution_en: "<p>7.(a)&nbsp;<span style=\"font-weight: 400;\">Sports TV is showing the matches live.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) Sports TV </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">may be</span></span><span style=\"font-weight: 400;\"> showing the matches live.(Incorrect Verb)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(c) Sports TV </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">will be</span></span><span style=\"font-weight: 400;\"> showing the matches live.(Incorrect Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) Sports TV </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">can be</span></span><span style=\"font-weight: 400;\"> showing the matches live.(Incorrect Verb)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>7.(a) <span style=\"font-weight: 400;\">Sports TV is showing the matches live.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) Sports TV </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">may be</span></span><span style=\"font-weight: 400;\"> showing the matches live.(&#2327;&#2354;&#2340; Verb)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(c) Sports TV<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">will be</span></span><span style=\"font-weight: 400;\"> showing the matches live.(&#2327;&#2354;&#2340; Tense)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) Sports TV </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">can be</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> </span>showing the matches live.(&#2327;&#2354;&#2340; Verb)</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> Parts of a sentence are given below in jumbled order. Arrange the parts in the correct </span><span style=\"font-family: Cambria Math;\">order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A. we need to understand where we are and,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B. that will require significant investment</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C</span><span style=\"font-family: Cambria Math;\">. where we are headed, and</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">D. in our data and information infrastructure</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">E. to stay ahead of these crises</span></p>\\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> Parts of a sentence are given below in jumbled order. Arrange the parts in the correct </span><span style=\"font-family: Cambria Math;\">order to for</span><span style=\"font-family: Cambria Math;\">m a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A. we need to understand where we are and,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B. that will require significant investment</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C. where we are headed, and</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">D. in our data and information infrastructure</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">E. to stay ahead of these crises</span></p>\\n",
                    options_en: ["<p>EACBD</p>\\n", "<p>CEDAB</p>\\n", 
                                "<p>BACDE</p>\\n", "<p>BAEDC</p>\\n"],
                    options_hi: ["<p>EACBD</p>\\n", "<p>CEDAB</p>\\n",
                                "<p>BACDE</p>\\n", "<p><span style=\"font-family: Cambria Math;\"> BAEDC</span></p>\\n"],
                    solution_en: "<p>8.(a) <span style=\"font-weight: 400;\">EACBD</span></p>\\r\\n<p><span style=\"font-weight: 400;\">The given sentence starts with Part E as it contains the main idea of the sentence, i.e. to stay ahead of these crises. Part E will be followed by Part A as it contains the subject(&lsquo;we&rsquo;) and main verb(&lsquo;need to&rsquo;) of the sentence. And, Part C talks about the direction in which we are moving. So, C will follow A. Further, Part B talks about the need for investment &amp; Part D states the field in which the investment is needed. So, D will follow B. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</span></p>\\n",
                    solution_hi: "<p>8.(a) <span style=\"font-family: Cambria Math;\">EACBD</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2357;&#2366;&#2325;&#2381;&#2351; Part E &#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2352;&#2350;&#2381;&#2349; &#2361;&#2379;&#2327;&#2366; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2311;&#2360;&#2350;&#2375;&#2306; sentence &#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351; &#2357;&#2367;&#2330;&#2366;&#2352; &lsquo;to stay ahead of these crises&rsquo; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2404; Part E &#2325;&#2375; &#2348;&#2366;&#2342; Part A &#2310;&#2319;&#2327;&#2366; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2311;&#2360;&#2350;&#2375;&#2306; sentence &#2325;&#2366; subject(&lsquo;we&rsquo;) &#2324;&#2352; main verb(&lsquo;need to&rsquo;) &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2404; &#2324;&#2352;, Part C &#2313;&#2360; &#2342;&#2367;&#2358;&#2366; &#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2348;&#2366;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2361;&#2350; &#2310;&#2327;&#2375; &#2348;&#2338;&#2364; &#2352;&#2361;&#2375; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, A &#2325;&#2375; &#2348;&#2366;&#2342; C &#2310;&#2319;&#2327;&#2366;&#2404; &#2311;&#2360;&#2325;&#2375; &#2309;&#2354;&#2366;&#2357;&#2366;, Part B &#2344;&#2367;&#2357;&#2375;&#2358; &#2325;&#2368; &#2310;&#2357;&#2358;&#2381;&#2351;&#2325;&#2340;&#2366; &#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2348;&#2366;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; Part D &#2313;&#2360; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2379; &#2348;&#2340;&#2366;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2344;&#2367;&#2357;&#2375;&#2358; &#2325;&#2368; &#2310;&#2357;&#2358;&#2381;&#2351;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, B &#2325;&#2375; &#2348;&#2366;&#2342;&nbsp; D &#2310;&#2319;&#2327;&#2366;&#2404; Options &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2332;&#2366;&#2344;&#2375; &#2346;&#2352;, option &lsquo;a&rsquo; &#2350;&#2375;&#2306; &#2360;&#2361;&#2368; sequence &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Select the most appropriate homophones to fill in the blanks.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Kindly accept my ________ on your new haircut. It ________ your personality.</span></p>\\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">Select the most appropriate homophones to fill in the blanks.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Kindly accept my ________ on your new haircut. It ________ your personality.</span></p>\\n",
                    options_en: ["<p>compliment; complaints</p>\\n", "<p>compliment; complements</p>\\n", 
                                "<p>compliment; complime<span style=\"font-family: Cambria Math;\">nts</span></p>\\n", "<p>complement; complements</p>\\n"],
                    options_hi: ["<p>compliment; complaints</p>\\n", "<p>compliment; complements</p>\\n",
                                "<p>compliment; com<span style=\"font-family: Cambria Math;\">pliments</span></p>\\n", "<p>complement; complements</p>\\n"],
                    solution_en: "<p>9.(b)&nbsp;<span style=\"font-weight: 400;\">compliment; complements</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&lsquo;Compliment&rsquo; means to politely congratulate or praise&nbsp; and &lsquo;complement&rsquo; means to enhance something. In the given sentence, the speaker is praising the new haircut, and it enhances the person\'s personality. Hence, &lsquo;compliment; complements&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>9.(b)&nbsp;<span style=\"font-weight: 400;\">compliment; complements</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&lsquo;Compliment&rsquo; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2357;&#2367;&#2344;&#2350;&#2381;&#2352;&#2340;&#2366;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325; &#2348;&#2343;&#2366;&#2312; &#2342;&#2375;&#2344;&#2366; &#2351;&#2366; &#2346;&#2381;&#2352;&#2358;&#2306;&#2360;&#2366; &#2325;&#2352;&#2344;&#2366; &#2361;&#2376;,&#2324;&#2352; &lsquo;complement&rsquo; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2325;&#2367;&#2360;&#2368; &#2330;&#2368;&#2332;&#2364; &#2325;&#2379; &#2348;&#2338;&#2364;&#2366;&#2344;&#2366;&#2404; &#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2366;&#2325;&#2381;&#2351; &#2350;&#2375;&#2306; &#2357;&#2325;&#2381;&#2340;&#2366;(speaker) &#2344;&#2319; haircut &#2325;&#2368; &#2346;&#2381;&#2352;&#2358;&#2306;&#2360;&#2366; &#2325;&#2352; &#2352;&#2361;&#2366; &#2361;&#2376; &#2324;&#2352; &#2311;&#2360;&#2360;&#2375; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2325;&#2375; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2340;&#2381;&#2357; &#2350;&#2375;&#2306; &#2344;&#2367;&#2326;&#2366;&#2352; &#2310;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;, &lsquo;compliment; complements&rsquo; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">Parts of a sentence are given below in jumbled order. Select the option that arranges </span><span style=\"font-family: Cambria Math;\">the parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A. embarrassment and difficulty for them and himself</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B. the great gods </span><span style=\"font-family: Cambria Math;\">Odin and Thor, helping them with</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C. his clever plans but sometimes causing</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">D. Loki was represented as the companion of</span></p>\\n",
                    question_hi: "<p>10. <span style=\"font-family: Cambria Math;\">Parts of a sentence are given below in jumbled order. Select the option that arranges </span><span style=\"font-family: Cambria Math;\">the parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A. embarrassment and difficulty for them and himself</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B. the great gods Odin and Thor, helping them with</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C. h</span><span style=\"font-family: Cambria Math;\">is clever plans but sometimes causing</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">D. Loki was represented as the companion of</span></p>\\n",
                    options_en: ["<p>D, B, C, A</p>\\n", "<p>C, D, A, B</p>\\n", 
                                "<p>B, D, C, A</p>\\n", "<p>A, B, D, C</p>\\n"],
                    options_hi: ["<p>D, B, C, A</p>\\n", "<p>C, D, A, B</p>\\n",
                                "<p>B, D, C, A</p>\\n", "<p>A, B, D, C</p>\\n"],
                    solution_en: "<p>10.(a) <span style=\"font-family: Cambria Math;\">D, B, C, A</span></p>\\r\\n<p><span style=\"font-weight: 400;\">The given sentence starts with Part D as it contains the main subject of the sentence, i.e. Loki. Part D will be followed by Part B as it states that Loki was the companion of great gods. Further, Part C states his clever plans &amp; Part A states that his plans sometimes caused difficulty. So, C will follow A. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</span></p>\\n",
                    solution_hi: "<p>10.(a) <span style=\"font-family: Cambria Math;\">D, B, C, A</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2357;&#2366;&#2325;&#2381;&#2351; Part D &#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2352;&#2350;&#2381;&#2349;&nbsp; &#2361;&#2379;&#2327;&#2366; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2311;&#2360;&#2350;&#2375;&#2306; &#2357;&#2366;&#2325;&#2381;&#2351; &#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351; &#2357;&#2367;&#2359;&#2351; &lsquo;Loki&rsquo; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2404; Part D &#2325;&#2375; &#2348;&#2366;&#2342; Part B &#2310;&#2319;&#2327;&#2366; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2311;&#2360;&#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; Loki &#2350;&#2361;&#2366;&#2344; &#2342;&#2375;&#2357;&#2340;&#2366;&#2323;&#2306; &#2325;&#2366; &#2360;&#2366;&#2341;&#2368; &#2341;&#2366;&#2404; &#2311;&#2360;&#2325;&#2375; &#2309;&#2354;&#2366;&#2357;&#2366;, Part C &#2350;&#2375;&#2306; &#2313;&#2344;&#2325;&#2368; &#2330;&#2340;&#2369;&#2352; &#2351;&#2379;&#2332;&#2344;&#2366;&#2319;&#2305; &#2348;&#2340;&#2366;&#2312; &#2327;&#2312; &#2361;&#2376;&#2306; &#2324;&#2352; Part A &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2313;&#2344;&#2325;&#2368; &#2351;&#2379;&#2332;&#2344;&#2366;&#2319;&#2305; &#2325;&#2349;&#2368;-&#2325;&#2349;&#2368; &#2325;&#2336;&#2367;&#2344;&#2366;&#2312; &#2325;&#2366; &#2325;&#2366;&#2352;&#2339; &#2348;&#2344;&#2340;&#2368; &#2341;&#2368;&#2306;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319; C &#2325;&#2375; &#2348;&#2366;&#2342; A &#2310;&#2319;&#2327;&#2366;&#2404; Options &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2332;&#2366;&#2344;&#2375; &#2346;&#2352;, option &lsquo;a&rsquo; &#2350;&#2375;&#2306; &#2360;&#2361;&#2368; sequence &#2361;&#2376;&#2404;</span>&nbsp;</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the word in brackets to fill in the blank.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Since she had never travelled abroad, she was both _______________ (CALM) and </span><span style=\"font-family: Cambria Math;\">nervous for her upcoming trip.</span></p>\\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">Select the most appropriate ANTONYM of the word in brackets to fill in the blank.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Since she had never travelled abroad, she was both _______________ (CALM) and </span><span style=\"font-family: Cambria Math;\">nervous for her upcoming trip.</span></p>\\n",
                    options_en: ["<p>Hyped</p>\\n", "<p>Apathetic</p>\\n", 
                                "<p>Excited</p>\\n", "<p>Hopeful</p>\\n"],
                    options_hi: ["<p>Hyped</p>\\n", "<p>Apathetic</p>\\n",
                                "<p>Excited</p>\\n", "<p>Hopeful</p>\\n"],
                    solution_en: "<p>11.(c) <strong>Excited- </strong><span style=\"font-weight: 400;\">eager or enthusiastic about something.</span></p>\\r\\n<p><strong>Calm- </strong><span style=\"font-weight: 400;\">peaceful and relaxed.</span></p>\\r\\n<p><strong>Hyped- </strong><span style=\"font-weight: 400;\">making something sound more exciting than it really is.</span></p>\\r\\n<p><strong>Apathetic-</strong><span style=\"font-weight: 400;\"> lacking interest, enthusiasm, or concern.</span></p>\\r\\n<p><strong>Hopeful- </strong><span style=\"font-weight: 400;\">feeling optimism or confidence about the future.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>11.(c)&nbsp;<strong>Excited </strong><span style=\"font-weight: 400;\">(&#2313;&#2340;&#2381;&#2340;&#2375;&#2332;&#2367;&#2340; &#2361;&#2379;&#2344;&#2366;)-</span><strong> </strong><span style=\"font-weight: 400;\">eager or enthusiastic about something.</span></p>\\r\\n<p><strong>Calm </strong><span style=\"font-weight: 400;\">(&#2358;&#2366;&#2306;&#2340;) - peaceful and relaxed.</span></p>\\r\\n<p><strong>Hyped </strong><span style=\"font-weight: 400;\">( &#2348;&#2397;&#2366; - &#2330;&#2397;&#2366; &#2325;&#2375; &#2346;&#2381;&#2352;&#2330;&#2366;&#2352; &#2325;&#2352;&#2344;&#2366; )- making something sound more exciting than it really is.</span></p>\\r\\n<p><strong>Apathetic </strong><span style=\"font-weight: 400;\">(&#2349;&#2366;&#2357;&#2361;&#2368;&#2344;)- lacking interest, enthusiasm, or concern.</span></p>\\r\\n<p><strong>Hopeful</strong><span style=\"font-weight: 400;\"> (&#2310;&#2358;&#2366;&#2346;&#2370;&#2352;&#2381;&#2339;)-</span><strong> </strong><span style=\"font-weight: 400;\">feeling optimism or confidence about the future.</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym for the underlined word in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The guest was </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">heckled</span></span><span style=\"font-family: Cambria Math;\"> by the spectators.</span></p>\\n",
                    question_hi: "<p>12. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym for the underlined word in the given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The guest was<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">heckled</span></span><span style=\"font-family: Cambria Math;\"> by the spectators.</span></p>\\n",
                    options_en: ["<p>Calmed</p>\\n", "<p>Aided</p>\\n", 
                                "<p>Taunted</p>\\n", "<p>Helped</p>\\n"],
                    options_hi: ["<p>Calmed</p>\\n", "<p>Aided</p>\\n",
                                "<p>Taunted</p>\\n", "<p>Helped</p>\\n"],
                    solution_en: "<p>12.(c)&nbsp;<strong>Taunted-</strong><span style=\"font-weight: 400;\"> teased or provoked someone mockingly.</span></p>\\r\\n<p><strong>Heckled- </strong><span style=\"font-weight: 400;\">unpleasantly interrupted or harassed.</span></p>\\r\\n<p><strong>Calmed- </strong><span style=\"font-weight: 400;\">made peaceful or relaxed.</span></p>\\r\\n<p><strong>Aided- </strong><span style=\"font-weight: 400;\">provided assistance or support.</span></p>\\r\\n<p><strong>Helped- </strong><span style=\"font-weight: 400;\">assisted or gave support to someone.</span></p>\\n",
                    solution_hi: "<p>12.(c)&nbsp;<strong>aunted</strong><span style=\"font-weight: 400;\"> (&#2313;&#2346;&#2361;&#2366;&#2360; &#2325;&#2352;&#2344;&#2366;)- teased or provoked someone mockingly.</span></p>\\r\\n<p><strong>Heckled </strong><span style=\"font-weight: 400;\">(&#2340;&#2306;&#2327; &#2351;&#2366; &#2346;&#2352;&#2375;&#2358;&#2366;&#2344; &#2325;&#2352;&#2344;&#2366;) - unpleasantly interrupted or harassed.</span></p>\\r\\n<p><strong>Calmed</strong><span style=\"font-weight: 400;\"> (&#2358;&#2366;&#2306;&#2340;) - made peaceful or relaxed.</span></p>\\r\\n<p><strong>Aided</strong><span style=\"font-weight: 400;\"> (</span><span style=\"font-weight: 400;\">&#2360;&#2361;&#2366;&#2351;&#2340;&#2366; &#2342;&#2375;&#2344;&#2366;</span><span style=\"font-weight: 400;\">)-</span><strong> </strong><span style=\"font-weight: 400;\">provided assistance or support.</span></p>\\r\\n<p><strong>Helped</strong><span style=\"font-weight: 400;\"> (&#2360;&#2361;&#2366;&#2351;&#2340;&#2366; &#2325;&#2352;&#2344;&#2366; ) -</span><strong> </strong><span style=\"font-weight: 400;\">assisted or gave support to someone.</span>&nbsp;</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of </span><span style=\"font-family: Cambria Math;\">words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The vehicle which carries dead bodies</span></p>\\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">Select the option that can be used as a one-word substitute for the given group of </span><span style=\"font-family: Cambria Math;\">words.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The vehicle which carries dead bodies</span></p>\\n",
                    options_en: ["<p>Coffin</p>\\n", "<p>Hearse</p>\\n", 
                                "<p>Ambulance</p>\\n", "<p>Corpse</p>\\n"],
                    options_hi: ["<p>Coffin</p>\\n", "<p>Hearse</p>\\n",
                                "<p>Ambulance</p>\\n", "<p>Corpse</p>\\n"],
                    solution_en: "<p>13.(b)&nbsp;<strong>Hearse -</strong><span style=\"font-weight: 400;\"> the vehicle which carries dead bodies.</span></p>\\r\\n<p><strong>Coffin- </strong><span style=\"font-weight: 400;\">a box used for burying or cremating deceased bodies.</span></p>\\r\\n<p><strong>Ambulance- </strong><span style=\"font-weight: 400;\">the vehicle used for transporting sick or injured people to the hospital.</span></p>\\r\\n<p><strong>Corpse- </strong><span style=\"font-weight: 400;\">the dead body of a human or animal.</span><span style=\"font-weight: 400;\">&nbsp;</span></p>\\n",
                    solution_hi: "<p>13.(b)<strong>&nbsp;Hearse </strong><span style=\"font-weight: 400;\">(&#2358;&#2357; &#2357;&#2366;&#2361;&#2344;)- the vehicle which carries dead bodies.</span></p>\\r\\n<p><strong>Coffin</strong><span style=\"font-weight: 400;\"> (&#2340;&#2366;&#2348;&#2370;&#2340;) -</span><strong> </strong><span style=\"font-weight: 400;\">a box used for burying or cremating deceased bodies.</span></p>\\r\\n<p><strong>Ambulance</strong><span style=\"font-weight: 400;\"> (&#2319;&#2350;&#2381;&#2348;&#2369;&#2354;&#2375;&#2306;&#2360;)- the vehicle used for transporting sick or injured people to the hospital.</span></p>\\r\\n<p><strong>Corpse</strong><span style=\"font-weight: 400;\"> (&#2358;&#2357;) -</span><strong> </strong><span style=\"font-weight: 400;\">the dead body of a human or animal.</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> Select the most appropriate opt</span><span style=\"font-family: Cambria Math;\">ion that can substitute the underlined words in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The principal posted all the staff rules </span><span style=\"font-family: Cambria Math;\">i<span style=\"text-decoration: underline;\">n black and white</span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">.</span></span></p>\\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> Select the most appropriate option that can substitu</span><span style=\"font-family: Cambria Math;\">te the underlined words in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The principal posted all the staff rules </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">in black and white</span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">.</span></span></p>\\n",
                    options_en: ["<p>on chart</p>\\n", "<p>on board</p>\\n", 
                                "<p>in writing</p>\\n", "<p>in coloured print</p>\\n"],
                    options_hi: ["<p>on chart</p>\\n", "<p>on board</p>\\n",
                                "<p>in writing</p>\\n", "<p>in coloured print</p>\\n"],
                    solution_en: "<p>14.(c) <span style=\"font-family: Cambria Math;\">In black and white- in writing.</span></p>\\n",
                    solution_hi: "<p>14.(c) <span style=\"font-family: Cambria Math;\">In black and white - in writing. / </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Support</span></p>\\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> Select the most appropriate ANTONYM of the given word.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Support</span></p>\\n",
                    options_en: ["<p>Oppose</p>\\n", "<p>Stay</p>\\n", 
                                "<p>Bear</p>\\n", "<p>Stand</p>\\n"],
                    options_hi: ["<p>Oppose</p>\\n", "<p>Stay</p>\\n",
                                "<p>Bear</p>\\n", "<p>Stand</p>\\n"],
                    solution_en: "<p>15.(a) <strong>Oppose- </strong><span style=\"font-weight: 400;\">to be against or disagree with.</span></p>\\r\\n<p><strong>Support-</strong><span style=\"font-weight: 400;\"> to help or assist.</span></p>\\r\\n<p><strong>Stay- </strong><span style=\"font-weight: 400;\">to remain in one place.</span></p>\\r\\n<p><strong>Bear- </strong><span style=\"font-weight: 400;\">to endure or tolerate.</span></p>\\r\\n<p><strong>Stand- </strong><span style=\"font-weight: 400;\">to be in an upright position.</span></p>\\n",
                    solution_hi: "<p>15.(a) <strong>Oppose</strong><span style=\"font-weight: 400;\"> (&#2309;&#2360;&#2361;&#2350;&#2340; &#2361;&#2379;&#2344;&#2366;) -</span><strong> </strong><span style=\"font-weight: 400;\">to be against or disagree with.</span></p>\\r\\n<p><strong>Support</strong><span style=\"font-weight: 400;\"> (&#2360;&#2361;&#2366;&#2351;&#2340;&#2366; &#2325;&#2352;&#2344;&#2366;)- to help or assist.</span></p>\\r\\n<p><strong>Stay </strong><span style=\"font-weight: 400;\">(&#2336;&#2361;&#2352;&#2344;&#2366;)- to remain in one place.</span></p>\\r\\n<p><strong>Bear </strong><span style=\"font-weight: 400;\">(&#2360;&#2361;&#2344; &#2325;&#2352;&#2344;&#2366; ) -</span><strong> </strong><span style=\"font-weight: 400;\">to endure or tolerate.</span></p>\\r\\n<p><strong>Stand</strong><span style=\"font-weight: 400;\"> (&#2326;&#2396;&#2375; &#2361;&#2379;&#2344;&#2366;) - to be in an upright position.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the underlined idiom.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The new electric car </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">costs an arm and a le</span><span style=\"font-family: Cambria Math;\">g</span><span style=\"font-family: Cambria Math;\">.</span></span></p>\\n",
                    question_hi: "<p>16. <span style=\"font-family: Cambria Math;\">Select the most appropriate meaning of the u</span><span style=\"font-family: Cambria Math;\">nderlined idiom.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The new electric car </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">costs an arm and a le</span><span style=\"font-family: Cambria Math;\">g</span><span style=\"font-family: Cambria Math;\">.</span></span></p>\\n",
                    options_en: ["<p>Needs physical strength to drive</p>\\n", "<p>Is very expensive</p>\\n", 
                                "<p>Is very cheap</p>\\n", "<p>Has no gears</p>\\n"],
                    options_hi: ["<p>Needs physical strength to drive</p>\\n", "<p>Is very expensive</p>\\n",
                                "<p>Is very cheap</p>\\n", "<p>Has no gears</p>\\n"],
                    solution_en: "<p>16.(b) <span style=\"font-family: Cambria Math;\">Costs an arm and a leg - very expensive.</span></p>\\n",
                    solution_hi: "<p>16.(b) <span style=\"font-family: Cambria Math;\">Costs an arm and a l</span><span style=\"font-family: Cambria Math;\">eg- very expensive./ </span><span style=\"font-family: Cambria Math;\">&#2348;&#2361;&#2369;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2306;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">She can draw very well and just </span><span style=\"font-family: Cambria Math;\">as creative</span><span style=\"font-family: Cambria Math;\">.</span></p>\\n",
                    question_hi: "<p>17. <span style=\"font-family: Cambria Math;\">Select the most appropriate option that can substitute the underlined segment in the </span><span style=\"font-family: Cambria Math;\">given sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">She can draw very well and just<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">as creative</span><span style=\"font-family: Cambria Math;\">.</span></span></p>\\n",
                    options_en: ["<p>as creativer</p>\\n", "<p>as creatively</p>\\n", 
                                "<p>creative than</p>\\n", "<p>creative</p>\\n"],
                    options_hi: ["<p>as creativer</p>\\n", "<p>as creatively</p>\\n",
                                "<p>creative than</p>\\n", "<p>creative</p>\\n"],
                    solution_en: "<p>17.(b) <span style=\"font-weight: 400;\">as creatively</span></p>\\r\\n<p><span style=\"font-weight: 400;\">We use &lsquo;and&rsquo;, &lsquo;or&rsquo; and &lsquo;but&rsquo; to connect two parts of sentences which are similar in grammatical status. Similarly, in the given sentence, \'and\' connects the adverb \'well\' with another adverb \'creatively\'. Hence, \'as creatively\' is the most appropriate answer.</span>&nbsp;</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>17.(b)&nbsp;<span style=\"font-weight: 400;\">as creatively</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&#2361;&#2350; sentences &#2325;&#2375; &#2342;&#2379; &#2349;&#2366;&#2327;&#2379;&#2306; &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &lsquo;and&rsquo;, &lsquo;or&rsquo; &#2324;&#2352; &lsquo;but&rsquo; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306; &#2332;&#2379; grammatical status &#2350;&#2375;&#2306; &#2360;&#2350;&#2366;&#2344; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;, &#2342;&#2367;&#2319; &#2327;&#2319; sentence &#2350;&#2375;&#2306; \'and\', adverb \'well\' &#2325;&#2379; &#2342;&#2370;&#2360;&#2352;&#2375; adverb \'creatively\' &#2360;&#2375; &#2332;&#2379;&#2337;&#2364;&#2340;&#2366; &#2361;&#2376;&#2404; &#2309;&#2340;&#2307; \'as creatively\' &#2361;&#2376; &#2357;&#2361; &#2360;&#2348;&#2360;&#2375; &#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340; &#2313;&#2340;&#2381;&#2340;&#2352; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate idiom for the following statement.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">It\'s better to get less than what you want than get nothing.</span></p>\\n",
                    question_hi: "<p>18. <span style=\"font-family: Cambria Math;\">Select the most appropriate idiom for the following statement.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">It\'s better to get less than what you want than get nothing.</span></p>\\n",
                    options_en: ["<p>Half a loaf is better than none.</p>\\n", "<p>Waste not,<span style=\"font-family: Cambria Math;\"> want not.</span></p>\\n", 
                                "<p>Where there is smoke, there is fire.</p>\\n", "<p>A storm in a teacup</p>\\n"],
                    options_hi: ["<p>Half a loaf is better than none.</p>\\n", "<p>Was<span style=\"font-family: Cambria Math;\">te not, want not.</span></p>\\n",
                                "<p>Where there is smoke, there is fire.</p>\\n", "<p>A storm in a teacup</p>\\n"],
                    solution_en: "<p>18.(a)&nbsp;<span style=\"font-weight: 400;\">Half a loaf is better than none- it\'s better to get less than what you want than get nothing.</span></p>\\r\\n<p><span style=\"font-weight: 400;\">E.g.- Even though I only got a small raise, I know that half a loaf is better than none.</span></p>\\n",
                    solution_hi: "<p>18.(a)&nbsp;<span style=\"font-weight: 400;\">Half a loaf is better than none- it\'s better to get less than what you want than get nothing./ &#2325;&#2369;&#2331; &#2344; &#2361;&#2379;&#2344;&#2375; &#2360;&#2375; &#2341;&#2379;&#2337;&#2364;&#2366; &#2361;&#2379;&#2344;&#2366; &#2348;&#2375;&#2361;&#2340;&#2352; &#2361;&#2376;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">E.g.-&nbsp; Even though I only got a small raise, I know that half a loaf is better than none.</span>&nbsp;</p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">Select the option that expresses the following sentence in superlative degree of </span><span style=\"font-family: Cambria Math;\">comparison. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">It looks like our bad fears are coming true.</span></p>\\n",
                    question_hi: "<p>19. <span style=\"font-family: Cambria Math;\">Select the option that expresses the following sentence in superlative degree of </span><span style=\"font-family: Cambria Math;\">comparison. </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">It looks like our bad fear</span><span style=\"font-family: Cambria Math;\">s are coming true.</span></p>\\n",
                    options_en: ["<p>No change required</p>\\n", "<p>The worse fea<span style=\"font-family: Cambria Math;\">rs are coming back it seems so.</span></p>\\n", 
                                "<p>It looks like our worst fears are coming true.</p>\\n", "<p>The fears with bad thoughts come back.</p>\\n"],
                    options_hi: ["<p>No change required</p>\\n", "<p>The worse fears are coming back it seems so.</p>\\n",
                                "<p>It looks like our worst fears are coming true.</p>\\n", "<p>The fears with bad thoughts come back.</p>\\n"],
                    solution_en: "<p>19.(c) <span style=\"font-family: Cambria Math;\">It looks like our worst fears are coming true.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) The </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">worse</span></span><span style=\"font-weight: 400;\"> fears are coming back it seems so.(Incorrect Degree)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) The fears with bad thoughts come back.(Incorrect Sentence Structure)</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>19.(c) <span style=\"font-family: Cambria Math;\">It looks like our worst fears are coming true</span><span style=\"font-family: Cambria Math;\">.(Correct)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(b) The </span><span style=\"text-decoration: underline;\"><span style=\"font-weight: 400;\">worse</span></span><span style=\"font-weight: 400;\"><span style=\"text-decoration: underline;\"> fears </span>are coming back it seems so.(&#2327;&#2354;&#2340; Degree)</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(d) The fears with bad thoughts come back.(&#2327;&#2354;&#2340; Sentence Structure)</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">Parts of a sentence are given below in jumbled order. Select the option that arranges </span><span style=\"font-family: Cambria Math;\">the parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A. Written in unadorned Sanskrit prose</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B. One of the world&rsquo;s earliest books</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C. Devoted to statecraft,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">D. The Arthashastra is</span></p>\\n",
                    question_hi: "<p>20. <span style=\"font-family: Cambria Math;\">Parts of a sentence are given below in jumbled order. Select the option that arranges </span><span style=\"font-family: Cambria Math;\">the parts in the correct order to form a meaningful sentence.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A. Written in unadorned Sanskrit prose</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B. One of the world&rsquo;s earliest books</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C. Devoted to statecraft,</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">D. The A</span><span style=\"font-family: Cambria Math;\">rthashastra is</span></p>\\n",
                    options_en: ["<p>D, B, C, A</p>\\n", "<p>C, D, A, B</p>\\n", 
                                "<p>B, C, A, D</p>\\n", "<p>D, A, B, C</p>\\n"],
                    options_hi: ["<p>D, B, C, A</p>\\n", "<p>C, D, A, B</p>\\n",
                                "<p>B, C, A, D</p>\\n", "<p>D, A, B, C</p>\\n"],
                    solution_en: "<p>20.(a)<span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-weight: 400;\">D, B, C, A</span></p>\\r\\n<p><span style=\"font-weight: 400;\">The given sentence starts with Part D as it contains the main subject of the sentence, i.e. Arthashastra. Part D will be followed by Part B as it states that it is one of the earliest books. Further, Part C states that it is devoted to statecraft &amp; Part A states that it is written in Sanskrit. So, C will follow A. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</span></p>\\n",
                    solution_hi: "<p>20.(a) <span style=\"font-weight: 400;\">D, B, C, A</span></p>\\r\\n<p><span style=\"font-weight: 400;\">&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2357;&#2366;&#2325;&#2381;&#2351; </span><span style=\"font-weight: 400;\">Part D </span><span style=\"font-weight: 400;\">&#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2352;&#2350;&#2381;&#2349; &#2361;&#2379;&#2327;&#2366; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2311;&#2360;&#2350;&#2375;&#2306; </span><span style=\"font-weight: 400;\">sentence </span><span style=\"font-weight: 400;\">&#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351; &#2357;&#2367;&#2359;&#2351; &lsquo;</span><span style=\"font-weight: 400;\">Arthashastra&rsquo;</span><span style=\"font-weight: 400;\"> &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2404; </span><span style=\"font-weight: 400;\">Part D </span><span style=\"font-weight: 400;\">&#2325;&#2375; &#2348;&#2366;&#2342; </span><span style=\"font-weight: 400;\">&nbsp;Part B</span><span style=\"font-weight: 400;\"> &#2310;&#2319;&#2327;&#2366; &#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367; &#2311;&#2360;&#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2351;&#2361; &#2360;&#2348;&#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; &#2361;&#2376;&#2404; &#2311;&#2360;&#2325;&#2375; &#2309;&#2354;&#2366;&#2357;&#2366;, </span><span style=\"font-weight: 400;\">Part C</span><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2351;&#2361; &#2358;&#2366;&#2360;&#2344; &#2325;&#2354;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; &#2360;&#2350;&#2352;&#2381;&#2346;&#2367;&#2340; &#2361;&#2376; &#2324;&#2352; </span><span style=\"font-weight: 400;\">Part A</span><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; &#2325;&#2361;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2351;&#2361; &#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340; &#2350;&#2375;&#2306; &#2354;&#2367;&#2326;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319; C &#2325;&#2375; &#2348;&#2366;&#2342; A &#2310;&#2319;&#2327;&#2366;&#2404; Options &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2332;&#2366;&#2344;&#2375; &#2346;&#2352;, Options &lsquo;a&rsquo; &#2350;&#2375;&#2306; &#2360;&#2361;&#2368; sequence &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21.<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 21.</span></p>\\n",
                    question_hi: "<p>21. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number </span><span style=\"font-family: Cambria Math;\">21.</span></p>\\n",
                    options_en: ["<p>chase</p>\\n", "<p>runs</p>\\n", 
                                "<p>stares</p>\\n", "<p>blinks</p>\\n"],
                    options_hi: ["<p>chase</p>\\n", "<p>runs</p>\\n",
                                "<p>stares</p>\\n", "<p>blinks</p>\\n"],
                    solution_en: "<p>21.(b) <span style=\"font-family: Cambria Math;\">Runs</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The given passage states that the woman shouts and runs up to the little girl as soon as she sees her. Hence, &lsquo;runs&rsquo; is the most appropriate answer.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>21.(b) <span style=\"font-family: Cambria Math;\">Runs</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2367;&#2354;&#2381;&#2354;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2337;&#2364;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2326;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2380;&#2337;&#2364;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;runs&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 22.</span></p>\\n",
                    question_hi: "<p>22. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most app</span><span style=\"font-family: Cambria Math;\">ropriate option to fill in blank number 22.</span></p>\\n",
                    options_en: ["<p>ears</p>\\n", "<p>nails</p>\\n", 
                                "<p>face</p>\\n", "<p>skin</p>\\n"],
                    options_hi: ["<p>ears</p>\\n", "<p>nails</p>\\n",
                                "<p>face</p>\\n", "<p>skin</p>\\n"],
                    solution_en: "<p>22.(c) <span style=\"font-family: Cambria Math;\">Face</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">The given passage states that the woman looks at her daughter\'s face. Hence, &lsquo;face&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>22.(c) <span style=\"font-family: Cambria Math;\">Face</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2375;&#2361;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2326;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;face&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23.<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 23.</span></p>\\n",
                    question_hi: "<p>23. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in blank number 23.</span></p>\\n",
                    options_en: ["<p>opts</p>\\n", "<p>discreet</p>\\n", 
                                "<p>greets</p>\\n", "<p>rejects</p>\\n"],
                    options_hi: ["<p>opts</p>\\n", "<p>discreet</p>\\n",
                                "<p>greets</p>\\n", "<p>rejects</p>\\n"],
                    solution_en: "<p>23.(c) <span style=\"font-family: Cambria Math;\">Greets</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Greet&rsquo; means to say hello to somebody or to welcome them. The given passage states that the woman turns a</span><span style=\"font-family: Cambria Math;\">nd says hello to the man. Hence, &lsquo;greets&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>23.(c) <span style=\"font-family: Cambria Math;\">Greets</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Greet&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2350;&#2360;&#2381;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2337;&#2364;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2352;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2350;&#2360;&#2381;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;greets&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.<strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> Select the most appropriate option to fill in blank number 24.</span></p>\\n",
                    question_hi: "<p>24.<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Cloze Test:</span></strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 24.</span></p>\\n",
                    options_en: ["<p>claim</p>\\n", "<p>announcement</p>\\n", 
                                "<p>notification</p>\\n", "<p>assertion</p>\\n"],
                    options_hi: ["<p>claim</p>\\n", "<p>annou<span style=\"font-family: Cambria Math;\">ncement</span></p>\\n",
                                "<p>notification</p>\\n", "<p>assertion</p>\\n"],
                    solution_en: "<p>24.(b) <span style=\"font-family: Cambria Math;\">Announcement</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Announcement&rsquo; means a public or formal declaration of information. The given passage states that the woman couldn\'t make this declaration over the speakers because her daughter was deaf. Hence, &lsquo;announcement&rsquo; is the most appropriate answer.</span></p>\\n",
                    solution_hi: "<p>24.(b) <span style=\"font-family: Cambria Math;\">Announcement</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&lsquo;Announcement&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2370;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2346;&#2330;&#2366;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2379;&#2359;&#2339;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2368;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2379;&#2359;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2361;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">(deaf) </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, &lsquo;announcement&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Se</span><span style=\"font-family: Cambria Math;\">lect the most appropriate option to fill in blank number 25.</span></p>\\n",
                    question_hi: "<p>25. <strong>Cloze Test:</strong></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A couple of minutes later, a woman comes running towards the desk. As soon as she sees </span><span style=\"font-family: Cambria Math;\">the little girl,</span><span style=\"font-family: Cambria Math;\"> she shouts and (21)______ up to her. She lifts the girl into her arms and holds her </span><span style=\"font-family: Cambria Math;\">very tight. Then she separates herself from her daughter and looks at her (22)_______ while </span><span style=\"font-family: Cambria Math;\">she says, &ldquo;I am here, Andrea. Everything is okay. I love you, honey.&rdquo; Then she </span><span style=\"font-family: Cambria Math;\">turns and </span>______ the man. &ldquo;Thank you so much, sir,&rdquo; she says. &ldquo;We got separated in the crowd <span style=\"font-family: Cambria Math;\">earlier. My daughter is deaf, so I could not make an (24)__________ over the speakers for her. </span><span style=\"font-family: Cambria Math;\">I was looking for her everywhere. I was so worried !&rdquo;. Now the m</span><span style=\"font-family: Cambria Math;\">an understands why the little </span><span style=\"font-family: Cambria Math;\">girl did not look at him until he (25)__________ her shoulder.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Select the most appropriate option to fill in blank number 25.</span></p>\\n",
                    options_en: ["<p>felt</p>\\n", "<p>touched</p>\\n", 
                                "<p>contacted</p>\\n", "<p>cracked</p>\\n"],
                    options_hi: ["<p>felt</p>\\n", "<p>touched</p>\\n",
                                "<p>contacted</p>\\n", "<p>cracked</p>\\n"],
                    solution_en: "<p>25.(b)&nbsp;<span style=\"font-weight: 400;\">Touched</span></p>\\r\\n<p><span style=\"font-weight: 400;\">The given passage states that the man couldn\'t understand why the girl didn\'t look at him until he physically touched her shoulder. Hence, &lsquo;touched&rsquo; is the most appropriate answer.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>25.(b) <span style=\"font-family: Cambria Math;\">Touched</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> passage </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2333;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;</span><span style=\"font-family: Cambria Math;\">&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2337;&#2364;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2352;&#2368;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2343;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2352;&#2381;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> &lsquo;touched&rsquo; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>