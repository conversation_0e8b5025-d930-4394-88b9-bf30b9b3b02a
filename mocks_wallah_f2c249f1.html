<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A sum of money placed at compound interest (compounding annually) doubles itself in 10 years. Find the time in which it will amount to 4 times itself.</p>",
                    question_hi: "<p>1. चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) पर रखी गई एक धनराशि 10 वर्षों में स्वयं की दोगुनी हो जाती है। वह समय ज्ञात कीजिए, जिसमें धनराशि स्वयं की 4 गुना हो जाएगी।</p>",
                    options_en: ["<p>20 years</p>", "<p>14 years</p>", 
                                "<p>10 years</p>", "<p>15 years</p>"],
                    options_hi: ["<p>20 वर्ष</p>", "<p>14 वर्ष</p>",
                                "<p>10 वर्ष</p>", "<p>15 वर्ष</p>"],
                    solution_en: "<p>1.(a)<br>Let the sum be P<br>As for a fixed time period, sum gets multiplied by the same factor. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731575025911.png\" alt=\"rId4\" width=\"277\" height=\"49\"><br>Hence, In 20 years, the amount gets 4 times itself.</p>",
                    solution_hi: "<p>1.(a)<br>माना मूलधन P है<br>एक निश्चित समय अवधि के लिए, मूलधन उसी कारक से गुणा हो जाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731575026024.png\" alt=\"rId5\" width=\"282\" height=\"51\"><br>अतः, 20 वर्षों में, राशि 4 गुना हो जाती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Rs. 9000 is lent at the rate of 80 percent per annum on compound interest (compounded half yearly). What will be the compound interest of 12 months ?</p>",
                    question_hi: "<p>2. 9000 रुपए 80 प्रतिशत की वार्षिक दर से चक्रवृद्धि ब्याज (अर्धवार्षिक रूप से संयोजित) पर उधार दिए जाते हैं। 12 महीने का चक्रवृद्धि ब्याज कितना होगा ?</p>",
                    options_en: ["<p>Rs. 9000</p>", "<p>Rs. 8760</p>", 
                                "<p>Rs. 8700</p>", "<p>Rs. 8640</p>"],
                    options_hi: ["<p>9000 रुपए</p>", "<p>8760 रुपए</p>",
                                "<p>8700 रुपए</p>", "<p>8640 रुपए</p>"],
                    solution_en: "<p>2.(d)<br>Rate compounded half yearly,<br>Then, rate = 40% , time = 2 cycle<br>CI for 12 months(or 1 yr) = (40 + 40 +&nbsp;<math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 96%<br><math display=\"inline\"><mo>&#8658;</mo></math> 96% of 9000 = ₹8640</p>",
                    solution_hi: "<p>2.(d)<br>दर अर्धवार्षिक संयोजित,<br>फिर, दर = 40%, समय = 2 चक्र<br>12 महीने (या 1 वर्ष) के लिए चक्रवृद्धि ब्याज = (40 + 40 + <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>&#215;</mo><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 96%<br><math display=\"inline\"><mo>&#8658;</mo></math> 9000 का 96% = ₹8640</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. If interest is being compound annually, then what sum will amount to Rs. 9464 in 2 years at the rate of 30 percent per annum at compound interest ?",
                    question_hi: "3. यदि ब्याज वार्षिक रूप से संयोजित हो रहा है, तो 30 प्रतिशत वार्षिक चक्रवृद्धि ब्याज की दर से कितनी धनराशि 2 वर्ष में 9464 रुपए हो जाएगी ?",
                    options_en: [" Rs. 5600", " Rs. 5400 ", 
                                " Rs. 6800 ", " Rs. 5750"],
                    options_hi: [" 5600 रुपए", " 5400 रुपए",
                                " 6800 रुपए", " 5750 रुपए"],
                    solution_en: "3.(a)<br />Let sum be 100%<br />Effective CI for 2 yrs at 30% rate = (30 + 30 + <math display=\"inline\"><mfrac><mrow><mn>30</mn><mo>×</mo><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 69%<br />169% ---------------- ₹9464<br />100% ----------------  <math display=\"inline\"><mfrac><mrow><mn>9464</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math> × 100 = ₹5600",
                    solution_hi: "3.(a)<br />माना धनराशि 100% है<br />30% दर पर 2 वर्षों के लिए प्रभावी चक्रवृद्धि ब्याज = (30 + 30 + <math display=\"inline\"><mfrac><mrow><mn>30</mn><mo>×</mo><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 69%<br />169% ---------------- ₹9464<br />100% ----------------  <math display=\"inline\"><mfrac><mrow><mn>9464</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math> × 100 = ₹5600",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Simple interest and compound interest on a principal at a certain rate of interest for 2 years is Rs. 12800 and Rs. 14400 respectively. What is the principal ?</p>",
                    question_hi: "<p>4. 2 वर्षों के लिए एक निश्चित ब्याज दर पर, मूलधन पर साधारण ब्याज और चक्रवृद्धि ब्याज क्रमशः 12800 रुपए और 14400 रुपए है। मूलधन कितना है ?</p>",
                    options_en: ["<p>Rs. 25600</p>", "<p>Rs. 27800</p>", 
                                "<p>Rs. 24400</p>", "<p>Rs. 29900</p>"],
                    options_hi: ["<p>25600 रुपए</p>", "<p>27800 रुपए</p>",
                                "<p>24400 रुपए</p>", "<p>29900 रुपए</p>"],
                    solution_en: "<p>4.(a)<br>ATQ, We have following diagram ;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731575026131.png\" alt=\"rId6\" width=\"176\" height=\"152\"><br>(CI - SI) for 2 yrs = 14400 - 12800 = ₹1600<br>SI for 1 yr = <math display=\"inline\"><mfrac><mrow><mn>12800</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6400<br>rate% = <math display=\"inline\"><mfrac><mrow><mn>1600</mn></mrow><mrow><mn>6400</mn></mrow></mfrac></math> &times; 100 = 25%<br>Principal amount = 6400 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = ₹25600</p>",
                    solution_hi: "<p>4.(a)<br>प्रश्नानुसार , हमारे पास निम्नलिखित चित्र है;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731575026233.png\" alt=\"rId7\" width=\"190\" height=\"157\"><br>(सा<math display=\"inline\"><mo>&#8728;</mo></math> ब्याज - चक्र∘ ब्याज) 2 साल के लिए = 14400 - 12800 = ₹1600<br>1 वर्ष के लिए सा ब्याज = <math display=\"inline\"><mfrac><mrow><mn>12800</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6400<br>दर% = <math display=\"inline\"><mfrac><mrow><mn>1600</mn></mrow><mrow><mn>6400</mn></mrow></mfrac></math> &times; 100 = 25%<br>मूल राशि = 6400 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = ₹25600</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Simple interest and compound interest (compounding annually) on a principal at a certain rate of interest for 2 years is Rs. 12000 and Rs. 14400 respectively. What is the principal ?</p>",
                    question_hi: "<p>5. निश्चित ब्याज दर पर 2 वर्षों के लिए एक मूलधन पर साधारण ब्याज और चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) क्रमशः 12000 रुपए और 14400 रुपए है। मूलधन कितना है ?</p>",
                    options_en: ["<p>Rs. 15000</p>", "<p>Rs. 22000</p>", 
                                "<p>Rs. 17000</p>", "<p>Rs. 18000</p>"],
                    options_hi: ["<p>15000 रुपए</p>", "<p>22000 रुपए</p>",
                                "<p>17000 रुपए</p>", "<p>18000 रुपए</p>"],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731575026371.png\" alt=\"rId8\" width=\"226\" height=\"163\"><br>(CI - SI) for 2 yrs = 14400 - 12000 = ₹2400<br>SI for 1 yr = <math display=\"inline\"><mfrac><mrow><mn>12000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹6000<br>rate% = <math display=\"inline\"><mfrac><mrow><mn>2400</mn></mrow><mrow><mn>6000</mn></mrow></mfrac></math> &times; 100 = 40%<br>Principal amount = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = ₹15,000</p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731575026518.png\" alt=\"rId9\" width=\"238\" height=\"175\"><br>(CI - SI) 2 साल के लिए = 14400 - 12000 = ₹2400<br>1 वर्ष के लिए साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>12000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹6000<br>दर% = <math display=\"inline\"><mfrac><mrow><mn>2400</mn></mrow><mrow><mn>6000</mn></mrow></mfrac></math> &times; 100 = 40%<br>मूल राशि = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = ₹15,000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The compound interest (compounded half-yearly) received on Rs 22000 for 2 years is Rs 10210.2. What is the annual rate of interest ?</p>",
                    question_hi: "<p>6. 2 वर्ष के लिए 22000 रुपए पर प्राप्त चक्रवृद्धि ब्याज (अर्धवार्षिक रूप से संयोजित) 10210.2 रुपए है। वार्षिक ब्याज दर कितनी है ?</p>",
                    options_en: ["<p>25 Percent</p>", "<p>20 Percent</p>", 
                                "<p>40 Percent</p>", "<p>30 Percent</p>"],
                    options_hi: ["<p>25 प्रतिशत</p>", "<p>20 प्रतिशत</p>",
                                "<p>40 प्रतिशत</p>", "<p>30 प्रतिशत</p>"],
                    solution_en: "<p>6.(b) According to question,<br>when principal is compounded half yearly,<br>Amount = P<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>)</mo></mrow><mrow><mn>2</mn><mi>T</mi></mrow></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> (22000 + 10210.2) = 22000 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>)</mo></mrow><mn>4</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32210</mn><mo>.</mo><mn>2</mn></mrow><mn>22000</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>)</mo></mrow><mn>4</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14641</mn><mrow><mn>10000</mn><mo>&#160;</mo></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>)</mo></mrow><mn>4</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac><mo>=</mo><mn>1</mn><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>&#8658;</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &rArr; R = 20%</p>",
                    solution_hi: "<p>6.(b) प्रश्न के अनुसार,<br>जब मूलधन अर्धवार्षिक रूप से संयोजित किया जाता है,<br>मिश्रधन = P<math display=\"inline\"><msup><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mfrac><mrow><mi>R</mi></mrow><mrow><mn>200</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn><mi>T</mi></mrow></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> (22000 + 10210.2) = 22000 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>)</mo></mrow><mn>4</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32210</mn><mo>.</mo><mn>2</mn></mrow><mn>22000</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>)</mo></mrow><mn>4</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14641</mn><mrow><mn>10000</mn><mo>&#160;</mo></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>1</mn><mi>&#160;</mi><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>)</mo></mrow><mn>4</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac><mo>=</mo><mn>1</mn><mo>+</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>&#8658;</mo><mfrac><mi>R</mi><mn>200</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>10</mn></mfrac></math> &rArr; R = 20%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The compound interest (compounding half yearly) received on Rs. 1000 for 2 years is Rs. 464.1 What is the rate of interest per annum ?</p>",
                    question_hi: "<p>7. 1000 रुपए पर 2 वर्ष के लिए प्राप्त चक्रवृद्धि ब्याज (अर्धवार्षिक रूप से संयोजित) 464.1 रुपए है। वार्षिक ब्याज दर कितनी है ?</p>",
                    options_en: ["<p>20 percent</p>", "<p>30 percent</p>", 
                                "<p>35 percent</p>", "<p>25 percent</p>"],
                    options_hi: ["<p>20 प्रतिशत</p>", "<p>30 प्रतिशत</p>",
                                "<p>35 प्रतिशत</p>", "<p>25 प्रतिशत</p>"],
                    solution_en: "<p>7.(a)<br>Amount = 1000 + 464.1 = ₹1464.1<br>Amount = P<math display=\"inline\"><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn><mi>n</mi></mrow></msup></math><br>1464.1 = 1000<math display=\"inline\"><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math><br><math display=\"inline\"><mfrac><mrow><mn>14641</mn></mrow><mrow><mn>10</mn><mo>,</mo><mn>000</mn></mrow></mfrac></math>= <math display=\"inline\"><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math><br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>11</mn></mrow><mrow><mn>10</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math> = <math display=\"inline\"><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math><br><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math><br>r<sub>Half year</sub> = 10%<br>Rate for 1 yr = 2 &times; 10% = 20%</p>",
                    solution_hi: "<p>7.(a)<br>राशि = 1000 + 464.1 = ₹1464.1<br>राशि = P<math display=\"inline\"><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn><mi>n</mi></mrow></msup></math><br>1464.1 = 1000<math display=\"inline\"><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math><br><math display=\"inline\"><mfrac><mrow><mn>14641</mn></mrow><mrow><mn>10</mn><mo>,</mo><mn>000</mn></mrow></mfrac></math>= <math display=\"inline\"><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math><br><math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>11</mn></mrow><mrow><mn>10</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math> = <math display=\"inline\"><mo>(</mo><mn>1</mn><mo>+</mo><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></math><br><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>1</mn><mo>+</mo><mfrac><mi>r</mi><mn>100</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>r</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math><br>r<sub>अर्धवार्षिक</sub> = 10%<br>1 वर्ष के लिए दर = 2 &times; 10% = 20%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. The difference between the compound interest (compounding half yearly) for 1 year and the simple interest for 1 year on a certain sum lent out at the annual rate of 30 percent is Rs. 337.5. What is the sum ?</p>",
                    question_hi: "<p>8. 30 प्रतिशत की वार्षिक दर से ऋण पर दी गई एक निश्चित धनराशि पर 1 वर्ष के लिए चक्रवृद्धि ब्याज (अर्धवार्षिक रूप से संयोजित) और 1 वर्ष के लिए साधारण ब्याज के बीच का अंतर 337.5 रुपए है। वह धनराशि कितनी है ?</p>",
                    options_en: ["<p>Rs. 15000</p>", "<p>Rs. 18500</p>", 
                                "<p>Rs. 17000</p>", "<p>Rs. 17700</p>"],
                    options_hi: ["<p>15000 रुपए</p>", "<p>18500 रुपए</p>",
                                "<p>17000 रुपए</p>", "<p>17700 रुपए</p>"],
                    solution_en: "<p>8.(a)<br>Rate for <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>year = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>2</mn></mfrac></math> = 15%<br>CI for 1 year (2 cycle) at 15% = (15 + 15 + <math display=\"inline\"><mfrac><mrow><mn>1</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 30 + 2.25 = 32.25%<br>SI for 1 year = 30%<br>(CI - SI) for 1 year = 32.25% - 30% = 2.25% of P<br>337.5 = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>P<br>P = <math display=\"inline\"><mfrac><mrow><mn>337</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = ₹15,000</p>",
                    solution_hi: "<p>8.(a)<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष के लिए दर = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>2</mn></mfrac></math> = 15%<br>15% पर 1 वर्ष (2 चक्र) के लिए चक्रवृद्धि ब्याज = (15 + 15 + <math display=\"inline\"><mfrac><mrow><mn>1</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 30 + 2.25 = 32.25%<br>1 वर्ष के लिए साधारण ब्याज = 30%<br>(चक्रवृद्धि ब्याज - साधारण ब्याज) 1 वर्ष के लिए = 32.25% - 30% = मूलधन का 2.25% <br>337.5 = मूलधन &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>.</mo><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>मूलधन = <math display=\"inline\"><mfrac><mrow><mn>337</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = ₹15,000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "9. The simple interest on a certain sum of money at rate of interest 6% per annum for 2 years is Rs. 960. What is the compound interest (compounding annually) on the same sum for the same period and at the same rate of interest ?",
                    question_hi: "9. एक निश्चित धनराशि पर 6% वार्षिक की ब्याज दर पर, 2 वर्षों के लिए साधारण ब्याज 960 रुपए है। समान अवधि और समान ब्याज दर पर समान धनराशि पर चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) कितना होगा ? ",
                    options_en: [" Rs. 985.4 ", " Rs. 988.8 ", 
                                " Rs.1122.2 ", " Rs.1025.1"],
                    options_hi: [" 985.4 रुपए ", " 988.8 रुपए ",
                                " 1122.2 रुपए ", " 1025.1 रुपए"],
                    solution_en: "9.(b)<br />Let principal be 100%<br />6%×2 = 12% -------------- ₹960<br />100% -------------- <math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> × 100 = ₹8,000<br />Successive rate for 2 years = 6 + 6 + <math display=\"inline\"><mfrac><mrow><mn>6</mn><mo>×</mo><mn>6</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 12.36%<br />CI on the same sum for the same period at the same rate = 8000 × 12.36% =  ₹ 988.8.",
                    solution_hi: "9.(b)<br />मान लीजिए मूलधन 100% है<br />6%×2 = 12% -------------- ₹960<br />100% -------------- <math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>×100 = ₹8,000<br />2 वर्षों के लिए क्रमिक दर = 6 + 6 + <math display=\"inline\"><mfrac><mrow><mn>6</mn><mo>×</mo><mn>6</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 12.36%<br />समान राशि पर समान अवधि के लिए समान दर पर चक्रवृद्धि ब्याज = 8000×<math display=\"inline\"><mo>(</mo><mfrac><mrow><mn>53</mn></mrow><mrow><mn>50</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - 8,000 = 8000 × 12.36% = ₹ 988.8.",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. On a sum, simple interest at the annual rate of 10 percent for 8 years is Rs. 8000. What will be the compound interest on the same sum at the annual rate of 10 percent (compounding annually) for 3 years ?</p>",
                    question_hi: "<p>10. किसी धनराशि पर 8 वर्षों के लिए 10 प्रतिशत वार्षिक की दर से साधारण ब्याज 8000 रुपए है। उसी धनराशि पर 3 वर्षों के लिए 10 प्रतिशत वार्षिक की दर (वार्षिक रूप से संयोजित) से चक्रवृद्धि ब्याज कितना होगा ?</p>",
                    options_en: ["<p>Rs. 3450</p>", "<p>Rs. 3750</p>", 
                                "<p>Rs. 3220</p>", "<p>Rs. 3310</p>"],
                    options_hi: ["<p>3450 रुपए</p>", "<p>3750 रुपए</p>",
                                "<p>3220 रुपए</p>", "<p>3310 रुपए</p>"],
                    solution_en: "<p>10.(d)<br>Let principal be 100% <br>8 &times; 10% = 80% ---------------- ₹8000<br>100% ---------------- <math display=\"inline\"><mfrac><mrow><mn>8000</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 100 = ₹10,000<br>Time = 3 yrs, rate = 10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>Principal&nbsp; &nbsp; Amount<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11<br>___________________<br>&nbsp; &nbsp;1000&nbsp; &nbsp; :&nbsp; &nbsp; 1331<br>CI = 1331 - 1000 = 331 unit <br>Now, 1000 unit ------------- ₹10,000<br>Then, 331 unit ------------- <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 331 = ₹3310</p>",
                    solution_hi: "<p>10.(d)<br>मान लीजिए मूलधन 100% है<br>8 &times; 10% = 80% ---------------- ₹8000<br>100% ---------------- <math display=\"inline\"><mfrac><mrow><mn>8000</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 100 = ₹10,000<br>समय = 3 वर्ष, दर = 10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math><br>मूलधन राशि <br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11<br>___________________<br>&nbsp; &nbsp;1000&nbsp; &nbsp; :&nbsp; &nbsp; 1331&nbsp;<br>चक्रवृद्धि ब्याज = 1331 - 1000 = 331 इकाई<br>अब, 1000 इकाई ------------- ₹10,000<br>अतः , 331 इकाई ------------- <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 331 = ₹3310</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A sum of Rs. 15,000 invested at compound interest (compounding annually) at the rate of 12% per annum for 2 years. Find the compound interest.</p>",
                    question_hi: "<p>11. 15,000 रुपए की धनराशि को 2 वर्षों के लिए 12% की वार्षिक दर से चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) पर निवेश किया जाता है। उस धनराशि पर चक्रवृद्धि ब्याज ज्ञात कीजिए।</p>",
                    options_en: ["<p>Rs. 4024</p>", "<p>Rs. 3668</p>", 
                                "<p>Rs. 4124</p>", "<p>Rs. 3816</p>"],
                    options_hi: ["<p>4024 रुपए</p>", "<p>3668 रुपए</p>",
                                "<p>4124 रुपए</p>", "<p>3816 रुपए</p>"],
                    solution_en: "<p>11.(d)<br>Rate = 12% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, Time = 2 yrs<br>&nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; :&nbsp; &nbsp; 28<br>&nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; :&nbsp; &nbsp; 28 <br>&nbsp; &nbsp;_____________<br>&nbsp; &nbsp; &nbsp; &nbsp;625&nbsp; :&nbsp; &nbsp;784<br>CI = 784 - 625 = 159 unit<br>625 unit = ₹15,000<br>159 unit = <math display=\"inline\"><mfrac><mrow><mn>15</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> &times; 159 = ₹3816</p>",
                    solution_hi: "<p>11.(d)<br>दर = 12% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, समय = 2 वर्ष<br>&nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; :&nbsp; &nbsp; 28<br>&nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; :&nbsp; &nbsp; 28 <br>&nbsp; &nbsp;_____________<br>&nbsp; &nbsp; &nbsp; &nbsp;625&nbsp; :&nbsp; &nbsp;784<br>चक्रबृद्धि ब्याज = 784 - 625 = 159 इकाई <br>625 इकाई = ₹15,000<br>159 इकाई = <math display=\"inline\"><mfrac><mrow><mn>15</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> &times; 159 = ₹3816</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "12.  Compound interest (compounding annually) on a sum for 2 years is Rs. 967.5. If rate of interest is 15 percent per annum, then what is the sum?",
                    question_hi: "12.  किसी धनराशि पर 2 वर्षों के लिए चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) 967.5 रुपए है। यदि ब्याज की दर 15 प्रतिशत वार्षिक है, तो वह धनराशि कितनी है?",
                    options_en: ["  Rs. 3000", "  Rs. 2400", 
                                "  Rs. 2800", " Rs. 2700"],
                    options_hi: [" 3000 रुपए", " 2400 रुपए",
                                " 2800 रुपए", " 2700 रुपए"],
                    solution_en: "12.(a)<br />Let the sum = P<br />Effective CI = (15 +15 + <math display=\"inline\"><mfrac><mrow><mn>15</mn><mo>×</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = (30 + 2.25)% = 32.25%<br />32.25% of P = 967.5<br />P = <math display=\"inline\"><mfrac><mrow><mn>967</mn><mo>.</mo><mn>5</mn><mo>×</mo><mn>100</mn></mrow><mrow><mn>32</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = ₹3000",
                    solution_hi: "12.(a)<br />माना , योग  = P<br />प्रभावी चक्रवृद्धि ब्याज <br />= (15+15+<math display=\"inline\"><mfrac><mrow><mn>15</mn><mo>×</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = (30+2.25)% = 32.25%<br />P का 32.25% = 967.5<br />P = <math display=\"inline\"><mfrac><mrow><mn>967</mn><mo>.</mo><mn>5</mn><mo>×</mo><mn>100</mn></mrow><mrow><mn>32</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = ₹3000",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A person borrowed Rs. 15000 on compound interest at the rate of 30 percent per annum. If the interest is compounded half yearly, then what will be the amount to be paid after 1 year?</p>",
                    question_hi: "<p>13. एक व्यक्ति ने 30 प्रतिशत वार्षिक चक्रवृद्धि ब्याज की दर पर 15000 रुपए ऋण पर लिए। यदि ब्याज अर्धवार्षिक रूप से संयोजित किया जाता है, तो 1 वर्ष के बाद भुगतान की जाने वाली धनराशि कितनी होगी ?</p>",
                    options_en: ["<p>Rs.20428.5</p>", "<p>Rs.19837.5</p>", 
                                "<p>Rs.17482.5</p>", "<p>Rs.18282.5</p>"],
                    options_hi: ["<p>20428.5 रुपए</p>", "<p>19837.5 रुपए</p>",
                                "<p>17482.5 रुपए</p>", "<p>18282.5 रुपए</p>"],
                    solution_en: "<p>13.(b)<br>Rate for half yrs = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 15%<br>Effective CI for 1 yr = (15 + 15 + <math display=\"inline\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32.25%<br>So, the amount to be paid after 1 yr = 132.25% of 15000 = ₹19,837.5</p>",
                    solution_hi: "<p>13.(b)<br>आधे साल के लिए दर = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 15%<br>1 वर्ष के लिए प्रभावी CI = (15 + 15 + <math display=\"inline\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 32.25%<br>तो, 1 वर्ष के बाद भुगतान की जाने वाली राशि = 132.25% of 15000 = ₹19,837.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. What will be the compound interest (compounded annually) on a principal sum of Rs. 72000 for 3 years at the rate of 16 percent per annum ?</p>",
                    question_hi: "<p>14. 72000 रुपए के मूलधन पर 3 वर्ष के लिए 16 प्रतिशत वार्षिक की दर से चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) कितना होगा ?</p>",
                    options_en: ["<p>Rs. 42248.64</p>", "<p>Rs. 40384.51</p>", 
                                "<p>Rs. 38245.22</p>", "<p>Rs. 36574.31</p>"],
                    options_hi: ["<p>42248.64 रुपए</p>", "<p>40384.51 रुपए</p>",
                                "<p>38245.22 रुपए</p>", "<p>36574.31 रुपए</p>"],
                    solution_en: "<p>14.(b)<br>Rate = 16% = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> , time = 3 yrs<br>&nbsp; &nbsp;Principal&nbsp; &nbsp; &nbsp; Amount<br>&nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;29<br>&nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;29<br>&nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;29<br>&nbsp; &nbsp;_______________<br>&nbsp; &nbsp; 15625&nbsp; &nbsp;:&nbsp; &nbsp; 24389<br>CI = 24389 - 15625 = 8764 unit<br><math display=\"inline\"><mo>&#8658;</mo></math> 15625 unit = ₹72,000<br>Then, 8764 unit = <math display=\"inline\"><mfrac><mrow><mn>72000</mn></mrow><mrow><mn>15625</mn></mrow></mfrac></math> &times; 8764 = ₹40,384.512 or ₹40,384.51</p>",
                    solution_hi: "<p>14.(b)<br>दर = 16% = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> , समय = 3 वर्ष<br>&nbsp; &nbsp;मूलधन&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; मिश्रधन<br>&nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;29<br>&nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;29<br>&nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;29<br>&nbsp; &nbsp;_______________<br>&nbsp; &nbsp; 15625&nbsp; &nbsp;:&nbsp; &nbsp; 24389<br>चक्रवृद्धि ब्याज = 24389 - 15625 = 8764 इकाई<br><math display=\"inline\"><mo>&#8658;</mo></math> 15625 इकाई = 72,000 रुपए <br>तो, 8764 इकाई = <math display=\"inline\"><mfrac><mrow><mn>72000</mn></mrow><mrow><mn>15625</mn></mrow></mfrac></math> &times; 8764 = 40,384.512 या 40,384.51 रुपए</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. A principal of Rs. 30000 is borrowed at compound interest (compounding annually) at the rate of 7 percent per annum. What will be the amount after 2 years ?</p>",
                    question_hi: "<p>15. 30000 रुपए का मूलधन 7 प्रतिशत वार्षिक की दर से चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) पर उधार लिया जाता है। 2 वर्ष के बाद धनराशि कितनी हो जाएगी ?</p>",
                    options_en: ["<p>Rs. 31247</p>", "<p>Rs. 35687</p>", 
                                "<p>Rs. 33248</p>", "<p>Rs. 34347</p>"],
                    options_hi: ["<p>31247 रुपए</p>", "<p>35687 रुपए</p>",
                                "<p>33248 रुपए</p>", "<p>34347 रुपए</p>"],
                    solution_en: "<p>15.(d)Amount after 2 yrs = 30,000 &times; <math display=\"inline\"><mfrac><mrow><mn>107</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>107</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹34,347</p>",
                    solution_hi: "<p>15.(d)<br>2 साल बाद राशि = 30,000 &times; <math display=\"inline\"><mfrac><mrow><mn>107</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>107</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹34,347</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>