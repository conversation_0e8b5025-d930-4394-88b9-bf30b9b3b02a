<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>3 &mdash; 20 &mdash; 7<br>5 &mdash; 26 &mdash; 8<br><strong>NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>1. उस समुच्चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार से संबंधित हैं जिस प्रकार निम्नलिखित समुच्चय की संख्याएँ संबंधित हैं।<br>3 &mdash; 20 &mdash; 7<br>5 &mdash; 26 &mdash; 8<br><strong>नोट</strong> : संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 को लें - 13 पर संक्रियाएं जैसे 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>18 &mdash; 70 &mdash; 6</p>", "<p>5 &mdash; 20 &mdash; 8</p>", 
                                "<p>8 &mdash; 28 &mdash; 6</p>", "<p>17 &mdash; 20 &mdash; 13</p>"],
                    options_hi: ["<p>18 &mdash; 70 &mdash; 6</p>", "<p>5 &mdash; 20 &mdash; 8</p>",
                                "<p>8 &mdash; 28 &mdash; 6</p>", "<p>17 &mdash; 20 &mdash; 13</p>"],
                    solution_en: "<p>1.(c) <strong>Logic:</strong> (1st no. + 3rd no.) &times; 2 = 2nd no.<br>3 &mdash; 20 &mdash; 7 :- (3 + 7) &times; 2 = 20 <br>5 &mdash; 26 &mdash; 8 :- (5 + 8) &times; 2 = 26<br>Similarly<br>8 &mdash; 28 &mdash; 6 :- (8 + 6) &times; 2 = 28</p>",
                    solution_hi: "<p>1.(c) <strong>तर्क:</strong> (पहली संख्या + तीसरी संख्या) &times; 2 = दूसरी संख्या <br>3 &mdash; 20 &mdash; 7 :- (3 + 7) &times; 2 = 20 <br>5 &mdash; 26 &mdash; 8 :- (5 + 8) &times; 2 = 26<br>इसी प्रकार <br>8 &mdash; 28 &mdash; 6 :- (8 + 6) &times; 2 = 28</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, &lsquo;she is girl&rsquo; is coded as &lsquo;7 3 9&rsquo;; &lsquo;he is boy&rsquo; is coded as &lsquo;9 6 5&rsquo; and &lsquo;girl is tall&rsquo; is coded as &lsquo;9 3 8&rsquo;. How is &lsquo;she&rsquo; coded in that language?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में \'she is girl\' को \'7 3 9\' के रूप में कूटबद्ध किया गया है; \'he is boy\' को \'9 6 5\' के रूप में कूटबद्ध किया गया है और \'girl is tall\' को \'9 3 8\' के रूप में कूटबद्ध किया गया है। उसी भाषा में \'she\' को किस प्रकार कूटबद्ध किया गया है?</p>",
                    options_en: ["<p>7</p>", "<p>3</p>", 
                                "<p>8</p>", "<p>9</p>"],
                    options_hi: ["<p>7</p>", "<p>3</p>",
                                "<p>8</p>", "<p>9</p>"],
                    solution_en: "<p>2.(a) &lsquo;she is girl&rsquo;&nbsp; &rarr; &lsquo;7 3 9&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;girl is tall&rsquo; &rarr; &lsquo;9 3 8&rsquo; &hellip;&hellip;&hellip;(ii)<br>in equation i and ii , (girl is) is common.<br>So the code of &lsquo;she&rsquo; is 7.</p>",
                    solution_hi: "<p>2.(a) &lsquo;she is girl&rsquo; &rarr; &lsquo;7 3 9&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;girl is tall&rsquo; &rarr; &lsquo;9 3 8&rsquo; &hellip;&hellip;&hellip;(ii)<br>समीकरण I और II में , (girl is) उभयनिष्ठ है।<br>अतः \'she\' का कोड 7 है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the number from among the given options that can replace the question mark (?) in the following series.<br>300, 152, 80, 48, 40, 52, ?</p>",
                    question_hi: "<p>3. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>300, 152, 80, 48, 40, 52, ?</p>",
                    options_en: ["<p>60</p>", "<p>80</p>", 
                                "<p>90</p>", "<p>100</p>"],
                    options_hi: ["<p>60</p>", "<p>80</p>",
                                "<p>90</p>", "<p>100</p>"],
                    solution_en: "<p>3.(c)<br><strong id=\"docs-internal-guid-c0825f3e-7fff-d1c7-e313-82278f4346ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfNVFxsHygywrNmFyKTRaLPS5mbiOGQo7MvkaSxuw_nCXNT9IZpYHAH32xVrmBJQB7P6kb1HrVVxckRmoXQIg5G1yAvJnUnqDUdU44cjTG_O69HPrQOIUTzBjPfWkZOf9_dSL1zBeQvfA4ncRNBC8yVu2ao?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"302\" height=\"44\"></strong></p>",
                    solution_hi: "<p>3.(c)<br><strong id=\"docs-internal-guid-c0825f3e-7fff-d1c7-e313-82278f4346ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfNVFxsHygywrNmFyKTRaLPS5mbiOGQo7MvkaSxuw_nCXNT9IZpYHAH32xVrmBJQB7P6kb1HrVVxckRmoXQIg5G1yAvJnUnqDUdU44cjTG_O69HPrQOIUTzBjPfWkZOf9_dSL1zBeQvfA4ncRNBC8yVu2ao?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"302\" height=\"44\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct mirror image of the given figure when the mirror is placed at MN as shown.<br><strong id=\"docs-internal-guid-2ceb10a7-7fff-2260-acbb-1336d5a0a748\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcMG2c_PNIf_h_pu9GZZYtmv2sYshIsWnLv9P6RoEC2bxJ_8JeaZAdzIJycPc8LwLkET8QWlpDBUySDPvIFw1ZeavfuFwR_Wsu40WkBVeVgyVT3hPOA1QaeDQuw2ON4bNuvVyLQU5g4pNtC3btB13aVYzM?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"99\" height=\"95\"></strong></p>",
                    question_hi: "<p>4. जब दर्पण को दर्शाए गए अनुसार MN पर रखा जाता है, तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन करें।<br><strong id=\"docs-internal-guid-4dabe378-7fff-ab67-77d6-b5dfc1834223\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcMG2c_PNIf_h_pu9GZZYtmv2sYshIsWnLv9P6RoEC2bxJ_8JeaZAdzIJycPc8LwLkET8QWlpDBUySDPvIFw1ZeavfuFwR_Wsu40WkBVeVgyVT3hPOA1QaeDQuw2ON4bNuvVyLQU5g4pNtC3btB13aVYzM?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"99\" height=\"95\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-c981db4f-7fff-3854-8edf-44a4d20c149a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd8r1dIE2lORxNnbJPPtfVBmjiea1iCel7dkdsfHZnCO1qqc-08SS_R_sKdkKzCkirrQ5Z1hqPmRat_WT_h9Hcbu3EG9Msq3W3qh5uT9-RGgCGuukyLdIcwY6CmshA7tsvp1mk_ne9Lb8vnZWnxuPxlpRqJ?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"104\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-b2a838f2-7fff-4da9-f7fd-a95c1c719116\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe70WwGBzApkixxIZP0m0lm2hYquPq1I6DXdjNC82kb014Hz0bPNH-Q7FiyGamxUOg97PyMBeZSc1Z5-lwM3pPQzGDC1gyy0GOu-KP8qpuaLFzz_9hTIHU8ZujvylwUI9pRzpn6bQXIbPiO7QeIGuiwccQ?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"100\" height=\"20\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-491471ce-7fff-6997-658c-6cb37f3ef24c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeEhXae5um_ZlMllXCigi-RjRZQFG1aqcPp5QxhpZHiUg6h4UUD2y_blbazAOvP0n8jAHK4W7bBOJooprqxGXjLHiwXeyUc3AG2yPzES7CA7J-uyXJVrfCcM-RvdjFvisw9plrDc1OzunTz5NVpdoX9atc?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"100\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-64460b40-7fff-8ee6-9d72-888299e5a34c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPjADwMK2-ob_-w35BZx6nH0DLSz23q1LLIMnvT63Le5kWZrUgI0dXIWw8S7xN_49lB-VFh4GJa8w_yshm9ZcX2R0CpqSkJUDMunDT9zimh5Hf3dOxI1uC82Kuw_Xnxy6ipPMPh10bELNQGE1ssIGvAkXj?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"100\" height=\"20\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-ae8c25b2-7fff-c372-56e7-635ad7d4bc26\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd8r1dIE2lORxNnbJPPtfVBmjiea1iCel7dkdsfHZnCO1qqc-08SS_R_sKdkKzCkirrQ5Z1hqPmRat_WT_h9Hcbu3EG9Msq3W3qh5uT9-RGgCGuukyLdIcwY6CmshA7tsvp1mk_ne9Lb8vnZWnxuPxlpRqJ?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"104\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-d4ec4b5d-7fff-ddf4-daef-ae99af643c6a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe70WwGBzApkixxIZP0m0lm2hYquPq1I6DXdjNC82kb014Hz0bPNH-Q7FiyGamxUOg97PyMBeZSc1Z5-lwM3pPQzGDC1gyy0GOu-KP8qpuaLFzz_9hTIHU8ZujvylwUI9pRzpn6bQXIbPiO7QeIGuiwccQ?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"100\" height=\"20\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-ee6d4c94-7fff-f59f-a66d-41f6c6bc3f9b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeEhXae5um_ZlMllXCigi-RjRZQFG1aqcPp5QxhpZHiUg6h4UUD2y_blbazAOvP0n8jAHK4W7bBOJooprqxGXjLHiwXeyUc3AG2yPzES7CA7J-uyXJVrfCcM-RvdjFvisw9plrDc1OzunTz5NVpdoX9atc?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"100\" height=\"20\"></strong></p>", "<p><strong id=\"docs-internal-guid-796ba350-7fff-0f46-45a3-3a556e68cbde\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPjADwMK2-ob_-w35BZx6nH0DLSz23q1LLIMnvT63Le5kWZrUgI0dXIWw8S7xN_49lB-VFh4GJa8w_yshm9ZcX2R0CpqSkJUDMunDT9zimh5Hf3dOxI1uC82Kuw_Xnxy6ipPMPh10bELNQGE1ssIGvAkXj?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"100\" height=\"20\"></strong></p>"],
                    solution_en: "<p>4.(d)<br><strong id=\"docs-internal-guid-796ba350-7fff-0f46-45a3-3a556e68cbde\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPjADwMK2-ob_-w35BZx6nH0DLSz23q1LLIMnvT63Le5kWZrUgI0dXIWw8S7xN_49lB-VFh4GJa8w_yshm9ZcX2R0CpqSkJUDMunDT9zimh5Hf3dOxI1uC82Kuw_Xnxy6ipPMPh10bELNQGE1ssIGvAkXj?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"100\" height=\"20\"></strong></p>",
                    solution_hi: "<p>4.(d)<br><strong id=\"docs-internal-guid-796ba350-7fff-0f46-45a3-3a556e68cbde\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPjADwMK2-ob_-w35BZx6nH0DLSz23q1LLIMnvT63Le5kWZrUgI0dXIWw8S7xN_49lB-VFh4GJa8w_yshm9ZcX2R0CpqSkJUDMunDT9zimh5Hf3dOxI1uC82Kuw_Xnxy6ipPMPh10bELNQGE1ssIGvAkXj?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"100\" height=\"20\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Six letters A, B, C, D, E and F are written on different faces of a dice. Three positions of this dice are shown in the figure. Find the letter on the face opposite to A.<br><strong id=\"docs-internal-guid-adcc8cd1-7fff-7ba2-c6cb-a5e410f42cdf\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdIBjriNDQuWAGVttphVA3s18gZUAWVzvU7A3vAOPFHuV_l2xuPAYaHAyKkzRfQo1wkdnD08bzxoBi3tnTP9adtdQM5eHBARajbHFqRh2OoONJdFKRx40DiFos6d36-TFDqns9iMq1iixjsGMdFXuVfK_Ae?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"228\" height=\"88\"></strong></p>",
                    question_hi: "<p>5. एक पासे के विभिन्न फलकों पर छ: अक्षर A, B, C, D, E और F लिखे गए हैं। नीचे चित्र में इस पासे की तीन स्थितियाँ दिखाई गई है। अक्षर A के विपरीत फलक पर कौन-सा अक्षर है?<br><strong id=\"docs-internal-guid-adcc8cd1-7fff-7ba2-c6cb-a5e410f42cdf\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdIBjriNDQuWAGVttphVA3s18gZUAWVzvU7A3vAOPFHuV_l2xuPAYaHAyKkzRfQo1wkdnD08bzxoBi3tnTP9adtdQM5eHBARajbHFqRh2OoONJdFKRx40DiFos6d36-TFDqns9iMq1iixjsGMdFXuVfK_Ae?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"228\" height=\"88\"></strong></p>",
                    options_en: ["<p>C</p>", "<p>D</p>", 
                                "<p>E</p>", "<p>F</p>"],
                    options_hi: ["<p>C</p>", "<p>D</p>",
                                "<p>E</p>", "<p>F</p>"],
                    solution_en: "<p>5.(c) in the dice i and ii , letter (B , C) is common<br>So, the opposite of &lsquo;A&rsquo; is &lsquo;E&rsquo;.</p>",
                    solution_hi: "<p>5.(c) पासे i और ii में, अक्षर (B, C) उभयनिष्ठ है<br>तो, \'&lsquo;A&rsquo;\' का विपरीत &lsquo;E है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which of the following letter-clusters will replace the question mark (?) in the given series to make it logically complete?<br>WIX, VHW, UGV, TFU, ?</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन-सा अक्षर-समूह दी गई श्रृंखला को तार्किक रूप से पूर्ण बनाने के लिए इसमें प्रश्न चिह्न (?) को प्रतिस्थापित करेगा?<br>WIX, VHW, UGV, TFU, ?</p>",
                    options_en: ["<p>TEU</p>", "<p>SEU</p>", 
                                "<p>SET</p>", "<p>SFT</p>"],
                    options_hi: ["<p>TEU</p>", "<p>SEU</p>",
                                "<p>SET</p>", "<p>SFT</p>"],
                    solution_en: "<p>6.(c)<br><strong id=\"docs-internal-guid-71d0f43c-7fff-fcd0-78e3-4031c7469f3d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfKzXLLt5R4NbUecez7GHyopGaaoF09u6Jrt6HraLN8brNoDizPPZgclm-cV5UVX0RY3DCCfo5XntH0iYcLxmH62YPwfWtZODAGZN5gy25b5tUtc65-uNllSWH4-eg3rS8HKrqFwbDzBrO7cjTO9clDnQbH?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"246\" height=\"91\"></strong></p>",
                    solution_hi: "<p>6.(c)<br><strong id=\"docs-internal-guid-71d0f43c-7fff-fcd0-78e3-4031c7469f3d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfKzXLLt5R4NbUecez7GHyopGaaoF09u6Jrt6HraLN8brNoDizPPZgclm-cV5UVX0RY3DCCfo5XntH0iYcLxmH62YPwfWtZODAGZN5gy25b5tUtc65-uNllSWH4-eg3rS8HKrqFwbDzBrO7cjTO9clDnQbH?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"246\" height=\"91\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the option that is related to the third word in the same way as the second word is related to the first word.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br>Sugar : Sweet : : Habanero : ?</p>",
                    question_hi: "<p>7. उस विकल्प का चयन कीजिए जो तीसरे शब्द से उसी प्रकार संबंधित है जिस प्रकार दूसरा शब्द, पहले शब्द से संबंधित है।<br>(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होनी चाहिए)<br>शक्कर : मीठी : : शिमला मिर्च : ?</p>",
                    options_en: ["<p>Salty</p>", "<p>Bitter</p>", 
                                "<p>Sour</p>", "<p>Spicy</p>"],
                    options_hi: ["<p>नमकीन</p>", "<p>कड़वी</p>",
                                "<p>खट्टी</p>", "<p>तीखी</p>"],
                    solution_en: "<p>7.(d)<br>The relationship between Sugar and Sweet is one of characteristic taste&mdash;sugar is known for its sweetness. In the same way, Habanero is a type of chili pepper, and its distinctive taste is spicy.</p>",
                    solution_hi: "<p>7.(d)<br>चीनी और मीठी के बीच का संबंध विशिष्ट स्वाद का है- चीनी अपनी मिठास के लिए जानी जाती है। उसी तरह, शिमला मिर्च एक प्रकार की मिर्च है, और इसका विशिष्ट स्वाद मसालेदार (तीखी) होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><strong id=\"docs-internal-guid-6f081012-7fff-db6c-b4aa-9601d14674ab\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc8VO9Bp2ug1MchcW5-qsk__N_vFpXnzXsvVT93zB4v471a3WZ8EnbReR_TsiwmoN5S0Qd7rv94U-7EJxqxS1KYx8XPzWhXKdCRFUHF8BJqvm7oqjlCNN5lK3EDLtjOHm_NAkO6REx8IxuK6pElpoxJthJ2?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"281\" height=\"59\"></strong></p>",
                    question_hi: "<p>8. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><strong id=\"docs-internal-guid-53448350-7fff-d72d-69f1-1aa0f2f31796\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc8VO9Bp2ug1MchcW5-qsk__N_vFpXnzXsvVT93zB4v471a3WZ8EnbReR_TsiwmoN5S0Qd7rv94U-7EJxqxS1KYx8XPzWhXKdCRFUHF8BJqvm7oqjlCNN5lK3EDLtjOHm_NAkO6REx8IxuK6pElpoxJthJ2?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"281\" height=\"59\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-b036b9db-7fff-f405-e314-883b7a966f9c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfD1f-zlc9GaRBW8mL01RJXqVRBJH0q1lgCfGyRa7Z2JQLmEPidB2IM7QTDFbNYxJ2TjqNA7SJb4_ggot3Sah-j_pcARJmtoibPLtP2JZ3mpi1gx_Ixyzvy1OTS63MET51wzfgmj1X13IaXQjTtovCxBYlR?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-bdc6ee9d-7fff-9184-1e16-4cd58a9226a6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdIb96qSoJITBoerX487LTEMMozNapYAq8dXnIAJWAWBKZ2_OqmHd1klqebbpLBkrdeRO1dAF5SPPTlKU3o0P-jlEeXUzs0NcNrRric7dbw21VuzYovDZGCsP851xPEu5hjVlFRLYWaXdEHt0MhTYYchlyy?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-66517379-7fff-a9c2-26bf-df44258c7d55\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfVW3jyx4O7mVss77OUakxoNkdNh6iVsSzlkwZ4VDnAHLYov8CSzJuUvH81P07ZyOteBnRWMe54Bavk484altiqfNrfP2PLoSaan6gU0vlFSa3mVTPbdNs2ZV4xqTq41kCmwwfxag?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-352e512a-7fff-c225-d503-b349d7333aa9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeJdJveZlrDYBwWZS7ApMiUw9-Cu0tE30muWeMacw9I8HuClrMQwfM_P6jFQ3bS93f85-mn_DtMtGWCgsL-nTcChV21hLIwhtjRuqvCv954zzwlgxitVtgJhU0NirBuLHsmPUpcYLPTvjMCCLu4UEGZt3nK?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-5cb2aa07-7fff-ed24-c4af-1a541d990fd1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfD1f-zlc9GaRBW8mL01RJXqVRBJH0q1lgCfGyRa7Z2JQLmEPidB2IM7QTDFbNYxJ2TjqNA7SJb4_ggot3Sah-j_pcARJmtoibPLtP2JZ3mpi1gx_Ixyzvy1OTS63MET51wzfgmj1X13IaXQjTtovCxBYlR?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-04f365c7-7fff-0597-3a7d-a4a3ab076b43\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdIb96qSoJITBoerX487LTEMMozNapYAq8dXnIAJWAWBKZ2_OqmHd1klqebbpLBkrdeRO1dAF5SPPTlKU3o0P-jlEeXUzs0NcNrRric7dbw21VuzYovDZGCsP851xPEu5hjVlFRLYWaXdEHt0MhTYYchlyy?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-adf9f337-7fff-1e2d-dc3d-fce3791deddc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfVW3jyx4O7mVss77OUakxoNkdNh6iVsSzlkwZ4VDnAHLYov8CSzJuUvH81P07ZyOteBnRWMe54Bavk484altiqfNrfP2PLoSaan6gU0vlFSa3mVTPbdNs2ZV4xqTq41kCmwwfxag?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-7ba8ddfc-7fff-37b5-ed4c-a0cae2389d77\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeJdJveZlrDYBwWZS7ApMiUw9-Cu0tE30muWeMacw9I8HuClrMQwfM_P6jFQ3bS93f85-mn_DtMtGWCgsL-nTcChV21hLIwhtjRuqvCv954zzwlgxitVtgJhU0NirBuLHsmPUpcYLPTvjMCCLu4UEGZt3nK?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>"],
                    solution_en: "<p>8.(a)<br><strong id=\"docs-internal-guid-ca95812e-7fff-5108-370e-e3f306605e25\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfD1f-zlc9GaRBW8mL01RJXqVRBJH0q1lgCfGyRa7Z2JQLmEPidB2IM7QTDFbNYxJ2TjqNA7SJb4_ggot3Sah-j_pcARJmtoibPLtP2JZ3mpi1gx_Ixyzvy1OTS63MET51wzfgmj1X13IaXQjTtovCxBYlR?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>",
                    solution_hi: "<p>8.(a)<br><strong id=\"docs-internal-guid-ca95812e-7fff-5108-370e-e3f306605e25\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfD1f-zlc9GaRBW8mL01RJXqVRBJH0q1lgCfGyRa7Z2JQLmEPidB2IM7QTDFbNYxJ2TjqNA7SJb4_ggot3Sah-j_pcARJmtoibPLtP2JZ3mpi1gx_Ixyzvy1OTS63MET51wzfgmj1X13IaXQjTtovCxBYlR?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(5, 80, 16)<br>(18, 108, 6)</p>",
                    question_hi: "<p>9. उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्&zwj;चय की संख्याएँ संबंधित हैं।<br><strong>(नोट</strong> : संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(5, 80, 16)<br>(18, 108, 6)</p>",
                    options_en: ["<p>(15, 185, 14)</p>", "<p>(13, 91, 7)</p>", 
                                "<p>(17, 8, 135)</p>", "<p>(9, 81, 8)</p>"],
                    options_hi: ["<p>(15, 185, 14)</p>", "<p>(13, 91, 7)</p>",
                                "<p>(17, 8, 135)</p>", "<p>(9, 81, 8)</p>"],
                    solution_en: "<p>9.(b) <strong>Logic:</strong> 1st no. &times; 3rd no. = 2nd no.<br>(5, 80, 16) :- 5 &times; 16 = 80<br>(18, 108, 6) :- 18 &times; 6 = 108<br>Similarly,<br>(13, 91, 7) :- 13 &times; 7 = 91</p>",
                    solution_hi: "<p>9.(b) <strong>Logic:</strong> तर्क: पहली संख्या &times; तीसरी संख्या = दूसरी संख्या<br>(5, 80, 16) :- 5 &times; 16 = 80<br>(18, 108, 6) :- 18 &times; 6 = 108<br>इसी प्रकार,<br>(13, 91, 7) :- 13 &times; 7 = 91</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;;<br>&lsquo;A &minus; B&rsquo; means &lsquo;A is the brother of B&rsquo;;<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo; and<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the father of B&rsquo;.<br>Based on the above, how is S related to P if &lsquo;P &times; Q &divide; R &minus; S &times; A&rsquo;?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है \'A, B की माता है\';<br>\'A &minus; B\' का अर्थ है \'A, B का भाई है\';<br>\'A &times; B\' का अर्थ है \'A, B की पत्नी है\' और<br>\'A &divide; B\' का अर्थ है \'A, B का पिता है\'।<br>उपर्युक्त के आधार पर, यदि \'P &times; Q &divide; R &minus; S &times; A\' है, तो S, P से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Mother</p>", "<p>Daughter</p>", 
                                "<p>Son</p>", "<p>Father</p>"],
                    options_hi: ["<p>माता</p>", "<p>पुत्री</p>",
                                "<p>पुत्र</p>", "<p>पिता</p>"],
                    solution_en: "<p>10.(b)<br><strong id=\"docs-internal-guid-33be9417-7fff-c242-bcbd-4dbca68a3663\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcRAjV86l3P-yHxB3jQU2qo8lnQJ3Vl3qJJG5VWIF6vXEhGa151zo8YTJV15Z5HF0-n-bgm6Jec5_XONvgsCHsUyLCLLi-nxdG6QTf3aCPg2LJknFH6GByQwrBrZQrh83Nw2WU7N9Tf7Ge2BXtPmk860kTO?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"186\" height=\"84\"></strong><br>&lsquo;S&rsquo; is the daughter of &lsquo;P&rsquo;.</p>",
                    solution_hi: "<p>10.(b)<br><strong id=\"docs-internal-guid-33be9417-7fff-c242-bcbd-4dbca68a3663\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcRAjV86l3P-yHxB3jQU2qo8lnQJ3Vl3qJJG5VWIF6vXEhGa151zo8YTJV15Z5HF0-n-bgm6Jec5_XONvgsCHsUyLCLLi-nxdG6QTf3aCPg2LJknFH6GByQwrBrZQrh83Nw2WU7N9Tf7Ge2BXtPmk860kTO?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"186\" height=\"84\"></strong><br>\'S\', \'P\' की पुत्री है ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Identify the figure given in the options, which when put in place of the question mark (?) will logically complete the series.<br><strong id=\"docs-internal-guid-a09354d1-7fff-1552-3f6f-2254951c610c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcGYd-hoUB1DeG0Li-iOmLhitcTlVtWh0iNfi0XK4UsQQWIiGiTcr4lEt_-fz8rQ0EEr89u4Jg63_tRv2iZoV9EK2igvYyGtIXDKQxkQMC-r9ZRzZJgv80y76RRCa0UaaLh4a9hNP7S4iClxTDdOXJaGIG6?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"302\" height=\"62\"></strong></p>",
                    question_hi: "<p>11. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे प्रश्न-चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><strong id=\"docs-internal-guid-38c52c58-7fff-87d8-facf-34f7c8b7b183\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcGYd-hoUB1DeG0Li-iOmLhitcTlVtWh0iNfi0XK4UsQQWIiGiTcr4lEt_-fz8rQ0EEr89u4Jg63_tRv2iZoV9EK2igvYyGtIXDKQxkQMC-r9ZRzZJgv80y76RRCa0UaaLh4a9hNP7S4iClxTDdOXJaGIG6?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"302\" height=\"62\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-056e3524-7fff-29c0-2da2-e2b378b3d025\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe59m1a4ry1-pyoeG22dlzxGQ1baYmigbzvUzb2UCv9ajHIHALpOEGB2DpsaSVgLcTSu-2UHlvKMXArpj9U37zlm_uHMEAm0gR6pXO7SjsZXL_4xjnHWoz0EmCTpuM-Bn1XTUxbsivwMnGCYMrUiy4XC6Ub?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-6d71b8e4-7fff-3d56-40b9-2e489cd2ac15\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfZJ6v8Oa3ll2_IL3ELPu6qDZCus_CfTlH7isabUUqieJIeXi00qhIn6-K2ziIHSkm70f5EKBWG6cNGGUSMn5j9Owe0EDs4D_WJuv9xE1O0YtS_GVUy2PCY0yKDi07w7DtATi6HnR9_LPv1DgJiRTq8m0oe?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-000b3e47-7fff-59b0-f4f0-c4c4e17f279d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcv3a8TUfJmUuiopq9dGb_KtZNeeMxj30DvAoxvSen_aloYKfVFYW1_DAKOUKK5YiWNdI0-kSueLfjeMxcP0L-UsP1rC3MenCZq1roQpbPboVGD-qOzAKaUul6TodISlUiXmQzHivb9f_bmtzXpnhwvO4Q?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-17cade49-7fff-703d-4c0b-6ee7caed7996\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcrG8SdiRmSjAcMtnhF7vE5CDS3Y4qhOlMumqloKbgzud0woTscQa0WUsKsazA602YgVBQ9MCQtw_6xOua3Z6JDZSbcjRZQzgh-Tk7d2UapO0HsAo4nHCVQyPXHDQc10wOlzPLA5uXqfhCB8H5sHwktqpuh?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-a88dc39c-7fff-9c6f-7668-67b471119722\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe59m1a4ry1-pyoeG22dlzxGQ1baYmigbzvUzb2UCv9ajHIHALpOEGB2DpsaSVgLcTSu-2UHlvKMXArpj9U37zlm_uHMEAm0gR6pXO7SjsZXL_4xjnHWoz0EmCTpuM-Bn1XTUxbsivwMnGCYMrUiy4XC6Ub?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-0a147f1e-7fff-b01d-6925-0fa27905ea53\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfZJ6v8Oa3ll2_IL3ELPu6qDZCus_CfTlH7isabUUqieJIeXi00qhIn6-K2ziIHSkm70f5EKBWG6cNGGUSMn5j9Owe0EDs4D_WJuv9xE1O0YtS_GVUy2PCY0yKDi07w7DtATi6HnR9_LPv1DgJiRTq8m0oe?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-86823467-7fff-c4c2-31c7-b9311cb95ae6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcv3a8TUfJmUuiopq9dGb_KtZNeeMxj30DvAoxvSen_aloYKfVFYW1_DAKOUKK5YiWNdI0-kSueLfjeMxcP0L-UsP1rC3MenCZq1roQpbPboVGD-qOzAKaUul6TodISlUiXmQzHivb9f_bmtzXpnhwvO4Q?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-25510812-7fff-e4dd-76a5-bf729c62bcd9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcrG8SdiRmSjAcMtnhF7vE5CDS3Y4qhOlMumqloKbgzud0woTscQa0WUsKsazA602YgVBQ9MCQtw_6xOua3Z6JDZSbcjRZQzgh-Tk7d2UapO0HsAo4nHCVQyPXHDQc10wOlzPLA5uXqfhCB8H5sHwktqpuh?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>"],
                    solution_en: "<p>11.(d)<br><strong id=\"docs-internal-guid-25510812-7fff-e4dd-76a5-bf729c62bcd9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcrG8SdiRmSjAcMtnhF7vE5CDS3Y4qhOlMumqloKbgzud0woTscQa0WUsKsazA602YgVBQ9MCQtw_6xOua3Z6JDZSbcjRZQzgh-Tk7d2UapO0HsAo4nHCVQyPXHDQc10wOlzPLA5uXqfhCB8H5sHwktqpuh?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>",
                    solution_hi: "<p>11.(d)<br><strong id=\"docs-internal-guid-25510812-7fff-e4dd-76a5-bf729c62bcd9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcrG8SdiRmSjAcMtnhF7vE5CDS3Y4qhOlMumqloKbgzud0woTscQa0WUsKsazA602YgVBQ9MCQtw_6xOua3Z6JDZSbcjRZQzgh-Tk7d2UapO0HsAo4nHCVQyPXHDQc10wOlzPLA5uXqfhCB8H5sHwktqpuh?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed).<br><strong id=\"docs-internal-guid-ab6b45ea-7fff-4f45-9cd4-cea07e48d35c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdNiACClZgD9JHzeTpdY9IK80zxa8J3Bm7DN3tuL4a6cOF4OXD4c2q6dnPM5Vfrqq74V8NGCiCrDHBrKGdrDnX5MYmImnJpCvEGzMGiOfIcyK06F_LtOLw03Y85DikW0qgaIOHK9dRTSNtk0Kd9je4Wwco?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"76\" height=\"92\"></strong></p>",
                    question_hi: "<p>12. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति (X) सन्निहित है (घुमाने की अनुमति नहीं है)।<br><strong id=\"docs-internal-guid-705888e4-7fff-be6d-d72e-8f06660926bf\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdNiACClZgD9JHzeTpdY9IK80zxa8J3Bm7DN3tuL4a6cOF4OXD4c2q6dnPM5Vfrqq74V8NGCiCrDHBrKGdrDnX5MYmImnJpCvEGzMGiOfIcyK06F_LtOLw03Y85DikW0qgaIOHK9dRTSNtk0Kd9je4Wwco?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"76\" height=\"92\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-62966ea0-7fff-68b3-68b8-0c799ed466fd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFv5ohNfkS0ywQ_cK2wD4yAb-HMMkrKqfYbe6l3EBd89eN7ebLuE0AAUavgo4heHfgNoHmlsF8Aow-Hh24J5avZxSGhed4v6DysolzPYNnf5GV7nPl99vIJhhTiVsuGBGMB35WDRVqAZQsKRPHp11Eyity?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-db15b202-7fff-7e8b-dbd8-44f1c5b6c578\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXctJHvQq4VvqIfCPwWPrM1_zaxtMb5mJNaepp7CwlqLvpyQJ3cLLf1nHf0xGArSTEhfD_5Y7R-IveA3fOFuMFN9CLqTaVtdns_Px9EqdP7Uvw4ok-cqYqUSh4ew3-edGcXv2sFTn8GbxSnus44MfUpG-xJE?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-5ea9c5b7-7fff-92fe-3fc7-4bfd62c06599\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXde9EYKY8iYSq9AaVlQdgQsT2hMYhZpZHDk_XjBHvBOmJb1GaD2qwuq6c6fsUgzrBlB6M3xE-C-qmTwxNssjIfQFU-b6-VoIUIVFN60QghQxU-oxie1LX8MJAOR9J7Rbj3QCV4lSMwsUmlqDuZ3oC5FAZI?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-43f20a31-7fff-a7f0-7f46-8d1de1af5de5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4eEKHMeaZJ4DfP4TR8XA8ql8AsaJwK_DT46FS92iIa4kucXNeeH3IyxXRc_Xx25gRnZU_aDNl-M_WXPjgnrTju-mI7SxwQ_JOhf2hCdu_-CXa66A4cgqRgXBk9HER8StvrxtbGPQ8C4WTkCzZCYXpY0aM?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-80b00ee4-7fff-d60b-f598-3bef01764911\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFv5ohNfkS0ywQ_cK2wD4yAb-HMMkrKqfYbe6l3EBd89eN7ebLuE0AAUavgo4heHfgNoHmlsF8Aow-Hh24J5avZxSGhed4v6DysolzPYNnf5GV7nPl99vIJhhTiVsuGBGMB35WDRVqAZQsKRPHp11Eyity?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-93002d7f-7fff-ebfd-1229-adf9b623f995\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXctJHvQq4VvqIfCPwWPrM1_zaxtMb5mJNaepp7CwlqLvpyQJ3cLLf1nHf0xGArSTEhfD_5Y7R-IveA3fOFuMFN9CLqTaVtdns_Px9EqdP7Uvw4ok-cqYqUSh4ew3-edGcXv2sFTn8GbxSnus44MfUpG-xJE?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-ad59498d-7fff-0174-3d76-3940cb31be5c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXde9EYKY8iYSq9AaVlQdgQsT2hMYhZpZHDk_XjBHvBOmJb1GaD2qwuq6c6fsUgzrBlB6M3xE-C-qmTwxNssjIfQFU-b6-VoIUIVFN60QghQxU-oxie1LX8MJAOR9J7Rbj3QCV4lSMwsUmlqDuZ3oC5FAZI?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-f82b825f-7fff-15cd-9dfe-19e0d11778e5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd4eEKHMeaZJ4DfP4TR8XA8ql8AsaJwK_DT46FS92iIa4kucXNeeH3IyxXRc_Xx25gRnZU_aDNl-M_WXPjgnrTju-mI7SxwQ_JOhf2hCdu_-CXa66A4cgqRgXBk9HER8StvrxtbGPQ8C4WTkCzZCYXpY0aM?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>"],
                    solution_en: "<p>12.(a)<br><strong id=\"docs-internal-guid-40fb12d0-7fff-3080-5056-fb5bd63ffe75\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFv5ohNfkS0ywQ_cK2wD4yAb-HMMkrKqfYbe6l3EBd89eN7ebLuE0AAUavgo4heHfgNoHmlsF8Aow-Hh24J5avZxSGhed4v6DysolzPYNnf5GV7nPl99vIJhhTiVsuGBGMB35WDRVqAZQsKRPHp11Eyity?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>",
                    solution_hi: "<p>12.(a)<br><strong id=\"docs-internal-guid-40fb12d0-7fff-3080-5056-fb5bd63ffe75\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdFv5ohNfkS0ywQ_cK2wD4yAb-HMMkrKqfYbe6l3EBd89eN7ebLuE0AAUavgo4heHfgNoHmlsF8Aow-Hh24J5avZxSGhed4v6DysolzPYNnf5GV7nPl99vIJhhTiVsuGBGMB35WDRVqAZQsKRPHp11Eyity?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"72\" height=\"72\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option that represents the letters that when placed from left to right in the blanks below will complete the letter series.<br>_ Y Z K X K X _ Z K X _ X Y _ K X K X Y Z _ X K X Y</p>",
                    question_hi: "<p>13. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्&zwj;हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर अक्षर श्रृंखला पूरी हो जाएगी।<br>_ Y Z K X K X _ Z K X _ X Y _ K X K X Y Z _ X K X Y</p>",
                    options_en: ["<p>XYZKK</p>", "<p>XYKZK</p>", 
                                "<p>KXYKZ</p>", "<p>KYXZK</p>"],
                    options_hi: ["<p>XYZKK</p>", "<p>XYKZK</p>",
                                "<p>KXYKZ</p>", "<p>KYXZK</p>"],
                    solution_en: "<p>13.(b)<br><strong><span style=\"text-decoration: underline;\">X</span></strong> Y Z K X K/ X <strong><span style=\"text-decoration: underline;\">Y</span></strong> Z K X K /X Y Z K X <strong><span style=\"text-decoration: underline;\">K</span></strong>/ X Y <strong><span style=\"text-decoration: underline;\">Z</span></strong> K X K /X Y</p>",
                    solution_hi: "<p>13.(b)<br><strong><span style=\"text-decoration: underline;\">X</span></strong> Y Z K X K/ X <strong><span style=\"text-decoration: underline;\">Y</span></strong> Z K X K /X Y Z K X <strong><span style=\"text-decoration: underline;\">K</span></strong>/ X Y <strong><span style=\"text-decoration: underline;\">Z</span></strong> K X K /X Y</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements</strong> :<br>Some pineapples are oranges.<br>All oranges are bananas.<br>All bananas are plums.<br><strong>Conclusions</strong> :<br>I. Some pineapples are plums.<br>II. All oranges are plums.<br>III. No pineapple is a banana.</p>",
                    question_hi: "<p>14. तीन कथन दिए गए हैं, जिसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्धारित करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन</strong> :<br>कुछ अनानास, संतरे हैं।<br>सभी संतरे, केले हैं।<br>सभी केले, आलूबुखारा हैं।<br><strong>निष्&zwj;कर्ष</strong> :<br>I. कुछ अनानास, आलूबुखारा हैं।<br>II. सभी संतरे, आलूबुखारा हैं।<br>III. कोई भी अनानास, केला नहीं है।</p>",
                    options_en: ["<p>Only conclusions I and III follow</p>", "<p>Only conclusions I and II follow</p>", 
                                "<p>Only conclusion III follows</p>", "<p>Only conclusions II and III follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I और III अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष I और II अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष III अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष II और III अनुसरण करते हैं</p>"],
                    solution_en: "<p>14.(b)<br><strong id=\"docs-internal-guid-294903e2-7fff-1fa8-e074-1337e8a8f261\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfBNOgTUK7ge6mjvc24Dh154MtFBEZtATjQBE_Ewl9TpaqJxnz_95qfHoRbjWhc4JZfJcg6VMQDsB7JxHVkO_4ybSClAmlzPZdE0uBEW54VVmHKo3pccC4R8e6Ke-8BwCGy-rGXi4ATpLQY9v_RxZn6eyRG?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"278\" height=\"104\"></strong><br>Only conclusions I and II follow</p>",
                    solution_hi: "<p>14.(b)<br><strong id=\"docs-internal-guid-9d92e0c9-7fff-2cb7-aef7-6a824b860bdd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe01hL3_1WRIRGCX3P8C7WQebGCraVCF92lX5KnQw6JMyJArBcFSgcC4IBqG1m1ktKLXmhDXlD8NevIu910NcjFQr7BrBfp4a1SFm0Fpwrba5KBerg6WUm_QmZyOAVt2oINkbUJVhrYpvDvtiQ9kWMJZvlA?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"261\" height=\"87\"></strong><br>केवल निष्कर्ष I और II अनुसरण करते हैं</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?</p>",
                    question_hi: "<p>15. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?</p>",
                    options_en: ["<p>1974</p>", "<p>1749</p>", 
                                "<p>1947</p>", "<p>1497</p>"],
                    options_hi: ["<p>1974</p>", "<p>1749</p>",
                                "<p>1947</p>", "<p>1497</p>"],
                    solution_en: "<p>15.(b) <strong>Given:</strong> 1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?<br>As per the instructions given in the question, after interchanging the symbols &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>1064 &divide; 2 + 653 &times; 2 - 89 = ?<br>532 + 653 &times; 2 - 89 <br>532 + 1306 - 89<br>1838 - 89 =1749</p>",
                    solution_hi: "<p>15.(b) <strong>दिया गया है</strong>: 1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीकों \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>1064 &divide; 2 + 653 &times; 2 - 89 = ?<br>532 + 653 &times; 2 - 89 <br>532 + 1306 - 89<br>1838 - 89 =1749</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out.<br><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>16. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।<br><strong>(नोट</strong> : गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>16 &ndash; 9 &ndash; 139</p>", "<p>25 &ndash; 7 &ndash; 175</p>", 
                                "<p>12 &ndash; 7 &ndash; 79</p>", "<p>17 &ndash; 5 &ndash; 80</p>"],
                    options_hi: ["<p>16 &ndash; 9 &ndash; 139</p>", "<p>25 &ndash; 7 &ndash; 175</p>",
                                "<p>12 &ndash; 7 &ndash; 79</p>", "<p>17 &ndash; 5 &ndash; 80</p>"],
                    solution_en: "<p>16.(b) <strong>Logic:</strong> (1st no. &times; 2nd no.) - 5 = 3rd no.<br>16 &ndash; 9 &ndash; 139 :- (16 &times; 9) - 5 = 139<br>12 &ndash; 7 &ndash; 79 :- (12 &times; 7) - 5 = 79<br>17 &ndash; 5 &ndash; 80 :- (17 &times; 5) - 5 = 80<br>But<br>25 &ndash; 7 &ndash; 175 :- (25 &times; 7) - 5 = 170 (<math display=\"inline\"><mo>&#8800;</mo></math>175)</p>",
                    solution_hi: "<p>16.(b) <strong>तर्क:</strong> (पहली संख्या &times; दूसरी संख्या) - 5 = तीसरी संख्या <br>16 &ndash; 9 &ndash; 139 :- (16 &times; 9) - 5 = 139<br>12 &ndash; 7 &ndash; 79 :- (12 &times; 7) - 5 = 79<br>17 &ndash; 5 &ndash; 80 :- (17 &times; 5) - 5 = 80<br>लेकिन<br>25 &ndash; 7 &ndash; 175 :- (25 &times; 7) - 5 = 170 (<math display=\"inline\"><mo>&#8800;</mo></math>175)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. 51 is related to 256 by certain logic. Following the same logic, 53 is related to 266. To which of the following is 55 related, following the same logic?</p>\n<p><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be&nbsp;performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>17. 51 एक निश्चित तर्क का अनुसरण करते हुए 256 से संबंधित है। इसी तर्क का अनुसरण करते हुए 53, 266 से संबंधित है। समान तर्क का अनुसरण करते हुए, 55 निम्नलिखित में से किससे संबंधित है?<br><strong>नोट</strong> : संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>276</p>", "<p>290</p>", 
                                "<p>249</p>", "<p>213</p>"],
                    options_hi: ["<p>276</p>", "<p>290</p>",
                                "<p>249</p>", "<p>213</p>"],
                    solution_en: "<p>17.(a) <strong>Logic:</strong> (1st number &times; 5) + 1 = 2nd number <br>(51 : 256) :- (51 &times; 5) + 1 = 256<br>(53 : 266) :- (53 &times; 5) + 1 = 266<br>Similarly<br>(55 : <math display=\"inline\"><mi>x</mi></math>) :- (55 &times; 5) + 1 = 276</p>",
                    solution_hi: "<p>17.(a) <strong>तर्क:</strong> (पहली संख्या &times; 5) + 1 = दूसरी संख्या&nbsp;<br>(51 : 256) :- (51 &times; 5) + 1 = 256<br>(53 : 266) :- (53 &times; 5) + 1 = 266<br>उसी प्रकार<br>(55 : <math display=\"inline\"><mi>x</mi></math>) :- (55 &times; 5) + 1 = 276</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language, \'TABU\' is written as \'SBAV\' and \'TANK&rsquo; is written as \'SBML\'. How will \'TAIL&rsquo; be written in that language?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में, \'TABU\' को \'SBAV\' लिखा जाता है और &lsquo;TANK&rsquo; को \'SBML\' लिखा जाता है। इसी कूट भाषा में \'TAIL&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>SMBH</p>", "<p>SMHB</p>", 
                                "<p>SBHM</p>", "<p>SBMH</p>"],
                    options_hi: ["<p>SMBH</p>", "<p>SMHB</p>",
                                "<p>SBHM</p>", "<p>SBMH</p>"],
                    solution_en: "<p>18.(c)<br><strong id=\"docs-internal-guid-262207e3-7fff-c121-25e3-bd59ed23da85\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcmpfCUj0TIbYOCIM7DdfCcu7aV4IX0H5OZbpu-LG6ngFRS2j1PXpAseo0E3bUBAvD1HzgvrnzyF6SGNFa4OwbQ1XdHeakwGvimgPmZvZ8B6BnXa2pwr8SYSCsLaIG6Rwu_nBTuBu-uWMIviH1_44d9IdE?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"103\" height=\"90\"></strong><br><strong id=\"docs-internal-guid-57b3f4de-7fff-cbec-6063-dba334977c88\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeEMWzv2vxKLx7mOgcBND8T95AEqE5yusbUNuPJQ-u6E7OV1oMxYrhOnTB6Uq6q0bxfFDEaVrFgJ0Fo2gTAc8cQ6LegIAtn_nzHVkufHX05vKlHiS3-DsCE9jmsslqReh61VKvTgCEkctgW-hMk10XgnTE?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"103\" height=\"90\"></strong><br>Similarly<br><strong id=\"docs-internal-guid-eab8445e-7fff-dbb3-373f-4b32fb2848e9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdpsE7OC5munZwYqVLNksswjd-aRHxF30VOTJbiuDrdZDpW4eA91lsT5QBd3Z01TFt-VsxCIok6kKKLjLmhOWmKoO_Qsda-RfY37BxtgI7IY2GyljtQ9j_qbFuilMhQorsP7jcKNsNzsCPJdKjpupizPw8s?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"103\" height=\"90\"></strong></p>",
                    solution_hi: "<p>18.(c)<br><strong id=\"docs-internal-guid-d0206f96-7fff-fdb3-3d17-316ef8554d8d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcmpfCUj0TIbYOCIM7DdfCcu7aV4IX0H5OZbpu-LG6ngFRS2j1PXpAseo0E3bUBAvD1HzgvrnzyF6SGNFa4OwbQ1XdHeakwGvimgPmZvZ8B6BnXa2pwr8SYSCsLaIG6Rwu_nBTuBu-uWMIviH1_44d9IdE?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"103\" height=\"90\"></strong><br><strong id=\"docs-internal-guid-747d42ef-7fff-07a5-cf87-828f20118175\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeEMWzv2vxKLx7mOgcBND8T95AEqE5yusbUNuPJQ-u6E7OV1oMxYrhOnTB6Uq6q0bxfFDEaVrFgJ0Fo2gTAc8cQ6LegIAtn_nzHVkufHX05vKlHiS3-DsCE9jmsslqReh61VKvTgCEkctgW-hMk10XgnTE?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"103\" height=\"90\"></strong><br>उसी प्रकार<br><strong id=\"docs-internal-guid-8cb35e2e-7fff-14f8-3d02-52a99e285d8d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdpsE7OC5munZwYqVLNksswjd-aRHxF30VOTJbiuDrdZDpW4eA91lsT5QBd3Z01TFt-VsxCIok6kKKLjLmhOWmKoO_Qsda-RfY37BxtgI7IY2GyljtQ9j_qbFuilMhQorsP7jcKNsNzsCPJdKjpupizPw8s?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"103\" height=\"90\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. The position of how many letters will remain unchanged if all the letters in the word MENTOR are arranged in English alphabetical order?</p>",
                    question_hi: "<p>19. यदि MENTOR शब्द के सभी अक्षरों को अंग्रेजी वर्णानुक्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित बनी रहेगी?</p>",
                    options_en: ["<p>Two</p>", "<p>One</p>", 
                                "<p>None</p>", "<p>Three</p>"],
                    options_hi: ["<p>दो</p>", "<p>एक</p>",
                                "<p>किसी की भी नहीं</p>", "<p>तीन</p>"],
                    solution_en: "<p>19.(b)<br><strong id=\"docs-internal-guid-9282be64-7fff-61ce-4aee-d185728d9f38\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdfQCyvadPas81de1c_wgIkheD4iTaQK9826S0IJY8i_Sdxm4MW47mg5b76Oq1xPSngY4dc9kHaBRXriYeBbXg0uptdZn9OXL386fX87kKAy0Dq8BwHDec6LArogTtA4OlXWHi3Wof0bpdTHX-1QKvyb-Xm?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"143\" height=\"92\"></strong><br>Position of only one letter will be unchanged.</p>",
                    solution_hi: "<p>19.(b)<br><strong id=\"docs-internal-guid-9282be64-7fff-61ce-4aee-d185728d9f38\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdfQCyvadPas81de1c_wgIkheD4iTaQK9826S0IJY8i_Sdxm4MW47mg5b76Oq1xPSngY4dc9kHaBRXriYeBbXg0uptdZn9OXL386fX87kKAy0Dq8BwHDec6LArogTtA4OlXWHi3Wof0bpdTHX-1QKvyb-Xm?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"143\" height=\"92\"></strong><br>केवल एक अक्षर की स्थिति अपरिवर्तित रहेगी ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; are interchanged?<br>37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?</p>",
                    question_hi: "<p>20. निम्नलिखित समीकरण में, यदि \'&divide;\' और \'&ndash;\' को आपस में बदल दिया जाए तथा \'&times;\' और \'+\' को आपस में बदल दिया जाए, तो \'?\' के स्थान पर क्या आएगा?<br>37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?</p>",
                    options_en: ["<p>41</p>", "<p>55</p>", 
                                "<p>37</p>", "<p>47</p>"],
                    options_hi: ["<p>41</p>", "<p>55</p>",
                                "<p>37</p>", "<p>47</p>"],
                    solution_en: "<p>20.(a) <strong>Given:</strong> 37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?<br>As per the instructions given in question , after interchanging the symbol &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo;and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; we get.<br>37 - 56 &divide; 7 + 2 &times; 6 = ?<br>37 - 8 + 2 &times; 6 <br>37 - 8 + 12<br>49 - 8 = 41</p>",
                    solution_hi: "<p>20.(a) <strong>दिया गया है</strong>: 37 &divide; 56 &ndash; 7 &times; 2 + 6 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'&divide;\' और \'-\' तथा \'&times;\' और \'+\' को आपस में बदलने पर हमें प्राप्त होता है।<br>37 - 56 &divide; 7 + 2 &times; 6 = ?<br>37 - 8 + 2 &times; 6 <br>37 - 8 + 12<br>49 - 8 = 41</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. How many triangles are there in the figure shown below?<br><strong id=\"docs-internal-guid-b2ceeee4-7fff-312c-b7f8-dedc6634d4f9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcocoCH0KyBxtuBrUCRR_yf8OW1BlX5SQqTJ8SEA5dPEupl4nM9EW15S2ZE6KJZ0R9teXBClDF-2xWLlTUbIDYnjoPm8HNGPLqnej8AdPgSzYUFPzuf0tVkG2oEfRrqJ_yfOCohCfrPiUd3z6mAGqkB_xXU?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"108\" height=\"102\"></strong></p>",
                    question_hi: "<p>21. नीचे दर्शायी गयी आकृति में कितने त्रिभुज हैं?<br><strong id=\"docs-internal-guid-b2ceeee4-7fff-312c-b7f8-dedc6634d4f9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcocoCH0KyBxtuBrUCRR_yf8OW1BlX5SQqTJ8SEA5dPEupl4nM9EW15S2ZE6KJZ0R9teXBClDF-2xWLlTUbIDYnjoPm8HNGPLqnej8AdPgSzYUFPzuf0tVkG2oEfRrqJ_yfOCohCfrPiUd3z6mAGqkB_xXU?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"108\" height=\"102\"></strong></p>",
                    options_en: ["<p>12</p>", "<p>14</p>", 
                                "<p>9</p>", "<p>11</p>"],
                    options_hi: ["<p>12</p>", "<p>14</p>",
                                "<p>9</p>", "<p>11</p>"],
                    solution_en: "<p>21.(b)<br><strong id=\"docs-internal-guid-44ea39a7-7fff-ab0c-033e-23d9e116ed3e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcxzYUKb_xaBieiMDNVFxmXBSSathCS9uZKq_qAg3dbCML1w88YZDKtvGHAt5dx4OF6Y072KWgYu66CdFLYAhTTvDDRITVp-N4Zt1rw4ZrVsOtzV_lRLPFkPtjJvXKquNoxLw92P2VInCjQPvzPLbtqgAUw?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"178\" height=\"168\"></strong><br>ABC, CDE, EFG , AHG, IJK, ILK, IKM, LKM, MNK, NOU, UVO, OPQ, RQS, TUS<br>There are 14 triangles.</p>",
                    solution_hi: "<p>21.(b)<br><strong id=\"docs-internal-guid-44ea39a7-7fff-ab0c-033e-23d9e116ed3e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcxzYUKb_xaBieiMDNVFxmXBSSathCS9uZKq_qAg3dbCML1w88YZDKtvGHAt5dx4OF6Y072KWgYu66CdFLYAhTTvDDRITVp-N4Zt1rw4ZrVsOtzV_lRLPFkPtjJvXKquNoxLw92P2VInCjQPvzPLbtqgAUw?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"178\" height=\"168\"></strong><br>ABC, CDE, EFG , AHG, IJK, ILK, IKM, LKM, MNK, NOU, UVO, OPQ, RQS, TUS<br>14 त्रिभुज हैं ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. In a certain code language, &lsquo;DRIVE&rsquo; is coded as &lsquo;62749&rsquo; and &lsquo;RIDES&rsquo; is coded as &lsquo;76284&rsquo;. What is the code for &lsquo;V&rsquo; in that language?</p>",
                    question_hi: "<p>22. एक निश्चित कूट भाषा में, \'DRIVE\' को \'62749\' के रूप में कूटबद्ध किया गया है और \'RIDES\' को \'76284\' के रूप में कूटबद्ध किया गया है। उस भाषा में \'V\' के लिए कूट क्या है?</p>",
                    options_en: ["<p>8</p>", "<p>7</p>", 
                                "<p>9</p>", "<p>6</p>"],
                    options_hi: ["<p>8</p>", "<p>7</p>",
                                "<p>9</p>", "<p>6</p>"],
                    solution_en: "<p>22.(c) \'DRIVE\' &rarr;&nbsp;\'62749\' &hellip;&hellip;&hellip;(i)<br>\'RIDES\' &rarr; \'76284\' &hellip;&hellip;&hellip;. (ii)<br>In both equations letters(R, I, D, E) are common.<br>So, from equation (i) the code of &lsquo;V&rsquo; is 9.</p>",
                    solution_hi: "<p>22.(c) \'DRIVE\' &rarr; \'62749\' &hellip;&hellip;&hellip;(i)<br>\'RIDES\' &rarr; \'76284\' &hellip;&hellip;&hellip;. (ii)<br>दोनों समीकरणों में अक्षर (R, I, D, E)उभयनिष्ठ हैं<br>तो, समीकरण (i) से \'V\' का कोड 9 है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "23. The question contains pairs of words that are related to each other in a certain way. Three of the following four word pairs are alike as these have the same relationship and thus form a group. Which word pair is the one that DOES NOT belong to that group?<br />(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)",
                    question_hi: "23. प्रश्न में ऐसे शब्द युग्म हैं जो एक निश्चित तरीके से एक-दूसरे से संबंधित हैं। निम्नलिखित चार शब्द युग्मों में से तीन शब्द युग्म एक समान हैं क्योंकि इनमें संबंध समान है और इस प्रकार वे एक समूह बनाते हैं। निम्नलिखित में से कौन-सा शब्द युग्म उस समूह से संबंधित नहीं है?<br />(शब्दों को अर्थपूर्ण अंग्रेजी/हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों / व्यंजनों / स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं होने चाहिए।)",
                    options_en: [" Tired - exhausted", " Loud - noisy", 
                                " Close - open", " Honest - truthful"],
                    options_hi: [" थका - थका माँदा", " ऊँचा स्वर - शोरगुल",
                                " बंद - खुला", " ईमानदार - सत्यनिष्ठ"],
                    solution_en: "23.(c) words (Tired - exhausted , Loud - noisy, Honest - truthful) are synonym each other  but (Close - open)<br /> is an antonym. ",
                    solution_hi: "23.(c) <br />शब्द (थका - थका माँदा, ऊँचा स्वर - शोरगुल, ईमानदार - सत्यनिष्ठ) एक दूसरे के पर्यायवाची हैं लेकिन (बंद - खुला)<br /> एक दूसरे का विलोम है",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last as shown. How would the paper look when unfolded?<br><strong id=\"docs-internal-guid-90541a91-7fff-c80d-eb35-18fee73762e2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXebYHeP8vgPOnaJ2QbMg9u23HV_rf93B3FxUWcJuxvBceB_zEnoQSSZ_lvPWEIkv5phRxW5VSEmK5djnNS6_GLPmP0m2rOE_SVwkwx7KTVQiscFK1v5i4oUl-fatQolGjs7VtTzYpQgbkkVsQBza5yiiqlO?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"201\" height=\"62\"></strong></p>",
                    question_hi: "<p>24. कागज की एक वर्गाकार शीट को बिंदीदार रेखा के साथ दिखाई गई दिशाओं के अनुसार क्रमिक रूप से मोड़ा जाता है और फिर दिखाए गए चित्र के अनुसार अंत में पंच किया जाता है। खुलने पर कागज कैसा दिखेगा?<br><strong id=\"docs-internal-guid-75b82e20-7fff-5683-84b0-636085da8d60\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXebYHeP8vgPOnaJ2QbMg9u23HV_rf93B3FxUWcJuxvBceB_zEnoQSSZ_lvPWEIkv5phRxW5VSEmK5djnNS6_GLPmP0m2rOE_SVwkwx7KTVQiscFK1v5i4oUl-fatQolGjs7VtTzYpQgbkkVsQBza5yiiqlO?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"201\" height=\"62\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-3da18a08-7fff-e05d-c31a-38205ab3ff13\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfugaAyse6miu522NTixjT0TCw3hEPXmp_dUIvBKVW2aKprc-xIj6GFbuAz-3rA8Jttc9TTlaG-OUW3SlqBV5ckusfrinZ36ppyxpBZLjHTtyVBGfo9nZQYatX-uaeCSmZXKp2WKh7T03OvnTwq95rFR9kR?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-070a57eb-7fff-0bcd-afe5-e54ef7649124\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJWbmuSX9nIsTBRogJY1ySrK2PxgEbz9-zTAoji_5odP-iKM2BwZHCu0sazJmyBPz1QyTm2nsfRhet0XSu7vzVv8WeITniksDfSkSQvJ8sOTOmymb_Vu7VxjVc5I7KgcpMG7-IBv08ytkotyP05jmwLB8?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-d961a7b3-7fff-2573-a902-18933505a951\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXemXu82DqFo2VsYdg73_3b64pG3YXe_3w2m4ynGbfJWgRr5hqOom_qFnzlLn5RlBZ2_RyBw4y7f-_Tb1xw1U_1x8D2hucUAV3Z5BzKaZ3Hrn0efwUbAF3G2723EXUx79cvH2P5_jN0mPQrhXP7LeW8kUFdq?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-8ec2aec1-7fff-4276-695c-02d79a6dafbc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLLzB0kxowim1QJxaMYD0KDLeknPCj8l0wzLopo_Wpc0WgBphtIIh2IKX0QTMBOeDofSQXhFy2PkVG_DS9A4S2NM7lFDAtEmFXHJz3yJrDZi70lbzDo8d-iIpWkoEYprAv0PJ_ic5dviuCEUGGBXHoXUsm?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-546cf0b8-7fff-6f50-5a8c-a228eb5eab67\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfugaAyse6miu522NTixjT0TCw3hEPXmp_dUIvBKVW2aKprc-xIj6GFbuAz-3rA8Jttc9TTlaG-OUW3SlqBV5ckusfrinZ36ppyxpBZLjHTtyVBGfo9nZQYatX-uaeCSmZXKp2WKh7T03OvnTwq95rFR9kR?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-a3b90fdf-7fff-849c-4f9b-596f092f51e2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJWbmuSX9nIsTBRogJY1ySrK2PxgEbz9-zTAoji_5odP-iKM2BwZHCu0sazJmyBPz1QyTm2nsfRhet0XSu7vzVv8WeITniksDfSkSQvJ8sOTOmymb_Vu7VxjVc5I7KgcpMG7-IBv08ytkotyP05jmwLB8?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-acb9ae26-7fff-999e-f891-9f02622f87db\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXemXu82DqFo2VsYdg73_3b64pG3YXe_3w2m4ynGbfJWgRr5hqOom_qFnzlLn5RlBZ2_RyBw4y7f-_Tb1xw1U_1x8D2hucUAV3Z5BzKaZ3Hrn0efwUbAF3G2723EXUx79cvH2P5_jN0mPQrhXP7LeW8kUFdq?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>", "<p><strong id=\"docs-internal-guid-2cc5cd9b-7fff-af53-f439-bcd4a50f1477\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLLzB0kxowim1QJxaMYD0KDLeknPCj8l0wzLopo_Wpc0WgBphtIIh2IKX0QTMBOeDofSQXhFy2PkVG_DS9A4S2NM7lFDAtEmFXHJz3yJrDZi70lbzDo8d-iIpWkoEYprAv0PJ_ic5dviuCEUGGBXHoXUsm?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>"],
                    solution_en: "<p>24.(b)<br><strong id=\"docs-internal-guid-859064e9-7fff-5cf7-f4ca-31779bc361b0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLLzB0kxowim1QJxaMYD0KDLeknPCj8l0wzLopo_Wpc0WgBphtIIh2IKX0QTMBOeDofSQXhFy2PkVG_DS9A4S2NM7lFDAtEmFXHJz3yJrDZi70lbzDo8d-iIpWkoEYprAv0PJ_ic5dviuCEUGGBXHoXUsm?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>",
                    solution_hi: "<p>24.(b)<br><strong id=\"docs-internal-guid-859064e9-7fff-5cf7-f4ca-31779bc361b0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLLzB0kxowim1QJxaMYD0KDLeknPCj8l0wzLopo_Wpc0WgBphtIIh2IKX0QTMBOeDofSQXhFy2PkVG_DS9A4S2NM7lFDAtEmFXHJz3yJrDZi70lbzDo8d-iIpWkoEYprAv0PJ_ic5dviuCEUGGBXHoXUsm?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"67\" height=\"67\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>JAP : BSH : : NDZ : FVR : : LCX : ?</p>",
                    question_hi: "<p>25. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>JAP : BSH : : NDZ : FVR : : LCX : ?</p>",
                    options_en: ["<p>EVP</p>", "<p>DUP</p>", 
                                "<p>DVP</p>", "<p>EUP</p>"],
                    options_hi: ["<p>EVP</p>", "<p>DUP</p>",
                                "<p>DVP</p>", "<p>EUP</p>"],
                    solution_en: "<p>25.(b)<br><strong id=\"docs-internal-guid-d3b0a0bd-7fff-0793-b185-fba719017ecd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd96uj-MO7Dvlfb4KDufLdDSK5LcC0HYh-yVMqObAZoR-ZD2dUo-2eMbrw4e2P7CLvjw-ykM2XGNy_kKPknZPkwRTyh8RB-NPi2Qsz1V82QDffRoEAN-DFvVNGP3YqIovduNVdT5bzxqwoEbNCmA1Cyoc8?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"120\" height=\"93\"></strong><br><strong id=\"docs-internal-guid-b57c11d3-7fff-1245-a95b-be9fb98d788b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc9ckKITsvrWEBU54B5Wje_Q22a-7VI49lbpub9Q9UrPl8kawr_9kZEyUG2HOBqvOjKPVhOHaVR2fcFupy21DU6W7ZUiwWN7fPzYBBNeXTIdZhxX0GaUxbjqZsN1Tlhwp3LOty9jp0GTb-KdtlIqUtceH98?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"119\" height=\"93\"></strong><br>Similarly<br><strong id=\"docs-internal-guid-dc26bb3a-7fff-1f74-0af3-c2ec863a9692\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfAPCmd1CeP8yKsSHw9HAVtHu86oYQxH4kB6T1mi8tt_Zg8qYrRM28BTwCOQTfI4ARvRDqlxhmWOCNmCHRsWaLl8P219W9tFJ7HbvED29ZcKhwYuIyifD_OOp1MRPktZKQagci9CIRpf3O38f-Qk_5uxYZn?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"119\" height=\"93\"></strong></p>",
                    solution_hi: "<p>25.(b)<br><strong id=\"docs-internal-guid-47482b4d-7fff-3ca8-fcbe-67aca2a676e8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd96uj-MO7Dvlfb4KDufLdDSK5LcC0HYh-yVMqObAZoR-ZD2dUo-2eMbrw4e2P7CLvjw-ykM2XGNy_kKPknZPkwRTyh8RB-NPi2Qsz1V82QDffRoEAN-DFvVNGP3YqIovduNVdT5bzxqwoEbNCmA1Cyoc8?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"120\" height=\"93\"></strong><br><strong id=\"docs-internal-guid-7266b6df-7fff-45db-480f-d4dd6cb4da16\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc9ckKITsvrWEBU54B5Wje_Q22a-7VI49lbpub9Q9UrPl8kawr_9kZEyUG2HOBqvOjKPVhOHaVR2fcFupy21DU6W7ZUiwWN7fPzYBBNeXTIdZhxX0GaUxbjqZsN1Tlhwp3LOty9jp0GTb-KdtlIqUtceH98?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"119\" height=\"93\"></strong><br>इसी तरह<br><strong id=\"docs-internal-guid-e3c38504-7fff-52b4-5f3f-19c6e55c1cc8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfAPCmd1CeP8yKsSHw9HAVtHu86oYQxH4kB6T1mi8tt_Zg8qYrRM28BTwCOQTfI4ARvRDqlxhmWOCNmCHRsWaLl8P219W9tFJ7HbvED29ZcKhwYuIyifD_OOp1MRPktZKQagci9CIRpf3O38f-Qk_5uxYZn?key=NvldBWNMCdOvbLSwHCWxDa4Z\" width=\"119\" height=\"93\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>