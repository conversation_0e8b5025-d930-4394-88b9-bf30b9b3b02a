<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that represents the letters that, when sequentially placed from left to right in the blanks below, will complete the letter series.<br>_ D R G _ Z D R _ B Z D _ G B Z _ R G B</p>",
                    question_hi: "<p>1. उस विकल्प का चयन कीजिए जो उन अक्षरों को दर्शाता है, जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ क्रमिक रूप से रखने पर अक्षर श्रृंखला पूरी हो जाएगी।<br>_ D R G _ Z D R _ B Z D _ G B Z _ R G B</p>",
                    options_en: ["<p>ZBDRD</p>", "<p>BBGRD</p>", 
                                "<p>ZBGRB</p>", "<p>ZBGRD</p>"],
                    options_hi: ["<p>ZBDRD</p>", "<p>BBGRD</p>",
                                "<p>ZBGRB</p>", "<p>ZBGRD</p>"],
                    solution_en: "<p>1.(d) <span style=\"text-decoration: underline;\"><strong>Z</strong></span> D R G <span style=\"text-decoration: underline;\"><strong>B</strong></span>/ Z D R <span style=\"text-decoration: underline;\"><strong>G</strong></span> B/ Z D <span style=\"text-decoration: underline;\"><strong>R</strong></span> G B/ Z <span style=\"text-decoration: underline;\"><strong>D</strong></span> R G B</p>",
                    solution_hi: "<p>1.(d) <span style=\"text-decoration: underline;\"><strong>Z</strong></span> D R G <span style=\"text-decoration: underline;\"><strong>B</strong></span>/ Z D R <span style=\"text-decoration: underline;\"><strong>G</strong></span> B/ Z D <span style=\"text-decoration: underline;\"><strong>R</strong></span> G B/ Z <span style=\"text-decoration: underline;\"><strong>D</strong></span> R G B</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the set in which the numbers are related in the same way as are the numbers of the given sets.<br>(429, 514)<br>(688, 773)<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g., 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>2. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ एक-दूसरे से उसी तरह संबंधित हैं, जैसे दिए गए समुच्चय की संख्याएँ हैं।<br>(429, 514)<br>(688, 773)<br>(ध्यान दीजिए : संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रिया जैसे जोड़ना / हटाना / गुणा करना आदि 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>(396, 491)</p>", "<p>(546, 631)</p>", 
                                "<p>(319, 399)</p>", "<p>(407, 512)</p>"],
                    options_hi: ["<p>(396, 491)</p>", "<p>(546, 631)</p>",
                                "<p>(319, 399)</p>", "<p>(407, 512)</p>"],
                    solution_en: "<p>2.(b) <strong>Logic: </strong>(1st number + 85) = 2nd number<br>(429, 514) :- 429 + 85 = 514<br>(688, 773) :- 688 + 85 = 773<br>Similarly<br>(546, 631) :- 546 + 85 = 631</p>",
                    solution_hi: "<p>2.(b)<strong> तर्क:</strong> (पहली संख्या + 85) = दूसरी संख्या<br>(429, 514) :- 429 + 85 = 514<br>(688, 773) :- 688 + 85 = 773<br>उसी प्रकार<br>(546, 631) :- 546 + 85 = 631</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the term from among the given options that can replace the question mark (?) in the following series.<br>PJ 16, SM 25, VP 36, YS 49, ?</p>",
                    question_hi: "<p>3. दिए गए विकल्पों में से उस पद का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकता है।<br>PJ 16, SM 25, VP 36, YS 49, ?</p>",
                    options_en: ["<p>CW 81</p>", "<p>BV 64</p>", 
                                "<p>DW 64</p>", "<p>CV 36</p>"],
                    options_hi: ["<p>CW 81</p>", "<p>BV 64</p>",
                                "<p>DW 64</p>", "<p>CV 36</p>"],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828495.png\" alt=\"rId4\" width=\"300\" height=\"103\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828495.png\" alt=\"rId4\" width=\"300\" height=\"103\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, &lsquo;BIRTHDAY&rsquo; is written as &lsquo;ADHTRIBY&rsquo; and &lsquo;CONGRESS&rsquo; is written as &lsquo;SERGNOCS&rsquo;. How will &lsquo;DIVISION&rsquo; be written in that language?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में, &lsquo;BIRTHDAY&rsquo; को &lsquo;ADHTRIBY&rsquo; के रूप में लिखा जाता है और &lsquo;CONGRESS&rsquo; को &lsquo;SERGNOCS&rsquo; के रूप में लिखा जाता है। उसी भाषा में &lsquo;DIVISION&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>NDOISIVI</p>", "<p>NOISIVID</p>", 
                                "<p>DNOISIVI</p>", "<p>OISIVIDN</p>"],
                    options_hi: ["<p>NDOISIVI</p>", "<p>NOISIVID</p>",
                                "<p>DNOISIVI</p>", "<p>OISIVIDN</p>"],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828609.png\" alt=\"rId5\" width=\"110\" height=\"83\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828720.png\" alt=\"rId6\" width=\"120\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828822.png\" alt=\"rId7\" width=\"110\"></p>",
                    solution_hi: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828609.png\" alt=\"rId5\" width=\"110\" height=\"83\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828720.png\" alt=\"rId6\" width=\"120\"><br>उसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828822.png\" alt=\"rId7\" width=\"110\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828933.png\" alt=\"rId8\" width=\"300\" height=\"105\"></p>",
                    question_hi: "<p>5. एक कागज को नीचे दिखाए अनुसार मोड़ा और काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123828933.png\" alt=\"rId8\" width=\"300\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829040.png\" alt=\"rId9\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829205.png\" alt=\"rId10\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829303.png\" alt=\"rId11\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829409.png\" alt=\"rId12\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829040.png\" alt=\"rId9\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829205.png\" alt=\"rId10\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829303.png\" alt=\"rId11\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829409.png\" alt=\"rId12\" width=\"90\"></p>"],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829409.png\" alt=\"rId12\" width=\"90\"></p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829409.png\" alt=\"rId12\" width=\"90\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different.<br>Note : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>6. चार अक्षर-समूह दिए गए हैं जिनमें से तीन किसी न किसी रूप में एक समान हैं, और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए।<br>नोट: अक्षर समूह में, असंगत व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: ["<p>BEH</p>", "<p>SVY</p>", 
                                "<p>ILN</p>", "<p>KNQ</p>"],
                    options_hi: ["<p>BEH</p>", "<p>SVY</p>",
                                "<p>ILN</p>", "<p>KNQ</p>"],
                    solution_en: "<p>6.(c<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829532.png\" alt=\"rId13\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829634.png\" alt=\"rId14\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829783.png\" alt=\"rId15\" width=\"100\"><br>But<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829940.png\" alt=\"rId16\" width=\"100\"></p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829532.png\" alt=\"rId13\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829634.png\" alt=\"rId14\" width=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829783.png\" alt=\"rId15\" width=\"100\"><br>लेकिन&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123829940.png\" alt=\"rId16\" width=\"100\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. 189 is related to 21 following a certain logic. Following the same logic, 234 is related to 26. To which of the following is 468 related, following the same logic?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>7. एक निश्चित तर्क का अनुसरण करते हुए 189, 21 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 234, 26 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 468 निम्नलिखित में से किससे संबंधित है?<br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>58</p>", "<p>54</p>", 
                                "<p>52</p>", "<p>48</p>"],
                    options_hi: ["<p>58</p>", "<p>54</p>",
                                "<p>52</p>", "<p>48</p>"],
                    solution_en: "<p>7.(c) <strong>Logic: </strong>1st number &divide; 9 = 2nd number<br>(189 : 21) :- 189 &divide; 9 = 21<br>(234 : 26) :- 234 &divide; 9 = 26<br>Similarly<br>468 : x&nbsp;:- 468 &divide; 9 = 52</p>",
                    solution_hi: "<p>7.(c) <strong>तर्क: </strong>पहली संख्या &divide; 9 = दूसरी संख्या<br>(189 : 21) :- 189 &divide; 9 = 21<br>(234 : 26) :- 234 &divide; 9 = 26<br>उसी प्रकार<br>468 : x&nbsp;:- 468 &divide; 9 = 52</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830058.png\" alt=\"rId17\" width=\"120\"></p>",
                    question_hi: "<p>8. जब दर्पण को MN पर रखा जाता हो तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830058.png\" alt=\"rId17\" width=\"120\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830163.png\" alt=\"rId18\" width=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830267.png\" alt=\"rId19\" width=\"110\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830372.png\" alt=\"rId20\" width=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830505.png\" alt=\"rId21\" width=\"110\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830163.png\" alt=\"rId18\" width=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830267.png\" alt=\"rId19\" width=\"110\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830372.png\" alt=\"rId20\" width=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830505.png\" alt=\"rId21\" width=\"110\"></p>"],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830163.png\" alt=\"rId18\" width=\"110\"></p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830163.png\" alt=\"rId18\" width=\"110\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the figure from among the given options that can replace the question mark (?) and logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830639.png\" alt=\"rId22\" width=\"350\" height=\"82\"></p>",
                    question_hi: "<p>9. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है और शृंखला को तार्किक रूप से पूरा कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830639.png\" alt=\"rId22\" width=\"350\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830754.png\" alt=\"rId23\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830887.png\" alt=\"rId24\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830994.png\" alt=\"rId25\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831096.png\" alt=\"rId26\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830754.png\" alt=\"rId23\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830887.png\" alt=\"rId24\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830994.png\" alt=\"rId25\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831096.png\" alt=\"rId26\" width=\"90\"></p>"],
                    solution_en: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830887.png\" alt=\"rId24\" width=\"90\"></p>",
                    solution_hi: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123830887.png\" alt=\"rId24\" width=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. G is the brother of F. E is the father of K.. C is the daughter of A. F is the mother of E. C is the sister of E. How is G related to A?</p>",
                    question_hi: "<p>10. G, F का भाई है। E, K का पिता है। C, A की पुत्री है। F, E की माता है। C, E की बहन है। G, A से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Wife&rsquo;s brother</p>", "<p>Sister&rsquo;s husband</p>", 
                                "<p>Husband&rsquo;s sister</p>", "<p>Brother&rsquo;s wife</p>"],
                    options_hi: ["<p>साला</p>", "<p>जीजा</p>",
                                "<p>ननद</p>", "<p>भाभी</p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831204.png\" alt=\"rId27\" width=\"150\"><br>&lsquo;G&rsquo; is the brother of A&rsquo;s wife.</p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831204.png\" alt=\"rId27\" width=\"150\"><br>\'G\', \'A\', का साला हैI</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, &lsquo;FLBS&rsquo; is coded as &lsquo;156&rsquo; and &lsquo;MXT&rsquo; is coded as &lsquo;228&rsquo;. How will &lsquo;VDZN&rsquo; be coded in the same language?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में \'FLBS\' को \'156\' और \'MXT\' को \'228\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'VDZN\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>160</p>", "<p>180</p>", 
                                "<p>264</p>", "<p>220</p>"],
                    options_hi: ["<p>160</p>", "<p>180</p>",
                                "<p>264</p>", "<p>220</p>"],
                    solution_en: "<p>11.(c) <strong>Logic:</strong>- (Sum of the place value of letters) &times; 4&nbsp;<br>FLBS :- (6 + 12 + 2 + 19) &times; 4 &rArr; (39) &times; 4 = 156<br>MXT :- (13 + 24 + 20) &times; 4 &rArr; (57) &times; 4 = 228<br>Similarly,<br>VDZN :- (22 + 4 + 26 + 14) &times; 4 &rArr; (66) &times; 4 = 264</p>",
                    solution_hi: "<p>11.(c) <strong>तर्क:- </strong>(अक्षरों के स्थानीय मान का योग) &times; 4<br>FLBS :- (6 + 12 + 2 + 19) &times; 4 &rArr; (39) &times; 4 = 156<br>MXT :- (13 + 24 + 20) &times; 4 &rArr; (57) &times; 4 = 228<br>उसी प्रकार<br>VDZN :- (22 + 4 + 26 + 14) &times; 4 &rArr; (66) &times; 4 = 264</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>101 &divide; 48 &minus; 13013 &times; 13 + 12 = ?</p>",
                    question_hi: "<p>12. यदि \'+\' और \'&minus;\' को आपस में बदल दिया जाए, और \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न-चिन्ह (?) के स्थान पर क्या आयेगा?<br>101 &divide; 48 &minus; 13013 &times; 13 + 12 = ?</p>",
                    options_en: ["<p>5836</p>", "<p>5835</p>", 
                                "<p>5837</p>", "<p>5834</p>"],
                    options_hi: ["<p>5836</p>", "<p>5835</p>",
                                "<p>5837</p>", "<p>5834</p>"],
                    solution_en: "<p>12.(c)<strong> Given:</strong> 101 &divide; 48 &minus; 13013 &times; 13 + 12<br>As per instructions given in the question, after interchanging the symbol &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;+&rsquo; and &lsquo;&minus;&rsquo; we get.<br>101 &times; 48 + 13013 &divide; 13 - 12<br>101 &times; 48 + 1001 - 12<br>4848 + 1001 - 12<br>5849 - 12 = 5837</p>",
                    solution_hi: "<p>12.(c) <strong>दिया गया है:</strong> 101 &divide; 48 &minus; 13013 &times; 13 +12<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीक \'&times;\' और \'&divide;\' तथा \'+\' और \'-\' को आपस में बदलने पर हमें प्राप्त होता है।<br>101 &times; 48 + 13013 &divide; 13 - 12<br>101 &times; 48 + 1001 - 12<br>4848 + 1001 - 12<br>5849 - 12 = 5837</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. A dice has numbers 1, 2, 3, 5, 6 and 8 on its sides. Two different positions of the same dice are shown below. Select the number that will be on the face opposite to the one having 6.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831327.png\" alt=\"rId28\" width=\"150\" height=\"74\"></p>",
                    question_hi: "<p>13. एक पासे के फलकों पर संख्याएँ 1, 2, 3, 5, 6 और 8 अंकित हैं। एक ही पासे की दो अलग-अलग स्थितियाँ नीचे दिखाई गई हैं। वह संख्या चुनिए, जो संख्या 6 वाले फलक के विपरीत फलक पर होगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831327.png\" alt=\"rId28\" width=\"150\" height=\"74\"></p>",
                    options_en: ["<p>8</p>", "<p>3</p>", 
                                "<p>2</p>", "<p>1</p>"],
                    options_hi: ["<p>8</p>", "<p>3</p>",
                                "<p>2</p>", "<p>1</p>"],
                    solution_en: "<p>13.(a) From the both dices opposite faces are<br>5 <math display=\"inline\"><mo>&#8596;</mo></math> 2, 3 &harr; 1, 6 &harr; 8</p>",
                    solution_hi: "<p>13.(a) दोनों पासों के विपरीत फलक हैं<br>5 <math display=\"inline\"><mo>&#8596;</mo></math> 2, 3 &harr; 1, 6 &harr; 8</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Some balls are toys.<br>All circles are balls.<br>All balls are pits.<br><strong>Conclusions :</strong><br>(I) Some circles are toys.<br>(II) All pits are toys.</p>",
                    question_hi: "<p>14. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष, कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>कुछ गेंदें, खिलौने हैं।<br>सभी वृत्त, गेंदें हैं।<br>सभी गेंदें, गड्ढे हैं।<br><strong>निष्कर्ष :</strong><br>(I) कुछ वृत्त, खिलौने हैं।<br>(II) सभी गड्ढे, खिलौने हैं।</p>",
                    options_en: ["<p>Both conclusions (I) and (II) follow.</p>", "<p>Neither conclusion (I) nor (II) follow.</p>", 
                                "<p>Only conclusion (I) follows.</p>", "<p>Only conclusion (II) follows.</p>"],
                    options_hi: ["<p>निष्कर्ष (I) और निष्कर्ष (II), दोनों अनुसरण करते हैं।</p>", "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>",
                                "<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>"],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831438.png\" alt=\"rId29\" width=\"200\" height=\"129\"><br>Neither conclusion (I) nor (II) follow.</p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831552.png\" alt=\"rId30\" width=\"200\"><br>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Which letter-cluster will replace the question mark (?) to complete the given series?<br>BKUZ, ENWB, ?, KTAF, NWCH</p>",
                    question_hi: "<p>15. दी गई शृंखला को पूरा करने के लिए कौन-सा अक्षर-समूह प्रश्न-चिह्न (?) को प्रतिस्थापित करेगा?<br>BKUZ, ENWB, ?, KTAF, NWCH</p>",
                    options_en: ["<p>HQYD</p>", "<p>HRYE</p>", 
                                "<p>HRZD</p>", "<p>HRYD</p>"],
                    options_hi: ["<p>HQYD</p>", "<p>HRYE</p>",
                                "<p>HRZD</p>", "<p>HRYD</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831661.png\" alt=\"rId31\" width=\"350\"></p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831661.png\" alt=\"rId31\" width=\"350\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into<br>1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>16. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है?<br>(ध्यान दें : संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>26 &ndash; 95 &ndash; 21</p>", "<p>22 &ndash; 67 &ndash; 22</p>", 
                                "<p>20 &ndash; 15 &ndash; 51</p>", "<p>16 &ndash; 45 &ndash; 21</p>"],
                    options_hi: ["<p>26 &ndash; 95 &ndash; 21</p>", "<p>22 &ndash; 67 &ndash; 22</p>",
                                "<p>20 &ndash; 15 &ndash; 51</p>", "<p>16 &ndash; 45 &ndash; 21</p>"],
                    solution_en: "<p>16.(b) <strong>Logic:-</strong> The sum of all the numbers in all the options except (b) is even.<br>(a) 26 &ndash; 95 &ndash; 21 <math display=\"inline\"><mo>&#8658;</mo></math> 26 + 95 + 21 = 142<br>(b) 22 &ndash; 67 &ndash; 22 <math display=\"inline\"><mo>&#8658;</mo></math> 22 + 67 + 22 = 111<br>(c) 20 &ndash; 15 &ndash; 51 <math display=\"inline\"><mo>&#8658;</mo></math> 20 + 15 + 51 = 86<br>(d) 16 &ndash; 45 &ndash; 21 <math display=\"inline\"><mo>&#8658;</mo></math> 16 + 45 + 21 = 82</p>",
                    solution_hi: "<p>16.(b) <strong>तर्क:-</strong> (B) को छोड़कर सभी विकल्पों में सभी संख्याओं का योग सम है।<br>(a) 26 &ndash; 95 &ndash; 21 <math display=\"inline\"><mo>&#8658;</mo></math> 26 + 95 + 21 = 142<br>(b) 22 &ndash; 67 &ndash; 22 <math display=\"inline\"><mo>&#8658;</mo></math> 22 + 67 + 22 = 111<br>(c) 20 &ndash; 15 &ndash; 51 <math display=\"inline\"><mo>&#8658;</mo></math> 20 + 15 + 51 = 86<br>(d) 16 &ndash; 45 &ndash; 21 <math display=\"inline\"><mo>&#8658;</mo></math> 16 + 45 + 21 = 82</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "17. ‘Magnify’ is related to ‘Expand’ in the same way as ‘Curtail’ is related to ‘________’.",
                    question_hi: "17. ‘Magnify’ का संबंध ‘Expand’ से उसी प्रकार है जैसे ‘Curtail’ का संबंध \'________\' से है।",
                    options_en: [" Big ", " Measure ", 
                                " Restrict", " Small<br /> "],
                    options_hi: [" Big ", " Measure ",
                                " Restrict", " Small<br /> "],
                    solution_en: "17.(c) ‘Magnify’ and ‘Expand’ are synonyms, similarly ‘curtail’ and ‘restrict’ are synonyms.",
                    solution_hi: "17.(c) ‘Magnify’ और ‘Expand’ पर्यायवाची हैं, इसी प्रकार ‘curtail’ और ‘restrict’ पर्यायवाची हैं।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. The position of how many letters will remain unchanged if each of the letter in the word &lsquo;CERTAIN&rsquo; is arranged in English alphabetical order?</p>",
                    question_hi: "<p>18. यदि शब्द \'CERTAIN\' के प्रत्येक अक्षर को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी?</p>",
                    options_en: ["<p>One</p>", "<p>Zero</p>", 
                                "<p>Two</p>", "<p>Four</p>"],
                    options_hi: ["<p>एक</p>", "<p>शून्य</p>",
                                "<p>दो</p>", "<p>चार</p>"],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831801.png\" alt=\"rId32\" width=\"150\" height=\"83\"><br>All letters will change.</p>",
                    solution_hi: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831801.png\" alt=\"rId32\" width=\"150\" height=\"83\"><br>सभी अक्षर बदल जायेंगे.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In this question, three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All cars are birds.<br>Some birds are plants.<br>All plants are leaves.<br><strong>Conclusions :</strong><br>I. Some birds are cars.<br>II. Some plants are birds.<br>III. Some leaves are plants.</p>",
                    question_hi: "<p>19. इस प्रश्न में, तीन कथन और उसके बाद तीन निष्कर्ष क्रमांक I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, हों तय कीजिए कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन :</strong><br>सभी कारें, पक्षी हैं।<br>कुछ पक्षी, पौधे हैं।<br>सभी पौधे, पत्ते हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ पक्षी, कारें हैं।<br>II. कुछ पौधे, पक्षी हैं।<br>III. कुछ पत्ते, पौधे हैं।</p>",
                    options_en: ["<p>Only conclusions I and III follow.</p>", "<p>None of the conclusions follows.</p>", 
                                "<p>All the conclusions follow.</p>", "<p>Only conclusions II and III follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I और III अनुसरण करते हैं।</p>", "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है।</p>",
                                "<p>सभी निष्कर्ष अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष II और III अनुसरण करते हैं।</p>"],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123831907.png\" alt=\"rId33\" width=\"200\"><br>All the conclusions follow.</p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832078.png\" alt=\"rId34\" width=\"200\"><br>सभी निष्कर्ष अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. A transparent sheet with a pattern is given below. Select the option that depicts the pattern that would appear when the given transparent sheet is folded at the middle horizontal line.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832220.png\" alt=\"rId35\" width=\"100\"></p>",
                    question_hi: "<p>20. एक पैटर्न के साथ एक पारदर्शी शीट नीचे दी गई है। उस विकल्प का चयन कीजिए जो उस पैटर्न को दर्शाता है जो दी गई पारदर्शी शीट को मध्य क्षैतिज रेखा पर मोड़ने पर दिखाई देगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832220.png\" alt=\"rId35\" width=\"100\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832308.png\" alt=\"rId36\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832413.png\" alt=\"rId37\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832520.png\" alt=\"rId38\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832685.png\" alt=\"rId39\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832308.png\" alt=\"rId36\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832413.png\" alt=\"rId37\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832520.png\" alt=\"rId38\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832685.png\" alt=\"rId39\" width=\"90\"></p>"],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832685.png\" alt=\"rId39\" width=\"90\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832685.png\" alt=\"rId39\" width=\"90\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832791.png\" alt=\"rId40\" width=\"350\"></p>",
                    question_hi: "<p>21. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832791.png\" alt=\"rId40\" width=\"350\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832948.png\" alt=\"rId41\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833077.png\" alt=\"rId42\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833177.png\" alt=\"rId43\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833281.png\" alt=\"rId44\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123832948.png\" alt=\"rId41\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833077.png\" alt=\"rId42\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833177.png\" alt=\"rId43\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833281.png\" alt=\"rId44\" width=\"90\"></p>"],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833177.png\" alt=\"rId43\" width=\"90\"></p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833177.png\" alt=\"rId43\" width=\"90\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; are interchanged?<br>4 + 9 &times; 48 &ndash; 6 &divide; 7 = ?</p>",
                    question_hi: "<p>22. निम्नलिखित समीकरण में, यदि \'&divide;\' और \'&ndash;\' को आपस में बदल दिया जाए तथा \'&times;\' और \'+\' को आपस में बदल दिया जाए, तो \'?\' के स्थान पर क्या आएगा?<br>4 + 9 &times; 48 &ndash; 6 &divide; 7 = ?</p>",
                    options_en: ["<p>30</p>", "<p>27</p>", 
                                "<p>41</p>", "<p>37</p>"],
                    options_hi: ["<p>30</p>", "<p>27</p>",
                                "<p>41</p>", "<p>37</p>"],
                    solution_en: "<p>22.(d) <strong>Given: </strong>4 + 9 &times; 48 &ndash; 6 &divide; 7 = ?<br>As per the instruction given in the question, after interchanging the symbols &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo; and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; we get.<br>4 &times; 9 + 48 &divide; 6 - 7 = ? <br>4 &times; 9 + 8 - 7<br>36 + 8 - 7<br>44 - 7 = 37</p>",
                    solution_hi: "<p>22.(d) <strong>दिया गया है:</strong> 4 + 9 &times; 48 &ndash; 6 &divide; 7 = ?<br>प्रश्न में दिए गए निर्देश के अनुसार, प्रतीकों \'&divide;\' और \'-\' तथा \'&times;\' और \'+\' को आपस में बदलने पर हमें प्राप्त होता है।<br>4 &times; 9 + 48 &divide; 6 - 7 = ? <br>4 &times; 9 + 8 - 7<br>36 + 8 - 7<br>44 - 7 = 37</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833387.png\" alt=\"rId45\" width=\"80\"></p>",
                    question_hi: "<p>23. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833387.png\" alt=\"rId45\" width=\"80\"></p>",
                    options_en: ["<p>6</p>", "<p>4</p>", 
                                "<p>2</p>", "<p>1</p>"],
                    options_hi: ["<p>6</p>", "<p>4</p>",
                                "<p>2</p>", "<p>1</p>"],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833551.png\" alt=\"rId46\" width=\"80\"><br>ABE, ACF, AIE, EDF, EGF, AHF <br>There are 6 triangles.</p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833551.png\" alt=\"rId46\" width=\"80\"><br>ABE, ACF, AIE, EDF, EGF, AHF <br>6 त्रिभुज हैं.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. 30 is related to 41 following certain logic. Following the same logic, 14 is related to 21. To which of the following numbers is 54 related, following the same logic? <br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>24. किसी निश्चित तर्क के अनुसार 30 का संबंध 41 से है। उसी तर्क के अनुसार 14 का संबंध 21 से है। उसी तर्क के अनुसार 54 निम्नलिखित में से किस संख्या से संबंधित है?<br>नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>63</p>", "<p>53</p>", 
                                "<p>59</p>", "<p>69</p>"],
                    options_hi: ["<p>63</p>", "<p>53</p>",
                                "<p>59</p>", "<p>69</p>"],
                    solution_en: "<p>24.(d)<br>(30 : 41) :- 5&sup2; + 5 : 6&sup2; + 5 = 30 : 41<br>(14 : 21) :- 3&sup2; + 5 : 4&sup2; + 5 <math display=\"inline\"><mo>=</mo></math> 14 : 21<br>Similarly<br>(54 : x) :- 7&sup2; +5 : 8&sup2; + 5 = 54 : 69</p>",
                    solution_hi: "<p>24.(d)<br>(30 : 41) :- 5&sup2; + 5 : 6&sup2; + 5 = 30 : 41<br>(14 : 21) :- 3&sup2; + 5 : 4&sup2; + 5 <math display=\"inline\"><mo>=</mo></math> 14 : 21<br>इसी प्रकार <br>(54 : <math display=\"inline\"><mi>x</mi></math>) :- 7&sup2; +5 : 8&sup2; + 5 = 54 : 69</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the number from among the given options that can replace the question mark (?) in the following series.<br>96, 77, 60, 47, 36, ?</p>",
                    question_hi: "<p>25. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>96, 77, 60, 47, 36, ?</p>",
                    options_en: ["<p>30</p>", "<p>29</p>", 
                                "<p>28</p>", "<p>32</p>"],
                    options_hi: ["<p>30</p>", "<p>29</p>",
                                "<p>28</p>", "<p>32</p>"],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833661.png\" alt=\"rId47\" width=\"300\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833661.png\" alt=\"rId47\" width=\"300\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Identify the Indian classical dancer associated with Kathak.</p>",
                    question_hi: "<p>26. कथक से संबंधित भारतीय शास्त्रीय नर्तक/नर्तकी की पहचान कीजिए।</p>",
                    options_en: ["<p>Vedantam Satyanarayana Sarma</p>", "<p>Kelucharan Mohapatra</p>", 
                                "<p>Rohini Bhate</p>", "<p>Sharodi Saikia</p>"],
                    options_hi: ["<p>वेदांतम सत्यनारायण सरमा</p>", "<p>केलुचरण महापात्र</p>",
                                "<p>रोहिणी भाटे</p>", "<p>शारोदी सैकिया</p>"],
                    solution_en: "<p>26.(c) <strong>Rohini Bhate.</strong> She received the Sangeet Natak Akademi Award in 1979. Other Exponents : Kathak (Uttar Pradesh) - Pandit Birju Maharaj, Sonal Mansingh, Shambhu Maharaj. Kuchipudi (Andhra Pradesh) - Vedantam Satyanarayana Sarma, Mallika Sarabhai, Bhavana Reddy. Odissi (Odisha) - Kelucharan Mohapatra, Sanjukta Panigrahi, Gangadhar Pradhan. Sattriya (Assam) - Sharodi Saikia, Guru Jatin Goswami.</p>",
                    solution_hi: "<p>26.(c) <strong>रोहिणी भाटे</strong>। उन्हें 1979 में संगीत नाटक अकादमी पुरस्कार मिला। अन्य प्रतिपादक: कथक (उत्तर प्रदेश) - पंडित बिरजू महाराज, सोनल मानसिंह, शंभू महाराज। कुचिपुड़ी (आंध्र प्रदेश) - वेदांतम सत्यनारायण शर्मा, मल्लिका साराभाई, भावना रेड्डी। ओडिसी (ओडिशा) - केलुचरण महापात्र, संजुक्ता पाणिग्रही, गंगाधर प्रधान। सत्रिया (असम) - शारोदी सैकिया, गुरु जतिन गोस्वामी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. The British Parliament introduced the Indian High Courts Act in which year?</p>",
                    question_hi: "<p>27. ब्रिटिश संसद ने किस वर्ष भारतीय उच्च न्यायालय अधिनियम पेश किया?</p>",
                    options_en: ["<p>1860</p>", "<p>1861</p>", 
                                "<p>1859</p>", "<p>1858</p>"],
                    options_hi: ["<p>1860</p>", "<p>1861</p>",
                                "<p>1859</p>", "<p>1858</p>"],
                    solution_en: "<p>27.(b) <strong>1861</strong>. The India High Courts Act 1861 was enacted to create High Courts for various provinces and abolished Supreme Courts at Calcutta, Madras and Bombay and also the Sadar Adalats in Presidency towns. The first High Court was established in Calcutta on July 1, 1862. The Supreme Court of India was established by the Regulating Act of 1773.</p>",
                    solution_hi: "<p>27.(b) <strong>1861</strong>. भारत उच्च न्यायालय अधिनियम 1861 को विभिन्न प्रांतों के लिए उच्च न्यायालय बनाने के लिए अधिनियमित किया गया था तथा कलकत्ता, मद्रास और बॉम्बे में सर्वोच्च न्यायालयों और प्रेसीडेंसी शहरों में सदर अदालतों को समाप्त कर दिया गया। प्रथम उच्च न्यायालय 1 जुलाई, 1862 को कलकत्ता में स्थापित किया गया था। भारत के सर्वोच्च न्यायालय की स्थापना 1773 के रेगुलेटिंग एक्ट द्वारा की गई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following is an example of private sector industry in India?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन-सा भारत में निजी क्षेत्र के उद्योग का एक उदाहरण है?</p>",
                    options_en: ["<p>Steel Authority of India Limited</p>", "<p>Tata Iron and Steel Company</p>", 
                                "<p>Oil India Limited</p>", "<p>Bharat Heavy Electricals Limited</p>"],
                    options_hi: ["<p>स्टील अथॉरिटी ऑफ इंडिया लिमिटेड</p>", "<p>टाटा आयरन एंड स्टील कंपनी</p>",
                                "<p>ऑयल इंडिया लिमिटेड</p>", "<p>भारत हेवी इलेक्ट्रिकल्स लिमिटेड</p>"],
                    solution_en: "<p>28.(b) <strong>Tata Iron and Steel Company</strong> (TISCO). The private sector is the part of the economy not run by the government. It comprises the businesses and enterprises that are controlled by private individuals and groups for the purpose of making a profit. Jamsetji Tata founded TISCO on August 26, 1907 in Jamshedpur, Jharkhand. Headquarters - Mumbai (Maharashtra). In 2005, TISCO changed its name to Tata Steel Ltd.</p>",
                    solution_hi: "<p>28.(b) <strong>टाटा आयरन एंड स्टील कंपनी </strong>(TISCO)। निजी क्षेत्र अर्थव्यवस्था का वह हिस्सा है जिसे सरकार द्वारा नहीं चलाया जाता है। इसमें वे व्यवसाय और उद्यम शामिल हैं जिन्हें लाभ कमाने के उद्देश्य से निजी व्यक्तियों और समूहों द्वारा नियंत्रित किये जाता है। जमशेदजी टाटा ने 26 अगस्त, 1907 को झारखंड के जमशेदपुर में TISCO की स्थापना की। मुख्यालय - मुंबई (महाराष्ट्र)। 2005 में, TISCO ने अपना नाम बदलकर टाटा स्टील लिमिटेड कर लिया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. In which of the following years was the Election Commission of India founded?</p>",
                    question_hi: "<p>29. निम्नलिखित में से किस वर्ष में भारत के चुनाव आयोग की स्थापना की गई थी?</p>",
                    options_en: ["<p>1955</p>", "<p>1952</p>", 
                                "<p>1950</p>", "<p>1947</p>"],
                    options_hi: ["<p>1955</p>", "<p>1952</p>",
                                "<p>1950</p>", "<p>1947</p>"],
                    solution_en: "<p>29.(c) <strong>1950</strong>. Election Commission of India (ECI) is a permanent Constitutional Body. The Commissioners have a fixed tenure of six years, or up to the age of 65 years, whichever is earlier. Article 324 : Superintendence, direction and control of elections to be vested in an Election Commission. Article 326 : Elections to the House of the People and to the Legislative Assemblies of States to be based on adult suffrage. Article 327 : Power of Parliament to make provision with respect to elections to Legislatures. Article 329 : Bar to interference by courts in electoral matters.</p>",
                    solution_hi: "<p>29.(c) <strong>1950</strong>. भारतीय चुनाव आयोग (ECI) एक स्थायी संवैधानिक निकाय है। आयुक्तों का कार्यकाल छ: वर्ष या 65 वर्ष की आयु तक, जो भी पहले हो, निर्धारित होता है। अनुच्छेद 324 : चुनावों का संचालन, निर्देशन और नियंत्रण चुनाव आयोग में निहित होगा। अनुच्छेद 326 : लोक सभा और राज्यों की विधानसभाओं के चुनाव वयस्क मताधिकार के आधार पर होंगे। अनुच्छेद 327 : विधानमंडलों के चुनावों के संबंध में प्रावधान करने की संसद की शक्ति। अनुच्छेद 329 : चुनावी मामलों में न्यायालयों द्वारा हस्तक्षेप पर रोक।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. In Pradhan Mantri Kisan Samman Nidhi Yojana, beneficiaries are provided with how much amount every year?</p>",
                    question_hi: "<p>30. प्रधानमंत्री किसान सम्मान निधि योजना में लाभार्थियों को हर वर्ष कितनी राशि प्रदान की जाती है?</p>",
                    options_en: ["<p>₹6,000</p>", "<p>₹4,000</p>", 
                                "<p>₹5,000</p>", "<p>₹3,000</p>"],
                    options_hi: ["<p>₹6,000</p>", "<p>₹4,000</p>",
                                "<p>₹5,000</p>", "<p>₹3,000</p>"],
                    solution_en: "<p>30.(a) <strong>₹6,000</strong> - per year in three equal installments, every four months is transferred into the bank accounts of farmers&rsquo; families across the country through Direct Benefit Transfer (DBT) mode. Implemented by the Ministry of Agriculture and Farmers Welfare. Pradhan Mantri Kisan Samman Nidhi Yojana launched on 24th February, 2019.</p>",
                    solution_hi: "<p>30.(a) <strong>₹6,000 -</strong> हर वर्ष तीन बराबर किस्तों में, हर चार महीने में प्रत्यक्ष लाभ अंतरण (DBT) मोड के माध्यम से देश भर के किसान परिवारों के बैंक खातों में धनराशि हस्तांतरित की जाती है। कृषि एवं किसान कल्याण मंत्रालय द्वारा लागू किया गया है। प्रधानमंत्री किसान सम्मान निधि योजना 24 फरवरी, 2019 को शुरू की गई थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. When was the pro kabaddi league inaugurated for the first time in India?</p>",
                    question_hi: "<p>31. भारत में प्रो कबड्डी लीग की शुरुआत पहली बार कब की गई?</p>",
                    options_en: ["<p>2007</p>", "<p>2008</p>", 
                                "<p>2015</p>", "<p>2014</p>"],
                    options_hi: ["<p>2007 में</p>", "<p>2008 में</p>",
                                "<p>2015 में</p>", "<p>2014 में</p>"],
                    solution_en: "<p>31.(d) <strong>2014</strong>. The duration of the match is divided into two halves of 20 minutes each. The Kabaddi court measures 13 &times; 10 meters for men and 12 &times; 8 meters for women. The Women Kabaddi Challenge was first played in 2016. The 2019 Kabaddi World Cup was held in Malaysia. It is the largest World Cup in the history of kabaddi, consisting of 32 men&rsquo;s teams and 24 women&rsquo;s teams.</p>",
                    solution_hi: "<p>31.(d) <strong>2014 में</strong>। मैच की अवधि 20-20 मिनट के दो हिस्सों में विभाजित होती है। कबड्डी कोर्ट पुरुषों के लिए 13 &times; 10 मीटर और महिलाओं के लिए 12 &times; 8 मीटर का होता है। महिला कबड्डी चैलेंज पहली बार 2016 में खेला गया था। 2019 कबड्डी विश्व कप मलेशिया में आयोजित किया गया था। यह कबड्डी के इतिहास का सबसे बड़ा विश्व कप था, जिसमें 32 पुरुष टीमें और 24 महिला टीमें शामिल थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Wood&rsquo;s Dispatch of 1854 primarily dealt with which of the following subject matters?</p>",
                    question_hi: "<p>32. वुड का घोषणा-पत्र, 1854 मुख्य रूप से निम्नलिखित में से किस विषय से संबंधित था?</p>",
                    options_en: ["<p>Education</p>", "<p>Railways</p>", 
                                "<p>Finance</p>", "<p>Health</p>"],
                    options_hi: ["<p>शिक्षा</p>", "<p>रेलवे</p>",
                                "<p>वित्त</p>", "<p>स्वास्थ्य</p>"],
                    solution_en: "<p>32.(a) <strong>Education</strong>. The Wood\'s Despatch of 1854 was a formal letter sent by Sir Charles Wood to Lord Dalhousie, the Governor-General of India, that outlined a plan for educational reform in India. It recommended that primary schools use vernacular languages, secondary schools use both English and vernacular languages, and colleges use English. It is often referred to as the &ldquo;Magna Carta of English Education in India.&rdquo;</p>",
                    solution_hi: "<p>32.(a) <strong>शिक्षा</strong>। 1854 में वुड का घोषणा-पत्र सर चार्ल्स वुड द्वारा भारत के गवर्नर-जनरल लॉर्ड डलहौजी को भेजा गया एक औपचारिक पत्र था, जिसमें भारत में शैक्षिक सुधार की योजना की रूपरेखा दी गई थी। इसने सिफारिश की कि प्राथमिक विद्यालयों में स्थानीय भाषाओं का उपयोग किया जाए, माध्यमिक विद्यालयों में अंग्रेजी और स्थानीय भाषाओं दोनों का उपयोग किया जाए, और कॉलेजों में अंग्रेजी का उपयोग किया जाए। इसे अक्सर \"भारत में अंग्रेजी शिक्षा का मैग्ना कार्टा\" कहा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Who among the following Indian women boxers was elected as the Chairperson and a voting member of the International Boxing Association in 2022?</p>",
                    question_hi: "<p>33. निम्नलिखित भारतीय महिला मुक्केबाजों में से किसे 2022 में अंतरराष्ट्रीय मुक्केबाजी संघ की अध्यक्ष और एक मतदान सदस्य के रूप में चुना गया था?</p>",
                    options_en: ["<p>Pooja Rani</p>", "<p>Mary Kom</p>", 
                                "<p>Nikhat Zareen</p>", "<p>Lovlina Borgohain</p>"],
                    options_hi: ["<p>पूजा रानी</p>", "<p>मैरी कॉम</p>",
                                "<p>निकहत ज़रीन</p>", "<p>लवलीना बोरगोहेन</p>"],
                    solution_en: "<p>33.(d) <strong>Lovlina Borgohain. </strong>She won a bronze medal at the 2020 Olympic Games in the women\'s welterweight event, becoming only the third Indian boxer to win a medal at the Olympics. Mary Kom is the only female to make a six times winning record of World Amateur Boxing Championship and the only female boxer to win a medal in each of the total seven world championships. Nikhat Zareen is a two-time gold medallist at the world championships.</p>",
                    solution_hi: "<p>33.(d) <strong>लवलीना बोरगोहेन</strong>। उन्होंने 2020 ओलंपिक खेलों में महिला वेल्टरवेट स्पर्धा में कांस्य पदक जीता, जिससे वह ओलंपिक में पदक जीतने वाली केवल तीसरी भारतीय मुक्केबाज बन गईं। मैरी कॉम विश्व एमेच्योर मुक्केबाजी चैंपियनशिप में छ: बार जीतने का रिकॉर्ड बनाने वाली एकमात्र महिला हैं और कुल सात विश्व चैंपियनशिप में से प्रत्येक में पदक जीतने वाली एकमात्र महिला मुक्केबाज हैं। निकहत ज़रीन विश्व चैंपियनशिप में दो बार स्वर्ण पदक विजेता हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. In which year was the MPLADS scheme implemented?</p>",
                    question_hi: "<p>34. MPLADS योजना किस वर्ष लागू की गई थी?</p>",
                    options_en: ["<p>2003</p>", "<p>1983</p>", 
                                "<p>1993</p>", "<p>2013</p>"],
                    options_hi: ["<p>2003</p>", "<p>1983</p>",
                                "<p>1993</p>", "<p>2013</p>"],
                    solution_en: "<p>34.(c) <strong>1993</strong>. Objective : The Member of Parliament Local Area Development Scheme (MPLADS) aims to empower MPs to recommend developmental projects that focus on creating durable community assets in areas such as drinking water, primary education, public health, sanitation, and road infrastructure, primarily within their constituencies. Each year, MPs receive ₹5 crore in two installments of ₹2.5 crore each. Notably, the funds allocated under MPLADS are non-lapsable.</p>",
                    solution_hi: "<p>34.(c) <strong>1993</strong>. उद्देश्य: संसद सदस्य स्थानीय क्षेत्र विकास योजना (MPLADS) का उद्देश्य सांसदों को विकास परियोजनाओं की सिफारिश करने के लिए सशक्त बनाना है, जो मुख्य रूप से उनके निर्वाचन क्षेत्रों में पेयजल, प्राथमिक शिक्षा, सार्वजनिक स्वास्थ्य, स्वच्छता और सड़क बुनियादी ढांचे जैसे क्षेत्रों में टिकाऊ सामुदायिक संपत्ति बनाने पर ध्यान केंद्रित करती हैं। प्रत्येक वर्ष, सांसदों को 2.5 करोड़ रुपये की दो किस्तों में 5 करोड़ रुपये मिलते हैं। उल्लेखनीय है कि MPLADS के तहत आवंटित धनराशि लैप्स नहीं होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The tropical cyclones in Australia are known as ________.</p>",
                    question_hi: "<p>35. ऑस्ट्रेलिया में उष्णकटिबंधीय चक्रवातों को _______ के रूप में जाना जाता है।</p>",
                    options_en: ["<p>hurricanes</p>", "<p>willy-willies</p>", 
                                "<p>cyclones</p>", "<p>typhoons</p>"],
                    options_hi: ["<p>हरिकेन</p>", "<p>विली-विलीज़</p>",
                                "<p>साइक्&zwj;लोन</p>", "<p>टाइफ़ून</p>"],
                    solution_en: "<p>35.(b)<strong> willy-willies.</strong> Tropical cyclones are intense low-pressure areas that form in the atmosphere between 30&deg; N and 30&deg; S latitudes. They can extend up to 1,000 km horizontally and 12&ndash;14 km vertically from the surface. Tropical cyclones rotate counterclockwise in the Northern Hemisphere and clockwise in the Southern Hemisphere. Their names in different countries : Hurricanes - United States, Typhoons - China Sea and Pacific Ocean, Cyclones - Indian Ocean.</p>",
                    solution_hi: "<p>35.(b) <strong>विली-विलीज़</strong>। उष्णकटिबंधीय चक्रवात तीव्र निम्न दाब वाले क्षेत्र में आते हैं जो 30&deg; N और 30&deg; S अक्षांशों के बीच वायुमंडल में होते हैं। वे सतह से क्षैतिज रूप से 1,000 किमी और ऊर्ध्वाधर रूप से 12-14 किमी तक फैल सकते हैं। उष्णकटिबंधीय चक्रवात उत्तरी गोलार्ध में वामावर्त और दक्षिणी गोलार्ध में दक्षिणावर्त घूमते हैं। विभिन्न देशों में उनके नाम: तूफान - संयुक्त राज्य अमेरिका, टाइफून - चीन सागर और प्रशांत महासागर, चक्रवात - हिंद महासागर।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following pairs is INCORRECTLY matched?.</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन-सा युग्म गलत सुमेलित है?</p>",
                    options_en: ["<p>Lactose: Milk</p>", "<p>Starch: Egg Yolk</p>", 
                                "<p>Fructose: Grapes</p>", "<p>Maltose: Wheat, cornmeal and barley</p>"],
                    options_hi: ["<p>लैक्टोज: दूध</p>", "<p>स्टार्च: अंडे की जर्द</p>",
                                "<p>फ्रक्टोज: अंगूर</p>", "<p>माल्टोज: गेहूँ, कॉर्नमील और जौ</p>"],
                    solution_en: "<p>36.(b) <strong>Starch </strong>: <strong>Egg Yolk</strong>. Starch is the main storage polysaccharide of plants. It is the most important dietary source for human beings. High content of starch is found in cereals, roots, tubers and some vegetables but not in egg yolk. It is a polymer of &alpha;-glucose and consists of two components&mdash; Amylose and Amylopectin.</p>",
                    solution_hi: "<p>36.(b) <strong>स्टार्च</strong>: <strong>अंडे की जर्द</strong>। स्टार्च पौधों का मुख्य भंडारण पॉलीसैकेराइड है। यह मनुष्यों के लिए सबसे महत्वपूर्ण आहार स्रोत है। स्टार्च की उच्च मात्रा अनाज, जड़ों, कंद और कुछ सब्जियों में पाई जाती है, लेकिन अंडे की जर्द में नहीं। यह &alpha;-ग्लूकोज का बहुलक है और इसमें दो घटक होते हैं- एमाइलोज और एमाइलोपेक्टिन।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Match the following items.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833846.png\" alt=\"rId48\" width=\"420\" height=\"134\"></p>",
                    question_hi: "<p>37. निम्नलिखित मदों का मिलान कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123833961.png\" alt=\"rId49\" width=\"420\" height=\"143\"></p>",
                    options_en: ["<p>i (d), ii (a), iii (b), iv (c)</p>", "<p>i (b), ii (a), iii (d), iv (c)</p>", 
                                "<p>i (c), ii (d), iii (a), iv (b)</p>", "<p>i (b), ii (c), iii (d), iv (a) </p>"],
                    options_hi: ["<p>i (d), ii (a), iii (b), iv (c)</p>", "<p>i (b), ii (a), iii (d), iv (c)</p>",
                                "<p>i (c), ii (d), iii (a), iv (b)</p>", "<p>i (b), ii (c), iii (d), iv (a) </p>"],
                    solution_en: "<p>37.(d) <strong>i (b), ii (c), iii (d), iv (a)</strong>. Global System of Trade Preferences (1988) - Promotes trade among developing countries. India-Gulf Cooperation Council Free Trade Agreement (2004) - Aims to liberalize trade relations and explore a Free Trade Agreement. India-Republic of Korea Comprehensive Economic Partnership Agreement (2010) - Enhances economic cooperation by reducing tariffs on goods. 7th Trade Policy Review (2021) - A WTO mechanism for regularly reviewing member countries\' trade policies.</p>",
                    solution_hi: "<p>37.(d) <strong>i (b), ii (c), iii (d), iv (a)</strong>. व्यापार प्राथमिकताओं की वैश्विक प्रणाली (1988) - विकासशील देशों के बीच व्यापार को बढ़ावा देती है। भारत-खाड़ी सहयोग परिषद मुक्त व्यापार समझौता (2004) - इसका उद्देश्य व्यापार संबंधों को उदार बनाना और मुक्त व्यापार समझौते की संभावना तलाशना है। भारत-कोरिया गणराज्य व्यापक आर्थिक भागीदारी समझौता (2010) - वस्तुओं पर शुल्क कम करके आर्थिक सहयोग को बढ़ाना है। 7वीं व्यापार नीति समीक्षा (2021) - सदस्य देशों की व्यापार नीतियों की नियमित समीक्षा के लिए एक WTO क्रियाविधि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Prabha Atre, who received the Padma Vibhushan Award 2022, belongs to which Gharana?</p>",
                    question_hi: "<p>38. पद्म विभूषण पुरस्कार 2022 प्राप्त करने वाली प्रभा अत्रे किस घराने से संबंधित हैं?</p>",
                    options_en: ["<p>Indore gharana</p>", "<p>Kirana gharana</p>", 
                                "<p>Agra gharana</p>", "<p>Mewati gharana</p>"],
                    options_hi: ["<p>इंदौर घराना</p>", "<p>किराना घराना</p>",
                                "<p>आगरा घराना</p>", "<p>मेवाती घराना</p>"],
                    solution_en: "<p>38.(b) <strong>Kirana gharana</strong>. It was founded by Ustad Abdul Karim Khan. Famous artists such as Abdul Wahid Khan, Suresh Babu Mane, Hira Bai Badodekar and Roshanara Begum belong to this Gharana. Awards of Prabha Atre : Sangeet Natak Akademi Award (1991) for her contribution to Hindustani vocal music. Gharana and their Founders : Indore gharana (Amir Khan), Agra gharana (Hajisujan Khan), Mewati gharana (Ustad Wahid Khan and Ustad Ghagge Nazir Khan), Jaipur Atrauli (Alladiya Khan).</p>",
                    solution_hi: "<p>38.(b) <strong>किराना घराना।</strong> इसकी स्थापना उस्ताद अब्दुल करीम खान ने की थी। अब्दुल वाहिद खान, सुरेश बाबू माने, हीरा बाई बडोडेकर और रोशनआरा बेगम जैसे प्रसिद्ध कलाकार इसी घराने से संबंध रखते हैं। प्रभा अत्रे के पुरस्कार: हिंदुस्तानी गायन संगीत में उनके योगदान के लिए संगीत नाटक अकादमी पुरस्कार (1991)। घराना एवं उनके संस्थापक: इंदौर घराना (अमीर खान), आगरा घराना (हाजीसुजान खान), मेवाती घराना (उस्ताद वाहिद खान और उस्ताद घग्गे नजीर खान), जयपुर अतरौली (अल्लादिया खान)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. An inscription, Takht-i-Bahi recovered from Mardan near Peshawar, indicates the rule of which of the following dynasties in north-western area of present Pakistan?</p>",
                    question_hi: "<p>39. पेशावर के पास मर्दन से बरामद तख्त-ए-बही शिलालेख, वर्तमान पाकिस्तान के उत्तर-पश्चिमी क्षेत्र में निम्नलिखित में से किस राजवंश के शासन का वर्णन करता है?</p>",
                    options_en: ["<p>Bactrian</p>", "<p>Sakas</p>", 
                                "<p>Indo-Greek</p>", "<p>Parthians</p>"],
                    options_hi: ["<p>बैक्ट्रियन (Bactrian)</p>", "<p>शक (Sakas)</p>",
                                "<p>हिंद-यूनानी (Indo-Greek)</p>", "<p>पार्थियन (Parthians)</p>"],
                    solution_en: "<p>39.(d) <strong>Parthians </strong>(247 BC - 224 AD). It was founded by Arsaces I of Parthia when he rebelled against the Seleucid Empire. They replaced the Sakas in North-Western India. The Takht-i-Bahi inscription, dated in 45 AD, refers to Gondophernes as a Parthian ruler.</p>",
                    solution_hi: "<p>39.(d) <strong>पार्थियन </strong>(247 ई.पू. - 224 ई.)। इसकी स्थापना पार्थिया के अर्सेस प्रथम ने की थी, जब उसने सेल्यूसिड साम्राज्य के खिलाफ विद्रोह किया था। उन्होंने उत्तर-पश्चिमी भारत में शकों की जगह ली। 45 ईस्वी के तख्त-ए-बही शिलालेख में गोंडोफर्नेस को पार्थियन शासक के रूप में संदर्भित किया गया है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. According to Census of India 2011, which Union Territory has the highest slum population?</p>",
                    question_hi: "<p>40 भारत की जनगणना 2011 के अनुसार, किस केंद्र शासित प्रदेश में झुग्गियों में रहने वाली आबादी सबसे अधिक है?</p>",
                    options_en: ["<p>Andaman and Nicobar Islands</p>", "<p>Chandigarh</p>", 
                                "<p>Puducherry</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>अंडमान और निकोबार द्वीपसमूह</p>", "<p>चंडीगढ़</p>",
                                "<p>पुदुचेरी</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>40.(d) <strong>Delhi</strong>. Slum Population - Census 2011 : India - 5.41%. Maharashtra (10.54%) has the highest slum population in India. The Dharavi slum in Mumbai is considered one of the most educated slums in India, with an estimated literacy rate of 69%.</p>",
                    solution_hi: "<p>40.(d) <strong>दिल्ली</strong>। झुग्गी-झोपड़ियों की आबादी - जनगणना 2011 : भारत - 5.41%। महाराष्ट्र (10.54%) में भारत की सबसे ज़्यादा झुग्गी-झोपड़ियाँ हैं। मुंबई में धारावी झुग्गी-झोपड़ी को भारत की सबसे शिक्षित झुग्गियों में से एक माना जाता है, जिसकी अनुमानित साक्षरता दर 69% है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. If GNP is 16% higher than the last year and the rate of inflation is 9%, production in the economy has grown by :</p>",
                    question_hi: "<p>41. यदि जीएनपी पिछले वर्ष की तुलना में 16% अधिक है और मुद्रास्फीति की दर 9% है, तो अर्थव्यवस्था में उत्पादन में ______ की वृद्धि हुई है।</p>",
                    options_en: ["<p>4%</p>", "<p>6%</p>", 
                                "<p>5%</p>", "<p>7%</p>"],
                    options_hi: ["<p>4%</p>", "<p>6%</p>",
                                "<p>5%</p>", "<p>7%</p>"],
                    solution_en: "<p>41.(d)<strong> 7%</strong>. To determine the real growth in production, we need to adjust the increase in Gross National Product (GNP) for inflation. The real growth rate can be approximated by subtracting the inflation rate from the nominal growth rate. Real Growth Rate = Nominal Growth Rate - Inflation Rate (16% - 9% = 7%).</p>",
                    solution_hi: "<p>41.(d) <strong>7%</strong>. उत्पादन में वास्तविक वृद्धि का निर्धारण करने के लिए, हमें मुद्रास्फीति के लिए सकल राष्ट्रीय उत्पाद (GNP) में वृद्धि को समायोजित करने की आवश्यकता है। वास्तविक विकास दर को सांकेतिक विकास दर से मुद्रास्फीति दर घटाकर अनुमानित किया जा सकता है। वास्तविक विकास दर = सांकेतिक विकास दर - मुद्रास्फीति दर (16% - 9% = 7%)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Bhupen Hazarika was one of the most well-known personalities from the state of ________.</p>",
                    question_hi: "<p>42. भूपेन हजारिका ________ राज्य के सबसे प्रसिद्ध व्यक्तित्वों में से एक थे।</p>",
                    options_en: ["<p>Odisha</p>", "<p>Assam</p>", 
                                "<p>Nagaland</p>", "<p>Manipur</p>"],
                    options_hi: ["<p>ओड़िशा</p>", "<p>असम</p>",
                                "<p>नागालैंड</p>", "<p>मणिपुर</p>"],
                    solution_en: "<p>42.(b) <strong>Assam</strong>. Bhupen Hazarika was a singer known for his baritone voice. He received the National Film Award for Best Music Direction in 1975, the Sangeet Natak Akademi Award (1987), Bharat Ratna (2019), Padma Vibhushan (2012), Padma Bhushan (2001), and Padma Shri (1977), Dada Saheb Phalke Award (1992), Asom Ratna (2009).</p>",
                    solution_hi: "<p>42.(b) <strong>असम</strong>। भूपेन हजारिका एक गायक थे जो अपनी बैरिटोन वॉइस (मध्यम सुर का गायक) के लिए जाने जाते थे। उन्हें 1975 में सर्वश्रेष्ठ संगीत निर्देशन के लिए राष्ट्रीय फ़िल्म पुरस्कार, संगीत नाटक अकादमी पुरस्कार (1987), भारत रत्न (2019), पद्म विभूषण (2012), पद्म भूषण (2001), और पद्म श्री (1977), दादा साहब फाल्के पुरस्कार (1992), असम रत्न (2009) से सम्मानित किया गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "43. Calorimeter is a small container made of a thin sheet of _______ with good thermal conductivity. ",
                    question_hi: "43. कैलोरीमीटर अच्छी तापीय चालकता वाली _______ की पतली शीट से बना एक छोटा कंटेनर है। ",
                    options_en: [" silver", " platinum", 
                                " copper", " cesium"],
                    options_hi: [" चाँदी", " प्लैटिनम",
                                " ताँबें", " सीज़ियम<br /> "],
                    solution_en: "<p>43.(c) <strong>copper</strong>. A device in which heat measurement can be done is called a calorimeter. It consists of a metallic vessel and stirrer of the same material, like copper or aluminium. Copper is a good heat conductor, which means it can raise its temperature to measure heat flow by absorbing a small amount of heat.</p>",
                    solution_hi: "<p>43.(c) <strong>ताँबें</strong>। एक उपकरण जिसमें ऊष्मा मापी जा सकती है उसे कैलोरीमीटर कहा जाता है। इसमें तांबे या एल्युमीनियम जैसी ही पदार्थ से बना एक धातु का बर्तन और मिश्रण बनाने वाला उपकरण होता है। तांबा, ऊष्मा का एक अच्छा चालक है, जिसका अर्थ है कि यह थोड़ी मात्रा में ऊष्मा को अवशोषित करके, अपना तापमान बढ़ा कर ऊष्मा प्रवाह को माप सकता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following gases get released when dilute sulphuric acid (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><msub><mi>SO</mi><mn>4</mn></msub></math>) reacts with magnesium (Mg)?</p>",
                    question_hi: "<p>44. जब तनु सल्फ़्यूरिक अम्ल (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><msub><mi>SO</mi><mn>4</mn></msub></math>), मैग्नीशियम (Mg) के साथ अभिक्रिया करता है, तो निम्नलिखित में से कौन-सी गैस निकलती है?</p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>SO</mi><mn>3</mn></msub></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>SO</mi><mn>2</mn></msub></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>SO</mi><mn>3</mn></msub></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>SO</mi><mn>2</mn></msub></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math></p>"],
                    solution_en: "<p>44.(d) <math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"bold\">H</mi><mn mathvariant=\"bold\">2</mn></msub></math>. The chemical equation for this reaction is : Mg (s) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><msub><mi>SO</mi><mn>4</mn></msub></math> (aq) <math display=\"inline\"><mo>&#8594;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>MgSO</mi><mn>4</mn></msub></math> (aq) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math> (g). This is a displacement reaction that occurs at normal temperatures. The gas produced (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math>) by a metal reacting with a dilute acid burn with a popping sound.</p>",
                    solution_hi: "<p>44.(d) <math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"bold\">H</mi><mn mathvariant=\"bold\">2</mn></msub></math>. इस अभिक्रिया का रासायनिक समीकरण है: Mg (s) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><msub><mi>SO</mi><mn>4</mn></msub></math>(aq) <math display=\"inline\"><mo>&#8594;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>MgSO</mi><mn>4</mn></msub></math>(aq) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math> (g)। यह एक विस्थापन अभिक्रिया है जो सामान्य तापमान पर होती है। धातु द्वारा तनु अम्ल के साथ अभिक्रिया करने पर उत्पन्न गैस (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math>) चटकने की ध्वनि के साथ जलती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following duties has been NOT prescribed by the Indian Constitution as Fundamental Duties? <br>1. To defend the country <br>2. To pay income tax <br>3. To cast the vote in election <br>4. To safeguard the public property</p>",
                    question_hi: "<p>45. निम्नलिखित में से कौन सा/से कर्तव्य भारतीय संविधान द्वारा मौलिक कर्तव्यों के रूप में निर्धारित नहीं किया गया है/किए गए हैं? <br>1. देश की रक्षा करना <br>2. आयकर का भुगतान करना <br>3. चुनाव में मतदान करना <br>4. सार्वजनिक संपत्ति की सुरक्षा करना</p>",
                    options_en: ["<p>1 only</p>", "<p>2 only</p>", 
                                "<p>2 and 3 both</p>", "<p>2 and 4 both</p>"],
                    options_hi: ["<p>केवल 1</p>", "<p>केवल 2</p>",
                                "<p>2 और 3 दोनों</p>", "<p>2 और 4 दोनों</p>"],
                    solution_en: "<p>45.(c) <strong>2 and 3 both.</strong> All eleven Fundamental Duties are listed in Article 51-A (Part IV-A) of the Constitution. These duties were incorporated into the Constitution by the 42nd Constitutional Amendment Act of 1976 on the recommendations of the Swaran Singh Committee. Article 51-A (d) states the duty to defend the country and render national service when called upon to do so, while Article 51-A (i) emphasizes the duty to safeguard public property and abjure violence.</p>",
                    solution_hi: "<p>45.(c)<strong> 2 और 3 दोनों।</strong> सभी ग्यारह मौलिक कर्तव्यों संविधान के अनुच्छेद 51-A (भाग IV-A) में सूचीबद्ध हैं। स्वर्ण सिंह समिति की सिफारिशों पर 1976 के 42वें संविधान संशोधन अधिनियम द्वारा इन कर्तव्यों को संविधान में शामिल किया गया था। अनुच्छेद 51-A (d) देश की रक्षा करने और आह्वान किए जाने पर राष्ट्रीय सेवा करने के कर्तव्य को बताता है, जबकि अनुच्छेद 51-A (i) सार्वजनिक संपत्ति की सुरक्षा और हिंसा से दूर रहने के कर्तव्य पर जोर देता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who among the following was the successor of Mughal Emperor, Babur?</p>",
                    question_hi: "<p>46. निम्नलिखित में से कौन मुगल सम्राट बाबर का उत्तराधिकारी था?</p>",
                    options_en: ["<p>Shahjahan</p>", "<p>Humayun</p>", 
                                "<p>Jahangir</p>", "<p>Aurangzeb</p>"],
                    options_hi: ["<p>शाहजहाँ</p>", "<p>हुमायूं</p>",
                                "<p>जहांगीर</p>", "<p>औरंगज़ेब</p>"],
                    solution_en: "<p>46.(b) <strong>Humayun</strong>. Babur (Founder of Mughal emperor) was descended from Timur and Genghis Khan. Key Battles by him : First Battle of Panipat (1526) - Defeated Ibrahim Lodi. Battle of Khanwa (1527) - Defeated Rana Sanga of Mewar and Afghan allies. Battle of Chanderi (1528) - Victory over Medini Rai. Battle of Ghagra (1529) - Defeated Afghan rebels led by Mahmud Lodi. Mughal emperors chronologically : Babur &rarr; Humayun &rarr; Akbar &rarr; Jahangir &rarr; Shah-Jahan &rarr; Aurangzeb etc.</p>",
                    solution_hi: "<p>46.(b) <strong>हुमायूं</strong>। बाबर (मुगल सम्राट का संस्थापक) तैमूर और चंगेज खान का वंशज था। उसके द्वारा लड़े गए प्रमुख युद्ध : पानीपत का पहला युद्ध (1526) - इब्राहिम लोदी को हराया। खानवा का युद्ध (1527) - मेवाड़ के राणा साँगा और अफगान सहयोगियों को हराया। चंदेरी का युद्ध (1528) - मेदिनी राय पर विजय। घाघरा का युद्ध (1529) - महमूद लोदी के नेतृत्व में अफगान विद्रोहियों को हराया। मुगल सम्राट कालानुक्रमिक रूप से : बाबर &rarr; हुमायूँ &rarr; अकबर &rarr; जहाँगीर &rarr; शाहजहाँ &rarr; औरंगज़ेब आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following hills is located in the Eastern Ghats?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन-सी पहाड़ी पूर्वी घाट में स्थित है?</p>",
                    options_en: ["<p>Shevaroy hill</p>", "<p>Mizo hill</p>", 
                                "<p>Khasi hill</p>", "<p>Anai Mudi hill</p>"],
                    options_hi: ["<p>शेवरॉय पहाड़ी</p>", "<p>मिज़ो पहाड़ी</p>",
                                "<p>खासी पहाड़ी</p>", "<p>अनई मुडी पहाड़ी</p>"],
                    solution_en: "<p>47.(a) <strong>Shevaroy hill.</strong> The highest peak in the Eastern Ghats is Jindhagada Peak (1,690 metres). Shevaroy Hills and the Javadi Hills are located to the southeast of the Eastern Ghats. The Khasi hills are a part of the Garo-Khasi-Jaintia range. The height of the Western Ghats progressively increases from north to south. The highest peaks include the Anamudi (2,695metres) and the Doda Betta (2,637 metres).</p>",
                    solution_hi: "<p>47.(a) <strong>शेवरॉय पहाड़ी</strong>। पूर्वी घाट की सबसे ऊँची चोटी जिंदगड़ा चोटी (1,690 मीटर) है। शेवरॉय पहाड़ियाँ और जावड़ी पहाड़ियाँ पूर्वी घाट के दक्षिण-पूर्व में स्थित हैं। खासी पहाड़ियाँ गारो-खासी-जयंतिया पर्वतमाला का हिस्सा हैं। पश्चिमी घाट की ऊँचाई उत्तर से दक्षिण की ओर क्रमशः बढ़ती जाती है। सबसे ऊँची चोटियों में अनाईमुडी (2,695 मीटर) और डोडा बेट्टा (2,637 मीटर) शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which set of diseases are caused by bacteria?</p>",
                    question_hi: "<p>48. रोगों का कौन-सा समूह जीवाणुओं के कारण होता है?</p>",
                    options_en: ["<p>Influenza, Dengue, Cholera</p>", "<p>Typhoid, Cholera, Tuberculosis</p>", 
                                "<p>Dengue, Malaria, Cholera</p>", "<p>Malaria, Common cold, Influenza</p>"],
                    options_hi: ["<p>इन्फ्लुएंजा, डेंगी, हैजा</p>", "<p>आंत्र ज्वर, हैजा, तपेदिक</p>",
                                "<p>डेंगी, मलेरिया, हैजा</p>", "<p>मलेरिया, सामान्य सर्दी-जुकाम, इन्फ्लूएंजा</p>"],
                    solution_en: "<p>48.(b) <strong>Typhoid</strong>, <strong>Cholera</strong>, <strong>Tuberculosis</strong>. Typhoid fever is caused by the bacteria Salmonella Typhi. Cholera is caused by the bacterium Vibrio cholerae. Tuberculosis (TB) is caused by a bacterium (or germ) called Mycobacterium tuberculosis. Viral Disease : Common cold (rhinovirus), Influenza (Influenza), HIV infection (human immunodeficiency virus), Dengue (transmitted by the bite of an infected mosquito, and is spread by the Aedes aegypti and Aedes albopictus species).</p>",
                    solution_hi: "<p>48.(b) <strong>टाइफाइड</strong>, <strong>हैजा</strong>, <strong>टीबी</strong>। टाइफाइड ज्वर साल्मोनेला टाइफी नामक बैक्टीरिया के कारण होता है। हैजा विब्रियो कोलेरा नामक बैक्टीरिया के कारण होता है। क्षय रोग (TB) माइकोबैक्टीरियम ट्यूबरकुलोसिस नामक जीवाणु (या रोगाणु) के कारण होता है। वायरल रोग: सामान्य सर्दी (राइनोवायरस), इन्फ्लूएंजा (इन्फ्लूएंजा), HIV संक्रमण (मानव इम्यूनोडिफीसिअन्सी वायरस), डेंगू (संक्रमित मच्छर के काटने से फैलता है, जो एडीज एजिप्टी और एडीज एल्बोपिक्टस प्रजातियों द्वारा फैलता है)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following numbers best describes the number of white pieces used in chess?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन-सी संख्या शतरंज में इस्तेमाल होने वाली सफेद मोहरों की संख्या का सबसे अच्छा वर्णन करती है?</p>",
                    options_en: ["<p>18</p>", "<p>16</p>", 
                                "<p>19</p>", "<p>17</p>"],
                    options_hi: ["<p>18</p>", "<p>16</p>",
                                "<p>19</p>", "<p>17</p>"],
                    solution_en: "<p>49.(b) <strong>16. </strong>There are six different types of chess pieces. Each side starts with 16 pieces : eight pawns, two bishops, two knights, two rooks, one queen, and one king. King : Moves one square in any direction. Queen : Moves any number of squares diagonally, horizontally, or vertically. Rook : Moves any number of squares horizontally or vertically. Bishop : Moves any number of squares diagonally. Knight : Moves in an \'L-shape\' (two squares in one direction and then one square perpendicular). Pawn : Moves one square forward (two squares on its first move) and captures diagonally one square forward.</p>",
                    solution_hi: "<p>49.(b) <strong>16.</strong> शतरंज के छ: अलग-अलग प्रकार के मोहरे होते हैं। प्रत्येक पक्ष में 16 मोहरे होते है: आठ प्यादे , दो बिशप (bishops), दो घोड़े (knights), दो हाथी (rooks) , एक रानी और एक राजा। राजा: किसी भी दिशा में एक वर्ग चलता है। रानी: तिरछे, क्षैतिज या लंबवत रूप से किसी भी संख्या के वर्गों में आगे बढ़ती है। हाथी (rooks) : क्षैतिज या लंबवत किसी भी संख्या में वर्गों को चलता है। बिशप: तिरछे किसी भी संख्या में वर्गों को चलता है। घोड़ा: \'L-आकार\' (एक दिशा में दो वर्ग और फिर एक वर्ग लंबवत) में चलता है । प्यादा : एक वर्ग आगे बढ़ता है (अपनी पहली चाल में दो वर्ग) और कब्जा के लिए तिरछे एक वर्ग आगे बढ़ता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Paryushana festival is celebrated by the people of which of the following communities?</p>",
                    question_hi: "<p>50. पर्यूषण पर्व निम्नलिखित में से किस समुदाय के लोगों द्वारा मनाया जाता है?</p>",
                    options_en: ["<p>Jain</p>", "<p>Muslim</p>", 
                                "<p>Hindu</p>", "<p>Sikh</p>"],
                    options_hi: ["<p>जैन</p>", "<p>मुस्लिम</p>",
                                "<p>हिंदू</p>", "<p>सिख</p>"],
                    solution_en: "<p>50.(a) <strong>Jain</strong>. This 8-10 days festival inspires Jains worldwide to remember the roots and philosophies of Jainism. Celebrating paryushan also brings a social purpose as well. Other Religious festivals : Jain - Mahavir Jayanti, Rohini Vrat, Samvatsari, Meru Trayodashi etc. Muslim - Eid ul Fitr, Eid ul Adha, Ramadan, Muharram etc. Sikh - Hola Mohalla, Vaisakhi, Maghi etc. Hindu - Holi, Diwali, Rakshabndahn etc.</p>",
                    solution_hi: "<p>50.(a) <strong>जैन</strong>। यह 8-10 दिवसीय त्योहार दुनिया भर के जैन धर्मावलंबियों को जैन धर्म की जड़ों और दर्शन को याद करने के लिए प्रेरित करता है। पर्युषण मनाने से सामाजिक उद्देश्य भी पूरा होता है। अन्य धार्मिक त्योहार : जैन - महावीर जयंती, रोहिणी व्रत, संवत्सरी, मेरु त्रयोदशी आदि। मुस्लिम - ईद उल फितर, ईद उल अज़हा, रमजान, मुहर्रम आदि। सिख - होला मोहल्ला, वैसाखी, माघी आदि। हिंदू - होली, दिवाली, रक्षाबंधन आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Simplify: <br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#160;</mo><mo>&#247;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></math>]</p>",
                    question_hi: "<p>51. सरलीकरण करें: <br>[<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#160;</mo><mo>&#247;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></math>]</p>",
                    options_en: ["<p>9</p>", "<p>23</p>", 
                                "<p>27</p>", "<p>15</p>"],
                    options_hi: ["<p>9</p>", "<p>23</p>",
                                "<p>27</p>", "<p>15</p>"],
                    solution_en: "<p>51.(b) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#160;</mo><mo>&#247;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></math>]<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>9</mn><mn>1</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></math>]<br>=&nbsp;[(1) &times; 18 + 5]&nbsp;<strong id=\"docs-internal-guid-217f2f45-7fff-9adb-99c0-80e64e07b0c2\"></strong>= 23</p>",
                    solution_hi: "<p>51.(b) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#160;</mo><mo>&#247;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></math>]<br>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>9</mn><mn>1</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>5</mn></math>]<br>=&nbsp;[(1) &times; 18 + 5]&nbsp;<strong id=\"docs-internal-guid-217f2f45-7fff-9adb-99c0-80e64e07b0c2\"></strong>= 23</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The average of the squares of the first 47 natural numbers is</p>",
                    question_hi: "<p>52. प्रथम 47 प्राकृतिक संख्याओं के वर्गों का औसत ज्ञात कीजिए।</p>",
                    options_en: ["<p>760</p>", "<p>761</p>", 
                                "<p>759</p>", "<p>762</p>"],
                    options_hi: ["<p>760</p>", "<p>761</p>",
                                "<p>759</p>", "<p>762</p>"],
                    solution_en: "<p>52.(a) Sum of square of first 47 natural number = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>6</mn></mfrac></math>(n + 1)(2n + 1)<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>(47 + 1)(2 &times; 47 + 1)<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>(48)(95)<br>= 47 &times; 8 &times; 95 = 35720<br>Average of square of first 47 natural numbers = <math display=\"inline\"><mfrac><mrow><mn>35720</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> = 760</p>",
                    solution_hi: "<p>52.(a) प्रथम 47 प्राकृतिक संख्या के वर्ग का योग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>6</mn></mfrac></math>(n + 1)(2n + 1)<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>(47 + 1)(2 &times; 47 + 1)<br>= <math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>(48)(95)<br>= 47 &times; 8 &times; 95 = 35720<br>प्रथम 47 प्राकृत संख्याओं के वर्ग का औसत = <math display=\"inline\"><mfrac><mrow><mn>35720</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math> = 760</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Akshara and Siddharth travel from point P to Q, a distance of 72 km at a rate of 8 km/h and 10 km/h, respectively. Siddharth reaches Q first and returns immediately and meets Akshara at R. Find the distance from P to R.</p>",
                    question_hi: "<p>53. अक्षरा और सिद्धार्थ क्रमशः 8 km/h और 10 km/h की दर से स्&zwj;थल P से Q तक 72 km की दूरी तय करते हैं। सिद्धार्थ स्&zwj;थल Q पर पहले पहुंचता है और तुरंत लौटता है और अक्षरा से स्&zwj;थल R पर मिलता है। P से R की दूरी ज्ञात कीजिए।</p>",
                    options_en: ["<p>65 km</p>", "<p>63 km</p>", 
                                "<p>64 km</p>", "<p>66 km</p>"],
                    options_hi: ["<p>65 km</p>", "<p>63 km</p>",
                                "<p>64 km</p>", "<p>66 km</p>"],
                    solution_en: "<p>53.(c) PQ = 72 km<br>They are meeting at point R, means within same time Akshara travels PR and Siddharth travels PQ + QR,<br>When time is the same, distance travelled is proportional to speed. <br><math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>R</mi></mrow><mrow><mi>P</mi><mi>Q</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>PQ</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>RQ</mi></mrow><mrow><mi>PQ</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>QR</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 10PQ - 10RQ = 8PQ + 8QR<br><math display=\"inline\"><mo>&#8658;</mo></math> 2PQ = 18RQ<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 &times; 72 = 18RQ<br><math display=\"inline\"><mo>&#8658;</mo></math> RQ = 8 km<br>So, the distance between P to R = PQ - RQ = 72 - 8 = 64 km</p>",
                    solution_hi: "<p>53.(c) PQ = 72 km<br>सिद्धार्थ और अक्षरा बिंदु R पर मिल रहे हैं, इसका मतलब है कि उसी समय अक्षरा PR दुरी तय करती है और सिद्धार्थ PQ + QR,<br>दुरी तय करता है, जब समय समान होता है, तो तय की गई दूरी गति के समानुपाती होती है। <br><math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>R</mi></mrow><mrow><mi>P</mi><mi>Q</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>Q</mi><mi>R</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>PQ</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>RQ</mi></mrow><mrow><mi>PQ</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>QR</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 10PQ - 10RQ = 8PQ + 8QR<br><math display=\"inline\"><mo>&#8658;</mo></math> 2PQ = 18RQ<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 &times; 72 = 18RQ<br><math display=\"inline\"><mo>&#8658;</mo></math> RQ = 8 km<br>तो, P से R के बीच की दूरी = PQ - RQ = 72 - 8 = 64 किमी</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. In a collection of rare coins, there is one gold coin for every four non-gold coins. If 20 more gold coins are added to the collection, the ratio of the number of gold coins to that of non-gold coins will be 2 : 3. The total number of coins in the collection will now become ________.</p>",
                    question_hi: "<p>54. दुर्लभ सिक्कों के संग्रह में, प्रत्येक चार गैर-सोने के सिक्कों के लिए एक सोने का सिक्का है। यदि संग्रह में 20 और सोने के सिक्के जोड़े जाते हैं, तो सोने के सिक्कों की संख्या और गैर-सोने के सिक्कों की संख्या का अनुपात 2 : 3 होगा। संग्रह में सिक्कों की कुल संख्या अब ________ हो जाएगी।</p>",
                    options_en: ["<p>80</p>", "<p>60</p>", 
                                "<p>100</p>", "<p>48</p>"],
                    options_hi: ["<p>80</p>", "<p>60</p>",
                                "<p>100</p>", "<p>48</p>"],
                    solution_en: "<p>54.(a) Let the number of gold coins and non gold coins be <math display=\"inline\"><mi>G</mi></math> and N respectively,<br><math display=\"inline\"><mo>&#8658;</mo></math> G = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; N<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">G</mi><mi mathvariant=\"normal\">N</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>G = x&nbsp;and N = 4x<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>20</mn></mrow><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 3x + 60 = 8x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 12<br>Gold coins = 12<br>Non gold coins = 4 &times; 12 = 48<br>Total number of coins in the collection = 12 + 48 + 20 = 80</p>",
                    solution_hi: "<p>54.(a) माना , सोने के सिक्कों और गैर सोने के सिक्कों की संख्या क्रमशः G और N है<br><math display=\"inline\"><mo>&#8658;</mo></math> G = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; N<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">G</mi><mi mathvariant=\"normal\">N</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>G = x&nbsp;और N = 4x<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>20</mn></mrow><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 3x + 60 = 8x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 12<br>सोने के सिक्के = 12<br>गैर सोने के सिक्के = 4 &times; 12 = 48<br>संग्रह में सिक्कों की कुल संख्या= 12 + 48 + 20 = 80</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Find the least value of k for which a 7-digit number 249k876 is divisible by 6</p>",
                    question_hi: "<p>55. k का वह न्यूनतम मान ज्ञात करें जिसके लिए 7-अंकों वाली संख्या 249k876, 6 से विभाज्य हो।</p>",
                    options_en: ["<p>1</p>", "<p>0</p>", 
                                "<p>5</p>", "<p>7</p>"],
                    options_hi: ["<p>1</p>", "<p>0</p>",
                                "<p>5</p>", "<p>7</p>"],
                    solution_en: "<p>55.(b) A number is divisible by 6 if it is divisible by both 2 and 3:<br>Divisibility of 2 : A number whose last digit is divisible by 2. <br>Divisibility of 3 : A number is divisible by 3 if the sum of all digits of that number is divisible by 3.<br>Now, 249k876<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 + 4 + 9 + k + 8 + 7 + 6 = 36 + k<br>The least value of k is 0</p>",
                    solution_hi: "<p>55.(b) एक संख्या 6 से विभाज्य है यदि वह 2 और 3 दोनों से विभाज्य है:<br>2 की विभाज्यता :- वह संख्या जिसका अंतिम अंक 2 से विभाज्य हो।<br>3 की विभाज्यता :- एक संख्या 3 से विभाज्य होती है यदि उस संख्या के सभी अंकों का योग 3 से विभाज्य हो।<br>अब, 249k876<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 + 4 + 9 + k + 8 + 7 + 6 = 36 + k<br>K का न्यूनतम मान = 0</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. If the simple interest on ₹7,200 in 3 years at the rate of 16% per annum equals the simple interest on ₹9,600 at the rate of <math display=\"inline\"><mi>x</mi></math>% per annum in 4 years. The value of x is equal to:</p>",
                    question_hi: "<p>56. यदि ₹7,200 पर 16% प्रति वर्ष की दर से 3 वर्षों का साधारण ब्याज, ₹9,600 पर <math display=\"inline\"><mi>x</mi></math>% प्रति वर्ष की दर से 4 वर्षों के साधारण ब्याज के बराबर है। तो x का मान किसके बराबर होगा?</p>",
                    options_en: ["<p>9</p>", "<p>11</p>", 
                                "<p>8</p>", "<p>10</p>"],
                    options_hi: ["<p>9</p>", "<p>11</p>",
                                "<p>8</p>", "<p>10</p>"],
                    solution_en: "<p>56.(a) SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Principal</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>rate</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi></mrow><mn>100</mn></mfrac></math><br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7200</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>16</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow><mn>100</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>9600</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>72</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>16</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow><mrow><mn>96</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn></mrow></mfrac></math> = x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 9</p>",
                    solution_hi: "<p>56.(a) साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7200</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>16</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow><mn>100</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mn>9600</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>72</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>16</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow><mrow><mn>96</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn></mrow></mfrac></math> = x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 9</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If a shopkeeper sells sugar at ₹44.8 per kg, he is able to make a 12% profit. Due to water seepage, <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> of the sugar is damaged. What should now be the selling price per kg of the rest of the sugar to have a 5% profit?</p>",
                    question_hi: "<p>57. यदि एक दुकानदार चीनी को ₹44.8 प्रति kg की दर से बेचता है, तो वह 12% लाभ अर्जित करता है। पानी के रिसाव के कारण चीनी का <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> हिस्सा खराब हो जाता है। 5% लाभ प्राप्त करने के लिए शेष चीनी का प्रति किलोग्राम विक्रय मूल्य क्या होना चाहिए?</p>",
                    options_en: ["<p>₹49.5</p>", "<p>₹52.5</p>", 
                                "<p>₹48.5</p>", "<p>₹51.8</p>"],
                    options_hi: ["<p>₹49.5</p>", "<p>₹52.5</p>",
                                "<p>₹48.5</p>", "<p>₹51.8</p>"],
                    solution_en: "<p>57.(b) SP of 1 kg sugar = ₹44.8 <br>CP of 1 kg sugar = ₹44.8 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>112</mn></mrow></mfrac></math> = ₹40 <br>According to the question,<br>C.P.Remaining sugar = ₹40 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹50 <br>New SP of 1 kg sugar after profit of 5% = ₹ 50 &times; <math display=\"inline\"><mfrac><mrow><mn>105</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹52.5</p>",
                    solution_hi: "<p>57.(b) 1 किलो चीनी का विक्रय मूल्य = ₹44.8<br>1 किलो चीनी का क्रय मूल्य = ₹44.8 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>112</mn></mrow></mfrac></math> = ₹40 <br>प्रश्न के अनुसार,<br>शेष चीनी का क्रय मूल्य = ₹40 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹50 <br>5% लाभ के बाद 1 किलो चीनी का नया विक्रय मूल्य = ₹ 50 &times; <math display=\"inline\"><mfrac><mrow><mn>105</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹52.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. For what value of k, the system of equations kx&nbsp;- 15y + 7 = 0 and 7x - 21y - 19 = 0 has NO solution?</p>",
                    question_hi: "<p>58. k, के किस मान के लिए, समीकरण निकाय kx&nbsp;- 15y + 7 = 0 और 7x - 21y - 19 = 0 का कोई हल नहीं है?</p>",
                    options_en: ["<p>5</p>", "<p>12</p>", 
                                "<p>25</p>", "<p>6</p>"],
                    options_hi: ["<p>5</p>", "<p>12</p>",
                                "<p>25</p>", "<p>6</p>"],
                    solution_en: "<p>58.(a) Given equation, kx - 15y + 7 = 0 and 7x - 21y - 19 = 0<br>There is no solution = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub></mfrac></mstyle><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></mstyle></math>&nbsp;&ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math> <br>Then,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>7</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>-</mo><mn>15</mn></mrow><mrow><mo>-</mo><mn>21</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> k = 5</p>",
                    solution_hi: "<p>58.(a) दिया गया समीकरण , kx - 15y + 7 = 0 और 7x - 21y - 19 = 0<br>कोई हल नहीं = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><msub><mi mathvariant=\"normal\">a</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">a</mi><mn>2</mn></msub></mfrac></mstyle><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></mstyle></math>&nbsp;&ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math><br>इसलिए ,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">k</mi><mn>7</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>-</mo><mn>15</mn></mrow><mrow><mo>-</mo><mn>21</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> k = 5</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A shopkeeper sold the following items to Arushi earning profit on it. How much total profit (in ₹) did he earn on the basis of the given data?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123834108.png\" alt=\"rId50\" width=\"400\" height=\"119\"></p>",
                    question_hi: "<p>59. किसी दुकानदार ने निम्नांकित वस्तुएँ आरुषि को बेचकर लाभ कमाया । <br>दिए गए आँकड़ों के आधार पर उसने कुल कितना लाभ (₹ में) कमाया?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123834229.png\" alt=\"rId51\" width=\"400\"></p>",
                    options_en: ["<p>16,436</p>", "<p>34,760</p>", 
                                "<p>16,760</p>", "<p>16,636</p>"],
                    options_hi: ["<p>16,436</p>", "<p>34,760</p>",
                                "<p>16,760</p>", "<p>16,636</p>"],
                    solution_en: "<p>59.(a) Profit on mobile = 20000 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 2000<br>Profit on laptop = 72000 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 14400<br>Profit on mobile covers = 300 &times; <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 36<br>Total profit earn by a shopkeeper = 2000 + 14400 + 36 = ₹ 16436</p>",
                    solution_hi: "<p>59.(a) मोबाइल पर लाभ = 20000 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 2000<br>लैपटॉप पर लाभ = 72000 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 14400<br>मोबाइल कवर पर लाभ = 300 &times; <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 36<br>एक दुकानदार द्वारा अर्जित कुल लाभ = 2000 + 14400 + 36 = ₹ 16436</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The given pie chart shows the annual agricultural production (in tonnes) in a region. The total production is 8600 tonnes.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123834380.png\" alt=\"rId52\" width=\"200\"> <br>What is the annual production of rice in the region?</p>",
                    question_hi: "<p>60. दिया गया पाई चार्ट एक क्षेत्र में वार्षिक कृषि उत्पादन (टन में) दर्शाता है। कुल उत्पादन 8600 टन है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123834542.png\" alt=\"rId53\" width=\"200\"> <br>इस क्षेत्र में चावल का वार्षिक उत्पादन कितना है?</p>",
                    options_en: ["<p>5012 tonnes</p>", "<p>1052 tonnes</p>", 
                                "<p>2150 tonnes</p>", "<p>2051 tonnes</p>"],
                    options_hi: ["<p>5012 टन</p>", "<p>1052 टन</p>",
                                "<p>2150 टन</p>", "<p>2051 टन</p>"],
                    solution_en: "<p>60.(c) The production of rice = 8600 &times; <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2150 tonnes</p>",
                    solution_hi: "<p>60.(c) चावल का उत्पादन = 8600 &times; <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2150 टन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The number of three different types of cars, X, Y, and Z, manufactured by a company over three years is given in the below table. Study the table carefully and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123834675.png\" alt=\"rId54\" width=\"300\" height=\"129\"> <br>What is the average number of cars manufactured by the company in the year 2022?</p>",
                    question_hi: "<p>61. नीचे दी गई तालिका में, एक कंपनी द्वारा तीन वर्षों में निर्मित तीन अलग-अलग प्रकार की कारों, X, Y और Z की संख्या दी गई है। तालिका का ध्यानपूर्वक अध्ययन कीजिए और उसके बाद प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123834780.png\" alt=\"rId55\" width=\"300\" height=\"132\"> <br>वर्ष 2022 में कंपनी द्वारा निर्मित कारों की औसत संख्या कितनी है?</p>",
                    options_en: ["<p>3227</p>", "<p>2851</p>", 
                                "<p>3057</p>", "<p>2720</p>"],
                    options_hi: ["<p>3227</p>", "<p>2851</p>",
                                "<p>3057</p>", "<p>2720</p>"],
                    solution_en: "<p>61.(c) the average number of cars in the year 2022<br>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3551</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3020</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2600</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>= <math display=\"inline\"><mfrac><mrow><mn>9171</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 3057</p>",
                    solution_hi: "<p>61.(c) वर्ष 2022 में कारों की औसत संख्या&nbsp;<br>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3551</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3020</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2600</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>= <math display=\"inline\"><mfrac><mrow><mn>9171</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 3057</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Find the measure of the central angle of a sector if its area is 16&pi; and the radius is 8.</p>",
                    question_hi: "<p>62. उस त्रिज्यखण्ड के केन्द्रीय कोण की माप क्या होगी, यदि उसका क्षेत्रफल 16&pi; और त्रिज्या 8 है।</p>",
                    options_en: ["<p>75&deg;</p>", "<p>60&deg;</p>", 
                                "<p>108&deg;</p>", "<p>90&deg;</p>"],
                    options_hi: ["<p>75&deg;</p>", "<p>60&deg;</p>",
                                "<p>108&deg;</p>", "<p>90&deg;</p>"],
                    solution_en: "<p>62.(d) Area of sector = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>angle</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 16&pi; = &pi; &times; 8 &times; 8 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>angle</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>4</mn></mfrac></math> = &theta;<br><math display=\"inline\"><mo>&#8658;</mo></math> Angle(&theta;) = 90&deg;</p>",
                    solution_hi: "<p>62.(d) त्रिज्यखंड का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup></math> &times; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2379;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 16&pi; = &pi; &times; 8 &times; 8 &times; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2379;&#2339;</mi><mo>&#160;</mo><mo>(</mo><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>4</mn></mfrac></math> = &theta;<br><math display=\"inline\"><mo>&#8658;</mo></math> कोण (&theta;) = 90&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. What is the difference in area (in cm&sup2;) of <math display=\"inline\"><mi>&#916;</mi></math>ABC having sides of 10 cm, 20 cm and 20 cm, and a right angled triangle &Delta;PQR with hypotenuse of 13 cm and one of the perpendiculars of 12 cm?<br><strong>Note: </strong><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> = 1.41, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 1.73, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math> = 2.65, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math> = 3.61, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math> = 3.87, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>21</mn></msqrt></math> = 4.58</p>",
                    question_hi: "<p>63. 10 सेमी, 20 सेमी और 20 सेमी भुजाओं वाले <math display=\"inline\"><mi>&#916;</mi></math>ABC और 13 सेमी कर्ण और लम्बों में से 12 सेमी के एक लंब वाले समकोण त्रिभुज &Delta;PQR के क्षेत्रफल (सेमी&sup2; में) में कितना अंतर है?<br><strong>नोट: </strong><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> = 1.41, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 1.73, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math> = 2.65, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math> = 3.61, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math> = 3.87, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>21</mn></msqrt></math> = 4.58</p>",
                    options_en: ["<p>70.05</p>", "<p>36.57</p>", 
                                "<p>66.75</p>", "<p>53.58</p>"],
                    options_hi: ["<p>70.05</p>", "<p>36.57</p>",
                                "<p>66.75</p>", "<p>53.58</p>"],
                    solution_en: "<p>63.(c) Sides of <math display=\"inline\"><mi>&#916;</mi></math>ABC are 10 cm, 20 cm and 20 cm<br>Area of isosceles <math display=\"inline\"><mi>&#916;</mi></math>ABC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msqrt><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><mfrac><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mn>4</mn></mfrac></msqrt></math><br>Here, a = equal side and b = unequal side <br>So, <br>Area of isosceles <math display=\"inline\"><mi>&#916;</mi></math>ABC =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>10</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msqrt><msup><mn>20</mn><mn>2</mn></msup><mo>-</mo><mfrac><msup><mn>10</mn><mn>2</mn></msup><mn>4</mn></mfrac></msqrt></math><br>= 5 &times; <math display=\"inline\"><msqrt><mn>400</mn><mo>-</mo><mfrac><mrow><mn>100</mn></mrow><mrow><mn>4</mn></mrow></mfrac></msqrt></math> <br>= 5 &times; <math display=\"inline\"><msqrt><mn>400</mn><mo>-</mo><mn>25</mn></msqrt></math><br>= 5 &times; <math display=\"inline\"><msqrt><mn>375</mn></msqrt></math> = 5 &times; 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math> = 25 &times; 3.87 = 96.75<br>In right angle <math display=\"inline\"><mi>&#916;</mi></math>PQR, <br>Using pythagorean triplet (5 , 12 , 13)<br>Area of <math display=\"inline\"><mi>&#916;</mi></math>PQR = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; base &times; height<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 5 &times; 12 = 30<br>Required difference = 96.75 - 30 = 66.75 cm&sup2;</p>",
                    solution_hi: "<p>63.(c) ABC की भुजाएँ 10 सेमी, 20 सेमी और 20 सेमी हैं<br>समद्विबाहु <math display=\"inline\"><mi>&#916;</mi></math> ABC का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">b</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msqrt><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mo>-</mo><mfrac><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><mn>4</mn></mfrac></msqrt></math><br>यहाँ, a = बराबर भुजा और b = असमान भुजा <br>इसलिए, <br>समद्विबाहु <math display=\"inline\"><mi>&#916;</mi></math>ABC का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>10</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msqrt><msup><mn>20</mn><mn>2</mn></msup><mo>-</mo><mfrac><msup><mn>10</mn><mn>2</mn></msup><mn>4</mn></mfrac></msqrt></math><br>= 5 &times; <math display=\"inline\"><msqrt><mn>400</mn><mo>-</mo><mfrac><mrow><mn>100</mn></mrow><mrow><mn>4</mn></mrow></mfrac></msqrt></math> <br>= 5 &times; <math display=\"inline\"><msqrt><mn>400</mn><mo>-</mo><mn>25</mn></msqrt></math><br>= 5 &times; <math display=\"inline\"><msqrt><mn>375</mn></msqrt></math> = 5 &times; 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math> = 25 &times; 3.87 = 96.75<br>समकोण <math display=\"inline\"><mi>&#916;</mi></math>PQR में, <br>पायथागॉरियन त्रिक से (5, 12, 13)<br><math display=\"inline\"><mi>&#916;</mi></math>PQR का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; आधार &times; ऊँचाई<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 5 &times; 12 = 30<br>आवश्यक अंतर = 96.75 - 30 = 66.75 cm&sup2;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. At what angle is the arc length half the perimeter of the circle?</p>",
                    question_hi: "<p>64. किस कोण पर चाप की लंबाई वृत्त की परिधि की आधी होती है?</p>",
                    options_en: ["<p>120&deg;</p>", "<p>180&deg;</p>", 
                                "<p>90&deg;</p>", "<p>270&deg;</p>"],
                    options_hi: ["<p>120&deg;</p>", "<p>180&deg;</p>",
                                "<p>90&deg;</p>", "<p>270&deg;</p>"],
                    solution_en: "<p dir=\"ltr\">64.(b)<strong id=\"docs-internal-guid-e1f25f4e-7fff-8460-0deb-2cf1942b28a8\"> </strong>Let the arc length be l<br>According to the question,<br>l = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>&#960;r</mi></mrow><mn>2</mn></mfrac></math> &rArr; l = &pi;r<br>We know that,<br>&rArr; Area of a circle =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; r &times; l<br>&rArr; &pi;r&sup2; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; r &times; &pi;r<br><strong id=\"docs-internal-guid-406868f2-7fff-31af-a699-0f58fc8fa87a\">&rArr; </strong>&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math> = 180&deg;</p>",
                    solution_hi: "<p dir=\"ltr\">64.(b) माना , चाप की लंबाई = l<br>प्रश्न के अनुसार,<br>l = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>&#960;r</mi></mrow><mn>2</mn></mfrac></math> &rArr; l = &pi;r<br>हम जानते हैं,<br>&rArr; वृत्त का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; r &times; l<br>&rArr; &pi;r&sup2; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; r &times; &pi;r<br><strong id=\"docs-internal-guid-406868f2-7fff-31af-a699-0f58fc8fa87a\">&rArr; </strong>&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math> = 180&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. In a company, all employees are getting equal wages. If the number of employees is increased by 20% and their wages per head are decreased by 18%, what is the percentage decrease in total wages?</p>",
                    question_hi: "<p>65. एक कंपनी में सभी कर्मचारियों को समान वेतन मिल रहा है। यदि कर्मचारियों की संख्या 20% बढ़ जाती है और प्रति व्यक्ति उनका वेतन 18% कम हो जाता है, तो कुल वेतन में कितने प्रतिशत की कमी होगी?</p>",
                    options_en: ["<p>2.4%</p>", "<p>1%</p>", 
                                "<p>1.6%</p>", "<p>1.4%</p>"],
                    options_hi: ["<p>2.4%</p>", "<p>1%</p>",
                                "<p>1.6%</p>", "<p>1.4%</p>"],
                    solution_en: "<p>65.(c)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Initial : Final<br>Employees&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp;:&nbsp; &nbsp; 6<br>Wages&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 50&nbsp; &nbsp;:&nbsp; &nbsp;41<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;.<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 250&nbsp; :&nbsp; &nbsp;246<br>Percentage decrease = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>250</mn></mrow></mfrac></math> &times; 100 = 1.6%</p>",
                    solution_hi: "<p>65.(c)&nbsp; &nbsp; &nbsp; प्रारंभिक&nbsp; :&nbsp; अंतिम<br>कर्मचारी&nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;6<br>वेतन&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 50&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 41<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;&hellip;.<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 250&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;246<br>प्रतिशत में कमी = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>250</mn></mrow></mfrac></math> &times; 100 = 1.6%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A shopkeeper marked the price 20% more than its cost price. If he allows a discount of 30%, then find his loss percent.</p>",
                    question_hi: "<p>66. एक दुकानदार अपने क्रय मूल्य से 20% अधिक मूल्य अंकित करता है। यदि वह 30% की छूट देता है, तो उसका हानि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>20%</p>", "<p>16%</p>", 
                                "<p>25%</p>", "<p>15%</p>"],
                    options_hi: ["<p>20%</p>", "<p>16%</p>",
                                "<p>25%</p>", "<p>15%</p>"],
                    solution_en: "<p>66.(b) Let the CP of the article be 100<br>MP of the article = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 120 <br>SP of the article = 120 &times; <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 84<br>Loss % = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>84</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 16 %</p>",
                    solution_hi: "<p>66.(b) माना , वस्तु का क्रय मूल्य = 100<br>वस्तु का अंकित मूल्य = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 120 <br>वस्तु का विक्रय मूल्य= 120 &times; <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 84<br>हानि % = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>84</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 16 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Pipes A and B can fill a tank in 15 hours and 25 hours, respectively, whereas pipe C can empty the full tank in 40 hours. All three pipes are opened together, but pipe A is closed after 5 hours. After how many hours will the remaining part of the tank be filled?</p>",
                    question_hi: "<p>67. पाइप A और B एक टंकी को क्रमशः 15 घंटे और 25 घंटे में भर सकते हैं, जबकि पाइप C पूरी टंकी को 40 घंटे में खाली कर सकता है। तीनों पाइप एक साथ खोले जाते हैं, लेकिन पाइप A, 5 घंटे बाद बंद हो जाता है। टंकी का शेष भाग कितने घंटे बाद भर जाएगा?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>41</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>43</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>44</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>39</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>41</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>43</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>44</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>39</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>"],
                    solution_en: "<p>67.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123834896.png\" alt=\"rId56\" width=\"250\" height=\"131\"><br>All three pipes capacity of after 5 hours = 5 (40 + 24 - 15) = 245 units <br>Remaining capacity = 600 - 245 = 355 units<br>According to the question,<br>Remaining tank can be filled = <math display=\"inline\"><mfrac><mrow><mn>355</mn></mrow><mrow><mn>24</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>355</mn><mn>9</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>39</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math> hours</p>",
                    solution_hi: "<p>67.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123834985.png\" alt=\"rId57\" width=\"250\"><br>5 घंटे के बाद तीनों पाइपों की क्षमता = 5 (40 + 24 - 15) = 245 इकाई<br>शेष क्षमता = 600 - 245 = 355 इकाई<br>प्रश्न के अनुसार,<br>शेष टंकी को भरने मे लगा समय = <math display=\"inline\"><mfrac><mrow><mn>355</mn></mrow><mrow><mn>24</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>15</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>355</mn><mn>9</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>39</mn><mfrac><mn>4</mn><mn>9</mn></mfrac></math>&nbsp;घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. In a right angle <math display=\"inline\"><mi>&#916;</mi></math> ABC, &ang;B = 90&deg;, if tanA = 1, then 4 sinA cosA = _______.</p>",
                    question_hi: "<p>68. एक समकोण <math display=\"inline\"><mi>&#916;</mi></math>ABC में &ang;B = 90&deg; है, यदि tanA = 1 है, तो 4 sinA cos A = ______ होगा।</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>4</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>4</p>"],
                    solution_en: "<p>68.(b) In right <math display=\"inline\"><mi>&#916;</mi></math>ABC, &ang;B = 90&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = tan 45&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> A = 45&deg;<br>Now,<br>4sinA.cosA<br>= 4 &times; sin45&deg; &times; cos45&deg;<br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 2</p>",
                    solution_hi: "<p>68.(b) समकोण <math display=\"inline\"><mi>&#916;</mi></math>ABC में , &ang;B = 90&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> tan A = tan 45&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> A = 45&deg;<br>अब,<br>4sinA.cosA<br>= 4 &times; sin45&deg; &times; cos45&deg;<br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. If x&nbsp;&gt; 1 and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> = 83, then <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math> is:</p>",
                    question_hi: "<p>69. यदि x&nbsp;&gt; 1 और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> = 83 है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>884</p>", "<p>876</p>", 
                                "<p>754</p>", "<p>756</p>"],
                    options_hi: ["<p>884</p>", "<p>876</p>",
                                "<p>754</p>", "<p>756</p>"],
                    solution_en: "<p>69.(d) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math>&nbsp;= 83<br>Subtract both side by 2 in above equation,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> - 2 = 83 - 2<br><math display=\"inline\"><mo>&#8658;</mo></math> (x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)&sup2; = 81<br><math display=\"inline\"><mo>&#8658;</mo></math> x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> = 9<br>We know that,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math> = a&sup3; + 3a<br>= 9&sup3;&nbsp;+ 3 &times; 9<br>= 729 + 27 = 756</p>",
                    solution_hi: "<p>69.(d) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math>&nbsp;= 83<br>उपरोक्त समीकरण में दोनों पक्षों को 2 से घटाएं,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mfrac></math> - 2 = 83 - 2<br><math display=\"inline\"><mo>&#8658;</mo></math> (x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)&sup2; = 81<br><math display=\"inline\"><mo>&#8658;</mo></math> x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> = 9<br>हम वह जानते हैं,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3</mn></msup></mfrac></math> = a&sup3; + 3a<br>= 9&sup3;&nbsp;+ 3 &times; 9<br>= 729 + 27 = 756</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. If sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math> where 0 &lt; &theta; &lt; 90&deg;, then what is the value of 3cosec&theta; + 3cot&theta;?</p>",
                    question_hi: "<p>70. यदि sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math> है, जहाँ 0 &lt; &theta; &lt; 90&deg;है, तो 3cosec&theta; + 3cot&theta; का मान क्या होगा?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math></p>", "<p>7</p>", 
                                "<p>14</p>", "<p>49</p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math></p>", "<p>7</p>",
                                "<p>14</p>", "<p>49</p>"],
                    solution_en: "<p>70.(b) <strong>Given :</strong> sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>hypotenuse</mi><mi>base</mi></mfrac></math><br>By pythagorean triplet (20 , 21 , 29)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123835099.png\" alt=\"rId58\" width=\"180\" height=\"144\"><br>Then,<br>3cosec&theta; + 3cot&theta;<br>= 3 &times; <math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> + 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>21</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>87</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>147</mn><mn>21</mn></mfrac></math> = 7</p>",
                    solution_hi: "<p>70.(b) <strong>दिया गया है :</strong> sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>20</mn></mfrac></math>= <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math><br>(20 , 21 , 29) पायथागॉरियन त्रिक द्वारा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123835099.png\" alt=\"rId58\" width=\"180\" height=\"144\"><br>इसलिए ,<br>3cosec&theta; + 3cot&theta;<br>= 3 &times; <math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> + 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>21</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>87</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>147</mn><mn>21</mn></mfrac></math> = 7</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. AP and AQ are two tangents drawn to a circle with center O from an external point A. If &ang;PAQ = 40&deg;, then &ang;POQ is:</p>",
                    question_hi: "<p>71. AP और AQ केंद्र O वाले किसी वृत्त पर बाह्य बिंदु A से खींची गई दो स्पर्श रेखाएं हैं। यदि &ang;PAQ = 40&deg; है, तो &ang;POQ ज्ञात कीजिए।</p>",
                    options_en: ["<p>120&deg;</p>", "<p>130&deg;</p>", 
                                "<p>150&deg;</p>", "<p>140&deg;</p>"],
                    options_hi: ["<p>120&deg;</p>", "<p>130&deg;</p>",
                                "<p>150&deg;</p>", "<p>140&deg;</p>"],
                    solution_en: "<p>71.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123835263.png\" alt=\"rId59\" width=\"220\" height=\"133\"><br>We know that,<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;POQ + &ang;PAQ = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;POQ + 40&deg; = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;POQ = 180&deg; - 40&deg; = 140&deg;</p>",
                    solution_hi: "<p>71.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123835263.png\" alt=\"rId59\" width=\"220\" height=\"133\"><br>हम जानते हैं,<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;POQ + &ang;PAQ = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;POQ + 40&deg; = 180&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &ang;POQ = 180&deg; - 40&deg; = 140&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If A : B = 6 : 8 and B : C = 7 : 10, then A : B : C is:</p>",
                    question_hi: "<p>72. यदि A : B = 6 : 8 तथा B : C = 7 : 10 है, तो A : B : C का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>21 : 28 : 40</p>", "<p>21 : 23 : 40</p>", 
                                "<p>23 : 32 : 40</p>", "<p>14 : 31 : 20</p>"],
                    options_hi: ["<p>21 : 28 : 40</p>", "<p>21 : 23 : 40</p>",
                                "<p>23 : 32 : 40</p>", "<p>14 : 31 : 20</p>"],
                    solution_en: "<p>72.(a) Ratio <math display=\"inline\"><mo>&#8594;</mo></math> A : B : C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 6 : 8 : 8<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7 : 7 : 10<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; _______________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 42 : 56 : 80 or 21 : 28 : 40</p>",
                    solution_hi: "<p>72.(a) अनुपात&nbsp;<math display=\"inline\"><mo>&#8594;</mo></math> A : B : C<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 6 : 8 : 8<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7 : 7 : 10<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;_______________<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 42 : 56 : 80 या 21 : 28 : 40</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. If cos24&deg; = <math display=\"inline\"><mfrac><mrow><mi>m</mi></mrow><mrow><mi>n</mi></mrow></mfrac></math>, then the value of (cosec 24&deg; - cos 66&deg;) is:</p>",
                    question_hi: "<p>73. यदि cos24&deg; = <math display=\"inline\"><mfrac><mrow><mi>m</mi></mrow><mrow><mi>n</mi></mrow></mfrac></math>, तो (cosec 24&deg; - cos 66&deg;) का मान क्या होगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>n</mi><msqrt><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>m</mi><msqrt><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>m</mi><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>n</mi><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>n</mi><msqrt><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>m</mi><msqrt><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>m</mi><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>n</mi><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>"],
                    solution_en: "<p>73.(d) <strong>Given: </strong>cos24&deg; = <math display=\"inline\"><mfrac><mrow><mi>m</mi></mrow><mrow><mi>n</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>base</mi><mi>hypotenuse</mi></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123835417.png\" alt=\"rId60\" width=\"180\" height=\"150\"><br>By using pythagoras theorem,<br>AB&sup2; = AC&sup2; - BC&sup2;<br>AB&sup2; = n&sup2; - m&sup2;<br>AB = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt></math><br>Now,<br>(cosec 24&deg; - cos 66&deg;)<br>= cosec 24&deg; - cos (90&deg;- 24&deg;)<br>= cosec 24&deg; - sin 24&deg;<br>= <math display=\"inline\"><mfrac><mrow><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>perpendicular</mi><mi>hypotenuse</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>n</mi></mrow><mrow><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt><mi mathvariant=\"normal\">n</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>n</mi><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>n</mi><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>",
                    solution_hi: "<p>73.(d) दिया गया है: cos24&deg; = <math display=\"inline\"><mfrac><mrow><mi>m</mi></mrow><mrow><mi>n</mi></mrow></mfrac></math>= <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></math> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123835417.png\" alt=\"rId60\" width=\"180\"><br>पाइथागोरस प्रमेय का उपयोग करने पर -<br>AB&sup2; = AC&sup2; - BC&sup2;<br>AB&sup2; = n&sup2; - m&sup2;<br>AB = <math display=\"inline\"><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>अब,<br>(cosec 24&deg; - cos 66&deg;)<br>= cosec 24&deg; - cos (90&deg;- 24&deg;)<br>= cosec 24&deg; - sin 24&deg;<br><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mo>&#160;</mo><mfrac><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#2354;&#2306;&#2348;&#2357;&#2340;</mi></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mi>&#2354;&#2306;&#2348;&#2357;&#2340;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>n</mi></mrow><mrow><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><msup><mi mathvariant=\"normal\">n</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></msqrt><mi mathvariant=\"normal\">n</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>n</mi><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi>n</mi><msqrt><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></msqrt></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The circumference of the base of a cylindrical vessel is 44 cm and its height is 25 cm. How many liters of water can it hold? (Take &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    question_hi: "<p>74. एक बेलनाकार बर्तन के आधार की परिधि 44 सेमी है और इसकी ऊंचाई 25 सेमी है। इसमें कितने लीटर जल आ सकता है? (&pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> लें)</p>",
                    options_en: ["<p>38.5</p>", "<p>3.58</p>", 
                                "<p>3.85</p>", "<p>83.5</p>"],
                    options_hi: ["<p>38.5</p>", "<p>3.58</p>",
                                "<p>3.85</p>", "<p>83.5</p>"],
                    solution_en: "<p>74.(c) Circumference of circle = 2<math display=\"inline\"><mi>&#960;</mi></math>r<br><math display=\"inline\"><mo>&#8658;</mo></math> 44 = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r<br><math display=\"inline\"><mo>&#8658;</mo></math> r = 7 cm<br>Now,<br>Volume of cylinder = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math>h<br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 7 &times; 7 &times; 25<br>= 3850 cm&sup3;<br>1000 cm&sup3; = 1 litre<br>1 cm&sup3; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> litre<br>3850 cm&sup3; = <math display=\"inline\"><mfrac><mrow><mn>3850</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 3.85 litres</p>",
                    solution_hi: "<p>74.(c) वृत्त की परिधि = 2<math display=\"inline\"><mi>&#960;</mi></math>r<br><math display=\"inline\"><mo>&#8658;</mo></math> 44 = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r<br><math display=\"inline\"><mo>&#8658;</mo></math> r = 7 cm<br>अब,<br>बेलन का आयतन = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math>h<br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 7 &times; 7 &times; 25<br>= 3850 सेमी&sup3;&nbsp;<br>1000 सेमी&sup3; = 1 लीटर<br>1 सेमी&sup3; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math>लीटर<br>3850 सेमी&sup3; = <math display=\"inline\"><mfrac><mrow><mn>3850</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 3.85 लीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Study the given graph and answer the question that follows. <br>The graph shows the sales of three products: P, Q and R by four companies: A, B, C and D.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123835575.png\" alt=\"rId61\" width=\"350\" height=\"249\"> <br>What percentage is the total production by company C, of the total production by company A?</p>",
                    question_hi: "<p>75. दिए गए ग्राफ़ का अध्ययन करें और उसके बाद पूछे जाने वाले प्रश्&zwj;न का उत्तर दें।<br>ग्राफ़ चार कंपनियों: A, B, C यों और D द्वारा निर्मित तीन उत्पादों: P, Q और R की बिक्री दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731123835678.png\" alt=\"rId62\" width=\"350\" height=\"270\"> <br>कंपनी C का कुल उत्पादन, कंपनी A के कुल उत्पादन का कितना प्रतिशत है?</p>",
                    options_en: ["<p>110%</p>", "<p>90%</p>", 
                                "<p>100%</p>", "<p>105%</p>"],
                    options_hi: ["<p>110%</p>", "<p>90%</p>",
                                "<p>100%</p>", "<p>105%</p>"],
                    solution_en: "<p>75.(c) Total production of company C = 8 + 9 + 6 = 23<br>Total production of company A = 9 + 6 + 8 = 23<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math> &times; 100 = 100%</p>",
                    solution_hi: "<p>75.(c) कंपनी C का कुल उत्पादन = 8 + 9 + 6 = 23<br>कंपनी A का कुल उत्पादन = 9 + 6 + 8 = 23<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>23</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math> &times; 100 = 100%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>It is very hard for her to provide <span style=\"text-decoration: underline;\">the necessities of her family</span>.</p>",
                    question_hi: "<p>76. Select the most appropriate option to substitute the underlined segment in the given sentence. It is very hard for her to provide <span style=\"text-decoration: underline;\">the necessities of her family</span>.</p>",
                    options_en: [" the necessities to her family", " the necessities by her family", 
                                " the necessity of her family", " the necessities from her families"],
                    options_hi: [" the necessities to her family", " the necessities by her family",
                                " the necessity of her family", " the necessities from her families"],
                    solution_en: "76.(a) the necessities to her family<br />‘To’ is used to indicate the receiver of an action or object. In the given sentence, ‘her family’ is the receiver of the necessities. Hence, ‘the necessities to her family’ is the most appropriate answer.",
                    solution_hi: "76.(a) the necessities to her family<br />‘To’ का use किसी क्रिया (action) या वस्तु (object) के प्राप्तकर्ता (receiver) को indicate करने के लिए किया जाता है। दिए गए sentence में,  ‘her family,’ necessities की receiver है। अतः,  ‘the necessities to her family’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "77. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY. <br />UNESCO works to / strengthen an intellectual / and moral solidarity / of humankind.",
                    question_hi: "77. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY. <br />UNESCO works to / strengthen an intellectual / and moral solidarity / of humankind.",
                    options_en: [" strengthen an intellectual", " of humankind.", 
                                " UNESCO works to", " and moral solidarity"],
                    options_hi: [" strengthen an intellectual", " of humankind.",
                                " UNESCO works to", " and moral solidarity"],
                    solution_en: "77.(a) strengthen an intellectual<br />Indefinite article ‘a/an’ is not used for an abstract noun(solidarity). The definite article ‘the’ is used before a specific or particular noun. The given sentence is referring to a specific type of solidarity. Hence, ‘strengthen the intellectual and moral solidarity’ is the most appropriate answer.",
                    solution_hi: "77.(a) strengthen an intellectual<br />Indefinite article ‘a/an’ का प्रयोग abstract noun (solidarity) के लिए नहीं किया जाता है। Definite article ‘the’ का प्रयोग specific या particular noun से पहले किया जाता है। दिया गया sentence, specific प्रकार की एकजुटता(solidarity) को संदर्भित कर रहा है। अतः, ‘strengthen the intellectual and moral solidarity’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the option that can be used as a one-word substitute for the given group of words. The study of coins</p>",
                    question_hi: "<p>78. Select the option that can be used as a one-word substitute for the given group of words.<br>The study of coins</p>",
                    options_en: ["<p>Numismatics</p>", "<p>Choreography</p>", 
                                "<p>Informatics</p>", "<p>Cartography</p>"],
                    options_hi: ["<p>Numismatics</p>", "<p>Choreography</p>",
                                "<p>Informatics</p>", "<p>Cartography</p>"],
                    solution_en: "<p>78.(a) <strong>Numismatics</strong>- the study of coins.<br><strong>Choreography</strong>- the sequence of steps and movements in dance or figure skating, especially in a ballet or other staged dance.<br><strong>Informatics</strong>- the science of processing data for storage and retrieval.<br><strong>Cartography</strong>- the science or practice of drawing maps.</p>",
                    solution_hi: "<p>78.(a) <strong>Numismatics </strong>(मुद्राशास्त्र)- the study of coins.<br><strong>Choreography </strong>(नृत्यकला)- the sequence of steps and movements in dance or figure skating, especially in a ballet or other staged dance.<br><strong>Informatics </strong>(सूचना प्रौद्योगिकी)- the science of processing data for storage and retrieval.<br><strong>Cartography </strong>(मानचित्रण)- the science or practice of drawing maps.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "79. Select the INCORRECTLY spelt word.",
                    question_hi: "79. Select the INCORRECTLY spelt word.",
                    options_en: [" Eficient", " Contribution", 
                                " Miscalculation", " Mediation"],
                    options_hi: [" Eficient", " Contribution",
                                " Miscalculation", " Mediation"],
                    solution_en: "79.(a) Eficient<br />\'Efficient\' is the correct spelling.",
                    solution_hi: "79.(a) Eficient<br />\'Efficient\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that can be used as a one-word substitute for the given group of words. A person who is inclined to see the worst aspect of things</p>",
                    question_hi: "<p>80. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who is inclined to see the worst aspect of things</p>",
                    options_en: ["<p>Pessimist</p>", "<p>Philanthropist</p>", 
                                "<p>Optimist</p>", "<p>Altruist</p>"],
                    options_hi: ["<p>Pessimist</p>", "<p>Philanthropist</p>",
                                "<p>Optimist</p>", "<p>Altruist</p>"],
                    solution_en: "<p>80.(a) <strong>Pessimist</strong>- a person who is inclined to see the worst aspect of things.<br><strong>Philanthropist</strong>- a person who seeks to promote the welfare of others, especially by the generous donation of money to good causes.<br><strong>Optimist</strong>- a person who is inclined to be hopeful and to expect good outcomes.<br><strong>Altruist</strong>- a person who cares about others and helps them despite not gaining anything by doing this.</p>",
                    solution_hi: "<p>80.(a) <strong>Pessimist </strong>(निराशावादी)- a person who is inclined to see the worst aspect of things.<br><strong>Philanthropist </strong>(परोपकारी)- a person who seeks to promote the welfare of others, especially by the generous donation of money to good causes.<br><strong>Optimist </strong>(आशावादी)- a person who is inclined to be hopeful and to expect good outcomes.<br><strong>Altruist </strong>(परोपकारी)- a person who cares about others and helps them despite not gaining anything by doing this.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the given sentence in passive voice. <br>He will give a lecture on personality development.</p>",
                    question_hi: "<p>81. Select the option that expresses the given sentence in passive voice. <br>He will give a lecture on personality development.</p>",
                    options_en: ["<p>A lecture on personality development can be given by him.</p>", "<p>A lecture will be given on personality development.</p>", 
                                "<p>A lecture on personality development will be given by him.</p>", "<p>A lecture on personality development will be giving by him.</p>"],
                    options_hi: ["<p>A lecture on personality development can be given by him.</p>", "<p>A lecture will be given on personality development.</p>",
                                "<p>A lecture on personality development will be given by him.</p>", "<p>A lecture on personality development will be giving by him.</p>"],
                    solution_en: "<p>81.(c) A lecture on personality development will be given by him. (Correct)<br>(a) A lecture on personality development <span style=\"text-decoration: underline;\">can</span> be given by him. (Incorrect Modal)<br>(b) A lecture will be given on personality development. (Incorrect Sentence Structure)<br>(c) A lecture on personality development will be <span style=\"text-decoration: underline;\">giving</span> by him. (Incorrect form of the Verb)</p>",
                    solution_hi: "<p>81.(c) A lecture on personality development will be given by him. (Correct)<br>(a) A lecture on personality development <span style=\"text-decoration: underline;\">can</span> be given by him. (गलत Modal)<br>(b) A lecture will be given on personality development. (गलत Sentence Structure)<br>(c) A lecture on personality development will be <span style=\"text-decoration: underline;\">giving</span> by him. (Verb की गलत form)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "82. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. Note: This is an advertisement of an ice-cream brand. <br />(A) That’s why Silky is deliciously different and exceptionally versatile, natural, thick and durable ice cream. <br />(B) Beautifully firm, nice, silky rather than thin and watery, it stays in shape and stands on top of fruits or pastries without soaking in. <br />(C) Silky thick double ice cream has a unique, fresh taste and texture",
                    question_hi: "82. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. Note: This is an advertisement of an ice-cream brand. <br />(A) That’s why Silky is deliciously different and exceptionally versatile, natural, thick and durable ice cream. <br />(B) Beautifully firm, nice, silky rather than thin and watery, it stays in shape and stands on top of fruits or pastries without soaking in. <br />(C) Silky thick double ice cream has a unique, fresh taste and texture",
                    options_en: [" ABC", " BAC", 
                                " CBA", " BCA"],
                    options_hi: [" ABC", " BAC",
                                " CBA", " BCA"],
                    solution_en: "82.(c) CBA<br />Sentence C will be the starting line as it introduces the main idea of the parajumble, i.e. “Silky thick double ice cream has a unique, fresh taste and texture”. And Sentence B states that it stays in shape and stands on top of fruits or pastries due to being beautifully firm. So, B will follow C. Finally, Sentence A concludes the parajumble by stating that Silky is deliciously different and exceptionally versatile for the aforementioned reasons. Going through the options, option ‘c’ has the correct sequence.",
                    solution_hi: "82.(c) CBA<br />Sentence C प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार  “Silky thick double ice cream has a unique, fresh taste and texture” का परिचय देता है। और Sentence B बताता है कि यह shape में बना रहती है और खूबसूरती से दृढ़ होने के कारण fruits या pastries के ऊपर दटी रहती है। इसलिए, C के बाद B आएगा। अंत में, Sentence A यह बताकर parajumble का समापन करता है कि उपर्युक्त कारणों से Silky स्वादिष्ट रूप से भिन्न और असाधारण रूप से versatile है। अतः options के माध्यम से जाने पर,  option ‘c’ में सही sequence है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate synonym of the given word. <br>Bully</p>",
                    question_hi: "<p>83. Select the most appropriate synonym of the given word. <br>Bully</p>",
                    options_en: ["<p>Plough</p>", "<p>Oxen</p>", 
                                "<p>Coddle</p>", "<p>Oppressor</p>"],
                    options_hi: ["<p>Plough</p>", "<p>Oxen</p>",
                                "<p>Coddle</p>", "<p>Oppressor</p>"],
                    solution_en: "<p>83.(d) <strong>Oppressor</strong>- one who exercises harsh control or authority over others.<br><strong>Bully</strong>- someone who intimidates people, usually those who are weaker or smaller.<br><strong>Plough</strong>- a farming tool used for cutting, lifting, and turning over soil.<br><strong>Oxen</strong>- domesticated cattle, typically used to pull ploughs.<br><strong>Coddle</strong>- to treat someone in an overly protective or indulgent manner.</p>",
                    solution_hi: "<p>83.(d) <strong>Oppressor </strong>(अत्याचारी)- one who exercises harsh control or authority over others.<br><strong>Bully </strong>(धौंसिया/दबंग)- someone who intimidates people, usually those who are weaker or smaller.<br><strong>Plough </strong>(हल)- a farming tool used for cutting, lifting, and turning over soil.<br><strong>Oxen </strong>(बैल/वृषभ)- domesticated cattle, typically used to pull ploughs.<br><strong>Coddle </strong>(लाड़ प्यार करना)- to treat someone in an overly protective or indulgent manner.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate synonym of the given word. <br>Thankful</p>",
                    question_hi: "<p>84. Select the most appropriate synonym of the given word. <br>Thankful</p>",
                    options_en: ["<p>Ungrateful</p>", "<p>Affirmation</p>", 
                                "<p>Obliged</p>", "<p>Obscure</p>"],
                    options_hi: ["<p>Ungrateful</p>", "<p>Affirmation</p>",
                                "<p>Obliged</p>", "<p>Obscure</p>"],
                    solution_en: "<p>84.(c) <strong>Obliged</strong>- feeling indebted or grateful.<br><strong>Thankful</strong>- expressing gratitude or appreciation.<br><strong>Ungrateful</strong>- not showing or expressing thanks.<br><strong>Affirmation</strong>- a positive assertion or confirmation.<br><strong>Obscure</strong>- not clear or difficult to understand.</p>",
                    solution_hi: "<p>84.(c) <strong>Obliged </strong>(कृतज्ञ होना)- feeling indebted or grateful.<br><strong>Thankful </strong>(आभारी)- expressing gratitude or appreciation.<br><strong>Ungrateful </strong>(अहसान फरामोश)- not showing or expressing thanks.<br><strong>Affirmation </strong>(पुष्टिकरण)- a positive assertion or confirmation.<br><strong>Obscure </strong>(अस्पष्ट)- not clear or difficult to understand.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "85. Select the most appropriate option to fill in the blank.  <br />Ram went to _____ an ice cream.",
                    question_hi: "85. Select the most appropriate option to fill in the blank.  <br />Ram went to _____ an ice cream.",
                    options_en: [" buy", " by", 
                                " byre", " bye"],
                    options_hi: [" buy", " by",
                                " byre", " bye"],
                    solution_en: "85.(a) buy<br />The given sentence states that Ram went to buy an ice cream. Hence, ‘buy’ is the most appropriate answer.",
                    solution_hi: "85.(a) buy<br />दिए गए sentence में कहा गया है कि राम ice cream खरीदने गया था। अतः, ‘buy’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "86. The following sentence has been split into four segments. Identify the segment that contains an error. <br />Dr. Sinha has / brought about a new / book on social / impact of child labour. ",
                    question_hi: "86. The following sentence has been split into four segments. Identify the segment that contains an error. <br />Dr. Sinha has / brought about a new / book on social / impact of child labour. ",
                    options_en: [" impact of child labour", " book on social ", 
                                " brought about a new", " Dr. Sinha has"],
                    options_hi: [" impact of child labour", " book on social ",
                                " brought about a new", " Dr. Sinha has"],
                    solution_en: "86.(c) brought about a new<br />‘Brought out’ is the correct phrasal verb here, which means ‘published’. The given sentence is talking about publishing a new book. Hence, ‘brought out a new’ is the most appropriate answer.",
                    solution_hi: "86.(c) brought about a new<br />‘Brought out’ यहाँ सही phrasal verb है, जिसका अर्थ है ‘प्रकाशित करना’। दिया गया sentence एक नई पुस्तक के प्रकाशन के बारे में बात कर रहा है। अतः, ‘brought out a new’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "87. Select the option that expresses the given sentence in passive voice. <br />His behaviour vexes me sometimes.  ",
                    question_hi: "87. Select the option that expresses the given sentence in passive voice. <br />His behaviour vexes me sometimes.  ",
                    options_en: [" He is being vexing me with his behaviour.", " Sometimes he vexes me with his behaviour.", 
                                " I am sometimes vexed by his behaviour.", " His behaviour is vexed."],
                    options_hi: [" He is being vexing me with his behaviour", " Sometimes he vexes me with his behaviour.",
                                " I am sometimes vexed by his behaviour.", " His behaviour is vexed."],
                    solution_en: "87.(c) I am sometimes vexed by his behaviour. (Correct)<br />(a) He is being vexing me with his behaviour. (Incorrect Sentence Structure)<br />(b) Sometimes he vexes me with his behaviour. (Incorrect Sentence Structure)<br />(d) His behaviour is vexed. (Incorrect Sentence Structure)",
                    solution_hi: "87.(c) I am sometimes vexed by his behaviour. (Correct)<br />(a) He is being vexing me with his behaviour. (गलत Sentence Structure)<br />(b) Sometimes he vexes me with his behaviour. (गलत Sentence Structure)<br />(d) His behaviour is vexed. (गलत Sentence Structure)",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the sentence that contains a spelling error.</p>",
                    question_hi: "<p>88. Select the sentence that contains a spelling error.</p>",
                    options_en: ["<p>The host institution must provide the delegates with accomodation at a subsidised rate.</p>", "<p>The occurrence of the lunar eclipse is often a mystery for children.</p>", 
                                "<p>The boss addressed the meeting.</p>", "<p>Rail lines go parallel.</p>"],
                    options_hi: ["<p>The host institution must provide the delegates with accomodation at a subsidised rate.</p>", "<p>The occurrence of the lunar eclipse is often a mystery for children.</p>",
                                "<p>The boss addressed the meeting.</p>", "<p>Rail lines go parallel.</p>"],
                    solution_en: "<p>88.(a) The host institution must provide the delegates with <span style=\"text-decoration: underline;\">accomodation</span> at a subsidised rate.<br>&lsquo;Accommodation&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>88.(a) The host institution must provide the delegates with <span style=\"text-decoration: underline;\">accomodation</span> at a subsidised rate.<br>&lsquo;Accommodation&rsquo; सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate synonym of the given word. <br>Labour</p>",
                    question_hi: "<p>89. Select the most appropriate synonym of the given word. <br>Labour</p>",
                    options_en: ["<p>Mesh</p>", "<p>Toil</p>", 
                                "<p>Dalliance</p>", "<p>Relaxation</p>"],
                    options_hi: ["<p>Mesh</p>", "<p>Toil</p>",
                                "<p>Dalliance</p>", "<p>Relaxation</p>"],
                    solution_en: "<p>89.(b) <strong>Toil</strong>- hard and continuous work.<br><strong>Labour</strong>- physical or mental effort.<br><strong>Mesh</strong>- material made of a network of wire or thread.<br><strong>Dalliance</strong>- a casual or non-serious involvement.<br><strong>Relaxation</strong>- a state of rest or ease.</p>",
                    solution_hi: "<p>89.(b) <strong>Toil </strong>(कठिन परिश्रम)- hard and continuous work.<br><strong>Labour </strong>(श्रम)- physical or mental effort.<br><strong>Mesh </strong>(जाल)- material made of a network of wire or thread.<br><strong>Dalliance </strong>(विलास)- a casual or non-serious involvement.<br><strong>Relaxation </strong>(विश्राम/आराम)- a state of rest or ease.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>To the general public, William was best known as a crusader<span style=\"text-decoration: underline;\"> for peace and as an admired critic about</span> social, political and ethical subjects.</p>",
                    question_hi: "<p>90. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>To the general public, William was best known as a crusader <span style=\"text-decoration: underline;\">for peace and as an admired critic about</span> social, political and ethical subjects.</p>",
                    options_en: ["<p>for peace and as an admired critic by</p>", "<p>for peace and as an admired critic on</p>", 
                                "<p>about peace and as an admired critic about</p>", "<p>in peace and as an admired critic about</p>"],
                    options_hi: ["<p>for peace and as an admired critic by</p>", "<p>for peace and as an admired critic on</p>",
                                "<p>about peace and as an admired critic about</p>", "<p>in peace and as an admired critic about</p>"],
                    solution_en: "<p>90.(b) for peace and as an admired critic on<br>&lsquo;On&rsquo; is generally used to indicate the topic or subject of discussion. Hence, &lsquo;on&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(b) for peace and as an admired critic on<br>&lsquo;On&rsquo; का प्रयोग सामान्यतः discussion के topic या subject को indicate करने के लिए किया जाता है। अतः, &lsquo;on&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate meaning of the underlined idiom. <br>In those days, we did not expect luxuries; we were thankful if we could <span style=\"text-decoration: underline;\">keep the wolf from the door</span>.</p>",
                    question_hi: "<p>91. Select the most appropriate meaning of the underlined idiom. <br>In those days, we did not expect luxuries; we were thankful if we could <span style=\"text-decoration: underline;\">keep the wolf from the door</span>.</p>",
                    options_en: ["<p>Avoid starvation</p>", "<p>Wallow in poverty</p>", 
                                "<p>Relish food</p>", "<p>Encourage starvation</p>"],
                    options_hi: ["<p>Avoid starvation</p>", "<p>Wallow in poverty</p>",
                                "<p>Relish food</p>", "<p>Encourage starvation</p>"],
                    solution_en: "<p>91.(a) <strong>Keep the wolf from the door</strong>- avoid starvation.</p>",
                    solution_hi: "<p>91.(a) <strong>Keep the wolf from the door</strong>- avoid starvation./भुखमरी से बचना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate ANTONYM of the given word. <br>Entire</p>",
                    question_hi: "<p>92. Select the most appropriate ANTONYM of the given word. <br>Entire</p>",
                    options_en: ["<p>Ingress</p>", "<p>Thorough</p>", 
                                "<p>Incomplete</p>", "<p>Undivided</p>"],
                    options_hi: ["<p>Ingress</p>", "<p>Thorough</p>",
                                "<p>Incomplete</p>", "<p>Undivided</p>"],
                    solution_en: "<p>92.(c) <strong>Incomplete</strong>- lacking some parts or elements.<br><strong>Entire</strong>- whole and complete.<br><strong>Ingress</strong>- the act of entering.<br><strong>Thorough</strong>- detailed and comprehensive.<br><strong>Undivided</strong>- not separated or broken into parts.</p>",
                    solution_hi: "<p>92.(c) <strong>Incomplete </strong>(अपूर्ण/अधूरा)- lacking some parts or elements.<br><strong>Entire </strong>(पूर्ण/सम्पूर्ण)- whole and complete.<br><strong>Ingress </strong>(प्रवेश)- the act of entering.<br><strong>Thorough </strong>(विस्तृत)- detailed and comprehensive.<br><strong>Undivided </strong>(अविभाजित)- not separated or broken into parts.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate option that can substitute the underlined segment in the given sentence. If no substitution is required, select &lsquo;No substitution&rsquo;. <br>No matter how <span style=\"text-decoration: underline;\">hard work you</span>, your boss will not recognise your efforts.</p>",
                    question_hi: "<p>93. Select the most appropriate option that can substitute the underlined segment in the given sentence. If no substitution is required, select &lsquo;No substitution&rsquo;. <br>No matter how <span style=\"text-decoration: underline;\">hard work you</span>, your boss will not recognise your efforts.</p>",
                    options_en: [" hard you work", " work you do hard", 
                                " hardly you work", " no substitution"],
                    options_hi: [" hard you work", " work you do hard",
                                " hardly you work", " no substitution"],
                    solution_en: "93.(a) hard you work<br />‘Subject + main verb’ is the correct order for the sentence. ‘You’ is the subject and ‘work’ is its main verb. Hence, ‘hard you work’ is the most appropriate answer.",
                    solution_hi: "93.(a) hard you work<br />Sentence का सही order ‘subject + main verb’ है। ‘You’ subject है और ‘work’ इसकी main verb है। अतः, ‘hard you work’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>My cousin works <span style=\"text-decoration: underline;\">day and night</span> to achieve success in life.</p>",
                    question_hi: "<p>94. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>My cousin works day and night to achieve success in life.</p>",
                    options_en: ["<p>vale of tears</p>", "<p>around the clock</p>", 
                                "<p>great dealing</p>", "<p>a handful</p>"],
                    options_hi: ["<p>vale of tears</p>", "<p>around the clock</p>",
                                "<p>great dealing</p>", "<p>a handful</p>"],
                    solution_en: "<p>94.(b) Around the clock (idiom) - day and night.</p>",
                    solution_hi: "<p>94.(b) Around the clock (idiom) - day and night./दिन-रात।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the word OPPOSITE in meaning to the underlined word in the given sentence. <br>Feeling <span style=\"text-decoration: underline;\">discouraged</span>, she finally finds the motivation for pursuing her goal.</p>",
                    question_hi: "<p>95. Select the word OPPOSITE in meaning to the underlined word in the given sentence. <br>Feeling discouraged, she finally finds the motivation for pursuing her goal.</p>",
                    options_en: ["<p>inspired</p>", "<p>energetic</p>", 
                                "<p>admired</p>", "<p>disappointed</p>"],
                    options_hi: ["<p>inspired</p>", "<p>energetic</p>",
                                "<p>admired</p>", "<p>disappointed</p>"],
                    solution_en: "<p>95.(a) <strong>Inspired</strong>- filled with motivation or creativity.<br><strong>Discouraged</strong>- lacking confidence or hope.<br><strong>Energetic</strong>- full of energy and enthusiasm.<br><strong>Admired</strong>- regarded with respect or approval.<br><strong>Disappointed</strong>- saddened by unmet expectations.</p>",
                    solution_hi: "<p>95.(a) <strong>Inspired </strong>(प्रेरित)- filled with motivation or creativity.<br><strong>Discouraged </strong>(हतोत्साहित)- lacking confidence or hope.<br><strong>Energetic </strong>(ऊर्जावान)- full of energy and enthusiasm.<br><strong>Admired </strong>(प्रशंसित)- regarded with respect or approval.<br><strong>Disappointed </strong>(निराश)- saddened by unmet expectations.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:</strong> <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong> <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: ["<p>better</p>", "<p>best</p>", 
                                "<p>strong</p>", "<p>more</p>"],
                    options_hi: ["<p>better</p>", "<p>best</p>",
                                "<p>strong</p>", "<p>more</p>"],
                    solution_en: "<p>96.(d) more<br>&lsquo;More&rsquo; is a comparative adjective used to make a comparison between two things. Similarly, in the given passage, there is a comparison between two dreams. One dream is more pertinent than the other. Hence, &lsquo;more&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) more<br>&lsquo;More&rsquo; एक comparative adjective है जिसका प्रयोग दो चीजों के बीच तुलना करने के लिए किया जाता है। इसी तरह, दिए गए passage में, दो dreams के बीच तुलना की गई है। एक dream दूसरे से अधिक प्रासंगिक (pertinent) है। अतः, &lsquo;more&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong> <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong> <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: ["<p>realise</p>", "<p>praise</p>", 
                                "<p>offer</p>", "<p>promise</p>"],
                    options_hi: ["<p>realise</p>", "<p>praise</p>",
                                "<p>offer</p>", "<p>promise</p>"],
                    solution_en: "<p>97.(a) realise<br>&lsquo;Realise&rsquo; means to become fully aware of something. The given passage states that I suddenly realise that there is a class I forgot to attend. Hence, &lsquo;realise&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) realise<br>&lsquo;Realise&rsquo; का अर्थ है किसी के बारे में पूरी तरह से जागरूक होना। दिए गए passage में बताया गया है कि मुझे अचानक एहसास होता है कि एक class है जिसमें मैं जाना भूल गया था। अतः, &lsquo;realise&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong> <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong> <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: ["<p>unpredictability</p>", "<p>consistently</p>", 
                                "<p>conflictingly</p>", "<p>clumsily</p>"],
                    options_hi: ["<p>unpredictability</p>", "<p>consistently</p>",
                                "<p>conflictingly</p>", "<p>clumsily</p>"],
                    solution_en: "<p>98.(b) consistently<br>&lsquo;Consistently&rsquo; means continuously in the same manner. The given passage states a question that the person is asking why he so consistently dissatisfies himself. Hence, &lsquo;consistently&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) consistently<br>&lsquo;Consistently&rsquo; का अर्थ है लगातार एक ही तरीके से। दिए गए passage में एक प्रश्न पूछा गया है कि वह लगातार खुद को असंतुष्ट क्यों रखता है। अतः, &lsquo;consistently&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong> <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong> <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: ["<p>instigate</p>", "<p>distant</p>", 
                                "<p>remote</p>", "<p>bleak</p>"],
                    options_hi: ["<p>instigate</p>", "<p>distant</p>",
                                "<p>remote</p>", "<p>bleak</p>"],
                    solution_en: "<p>99.(c) remote<br>&lsquo;Remote&rsquo; means slight. The given passage states that someone with slight knowledge of my academic career might point out that this nightmare scenario is not far removed from my actual collegiate experience. Hence, &lsquo;remote&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(c) remote<br>&lsquo;Remote&rsquo; का अर्थ है मामूली या थोड़ी सी। दिए गए passage में कहा गया है कि मेरे academic career के बारे में थोड़ा-बहुत जानने वाला कोई व्यक्ति यह बता सकता है कि यह दुःस्वप्न परिदृश्य (nightmare scenario) मेरे वास्तविक college ke अनुभव से बहुत दूर नहीं है। अतः, &lsquo;remote&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong> <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank. <br>I have a recurring dream. Actually, I have a few: one is about dismembering a body , I&rsquo;d rather not get into it, but the (96) ________ pertinent one is about college. It&rsquo;s the end of the semester, and I suddenly (97) ________ that there is a class I forgot to attend, ever, and now I have to sit for the final exam. I wake up panicked, my GPA in peril. How could I have done this? Why do I so (98) ________ dissatisfy myself? Then I remember I haven&rsquo;t been in college in more than a decade. <br>Someone with (99) ________ knowledge of my academic career might point out that this nightmare scenario is not that far removed from my actual collegiate experience, and that at certain times in my life, it did not take the magic of slumber to find me completely unprepared for a final. And, well, (100) ________ of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. <br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: ["<p>nevertheless</p>", "<p>notwithstanding</p>", 
                                "<p>regardless</p>", "<p>inasmuch</p>"],
                    options_hi: ["<p>nevertheless</p>", "<p>notwithstanding</p>",
                                "<p>regardless</p>", "<p>inasmuch</p>"],
                    solution_en: "<p>100.(c) regardless<br>&lsquo;Regardless&rsquo; means without regard or consideration for. The given passage states that regardless of what may or may not be true of my personal scholastic rigour, I suspect the schoolstress dream is quite a common one. Hence, &lsquo;regardless&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) regardless<br>&lsquo;Regardless&rsquo; का अर्थ है किसी चीज की परवाह किये बिना। दिए गए passage में कहा गया है कि इसकी परवाह किये बिना कि मेरी व्यक्तिगत शैक्षिक कठिनाई(rigour) के बारे में कुछ भी सच हो या न हो, मुझे शक है कि स्कूली तनाव का सपना (schoolstress dream) काफी common है। अतः, &lsquo;regardless\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>