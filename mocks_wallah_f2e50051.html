<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Choose the most appropriate ANTONYM for the underlined word in the given sentence. <br>The war was followed by a long period of <span style=\"text-decoration: underline;\">prosperity.</span></p>",
                    question_hi: "<p>1. Choose the most appropriate ANTONYM for the underlined word in the given sentence. <br>The war was followed by a long period of <span style=\"text-decoration: underline;\">prosperity.</span></p>",
                    options_en: ["<p>Security</p>", "<p>Secure</p>", 
                                "<p>Affluence</p>", "<p>Failure</p>"],
                    options_hi: ["<p>Security</p>", "<p>Secure</p>",
                                "<p>Affluence</p>", "<p>Failure</p>"],
                    solution_en: "<p>1.(d) <strong>Failure-</strong> lack of success.<br><strong>Prosperity-</strong> the state of being successful or flourishing.<br><strong>Security-</strong> the state of being free from danger or threat.<br><strong>Secure-</strong> to make safe or protect.<br><strong>Affluence-</strong> the state of having a great deal of wealth or abundance.</p>",
                    solution_hi: "<p>1.(d) <strong>Failure</strong> (असफलता)- lack of success.<br><strong>Prosperity</strong> (समृद्धि)- the state of being successful or flourishing.<br><strong>Security</strong> (सुरक्षा)- the state of being free from danger or threat.<br><strong>Secure</strong> (सुरक्षित)- to make safe or protect.<br><strong>Affluence</strong> (संपन्नता)- the state of having a great deal of wealth or abundance.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the option that can be used as a one-word substitute for the given phrase. <br>An important person who represents his/her country in a foreign country</p>",
                    question_hi: "<p>2. Select the option that can be used as a one-word substitute for the given phrase. <br>An important person who represents his/her country in a foreign country</p>",
                    options_en: ["<p>Amateur</p>", "<p>Ambassador</p>", 
                                "<p>Altruist</p>", "<p>Anonymous</p>"],
                    options_hi: ["<p>Amateur</p>", "<p>Ambassador</p>",
                                "<p>Altruist</p>", "<p>Anonymous</p>"],
                    solution_en: "<p>2.(b) <strong>Ambassador-</strong> an important person who represents his/her country in a foreign country.<br><strong>Amateur-</strong> a person who takes part in an activity for pleasure, not as a job.<br><strong>Altruist-</strong> a person who cares about others and helps them despite not gaining anything by doing this.<br><strong>Anonymous-</strong> of unknown authorship or origin.</p>",
                    solution_hi: "<p>2.(b) <strong>Ambassador</strong> (राजदूत)- an important person who represents his/her country in a foreign country.<br><strong>Amateur</strong> (शौकिया/शौकीन)- a person who takes part in an activity for pleasure, not as a job.<br><strong>Altruist</strong> (परोपकारी)- a person who cares about others and helps them despite not gaining anything by doing this.<br><strong>Anonymous</strong> (अज्ञात/गुमनाम)- of unknown authorship or origin.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the option that expresses the given sentence in passive voice. <br>Martha made pancakes yesterday.</p>",
                    question_hi: "<p>3. Select the option that expresses the given sentence in passive voice. <br>Martha made pancakes yesterday.</p>",
                    options_en: ["<p>Pancakes had been made by Martha yesterday.</p>", "<p>Pancakes were being made by Martha yesterday.</p>", 
                                "<p>Pancakes were made by Martha yesterday.</p>", "<p>Pancakes was made by Martha yesterday.</p>"],
                    options_hi: ["<p>Pancakes had been made by Martha yesterday.</p>", "<p>Pancakes were being made by Martha yesterday.</p>",
                                "<p>Pancakes were made by Martha yesterday.</p>", "<p>Pancakes was made by Martha yesterday.</p>"],
                    solution_en: "<p>3.(c) Pancakes were made by Martha yesterday. (Correct)<br>(a) Pancakes <span style=\"text-decoration: underline;\">had been made</span> by Martha yesterday. (Incorrect Tense)<br>(b) Pancakes <span style=\"text-decoration: underline;\">were being made</span> by Martha yesterday. (Incorrect Tense)<br>(d) Pancakes <span style=\"text-decoration: underline;\">was</span> made by Martha yesterday. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>3.(c) Pancakes were made by Martha yesterday. (Correct)<br>(a) Pancakes <span style=\"text-decoration: underline;\">had been made</span> by Martha yesterday. (गलत Tense)<br>(b) Pancakes <span style=\"text-decoration: underline;\">were being made</span> by Martha yesterday. (गलत Tense)<br>(d) Pancakes <span style=\"text-decoration: underline;\">was</span> made by Martha yesterday. (गलत Helping Verb)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "4. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement. <br />O: also benefit from knowing <br />P: there are several reasons <br />Q: why even parents would <br />R: more about how and why <br />S: children do the things that they do",
                    question_hi: "4. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement. <br />O: also benefit from knowing <br />P: there are several reasons <br />Q: why even parents would <br />R: more about how and why <br />S: children do the things that they do",
                    options_en: [" RQOPS ", " OPQSR  ", 
                                " PQORS ", " PQROS"],
                    options_hi: [" RQOPS ", " OPQSR  ",
                                " PQORS ", " PQROS"],
                    solution_en: "4.(c) PQORS<br />The given sentence starts with Part P as it introduces the main idea of the sentence, i.e. ‘There are several reasons’. Part Q starts the question related to parents and Part O continues the question by asking about the benefit from knowing something. So, O will follow Q. Further, Part R introduces another question by asking how and why & Part S continues the question by enquiring about the actions of children. So, S will follow R. Going through the options, option ‘c’ has the correct sequence.",
                    solution_hi: "4.(c) PQORS<br />दिया गया sentence, Part P से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार ‘There are several reasons’ का परिचय देता है। Part Q माता-पिता (parents) से संबंधित प्रश्न शुरू करता है और Part O कुछ जानने से होने वाले लाभ के बारे में पूछकर प्रश्न को जारी रखता है। इसलिए, Q के बाद O आएगा। इसके अलावा, Part R, how और why पूछकर एक और प्रश्न प्रस्तुत करता है और Part S बच्चों के actions के बारे में पूछताछ करके प्रश्न को जारी रखता है। अतः options के माध्यम से जाने पर,  option ‘c’ में सही sequence है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate meaning of the given idiom. <br>Radhika stood by Monica&rsquo;s side <span style=\"text-decoration: underline;\">through thick and thin</span>.</p>",
                    question_hi: "<p>5. Select the most appropriate meaning of the given idiom. <br>Radhika stood by Monica&rsquo;s side <span style=\"text-decoration: underline;\">through thick and thin</span>.</p>",
                    options_en: ["<p>Under easy situations wherein she could ensure Monica&rsquo;s safety and security</p>", "<p>In all the happy moments, personally spending most of her time</p>", 
                                "<p>Under all conditions, no matter how challenging or difficult</p>", "<p>In the moments when Monica couldn&rsquo;t complete some simple tasks </p>"],
                    options_hi: ["<p>Under easy situations wherein she could ensure Monica&rsquo;s safety and security</p>", "<p>In all the happy moments, personally spending most of her time</p>",
                                "<p>Under all conditions, no matter how challenging or difficult</p>", "<p>In the moments when Monica couldn&rsquo;t complete some simple tasks</p>"],
                    solution_en: "<p>5.(c) <strong>Through thick and thin- </strong>under all conditions, no matter how challenging or difficult.</p>",
                    solution_hi: "<p>5.(c)<strong> Through thick and thin-</strong> under all conditions, no matter how challenging or difficult./सभी परिस्थितियों में, चाहे कितनी भी चुनौतीपूर्ण हो।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Give one word substitution for : <br>A person who is unsure about God&rsquo;s existence</p>",
                    question_hi: "<p>6. Give one word substitution for : <br>A person who is unsure about God&rsquo;s existence</p>",
                    options_en: ["<p>Ignorant</p>", "<p>Atheist</p>", 
                                "<p>Agnostic</p>", "<p>Theist</p>"],
                    options_hi: ["<p>Ignorant</p>", "<p>Atheist</p>",
                                "<p>Agnostic</p>", "<p>Theist</p>"],
                    solution_en: "<p>6.(c) <strong>Agnostic-</strong> a person who is unsure about God&rsquo;s existence.<br><strong>Ignorant-</strong> having no knowledge or awareness of something or of things in general.<br><strong>Atheist-</strong> one who does not believe in the existence of god.<br><strong>Theist-</strong> one who believes in the existence of god.</p>",
                    solution_hi: "<p>6.(c) <strong>Agnostic</strong> (अज्ञेयवादी/संशयवादी)- a person who is unsure about God&rsquo;s existence.<br><strong>Ignorant</strong> (अज्ञानी)- having no knowledge or awareness of something or of things in general.<br><strong>Atheist</strong> (नास्तिक)- one who does not believe in the existence of god.<br><strong>Theist</strong> (आस्तिक)- one who believes in the existence of god.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate synonym of the underlined word in the given sentence. <br>His approach was filled with such <span style=\"text-decoration: underline;\">perseverance</span> that it was difficult to overlook his merit.</p>",
                    question_hi: "<p>7 Select the most appropriate synonym of the underlined word in the given sentence. <br>His approach was filled with such <span style=\"text-decoration: underline;\">perseverance</span> that it was difficult to overlook his merit.</p>",
                    options_en: ["<p>Ignorance</p>", "<p>Doggedness</p>", 
                                "<p>Reluctance</p>", "<p>Monotonous</p>"],
                    options_hi: ["<p>Ignorance</p>", "<p>Doggedness</p>",
                                "<p>Reluctance</p>", "<p>Monotonous</p>"],
                    solution_en: "<p>7.(b) <strong>Doggedness-</strong> persistence and determination.<br><strong>Perseverance-</strong> continued effort to achieve something despite difficulties.<br><strong>Ignorance-</strong> lack of knowledge or awareness.<br><strong>Reluctance-</strong> unwillingness or hesitation.<br><strong>Monotonous-</strong> dull, repetitive, and lacking variety.</p>",
                    solution_hi: "<p>7.(b) <strong>Doggedness</strong> (दृढ़ संकल्प)- persistence and determination.<br><strong>Perseverance</strong> (दृढता/अटलता)- continued effort to achieve something despite difficulties.<br><strong>Ignorance</strong> (अज्ञानता)- lack of knowledge or awareness.<br><strong>Reluctance</strong> (अनिच्छा)- unwillingness or hesitation.<br><strong>Monotonous</strong> (नीरस)- dull, repetitive, and lacking variety.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate option that can substitute the underlined part in the following sentence. Everyone called the reporter <span style=\"text-decoration: underline;\">a daredevil</span> after he came back safely from the war zone.</p>",
                    question_hi: "<p>8. Select the most appropriate option that can substitute the underlined part in the following sentence. Everyone called the reporter <span style=\"text-decoration: underline;\">a daredevil</span> after he came back safely from the war zone.</p>",
                    options_en: ["<p>a common man</p>", "<p>a fearless person</p>", 
                                "<p>an insignificant person</p>", "<p>a hypocrite</p>"],
                    options_hi: ["<p>a common man</p>", "<p>a fearless person</p>",
                                "<p>an insignificant person</p>", "<p>a hypocrite</p>"],
                    solution_en: "<p>8.(b) A daredevil- a fearless person.</p>",
                    solution_hi: "<p>8.(b) A daredevil- a fearless person./ निडर व्यक्ति।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Parts of the given sentence have been underlined. Identify the part that has an error. Select \'No error\' if there is no error. <br>Although the team had <span style=\"text-decoration: underline;\">trained diligently for months</span>, they <span style=\"text-decoration: underline;\">had still overwhelmed</span> by the opponent\'s unexpected tactics.</p>",
                    question_hi: "<p>9. Parts of the given sentence have been underlined. Identify the part that has an error. Select \'No error\' if there is no error. <br>Although the team had <span style=\"text-decoration: underline;\">trained diligently for months</span>, they <span style=\"text-decoration: underline;\">had still overwhelmed</span> by the opponent\'s unexpected tactics.</p>",
                    options_en: ["<p>No error</p>", "<p>had still overwhelmed</p>", 
                                "<p>trained diligently</p>", "<p>for months</p>"],
                    options_hi: ["<p>No error</p>", "<p>had still overwhelmed</p>",
                                "<p>trained diligently</p>", "<p>for months</p>"],
                    solution_en: "<p>9.(b) had still overwhelmed<br>&lsquo;Had&rsquo; must be replaced with &lsquo;were&rsquo; as the given sentence is an example of a passive voice of simple past tense and &lsquo;Plural subject (they) + were + V<sub>3</sub>&rsquo; is the correct grammatical structure for it. Hence, &lsquo;were still overwhelmed(V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(b) had still overwhelmed<br>&lsquo;Had&rsquo; के स्थान पर &lsquo;were&rsquo; का प्रयोग होगा, क्योंकि दिया गया sentence, simple past tense के passive voice का example है और इसके लिए सही grammatical structure, &lsquo;Plural subject (they) + were + V<sub>3</sub>&rsquo; है। अतः, &lsquo;were still overwhelmed(V<sub>3</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate ANTONYM of the underlined word. <br>If someone <span style=\"text-decoration: underline;\">babbles,</span> it means that they talk in a confused or excited way.</p>",
                    question_hi: "<p>10. Select the most appropriate ANTONYM of the underlined word. <br>If someone <span style=\"text-decoration: underline;\">babbles,</span> it means that they talk in a confused or excited way.</p>",
                    options_en: ["<p>Gossips</p>", "<p>Quiet</p>", 
                                "<p>Talkative</p>", "<p>Rants</p>"],
                    options_hi: ["<p>Gossips</p>", "<p>Quiet</p>",
                                "<p>Talkative</p>", "<p>Rants</p>"],
                    solution_en: "<p>10.(b) <strong>Quiet-</strong> making little or no noise.<br><strong>Babbles-</strong> the sound of people talking simultaneously.<br><strong>Gossips-</strong> light informal conversation for social occasions..<br><strong>Talkative-</strong> inclined to talk a great deal.<br><strong>Rants-</strong> speaks or shouts at length in a wild, impassioned way.</p>",
                    solution_hi: "<p>10.(b) <strong>Quiet</strong> (शांत)- making little or no noise.<br><strong>Babbles</strong> (बड़बड़ाहट)- the sound of people talking simultaneously.<br><strong>Gossips</strong> (गपशप)- light informal conversation for social occasions..<br><strong>Talkative</strong> (वाचाल)- inclined to talk a great deal.<br><strong>Rants</strong> (बड़बड़ाना)- speaks or shouts at length in a wild, impassioned way.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>Akash went to ______ his school uniform. [sell]</p>",
                    question_hi: "<p>11. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>Akash went to ______ his school uniform. [sell]</p>",
                    options_en: ["<p>loan</p>", "<p>rent</p>", 
                                "<p>buy</p>", "<p>make</p>"],
                    options_hi: ["<p>loan</p>", "<p>rent</p>",
                                "<p>buy</p>", "<p>make</p>"],
                    solution_en: "<p>11.(c) Buy<br><strong>Loan-</strong> a thing that is borrowed, especially a sum of money that is expected to be paid back with interest.<br><strong>Rent-</strong> a regular payment made by a tenant to an owner for the use of land, a building, or other property.</p>",
                    solution_hi: "<p>11.(c) Buy<br><strong>Loan</strong> (ऋण/कर्ज)- a thing that is borrowed, especially a sum of money that is expected to be paid back with interest.<br><strong>Rent</strong> (किराया)- a regular payment made by a tenant to an owner for the use of land, a building, or other property.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate option to substitute the underlined word segment in the given sentence. Carnatic music is usually performed by a <span style=\"text-decoration: underline;\">small troupe of actors</span>, consisting of a principal performer, a melodic accompaniment, and a rhythm accompaniment.</p>",
                    question_hi: "<p>12. Select the most appropriate option to substitute the underlined word segment in the given sentence. Carnatic music is usually performed by a <span style=\"text-decoration: underline;\">small troupe of actors</span>, consisting of a principal performer, a melodic accompaniment, and a rhythm accompaniment.</p>",
                    options_en: ["<p>board of directors</p>", "<p>ensemble of musicians</p>", 
                                "<p>panel of judges</p>", "<p>band of singers</p>"],
                    options_hi: ["<p>board of directors</p>", "<p>ensemble of musicians</p>",
                                "<p>panel of judges</p>", "<p>band of singers</p>"],
                    solution_en: "<p>12.(b) ensemble of musicians<br>&lsquo;Ensemble&rsquo; means a group of musicians, dancers, or actors who perform together. The given sentence talks about carnatic music, so the correct phrase to use is &lsquo;ensemble of musicians&rsquo;. Hence, &lsquo;ensemble of musicians&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(b) ensemble of musicians<br>&lsquo;Ensemble&rsquo; का अर्थ है musicians, dancers या actors का समूह जो एक साथ perform करते हैं। दिया गया sentence कर्नाटक संगीत के बारे में बात करता है, इसलिए उपयोग करने के लिए सही phrase, &lsquo;ensemble of musicians&rsquo; है। अतः, &lsquo;ensemble of musicians&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the option that can be used as a one-word substitute for the given group of words. <br>One who speaks less in a forum.</p>",
                    question_hi: "<p>13. Select the option that can be used as a one-word substitute for the given group of words. <br>One who speaks less in a forum.</p>",
                    options_en: ["<p>Eccentric</p>", "<p>Loquacious</p>", 
                                "<p>Gabby</p>", "<p>Reticent</p>"],
                    options_hi: ["<p>Eccentric</p>", "<p>Loquacious</p>",
                                "<p>Gabby</p>", "<p>Reticent</p>"],
                    solution_en: "<p>13.(d) <strong>Reticent-</strong> one who speaks less in a forum.<br><strong>Eccentric-</strong> a person of unconventional and slightly strange views or behaviour.<br><strong>Loquacious-</strong> full of excessive talk.<br><strong>Gabby-</strong> excessively or annoyingly talkative.</p>",
                    solution_hi: "<p>13.(d) <strong>Reticent</strong> (मितभाषी)- one who speaks less in a forum.<br><strong>Eccentric</strong> (विलक्षण/सनकी व्यक्ति)- a person of unconventional and slightly strange views or behaviour.<br><strong>Loquacious</strong> (वाचाल)- full of excessive talk.<br><strong>Gabby</strong> (बातूनी)- excessively or annoyingly talkative.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "14. Parts of a sentence are given below in jumbled order. Select the option that gives their correct logical sequence and forms a meaningful sentence. <br />In November, Lakshman fell (P) / with fever (Q) / seriously ill (R) and pneumonia (S). ",
                    question_hi: "14. Parts of a sentence are given below in jumbled order. Select the option that gives their correct logical sequence and forms a meaningful sentence. <br />In November, Lakshman fell (P) / with fever (Q) / seriously ill (R) and pneumonia (S). ",
                    options_en: [" P, Q, S, R ", " P, R, Q, S ", 
                                " P, Q, R, S ", " R, Q, S, P"],
                    options_hi: [" P, Q, S, R ", " P, R, Q, S ",
                                " P, Q, R, S ", " R, Q, S, P"],
                    solution_en: "14.(b) P, R, Q, S<br />The correct sentence is “In November, Lakshman fell seriously ill with fever and pneumonia.”",
                    solution_hi: "14.(b) P, R, Q, S<br /> “In November, Lakshman fell seriously ill with fever and pneumonia.” सही sentence है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. In the following sentence, four words are underlined out of which one word is misspelt. Identify the INCORRECTLY spelt word. <br>I felt <span style=\"text-decoration: underline;\">absolutely</span> (A) fit at the summit. My mind was absolutely <span style=\"text-decoration: underline;\">clear.(B)</span> I didn&rsquo;t feel tired, I felt <span style=\"text-decoration: underline;\">exhelarated.</span> (C)It was a very clear <span style=\"text-decoration: underline;\">sensation.</span> (D)</p>",
                    question_hi: "<p>15. In the following sentence, four words are underlined out of which one word is misspelt. Identify the INCORRECTLY spelt word. <br>I felt <span style=\"text-decoration: underline;\">absolutely</span> (A) fit at the summit. My mind was absolutely <span style=\"text-decoration: underline;\">clear.(B)</span> I didn&rsquo;t feel tired, I felt <span style=\"text-decoration: underline;\">exhelarated.</span> (C)It was a very clear <span style=\"text-decoration: underline;\">sensation.</span> (D)</p>",
                    options_en: ["<p>C</p>", "<p>D</p>", 
                                "<p>B</p>", "<p>A</p>"],
                    options_hi: ["<p>C</p>", "<p>D</p>",
                                "<p>B</p>", "<p>A</p>"],
                    solution_en: "<p>15.(a) C <span style=\"text-decoration: underline;\">exhelarated</span><br>\'Exhilarated\' is the correct spelling.</p>",
                    solution_hi: "<p>15.(a) C <span style=\"text-decoration: underline;\">exhelarated</span><br>\'Exhilarated\' सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. The following sentence has been divided into five segments. The first segment has no error. Select the option that has the segment with the grammatical error. <br>Due to the pandemic, / the prices of / medicines increased / every day since / last December.</p>",
                    question_hi: "<p>16. The following sentence has been divided into five segments. The first segment has no error. Select the option that has the segment with the grammatical error. <br>Due to the pandemic, / the prices of / medicines increased / every day since / last December.</p>",
                    options_en: ["<p>every day since</p>", "<p>medicines increased</p>", 
                                "<p>last December.</p>", "<p>the prices of</p>"],
                    options_hi: ["<p>every day since</p>", "<p>medicines increased</p>",
                                "<p>last December.</p>", "<p>the prices of</p>"],
                    solution_en: "<p>16.(b) medicines increased<br>We use present continuous tense with &lsquo;for/since&rsquo; to show that something has been happening for a period of time. &lsquo;Plural subject (prices) + have been + V<sub>ing</sub>&rsquo; is the correct grammatical structure for it. Hence, &lsquo;medicines have been increasing&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>16.(b) medicines increased<br>\'For/since\' के साथ present continuous tense का प्रयोग यह दर्शाने के लिए किया जाता हैं कि कोई चीज कुछ समय से घटित हो रही है। इसके लिए सही grammatical structure, &lsquo;Plural subject (prices) + have been + V<sub>ing</sub>&rsquo; है। अतः, &lsquo;medicines have been increasing&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the option that expresses the given sentence in active voice. <br>I will be gifted a fancy doll by them the next day.</p>",
                    question_hi: "<p>17. Select the option that expresses the given sentence in active voice. <br>I will be gifted a fancy doll by them the next day.</p>",
                    options_en: ["<p>They gifted me a fancy doll yesterday.</p>", "<p>They are gifting me a fancy doll the next day.</p>", 
                                "<p>They gift me a fancy doll the previous day.</p>", "<p>They will gift me a fancy doll tomorrow.</p>"],
                    options_hi: ["<p>They gifted me a fancy doll yesterday.</p>", "<p>They are gifting me a fancy doll the next day.</p>",
                                "<p>They gift me a fancy doll the previous day.</p>", "<p>They will gift me a fancy doll tomorrow.</p>"],
                    solution_en: "<p>17.(d) They will gift me a fancy doll tomorrow. (Correct)<br>(a) They <span style=\"text-decoration: underline;\">gifted</span> me a fancy doll <span style=\"text-decoration: underline;\">yesterday.</span> (Incorrect Tense &amp; Adverb)<br>(b) They <span style=\"text-decoration: underline;\">are gifting</span> me a fancy doll <span style=\"text-decoration: underline;\">the next day</span>. (Incorrect Tense &amp; Adverb)<br>(c) They <span style=\"text-decoration: underline;\">gift</span> me a fancy doll <span style=\"text-decoration: underline;\">the previous day</span>. (Incorrect Tense &amp; Adverb)</p>",
                    solution_hi: "<p>17.(d) They will gift me a fancy doll tomorrow. (Correct)<br>(a) They <span style=\"text-decoration: underline;\">gifted</span> me a fancy doll <span style=\"text-decoration: underline;\">yesterday.</span> (गलत Tense एवं Adverb)<br>(b) They <span style=\"text-decoration: underline;\">are gifting</span> me a fancy doll <span style=\"text-decoration: underline;\">the next day</span>. (गलत Tense एवं Adverb)<br>(c) They <span style=\"text-decoration: underline;\">gift</span> me a fancy doll <span style=\"text-decoration: underline;\">the previous day</span>. (गलत Tense एवं Adverb)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "18. Select the most appropriate option that can rectify the incorrectly spelt word in the following sentence. The government has ordered for the maintainance of the roads. ",
                    question_hi: "18. Select the most appropriate option that can rectify the incorrectly spelt word in the following sentence. The government has ordered for the maintainance of the roads. ",
                    options_en: [" maintenance", " maintainence ", 
                                " maintainiance", " maintennance"],
                    options_hi: [" maintenance", " maintainence ",
                                " maintainiance", " maintennance"],
                    solution_en: "18.(a) maintenance<br />\'Maintenance\' is the correct spelling.",
                    solution_hi: "18.(a) maintenance<br />\'Maintenance\' सही spelling है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate meaning of the given word. <br>Illiterate</p>",
                    question_hi: "<p>19. Select the most appropriate meaning of the given word. <br>Illiterate</p>",
                    options_en: ["<p>A person who cannot speak</p>", "<p>One who is well-educated</p>", 
                                "<p>A person who cannot read and write</p>", "<p>Highly rated thing</p>"],
                    options_hi: ["<p>A person who cannot speak</p>", "<p>One who is well-educated</p>",
                                "<p>A person who cannot read and write</p>", "<p>Highly rated thing</p>"],
                    solution_en: "<p>19.(c) <strong>Illiterate-</strong> a person who cannot read and write.</p>",
                    solution_hi: "<p>19.(c) <strong>Illiterate-</strong> a person who cannot read and write./अनपढ़ ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the most appropriate ANTONYM of the given word. <br>Affirmative</p>",
                    question_hi: "<p>20. Select the most appropriate ANTONYM of the given word. <br>Affirmative</p>",
                    options_en: ["<p>Sanction</p>", "<p>Negative</p>", 
                                "<p>Acquiescence</p>", "<p>Accession</p>"],
                    options_hi: ["<p>Sanction</p>", "<p>Negative</p>",
                                "<p>Acquiescence</p>", "<p>Accession</p>"],
                    solution_en: "<p>20.(b) <strong>Negative</strong><br><strong>Affirmative-</strong> positive or showing agreement.<br><strong>Sanction-</strong> official permission or approval for an action.<br><strong>Acquiescence-</strong> passive acceptance or submission without protest.<br><strong>Accession-</strong> the act of agreeing or consenting to a demand, request, or treaty.</p>",
                    solution_hi: "<p>20.(b) <strong>Negative</strong><br><strong>Affirmative</strong> (सकारात्मक)- positive or showing agreement.<br><strong>Sanction</strong> (स्वीकृति)- official permission or approval for an action.<br><strong>Acquiescence</strong> (सहमति)- passive acceptance or submission without protest.<br><strong>Accession</strong> (परिग्रहण-)- the act of agreeing or consenting to a demand, request, or treaty.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze Test:&nbsp;</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>When</p>", "<p>Sooner</p>", 
                                "<p>For</p>", "<p>Unless</p>"],
                    options_hi: ["<p>When</p>", "<p>Sooner</p>",
                                "<p>For</p>", "<p>Unless</p>"],
                    solution_en: "<p>21.(a) When<br>&lsquo;When&rsquo; is a relative adverb used to indicate the time of an action or event. Similarly, in the given passage, it tells us the time of the event &lsquo;Nanda kaul pursing her lips&rsquo;. Hence, &lsquo;When&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(a) When<br>&lsquo;When&rsquo; एक relative adverb है जिसका use किसी action) या event के समय को इंगित करने के लिए किया जाता है। इसी तरह, दिए गए passage में, यह Nanda kaul के होंठ सिकुड़ने के action का समय बताता है। अतः, &lsquo;When&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:&nbsp;</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>have</p>", "<p>has</p>", 
                                "<p>had</p>", "<p>having</p>"],
                    options_hi: ["<p>have</p>", "<p>has</p>",
                                "<p>had</p>", "<p>having</p>"],
                    solution_en: "<p>22.(d) having<br>&lsquo;Having + V<sub>3</sub>&rsquo; is a perfect participle used to denote an action happening immediately after it. Similarly, in the given passage, the action &ldquo;flying to Switzerland&rdquo; happens immediately after the action &ldquo;seeing another grandchild&rdquo;. Hence, &lsquo;having&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(d) having<br>&lsquo;Having + V<sub>3</sub>&rsquo; एक perfect participle है जिसका प्रयोग इसके तुरंत बाद होने वाले action को दर्शाने के लिए किया जाता है। इसी प्रकार, दिए गए passage में, action &ldquo;seeing another grandchild&rdquo; के तुरंत बाद action &ldquo;flying to Switzerland&rdquo; होता है। अतः, &lsquo;having&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:&nbsp;</strong><br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>stuck</p>", "<p>dirt</p>", 
                                "<p>dusted</p>", "<p>pursed</p>"],
                    options_hi: ["<p>stuck</p>", "<p>dirt</p>",
                                "<p>dusted</p>", "<p>pursed</p>"],
                    solution_en: "<p>23.(d) pursed<br>&lsquo;Purse&rsquo; means to bring your lips tightly together so that they form a rounded shape, usually as an expression of disapproval. The given passage states that Nana Kaul pursed her lips. Hence, &lsquo;pursed&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(d) pursed<br>&lsquo;Purse&rsquo; का अर्थ है गोल आकार में होठ को सिकोड़ना, जो आमतौर पर अस्वीकृति (disapproval) का expression होता है। दिए गए passage में कहा गया है कि Nana Kaul ने अपने होठों को सिकोड़ा। अतः, &lsquo;pursed&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24.<strong> Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>folded up</p>", "<p>cleared up</p>", 
                                "<p>hid away</p>", "<p>tucked away</p>"],
                    options_hi: ["<p>folded up</p>", "<p>cleared up</p>",
                                "<p>hid away</p>", "<p>tucked away</p>"],
                    solution_en: "<p>24.(a) folded up<br>The given passage states that Nanda Kaul folded up the blue sheets of paper with that distasteful sprawl across them. Hence, &lsquo;folded up&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(a) folded up<br>दिए गए passage में बताया गया है कि Nanda Kaul ने कागज़ की blue sheets को उस अप्रिय फैलाव के साथ मोड़ दिया। अतः, &lsquo;folded up&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25. <strong>Cloze Test:</strong> <br>In the following passage, some words have been deleted. Read the passage carefully and select the most appropriate option to fill in each blank.<br>(21) ______a letter came to inform them that Raka&rsquo;s mother Tara had another breakdown and was in a nursing home in Geneva and that Raka&rsquo;s grandmother Asha, (22) ______seen another grandchild safely into the world, was flying to Switzerland to be with her, Nanda Kaul (23) _______ her lips, (24) _______ the blue sheets of paper with that distasteful sprawl (25) ______them, and hid them in her desk. <br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>across</p>", "<p>at</p>", 
                                "<p>in</p>", "<p>by</p>"],
                    options_hi: ["<p>across</p>", "<p>at</p>",
                                "<p>in</p>", "<p>by</p>"],
                    solution_en: "<p>25.(a) across<br>&lsquo;Across&rsquo; means from one side to the other. The given passage states that Nana Kaul pursed her lips, folded up the blue sheets of paper with that distasteful sprawl across them, and hid them in her desk. Hence, &lsquo;across&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(a) across<br>&lsquo;Across&rsquo; का अर्थ है एक तरफ से दूसरी तरफ। दिए गए passage में बताया गया है कि Nana Kaul ने अपने होठों को सिकोड़ा, कागज़ की blue sheets को उस अप्रिय फैलाव के साथ मोड़ा और उन्हें अपनी मेज़ में छिपा दिया। अतः, &lsquo;across&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>