<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340660802.png\" alt=\"rId4\" width=\"124\" height=\"77\"></p>",
                    question_hi: "<p>1. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) उसके भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340660802.png\" alt=\"rId4\" width=\"124\" height=\"77\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340660919.png\" alt=\"rId5\" width=\"100\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661038.png\" alt=\"rId6\" width=\"100\" height=\"99\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661150.png\" alt=\"rId7\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661245.png\" alt=\"rId8\" width=\"100\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340660919.png\" alt=\"rId5\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661038.png\" alt=\"rId6\" width=\"100\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661150.png\" alt=\"rId7\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661245.png\" alt=\"rId8\" width=\"100\"></p>"],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661348.png\" alt=\"rId9\" width=\"101\" height=\"86\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661348.png\" alt=\"rId9\" width=\"101\" height=\"86\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. What should come in place of the question mark (?) in the given series based on the English alphabetical order?<br>LDP, GHO, BLN, WPM, ?</p>",
                    question_hi: "<p>2. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न-चिह्न (?) के स्थान पर आना चाहिए?<br>LDP, GHO, BLN, WPM, ?</p>",
                    options_en: ["<p>RSN</p>", "<p>QSM</p>", 
                                "<p>RTL</p>", "<p>QSN</p>"],
                    options_hi: ["<p>RSN</p>", "<p>QSM</p>",
                                "<p>RTL</p>", "<p>QSN</p>"],
                    solution_en: "<p>2.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661456.png\" alt=\"rId10\" width=\"399\" height=\"125\"></p>",
                    solution_hi: "<p>2.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661456.png\" alt=\"rId10\" width=\"399\" height=\"125\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. If \'A\' stands for \'&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for &lsquo;-\', what will come in place of the question mark (?) in the following equation?<br>16 B 8 D 51 A 3 C 57 = ?</p>",
                    question_hi: "<p>3. यदि \'A\' का अर्थ &divide;\'&shy;\', \'B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>16 B 8 D 51 A 3 C 57 = ?</p>",
                    options_en: ["<p>168</p>", "<p>188</p>", 
                                "<p>148</p>", "<p>128</p>"],
                    options_hi: ["<p>168</p>", "<p>188</p>",
                                "<p>148</p>", "<p>128</p>"],
                    solution_en: "<p>3.(a) <strong>Given</strong> :- 16 B 8 D 51 A 3 C 57<br>As per given instruction after interchanging the letter with sign we get<br>16 &times; 8 - 51 &divide; 3 + 57<br>128 - 17 + 57 = 168</p>",
                    solution_hi: "<p>3.(a) <strong>दिया गया :- </strong>16<strong> </strong>B 8 D 51 A 3 C 57<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>16 &times; 8 - 51 &divide; 3 + 57<br>128 - 17 + 57 = 168</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the correct mirror image of the given figure when the mirror is placed at MN. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661570.png\" alt=\"rId11\" width=\"119\" height=\"118\"></p>",
                    question_hi: "<p>4. जब दर्पण को MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661570.png\" alt=\"rId11\" width=\"119\" height=\"118\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661682.png\" alt=\"rId12\" width=\"100\" height=\"34\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661775.png\" alt=\"rId13\" width=\"100\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661863.png\" alt=\"rId14\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661955.png\" alt=\"rId15\" width=\"100\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661682.png\" alt=\"rId12\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661775.png\" alt=\"rId13\" width=\"100\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661863.png\" alt=\"rId14\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661955.png\" alt=\"rId15\" width=\"100\"></p>"],
                    solution_en: "<p>4.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661863.png\" alt=\"rId14\" width=\"100\"></p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340661863.png\" alt=\"rId14\" width=\"100\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language, FIGURE is coded as &lsquo;VSUGJW&rsquo; and COPIED is coded as &lsquo;YMLSWX&rsquo;. What will be the code for BLANKET ?</p>",
                    question_hi: "<p>5. एक निश्चित कूट भाषा में, FIGURE को \'VSUGJW\' के रूप में कूटबद्ध किया जाता है और COPIED को \'YMLSWX\' के रूप में कूटबद्ध किया जाता है। तो उसी कूट भाषा में BLANKET के लिए कूट क्या होगा?</p>",
                    options_en: ["<p>ZPANQWH</p>", "<p>ZPMNQHW</p>", 
                                "<p>ZPANQHW</p>", "<p>ZPNAQWH</p>"],
                    options_hi: ["<p>ZPANQWH</p>", "<p>ZPMNQHW</p>",
                                "<p>ZPANQHW</p>", "<p>ZPNAQWH</p>"],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340662098.png\" alt=\"rId16\" width=\"220\"> &nbsp;and<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340662292.png\" alt=\"rId17\" width=\"220\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340662456.png\" alt=\"rId18\" width=\"220\"></p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340662654.png\" alt=\"rId19\" width=\"220\"> और<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340662793.png\" alt=\"rId20\" width=\"220\"><br>इसी प्रकार <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340662976.png\" alt=\"rId21\" width=\"220\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. The sequence of folding a piece of paper and the manner in which the folded paper is punched is shown in the following figures. How would this paper look when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663171.png\" alt=\"rId22\" width=\"263\" height=\"81\"></p>",
                    question_hi: "<p>6. निम्नलिखित आकृतियों में एक कागज़ को मोड़ने के क्रम और आखिर में उसमें छेद करने के तरीके को दर्शाया गया है। खोलने पर यह कागज़ कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663171.png\" alt=\"rId22\" width=\"263\" height=\"81\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663265.png\" alt=\"rId23\" width=\"100\" height=\"94\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663358.png\" alt=\"rId24\" width=\"100\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663460.png\" alt=\"rId25\" width=\"91\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663556.png\" alt=\"rId26\" width=\"100\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663265.png\" alt=\"rId23\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663358.png\" alt=\"rId24\" width=\"100\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663460.png\" alt=\"rId25\" width=\"89\" height=\"87\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663556.png\" alt=\"rId26\" width=\"100\"></p>"],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663265.png\" alt=\"rId23\" width=\"89\" height=\"84\"></p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663265.png\" alt=\"rId23\" width=\"89\" height=\"84\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Three of the following four number-pairs are alike in a certain way and thus form a group. Which number-pair does NOT belong to that group? <br>(NOTE: The relation should be found without breaking down the numbers into its constituent digits)</p>",
                    question_hi: "<p>7. निम्नलिखित चार संख्या-युग्मों में से तीन एक निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा संख्या-युग्म उस समूह से संबंधित नहीं है? <br>(नोट: संख्याओं को उसके घटक अंकों में विभाजित किए बिना संबंध ज्ञात किया जाना चाहिए</p>",
                    options_en: ["<p>83, 97</p>", "<p>73, 79</p>", 
                                "<p>61,71</p>", "<p>31, 41</p>"],
                    options_hi: ["<p>83, 97</p>", "<p>73, 79</p>",
                                "<p>61,71</p>", "<p>31, 41</p>"],
                    solution_en: "<p>7.(b) <strong>Logic:</strong> There is a prime number between both the prime numbers but in option (b) the prime numbers are consecutive.</p>",
                    solution_hi: "<p>7.(b) <strong>तर्क:</strong> दोनों अभाज्य संख्याओं के बीच एक अभाज्य संख्या है लेकिन विकल्प (b) में अभाज्य संख्याएँ क्रमागत हैं</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What should come in place of ? in the given series?<br>4 13 ? 58 94 139 193</p>",
                    question_hi: "<p>8. दी गई श्रृंखला में प्रश्न चिह्न \'?\' के स्थान पर क्या आना चाहिए?<br>4 13 ? 58 94 139 193</p>",
                    options_en: ["<p>36</p>", "<p>31</p>", 
                                "<p>27</p>", "<p>26</p>"],
                    options_hi: ["<p>36</p>", "<p>31</p>",
                                "<p>27</p>", "<p>26</p>"],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663652.png\" alt=\"rId27\" width=\"309\" height=\"72\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663652.png\" alt=\"rId27\" width=\"309\" height=\"72\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language, &lsquo;Try to act&rsquo; is coded as &lsquo;ZX MN HO&rsquo; and &lsquo;act your age&rsquo; is coded as &lsquo;CO MB ZX&rsquo;. How is &lsquo;act&rsquo; coded in that code language?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में, &lsquo;Try to act&rsquo; को &lsquo;ZX MN HO&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;act your age&rsquo; को &lsquo;CO MB ZX&rsquo; के रूप में कूटबद्ध किया जाता है। इसी कूट भाषा में &lsquo;act&rsquo; को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>CO</p>", "<p>HO</p>", 
                                "<p>ZX</p>", "<p>MB</p>"],
                    options_hi: ["<p>CO</p>", "<p>HO</p>",
                                "<p>ZX</p>", "<p>MB</p>"],
                    solution_en: "<p>9.(c)<br>&lsquo;Try to act&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;ZX MN HO&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;act your age&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;CO MB ZX&rsquo; ....... (ii)<br>From (i) and (ii) &lsquo;act&rsquo; and &lsquo;zx&rsquo; are common.<br>So, the code of &lsquo;act&rsquo; is &lsquo;zx&rsquo;.</p>",
                    solution_hi: "<p>9.(c)<br>&lsquo;Try to act&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;ZX MN HO&rsquo; &hellip;&hellip;&hellip; (i)<br>&lsquo;act your age&rsquo; <math display=\"inline\"><mo>&#8594;</mo></math> &lsquo;CO MB ZX&rsquo; ....... (ii)<br>(i) और (ii) से \'act\' और \'zx\' उभयनिष्ठ हैं।<br>तो, \'act\' का कोड \'zx\' है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In this question, three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow&nbsp;from the statements.<br><strong>Statements</strong> :<br>All rings are earrings.<br>Some rings are chains.<br>All chains are bangles.<br><strong>Conclusions</strong> :<br>I. Some rings are bangles.<br>II. Some earrings are bangles.<br>III. Some chains are earrings.</p>",
                    question_hi: "<p>10. इस प्रश्न में तीन कथन दिए गए हैं, जिनके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से अलग प्रतीत होते हों, निर्धारित करें कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/ करते हैं।<br><strong>कथन</strong> :<br>सभी अंगूठियाँ, झुमके हैं।<br>कुछ अंगूठियाँ, जंजीरें हैं।<br>सभी जंजीरें, चूड़ियाँ हैं।<br><strong>निष्कर्ष</strong> :<br>I. कुछ अंगूठियाँ, चूड़ियाँ हैं।<br>II. कुछ झुमके, चूड़ियाँ हैं।<br>III. कुछ जंजीरें, झुमके हैं।</p>",
                    options_en: ["<p>Both conclusions I and III follow.</p>", "<p>Both conclusions I and II follow.</p>", 
                                "<p>Both conclusions II and III follow.</p>", "<p>All conclusions I, II and III follow.</p>"],
                    options_hi: ["<p>निष्कर्ष I और III, दोनों अनुसरण करते हैं।</p>", "<p>निष्कर्ष I और II, दोनों अनुसरण करते हैं।</p>",
                                "<p>निष्कर्ष II और III, दोनों अनुसरण करते हैं।</p>", "<p>I, II और III, सभी निष्कर्ष अनुसरण करते हैं।</p>"],
                    solution_en: "<p>10.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663765.png\" alt=\"rId28\" width=\"237\" height=\"85\"><br>All conclusions I, II and III follow.</p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663874.png\" alt=\"rId29\" width=\"293\" height=\"110\"><br>सभी निष्कर्ष I, II और III अनुसरण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Which letter cluster will replace the question mark (?) to complete the given series ?<br>WTNM, SYHT, ? , KIVH, GNPO</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन-सा अक्षर समूह प्रश्न-चिह्न (?) का स्थान लेगा और दी गई शृंखला को पूरा करेगा ?<br>WTNM, SYHT, ? , KIVH, GNPO</p>",
                    options_en: ["<p>OFHA</p>", "<p>ODBA</p>", 
                                "<p>UIDS</p>", "<p>HREK</p>"],
                    options_hi: ["<p>OFHA</p>", "<p>ODBA</p>",
                                "<p>UIDS</p>", "<p>HREK</p>"],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663996.png\" alt=\"rId30\" width=\"398\" height=\"129\"></p>",
                    solution_hi: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340663996.png\" alt=\"rId30\" width=\"398\" height=\"129\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;,<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the husband of B&rsquo;,<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the daughter of B&rsquo;.<br>Based on the above, how is Q related to T if &lsquo;P &divide; Q &divide; R &times; S + T&rsquo;?</p>",
                    question_hi: "<p>12. एक निश्चित कूट भाषा में,<br>&lsquo;A + B का अर्थ है \'A, B की माँ है\',<br>&lsquo;A &times; B&rsquo; का अर्थ है \'A, B का पति है\',<br>&lsquo;A &divide; B&rsquo; का अर्थ है \'A, B की पुत्री है\'।<br>उपरोक्त के आधार पर, यदि \'P &divide; Q &divide; R &times; S + T\' है, तो Q का T से क्या संबंध है?</p>",
                    options_en: ["<p>Daughter</p>", "<p>Sister</p>", 
                                "<p>Mother</p>", "<p>Father&rsquo;s sister</p>"],
                    options_hi: ["<p>पुत्री</p>", "<p>बहन</p>",
                                "<p>माँ</p>", "<p>पिता की बहन</p>"],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664109.png\" alt=\"rId31\" width=\"104\" height=\"153\"><br>Q is the sister of T.</p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664109.png\" alt=\"rId31\" width=\"104\" height=\"153\"><br>Q, T की बहन है.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Six letters V, C, R, E, X and Z are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to C.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664242.png\" alt=\"rId32\" width=\"137\" height=\"67\"></p>",
                    question_hi: "<p>13. एक पासे के विभिन्न फलकों पर छह अक्षर V, C, R, E, X और Z लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। C के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664242.png\" alt=\"rId32\" width=\"137\" height=\"67\"></p>",
                    options_en: ["<p>X</p>", "<p>R</p>", 
                                "<p>V</p>", "<p>Z</p>"],
                    options_hi: ["<p>X</p>", "<p>R</p>",
                                "<p>V</p>", "<p>Z</p>"],
                    solution_en: "<p>13.(a) From both the dice the opposite face are <br>R &harr; Z , C &harr; X, V &harr; E</p>",
                    solution_hi: "<p>13.(a) दोनों पासों के विपरीत फलक हैं<br>R &harr; Z , C &harr; X, V &harr; E</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. M, N, O, P, Q and R are sitting around a circular table facing the centre. O is an immediate neighbour of M. Only two people sit between O and Q. P sits second to the left of R. N is an immediate neighbour of P. Who is sitting second to the right of M?</p>",
                    question_hi: "<p>14. M, N, O, P, Q और R एक वृत्ताकार मेज के चारो ओर केंद्र की ओर मुख करके बैठे हैं। O, M का निकटतम पड़ोसी है। O और Q के बीच केवल दो व्यक्ति बैठे हैं। P, R के बाएं से दूसरे स्थान पर बैठा है। N, P का निकटतम पड़ोसी है। M के दाएं से दूसरे स्थान पर कौन बैठा है?</p>",
                    options_en: ["<p>N</p>", "<p>Q</p>", 
                                "<p>R</p>", "<p>P</p>"],
                    options_hi: ["<p>N</p>", "<p>Q</p>",
                                "<p>R</p>", "<p>P</p>"],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664394.png\" alt=\"rId33\" width=\"158\" height=\"112\"></p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664394.png\" alt=\"rId33\" width=\"158\" height=\"112\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. All of the letters in the word &lsquo;JUNGLE&rsquo; are arranged in alphabetical order. How many letters are there in the English alphabetical series between the letter which is second from the left end and the one which is third from the right end in the new letter-cluster thus formed?</p>",
                    question_hi: "<p>15. शब्द \'JUNGLE\' के सभी अक्षर वर्णानुक्रम में व्यवस्थित किये जाते हैं। इस प्रकार बने नए अक्षर-समूह में बाएं छोर से दूसरे और दाएं छोर से तीसरे अक्षर के बीच अंग्रेजी वर्णमाला क्रम में कितने अक्षर हैं?</p>",
                    options_en: ["<p>Three</p>", "<p>Two</p>", 
                                "<p>Five</p>", "<p>Four</p>"],
                    options_hi: ["<p>तीन</p>", "<p>दो</p>",
                                "<p>पाँच</p>", "<p>चार</p>"],
                    solution_en: "<p>15.(d) <strong>Given:</strong> JUNGLE<br>As per given instruction after arranging the letter alphabetically we get,- EGJLNU<br>The letter Second from the left end is &lsquo;G&rsquo; and third from the right end is &lsquo;L&rsquo; <br>Then, the number of letters between G and L is 4.</p>",
                    solution_hi: "<p>15.(d) <strong>दिया गया:</strong> JUNGLE<br>दिए गए निर्देश के अनुसार अक्षर को वर्णानुक्रम में व्यवस्थित करने पर हमें प्राप्त होता है, - EGJLNU,<br>बाएं छोर से दूसरा अक्षर \'G\' है और दाएं छोर से तीसरा अक्षर \'L\' है <br>फिर, G और L के बीच अक्षरों की संख्या 4 है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. If + means &minus; , &minus; means &times; , &times; means &divide; , &divide; means +, what will come in place of the question mark (?) in the given equation? <br>180 &times; 60 &ndash; 33 &divide; 1 + 50 = ?</p>",
                    question_hi: "<p>16. यदि + का अर्थ &minus; है, &minus; का अर्थ &times; है, &times; का अर्थ &divide; है, &divide; का अर्थ + है, तो दिए गए समीकरण में प्रश्न-चिह्न (?) के स्थान पर क्या आएगा?<br>180 &times; 60 - 33 &divide; 1 + 50 = ?</p>",
                    options_en: ["<p>40</p>", "<p>55</p>", 
                                "<p>50</p>", "<p>51</p>"],
                    options_hi: ["<p>40</p>", "<p>55</p>",
                                "<p>50</p>", "<p>51</p>"],
                    solution_en: "<p>16.(c) <strong>Given</strong> :- 180 &times; 60 - 33 &divide;&nbsp;1 + 50<br>As per given instruction after interchanging the sign we get,<br>180 &divide; 60 &times; 33 + 1 - 50<br>3 &times; 33 + 1 - 50<br>100 - 50 = 50</p>",
                    solution_hi: "<p>16.(c) <strong>दिया गया :- </strong>180 &times; 60 - 33 &divide; 1 + 50<br>दिए गए निर्देश के अनुसार चिन्ह को आपस में बदलने पर हमें मिलता है<br>180 &divide; 60 &times; 33 + 1 - 50<br>3 &times; 33 + 1 - 50<br>100 - 50 = 50</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Study the given Venn diagram and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664558.png\" alt=\"rId34\" width=\"194\" height=\"190\"> <br>How many women entrepreneurs are NOT Indian?</p>",
                    question_hi: "<p>17. दिए गए वेन आरेख का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664666.png\" alt=\"rId35\" width=\"180\" height=\"194\"> <br>कितनी महिला उद्यमी भारतीय नहीं हैं?</p>",
                    options_en: ["<p>11</p>", "<p>17</p>", 
                                "<p>29</p>", "<p>18</p>"],
                    options_hi: ["<p>11</p>", "<p>17</p>",
                                "<p>29</p>", "<p>18</p>"],
                    solution_en: "<p>17.(d) The number of women entrepreneurs who are not Indian = 18</p>",
                    solution_hi: "<p>17.(d) महिला उद्यमियों की संख्या जो भारतीय नहीं हैं = 18</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. If 15 August 2006 is a Tuesday, then what will the day of the week on 19 February 2012?</p>",
                    question_hi: "<p>18. यदि 15 अगस्त 2006 को मंगलवार है, तो 19 फरवरी 2012 को कौन-सा दिन होगा?</p>",
                    options_en: ["<p>Friday</p>", "<p>Saturday</p>", 
                                "<p>Sunday</p>", "<p>Tuesday</p>"],
                    options_hi: ["<p>शुक्रवार</p>", "<p>शनिवार</p>",
                                "<p>रविवार</p>", "<p>मंगलवार</p>"],
                    solution_en: "<p>18.(c) 15 August 2006 is Tuesday. Number of odd days in going from 2006 to 2012 is <br>+1 + 2 + 1 + 1 + 1 + 2 = 8. On dividing 8 by 7 the remainder = 1. Tuesday + 1 = Wednesday. We have reached till 15 August 2012. We have to go back to 19 Feb 2012, number of days between = 15 + 31 + 30 + 31 + 30 + 31 + 10 = 178. On dividing 178 by 7, remainder = 3. Wednesday - 3 = Sunday.</p>",
                    solution_hi: "<p>18.(c) 15 अगस्त 2006 को मंगलवार है. 2006 से 2012 तक विषम दिनों की संख्या है <br>+1 + 2 + 1 + 1 + 1 + 2 = 8. 8 को 7 से विभाजित करने पर शेषफल = 1. मंगलवार + 1 = बुधवार। हम 15 अगस्त 2012 तक पहुंच गए हैं। हमें 19 फरवरी 2012 पर वापस जाना है, बीच में दिनों की संख्या = 15 + 31 + 30 + 31 + 30 + 31 + 10 = 178। 178 को 7 से विभाजित करने पर शेषफल = 3. बुधवार - 3 = रविवार.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. The present ages (in years) of A and B are in the ratio of 3 : 4. Five years back, the ratio of their ages was 2 : 3. What is the present age (in years) of A ?</p>",
                    question_hi: "<p>19. A और B की वर्तमान आयु (वर्षों में) 3 : 4 के अनुपात में है। पाँच वर्ष पूर्व, उनकी आयु का अनुपात 2 : 3 था। A की वर्तमान आयु (वर्षों में) कितनी है?</p>",
                    options_en: ["<p>15</p>", "<p>28</p>", 
                                "<p>20</p>", "<p>21</p>"],
                    options_hi: ["<p>15</p>", "<p>28</p>",
                                "<p>20</p>", "<p>21</p>"],
                    solution_en: "<p>19.(a) Let the present age of A and B be 3<math display=\"inline\"><mi>x</mi></math> and 4x respectively,<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn></mrow><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 9x - 15 = 8x - 10<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5<br>So, the present age of A = 3 &times; 5 = 15 years</p>",
                    solution_hi: "<p>19.(a) माना A और B की वर्तमान आयु क्रमशः 3<math display=\"inline\"><mi>x</mi></math> और 4x है,<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn></mrow><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 9x - 15 = 8x - 10<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 5<br>तो, A की वर्तमान आयु = 3 &times; 5 = 15 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Identify the figure given in the options which when put in place of \'?\' will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664807.png\" alt=\"rId36\" width=\"400\"></p>",
                    question_hi: "<p>20. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए, जिसे\'?\' के स्थान पर रखने पर शृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664807.png\" alt=\"rId36\" width=\"400\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664967.png\" alt=\"rId37\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665103.png\" alt=\"rId38\" width=\"100\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665223.png\" alt=\"rId39\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665355.png\" alt=\"rId40\" width=\"100\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340664967.png\" alt=\"rId37\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665103.png\" alt=\"rId38\" width=\"100\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665223.png\" alt=\"rId39\" width=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665355.png\" alt=\"rId40\" width=\"100\"></p>"],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665355.png\" alt=\"rId40\" width=\"100\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665355.png\" alt=\"rId40\" width=\"100\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Three of the following four letter-clusters are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group? <br>(Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.) <br>1) ADBC <br>2) JMKL <br>3) FJHI <br>4) VYWX</p>",
                    question_hi: "<p>21. निलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br>1) ADBC <br>2) JMKL <br>3) FJHI <br>4) VYWX</p>",
                    options_en: ["<p>VYWX</p>", "<p>JMKL</p>", 
                                "<p>FJHI</p>", "<p>ADBC</p>"],
                    options_hi: ["<p>VYWX</p>", "<p>JMKL</p>",
                                "<p>FJHI</p>", "<p>ADBC</p>"],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665448.png\" alt=\"rId41\" width=\"107\" height=\"106\"> ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665563.png\" alt=\"rId42\" width=\"107\"> ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665665.png\" alt=\"rId43\" width=\"120\" height=\"113\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665762.png\" alt=\"rId44\" width=\"104\" height=\"110\"></p>",
                    solution_hi: "<p>21.(c)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665448.png\" alt=\"rId41\" width=\"107\" height=\"106\"> ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665563.png\" alt=\"rId42\" width=\"107\"> ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665665.png\" alt=\"rId43\" width=\"120\" height=\"113\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665762.png\" alt=\"rId44\" width=\"104\" height=\"110\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. How many squares are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665963.png\" alt=\"rId45\" width=\"116\" height=\"105\"></p>",
                    question_hi: "<p>22. दी गई आकृति में कितने वर्ग हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340665963.png\" alt=\"rId45\" width=\"116\" height=\"105\"></p>",
                    options_en: ["<p>10</p>", "<p>11</p>", 
                                "<p>12</p>", "<p>9</p>"],
                    options_hi: ["<p>10</p>", "<p>11</p>",
                                "<p>12</p>", "<p>9</p>"],
                    solution_en: "<p>22.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666114.png\" alt=\"rId46\" width=\"227\" height=\"196\"><br>There are total 11 squares = ABCD, EUCV, EFGH, MNOP, D&rsquo;RCP, RB&rsquo;OC, QRST, RZA&rsquo;S, IJKL, WXYL, EXA&rsquo;E&rsquo;.</p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666114.png\" alt=\"rId46\" width=\"227\" height=\"196\"><br>कुल 11 वर्ग हैं = ABCD, EUCV, EFGH, MNOP, D&rsquo;RCP, RB&rsquo;OC, QRST, RZA&rsquo;S, IJKL, WXYL, EXA&rsquo;E&rsquo;.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In a certain code language, &lsquo;PANTHER&rsquo; is written as &lsquo;0146283&rsquo; and &lsquo;ANOTHER&rsquo; is written as &lsquo;2864109&rsquo;. How will &lsquo;O&rsquo; be written in that language?</p>",
                    question_hi: "<p>23. एक निश्चित कूट भाषा में, &lsquo;PANTHER&rsquo; को &lsquo;0146283&rsquo; के रूप में लिखा जाता है और &lsquo;ANOTHER&rsquo; को &lsquo;2864109&rsquo; के रूप में लिखा जाता है। उसी भाषा में &lsquo;O&rsquo; को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: ["<p>0</p>", "<p>8</p>", 
                                "<p>3</p>", "<p>9</p>"],
                    options_hi: ["<p>0</p>", "<p>8</p>",
                                "<p>3</p>", "<p>9</p>"],
                    solution_en: "<p>23.(d)<br>P A N T H E R <math display=\"inline\"><mo>&#8594;</mo></math> 0 1 4 6 2 8 3<br>A N O T H E R <math display=\"inline\"><mo>&#8594;</mo></math> 2 8 6 4 1 0 9<br>From above coding, the code for &ldquo;O&rdquo; is 9.</p>",
                    solution_hi: "<p>23.(d)<br>P A N T H E R <math display=\"inline\"><mo>&#8594;</mo></math> 0 1 4 6 2 8 3<br>A N O T H E R <math display=\"inline\"><mo>&#8594;</mo></math> 2 8 6 4 1 0 9<br>उपरोक्त कोडिंग से, \"O\" का कोड 9 है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(14, 28, 84)<br>(17, 34, 102)</p>",
                    question_hi: "<p>24. उस समुच्चय का चयन करें जिसकी संख्याएं उसी तरह से संबंधित हैं जिस तरह निम्नलिखित समुच्चयों की संख्याएं संबंधित हैं।<br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें -13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)<br>(14, 28, 84)<br>(17, 34, 102)</p>",
                    options_en: ["<p>(20, 41, 136)</p>", "<p>(21, 42, 136)</p>", 
                                "<p>(20, 41, 126)</p>", "<p>(21, 42, 126)</p>"],
                    options_hi: ["<p>(20, 41, 136)</p>", "<p>(21, 42, 136)</p>",
                                "<p>(20, 41, 126)</p>", "<p>(21, 42, 126)</p>"],
                    solution_en: "<p>24.(d) <strong>Logic </strong>:- (3<sup>rd</sup> number - 2<sup>nd</sup> number) &divide;&nbsp;4 = (1<sup>st</sup> number)<br>(14, 28,84) :- (84 - 28) &divide;&nbsp;4 &rArr; (56) &divide; 4 = 14<br>(17, 34, 102) :- (102 - 34) &divide; 4 &rArr; (68) &divide; 4 = 17<br>Similarly,<br>(21, 42, 126) :- (126 - 42) &divide; 4 &rArr; (84) &divide; 4 = 21</p>",
                    solution_hi: "<p>24.(d) <strong>तर्क</strong> :- (तीसरी संख्या - दूसरी संख्या) &divide; 4 = (पहली संख्या)<br>(14, 28,84) :- (84 - 28) &divide; 4 &rArr; (56) &divide; 4 = 14<br>(17, 34, 102) :- (102 - 34) &divide; 4 &rArr; (68) &divide; 4 = 17<br>इसी प्रकार,<br>(21, 42, 126) :- (126 - 42) &divide; 4 &rArr; (84) &divide; 4 = 21</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. LH3 is related to RN21 in a certain way. In the same way, JF5 is related to PL35. To which of the following is ZV6 related following the same logic ?</p>",
                    question_hi: "<p>25. LH3, RN21 से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, JF5, PL35 से संबंधित है। समान तर्क का अनुसरण करते हुए ZV6 निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: ["<p>EA45</p>", "<p>EA24</p>", 
                                "<p>FB42</p>", "<p>FB45</p>"],
                    options_hi: ["<p>EA45</p>", "<p>EA24</p>",
                                "<p>FB42</p>", "<p>FB45</p>"],
                    solution_en: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666216.png\" alt=\"rId47\" width=\"110\" height=\"140\">,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666308.png\" alt=\"rId48\" width=\"110\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666422.png\" alt=\"rId49\" width=\"110\"></p>",
                    solution_hi: "<p>25.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666216.png\" alt=\"rId47\" width=\"110\" height=\"140\">,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666308.png\" alt=\"rId48\" width=\"110\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666422.png\" alt=\"rId49\" width=\"110\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following battles is considered the formal beginning of the British Raj in India?</p>",
                    question_hi: "<p>26. निम्नलिखित में से किस युद्ध को भारत में ब्रिटिश राज की औपचारिक शुरुआत माना जाता है?</p>",
                    options_en: ["<p>First Carnatic War</p>", "<p>Battle of Plassey</p>", 
                                "<p>Battle of Wandiwash</p>", "<p>Battle of Buxar</p>"],
                    options_hi: ["<p>प्रथम कर्नाटक युद्ध</p>", "<p>प्लासी का युद्ध</p>",
                                "<p>वांडिवाश का युद्ध</p>", "<p>बक्सर का युद्ध</p>"],
                    solution_en: "<p>26.(b) <strong>The Battle of Plassey </strong>took place in Bengal on June 23, 1757. British East India Company troops, led by Robert Clive, confronted the forces of Siraj-ud-Daulah, the Nawab of Bengal, along with his French allies. The British East India Company won this battle. The Battle of Buxar was fought in 1764 between the forces of the British East India Company, led by Hector Munro, and the combined armies of Mir Qasim, Shah Alam II, and Shuja-ud-Daulah of Awadh. The Battle of Wandiwash was fought in 1760, between the British and French East India Company.</p>",
                    solution_hi: "<p>26.(b)<strong> प्लासी का युद्ध </strong>23 जून, 1757 को बंगाल में हुआ था। रॉबर्ट क्लाइव के नेतृत्व में ब्रिटिश ईस्ट इंडिया कंपनी की सेना ने बंगाल के नवाब सिराजुद्दौला की सेना एवं उनके फ्रांसीसी सहयोगियों का सामना किया। इस युद्ध में ब्रिटिश ईस्ट इंडिया कंपनी की जीत हुई। बक्सर की लड़ाई 1764 में हेक्टर मुनरो के नेतृत्व में ब्रिटिश ईस्ट इंडिया कंपनी की सेनाओं और अवध के मीर कासिम, शाह आलम द्वितीय एवं शुजाउद्दौला की संयुक्त सेनाओं के बीच हुआ। वांडिवाश का युद्ध 1760 में ब्रिटिश एवं फ्रांसीसी ईस्ट इंडिया कंपनी के बीच हुआ था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Who among the following is the first ever recipient of Rajiv Gandhi Khel Ratna Award?</p>",
                    question_hi: "<p>27. निम्नलिखित में से कौन राजीव गांधी खेल रत्न पुरस्कार पाने वाले पहले व्यक्ति हैं?</p>",
                    options_en: ["<p>Vishwanathan Anand</p>", "<p>Dhyan Chand</p>", 
                                "<p>Virat Kohli</p>", "<p>Sunil Chhetri</p>"],
                    options_hi: ["<p>विश्वनाथन आनंद</p>", "<p>ध्यानचंद</p>",
                                "<p>विराट कोहली</p>", "<p>सुनील छेत्री</p>"],
                    solution_en: "<p>27.(a) <strong>Vishwanathan Anand.</strong> His Awards - Padma Shri (1988), Padma Bhushan (2001), Padma Vibhushan (2008). Autobiography - &lsquo;Mind Master&rsquo;. The Khel Ratna was established in 1991-92 as the Rajiv Gandhi Khel Ratna Award before it was renamed to Major Dhyan Chand Khel Ratna Award in 2021. Other sports awards in India: Arjuna Award (1961), Dronacharya Award (1985).</p>",
                    solution_hi: "<p>27.(a) <strong>विश्वनाथन आनंद।</strong> इनको प्राप्त पुरस्कार - पद्म श्री (1988), पद्म भूषण (2001), पद्म विभूषण (2008)। आत्मकथा - \'माइंड मास्टर\'। खेल रत्न की स्थापना 1991-92 में राजीव गांधी खेल रत्न पुरस्कार के रूप में की गई थी, 2021 में इसका नाम बदलकर मेजर ध्यानचंद खेल रत्न पुरस्कार कर दिया गया। भारत में अन्य खेल संबंधित पुरस्कार: अर्जुन पुरस्कार (1961), द्रोणाचार्य पुरस्कार (1985)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The famous &lsquo;Sriharikota&rsquo; island of India is well known as _____.</p>",
                    question_hi: "<p>28. भारत का प्रसिद्ध \'श्रीहरिकोटा\' द्वीप _____ के रूप में जाना जाता है।</p>",
                    options_en: ["<p>the largest island in India</p>", "<p>a famous hill station</p>", 
                                "<p>a Space centre</p>", "<p>an IIM center</p>"],
                    options_hi: ["<p>भारत के सबसे बड़े द्वीप</p>", "<p>एक प्रसिद्ध हिल स्टेशन</p>",
                                "<p>एक अंतरिक्ष केंद्र</p>", "<p>एक आईआईएम (IIM) केंद्र</p>"],
                    solution_en: "<p>28.(c) <strong>a Space centre. </strong>Satish Dhawan Space Centre (in Sriharikota, Andhra Pradesh), the Spaceport of India, is responsible for providing Launch Base Infrastructure for the Indian Space Programme. List of Space agencies: USA - National Aeronautics and Space Administration (NASA), India - Indian Space Research Organisation (ISRO), European Space Agency (ESA).</p>",
                    solution_hi: "<p>28.(c) <strong>एक अंतरिक्ष केंद्र। </strong>सतीश धवन अंतरिक्ष केंद्र (श्रीहरिकोटा, आंध्र प्रदेश में), भारत का अंतरिक्ष केंद्र, भारतीय अंतरिक्ष कार्यक्रम के लिए लॉन्च बेस इंफ्रास्ट्रक्चर प्रदान करने के लिए जिम्मेदार है। अंतरिक्ष एजेंसियों की सूची: USA - नेशनल एयरोनॉटिक्स एंड स्पेस एडमिनिस्ट्रेशन (NASA), भारत - भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO).</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. What is India&rsquo;s rank in the Global Intellectual Property Index 2024?</p>",
                    question_hi: "<p>29. वैश्विक बौद्धिक संपदा सूचकांक 2024 में भारत की रैंक क्या है ?</p>",
                    options_en: ["<p>40</p>", "<p>42</p>", 
                                "<p>45</p>", "<p>50</p>"],
                    options_hi: ["<p>40</p>", "<p>42</p>",
                                "<p>45</p>", "<p>50</p>"],
                    solution_en: "<p>29.(b) <strong>42.</strong> India secured the 42nd position out of 55 countries evaluated in the Global Intellectual Property Index 2024, with an overall score of 38.64%. This ranking reflects India\'s efforts and challenges in the intellectual property landscape. It is released by the World Intellectual Property Organization (WIPO).</p>",
                    solution_hi: "<p>29.(b) <strong>42.</strong> भारत ने वैश्विक बौद्धिक संपदा सूचकांक 2024 में 38.64% के समग्र स्कोर के साथ 55 देशों में से 42वां स्थान हासिल किया। यह रैंकिंग बौद्धिक संपदा परिदृश्य में भारत के प्रयासों और चुनौतियों को दर्शाती है। इसे विश्व बौद्धिक संपदा संगठन (WIPO) द्वारा जारी किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. In 1955, _____ noted the possibility of using small-scale industries for promoting rural development.</p>",
                    question_hi: "<p>30. 1955 में, ______ ने ग्रामीण विकास को बढ़ावा देने के लिए लघुस्तर उद्योगों (small-scale industries) का इस्तेमाल करने की संभावनाओं का उल्लेख किया।</p>",
                    options_en: ["<p>Kelkar Committee</p>", "<p>Sarkaria Committee</p>", 
                                "<p>Karve Committee</p>", "<p>Chakravarty Committee</p>"],
                    options_hi: ["<p>केलकर समिति (Kelkar Committee)</p>", "<p>सरकारिया समिति (Sarkaria Committee)</p>",
                                "<p>कर्वे समिति (Karve Committee)</p>", "<p>चक्रवर्ती समिति (Chakravarty Committee)</p>"],
                    solution_en: "<p>30.(c) <strong>Karve Committee.</strong> Other committees are: Kelkar Committee - It was formed in 2002 under the chairmanship of Vijay Kelkar. The Kelkar Committee was re-constituted in 2015 to study and evaluate the Public Private Partnership (PPP) model in India. Sarkaria Committee (1983) formed under the chairmanship of Justice R.S. Sarkaria to review the question of center-state relations. The S. Chakravarty Committee (1982) was set up to review the working of the monetary system.</p>",
                    solution_hi: "<p>30.(c) <strong>कर्वे समिति।</strong> अन्य समितियाँ: केलकर समिति - इसका गठन 2002 में विजय केलकर की अध्यक्षता में किया गया था। सार्वजनिक निजी साझेदारी (PPP) मॉडल का अध्ययन और मूल्यांकन करने के लिए भारत में केलकर समिति का पुनः गठन 2015 में किया गया था। सरकारिया समिति (1983) का गठन न्यायमूर्ति आर.एस. सरकारिया की अध्यक्षता में केंद्र-राज्य संबंधों के प्रश्न की समीक्षा करने के लिए किया गया था। एस. चक्रवर्ती समिति (1982) की स्थापना मौद्रिक प्रणाली के कामकाज की समीक्षा करने के लिए की गई थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. &lsquo;My Dateless Diary&rsquo; is a novel written by whom among the following authors?</p>",
                    question_hi: "<p>31. \'माई डेटलेस डायरी\' निम्नलिखित लेखकों में से किसके द्वारा लिखा गया उपन्यास है?</p>",
                    options_en: ["<p>Robin Singh</p>", "<p>RK Narayan</p>", 
                                "<p>Ruskin Bond</p>", "<p>Chetan Bhagat</p>"],
                    options_hi: ["<p>रोबिन सिंह</p>", "<p>आर के नारायण</p>",
                                "<p>रस्किन बांड</p>", "<p>चेतन भगत</p>"],
                    solution_en: "<p>31.(b) <strong>RK Narayan. </strong>His other Novels- The Guide, Swami and Friends, The World of Nagaraj, A Writer\'s Nightmare, The Financial Expert. Authors and their books: Ruskin Bond- The Room on the Roof, Rusty Runs Away, A Flight of Pigeons. Chetan Bhagat- Half Girlfriend, 2 States, The 3 mistakes of my life, Five Point Someone.</p>",
                    solution_hi: "<p>31.(b) <strong>आर के नारायण। </strong>उनके अन्य उपन्यास - द गाइड, स्वामी एंड फ्रेंड्स, द वर्ल्ड ऑफ नागराज, ए राइटर्स नाइटमेयर, द फाइनेंशियल एक्सपर्ट। लेखक एवं उनकी पुस्तकें: रस्किन बॉन्ड - द रूम ऑन द रूफ, रस्टी रन्स अवे, ए फ़्लाइट ऑफ़ पिजन्स। चेतन भगत- हाफ गर्लफ्रेंड, 2 स्टेट्स, द थ्री मिस्टेक्स ऑफ माई लाइफ, फाइव पॉइंट समवन।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which technique is used in cell biology research to image cellular organelles and surface topography?</p>",
                    question_hi: "<p>32. कोशिकीय अंगकों और सतह स्थलाकृति प्रतिबिंबन के लिए कोशिका जैविकी शोध में किस तकनीक का उपयोग किया जाता है?</p>",
                    options_en: ["<p>Phase contrast microscopy</p>", "<p>Scanning electron microscopy</p>", 
                                "<p>Dark-field microscopy</p>", "<p>Fluorescence microscopy</p>"],
                    options_hi: ["<p>अवस्था विपर्यासी सूक्ष्मदर्शिकी (Phase contrast microscopy)</p>", "<p>क्रमवीक्षण इलेक्ट्रॉन सूक्ष्मदर्शिकी (Scanning electron microscopy)</p>",
                                "<p>अदीप्त क्षेत्र सूक्ष्मदर्शिकी (Dark-field microscopy)</p>", "<p>प्रतिदीप्ति सूक्ष्मदर्शिकी (Fluorescence microscopy)</p>"],
                    solution_en: "<p>32.(b)<strong> Scanning electron microscopy.</strong> Phase contrast microscopy - A technique used for gaining contrast in a translucent specimen without staining the specimen. Dark-field microscopy is used to illuminate unstained samples causing them to appear brightly lit against a dark background. Fluorescent microscopy is often used to image specific features of small specimens such as microbes.</p>",
                    solution_hi: "<p>32.(b) <strong>क्रमवीक्षण इलेक्ट्रॉन सूक्ष्मदर्शिकी (Scanning electron microscopy)</strong> । अवस्था विपर्यासी सूक्ष्मदर्शिकी - नमूने को धुंधला किए बिना पारभासी नमूने में कंट्रास्ट प्राप्त करने के लिए उपयोग की जाने वाली तकनीक। अदीप्त क्षेत्र सूक्ष्मदर्शिकी का उपयोग दाग रहित नमूनों को रोशन करने के लिए किया जाता है, जिससे वे गहरे रंग की पृष्ठभूमि पर चमकते हुए दिखाई देते हैं। प्रतिदीप्ति सूक्ष्मदर्शिकी का उपयोग अक्सर सूक्ष्म जीवों जैसे छोटे नमूनों की विशिष्ट विशेषताओं की छवि के लिए किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. When Mendeleev started his work, how many elements were known at that time?</p>",
                    question_hi: "<p>33. जब मेंडलीफ ने अपना कार्य आरंभ किया, उस समय कितने तत्व ज्ञात थे?</p>",
                    options_en: ["<p>36</p>", "<p>29</p>", 
                                "<p>63</p>", "<p>47</p>"],
                    options_hi: ["<p>36</p>", "<p>29</p>",
                                "<p>63</p>", "<p>47</p>"],
                    solution_en: "<p>33.(c) <strong>63.</strong> Dmitri Mendeleev was a Russian chemist who is famous for developing the Periodic Table. Mendeleev&rsquo;s periodic law: The physical and chemical properties of the elements are the periodic function of their atomic masses.</p>",
                    solution_hi: "<p>33.(c) <strong>63 ।</strong> दिमित्री मेंडलीफ एक रूसी रसायनज्ञ थे जो आवर्त सारणी विकसित करने के लिए प्रसिद्ध हैं। मेंडलीफ का आवर्त नियम : तत्वों के भौतिक और रासायनिक गुण उनके परमाणु द्रव्यमान के आवर्ती फलन होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. The transfer of heat through horizontal movement of air is called _____________.</p>",
                    question_hi: "<p>34. वायु की क्षैतिज गति के माध्यम से ऊष्मा के स्थानांतरण को__________ कहा जाता है।</p>",
                    options_en: ["<p>conduction</p>", "<p>advection</p>", 
                                "<p>variation</p>", "<p>convection</p>"],
                    options_hi: ["<p>प्रवाहकत्त्व</p>", "<p>अभिवहन</p>",
                                "<p>विचरण</p>", "<p>संवहन</p>"],
                    solution_en: "<p>34.(b) <strong>advection.</strong> Some other mode of heat transfer: Conduction: Heat transfer through direct contact between materials. Convection : A process in which heat is carried from place to place by the bulk movement of a fluid and gases. Radiation: Heat is transferred by electromagnetic waves, even in a vacuum, making it the most effective method of heat transfer.</p>",
                    solution_hi: "<p>34.(b) <strong>अभिवहन।</strong> ऊष्मा स्थानांतरण के कुछ अन्य तरीके: प्रवाहकत्त्व: पदार्थों के बीच सीधे संपर्क के माध्यम से ऊष्मा स्थानांतरण। संवहन: एक प्रक्रिया जिसमें तरल पदार्थ और गैसों के थोक संचलन द्वारा ऊष्मा को एक स्थान से दूसरे स्थान तक ले जाया जाता है। विकिरण: ऊष्मा विद्युत चुम्बकीय तरंगों द्वारा स्थानांतरित होती है, यहाँ तक कि निर्वात में भी, जो इसे ऊष्मा स्थानांतरण का सबसे प्रभावी तरीका बनाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. In 1882, the headquarters of the Theosophical Society was established in _________.</p>",
                    question_hi: "<p>35. 1882 में, थियोसोफिकल सोसायटी का मुख्यालय _________ में स्थापित किया गया था।</p>",
                    options_en: ["<p>Gwalior</p>", "<p>Calcutta</p>", 
                                "<p>Adyar</p>", "<p>Allahabad</p>"],
                    options_hi: ["<p>ग्वालियर</p>", "<p>कलकत्ता</p>",
                                "<p>अड्यार</p>", "<p>इलाहाबाद</p>"],
                    solution_en: "<p>35.(c) <strong>Adyar</strong> near Madras (now Chennai). The Theosophical Society was founded in New York (USA) in 1875 by Madam H.P. Blavatsky and Henry Steel Olcott. They arrived in India and established their headquarters at Adyar in Madras in 1882. Later, Annie Besant took over the leadership of the Society.</p>",
                    solution_hi: "<p>35.(c) <strong>अडयार,</strong> मद्रास (अब चेन्नई) के पास । थियोसोफिकल सोसायटी की स्थापना 1875 में मैडम एच.पी. ब्लावात्स्की और हेनरी स्टील ओल्कोट के द्वारा न्यूयॉर्क (अमेरिका) में की गई थी। वे भारत आए और 1882 में मद्रास के अडयार में अपना मुख्यालय स्थापित किया। बाद में एनी बेसेंट ने इस सोसाइटी का नेतृत्व संभाला।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Writ of Mandamus is a Fundamental Right classifiable under:</p>",
                    question_hi: "<p>36. परमादेश रिट (Writ of Mandamus) एक मौलिक अधिकार है जिसे किसके तहत वर्गीकृत किया जा सकता है ?</p>",
                    options_en: ["<p>cultural and educational rights</p>", "<p>the right to freedom of religion</p>", 
                                "<p>constitutional remedies</p>", "<p>the right to equality</p>"],
                    options_hi: ["<p>सांस्कृतिक और शैक्षिक अधिकार</p>", "<p>धर्म की स्वतंत्रता का अधिकार</p>",
                                "<p>संवैधानिक उपचार</p>", "<p>समानता का अधिकार</p>"],
                    solution_en: "<p>36.(c) <strong>Constitutional remedies.</strong> Right to Constitutional Remedies (Article 32) : This allows Indian citizens to approach the Supreme Court to enforce their fundamental rights. B.R. Ambedkar described it as the \"heart and soul\" of the Constitution. The writ in the Indian Constitution is borrowed from England. There are five types of writs : &lsquo;Habeas Corpus&rsquo;, &lsquo;Mandamus&rsquo;, &lsquo;Prohibition&rsquo; , &lsquo;Certiorari&rsquo; , and &lsquo;Quo Warranto&rsquo; .</p>",
                    solution_hi: "<p>36.(c) <strong>संवैधानिक उपचार।</strong> संवैधानिक उपचारों का अधिकार (अनुच्छेद 32): यह भारतीय नागरिकों को अपने मौलिक अधिकारों को लागू करने के लिए सर्वोच्च न्यायालय में जाने की अनुमति देता है। बी.आर.अंबेडकर ने इसे संविधान का \"हृदय और आत्मा\" बताया है। भारत के संविधान में रिट का प्रावधान इंग्लैंड से लिया गया है। रिट पाँच प्रकार की होती हैं: &lsquo;बंदी प्रत्यक्षीकरण\', &lsquo;परमादेश&rsquo; , &lsquo;प्रतिषेध&rsquo;, &lsquo;उत्प्रेषण&rsquo; , और &lsquo;अधिकार-पृच्छा&rsquo;।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Totaram Sharma is a famous________ player.</p>",
                    question_hi: "<p>37. तोताराम शर्मा एक प्रसिद्ध _________ वादक हैं।</p>",
                    options_en: ["<p>sarangi</p>", "<p>flute</p>", 
                                "<p>guitar</p>", "<p>pakhawaj</p>"],
                    options_hi: ["<p>सारंगी</p>", "<p>बाँसुरी</p>",
                                "<p>गिटार</p>", "<p>पखावज</p>"],
                    solution_en: "<p>37.(d) <strong>Pakhawaj</strong> : Famous players - Mohan Shyam Sharma, Pt Ayodhya Prasad, Gopal Das, Babu Ram Shankar Pagal Das, etc. Other musical instruments and their exponents: Flute: Hari Prasad Chaurasia, Pannalal Ghosh, TR Mahalingam. Tabla: Zakir Hussain, Allah Rakha khan, Pt. Kishan Maharaj. Sarangi: Shakoor Khan, Pt Ram Narayan, Ramesh Mishra, Ustad Binda Khan. Santoor: Pt Shiv Kumar Sharma, Bhajan Sopori. Mridangam: KV Prasad, Palghat Mani Iyer, Umalayapuram Sivaraman.</p>",
                    solution_hi: "<p>37.(d) <strong>पखावज:</strong> प्रसिद्ध वादक - मोहन श्याम शर्मा, पं. अयोध्या प्रसाद, गोपाल दास, बाबू राम शंकर पागल दास, आदि। अन्य संगीत वाद्ययंत्र एवं उनके वादक: बांसुरी: हरि प्रसाद चौरसिया, पन्नालाल घोष, टी. आर. महालिंगम। तबला: जाकिर हुसैन, अल्लाह रक्खा खान, पं. किशन महाराज। सारंगी: शकूर खान, पं. राम नारायण, रमेश मिश्रा, उस्ताद बिंदा खान। संतूर: पं. शिव कुमार शर्मा, भजन सोपोरी। मृदंगम: के.वी. प्रसाद, पालघाट मणि अय्यर, उमालयपुरम शिवरामन।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Daler Mehndi is primarily a famous singer of pop songs in which Indian language?</p>",
                    question_hi: "<p>38. दलेर मेहंदी मुख्य रूप से किस भारतीय भाषा के पॉप गीतों के एक प्रसिद्ध गायक हैं?</p>",
                    options_en: ["<p>Punjabi</p>", "<p>English</p>", 
                                "<p>Bengali</p>", "<p>Hindi</p>"],
                    options_hi: ["<p>पंजाबी</p>", "<p>अंग्रेज़ी</p>",
                                "<p>बंगाली</p>", "<p>हिंदी</p>"],
                    solution_en: "<p>38.(a) <strong>Punjabi.</strong> Daler Mehndi (King of Bhangra). Other notable Punjabi singers : Diljit Dosanjh, Mikka Singh, Sukhbir Singh, Gurdas Maan, Hans Raj Hans, and Babbu Maan, Sukhwinder Singh.</p>",
                    solution_hi: "<p>38.(a) <strong>पंजाबी।</strong> दलेर मेहंदी (किंग ऑफ भांगड़ा)। अन्य मशहूर पंजाबी गायक : दिलजीत दोसांझ, मिक्का सिंह, सुखबीर सिंह, गुरदास मान, हंस राज हंस और बब्बू मान, सुखविंदर सिंह।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. At what age did Sharda Sinha, the legendary folk singer known as the &ldquo;Bihar Kokila,&rdquo; pass away?</p>",
                    question_hi: "<p>39. बिहार कोकिला के नाम से प्रसिद्ध शारदा सिन्हा का निधन किस आयु में हुआ?</p>",
                    options_en: ["<p>68</p>", "<p>72</p>", 
                                "<p>75</p>", "<p>80</p>"],
                    options_hi: ["<p>68</p>", "<p>72</p>",
                                "<p>75</p>", "<p>80</p>"],
                    solution_en: "<p>39.(b) <strong>72.</strong> Sharda Sinha was a renowned folk singer in Maithili and Bhojpuri. She received the Padma Shri in 1991, Sangeet Natak Akademi Award in 2000, and Padma Bhushan in 2018 for her contributions to music. She was famous for songs like \"Vivah Geet\" and \"Chhath Geet.\"</p>",
                    solution_hi: "<p>39.(b) <strong>72।</strong> शारदा सिन्हा मैथिली और भोजपुरी लोक गायन की प्रसिद्ध गायिका थीं। उन्होंने 1991 में पद्म श्री, 2000 में संगीत नाटक अकादमी पुरस्कार और 2018 में पद्म भूषण प्राप्त किया। उनके प्रसिद्ध गीतों में \"विवाह गीत\" और \"छठ गीत\" शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which of the following statements about cells is correct?</p>",
                    question_hi: "<p>40.कोशिका के बारे में निम्नलिखित में से कौन-सा कथन सही है?</p>",
                    options_en: ["<p>Bacterial cell contains cellulose</p>", "<p>Powerhouse of a cell is plastid</p>", 
                                "<p>Cell is a functional unit of life</p>", "<p>Cell membrane of a cell is hard and rigid.</p>"],
                    options_hi: ["<p>जीवाणु कोशिका में सेल्यूलोज़ होता है</p>", "<p>कोशिका का पावरहाउस प्लास्टिड होता है।</p>",
                                "<p>कोशिका जीवन की क्रियात्मक इकाई है।</p>", "<p>कोशिका की कोशिका झिल्ली कठोर और सख्त होती है।</p>"],
                    solution_en: "<p>40.(c) <strong>Cell is a functional unit of life</strong>. The cell was discovered by Robert Hooke in 1665. Cell is the fundamental structural and functional unit of all living organisms. Examples - Red blood cells, White blood cells, Columnar epithelial cells, Nerve cells and Mesophyll cells.</p>",
                    solution_hi: "<p>40.(c) <strong>कोशिका जीवन की एक कार्यात्मक इकाई है।</strong> कोशिका की खोज रॉबर्ट हुक ने 1665 में की थी। कोशिका सभी जीवित जीवों की मूलभूत संरचनात्मक और कार्यात्मक इकाई है। उदाहरण - लाल रक्त कोशिकाएं, श्वेत रक्त कोशिकाएं, स्तंभ उपकला कोशिकाएं, तंत्रिका कोशिकाएं और मेसोफिल कोशिकाएं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The highly viscous, mechanically-weak and ductile region of Earth\'s upper mantle is called:</p>",
                    question_hi: "<p>41. पृथ्वी के ऊपरी मेंटल (परत) का अत्यधिक चिपचिपा, यांत्रिक रूप से कमजोर और लचीला क्षेत्र कहलाता है:</p>",
                    options_en: ["<p>lithosphere</p>", "<p>asthenosphere</p>", 
                                "<p>mesosphere</p>", "<p>exosphere</p>"],
                    options_hi: ["<p>लिथोस्फीयर</p>", "<p>एस्थेनोस्फीयर</p>",
                                "<p>मेसोस्फीयर</p>", "<p>एक्सोस्फीयर</p>"],
                    solution_en: "<p>41.(b) <strong>Asthenosphere.</strong> Lithosphere : The rigid outer layer of Earth, consisting of the crust and the uppermost part of the mantle. It is divided into tectonic plates. Mesosphere : The mesosphere is the third highest layer of atmosphere and occupies the region above the stratosphere and below the thermosphere. Exosphere : The outermost layer of Earth\'s atmosphere, where air is extremely thin, and particles can escape into space.</p>",
                    solution_hi: "<p>41.(b) <strong>एस्थेनोस्फीयर।</strong> लिथोस्फीयर: पृथ्वी की कठोर बाहरी परत, जिसमें क्रस्ट और मेंटल का सबसे ऊपरी हिस्सा शामिल है। यह टेक्टोनिक प्लेटों में विभाजित है। मेसोस्फीयर: मेसोस्फीयर वायुमंडल की तीसरी सबसे ऊंची परत है और यह समताप मंडल के ऊपर और थर्मोस्फीयर के नीचे के क्षेत्र में स्थित है। एक्सोस्फीयर: पृथ्वी के वायुमंडल की सबसे बाहरी परत, जहाँ पर वायु अत्यधिक हल्की होती है, और कण अंतरिक्ष में गति कर सकते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Since 1994, Winter Olympics traditionally take place _______ after the Summer Olympics</p>",
                    question_hi: "<p>42. 1994 से, शीतकालीन ऑलिंपिक (Winter Olympics) पारंपरिक रूप से ग्रीष्म कालीन ऑलिंपिक (Summer Olympics) के __________ बाद होते हैं।</p>",
                    options_en: ["<p>3 months</p>", "<p>2 years</p>", 
                                "<p>4 years</p>", "<p>6 months</p>"],
                    options_hi: ["<p>3 महीने</p>", "<p>2 वर्ष</p>",
                                "<p>4 वर्ष</p>", "<p>6 महीने</p>"],
                    solution_en: "<p>42.(b)<strong> 2 years.</strong> The Winter Olympic Games is a major international multi-sport event held once every four years for sports practiced on snow and ice. The first Winter Olympic Games, the 1924 Winter Olympics, were held in Chamonix, France. From 1924 to 1992, the Summer and Winter Games were each held in the same year, every four years. The four-year period between the ancient and modern Olympic Games is called an Olympiad.</p>",
                    solution_hi: "<p>42.(b<strong>) 2 वर्ष। </strong>शीतकालीन ओलंपिक खेल स्नो और आइस पर खेले जाने वाले खेलों के लिए प्रत्येक चार वर्ष में एक बार आयोजित होने वाला एक प्रमुख अंतरराष्ट्रीय बहु-खेल आयोजन है। पहला शीतकालीन ओलंपिक खेल, 1924 शीतकालीन ओलंपिक, फ्रांस के शैमॉनिक्स में आयोजित किया गया था। 1924 से 1992 तक, ग्रीष्मकालीन और शीतकालीन खेल प्रत्येक चार वर्ष बाद, एक ही वर्ष में आयोजित किए जाते थे। प्राचीन और आधुनिक ओलंपिक खेलों के बीच की चार वर्ष की अवधि को ओलंपियाड कहा जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Geoid is the____of earth.</p>",
                    question_hi: "<p>43. भू-आभ (Geoid) पृथ्वी का/की_______है।</p>",
                    options_en: ["<p>orbit</p>", "<p>axis</p>", 
                                "<p>colour</p>", "<p>shape</p>"],
                    options_hi: ["<p>कक्षा</p>", "<p>ध्रुव</p>",
                                "<p>रंग</p>", "<p>आकार</p>"],
                    solution_en: "<p>43.(d) <strong>shape.</strong> The Earth looks spherical in shape, but it is not a perfect sphere. It is slightly flattened at the top and bottom (near the South and North Poles) and bulges at the Equator due to the outward force caused by the rotation of the Earth. The Geoid shape is not perfectly spherical.</p>",
                    solution_hi: "<p>43.(d) <strong>आकार।</strong> पृथ्वी आकार में गोलाकार प्रतीत होती है, लेकिन यह पूर्ण गोलाकार नहीं है। यह ऊपर और नीचे (दक्षिण और उत्तरी ध्रुवों के पास) थोड़ी चपटी है और पृथ्वी के घूर्णन और बाह्य बल के कारण भूमध्य रेखा पर उभरी हुई है। भू-आभ (Geoid) आकृति पूर्णतः गोलाकार नहीं है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In which of the following areas did the Uprising of Khurda in 1817 take place?</p>",
                    question_hi: "<p>44. 1817 में खुर्दा का विद्रोह निम्नलिखित में से किस क्षेत्र में किया गया था ?</p>",
                    options_en: ["<p>United Province</p>", "<p>Odisha</p>", 
                                "<p>Madras</p>", "<p>Bengal</p>"],
                    options_hi: ["<p>संयुक्त प्रांत</p>", "<p>ओडिशा</p>",
                                "<p>मद्रास</p>", "<p>बंगाल</p>"],
                    solution_en: "<p>44.(b) <strong>Odisha.</strong> The Khurda Revolt of 1817 was an armed uprising against the British, sparked by the rapid and disruptive changes introduced by the East India Company, which agitated the local population. This revolt also impacted other districts of Odisha, such as Cuttack. It is also known as the Paika Rebellion or Paika Bidroha, and it was led by Bakshi Jagabandhu.</p>",
                    solution_hi: "<p>44.(b) <strong>ओडिशा।</strong> 1817 में खुर्दा का विद्रोह अंग्रेजों के खिलाफ एक सशस्त्र विद्रोह था, जो ईस्ट इंडिया कंपनी द्वारा शुरू किए गए तेज़ और विध्वंसकारी परिवर्तनों से प्रेरित था, जिसने स्थानीय आबादी को आक्रोशित कर दिया था। इस विद्रोह ने ओडिशा के कटक जैसे अन्य जिलों को भी प्रभावित किया था। इसे पाइका विद्रोह के नाम से भी जाना जाता है, और इसका नेतृत्व बक्शी जगबंधु ने किया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which physicist is best known for his experiments on generating and confirming the existence of electromagnetic waves?</p>",
                    question_hi: "<p>45. कौन-सा भौतिक विज्ञानी विद्युत चुम्बकीय तरंगों को उत्पन्न करने और उनके अस्तित्व की पुष्टि करने के अपने प्रयोगों के लिए जाना जाता है?</p>",
                    options_en: ["<p>Isaac Newton</p>", "<p>Albert Einstein</p>", 
                                "<p>JJ Thomson</p>", "<p>Heinrich Rudolf Hertz</p>"],
                    options_hi: ["<p>आइजैक न्यूटन</p>", "<p>अल्बर्ट आइंस्टीन</p>",
                                "<p>जे.जे. थॉमसन</p>", "<p>हेनरिक रुडोल्फ हर्ट्ज़</p>"],
                    solution_en: "<p>45.(d)<strong> Heinrich Rudolf Hertz.</strong> He demonstrated that radio waves could be produced and detected, laying the foundation for the development of modern wireless communication. Albert Einstein was awarded the Nobel Prize in 1921 for his work on the photoelectric effect and established the mass-energy equivalence relation through his special theory of relativity in 1905. JJ Thomson: Discovered electrons and contributed to atomic physics.</p>",
                    solution_hi: "<p>45.(d) <strong>हेनरिक रुडोल्फ हर्ट्ज़।</strong> उन्होंने प्रदर्शित किया कि रेडियो तरंगों का उत्पादन और पता लगाया जा सकता है, जिससे आधुनिक बेतार संचार के विकास की नींव रखी गई। अल्बर्ट आइंस्टीन को प्रकाश-विद्युत प्रभाव पर उनके काम के लिए 1921 में नोबेल पुरस्कार से सम्मानित किया गया था और उन्होंने 1905 में अपने विशेष सापेक्षता सिद्धांत के माध्यम से द्रव्यमान-ऊर्जा तुल्यता संबंध स्थापित किया। जे.जे. थॉमसन: इलेक्ट्रॉनों की खोज की और परमाणु भौतिकी में योगदान दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. The Upper House of a State Legislature is known as the ____________.</p>",
                    question_hi: "<p>46. राज्य विधानमंडल के ऊपरी सदन को __________ के रूप में जाना जाता है।</p>",
                    options_en: ["<p>Vidhan Parishad</p>", "<p>Rajya Sabha</p>", 
                                "<p>Lok Sabha</p>", "<p>Vidhan Sabha</p>"],
                    options_hi: ["<p>विधान परिषद</p>", "<p>राज्य सभा</p>",
                                "<p>लोक सभा</p>", "<p>विधान सभा</p>"],
                    solution_en: "<p>46.(a) <strong>Vidhan Parishad.</strong> A State Legislature that has two houses includes: Lower House (State Legislative Assembly or Vidhan Sabha) and Upper House (State Council or Vidhan Parishad). State Legislative Council (Vidhan Parishad): Article 169 of the Constitution provides the provisions for abolition or creation of Legislative Councils in States. Article 171: Composition of the Legislative Councils in a state should have: No more than one-third of the total strength of the State Assembly and At least 40 members.</p>",
                    solution_hi: "<p>46.(a) <strong>विधान परिषद।</strong> एक राज्य विधानमंडल जिसमें दो सदन होते हैं, उनमें निम्न सदन (राज्य विधान सभा या विधान सभा) और उच्च सदन (राज्य परिषद या विधान परिषद) शामिल हैं। राज्य विधान परिषद (विधान परिषद): संविधान का अनुच्छेद 169 राज्यों में विधान परिषदों के उन्मूलन या निर्माण के लिए प्रावधान प्रदान करता है। अनुच्छेद 171: किसी राज्य में विधान परिषदों की संरचना: राज्य विधानसभा की कुल संख्या के एक तिहाई से अधिक तथा 40 सदस्यों से कम नहीं होना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. _______ at Market Price refers to the sum total of factor incomes earned by residents of a country during an accounting year including net indirect taxes.</p>",
                    question_hi: "<p>47. किसी लेखांकन वर्ष के दौरान निवल अप्रत्यक्ष करों सहित एक देश के निवासियों द्वारा अर्जित कारक लागत का योगफल बाजार मूल्य पर _____ कहलाता है।</p>",
                    options_en: ["<p>Net National Product (NNP)</p>", "<p>Net Domestic Product (NDP)</p>", 
                                "<p>Gross National Product (GNP)</p>", "<p>Gross Domestic Product (GDP)</p>"],
                    options_hi: ["<p>निवल राष्ट्रीय उत्पाद (NNP)</p>", "<p>निवल घरेलू उत्पाद (NDP)</p>",
                                "<p>सकल राष्ट्रीय उत्पाद (GNP)</p>", "<p>सकल घरेलू उत्पाद (GDP)</p>"],
                    solution_en: "<p>47.(a)<strong> Net National Product (NNP).</strong> It is at Factor Cost, also known as national income. When calculated at factor cost, the NNP includes the sum of wages, rents, interests, and profits distributed in an economy but excludes indirect taxes while including subsidies. Thus, it reflects the income earned through the productive activities of the country\'s normal residents. Factor Cost + Indirect Tax - Subsidy = Market Price. NNP = GNP &ndash; Depreciation.</p>",
                    solution_hi: "<p>47.(a) <strong>निवल राष्ट्रीय उत्पाद (NNP)। </strong>यह कारक लागत पर है, जिसे राष्ट्रीय आय भी कहा जाता है। कारक लागत पर गणना करने पर, NNP में अर्थव्यवस्था में वितरित मजदूरी, किराए, ब्याज और मुनाफे का योग शामिल होता है, लेकिन सब्सिडी को शामिल करते हुए अप्रत्यक्ष करों को शामिल नहीं किया जाता है। इस प्रकार, यह देश के सामान्य निवासियों की उत्पादक गतिविधियों के माध्यम से अर्जित आय को दर्शाता है। कारक लागत + अप्रत्यक्ष कर - सब्सिडी = बाजार मूल्य। NNP = GNP - मूल्यह्रास।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. What is the chemical formula of barium hydroxide ?</p>",
                    question_hi: "<p>48. बेरियम हाइड्रॉक्साइड का रासायनिक सूत्र क्या है?</p>",
                    options_en: ["<p>BaOH</p>", "<p>Ba<sub>2</sub>OH</p>", 
                                "<p>Ba(OH)<sub>3</sub></p>", "<p>Ba(OH)<sub>2</sub></p>"],
                    options_hi: ["<p>BaOH</p>", "<p>Ba<sub>2</sub>OH</p>",
                                "<p>Ba(OH)<sub>3</sub></p>", "<p>Ba(OH)<sub>2</sub></p>"],
                    solution_en: "<p>48.(d) <strong>Ba(OH)<sub>2</sub>.</strong> It occurs as an anhydrate whose prismatic, colorless crystals are very deliquescent. It is used as a general-purpose additive for lubricants and greases.</p>",
                    solution_hi: "<p>48.(d) <strong>Ba(OH)<sub>2</sub>.</strong> यह एनहाइड्रेट के रूप में पाया जाता है, जिसके प्रिज्मीय, रंगहीन क्रिस्टल बहुत ही विलेयशील होते हैं। इसका उपयोग स्नेहक और ग्रीस के लिए एक सामान्य प्रयोजन योजक के रूप में किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. What is the theme of World Elephant Day 2024, observed on August 12?</p>",
                    question_hi: "<p>49.12 अगस्त को मनाए जाने वाले विश्व हाथी दिवस 2024 का थीम क्या है?</p>",
                    options_en: ["<p>\"Save Elephants, Save Earth\"</p>", "<p>\"Protecting Elephant Habitats\"</p>", 
                                "<p>\"Elephants: Guardians of the Forest\"</p>", "<p>\"Conserve Elephants for Future Generations\"</p>"],
                    options_hi: ["<p>\"हाथी बचाओ, पृथ्वी बचाओ\"</p>", "<p>\"हाथी की रक्षा करना पर्यावास\"</p>",
                                "<p>\"हाथी: जंगल के संरक्षक\"</p>", "<p>\"हाथियों को भावी पीढ़ियों के लिए संरक्षित करें\"</p>"],
                    solution_en: "<p>49.(b) <strong>\"Protecting Elephant Habitats\"</strong>. World Elephant Day is dedicated to raising awareness about the challenges faced by elephants, including threats like habitat loss, poaching, and human-wildlife conflict. The event aims to promote the protection and conservation of elephants, the largest land mammals.</p>",
                    solution_hi: "<p>49.(b) <strong>\"हाथी पर्यावास की सुरक्षा\"। </strong>विश्व हाथी दिवस हाथियों के सामने आने वाली चुनौतियों के बारे में जागरूकता बढ़ाने के लिए समर्पित है, जिसमें आवास की हानि, अवैध शिकार और मानव-वन्यजीव संघर्ष जैसे खतरे शामिल हैं। इस कार्यक्रम का उद्देश्य हाथियों, सबसे बड़े भूमि स्तनधारी, के संरक्षण और संरक्षण को बढ़ावा देना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Usain Bolt shared the journey of his life in which of the following autobiographies?</p>",
                    question_hi: "<p>50. उसैन बोल्ट ने निम्नलिखित में से किस आत्मकथा में अपने जीवन की यात्रा साझा की है?</p>",
                    options_en: ["<p>Let Your Mind Run</p>", "<p>Faster Than Lightning</p>", 
                                "<p>Running for My Life</p>", "<p>Race of My Life</p>"],
                    options_hi: ["<p>लेट योर माइंड रन (Let Your Mind Run)</p>", "<p>फास्टर दैन लाइटनिंग (Faster Than Lightning)</p>",
                                "<p>रनिंग फॉर माई लाइफ (Running for My Life)</p>", "<p>रेस ऑफ माई लाइफ (Race of My Life)</p>"],
                    solution_en: "<p>50.(b) <strong>Faster Than Lightning.</strong> Sports personalities and their autobiographies include &rdquo;The Race of My Life&rdquo; by Milkha Singh, &ldquo;A Shot at History&rdquo; by Abhinav Bindra, &ldquo;Playing It My Way&rdquo; by Sachin Tendulkar, &ldquo;The Test of My Life&rdquo; by Yuvraj Singh, &ldquo;Ace Against Odds&rdquo; by Sania Mirza, &ldquo;Open&rdquo; by Andre Agassi, &ldquo;The Greatest: My Own Story&rdquo; by Muhammad Ali, and &ldquo;No Spin&rdquo; by Shane Warne.</p>",
                    solution_hi: "<p>50.(b) <strong>फास्टर दैन लाइटनिंग (Faster Than Lightning)।</strong> कुछ प्रमुख खेल हस्तियाँ एवं उनकी आत्मकथाएं - मिल्खा सिंह - \"द रेस ऑफ़ माई लाइफ\", अभिनव बिंद्रा - \"ए शॉट एट हिस्ट्री\", सचिन तेंदुलकर - \"प्लेइंग इट माई वे\", युवराज सिंह - \"द टेस्ट ऑफ़ माई लाइफ\", सानिया मिर्ज़ा - \"ऐस अगेंस्ट ऑड्स\", आंद्रे अगासी -\"ओपन\", मुहम्मद अली -\"द ग्रेटेस्ट: माई ओन स्टोरी\" और शेन वार्न - \"नो स्पिन\" शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Pipes M, N and S can fill a tank in 25, 50 and 100 minutes, respectively. Initially, pipes N and S are kept open for 10 minutes, and then pipe N is shut while pipe M is opened. Pipe S is closed 15 minutes before the tank overflows. How much time (in minutes) will it take to fill the tank if the three pipes work in this pattern?</p>",
                    question_hi: "<p>51. पाइप M, N और S एक टैंक को क्रमशः 25, 50 और 100 मिनट में भर सकते हैं। प्रारंभ में, पाइप N और S को 10 मिनट के लिए खुला रखा जाता है, और फिर पाइप N को बंद कर दिया जाता है जबकि पाइप M को खोल दिया जाता है। टैंक में अतिप्रवाह होने से 15 मिनट पहले पाइप S को बंद कर दिया जाता है। यदि तीनों पाइप इस पैटर्न में कार्य करें तो टैंक को भरने में कितना समय (मिनट में) लगेगा?</p>",
                    options_en: ["<p>30</p>", "<p>33</p>", 
                                "<p>42</p>", "<p>27</p>"],
                    options_hi: ["<p>30</p>", "<p>33</p>",
                                "<p>42</p>", "<p>27</p>"],
                    solution_en: "<p>51.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666583.png\" alt=\"rId50\" width=\"200\"><br>(N + S) &times; 10 + (M +S) &times; T + M &times; 15 = 100<br>3 &times; 10 + 5 &times; T + 4 &times; 15 = 100<br>T = 2 <br>And total time = 10 + 15 + 2 = 27</p>",
                    solution_hi: "<p>51.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666746.png\" alt=\"rId51\" width=\"200\"><br>(N + S) &times; 10 + (M +S) &times; T + M &times; 15 = 100<br>3 &times; 10 + 5 &times; T + 4 &times; 15 = 100<br>T = 2 <br>कुल समय = 10 + 15 + 2 = 27</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Ravi sells his goods at a 25% loss on cost price but uses 45% less weight. What is his percentage profit or loss?</p>",
                    question_hi: "<p>52. रवि अपने माल को क्रय मूल्य पर 25% की हानि पर बेचता है लेकिन 45% कम भार का उपयोग करता है। उसका लाभ या हानि प्रतिशत क्या है?</p>",
                    options_en: ["<p>Loss, 47<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>%</p>", "<p>Loss, 36<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>%</p>", 
                                "<p>Profit, 36<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>%</p>", "<p>Profit, 47<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>हानि, 47<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>%</p>", "<p>हानि, 36<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>%</p>",
                                "<p>लाभ, 36<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>%</p>", "<p>लाभ, 47<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>52.(c)<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong><math display=\"inline\"><mo>&#8594;</mo></math> CP&nbsp; :&nbsp; SP</strong> <br>at (25% loss)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp;:&nbsp; &nbsp;3<br>(45% less wt.)&nbsp; &nbsp; &nbsp; 11&nbsp; &nbsp;:&nbsp; 20<br>-------------------------------------<br><strong>Overall ratio</strong> <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;11&nbsp; &nbsp;:&nbsp; 15<br>Hence, required % = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 100 = 36<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>11</mn></mfrac></math>% (Profit)</p>",
                    solution_hi: "<p>52.(c)<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; <strong>&nbsp;क्र. मू. : वि. मू.</strong><br>(25% हानि) पर&nbsp; &nbsp; &nbsp; &nbsp;4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 3<br>(45% कम वजन)&nbsp; &nbsp; 11&nbsp; &nbsp; :&nbsp; &nbsp; 20<br>---------------------------------------------<br><strong>कुल अनुपात</strong> <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;11&nbsp; &nbsp;:&nbsp; &nbsp;15<br>अतः, आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> &times; 100 = 36<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math>% (लाभ)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The surface area of a cube is 1728 cm&sup2;. Find its volume.</p>",
                    question_hi: "<p>53. एक घन का पृष्ठीय क्षेत्रफल 1728 cm&sup2; है। इसका आयतन ज्ञात कीजिए।</p>",
                    options_en: ["<p>6453<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm&sup3;</p>", "<p>5436<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm&sup3;</p>", 
                                "<p>3456<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm&sup3;</p>", "<p>4356<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm&sup3;</p>"],
                    options_hi: ["<p>6453<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm&sup3;</p>", "<p>5436<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm&sup3;</p>",
                                "<p>3456<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm&sup3;</p>", "<p>4356<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm&sup3;</p>"],
                    solution_en: "<p>53.(c) <strong>Given:</strong> Surface area of cube (6a<sup>2</sup>) = 1728 cm<sup>2</sup></p>\n<p>So, a = 12<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>cm<br>Volume of cube <math display=\"inline\"><msup><mrow><mo>(</mo><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>)</mo></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><msqrt><mn>2</mn></msqrt></math>)<sup>3</sup>&nbsp; = 3456<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm<sup>3</sup></p>",
                    solution_hi: "<p>53.(c) <strong>दिया गया है:</strong> घन का पृष्ठीय क्षेत्रफल (6a<sup>2</sup>) = 1728 cm<sup>2&nbsp; &nbsp;<br></sup>इसलिए, a = 12<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>cm<br>घन का आयतन <math display=\"inline\"><msup><mrow><mo>(</mo><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>)</mo></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><msqrt><mn>2</mn></msqrt></math>)<sup>3</sup>&nbsp; = 3456<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> cm<sup>3</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. When the integer n is divided by 18, the quotient is x and the remainder is 6, When the integer n is divided by 25, the quotient is y and the remainder is 15. Which of the following is true?</p>",
                    question_hi: "<p>54. जब पूर्णांक n को 18 से विभाजित किया जाता है, तो भागफल x और शेषफल 6 प्रात होता है, जब पूर्णांक n को 25 से विभाजित किया जाता है, तो भागफल y और शेषफल 15 प्राप्त होता है। निम्न में से कौन सा विकल्प सत्य है?</p>",
                    options_en: ["<p>18x &minus; 25y = 9</p>", "<p>18x + 25y = 9</p>", 
                                "<p>25y &minus; 18x = 9</p>", "<p>25y +18x = 9</p>"],
                    options_hi: ["<p>18x &minus; 25y = 9</p>", "<p>18x + 25y = 9</p>",
                                "<p>25y &minus; 18x = 9</p>", "<p>25y +18x = 9</p>"],
                    solution_en: "<p>54.(a)<br>Dividend = divisor &times; quotient + remainder<br>When, n is divided by 18, the quotient is x and the remainder is 6<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 18x + 6 --------(i)<br>When, n is divided by 25, the quotient is y and the remainder is 15<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 25y + 15 --------(ii)<br>Equate equation (i) and (ii) we get;<br>18x + 6 = 25y + 15 <br>18x - 25y = 9</p>",
                    solution_hi: "<p>54.(a)<br>भाज्य = भाजक &times; भागफल + शेषफल<br>जब, n को 18 से विभाजित किया जाता है, तो भागफल x होता है और शेषफल 6 होता है<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 18x + 6 --------(i)<br>जब, n को 25 से विभाजित किया जाता है, तो भागफल y होता है और शेषफल 15 होता है<br><math display=\"inline\"><mo>&#8658;</mo></math> n = 25y + 15 --------(ii)<br>समीकरण (i) और (ii) को समान करने पर हमें प्राप्त होता है;<br>18x + 6 = 25y + 15 <br>18x - 25y = 9</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. A dishonest shopkeeper promises to sell his goods at cost price. However, he uses a weight that actually weighs 11% less than what is written on it. Find his profit percentage.</p>",
                    question_hi: "<p>55. एक बेईमान दुकानदार अपने सामान को क्रय मूल्य पर बेचने का दावा करता है। हालाँकि, वह एक ऐसे बाट का उपयोग करता है जिसका वजन वास्तव में उस पर लिखे बाट से 11% कम है। उसका लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>13<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>33</mn></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>", 
                                "<p>14<math display=\"inline\"><mfrac><mrow><mn>64</mn><mi>&#160;</mi></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>", "<p>11<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>13<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>33</mn></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>", "<p>12<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>",
                                "<p>14<math display=\"inline\"><mfrac><mrow><mn>64</mn><mi>&#160;</mi></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>", "<p>11<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>55.(b)<br><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Ratio -&nbsp; &nbsp; &nbsp;CP&nbsp; :&nbsp; SP</strong><br><strong>Due to weight -</strong> 890 : 1000 <br>-------------------------------------<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>890</mn></mrow></mfrac></math> &times; 100 = 12<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>55.(b)<br><strong>अनुपात -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CP&nbsp; :&nbsp; &nbsp;SP</strong><br><strong>वजन के कारण - </strong>890 : 1000 <br>-------------------------------------<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>110</mn></mrow><mrow><mn>890</mn></mrow></mfrac></math> &times; 100 = 12<math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>89</mn></mrow></mfrac></math>%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Two poles of heights 16 m and 10 m are standing on a level ground a distance of 8 m. Find the distance between their tops.</p>",
                    question_hi: "<p>56. 16 मीटर और 10 मीटर ऊंचाई वाले दो खंभे 8 मीटर की दूरी पर एक समतल जमीन पर खड़े हैं। उनके शीर्षों के बीच की दूरी ज्ञात कीजिए।</p>",
                    options_en: ["<p>8 m</p>", "<p>12 m</p>", 
                                "<p>6 m</p>", "<p>10 m</p>"],
                    options_hi: ["<p>8 m</p>", "<p>12 m</p>",
                                "<p>6 m</p>", "<p>10 m</p>"],
                    solution_en: "<p>56.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666957.png\" alt=\"rId52\"><br>Given , EC = 16 cm , AB = 10 m and BC = 8 m<br>So that , AD = BC = 8 cm , ED = 16 - 10 = 6 cm<br>On triangle EAD<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AE</mi><mn>2</mn></msup><mo>=</mo><msup><mi>AD</mi><mn>2</mn></msup><mo>+</mo><msup><mi>ED</mi><mn>2</mn></msup></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AE</mi><mn>2</mn></msup><mo>=</mo><msup><mn>8</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup></math><br>AE = 10 cm</p>",
                    solution_hi: "<p>56.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340666957.png\" alt=\"rId52\"><br>दिया गया है, EC = 16 cm , AB = 10 m तथा BC = 8 m<br>इसलिए, AD = BC = 8 cm , ED = 16 - 10 = 6 cm<br>त्रिभुज EAD में,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AE</mi><mn>2</mn></msup><mo>=</mo><msup><mi>AD</mi><mn>2</mn></msup><mo>+</mo><msup><mi>ED</mi><mn>2</mn></msup></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AE</mi><mn>2</mn></msup><mo>=</mo><msup><mn>8</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup></math><br>AE = 10 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The following table gives the total number of employees in 5 different companies A, B, C, D and E and the percentage of males and females. Study the table carefully and answer the question. <br>Find the number of females in companies A, C and E put together.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340667058.png\" alt=\"rId53\" width=\"541\" height=\"118\"></p>",
                    question_hi: "<p>57. निम्नलिखित तालिका 5 अलग-अलग कंपनियों A, B, C, D और E में कर्मचारियों की कुल संख्या और पुरुषों और महिलाओं का प्रतिशत दर्शाती है। तालिका का ध्यानपूर्वक अध्ययन करें और प्रश्न का उत्तर दें। <br>कंपनी A, C और E में मिलाकर महिलाओं की संख्या ज्ञात करें। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340667175.png\" alt=\"rId54\" width=\"473\" height=\"135\"></p>",
                    options_en: ["<p>700</p>", "<p>675</p>", 
                                "<p>650</p>", "<p>725</p>"],
                    options_hi: ["<p>700</p>", "<p>675</p>",
                                "<p>650</p>", "<p>725</p>"],
                    solution_en: "<p>57.(a)<br>Females in A = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 600 = 150 <br>Females in C = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 700 = 210<br>Females in E = <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 850 = 340<br>Total females in A, C, E = 150 + 210 + 340 = 700</p>",
                    solution_hi: "<p>57.(a)<br>A में महिलाएँ = <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 600 = 150 <br>C में महिलाएँ = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 700 = 210<br>E में महिलाएँ = <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 850 = 340<br>A, C, E में कुल महिलाएँ = 150 + 210 + 340 = 700</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Consider AD is a tangent to a circle of radius 6 cm. AC is a secant meeting the circle at B and CD is a diameter. If AB is 7 cm, then the value of AC (in cm) is:</p>",
                    question_hi: "<p>58. मान लीजिए कि AD एक 6 cm त्रिज्या वाले वृत्त की स्पर्श रेखा है। AC एक छेदक रेखा है जो वृत्त से बिन्दु B पर मिलती है और CD व्यास है। यदि AB का मान 7 cm है, तो AC का मान (cm में) क्या है ?</p>",
                    options_en: ["<p>16</p>", "<p>20</p>", 
                                "<p>9</p>", "<p>18</p>"],
                    options_hi: ["<p>16</p>", "<p>20</p>",
                                "<p>9</p>", "<p>18</p>"],
                    solution_en: "<p>58.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340667339.png\" alt=\"rId55\" width=\"237\" height=\"121\"><br>Let BC = x<br>In <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo><mi>ADC</mi></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AC</mi><mn>2</mn></msup></math> = AD<sup>2</sup> + CD<sup>2</sup> ( pythagoras theorem )<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = AD<sup>2</sup> + (12)<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AD</mi><mn>2</mn></msup></math> = (7 + x)<sup>2</sup> - (12)<sup>2</sup> -----(i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AD</mi><mn>2</mn></msup></math> = 7 (7 + x ) ---- (ii) [ ∵ AD<sup>2</sup> = AB &times; AC ]<br>Now <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup></math> - (12)<sup>2</sup> = 7 (7 + x ) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></math> + 7x - 144 = 0&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></math> + 16x - 9x - 144 = 0<br>x (x + 16 ) - 9 (x + 16 ) = 0<br>( x&nbsp;- 9 ) (x + 16 ) = 0<br>x = 9 , -16 (∵ side cannot be negative )<br>x = 9 <br>AC = AB + BC = 7 + 9 = 16cm</p>",
                    solution_hi: "<p>58.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340667339.png\" alt=\"rId55\" width=\"237\" height=\"121\"><br>माना , BC = <math display=\"inline\"><mi>x</mi></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo><mi>ADC</mi></math> में <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AC</mi><mn>2</mn></msup></math> = AD<sup>2</sup> + CD<sup>2</sup> ( पाइथागोरस प्रमेय )<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = AD<sup>2</sup> + (12)<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AD</mi><mn>2</mn></msup></math> = (7 + x)<sup>2</sup> - (12)<sup>2</sup> -----(i)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AD</mi><mn>2</mn></msup></math> = 7 (7 + x ) ---- (ii) [ ∵ AD<sup>2</sup> = AB &times; AC ]<br>अब <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>2</mn></msup></math> - (12)<sup>2</sup> = 7 (7 + x ) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></math> + 7x - 144 = 0&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></math> + 16x - 9x - 144 = 0<br>x (x + 16 ) - 9 (x + 16 ) = 0<br>( x - 9 ) (x + 16 ) = 0<br><math display=\"inline\"><mi>x</mi></math> = 9 , -16 (∵ भुजा ,ऋणात्मक नहीं हो सकती )<br><math display=\"inline\"><mi>x</mi></math> = 9 <br>AC = AB + BC = 7 + 9 = 16cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Evaluate&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mrow><mi>tan</mi><mn>54</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mn>36</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>41</mn><mo>&#176;</mo></mrow><mrow><mi>tan</mi><mn>49</mn><mo>&#176;</mo></mrow></mfrac></math></p>",
                    question_hi: "<p>59. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mrow><mi>tan</mi><mn>54</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mn>36</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>41</mn><mo>&#176;</mo></mrow><mrow><mi>tan</mi><mn>49</mn><mo>&#176;</mo></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>-1</p>", "<p>1</p>", 
                                "<p>0</p>", "<p>2</p>"],
                    options_hi: ["<p>-1</p>", "<p>1</p>",
                                "<p>0</p>", "<p>2</p>"],
                    solution_en: "<p>59.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mrow><mi>tan</mi><mn>54</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mn>36</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>41</mn><mo>&#176;</mo></mrow><mrow><mi>tan</mi><mn>49</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp; &nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mrow><mi>tan</mi><mn>54</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>54</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>41</mn><mo>&#176;</mo></mrow><mrow><mi>tan</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>41</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>&rarr; (by complementary angle)</p>\n<p>= 2 - 1 = 1</p>",
                    solution_hi: "<p>59.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mrow><mi>tan</mi><mn>54</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mn>36</mn><mo>&#176;</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>41</mn><mo>&#176;</mo></mrow><mrow><mi>tan</mi><mn>49</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mrow><mi>tan</mi><mn>54</mn><mo>&#176;</mo></mrow><mrow><mi>cot</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>54</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cot</mi><mo>&#160;</mo><mn>41</mn><mo>&#176;</mo></mrow><mrow><mi>tan</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>41</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>&rarr; (पूरक कोण द्वारा)&nbsp;</p>\n<p>= 2 - 1 = 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Simplify the given expression.<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>-</mo><mfrac><mn>2</mn><mn>7</mn></mfrac><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mn>63</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>72</mn><mo>-</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>96</mn><mo>-</mo><mn>31</mn><mo>)</mo></mrow></mfrac></math></p>",
                    question_hi: "<p>60. दिए गए व्यंजक को सरल कीजिए। <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>-</mo><mfrac><mn>2</mn><mn>7</mn></mfrac><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mn>63</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>72</mn><mo>-</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>96</mn><mo>-</mo><mn>31</mn><mo>)</mo></mrow></mfrac></math></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>60.(c)<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>-</mo><mfrac><mn>2</mn><mn>7</mn></mfrac><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mn>63</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>72</mn><mo>-</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>96</mn><mo>-</mo><mn>31</mn><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>9</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>72</mn><mo>-</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>65</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>-</mo><mn>18</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>72</mn><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>13</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>46</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>23</mn></mfrac></math></p>",
                    solution_hi: "<p>60.(c)<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>-</mo><mfrac><mn>2</mn><mn>7</mn></mfrac><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mn>63</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>72</mn><mo>-</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>96</mn><mo>-</mo><mn>31</mn><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>9</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>72</mn><mo>-</mo><mfrac><mn>2</mn><mn>5</mn></mfrac><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>65</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>53</mn><mo>-</mo><mn>18</mn><mo>-</mo><mn>9</mn></mrow><mrow><mn>72</mn><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>13</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>26</mn></mrow><mrow><mn>46</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>23</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. What is the HCF of 513, 1134 and 1215 ?</p>",
                    question_hi: "<p>61. 513, 1134 और 1215 का महत्तम समापवर्तक (HCF) ज्ञात करें।</p>",
                    options_en: ["<p>27</p>", "<p>81</p>", 
                                "<p>3</p>", "<p>19</p>"],
                    options_hi: ["<p>27</p>", "<p>81</p>",
                                "<p>3</p>", "<p>19</p>"],
                    solution_en: "<p>61.(a)<br>513 = 3 &times;&nbsp;3 &times; 3 &times; 19<br>1134 = 2 &times; 3 &times; 3 &times; 3 &times; 3 &times; 7<br>1215 = 3 &times; 3 &times; 3 &times; 3 &times; 3 &times; 5<br>H.C.F. (513, 1134, 1215) = 3 &times; 3 &times; 3 = <strong>27</strong></p>",
                    solution_hi: "<p>61.(a)<br>513 = 3&nbsp;&times;&nbsp;3 &times; 3 &times; 19<br>1134 = 2 &times; 3 &times; 3 &times; 3 &times; 3 &times; 7<br>1215 = 3 &times; 3 &times; 3 &times; 3 &times; 3 &times; 5<br>H.C.F. (513, 1134, 1215) = 3&nbsp;&times;&nbsp;3 &times; 3 = <strong>27</strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. 7<sup>15</sup> + 7<sup>16</sup> + 7<sup>17</sup> is divisible by which of the following given numbers?</p>",
                    question_hi: "<p>62. 7<sup>15</sup> + 7<sup>16</sup> + 7<sup>17&nbsp;</sup> निम्नलिखित में से कौन-सी संख्या से विभाज्य है?</p>",
                    options_en: ["<p>3</p>", "<p>5</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>5</p>",
                                "<p>2</p>", "<p>4</p>"],
                    solution_en: "<p>62.(a)<br>7<sup>15</sup> + 7<sup>16</sup> + 7<sup>17</sup><br><math display=\"inline\"><msup><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></msup></math>(1 + 7 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>7</mn><mn>2</mn></msup></math> ) <br><math display=\"inline\"><msup><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></msup></math>(1 + 7 + 49 ) <br><math display=\"inline\"><msup><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></msup></math>(57) <br>Among the options 57 is divisible by 3 only.</p>",
                    solution_hi: "<p>62.(a)<br>7<sup>15</sup> + 7<sup>16</sup> + 7<sup>17</sup><br><math display=\"inline\"><msup><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></msup></math>(1 + 7 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>7</mn><mn>2</mn></msup></math> ) <br><math display=\"inline\"><msup><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></msup></math>(1 + 7 + 49 ) <br><math display=\"inline\"><msup><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></msup></math>(57)<br>विकल्पों में से 57 केवल 3 से विभाज्य है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A spherical box has a surface area 528 cm&sup2;. Find the radius of the spherical box.</p>",
                    question_hi: "<p>63. एक गोलाकार बक्से का पृष्ठीय क्षेत्रफल 528 cm&sup2; है उस गोलाकार बक्से की त्रिज्या ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>37</mn></msqrt></math> cm</p>", "<p><math display=\"inline\"><msqrt><mn>42</mn></msqrt></math> cm</p>", 
                                "<p><math display=\"inline\"><msqrt><mn>29</mn></msqrt></math> cm</p>", "<p><math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> cm</p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>37</mn></msqrt></math> cm</p>", "<p><math display=\"inline\"><msqrt><mn>42</mn></msqrt></math> cm</p>",
                                "<p><math display=\"inline\"><msqrt><mn>29</mn></msqrt></math> cm</p>", "<p><math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> cm</p>"],
                    solution_en: "<p>63.(b)<br>Surface area of spherical box = 528 cm&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi>&#960;r</mi><mn>2</mn></msup></math> = 528 cm&sup2;<br>4 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r <sup>2</sup> = 528 cm&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mn>2</mn></msup></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>528</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>22</mn><mo>&#160;</mo></mrow></mfrac></math> = 42<br>r = <math display=\"inline\"><msqrt><mn>42</mn></msqrt></math><br>Hence, radius = <math display=\"inline\"><msqrt><mn>42</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>63.(b)<br>गोलाकार बक्से का पृष्ठीय क्षेत्रफल = 528 cm&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi>&#960;r</mi><mn>2</mn></msup></math> = 528 cm&sup2;<br>4 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; r <sup>2</sup> = 528 cm&sup2;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">r</mi><mn>2</mn></msup></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>528</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>22</mn><mo>&#160;</mo></mrow></mfrac></math> = 42<br>r = <math display=\"inline\"><msqrt><mn>42</mn></msqrt></math><br>अत: त्रिज्या = <math display=\"inline\"><msqrt><mn>42</mn></msqrt></math> cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If cot &alpha; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math>, then find the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mo>-</mo><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mo>+</mo><mi>Qsin&#945;</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3.</p>",
                    question_hi: "<p>64. यदि cot &alpha; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mo>-</mo><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mo>+</mo><mi>Qsin&#945;</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3 का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>0</p>", "<p>3</p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math></p>"],
                    options_hi: ["<p>0</p>", "<p>3</p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math></p>"],
                    solution_en: "<p>64.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mo>-</mo><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mo>+</mo><mi>Qsin&#945;</mi></mrow></mfrac></math><br>Divide numerator and denominator by sin&alpha;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mfrac><mi>cos&#945;</mi><mrow><mi>sin&#945;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mo>-</mo><mi mathvariant=\"normal\">Q</mi><mo>&#215;</mo><mfrac><mi>sin&#945;</mi><mi>sin&#945;</mi></mfrac></mrow><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mfrac><mi>cos&#945;</mi><mrow><mi>sin&#945;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mo>+</mo><mi mathvariant=\"normal\">Q</mi><mo>&#215;</mo><mfrac><mi>sin&#945;</mi><mi>sin&#945;</mi></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></mstyle><mo>-</mo><mi mathvariant=\"normal\">Q</mi><mo>&#160;</mo></mrow><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></mstyle><mo>+</mo><mi mathvariant=\"normal\">Q</mi><mo>&#160;</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math><br>So, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mo>-</mo><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mo>+</mo><mi>Qsin&#945;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3 = 3</p>",
                    solution_hi: "<p>64.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mo>-</mo><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mo>+</mo><mi>Qsin&#945;</mi></mrow></mfrac></math><br>अंश और हर को sin&alpha; से विभाजित करने पर <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mfrac><mi>cos&#945;</mi><mrow><mi>sin&#945;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mo>-</mo><mi mathvariant=\"normal\">Q</mi><mo>&#215;</mo><mfrac><mi>sin&#945;</mi><mi>sin&#945;</mi></mfrac></mrow><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mfrac><mi>cos&#945;</mi><mrow><mi>sin&#945;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mo>+</mo><mi mathvariant=\"normal\">Q</mi><mo>&#215;</mo><mfrac><mi>sin&#945;</mi><mi>sin&#945;</mi></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></mstyle><mo>-</mo><mi mathvariant=\"normal\">Q</mi><mo>&#160;</mo></mrow><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></mstyle><mo>+</mo><mi mathvariant=\"normal\">Q</mi><mo>&#160;</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math><br>तो, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mo>-</mo><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mo>+</mo><mi>Qsin&#945;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>-</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3 = 3</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. In triangle XYZ, A is a point on YZ such that XA = YA. If &ang;XYA = 50&deg; and &ang;AXZ = 19&deg;, what is the degree measure.</p>",
                    question_hi: "<p>65. त्रिभुज XYZ में, YZ पर एक बिंदु A इस प्रकार है कि XA = YA है। यदि &ang;XYA = 50&deg; और &ang;AXZ = 19&deg; है, तो &ang;XZA का डिग्री माप ज्ञात कीजिए।</p>",
                    options_en: ["<p>61&deg;</p>", "<p>53&deg;</p>", 
                                "<p>49&deg;</p>", "<p>41&deg;</p>"],
                    options_hi: ["<p>61&deg;</p>", "<p>53&deg;</p>",
                                "<p>49&deg;</p>", "<p>41&deg;</p>"],
                    solution_en: "<p>65.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340667535.png\" alt=\"rId56\" width=\"246\" height=\"144\"><br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;XYA</mi></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>YXA</mi></math> = &ang;AYX = 50&deg; &hellip;.(angle opposite to equal sides)<br>Now<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>XAZ</mi></math> = &ang;YXA + &ang;AYX = 50&deg; + 50&deg; = 100&deg;<br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;AXZ</mi></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>XZA</mi></math> + &ang;XAZ + &ang;AXZ = 180&deg; <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>XZA</mi></math> = 180&deg; - 100&deg;- 19&deg; <br>= <math display=\"inline\"><msup><mrow><mn>61</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math></p>",
                    solution_hi: "<p>65.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340667535.png\" alt=\"rId56\" width=\"246\" height=\"144\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;XYA</mi></math> में<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>YXA</mi></math> = &ang;AYX = 50&deg; &hellip;.(समान भुजाओं के विपरीत कोण)<br>अब<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>XAZ</mi></math> = &ang;YXA + &ang;AYX = 50&deg; + 50&deg; = 100&deg;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;AXZ</mi></math> में<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>XZA</mi></math> + &ang;XAZ + &ang;AXZ = 180&deg; <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#8736;</mi><mi>XZA</mi></math> = 180&deg; - 100&deg;- 19&deg; <br>= <math display=\"inline\"><msup><mrow><mn>61</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>9</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = 3, then the value of 9x&sup2; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>66. यदि x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#160;</mi><mn>9</mn><mi>x</mi></mrow></mfrac></math> = 3 हो, तो 9x&sup2; +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>9</mn><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>87</p>", "<p>81</p>", 
                                "<p>77</p>", "<p>79</p>"],
                    options_hi: ["<p>87</p>", "<p>81</p>",
                                "<p>77</p>", "<p>79</p>"],
                    solution_en: "<p>66.(d)<br>x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#160;</mi><mn>9</mn><mi>x</mi></mrow></mfrac></math> = 3<br>Multiplying both side by 3, we have ;<br>3x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>x</mi></mrow></mfrac></math> = 9<br>Then, 9x&sup2; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mn>9</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></mfrac></math>= 9<sup>2</sup> - 2 = 81 - 2 = 79</p>",
                    solution_hi: "<p>66.(d)<br>x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi mathvariant=\"normal\">&#160;</mi><mn>9</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = 3<br>दोनो तरफ 3 से गुणा करने , हमे प्राप्त होता है ;<br>3x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>x</mi></mrow></mfrac></math> = 9<br>तब, 9x&sup2; + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mn>9</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>2</mn></msup></mfrac></math>= 9<sup>2</sup> - 2 = 81 - 2 = 79</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The difference between two numbers is 48. The ratio of these two numbers is 7 : 3. What is the sum of the two numbers ?</p>",
                    question_hi: "<p>67. दो संख्याओं के बीच का अंतर 48 है। इन दोनों संख्याओं का अनुपात 7 : 3 है। इन दोनों संख्याओं का योग क्या है ?</p>",
                    options_en: ["<p>110</p>", "<p>100</p>", 
                                "<p>90</p>", "<p>120</p>"],
                    options_hi: ["<p>110</p>", "<p>100</p>",
                                "<p>90</p>", "<p>120</p>"],
                    solution_en: "<p>67.(d) <br>Let numbers be 7x&nbsp;and 3x respectively,<br>According to the question,<br>7x&nbsp;- 3x = 48 &rArr; x = 12<br>Sum of numbers = 10x &nbsp;= 120</p>",
                    solution_hi: "<p>67.(d) <br>माना संख्याएँ क्रमशः 7x&nbsp;और 3x हैं,<br>प्रश्न के अनुसार,<br>7x&nbsp;- 3x = 48 &rArr; x = 12<br>संख्याओं का योग = 10x&nbsp;= 120</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A boat can travel 60 km downstream in 4 hours. If the speed of the boat in still water is 13 km/h, then in what time will it cover 30 km upstream?</p>",
                    question_hi: "<p>68. एक नौका धारा के अनुकूल 60 km की यात्रा 4 घंटे में कर सकती है। यदि स्थिर जल में नौका की चाल 13 km/h है, तो धारा के प्रतिकूल 30 km की यात्रा यह कितने समय में तय करेगी?</p>",
                    options_en: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hours</p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hours</p>", 
                                "<p>2<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hours</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> hours</p>"],
                    options_hi: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे</p>", "<p>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे</p>",
                                "<p>2<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे</p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> घंटे</p>"],
                    solution_en: "<p>68.(d)<br>Speed of Boat = 13 km/h<br>Let the speed of stream be y&nbsp;km/h<br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>distance</mi><mi>speed</mi></mfrac></math><br>According to the question,<br>4 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mrow><mn>13</mn><mo>+</mo><mi>y</mi></mrow></mfrac></math><br>52 + 4<math display=\"inline\"><mi>y</mi></math> = 60<br>4<math display=\"inline\"><mi>y</mi></math> = 8 &rArr; y = 2 km/h<br>So, time taken by boat to cover 30 km in upstream = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>13</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>11</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>11</mn></mfrac></math> hours</p>",
                    solution_hi: "<p>68.(d)<br>नाव की गति = 13 km/h<br>माना शांत पानी में धारा की गति y km/h है <br>समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2352;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><mi>&#2327;&#2340;&#2367;</mi><mo>&#160;</mo></mrow></mfrac></math><br>प्रश्न के अनुसार,<br>4 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mrow><mn>13</mn><mo>+</mo><mi>y</mi></mrow></mfrac></math><br>52 + 4<math display=\"inline\"><mi>y</mi></math> = 60<br>4<math display=\"inline\"><mi>y</mi></math> = 8 &rArr; y = 2 किमी/घंटा<br>तो, धारा के विपरीत 30 किमी की दूरी तय करने में नाव द्वारा लिया गया समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mrow><mn>13</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>11</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>11</mn></mfrac></math>&nbsp;घंटेI</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A 12 litre solution of acid and water contains 30% acid. How much water (in litres) must be added to get a solution having 20% acid?</p>",
                    question_hi: "<p>69. एसिड और जल के 12 लीटर विलयन में 30% एसिड है। 20% एसिड वाला विलयन प्राप्त करने के लिए कितना जल (लीटर में) मिलाना होगा?</p>",
                    options_en: ["<p>6</p>", "<p>3</p>", 
                                "<p>5</p>", "<p>4</p>"],
                    options_hi: ["<p>6</p>", "<p>3</p>",
                                "<p>5</p>", "<p>4</p>"],
                    solution_en: "<p>69.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340667670.png\" alt=\"rId57\" width=\"267\" height=\"161\"><br>2 unit ------------- 12 L<br>1 unit ------------- <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6 L<br>Required quantity of water to be added = 6 L</p>",
                    solution_hi: "<p>69.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340667794.png\" alt=\"rId58\" width=\"215\" height=\"174\"><br>2 इकाई ------------- 12 लीटर<br>1 इकाई ------------- <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6 लीटर<br>मिलाए जाने वाले पानी की आवश्यक मात्रा = 6 लीटर</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. To finish a work in 12 days, 8 workers are engaged. After 2 days, due to some urgency, the work is to be finished in next 2 days. How many more workers are to be engaged now to finish it on time?</p>",
                    question_hi: "<p>70. किसी काम को 12 दिनों में पूरा करने के लिए 8 श्रमिक लगाए जाते हैं। 2 दिनों के बाद, कुछ तात्कालिकता के कारण, काम अगले 2 दिनों में समाप्त होना है। इसे समय पर पूरा करने के लिए कितने और श्रमिकों की आवश्यकता होगी?</p>",
                    options_en: ["<p>36</p>", "<p>32</p>", 
                                "<p>40</p>", "<p>30</p>"],
                    options_hi: ["<p>36</p>", "<p>32</p>",
                                "<p>40</p>", "<p>30</p>"],
                    solution_en: "<p>70.(b) <br>Total work = 12 &times; 8 = 96 unit<br>Work done by 8 workers in 2 days = 2 &times; 8 = 16 unit<br>Remaining work = 96 - 16 = 80 unit<br>Now, let required additional worker be x<br>According to question,<br>(8 + x) &times; 2 = 80 <br>2x&nbsp;= 64 &rArr; x = 32</p>",
                    solution_hi: "<p>70.(b) <br>कुल कार्य = 12 &times; 8 = 96 इकाई<br>8 श्रमिकों द्वारा 2 दिन में किया गया कार्य = 2 &times; 8 = 16 इकाई<br>शेष कार्य = 96 - 16 = 80 इकाई<br>अब, मान लीजिए कि आवश्यक अतिरिक्त कर्मचारी x&nbsp;है<br>प्रश्न के अनुसार,<br>(8 + x )&times; 2 = 80&nbsp;<br>2x&nbsp;= 64 &rArr; x = 32</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Ram travelled an equal distance with the speed of 40 km/hr, 45 km/hr and 60 km/hr. What is the average speed of Ram during the whole journey? Correct to two decimal places.</p>",
                    question_hi: "<p>71. राम ने 40 km/hr, 45 km/hr और 60 km/hr की चाल से समान दूरी तय की। पूरी यात्रा के दौरान राम की औसत चाल क्या थी? दशमलव के दो स्थानों तक सही।</p>",
                    options_en: ["<p>94.66 km/hr</p>", "<p>46.96 km/hr</p>", 
                                "<p>64.96 km/hr</p>", "<p>66.94 km/hr</p>"],
                    options_hi: ["<p>94.66 km/hr</p>", "<p>46.96 km/hr</p>",
                                "<p>64.96 km/hr</p>", "<p>66.94 km/hr</p>"],
                    solution_en: "<p>71.(b)<br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>distance</mi><mi mathvariant=\"normal\">&#160;</mi><mi>covered</mi></mrow><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi><mi mathvariant=\"normal\">&#160;</mi><mi>taken</mi></mrow></mfrac></math><br>Ram travels an equal distance <br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mfrac><mi mathvariant=\"normal\">x</mi><mn>40</mn></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">x</mi><mn>45</mn></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">x</mi><mn>60</mn></mfrac></mrow></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>9</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>6</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>360</mn></mfrac></mstyle></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#215;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>23</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = 46.96 km/hr</p>",
                    solution_hi: "<p>71.(b)<br>औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2340;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2327;&#2312;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2354;&#2367;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2327;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math><br>राम समान दूरी तय करता है <br>औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mfrac><mi mathvariant=\"normal\">x</mi><mn>40</mn></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">x</mi><mn>45</mn></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">x</mi><mn>60</mn></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mn>9</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>6</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>360</mn></mfrac></mstyle></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>360</mn><mo>&#215;</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>23</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math>&nbsp;= 46.96 किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The salary of a worker is first increased by 25%, then it is decreased by 12%. The percentage change in his salary is:</p>",
                    question_hi: "<p>72. एक कर्मचारी के वेतन में पहले 25% की वृद्धि की जाती है, फिर 12% की कमी की जाती है। उसके वेतन में प्रतिशत परिवर्तन कितना है?</p>",
                    options_en: ["<p>10% increase</p>", "<p>13% increase</p>", 
                                "<p>10% decrease</p>", "<p>13% decrease</p>"],
                    options_hi: ["<p>10% वृद्धि</p>", "<p>13% वृद्धि</p>",
                                "<p>10% कमी</p>", "<p>13% कमी</p>"],
                    solution_en: "<p>72.(a)<br>% change in salary = (25 - 12 - <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> )% = 13 - 3 = +10% or 10% increase</p>",
                    solution_hi: "<p>72.(a)<br>वेतन में % परिवर्तन = (25 - 12 - <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mn>12</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> )% = 13 - 3 = +10% या 10% वृद्धि</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. In how many years will the simple interest on a sum of money be equal to the principal at the rate of 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>% per annum?</p>",
                    question_hi: "<p>73. कितने वर्षों में किसी धनराशि पर 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>% वार्षिक की दर से साधारण ब्याज मूलधन के बराबर हो जायेगा?</p>",
                    options_en: ["<p>34 years</p>", "<p>37 years</p>", 
                                "<p>35 years</p>", "<p>33 years</p>"],
                    options_hi: ["<p>34 वर्ष</p>", "<p>37 वर्ष</p>",
                                "<p>35 वर्ष</p>", "<p>33 वर्ष</p>"],
                    solution_en: "<p>73.(c) Let principal = x</p>\n<p>S.I = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>R</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>according to question</p>\n<p>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>20</mn><mo>&#160;</mo><mo>&#215;</mo><mi>T</mi><mo>&#160;</mo></mrow><mrow><mn>100</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>7</mn></mrow></mfrac></math></p>\n<p>T = 35 years&nbsp;</p>",
                    solution_hi: "<p>73.(c)</p>\n<p>माना मूलधन = x</p>\n<p>S.I = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>R</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>प्रश्न के अनुसार</p>\n<p>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>20</mn><mo>&#160;</mo><mo>&#215;</mo><mi>T</mi><mo>&#160;</mo></mrow><mrow><mn>100</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>7</mn></mrow></mfrac></math></p>\n<p>T = 35 years&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The average marks of 60 students was found to be 48. Later on, it was discovered that a score of 68 was misread as 56. Find the correct average corresponding to the correct score.</p>",
                    question_hi: "<p>74. 60 विद्यार्थियों के औसत अंक 48 पाए गए। बाद में, यह पाया गया कि 68 अंक को गलती से 56 पढ़ लिया गया था। सही अंक के अनुरूप सही औसत ज्ञात कीजिए।</p>",
                    options_en: ["<p>54.2</p>", "<p>44.2</p>", 
                                "<p>40.8</p>", "<p>48.2</p>"],
                    options_hi: ["<p>54.2</p>", "<p>44.2</p>",
                                "<p>40.8</p>", "<p>48.2</p>"],
                    solution_en: "<p>74.(d)<br>Correct average = Incorrect average +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>68</mn><mo>-</mo><mn>56</mn></mrow><mn>60</mn></mfrac></math><br>= 48 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>60</mn></mfrac></math>= 48 + 0.2 = 48.2</p>",
                    solution_hi: "<p>74.(d)<br>सही औसत = ग़लत औसत + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>68</mn><mo>-</mo><mn>56</mn></mrow><mn>60</mn></mfrac></math><br>= 48 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>60</mn></mfrac></math>= 48 + 0.2 = 48.2</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The given line graph shows the number of scooters manufactured (in thousands) by companies X and Z, over the years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340667899.png\" alt=\"rId59\" width=\"500\" height=\"327\"> <br>What is the approximate difference between the total production by companies X and Z in the given years?</p>",
                    question_hi: "<p>75. दिया गया लाइन ग्राफ पिछले वर्षों में कंपनी X और Z द्वारा निर्मित स्कूटरों की संख्या (हजार में) दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739340668018.png\" alt=\"rId60\" width=\"500\"> <br>दिए गए वर्षों में कंपनी X और Z द्वारा कुल उत्पादन के बीच लगभग कितना अंतर है?</p>",
                    options_en: ["<p>205000</p>", "<p>292000</p>", 
                                "<p>210000</p>", "<p>192000</p>"],
                    options_hi: ["<p>205000</p>", "<p>292000</p>",
                                "<p>210000</p>", "<p>192000</p>"],
                    solution_en: "<p>75.(c)<br>Total production of scooters in company X = 128 + 110 + 102 + 80 + 109 + 161 = 690<br>Total production of scooters in company Y = 141 + 192 + 145 + 130 + 122 + 170= 900<br>Required difference = 900 - 690 = 210 <strong>&times; 1000</strong> = 210000 (value is given in thousand)</p>",
                    solution_hi: "<p>75.(c)<br>कंपनी X में स्कूटरों का कुल उत्पादन = 128 + 110 + 102 + 80 + 109 + 161 = 690<br>कंपनी Y में स्कूटरों का कुल उत्पादन = 141 + 192 + 145 + 130 + 122 + 170= 900<br>आवश्यक अंतर = 900- 690 = 210 <strong>&times; 1000 </strong>= 210000 (मूल्य हजार में दिया गया है)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate meaning of the given idiom.<br>Break a leg</p>",
                    question_hi: "<p>76. Select the most appropriate meaning of the given idiom.<br>Break a leg</p>",
                    options_en: ["<p>Hurt yourself</p>", "<p>Wish bad luck</p>", 
                                "<p>Wish good luck</p>", "<p>Sprain a leg</p>"],
                    options_hi: ["<p>Hurt yourself</p>", "<p>Wish bad luck</p>",
                                "<p>Wish good luck</p>", "<p>Sprain a leg</p>"],
                    solution_en: "<p>76.(c) <strong>Break a leg-</strong> wish good luck.<br>E.g.- The coach smiled and told the team to break a leg before the big game.</p>",
                    solution_hi: "<p>76.(c) <strong>Break a leg-</strong> wish good luck./शुभकामनाएँ देना। <br>E.g.- The coach smiled and told the team to break a leg before the big game.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. The following sentence has been divided into four segments. Identify the segment that has a grammatical error.<br>When he resigned, / the company offered him / a huge sum of money but / he refused to agree to it.</p>",
                    question_hi: "<p>77. The following sentence has been divided into four segments. Identify the segment that has a grammatical error.<br>When he resigned, / the company offered him / a huge sum of money but / he refused to agree to it.</p>",
                    options_en: ["<p>the company offered him</p>", "<p>When he resigned,</p>", 
                                "<p>he refused to agree to it.</p>", "<p>a huge sum of money but</p>"],
                    options_hi: ["<p>the company offered him</p>", "<p>When he resigned,</p>",
                                "<p>he refused to agree to it.</p>", "<p>a huge sum of money but</p>"],
                    solution_en: "<p>77.(c) he refused to agree to it<br>The use of the verb &lsquo;agree&rsquo; after &lsquo;refused&rsquo; is superfluous. &lsquo;Refuse&rsquo; means to not agree to accept something. The phrase &lsquo;to agree to it&rsquo; must be replaced with &lsquo;offer&rsquo;. Hence, &lsquo;he refused the offer&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(c) he refused to agree to it<br>&lsquo;refused&rsquo; के बाद verb &lsquo;agree&rsquo; का प्रयोग superfluous है। &lsquo;Refuse&rsquo; का अर्थ है किसी something को accept करने के लिए सहमत न होना। Phrase &lsquo;to agree to it&rsquo; को &lsquo;offer&rsquo; से बदला जाना चाहिए। इसलिए, &lsquo;he refused the offer&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The teacher has instructed all of us to finish the work by tomorrow, <span style=\"text-decoration: underline;\"><strong>isn&rsquo;t it</strong></span> ?</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The teacher has instructed all of us to finish the work by tomorrow, <span style=\"text-decoration: underline;\"><strong>isn&rsquo;t it</strong></span> ?</p>",
                    options_en: ["<p>has she?</p>", "<p>didn&rsquo;t she ?</p>", 
                                "<p>hasn&rsquo;t she ?</p>", "<p>No improvement</p>"],
                    options_hi: ["<p>has she?</p>", "<p>didn&rsquo;t she ?</p>",
                                "<p>hasn&rsquo;t she ?</p>", "<p>No improvement</p>"],
                    solution_en: "<p>78.(c) hasn&rsquo;t she?<br>According to the &ldquo;<span style=\"text-decoration: underline;\">Question Tag rule</span>&rdquo;, if the statement is positive, the question tag will be negative but in the contracted form(&lsquo;t). However, the sentence and the question tag must be <span style=\"text-decoration: underline;\">in the same tense</span>. Similarly, the given sentence is positive so the question tag will be negative(hasn&rsquo;t). Hence, &lsquo;hasn&rsquo;t she&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(c) hasn&rsquo;t she?<br>\"Question Tag नियम\" के अनुसार, यदि statement positive है, तो Question Tag negative होगा लेकिन contracted form (\'t) में होगा। Sentence और Question Tag को <span style=\"text-decoration: underline;\"><strong>एक ही tense में होने चाहिए।</strong></span> इसी तरह, दिया गया sentence positive है इसलिए Question Tag negative (hasn&rsquo;t) होगा। इसलिए, \'hasn&rsquo;t she\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Identify the most appropriate ANTONYM of the given word.<br>Reliable</p>",
                    question_hi: "<p>79. Identify the most appropriate ANTONYM of the given word.<br>Reliable</p>",
                    options_en: ["<p>Unknown</p>", "<p>Untrustworthy</p>", 
                                "<p>Uncommon</p>", "<p>Unfamiliar</p>"],
                    options_hi: ["<p>Unknown</p>", "<p>Untrustworthy</p>",
                                "<p>Uncommon</p>", "<p>Unfamiliar</p>"],
                    solution_en: "<p>79.(b) <strong>Untrustworthy-</strong> not able to be trusted.<br><strong>Reliable-</strong> able to be trusted.<br><strong>Unknown-</strong> not known.<br><strong>Uncommon-</strong> not seen very often.<br><strong>Unfamiliar-</strong> not well known.</p>",
                    solution_hi: "<p>79.(b) <strong>Untrustworthy</strong> (विश्वास न करने योग्य व्यक्ति) - not able to be trusted.<br><strong>Reliable</strong> (भरोसेमंद) - able to be trusted.<br><strong>Unknown</strong> (अनजान) - not known.<br><strong>Uncommon</strong> (असामान्य) - not seen very often.<br><strong>Unfamiliar</strong> (अपरिचित) - not well known.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who renounces a religious or political belief or principle.</p>",
                    question_hi: "<p>80. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who renounces a religious or political belief or principle.</p>",
                    options_en: ["<p>Agnostic</p>", "<p>Ascetic</p>", 
                                "<p>Apostate</p>", "<p>Atheist</p>"],
                    options_hi: ["<p>Agnostic</p>", "<p>Ascetic</p>",
                                "<p>Apostate</p>", "<p>Atheist</p>"],
                    solution_en: "<p>80.(c) <strong>Apostate-</strong> a person who renounces a religious or political belief or principle.<br><strong>Agnostic-</strong> one who is not sure about the existence of god.<br><strong>Ascetic-</strong> avoiding physical pleasures and living a simple life, often for religious reasons.<br><strong>Atheist-</strong> one who does not believe in the existence of God.</p>",
                    solution_hi: "<p>80.(c) <strong>Apostate</strong> (धर्मत्यागी)- a person who renounces a religious or political belief or principle.<br><strong>Agnostic</strong> (अनीश्वरवादी )- one who is not sure about the existence of god.<br><strong>Ascetic</strong> (तपस्वी)- avoiding physical pleasures and living a simple life, often for religious reasons.<br><strong>Atheist</strong> (नास्तिक)- one who does not believe in the existence of God.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the given sentence in passive voice<br>Let me buy an expensive bag for my friend.</p>",
                    question_hi: "<p>81. Select the option that expresses the given sentence in passive voice<br>Let me buy an expensive bag for my friend.</p>",
                    options_en: ["<p>I request you to buy an expensive bag for my friend.</p>", "<p>An excessive bag will be bought by me for my friend.</p>", 
                                "<p>Let an expensive bag was bought by me for my friend.</p>", "<p>Let an expensive bag be bought by me for my friend.</p>"],
                    options_hi: ["<p>I request you to buy an expensive bag for my friend.</p>", "<p>An excessive bag will be bought by me for my friend.</p>",
                                "<p>Let an expensive bag was bought by me for my friend.</p>", "<p>Let an expensive bag be bought by me for my friend.</p>"],
                    solution_en: "<p>81.(d) Let an expensive bag be bought by me for my friend. (Correct)<br>(a) I request you to buy an expensive bag for my friend. (Incorrect Sentence Structure)<br>(b) An excessive bag will be bought by me for my friend. (Incorrect Sentence Structure)<br>(c) Let an expensive bag <span style=\"text-decoration: underline;\">was</span> bought by me for my friend. (Incorrect Verb)</p>",
                    solution_hi: "<p>81.(d) Let an expensive bag be bought by me for my friend.<br>(Correct)<br>(a) I request you to buy an expensive bag for my friend. (गलत Sentence Structure)<br>(b) An excessive bag will be bought by me for my friend. (गलत Sentence Structure)<br>(c) Let an expensive bag <span style=\"text-decoration: underline;\">was</span> bought by me for my friend. (गलत Verb)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence. <br>He said, &lsquo;I have read this novel.&rsquo;</p>",
                    question_hi: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence. <br>He said, &lsquo;I have read this novel.&rsquo;</p>",
                    options_en: ["<p>He said that he has read this novel.</p>", "<p>He said that he had read that novel.</p>", 
                                "<p>He said that he- read that novel.</p>", "<p>He said that he had read this novel.</p>"],
                    options_hi: ["<p>He said that he has read this novel.</p>", "<p>He said that he had read that novel.</p>",
                                "<p>He said that he- read that novel.</p>", "<p>He said that he had read this novel.</p>"],
                    solution_en: "<p>82.(b) He said that he had read that novel. <br>(a) He said that he <strong>has read</strong> this novel. ( Incorrect change of tense) <br>(c) He said that <strong>he read</strong> that novel. (Incorrect change of tense) <br>(d) He said that he had read <strong>this</strong> novel. (Incorrect word used)</p>",
                    solution_hi: "<p>82.(b) He said that he had read that novel. <br>(a) He said that he <strong>has read</strong> this novel. (Tense का गलत परिवर्तन) <br>(c) He said that <strong>he read</strong> that novel. (Tense का गलत परिवर्तन) <br>(d) He said that he had read <strong>this</strong> novel. (गलत शब्द का प्रयोग)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Choose the most appropriate synonym for the word given.</p>\n<p>Ramifications</p>",
                    question_hi: "<p>83. Choose the most appropriate synonym for the word given.</p>\n<p>Ramifications</p>",
                    options_en: ["<p>uses</p>", "<p>developments</p>", 
                                "<p>consequences</p>", "<p>conclusions</p>"],
                    options_hi: ["<p>uses</p>", "<p>developments</p>",
                                "<p>consequences</p>", "<p>conclusions</p>"],
                    solution_en: "<p>83.(c) consequences. <br><strong>Ramification</strong> - a complex or unwelcome consequence of an action or event.</p>",
                    solution_hi: "<p>83.(c) consequences. <br><strong>Ramification</strong> का अर्थ है कोई घटना या अप्रिय परिणाम।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement.<br>O: its first constitution<br>P: and soon got himself elected<br>Q: the administration of the country<br>R: general Ayub Khan took over<br>S: after Pakistan framed</p>",
                    question_hi: "<p>84. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement.<br>O: its first constitution<br>P: and soon got himself elected<br>Q: the administration of the country<br>R: general Ayub Khan took over<br>S: after Pakistan framed</p>",
                    options_en: ["<p>SORQP</p>", "<p>SPORQ</p>", 
                                "<p>POSRQ</p>", "<p>ROPQS</p>"],
                    options_hi: ["<p>SORQP</p>", "<p>SPORQ</p>",
                                "<p>POSRQ</p>", "<p>ROPQS</p>"],
                    solution_en: "<p>84.(a) <strong>SORQP</strong><br>The given sentence starts with Part S as it introduces the main idea of the sentence, i.e. the time period after Pakistan framed its first constitution. Part O contains the object of the verb &lsquo;framed&rsquo; &amp; Part R contains the main subject of the sentence. So, R will follow O. Further, Part Q states that general Ayub Khan took over the administration of the country &amp; Part P states that he soon got himself elected. So, P will follow Q. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>84.(a) <strong>SORQP</strong><br>दिया गया sentence, Part S से शुरू होता है क्योंकि यह sentence के मुख्य विचार &lsquo;the time period after Pakistan framed its first constitution&rsquo; को प्रस्तुत करता है। Part O में verb \'framed\' का object शामिल है और Part R में sentence का main subject शामिल है। इसलिए, O के बाद R आएगा। इसके अलावा, Part Q में कहा गया है कि जनरल अयूब खान ने देश का प्रशासन संभाला और Part P में कहा गया है कि उन्होंने जल्द ही स्वयं को निर्वाचित (elected) कर लिया। इसलिए, Q के बाद P, आएगा। अतः options के माध्यम से जाने पर, option \'a\' में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85.&nbsp;Find the correctly spelt word.</p>",
                    question_hi: "<p>85.&nbsp;Find the correctly spelt word.</p>",
                    options_en: ["<p>longveity</p>", "<p>observible</p>", 
                                "<p>playfull</p>", "<p>Idolatry</p>"],
                    options_hi: ["<p>longveity</p>", "<p>observible</p>",
                                "<p>playfull</p>", "<p>Idolatry</p>"],
                    solution_en: "<p>85.(d) <strong>Idolatry</strong> <br>Other words - longevity , observable , playful</p>",
                    solution_hi: "<p>85.(d) <strong>Idolatry/</strong> मूर्ति पूजा <br>Other words - longevity , observable , playful</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate option that can substitute the underlined words in the given sentence. <br>The road is too busy for elderly people <span style=\"text-decoration: underline;\">that they cannot cross safely</span>.</p>",
                    question_hi: "<p>86. Select the most appropriate option that can substitute the underlined words in the given sentence. <br>The road is too busy for elderly people <span style=\"text-decoration: underline;\">that they cannot cross safely</span>.</p>",
                    options_en: ["<p>that it is impossible for them to cross</p>", "<p>to cross safely</p>", 
                                "<p>that it cannot be crossed properly.</p>", "<p>that it is difficult to cross.</p>"],
                    options_hi: ["<p>that it is impossible for them to cross</p>", "<p>to cross safely</p>",
                                "<p>that it cannot be crossed properly.</p>", "<p>that it is difficult to cross.</p>"],
                    solution_en: "<p>86.(b) to cross safely<br>&lsquo;Too + adjective/ adverb + to + V<sub>1</sub>&rsquo; is the correct grammatical structure. Hence, &lsquo;to cross(V<sub>1</sub>) safely&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>86.(b) to cross safely<br>&lsquo;Too + adjective/ adverb + to + V<sub>1</sub>&rsquo; सही grammatical structure है। इसलिए, &lsquo;to cross(V<sub>1</sub>) safely&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the idiom given in <strong>Bold/Underline</strong> in the following Question.<br>The lecture by the French professor seemed <span style=\"text-decoration: underline;\"><strong>Latin and Greek</strong></span> to the young students.</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the idiom given in <strong>Bold/Underline</strong> in the following Question.<br>The lecture by the French professor seemed <span style=\"text-decoration: underline;\"><strong>Latin and Greek</strong></span> to the young students.</p>",
                    options_en: ["<p>Interesting</p>", "<p>Lucid</p>", 
                                "<p>Unbiased</p>", "<p>Incomprehensible</p>"],
                    options_hi: ["<p>Interesting</p>", "<p>Lucid</p>",
                                "<p>Unbiased</p>", "<p>Incomprehensible</p>"],
                    solution_en: "<p>87.(d) <strong>Latin and Greek</strong> - Incomprehensible</p>",
                    solution_hi: "<p>87.(d) <strong>Latin and Greek</strong> - Incomprehensible</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the option that expresses the given sentence in passive voice. <br>Take the wheels.</p>",
                    question_hi: "<p>88. Select the option that expresses the given sentence in passive voice. <br>Take the wheels.</p>",
                    options_en: ["<p>Let the wheels take you</p>", "<p>Let you take the wheels</p>", 
                                "<p>Let the wheels be taken by you.</p>", "<p>Let wheels you take.</p>"],
                    options_hi: ["<p>Let the wheels take you</p>", "<p>Let you take the wheels</p>",
                                "<p>Let the wheels be taken by you.</p>", "<p>Let wheels you take.</p>"],
                    solution_en: "<p>88.(c) Let the wheels be taken by you (Correct)<br>(a) Let the wheels take you. (Incorrect Sentence structure)<br>(b) Let you take the wheels. (Incorrect Sentence structure)<br>(d) Let wheels you take. (Incorrect Sentence structure)</p>",
                    solution_hi: "<p>88.(c) Let the wheels be taken by you (Correct)<br>(a) Let the wheels take you. (गलत Sentence structure)<br>(b) Let you take the wheels. (गलत Sentence structure)<br>(d) Let wheels you take. (गलत Sentence structure)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>A person who remains unmoved and unaffected by other people\'s opinions, suggestions</p>",
                    question_hi: "<p>89. Out of the four alternatives choose the one which can be substituted for the given words/sentences.<br>A person who remains unmoved and unaffected by other people\'s opinions, suggestions</p>",
                    options_en: ["<p>Irresponsible</p>", "<p>Unreliable</p>", 
                                "<p>Imprudent</p>", "<p>Impervious</p>"],
                    options_hi: ["<p>Irresponsible</p>", "<p>Unreliable</p>",
                                "<p>Imprudent</p>", "<p>Impervious</p>"],
                    solution_en: "<p>89.(d) Impervious<br><strong>Impervious-</strong> A person who remains unmoved and unaffected by other <strong>people\'s</strong> opinions, suggestions <br><strong>Irresponsible-</strong> not thinking about the effect your actions will have <br><strong>Unreliable-</strong> that cannot be trusted or depended on <br><strong>Imprudent-</strong> not showing care for the consequences of an action</p>",
                    solution_hi: "<p>89.(d) Impervious<br><strong>Impervious</strong> (प्रबल)- A person who remains unmoved and unaffected by other people\'s opinions, suggestions <br><strong>Irresponsible</strong> (गैर जिम्मेदार)- not thinking about the effect your actions will have <br><strong>Unreliable</strong> (अविश्वसनीय)- that cannot be trusted or depended on <br><strong>Imprudent</strong> (ढीठ)- not showing care for the consequences of an action</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate homophones to fill in the blanks. <br>I ________ that he has been promoted to the post of _____ officer.</p>",
                    question_hi: "<p>90. Select the most appropriate homophones to fill in the blanks. <br>I ________ that he has been promoted to the post of _____ officer.</p>",
                    options_en: ["<p>heard; ceiling</p>", "<p>heard; sealing</p>", 
                                "<p>herd; ceiling</p>", "<p>herd; sealing</p>"],
                    options_hi: ["<p>heard; ceiling</p>", "<p>heard; sealing</p>",
                                "<p>herd; ceiling</p>", "<p>herd; sealing</p>"],
                    solution_en: "<p>90.(b) heard, sealing<br>&lsquo;Heard&rsquo; is the past form of the verb &lsquo;hear&rsquo;. A sealing officer is someone who officially locks items or places to keep them secure. The given sentence states that I heard that he has been promoted to the post of sealing officer. Hence, &lsquo;heard; sealing&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>90.(b) heard, sealing<br>&lsquo;Heard&rsquo;, verb &lsquo;hear&rsquo; का past form है। एक sealing officer वह व्यक्ति होता है जो वस्तुओं (items ) या स्थानों (places) को सुरक्षित रखने के लिए उन्हे officially lock करता है। दिए गए sentence में कहा गया है कि मैंने सुना है कि उसे sealing officer के पद पर promote किया गया है। अत: &lsquo;heard; sealing&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the given word.<br>Voracious</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the given word.<br>Voracious</p>",
                    options_en: ["<p>Compensated</p>", "<p>Quenched</p>", 
                                "<p>Appreciate</p>", "<p>Greedy</p>"],
                    options_hi: ["<p>Compensated</p>", "<p>Quenched</p>",
                                "<p>Appreciate</p>", "<p>Greedy</p>"],
                    solution_en: "<p>91.(d) <strong>Greedy-</strong> having an intense desire for something, especially food.<br><strong>Voracious-</strong> very eager for something, especially a lot of food.<br><strong>Compensated-</strong> provided something to balance or offset.<br><strong>Quenched-</strong> satisfied thirst or desire.<br><strong>Appreciate-</strong> to recognize the value or significance of something.</p>",
                    solution_hi: "<p>91.(d) <strong>Greedy</strong> (लालची) - having an intense desire for something, especially food.<br><strong>Voracious</strong> (पेटू) - very eager for something, especially a lot of food.<br><strong>Compensated</strong> (मुआवजा दिया) - provided something to balance or offset.<br><strong>Quenched</strong> (बुझ गया) - satisfied thirst or desire.<br><strong>Appreciate</strong> (प्रशंसा करना) - to recognize the value or significance of something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Given below are four jumbled sentences. Pick the option that gives their correct order. <br>P. These model inventions showed the progress of Science in our Schools.<br>Q.The best exhibit was a robot that served us tea.<br>R.The young scientists were explaining their inventions with pride.<br>S.There were 160 exhibits on display in the science fair..</p>",
                    question_hi: "<p>92. Given below are four jumbled sentences. Pick the option that gives their correct order. <br>P. These model inventions showed the progress of Science in our Schools.<br>Q.The best exhibit was a robot that served us tea.<br>R.The young scientists were explaining their inventions with pride.<br>S.There were 160 exhibits on display in the science fair..</p>",
                    options_en: ["<p>RPQS</p>", "<p>QRPS</p>", 
                                "<p>SPRQ</p>", "<p>PRSQ</p>"],
                    options_hi: ["<p>RPQS</p>", "<p>QRPS</p>",
                                "<p>SPRQ</p>", "<p>PRSQ</p>"],
                    solution_en: "<p>92.(c) <strong>SPRQ</strong><br>Sentence S will be the starting line as it contains the main idea of the parajumble i.e. 160 exhibits on display in the science fair. However, Sentence P states that these model inventions showed the progress of Science in our Schools. So, P will follow S. Further, Sentence R states that the young scientists were explaining their inventions with pride &amp; Sentence Q states that the best exhibit was a robot that served us tea. So, Q will follow R. Going through the options, only option c has the correct sequence.</p>",
                    solution_hi: "<p>92.(c) <strong>SPRQ</strong><br>वाक्य S शुरुआती line होगी क्योंकि इसमें parajumble का मुख्य विचार शामिल है अर्थात विज्ञान मेले में प्रदर्शित होने वाली 160 प्रदर्शनी है। हालाँकि, वाक्य P कहता है कि इन मॉडल आविष्कारों ने हमारे स्कूलों में विज्ञान की प्रगति को दिखाया। तो, S के बाद P आयेगा । आगे, वाक्य R कहता है कि युवा वैज्ञानिक अपने आविष्कारों को गर्व के साथ समझा रहे थे और वाक्य Q बताता है कि सबसे अच्छा प्रदर्शन एक रोबोट का था जो हमें चाय परोसता था।अतः R के बाद Q आयेगा। विकल्पों के माध्यम से जाने पर, केवल विकल्प c में सही क्रम है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>Our is the only country in the world that can boast of unity in diversity.</p>",
                    question_hi: "<p>93. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>Our is the only country in the world that can boast of unity in diversity.</p>",
                    options_en: ["<p>Our is only country</p>", "<p>in the world that can boast of</p>", 
                                "<p>unity in diversity</p>", "<p>No error</p>"],
                    options_hi: ["<p>Our is only country</p>", "<p>in the world that can boast of</p>",
                                "<p>unity in diversity</p>", "<p>No error</p>"],
                    solution_en: "<p>93.(a) Our is only country<br>Both the structures &lsquo;It is only our country&rsquo; and &lsquo;Ours is the only country&rsquo; are correct in the context of the given sentence.</p>",
                    solution_hi: "<p>93.(a) Our is only country<br>दिए गए वाक्य के संदर्भ में दोनों संरचनाएँ \'It is only our country\' और \'Ours is the only country\' सही हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate ANTONYM of the bracketed word in the following sentence to fill in the blank.<br>My friend is not stubborn with regard to opinions; in fact, she is____________ (intransigent).</p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the bracketed word in the following sentence to fill in the blank.<br>My friend is not stubborn with regard to opinions; in fact, she is____________ (intransigent).</p>",
                    options_en: ["<p>pertinacious</p>", "<p>obstinate</p>", 
                                "<p>subservient</p>", "<p>tenacious</p>"],
                    options_hi: ["<p>pertinacious</p>", "<p>obstinate</p>",
                                "<p>subservient</p>", "<p>tenacious</p>"],
                    solution_en: "<p>94.(c) <strong>Subservient-</strong> willing to obey others without question.<br><strong>Intransigent-</strong> unwilling to change opinions or agree with others.<br><strong>Pertinacious-</strong> stubbornly holding on to an idea or purpose.<br><strong>Obstinate-</strong> refusing to change behavior or beliefs, even when persuaded.<br><strong>Tenacious-</strong> not giving up easily.</p>",
                    solution_hi: "<p>94.(c) <strong>Subservient(अधीन)-</strong> willing to obey others without question.<br><strong>Intransigent</strong> (हठी)- unwilling to change opinions or agree with others.<br><strong>Pertinacious</strong> (अड़ियल)- stubbornly holding on to an idea or purpose.<br><strong>Obstinate</strong> (जिद्दी)- refusing to change behavior or beliefs, even when persuaded.<br><strong>Tenacious</strong> (दृढ़)- not giving up easily.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank. <br>I ________ filled the form before my mother told me to do so.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank. <br>I ________ filled the form before my mother told me to do so.</p>",
                    options_en: ["<p>had</p>", "<p>have</p>", 
                                "<p>had had</p>", "<p>has had</p>"],
                    options_hi: ["<p>had</p>", "<p>have</p>",
                                "<p>had had</p>", "<p>has had</p>"],
                    solution_en: "<p>95.(a) had. <br>If two actions took place in the past then the 1st action must be in the Past perfect tense &ldquo;Had + V<sub>3 </sub>(third form of the verb)&rdquo; and the 2nd action must be in the Simple Past tense(V<sub>2</sub>). Similarly, in the given sentence, filling up the form was the first action. Hence, &lsquo;had filled(V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(a) had. <br>यदि दो कार्य ,past में हुए हैं तो पहली action, Past perfect tense में होनी चाहिए \"Had V<sub>3</sub> (verb का third form)\" और दूसरी Simple Past tense (V<sub>2</sub>) में होनी चाहिए। इसी प्रकार दिए गए वाक्य में फॉर्म भरना पहला कार्य था । इसलिए, &lsquo;had filled(V<sub>3</sub>)\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99)______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities. <br>Select the most appropriate option to fill in the blank no 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99)______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: ["<p>simplest</p>", "<p>more simpler</p>", 
                                "<p>simple</p>", "<p>simpler</p>"],
                    options_hi: ["<p>simplest</p>", "<p>more simpler</p>",
                                "<p>simple</p>", "<p>simpler</p>"],
                    solution_en: "<p>96.(d) &ldquo;simpler&rdquo; <br>&ldquo;simpler&rdquo; should be used. &ldquo;In the given sentence, comparison is done between the processes of centuries ago and that of today. So; a comparative degree should be used&rdquo;.</p>",
                    solution_hi: "<p>96.(d) &ldquo;simpler&rdquo;<br>&ldquo;Simpler&rdquo; इस्तेमाल किया जाना चाहिए।. &ldquo;दिए गए वाक्य में सदियों पहले की प्रक्रियाओं और आज की प्रक्रियाओं के बीच तुलना की गई है। इसलिए; comparative degree का उपयोग किया जाना चाहिए।&rdquo;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99)______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99)______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: ["<p>chose</p>", "<p>followed</p>", 
                                "<p>chased</p>", "<p>worked</p>"],
                    options_hi: ["<p>chose</p>", "<p>followed</p>",
                                "<p>chased</p>", "<p>worked</p>"],
                    solution_en: "<p>97.(b) followed<br>follow (one\'s) footsteps. To pursue something that someone else (often a family member) has already done.</p>",
                    solution_hi: "<p>97.(b) followed <br>किसी के नक्शेकदम पर चलना। कुछ ऐसा करना जो कोई और (अक्सर परिवार का सदस्य) पहले ही कर चुका हो।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99)______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99)______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: ["<p>the</p>", "<p>a</p>", 
                                "<p>each</p>", "<p>an</p>"],
                    options_hi: ["<p>the</p>", "<p>a</p>",
                                "<p>each</p>", "<p>an</p>"],
                    solution_en: "<p>98.(a) the<br>&ldquo;a&rdquo;, &ldquo;an&rdquo; and &ldquo;each&rdquo; can&rsquo;t be used for plural (here, household skills). So , option (a) is the answer.</p>",
                    solution_hi: "<p>98.(a) the<br>&ldquo;a&rdquo;, &ldquo;an&rdquo; तथा&ldquo;each&rdquo; का उपयोग बहुवचन (यहाँ, घरेलू कौशल) के लिए नहीं किया जा सकता है। अतः विकल्प (a) उत्तर है ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99)______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99)______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: ["<p>become</p>", "<p>becoming</p>", 
                                "<p>becomes</p>", "<p>became</p>"],
                    options_hi: ["<p>become</p>", "<p>becoming</p>",
                                "<p>becomes</p>", "<p>became</p>"],
                    solution_en: "<p>99.(a) become <br>&ldquo;To&rdquo; should be followed by &ldquo;1st form of the verb&rdquo; In this sentence, &ldquo;become&rdquo; should be used.</p>",
                    solution_hi: "<p>99.(a) become <br>&ldquo;To&rdquo; के बाद \"verb 1st form\" होना चाहिए इसलिए, इस वाक्य में, \"become\" का प्रयोग किया जाना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99)______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-</strong><br>A century ago the process of choosing a career was a (96)______ matter than it is today. A boy often (97)______ in his father&rsquo;s footsteps. A girl learned (98)______ household skills that helped her to (99) ______ a good wife and mother. Now-a-days (100)______ people grow up in a much freer society where they enjoy unlimited career opportunities.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: ["<p>old</p>", "<p>sensible</p>", 
                                "<p>young</p>", "<p>mature</p>"],
                    options_hi: ["<p>old</p>", "<p>sensible</p>",
                                "<p>young</p>", "<p>mature</p>"],
                    solution_en: "<p>100.(c) young<br>&ldquo;Old&rdquo; and &ldquo;mature&rdquo; are used for the grown-up ones. Option (b) sensible is irrelevant in the context of the passage. </p>",
                    solution_hi: "<p>100.(c) young<br>&ldquo;Old&rdquo; और &ldquo;mature&rdquo; का उपयोग वयस्क के लिए किया जाता है। Passage के संदर्भ में विकल्प (b) sensible असंगत है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>