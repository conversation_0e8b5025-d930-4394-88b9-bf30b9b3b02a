<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Total number of students in different subjects of a college and percentage of girls and boys are shown in the below table.&nbsp;Girls students and boys student are NOT having multiple subjects.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837311284.png\" alt=\"rId5\" width=\"498\" height=\"131\"> <br>What is the ratio of girl students of Mathematics and Chemistry to the number of boy students of Physics and Biology?</p>",
                    question_hi: "<p>1. एक महाविद्यालय के विभिन्न विषयों में छात्रों की कुल संख्या और बालिकाओं और बालकों का प्रतिशत नीचे दी गई तालिका में दिखाया गया है।&nbsp;बालिका छात्राओं और बालक छात्रों ने एक से अधिक विषय नहीं लिए हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837311478.png\" alt=\"rId6\" width=\"498\" height=\"150\"> <br>गणित और रसायन विज्ञान की बालिका छात्राओं की संख्या और भौतिकी और जीव विज्ञान के बालक छात्रों की संख्या का अनुपात कितना है?</p>",
                    options_en: ["<p>98 : 71</p>", "<p>49 : 39</p>", 
                                "<p>61 : 98</p>", "<p>36 : 49</p>"],
                    options_hi: ["<p>98 : 71</p>", "<p>49 : 39</p>",
                                "<p>61 : 98</p>", "<p>36 : 49</p>"],
                    solution_en: "<p>1.(a)<br>No. of girls in Mathematics and chemistry = 840 &times; 40% + 220 &times; 70% = 336 + 154 = 490<br>No. of boys in Physics and Biology = 450 &times; 70% + 200 &times; 20% = 315 + 40 = 355<br>Required ratio = 490 : 355 = 98 : 71</p>",
                    solution_hi: "<p>1.(a)<br>गणित और रसायन विज्ञान में लड़कियों की संख्या = 840 &times; 40% + 220 &times;70% = 336 + 154 = 490<br>भौतिकी और जीवविज्ञान में लड़कों की संख्या = 450 &times; 70% + 200 &times; 20% = 315 + 40 = 355<br>आवश्यक अनुपात = 490 : 355 = 98 : 71</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The bar graph given below shows the sales of books (in thousands) from five branches of a publishing company during two consecutive years 2015 and 2016.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837311606.png\" alt=\"rId7\" width=\"449\" height=\"329\"> <br>What percentage (rounded off to 2 decimal places) of the average sales of branches P1, P3 and P5 in 2016 is the average sales of branches P1, P2, P3 and P4 in 2015?</p>",
                    question_hi: "<p>2. नीचे दिया गया दंड आलेख क्रमिक दो वर्षों 2015 और 2016 के दौरान एक प्रकाशन कंपनी की पाँच शाखाओं से पुस्तकों की बिक्री (हजार में) को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837311706.png\" alt=\"rId8\" width=\"453\" height=\"330\"> <br>2016 में शाखाओं P1, P3 और P5 की औसत बिक्री का कितना प्रतिशत (2 दशमलव स्थान तक सन्निकट) 2015 में शाखाओं P1, P2, P3 और P4 की औसत बिक्री है?</p>",
                    options_en: ["<p>76.53%</p>", "<p>80.12%</p>", 
                                "<p>81.21%</p>", "<p>88.15%</p>"],
                    options_hi: ["<p>76.53%</p>", "<p>80.12%</p>",
                                "<p>81.21%</p>", "<p>88.15%</p>"],
                    solution_en: "<p>2.(c)<br>Average sales of branches P1, P3 and P5 in 2016 = <math display=\"inline\"><mfrac><mrow><mn>120</mn><mo>+</mo><mn>110</mn><mo>+</mo><mn>96</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>326</mn><mn>3</mn></mfrac></math> <br>Average sales of branches P1, P2, P3 and P4 in 2015 = <math display=\"inline\"><mfrac><mrow><mn>85</mn><mo>+</mo><mn>78</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>90</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>353</mn><mn>4</mn></mfrac></math> <br>Required % = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>353</mn></mrow><mrow><mn>4</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>326</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>353</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>326</mn></mrow></mfrac></math>&times;100 &asymp; 81.21%</p>",
                    solution_hi: "<p>2.(c)<br>2016 में शाखाओं P1, P3 और P5 की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>120</mn><mo>+</mo><mn>110</mn><mo>+</mo><mn>96</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>326</mn><mn>3</mn></mfrac></math> <br>2015 में शाखाओं P1, P2, P3 और P4 की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>85</mn><mo>+</mo><mn>78</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>90</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>353</mn><mn>4</mn></mfrac></math> <br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>353</mn></mrow><mrow><mn>4</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mn>326</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>353</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>326</mn></mrow></mfrac></math>&times;100 &asymp; 81.21%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A student has 5 subjects in an examination. The distribution of hard work of 16 hours a day over 5 subjects and marks obtained 400 out of 500 are shown in the charts.<br>, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837311850.png\" alt=\"rId9\" width=\"282\" height=\"275\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837311968.png\" alt=\"rId10\" width=\"284\" height=\"278\"> <br>In which subject did the student get the highest outcome of the hard work (ratio of marks to hard work is highest)?</p>",
                    question_hi: "<p>3. एक छात्र की एक परीक्षा में 5 विषय हैं। 5 विषयों में प्रतिदिन 16 घंटे की कड़ी मेहनत का वितरण और 500 में से प्राप्त 400 अंक चार्ट में दिखाए गए हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837312087.png\" alt=\"rId11\" width=\"283\" height=\"281\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837312186.png\" alt=\"rId12\" width=\"280\" height=\"267\"> <br>विद्यार्थी को कड़ी मेहनत का सबसे अधिक परिणाम किस विषय में मिला (अंकों का कड़ी मेहनत से अनुपात सबसे अधिक किस विषय में है)?</p>",
                    options_en: ["<p>Math</p>", "<p>Science</p>", 
                                "<p>Economics</p>", "<p>English</p>"],
                    options_hi: ["<p>गणित</p>", "<p>विज्ञान</p>",
                                "<p>अर्थशास्त्र</p>", "<p>अंग्रेज़ी</p>"],
                    solution_en: "<p>3.(d)<br>Ratio of Marks to Hard work in asked subjects. <br>In Math = 21 : 40<br>In Science = 18 : 20 or 36 : 40<br>In Economics = 19 : 10 or 76 : 40<br>In English = 20 : 5 or 160 : 40<br>Clearly, we can see that students get the highest outcome of the hard work in English.</p>",
                    solution_hi: "<p>3.(d)<br>अंकों का कड़ी मेहनत से अनुपात,<br>गणित में = 21 : 40<br>विज्ञान में = 18 : 20 या 36 : 40<br>अर्थशास्त्र में = 19 : 10 या 76 : 40<br>अंग्रेजी में = 20 : 5 या 160 : 40<br>स्पष्ट रूप से, हम देख सकते हैं कि छात्र को कड़ी मेहनत का उच्चतम परिणाम अंग्रेजी में मिलता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The details of books of 6 subjects on the first floor of the college library are given in the following table.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837312305.png\" alt=\"rId13\"> <br>The ratio of Maths and Computers to Circuit Theory and Graph Theory is:</p>",
                    question_hi: "<p>4. महाविद्यालय के पुस्तकालय के प्रथम तल पर 6 विषयों की पुस्तकों का विवरण निम्न तालिका में दिया&nbsp;गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837312421.png\" alt=\"rId14\"> <br>गणित और कंप्यूटर का सर्किट थ्योरी और ग्राफ थ्योरी से अनुपात क्या है?</p>",
                    options_en: ["<p>3 : 1</p>", "<p>2 : 3</p>", 
                                "<p>1 : 3</p>", "<p>2 : 1</p>"],
                    options_hi: ["<p>3 : 1</p>", "<p>2 : 3</p>",
                                "<p>1 : 3</p>", "<p>2 : 1</p>"],
                    solution_en: "<p>4.(a)<br>Required ratio = 17,462+14308 : 9948+642 = 31,770 : 10,590 = 3 : 1</p>",
                    solution_hi: "<p>4.(a)<br>आवश्यक अनुपात = 17,462+14308 : 9948+642 = 31,770 : 10,590 = 3 : 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The given pie-diagram shows the expenditure incurred on the preparation of a book by a publisher, under various heads. Study the pie-diagram and answer the question that follows. Various Expenditures (in percentage) incurred in Publishing a Book<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837312556.png\" alt=\"rId15\" width=\"451\" height=\"279\"> <br>Which two expenditures together will form an angle of 108&deg; at the centre of the pie-diagram?</p>",
                    question_hi: "<p>5. दिया गया पाई-आरेख एक प्रकाशक द्वारा पुस्तक तैयार करने के लिए विभिन्न मदों के अंतर्गत किए गए व्यय को दर्शाता है। पाई-आरेख का अध्ययन कीजिए और नीचे दिए गए प्रश्नो के उत्तर दीजिए।<br>किसी पुस्तक के प्रकाशन में होने वाले विभिन्न व्यय (प्रतिशत में)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837312677.png\" alt=\"rId16\" width=\"454\" height=\"276\"> <br>पाई-आरेख के केंद्र पर कौन-से दो व्यय मिलकर 108&deg; का कोण बनाएंगे?</p>",
                    options_en: ["<p>A and E</p>", "<p>B and E</p>", 
                                "<p>A and D</p>", "<p>D and E</p>"],
                    options_hi: ["<p>A और E</p>", "<p>B और E</p>",
                                "<p>A और D</p>", "<p>D और E</p>"],
                    solution_en: "<p>5.(a)<br>360&deg; = 100%<br>108&deg; = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math>&times;108&deg; = 30%<br>Now, using the options, we have ;<br>(a) A+E = 20+10 = 30% <br>(b) B+E = 25+10 = 35%<br>(c) A+D = 20+15% = 35%<br>(d) D+E = 15+10 = 25%<br>Clearly, we can see that expenditure of A and E together will form an angle of 108&deg; at the centre of the pie-diagram.</p>",
                    solution_hi: "<p>5.(a)<br>360&deg; = 100%<br>108&deg; = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math>&times;108&deg; = 30%<br>अब, विकल्पों का उपयोग करते हुए, हमारे पास है;<br>(a) A+E = 20+10 = 30% <br>(b) B+E = 25+10 = 35%<br>(c) A+D = 20+15% = 35%<br>(d) D+E = 15+10 = 25%<br>स्पष्ट रूप से, हम देख सकते हैं कि A और E मिलकर पाई-आरेख के केंद्र पर 108&deg; का कोण बनाएंगे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Study the given pie-charts and answer the question that follows.<br>The pie-charts show the characteristics of foreign tourists visiting India during a given year.<br>,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837312800.png\" alt=\"rId17\" width=\"303\" height=\"277\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837312927.png\" alt=\"rId18\" width=\"303\" height=\"270\"> <br>If in a given year, 2,50,000 tourists visited India and the age wise distribution of data applies to all the countries, then what is the ratio of American tourists below 10 years to the other tourists above 50 years ?</p>",
                    question_hi: "<p>6. दिए गए पाई-चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। <br>पाई-चार्ट किसी दिए गए वर्ष के दौरान भारत में आने वाले विदेशी पर्यटकों की विशेषताओं को दर्शाता है।<br>,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837313075.png\" alt=\"rId19\" width=\"285\" height=\"287\">&nbsp; &nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837313190.png\" alt=\"rId20\" width=\"298\" height=\"268\"> <br>यदि किसी दिए गए वर्ष में 2,50,000 पर्यटक भारत आए और डेटा का आयु-वार वितरण सभी देशों पर लागू होता है, तो 10 वर्ष से कम आयु के अमेरिकी पर्यटकों का 50 वर्ष से ऊपर के अन्य पर्यटकों से अनुपात कितना है?</p>",
                    options_en: ["<p>1 : 5</p>", "<p>1 : 3</p>", 
                                "<p>3 : 1</p>", "<p>5 : 1</p>"],
                    options_hi: ["<p>1 : 5</p>", "<p>1 : 3</p>",
                                "<p>3 : 1</p>", "<p>5 : 1</p>"],
                    solution_en: "<p>6.(c)<br>Required ratio = 40%&times;<math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> : 20%&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>100</mn></mfrac></math> = 3 : 1</p>",
                    solution_hi: "<p>6.(c)<br>आवश्यक अनुपात = 40%&times;<math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> : 20%&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>100</mn></mfrac></math> = 3 : 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The following table gives the percentage of marks obtained by six students in five different subjects in an examination.<br>The numbers in the brackets give the maximum marks in each subject.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837313299.png\" alt=\"rId21\" width=\"591\" height=\"186\"> <br>What are the average marks obtained by all the six students in Hindi ?</p>",
                    question_hi: "<p>7. नीचे दी गई तालिका में एक परीक्षा के पांच अलग-अलग विषयों में छह छात्रों द्वारा प्राप्त अंकों का प्रतिशत दिखाया गया है।<br>कोष्ठक में दी गई संख्याएँ प्रत्येक विषय के अधिकतम अंक को दर्शाती हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837313450.png\" alt=\"rId22\" width=\"589\" height=\"202\"> <br>सभी छह छात्रों द्वारा हिंदी में प्राप्त औसत अंक क्या हैं?</p>",
                    options_en: ["<p>127</p>", "<p>106</p>", 
                                "<p>610</p>", "<p>172</p>"],
                    options_hi: ["<p>127</p>", "<p>106</p>",
                                "<p>610</p>", "<p>172</p>"],
                    solution_en: "<p>7.(b) <br>Average marks obtained by all the six students in Hindi = 120&times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>90</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>70</mn><mo>)</mo><mi>%</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> <br>= 120 &times; <math display=\"inline\"><mfrac><mrow><mn>530</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>636</mn><mn>6</mn></mfrac></math> = 106</p>",
                    solution_hi: "<p>7.(b) <br>सभी छह छात्रो द्वारा हिंदी मेे प्राप्त औसत अंक = 120 &times; <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>90</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>70</mn><mo>)</mo><mi>%</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> <br>= 120 &times; <math display=\"inline\"><mfrac><mrow><mn>530</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>636</mn><mn>6</mn></mfrac></math> = 106</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Study the given graph and answer the question that follows.<br>The graph shows the marks obtained by Riya and Rida in six subjects in the CBSE 12th exam. The maximum marks in Maths, Physics and Chemistry are 140, and that in English, IP and Biology are 180.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837313634.png\" alt=\"rId23\" width=\"468\" height=\"258\"> <br>What is the difference between the percentage of marks obtained by Riya and Rida in IP?</p>",
                    question_hi: "<p>8. निम्नलिखित ग्राफ का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>ग्राफ में सीबीएसई (CBSE) 12वीं की परीक्षा में छह विषयों में रिया और रिदा द्वारा प्राप्त अंकों को दर्शाया गया है। गणित, भौतिकी और रसायन विज्ञान में अधिकतम अंक 140 और अंग्रेजी, आईपी और जीवविज्ञान में अधिकतम अंक 180 हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837313837.png\" alt=\"rId24\" width=\"455\" height=\"250\"> <br>आईपी (IP) में रिया और रिदा द्वारा प्राप्त अंकों के प्रतिशत के बीच कितना अंतर है?</p>",
                    options_en: ["<p>19%</p>", "<p>11.11%</p>", 
                                "<p>20%</p>", "<p>31%</p>"],
                    options_hi: ["<p>19%</p>", "<p>11.11%</p>",
                                "<p>20%</p>", "<p>31%</p>"],
                    solution_en: "<p>8.(b)<br>Maximum marks in IP = 180<br>Marks obtained by Riya in IP = 150<br>Marks obtained by Rida in IP =130<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mo>-</mo><mn>130</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>9</mn></mfrac></math> = 11.11%</p>",
                    solution_hi: "<p>8.(b)<br>IP में अधिकतम अंक = 180<br>IP में रिया द्वारा प्राप्त अंक = 150<br>IP में रिदा द्वारा प्राप्त अंक =130<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mo>-</mo><mn>130</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>9</mn></mfrac></math> = 11.11%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The given table represents the percentage marks of three students in three subjects. Study the table and answer the question.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837313943.png\" alt=\"rId25\"> <br>The average marks (to the nearest integer) obtained by the students in History and Economics (respectively) are:</p>",
                    question_hi: "<p>9. दी गई तालिका तीन विषयों में तीन छात्रों के प्रतिशत अंकों को दर्शाती है। तालिका का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837314129.png\" alt=\"rId26\"> <br>इतिहास और अर्थशास्त्र (क्रमशः) में छात्रों द्वारा प्राप्त किए गए औसत अंक (निकटतम पूर्णांक तक) ज्ञात कीजिए।</p>",
                    options_en: ["<p>80 and 81</p>", "<p>78 and 80</p>", 
                                "<p>81 and 80</p>", "<p>80 and 78</p>"],
                    options_hi: ["<p>80 और 81</p>", "<p>78 और 80</p>",
                                "<p>81 और 80</p>", "<p>80 और 78</p>"],
                    solution_en: "<p>9.(a)<br>Average marks obtained by the students in History = <math display=\"inline\"><mfrac><mrow><mn>77</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>83</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>3</mn></mfrac></math> = 80<br>Average marks obtained by the students in Economics = <math display=\"inline\"><mfrac><mrow><mn>74</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>86</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>242</mn><mn>3</mn></mfrac></math> &asymp; 81<br>So, average marks obtained by the students in History and Economics are 80 and 81.</p>",
                    solution_hi: "<p>9.(a)<br>इतिहास मे छात्रों द्वारा प्राप्त औसत अंक = <math display=\"inline\"><mfrac><mrow><mn>77</mn><mo>+</mo><mn>80</mn><mo>+</mo><mn>83</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>3</mn></mfrac></math> = 80<br>अर्थशास्त्र में छात्रों द्वारा प्राप्त औसत अंक = <math display=\"inline\"><mfrac><mrow><mn>74</mn><mo>+</mo><mn>82</mn><mo>+</mo><mn>86</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>242</mn><mn>3</mn></mfrac></math> &asymp; 81<br>इसलिए, इतिहास और अर्थशास्त्र में छात्रों द्वारा प्राप्त औसत अंक 80 और 81 हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The following table shows the loan disbursed by four banks (in Crores) over four years:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837314231.png\" alt=\"rId27\"> <br>In which year total disbursement of loans of banks A and B is exactly equal to the total disbursement of loans of banks C and D?</p>",
                    question_hi: "<p>10. निम्न तालिका चार वर्षों में चार बैंकों द्वारा संवितरित ऋण (करोड़ में) दर्शाती है:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837314330.png\" alt=\"rId28\"> <br>किस वर्ष में बैंक A और B के ऋणों का कुल संवितरण, बैंक C और D के ऋणों के कुल संवितरण के बिल्कुल बराबर है?</p>",
                    options_en: ["<p>2019</p>", "<p>2017</p>", 
                                "<p>2016</p>", "<p>2018</p>"],
                    options_hi: ["<p>2019</p>", "<p>2017</p>",
                                "<p>2016</p>", "<p>2018</p>"],
                    solution_en: "<p>10.(a)<br><strong>In 2019,</strong> <br>Total disbursement of loans of banks A and B = 22+18 = 40<br>Total disbursement of loans of banks C and D = 20+20 = 40<br><strong>In 2017,</strong> <br>Total disbursement of loans of banks A and B = 15+10 = 25<br>Total disbursement of loans of banks C and D = 25+10 =35<br><strong>In 2016,</strong> <br>Total disbursement of loans of banks A and B = 10+5 = 15<br>Total disbursement of loans of banks C and D = 20+5 = 25<br><strong>In 2018,</strong> <br>Total disbursement of loans of banks A and B = 20+15 = 35<br>Total disbursement of loans of banks C and D = 15+25 = 40<br>Clearly, we can see that total disbursement of loans of banks A and B is exactly equal to the total disbursement of loans of banks C and D in 2019.</p>",
                    solution_hi: "<p>10.(a)<br><strong>2019 में,</strong> <br>बैंक A और B का कुल ऋण वितरण = 22+18 = 40<br>बैंक C और D का कुल ऋण वितरण = 20+20 = 40<br><strong>2017 में,</strong> <br>बैंक A और B का कुल ऋण वितरण= 15+10 = 25<br>बैंक C और D का कुल ऋण वितरण = 25+10 =35<br><strong>2016 में,</strong> <br>बैंक A और B का कुल ऋण वितरण = 10+5 = 15<br>बैंक C और D का कुल ऋण वितरण = 20+5 = 25<br><strong>2018 में,</strong> <br>बैंक A और B का कुल ऋण वितरण = 20+15 = 35<br>बैंक C और D का कुल ऋण वितरण = 15+25 = 40<br>स्पष्ट रूप से, हम देख सकते हैं कि बैंक ए और बी के ऋणों का कुल वितरण 2019 में बैंक सी और डी के ऋणों के कुल वितरण के बराबर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The following line graph shows the units manufactured by a company in some months. In which month did the company report the highest growth (in %) in production as compared with the respective previous month&rsquo;s production?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837314454.png\" alt=\"rId29\" width=\"475\" height=\"337\"></p>",
                    question_hi: "<p>11. निम्नलिखित लाइन ग्राफ एक कंपनी द्वारा कुछ माह में निर्मित यूनिटों को दर्शाता है। कंपनी ने किस माह में, संगत पिछले माह के उत्पादन की तुलना में उत्पादन में उच्चतम वृद्धि (% में) दर्ज की?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837314559.png\" alt=\"rId30\" width=\"470\" height=\"330\"></p>",
                    options_en: ["<p>July</p>", "<p>October</p>", 
                                "<p>June</p>", "<p>September</p>"],
                    options_hi: ["<p>जुलाई</p>", "<p>अक्टूबर</p>",
                                "<p>जून</p>", "<p>सितंबर</p>"],
                    solution_en: "<p>11.(c)<br>% growth in July = <math display=\"inline\"><mfrac><mrow><mn>380</mn><mo>-</mo><mn>350</mn></mrow><mrow><mn>350</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>350</mn></mfrac></math>&times;100 &asymp; 9% <br>% growth in October = <math display=\"inline\"><mfrac><mrow><mn>650</mn><mo>-</mo><mn>525</mn></mrow><mrow><mn>525</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>525</mn></mfrac></math>&times;100 &asymp; 24% <br>% growth in June= <math display=\"inline\"><mfrac><mrow><mn>350</mn><mo>-</mo><mn>252</mn></mrow><mrow><mn>252</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>98</mn><mn>252</mn></mfrac></math>&times;100 &asymp; 39%<br>% growth in September = <math display=\"inline\"><mfrac><mrow><mn>525</mn><mo>-</mo><mn>425</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>425</mn></mfrac></math>&times;100 &asymp; 24% <br>Clearly, we can see that the highest % growth in production of company in june.</p>",
                    solution_hi: "<p>11.(c)<br>जुलाई में %वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>380</mn><mo>-</mo><mn>350</mn></mrow><mrow><mn>350</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>30</mn><mn>350</mn></mfrac></math>&times;100 &asymp; 9% <br>अक्टूबर में % वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>650</mn><mo>-</mo><mn>525</mn></mrow><mrow><mn>525</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>525</mn></mfrac></math>&times;100 &asymp; 24% <br>जून में % वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>350</mn><mo>-</mo><mn>252</mn></mrow><mrow><mn>252</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>98</mn><mn>252</mn></mfrac></math>&times;100 &asymp; 39%<br>सितंबर में % वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>525</mn><mo>-</mo><mn>425</mn></mrow><mrow><mn>425</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>425</mn></mfrac></math>&times;100 &asymp; 24% <br>स्पष्ट रूप से, हम देख सकते हैं कि जून में कंपनी के उत्पादन में सबसे अधिक% वृद्धि हुई है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Study the given pie-chart and answer the question that follows.<br>The pie-chart represents the total number of valid votes obtained by four students who contested for school leadership. The total number of valid votes polled was 720.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837314671.png\" alt=\"rId31\" width=\"301\" height=\"297\"> <br>By how many votes did the winner defeat the nearest contestant?</p>",
                    question_hi: "<p>12. निम्नलिखित पाई-चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>यह पाई-चार्ट स्कूल लीडरशिप के लिए चुनाव लड़ने वाले चार वि&zwnj;द्यार्थियों द्वारा प्राप्त वैध मतों की कुल संख्या का निरूपण करता है। डाले गए वैध मतों की कुल संख्या 720 थी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837314779.png\" alt=\"rId32\" width=\"296\" height=\"272\"> <br>विजेता उम्मीदवार ने अपने निकटतम विरोधी उम्मीदवार को कितने मतों से हराया?</p>",
                    options_en: ["<p>30</p>", "<p>40</p>", 
                                "<p>10</p>", "<p>20</p>"],
                    options_hi: ["<p>30</p>", "<p>40</p>",
                                "<p>10</p>", "<p>20</p>"],
                    solution_en: "<p>12.(b)<br>Required no. of votes = 720&times;<math display=\"inline\"><mfrac><mrow><mn>120</mn><mo>&#176;</mo><mo>-</mo><mn>100</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math>= 720&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> = 40</p>",
                    solution_hi: "<p>12.(b)<br>आवश्यक वोटों की संख्या = 720&times;<math display=\"inline\"><mfrac><mrow><mn>120</mn><mo>&#176;</mo><mo>-</mo><mn>100</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math>= 720&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> = 40</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Study the given graph and answer the question that follows.<br>The graph shows the profit percentage earned by two companies A and B over a period of six years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837315005.png\" alt=\"rId33\" width=\"503\" height=\"213\"> <br>What is the percentage increase in profit percentage of company B in the year 2014 from the previous year?</p>",
                    question_hi: "<p>13. निम्नलिखित ग्राफ का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>ग्राफ में छः वर्षों की अवधि के दौरान दो कंपनियों - A और B द्वारा अर्जित लाभ के प्रतिशत को दर्शाया गया&nbsp;है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837315210.png\" alt=\"rId34\" width=\"501\" height=\"227\"> <br>वर्ष 2014 में कंपनी B के लाभ प्रतिशत में पिछले वर्ष की तुलना में कितने प्रतिशत की वृ&zwnj;द्धि हुई है?</p>",
                    options_en: ["<p>5.75%</p>", "<p>6.55%</p>", 
                                "<p>7.5%</p>", "<p>13.63%</p>"],
                    options_hi: ["<p>5.75%</p>", "<p>6.55%</p>",
                                "<p>7.5%</p>", "<p>13.63%</p>"],
                    solution_en: "<p>13.(d) <br>Profit of company B in the year 2013 = 44<br>Profit of company B in the year 2014 = 50<br>% increase in profit of company B = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>44</mn></mrow><mrow><mn>44</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>44</mn></mfrac></math> = 13.63%</p>",
                    solution_hi: "<p>13.(d) <br>वर्ष 2013 में कंपनी B का लाभ = 44<br>वर्ष 2024 में कंपनी B का लाभ= 50<br>कंपनी B के लाभ में % वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>44</mn></mrow><mrow><mn>44</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>44</mn></mfrac></math> = 13.63%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Study the given table and answer the question that follows.<br>The table shows the results of half-yearly and annual examinations of three&nbsp;sections A, B, C of class X students in a school.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837315361.png\" alt=\"rId35\" width=\"545\" height=\"204\"> <br>Find the pass percentage of section A in at least one of the two examinations.</p>",
                    question_hi: "<p>14. निम्नलिखित तालिका का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>तालिका में एक स्कूल में X कक्षा के विद्यार्थियों के तीन वर्गों A, B, C की अर्धवार्षिक और वार्षिक परीक्षाओं के परिणाम को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837315523.png\" alt=\"rId36\" width=\"461\" height=\"217\"> <br>वर्ग A में दोनों परीक्षाओं में से न्यूनतम एक परीक्षा में उत्तीर्ण होने वाले विद्यार्थियों का प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>75%</p>", "<p>70%</p>", 
                                "<p>65%</p>", "<p>80%</p>"],
                    options_hi: ["<p>75%</p>", "<p>70%</p>",
                                "<p>65%</p>", "<p>80%</p>"],
                    solution_en: "<p>14.(b) <br>Total no. of students in section A = 39+16+10+65 = 130<br>No. of students passed in section A in at least one of the two examinations = 130 - 39 = 91<br>required% = <math display=\"inline\"><mfrac><mrow><mn>91</mn></mrow><mrow><mn>130</mn></mrow></mfrac></math>&times;100 = 70%</p>",
                    solution_hi: "<p>14.(b) <br>अनुभाग A मे छात्रो की संख्या = 39+16+10+65 = 130<br>दोनो परीक्षाओं में से कम से कम एक में अनुभाग A मे उत्तीर्ण छात्र = 130 - 39 = 91<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>91</mn></mrow><mrow><mn>130</mn></mrow></mfrac></math>&times;100 = 70%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The following table shows the number of trees axed in 2016-2020. Study the given information carefully and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837315659.png\" alt=\"rId37\"> <br>What was the percentage increase in the number of banyan trees axed in 2020 compared to the number of banyan trees axed in 2016?</p>",
                    question_hi: "<p>15. निम्नलिखित तालिका 2016 - 2020 में काटे गए पेड़ों की संख्या को दर्शाती है। दी गई जानकारी का ध्यानपूर्वक अध्ययन कीजिए और आगे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736837315765.png\" alt=\"rId38\"> <br>2016 में काटे गए बरगद के पेड़ों की संख्या की तुलना में 2020 में काटे गए बरगद के पेड़ों की संख्या में कितने प्रतिशत की वृद्धि हुई है?</p>",
                    options_en: ["<p>90%</p>", "<p>9%</p>", 
                                "<p>47.4%</p>", "<p>60%</p>"],
                    options_hi: ["<p>90%</p>", "<p>9%</p>",
                                "<p>47.4%</p>", "<p>60%</p>"],
                    solution_en: "<p>15.(a)<br>Required % increase in the number of banyan trees = <math display=\"inline\"><mfrac><mrow><mn>38</mn><mo>,</mo><mn>000</mn><mo>-</mo><mn>20</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>20</mn><mo>,</mo><mn>000</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>20</mn></mfrac></math> &times; 100 = 90%</p>",
                    solution_hi: "<p>15.(a)<br>बरगद के पेंड़ो की संख्या में आवश्यक % वृद्धी = <math display=\"inline\"><mfrac><mrow><mn>38</mn><mo>,</mo><mn>000</mn><mo>-</mo><mn>20</mn><mo>,</mo><mn>000</mn></mrow><mrow><mn>20</mn><mo>,</mo><mn>000</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>20</mn></mfrac></math> &times; 100 = 90%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>