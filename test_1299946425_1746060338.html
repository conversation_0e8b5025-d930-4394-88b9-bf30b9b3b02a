<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option figure in which the given figure is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085087.png\" alt=\"rId4\" width=\"101\" height=\"117\"></p>",
                    question_hi: "<p>1. उस विकल्प आकृति का चयन कीजिए, जिसमें दी गई आकृति उसके भाग के रूप में सन्&zwj;निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085087.png\" alt=\"rId4\" width=\"100\" height=\"116\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085195.png\" alt=\"rId5\" width=\"95\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085309.png\" alt=\"rId6\" width=\"96\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085421.png\" alt=\"rId7\" width=\"96\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085619.png\" alt=\"rId8\" width=\"94\" height=\"94\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085195.png\" alt=\"rId5\" width=\"95\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085309.png\" alt=\"rId6\" width=\"94\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085421.png\" alt=\"rId7\" width=\"94\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085619.png\" alt=\"rId8\" width=\"95\" height=\"95\"></p>"
                    ],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085812.png\" alt=\"rId9\" width=\"105\" height=\"104\"></p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085812.png\" alt=\"rId9\" width=\"105\" height=\"104\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, &lsquo;FRAMED&rsquo; is coded as &lsquo;497621&rsquo; and &lsquo;DREAMT&rsquo; is coded as &lsquo;769542&rsquo;. What is the code for &lsquo;T&rsquo; in that language?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में, &lsquo;FRAMED&rsquo; को &lsquo;497621&rsquo; के रूप में कूटबद्ध किया जाता है और &lsquo;DREAMT&rsquo; को &lsquo;769542&rsquo; के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'T\' के लिए क्या कूट होगा?</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>2.(a)<br>F R A M E D &rarr;&nbsp;4 9 7 6 2 1<br>D R E A M T &rarr; 7 6 9 5 4 2<br>From above code for &lsquo;T&rsquo; is 5</p>",
                    solution_hi: "<p>2.(a)<br>F R A M E D &rarr; 4 9 7 6 2 1<br>D R E A M T &rarr; 7 6 9 5 4 2<br>उपरोक्त कोडिंग से \'T\' के लिए कोड 5 है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Which of the following terms will replace the question mark (?) in the given series?<br>ADO, DGR, GJU , ?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन-सा पद दी गई शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित करेगा?<br>ADO, DGR, GJU , ?</p>",
                    options_en: [
                        "<p>LON</p>",
                        "<p>JCB</p>",
                        "<p>JMX</p>",
                        "<p>RCB</p>"
                    ],
                    options_hi: [
                        "<p>LON</p>",
                        "<p>JCB</p>",
                        "<p>JMX</p>",
                        "<p>RCB</p>"
                    ],
                    solution_en: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085934.png\" alt=\"rId10\" width=\"237\" height=\"89\"></p>",
                    solution_hi: "<p>3.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756085934.png\" alt=\"rId10\" width=\"237\" height=\"89\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086067.png\" alt=\"rId11\" width=\"239\" height=\"80\"></p>",
                    question_hi: "<p>4. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086067.png\" alt=\"rId11\" width=\"239\" height=\"80\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086160.png\" alt=\"rId12\" width=\"101\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086256.png\" alt=\"rId13\" width=\"100\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086398.png\" alt=\"rId14\" width=\"101\" height=\"103\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086493.png\" alt=\"rId15\" width=\"102\" height=\"108\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086160.png\" alt=\"rId12\" width=\"100\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086256.png\" alt=\"rId13\" width=\"101\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086398.png\" alt=\"rId14\" width=\"101\" height=\"103\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086493.png\" alt=\"rId15\" width=\"101\" height=\"107\"></p>"
                    ],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086493.png\" alt=\"rId15\" width=\"110\" height=\"117\"></p>",
                    solution_hi: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086493.png\" alt=\"rId15\" width=\"110\" height=\"117\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Which of the following numbers will replace the question mark (?) in the given series?<br>78, 86, 258, 322, ?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>78, 86, 258, 322, ?</p>",
                    options_en: [
                        "<p>1288</p>",
                        "<p>1360</p>",
                        "<p>1420</p>",
                        "<p>1610</p>"
                    ],
                    options_hi: [
                        "<p>1288</p>",
                        "<p>1360</p>",
                        "<p>1420</p>",
                        "<p>1610</p>"
                    ],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086611.png\" alt=\"rId16\" width=\"234\" height=\"89\"></p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756086611.png\" alt=\"rId16\" width=\"234\" height=\"89\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In this question, three statements are given, followed by three conclusions numbered<br>I, II and III. Assuming the statements to be true, even if they seem to be at variance<br>with commonly known facts, decide which of the conclusion(s) logically follows/follow<br>from the statements.<br><strong>Statements :</strong><br>All chairs are sofas.<br>Some chairs are beds.<br>No sofa is a table.<br><strong>Conclusions :</strong><br>I. Some tables are beds.<br>II. No table is a chair.<br>III. Some sofas are beds.</p>",
                    question_hi: "<p>6. इस प्रश्न में तीन कथन दिए गए हैं, जिनके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए भले ही वे सामान्य रूप से ज्ञात तथ्यों से अलग प्रतीत होते हों, निर्णय करें कि कौन-सा/कौन-से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>सभी कुर्सियाँ, सोफे हैं।<br>कुछ कुर्सियाँ, पलंग हैं।<br>कोई सोफा, मेज नहीं है।<br><strong>निष्कर्ष :</strong><br>I. कुछ मेजें, पलंग हैं।<br>II. कोई मेज, कुर्सी नहीं है।<br>III. कुछ सोफे, पलंग हैं।</p>",
                    options_en: [
                        "<p>Both conclusions I and III follow.</p>",
                        "<p>Both conclusions I and II follow.</p>",
                        "<p>Both conclusions II and III follow.</p>",
                        "<p>All conclusions I, II and III follow.</p>"
                    ],
                    options_hi: [
                        "<p>निष्कर्ष I और III, दोनों अनुसरण करते हैं।</p>",
                        "<p>निष्कर्ष I और II, दोनों अनुसरण करते हैं।</p>",
                        "<p>निष्कर्ष II और III, दोनों अनुसरण करते हैं।</p>",
                        "<p>I, II और III, सभी निष्कर्ष अनुसरण करते हैं।</p>"
                    ],
                    solution_en: "<p>6.(c)<br><strong id=\"docs-internal-guid-89c3d8b7-7fff-c654-5c43-1e1a8c175e47\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfJ3B8zAmizkWjemNnQ0y5NfRs6rrPUeHiUjkA3sPk2pyZ-dd7Eu1YIaa3-PuwKwYPgF9QxuM9bBxd1hOe-xLlDke_4LS99Sj79O33MwwKO8pqf9MjfvUTCOKzYhe7MoLrm6R55Sg?key=Y8ZG8eARukJZgVG_2TaXNqYk\" width=\"309\" height=\"82\"></strong><br>Both conclusion II and III follow.</p>",
                    solution_hi: "<p>6.(c) <br><strong id=\"docs-internal-guid-7fdc702d-7fff-e0f4-962b-15e96cff9699\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXds0uyYtAtLnjxOr3Tha1JOGyIbQ6uGeTUA9IyDCCBFzUDkBrQd8EHsR3NRfTiEYOdiysLvgymo5iqs-bazwgKA9PF9WntMxnmRPFngDOwrcIgoYA0HL5lgWSSrL51MpSAs-zxP?key=Y8ZG8eARukJZgVG_2TaXNqYk\" width=\"330\" height=\"92\"></strong><br>दोनों निष्कर्ष II और III अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Three of the following four letter-cluster pairs are like in a certain way and thus form a group. Which is the letter-cluster pair that does NOT belong to that group?<br>(<strong>Note :</strong> The odd one out is not based on the number of consonants/vowels or their positions in the letter-cluster.)</p>",
                    question_hi: "<p>7. निम्नलिखित चार अक्षर-समूह युग्मों में से तीन अक्षर-समूह युग्म एक निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। इनमें से वह अक्षर-समूह युग्म कौन-सा है जो इस समूह से संबंधित नहीं है?<br>(<strong>ध्यान दें :</strong> असंगत अक्षर-समूह युग्म, अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>EJ - OT</p>",
                        "<p>PF - OI</p>",
                        "<p>KP - UZ</p>",
                        "<p>CH - MR</p>"
                    ],
                    options_hi: [
                        "<p>EJ - OT</p>",
                        "<p>PF - OI</p>",
                        "<p>KP - UZ</p>",
                        "<p>CH - MR</p>"
                    ],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087276.png\" alt=\"rId19\" width=\"104\" height=\"89\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087420.png\" alt=\"rId20\" width=\"107\" height=\"89\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087578.png\" alt=\"rId21\" width=\"102\" height=\"84\"><br>But, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087713.png\" alt=\"rId22\" width=\"99\" height=\"90\"></p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087276.png\" alt=\"rId19\" width=\"104\" height=\"89\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087420.png\" alt=\"rId20\" width=\"107\" height=\"89\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087578.png\" alt=\"rId21\" width=\"102\" height=\"84\"><br>लेकिन, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087713.png\" alt=\"rId22\" width=\"99\" height=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "8. ‘T + W’ means ‘T is the father of W’<br />‘T – W’ means ‘T is the wife of W’<br />‘T × W’ means ‘T is the brother of W’<br />‘T ÷ W’ means ‘T is the daughter of W’.<br />What does ‘S + T ÷ U’ mean ?",
                    question_hi: "8. T + W\' का अर्थ है \'T, W का पिता है\'<br />\'T - W\' का अर्थ है \'T, W की पत्नी है\'<br />\'T × W\' का अर्थ है \'T, W का भाई है\'<br />\'T ÷ W\' का अर्थ है \'T, W की पुत्री है\'।<br />‘S + T ÷ U’ का क्या अर्थ है ?",
                    options_en: [
                        " S is the brother of U",
                        " S is the husband of U ",
                        " S is the son of U ",
                        " S is the uncle of U"
                    ],
                    options_hi: [
                        " S, U का भाई है  ",
                        " S, U का पति है",
                        " S, U का पुत्र है ",
                        " S, U का अंकल है"
                    ],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087831.png\" alt=\"rId23\" width=\"137\" height=\"132\"><br>S is the husband of U.</p>",
                    solution_hi: "<p>8.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756087831.png\" alt=\"rId23\" width=\"137\" height=\"132\"><br>S, U का पति है.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the set in which the numbers are related in the same way as are the numbers of the following sets. (<strong>NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>(16, 7, 112) <br>(8, 12, 96)</p>",
                    question_hi: "<p>9. उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्&zwj;चय की संख्याएँ संबंधित हैं। <br>(<strong>नोट:</strong> संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।) <br>(16, 7, 112) <br>(8, 12, 96)</p>",
                    options_en: [
                        " (12, 5, 65)  ",
                        " (14, 9, 128) ",
                        " (13, 7, 96)  ",
                        " (15, 8, 120)"
                    ],
                    options_hi: [
                        " (12, 5, 65)  ",
                        " (14, 9, 128) ",
                        " (13, 7, 96)  ",
                        " (15, 8, 120)"
                    ],
                    solution_en: "<p>9.(d)<br><strong>Logic:-</strong> 1<sup>st</sup>no.&nbsp;&times; 2<sup>nd</sup>no. = 3<sup>rd</sup> no.<br>(16, 7, 112) :- 16 &times; 7 = 112<br>(8, 12, 96) :- 8 &times; 12 = 96<br>Similarly, <br>(15, 8, 120) :- 15 &times; 8 = 120</p>",
                    solution_hi: "<p>9.(d)<br><strong>तर्क:- </strong>पहली संख्या &times; दूसरी संख्या = तीसरी संख्या <br>(16, 7, 112) :- 16 &times; 7 = 112<br>(8, 12, 96) :- 8 &times; 12 = 96<br>इसी प्रकार, <br>(15, 8, 120) :- 15 &times; 8 = 120</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. The diagram given below has four different shapes, depicting different farmers of a small village, with different assets. The circle represents the farmers who own land of more than 100 yards, the pentagon represents farmers who own cows, the rhombus represents farmers who own goats, and the triangle represents farmers who own tractors.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756088105.png\" alt=\"rId24\" width=\"203\" height=\"142\"> <br>How many farmers are there who have all four assets?</p>",
                    question_hi: "<p>10. नीचे दिए गए आरेख में चार अलग-अलग आकृतियाँ हैं, जो एक छोटे से गाँव के विभिन्न किसानों को अलग-अलग संपत्ति के साथ दर्शाती हैं। वृत्त उन किसानों का प्रतिनिधित्व करता है, जिनके पास 100 गज से अधिक की जमीन है, पंचभुज उन किसानों का प्रतिनिधित्व करता है, जिनके पास गाएं हैं, समचतुर्भुज उन किसानों का प्रतिनिधित्व करता है, जिनके पास बकरियां हैं और त्रिभुज उन किसानों का प्रतिनिधित्व करता है, जिनके पास ट्रैक्टर हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756088105.png\" alt=\"rId24\" width=\"203\" height=\"142\"><br>ऐसे कितने किसान हैं, जिनके पास चारों संपत्तियां हैं?</p>",
                    options_en: [
                        "<p>14</p>",
                        "<p>30</p>",
                        "<p>16</p>",
                        "<p>21</p>"
                    ],
                    options_hi: [
                        "<p>14</p>",
                        "<p>30</p>",
                        "<p>16</p>",
                        "<p>21</p>"
                    ],
                    solution_en: "<p>10.(c) Farmers who have all four assets are16.</p>",
                    solution_hi: "<p>10.(c) ऐसे किसान जिनके पास सभी चार संपत्तियाँ 16 हैं</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. If &lsquo;I&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;J&rsquo; stands for &lsquo;+&rsquo;, &lsquo;K&rsquo; stands for &lsquo;&divide;&rsquo; and &lsquo;L&rsquo; stands for &lsquo;&minus;&rsquo;, what will come in place of the question mark (?) in the following equation? <br>(40 J 32 K 4) I 5 L 155 J 7 I 9 = ?</p>",
                    question_hi: "<p>11. यदि &lsquo;I&rsquo; का अर्थ &lsquo;&times;&rsquo; है, &lsquo;J&rsquo; का अर्थ &lsquo;+&rsquo; है, &lsquo;K&rsquo; का अर्थ &lsquo;&divide;&rsquo; है और L का अर्थ &minus; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>(40 J 32 K 4) I 5 L 155 J 7 I 9 = ?</p>",
                    options_en: [
                        "<p>156</p>",
                        "<p>148</p>",
                        "<p>126</p>",
                        "<p>134</p>"
                    ],
                    options_hi: [
                        "<p>156</p>",
                        "<p>148</p>",
                        "<p>126</p>",
                        "<p>134</p>"
                    ],
                    solution_en: "<p>11.(b) <strong>Given :-</strong> (40 J 32 K 4) I 5 L 155 J 7 I 9<br>As per given instruction after interchanging the letter with sign, we get<br>(40 + 32 &divide;&nbsp;4) &times; 5 - 155 + 7 &times; 9<br>(40 + 8) &times; 5 - 155 + 63<br>(48) &times; 5 - 155 + 63<br>240 - 155 + 63<br>303 - 155 = 148</p>",
                    solution_hi: "<p>11.(b) <strong>दिया गया :-</strong> (40 J 32 K 4) I 5 L 155 J 7 I 9<br>दिए गए निर्देश के अनुसार अक्षर को चिह्न से बदलने पर हमें प्राप्त होता है<br>(40 + 32 &divide; 4) &times; 5 - 155 + 7 &times; 9<br>(40 + 8)&times; 5 - 155 + 63<br>(48)&times; 5 - 155 + 63<br>240 - 155 + 63<br>303 - 155 = 148</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option that represents the letters that, when sequentially placed in the blanks below, will complete the letter-series.<br>kr_om_kry_mt_ _yom_kryo_t</p>",
                    question_hi: "<p>12. उस विकल्प का चयन कीजिए, जो उन अक्षरों का निरूपण करता हो, जिन्हें नीचे दिए गए रिक्त स्थानों में&nbsp;क्रमिक रूप से रखे जाने पर यह अक्षर-शृंखला पूरी होगी।<br>kr_om_kry_mt_ _yom_kryo_t</p>",
                    options_en: [
                        "<p>t k y r o m k</p>",
                        "<p>y t o k r t m</p>",
                        "<p>y t k r o t m</p>",
                        "<p>t y k o r m t</p>"
                    ],
                    options_hi: [
                        "<p>t k y r o m k</p>",
                        "<p>y t o k r t m</p>",
                        "<p>y t k r o t m</p>",
                        "<p>t y k o r m t</p>"
                    ],
                    solution_en: "<p>12.(b) <br>kr<span style=\"text-decoration: underline;\"><strong>y</strong></span>om<span style=\"text-decoration: underline;\"><strong>t</strong></span> / kry<strong><span style=\"text-decoration: underline;\">o</span></strong> mt / <strong><span style=\"text-decoration: underline;\">k r</span></strong>yomt / kryo<span style=\"text-decoration: underline;\"><strong>m</strong></span> t</p>",
                    solution_hi: "<p>12.(b)<br>kr<span style=\"text-decoration: underline;\"><strong>y</strong></span>om<span style=\"text-decoration: underline;\"><strong>t</strong></span> / kry<strong><span style=\"text-decoration: underline;\">o</span></strong> mt / <strong><span style=\"text-decoration: underline;\">k r</span></strong>yomt / kryo<span style=\"text-decoration: underline;\"><strong>m</strong></span> t</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Two different positions of the same dice are given below. Which is the number on the face opposite the face containing 5?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756088439.png\" alt=\"rId25\" width=\"159\" height=\"91\"></p>",
                    question_hi: "<p>13. नीचे एक ही पासे की दो अलग-अलग स्थितियाँ दी गई हैं। संख्या 5 वाले फलक के विपरीत फलक पर कौन-सी संख्या है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756088439.png\" alt=\"rId25\" width=\"159\" height=\"91\"></p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>6</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>13.(a) <br>From the two dice the opposite Faces are<br>6 &harr; 3 , 1 &harr; 5 , 2 &harr; 4</p>",
                    solution_hi: "<p>13.(a) <br>दोनों पासों के विपरीत फलक हैं<br>6 &harr; 3 , 1 &harr; 5 , 2 &harr; 4</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Six people&mdash;A, B, C, D, E, and F&mdash;are sitting around a circular table, facing the centre (but not necessarily in the same order). A is an immediate neighbour of both E and C. B is an immediate neighbour of both D and C. F is to the immediate right of D.&nbsp;Who among the following is an immediate neighbour of both F and A?</p>",
                    question_hi: "<p>14. छः व्यक्ति - A, B, C, D, E और F - एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। E और C दोनों का निकटतम पड़ोसी A है। D और C दोनों का निकटतम पड़ोसी B है। F, D के ठीक दाएँ है। निम्नलिखित में से कौन F और A दोनों का निकटतम पड़ोसी है?</p>",
                    options_en: [
                        "<p>C</p>",
                        "<p>B</p>",
                        "<p>E</p>",
                        "<p>D</p>"
                    ],
                    options_hi: [
                        "<p>C</p>",
                        "<p>B</p>",
                        "<p>E</p>",
                        "<p>D</p>"
                    ],
                    solution_en: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756088702.png\" alt=\"rId26\" width=\"164\" height=\"149\"></p>",
                    solution_hi: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756088702.png\" alt=\"rId26\" width=\"164\" height=\"149\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Manish starts from Point A and drives 12 km towards East. He then takes a left turn, drives 5 km, turns left and drives 30 km. He then takes a left turn and drives 20 km. He takes a final left turn, drives 18 km and stops at Point Q. How far (shortest distance) and towards which direction should he now drive to reach Point A again? (All turns are 90&deg; turns only.)</p>",
                    question_hi: "<p>15. मनीष बिंदु A से गाड़ी चलाना शुरू करता है और 12 km पूर्व की ओर गाड़ी चलाता है। फिर वह बाएँ मुड़ता है, 5 km गाड़ी चलाता है, बाएँ मुड़ता है और 30 km गाड़ी चलाता है। फिर वह बाएँ मुड़ता है और 20 km गाड़ी चलाता है। वह अंतिम बार बाएँ मुड़ता है, 18 km गाड़ी चलाता है और बिंदु Q पर रुकता है। बिंदु A पर फिर से पहुँचने के लिए उसे कितनी दूर (न्यूनतम दूरी) और किस दिशा में गाड़ी चलानी चाहिए? (सभी मोड़ केवल 90&deg; मोड़ वाले हैं।)</p>",
                    options_en: [
                        "<p>15 km towards North</p>",
                        "<p>10 km towards North</p>",
                        "<p>15 km towards South</p>",
                        "<p>10 km towards South</p>"
                    ],
                    options_hi: [
                        "<p>उत्तर की ओर 15 km</p>",
                        "<p>उत्तर की ओर 10 km</p>",
                        "<p>दक्षिण की ओर 15 km</p>",
                        "<p>दक्षिण की ओर 10 km</p>"
                    ],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756088938.png\" alt=\"rId27\" width=\"211\" height=\"138\"><br>He should drive 15 km towards the north direction to reach point A.</p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756088938.png\" alt=\"rId27\" width=\"211\" height=\"138\"><br>बिंदु A तक पहुंचने के लिए उसे उत्तर दिशा की ओर 15 km गाड़ी चलानी चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. What will come in the place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>13 &divide; 3 + 2 &times; 2 - 11 = ?</p>",
                    question_hi: "<p>16. यदि &lsquo;+&rsquo; और &rsquo;-&rsquo; को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में \'?\' के स्थान पर क्या आएगा?<br>13 &divide; 3 + 2 &times; 2 - 11 = ?</p>",
                    options_en: [
                        "<p>59</p>",
                        "<p>39</p>",
                        "<p>29</p>",
                        "<p>49</p>"
                    ],
                    options_hi: [
                        "<p>59</p>",
                        "<p>39</p>",
                        "<p>29</p>",
                        "<p>49</p>"
                    ],
                    solution_en: "<p>16.(d) <strong>Given :- </strong>13 &divide;&nbsp;3 + 2 &times; 2 - 11<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get<br>13 &times; 3 - 2 &divide; 2 + 11<br>39 - 1 + 11 = 49</p>",
                    solution_hi: "<p>16.(d) <strong>दिया गया :- </strong>13 &divide; 3 + 2 &times; 2 - 11<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>13 &times; 3 - 2 &divide; 2 + 11<br>39 - 1 + 11 = 49</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair.<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)bxs</p>",
                    question_hi: "<p>17. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए।<br>(<strong>नोट :</strong> संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: [
                        "<p>12 : 39</p>",
                        "<p>15 : 48</p>",
                        "<p>11 : 35</p>",
                        "<p>13 : 42</p>"
                    ],
                    options_hi: [
                        "<p>12 : 39</p>",
                        "<p>15 : 48</p>",
                        "<p>11 : 35</p>",
                        "<p>13 : 42</p>"
                    ],
                    solution_en: "<p>17.(c) <strong>Logic :&nbsp;</strong>(first number &times; 3 + 3) = second number<br>(12 : 39) &rArr;&nbsp;12 &times; 3 +3 = 39<br>(15 : 48) &rArr; 15 &times; 3 + 3 = 48 <br>(13 : 42) &rArr; 13 &times; 3 + 3 = 42 <br>But<br>(11 : 35) &rArr; 11 &times; 3 + 3 = 36 (&ne; 35)</p>",
                    solution_hi: "<p>17.(c) <strong>तर्क:</strong> (पहली संख्या &times; 3 + 3)&nbsp; = दूसरी संख्या<br>(12 : 39) &rArr; 12 &times; 3 +3 = 39<br>(15 : 48) &rArr; 15 &times; 3 + 3 = 48 <br>(13 : 42) &rArr; 13 &times; 3 + 3 = 42 <br>लेकिन,<br>(11 : 35) &rArr; 11 &times; 3 + 3 = 36 (&ne; 35)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18 The position of how many letters will change, if each of the letters in the word &lsquo;SYSTEM&rsquo; is arranged in the English alphabetical order?</p>",
                    question_hi: "<p>18. यदि शब्द \'SYSTEM\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला के क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति बदल जाएगी?</p>",
                    options_en: [
                        "<p>Five</p>",
                        "<p>Six</p>",
                        "<p>Three</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>पाँच</p>",
                        "<p>छः</p>",
                        "<p>तीन</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089063.png\" alt=\"rId28\" width=\"157\" height=\"90\"><br>The position of five letters changes.</p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089063.png\" alt=\"rId28\" width=\"157\" height=\"90\"><br>पांच अक्षरों की स्थिति बदलती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In the year 2000, Monu was 3 times his sister&rsquo;s age. In 2010, he was 24 years older than her. Find Monu&rsquo;s age in 2010.</p>",
                    question_hi: "<p>19. वर्ष 2000 में, मोनू की आयु, उसकी बहन की आयु की 3 गुनी थी। वर्ष 2010 में उसकी आयु, उसकी बहन की आयु से 24 वर्ष अधिक थी। 2010 में मोनू की आयु ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>52 years</p>",
                        "<p>62 years</p>",
                        "<p>46 years</p>",
                        "<p>38 years</p>"
                    ],
                    options_hi: [
                        "<p>52 वर्ष</p>",
                        "<p>62 वर्ष</p>",
                        "<p>46 वर्ष</p>",
                        "<p>38 वर्ष</p>"
                    ],
                    solution_en: "<p>19.(c)<br>Let the age of Monu and his sister is 3x&nbsp;and x respectively.<br>After 10 years &rarr;<br>3x&nbsp;+ 10 = (x + 10) + 24<br>2x&nbsp;= 24, x = 12<br>Age of Monu in 2010 = (3 &times; 12) + 10 = 46 years</p>",
                    solution_hi: "<p>19.(c)<br>माना कि मोनू और उसकी बहन की उम्र क्रमशः 3x&nbsp;और x है।<br>10 साल बाद &rarr;<br>3x&nbsp;+ 10 = (x + 10) + 24<br>2x&nbsp;= 24, x = 12<br>2010 में मोनू की आयु = (3 &times; 12) + 10 = 46 वर्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If 26 September 2005 was Monday, then what was the day of the week on 26 September 2017?</p>",
                    question_hi: "<p>20. यदि 26 सितम्बर 2005 को सोमवार था, तो 26 सितम्बर 2017 को सप्ताह का कौन सा दिन था?</p>",
                    options_en: [
                        "<p>Tuesday</p>",
                        "<p>Wednesday</p>",
                        "<p>Saturday</p>",
                        "<p>Sunday</p>"
                    ],
                    options_hi: [
                        "<p>मंगलवार</p>",
                        "<p>बुधवार</p>",
                        "<p>शनिवार</p>",
                        "<p>रविवार</p>"
                    ],
                    solution_en: "<p>20.(a) 26 September 2005 was Monday. On going to 26 September 2017 the number of odd days are + 1 + 1 + 2 + 1 + 1 + 1 +2 + 1 + 1 + 1 + 2 + 1 = 15. On dividing 15 by 7 the remainder is 1. Monday + 1 = Tuesday.</p>",
                    solution_hi: "<p>20.(a) 26 सितम्बर 2005 को सोमवार था। 26 सितंबर 2017 को जाने पर विषम दिनों की संख्या + 1 + 1 + 2 + 1 + 1 + 1 +2 + 1 + 1 + 1 + 2 + 1 = 15 है। 15 को 7 से विभाजित करने पर शेषफल 1 है। सोमवार +1 = मंगलवार.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Identify the figure from among the given options which when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089309.png\" alt=\"rId29\" width=\"361\" height=\"73\"></p>",
                    question_hi: "<p>21. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089309.png\" alt=\"rId29\" width=\"361\" height=\"73\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089596.png\" alt=\"rId30\" width=\"95\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089715.png\" alt=\"rId31\" width=\"95\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089863.png\" alt=\"rId32\" width=\"95\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089977.png\" alt=\"rId33\" width=\"95\" height=\"94\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089596.png\" alt=\"rId30\" width=\"95\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089715.png\" alt=\"rId31\" width=\"95\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089863.png\" alt=\"rId32\" width=\"95\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089977.png\" alt=\"rId33\" width=\"95\" height=\"94\"></p>"
                    ],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089863.png\" alt=\"rId32\" width=\"96\" height=\"95\"></p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756089863.png\" alt=\"rId32\" width=\"96\" height=\"95\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. How many triangles are there in the given figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090135.png\" alt=\"rId34\" width=\"133\" height=\"118\"></p>",
                    question_hi: "<p>22. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090135.png\" alt=\"rId34\" width=\"133\" height=\"118\"></p>",
                    options_en: [
                        "<p>13</p>",
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>11</p>"
                    ],
                    options_hi: [
                        "<p>13</p>",
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>11</p>"
                    ],
                    solution_en: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090267.png\" alt=\"rId35\" width=\"172\" height=\"144\"><br>There are 11 triangles = ABG, AJG, BGJ, EFG, EFD, DFH, GED, EDH, CHI, AHC, AHI.</p>",
                    solution_hi: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090267.png\" alt=\"rId35\" width=\"172\" height=\"144\"><br>11 त्रिभुज हैं = ABG, AJG, BGJ, EFG, EFD, DFH, GED, EDH, CHI, AHC, AHI.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>17 &ndash; 19 &ndash; 16 &ndash; 20; 22 &ndash; 24 &ndash; 21 &ndash; 25</p>",
                    question_hi: "<p>23. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं के प्रत्येक समुच्चय में, पहली संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी प्रकार, दूसरी संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे भी होती है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए समुच्चयों की तरह संक्रियाओं के समान समुच्चय का अनुसरण करता है ?<br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर की जाने वाली संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि केवल 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना, तथा 1 और 3 पर गणितीय संक्रिया करने की अनुमति नहीं है।)<br>17 &ndash; 19 &ndash; 16 &ndash; 20; 22 &ndash; 24 &ndash; 21 &ndash; 25</p>",
                    options_en: [
                        "<p>15 &ndash; 17 &ndash; 14 &ndash; 18</p>",
                        "<p>16 &ndash; 18 &ndash; 12 &ndash; 16</p>",
                        "<p>11 &ndash; 13 &ndash; 10 &ndash; 15</p>",
                        "<p>18 &ndash; 20 &ndash; 16 &ndash; 14</p>"
                    ],
                    options_hi: [
                        "<p>15 &ndash; 17 &ndash; 14 &ndash; 18</p>",
                        "<p>16 &ndash; 18 &ndash; 12 &ndash; 16</p>",
                        "<p>11 &ndash; 13 &ndash; 10 &ndash; 15</p>",
                        "<p>18 &ndash; 20 &ndash; 16 &ndash; 14</p>"
                    ],
                    solution_en: "<p>23.(a)<br><strong>Logic:- </strong>1<sup>at</sup>no.&nbsp;+<strong> 2 =</strong> 2<sup>nd</sup>no. , 2<sup>nd</sup>no. <strong>- 3 =</strong> 3<sup>rd</sup>no., 3<sup>rd</sup>no. <strong>+ 4 =</strong> 4<sup>th</sup>no. <br>(17 -&nbsp;19 - 16 - 20) :- <strong>17 </strong>+ 2 =<strong> 19</strong>, 19 - 3 = <strong>16</strong>, 16 + 4 = <strong>20</strong><br>(22 -&nbsp;24 - 21 - 25) :- <strong>22</strong> + 2 = <strong>24</strong>, 24 - 3 =<strong> 21</strong>, 21 + 4 = <strong>25</strong><br>Similarly<br>(15 -&nbsp;17 - 14 - 18) :- <strong>15</strong> + 2 = <strong>17</strong>, 17 - 3 =<strong> 14,</strong> 14 + 4 = <strong>18</strong></p>",
                    solution_hi: "<p>23.(a)<br><strong>तर्क:-</strong>&nbsp;पहली संख्या + 2 = दूसरी संख्या , दूसरी संख्या - 3 = तीसरी संख्या, तीसरी संख्या + 4 = चौथी संख्या<br>(17 -&nbsp;19 - 16 - 20) :- <strong>17 </strong>+ 2 =<strong> 19</strong>, 19 - 3 = <strong>16</strong>, 16 + 4 = <strong>20</strong><br>(22 -&nbsp;24 - 21 - 25) :- <strong>22</strong> + 2 = <strong>24</strong>, 24 - 3 =<strong> 21</strong>, 21 + 4 = <strong>25</strong><br>इसी प्रकार, <br>(15 -&nbsp;17 - 14 - 18) :- <strong>15</strong> + 2 = <strong>17</strong>, 17 - 3 =<strong> 14,</strong> 14 + 4 = <strong>18</strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090397.png\" alt=\"rId36\" width=\"143\" height=\"132\"></p>",
                    question_hi: "<p>24. दर्पण को MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090397.png\" alt=\"rId36\" width=\"149\" height=\"137\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090496.png\" alt=\"rId37\" width=\"151\" height=\"33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090627.png\" alt=\"rId38\" width=\"150\" height=\"33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090732.png\" alt=\"rId39\" width=\"151\" height=\"33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090819.png\" alt=\"rId40\" width=\"150\" height=\"33\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090496.png\" alt=\"rId37\" width=\"150\" height=\"33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090627.png\" alt=\"rId38\" width=\"150\" height=\"33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090732.png\" alt=\"rId39\" width=\"151\" height=\"33\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090819.png\" alt=\"rId40\" width=\"149\" height=\"33\"></p>"
                    ],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090732.png\" alt=\"rId39\" width=\"151\" height=\"33\"></p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756090732.png\" alt=\"rId39\" width=\"151\" height=\"33\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In a certain code language, \'my best friend\' is written as \'ck rl mk\' and \'friend is life\' is written as \'rt mp ck&rsquo;. How is \'friend\' written in the given language?</p>",
                    question_hi: "<p>25. एक निश्चित कूट भाषा में, \'my best friend\' को \'ck rI mk\' लिखा जाता है और &lsquo;friend is life\' को \'rt mp ck\' लिखा जाता है। उसी कूट भाषा में \'friend\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>rt</p>",
                        "<p>ck</p>",
                        "<p>rl</p>",
                        "<p>mk</p>"
                    ],
                    options_hi: [
                        "<p>rt</p>",
                        "<p>ck</p>",
                        "<p>rl</p>",
                        "<p>mk</p>"
                    ],
                    solution_en: "<p>25.(b) <br>my best friend &rarr; ck rl mk&hellip;&hellip;(i)<br>friend is life &rarr; rt mp ck&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;friend&rsquo; and &lsquo;ck&rsquo; are common. The code of &lsquo;friend&rsquo; = &lsquo;ck&rsquo;.</p>",
                    solution_hi: "<p>25.(b) <br>my best friend &rarr; ck rl mk&hellip;&hellip;(i)<br>friend is life &rarr; rt mp ck&hellip;&hellip;.(ii)<br>(i) और (ii) से \'friend\' और \'ck\' उभयनिष्ठ हैं। \'friend\' का कूट = \'ck\'</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Captain Hawkins visited India during the reign of:</p>",
                    question_hi: "<p>26. कैप्टन हॉकिन्स (Captain Hawkins) ने किसके शासनकाल के दौरान भारत का दौरा किया?</p>",
                    options_en: [
                        "<p>Akbar</p>",
                        "<p>Jahangir</p>",
                        "<p>Babur</p>",
                        "<p>Shahjahan</p>"
                    ],
                    options_hi: [
                        "<p>अकबर</p>",
                        "<p>जहाँगीर</p>",
                        "<p>बाबर</p>",
                        "<p>शाहजहाँ</p>"
                    ],
                    solution_en: "<p>26.(b) <strong>Jahangir</strong> (1605 - 1627) was the fourth Mughal Emperor. Captain William Hawkins visited India in 1608 as an envoy of the English East India Company, seeking permission to establish a trading post in India. He was able to secure a strong position in Jahangir\'s court, who referred to him as \"English Khan\". Sir Thomas Roe, another English ambassador to Jahangir\'s court from 1615&ndash;1619.</p>",
                    solution_hi: "<p>26.(b) <strong>जहांगीर</strong> (1605 - 1627) चौथे मुगल सम्राट थे। कैप्टन विलियम हॉकिन्स 1608 में अंग्रेजी ईस्ट इंडिया कंपनी के दूत के रूप में भारत आये तथा भारत में व्यापारिक केन्द्र स्थापित करने की अनुमति मांगी। वह जहांगीर के दरबार में एक मजबूत स्थान बनाने में सफल रहे, जहांगीर ने उन्हें \"इंग्लिश खान\" कहकर संबोधित किया। सर थॉमस रो, एक अन्य अंग्रेजी राजदूत, 1615-1619 के बीच जहांगीर के दरबार में उपस्थित थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following is NOT a part of the ocean floor?</p>",
                    question_hi: "<p>27. निम्नलिखित में से कौन महासागर सतह (ocean floor) का भाग नहीं है?</p>",
                    options_en: [
                        "<p>The continental shelf</p>",
                        "<p>The continental slope</p>",
                        "<p>The deep sea shelf</p>",
                        "<p>The deep sea plain</p>"
                    ],
                    options_hi: [
                        "<p>महादेशीय निधाय</p>",
                        "<p>महादेशीय ढलान</p>",
                        "<p>अगाध सागरी निधाय</p>",
                        "<p>अगाध सागरी मैदान</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>The deep sea shelf.</strong> A continental shelf is the submerged edge of a continent beneath the ocean. The continental slope extends seaward from the shelf to the upper edge of a continental rise or the point where the slope begins to flatten. An abyssal plain, or deep sea plain, is a flat region on the ocean floor, typically found at depths between 3,000 and 6,000 meters. Oceanic deeps are the deepest parts of the ocean, covered with fine-grained sediments such as clay and silt. Examples of oceanic landforms : Abyssal plains, Submarine canyons, Deep ocean trenches, Guyots, Seamounts, Mid-ocean ridges, Abyssal plains, etc,.</p>",
                    solution_hi: "<p>27.(c) <strong>अगाध सागरी निधाय।</strong> महाद्वीपीय निधाय समुद्र के नीचे महाद्वीप का डूबा हुआ तट है। महाद्वीपीय ढलान शेल्फ़ से महाद्वीपीय वृद्धि के ऊपरी किनारे या उस बिंदु तक समुद्र की ओर फैली हुई है जहाँ ढलान समतल होना शुरू होता है। एक अथाह मैदान, या गहरा समुद्री मैदान, समुद्र तल पर एक समतल क्षेत्र है, जो आमतौर पर 3,000 से 6,000 मीटर की गहराई पर पाया जाता है। महासागरीय गहराई समुद्र का सबसे गहरा हिस्सा है, जो मिट्टी और गाद जैसे महीन दाने वाले तलछट से ढका होता है। महासागरीय भू-आकृतियों के उदाहरण: वितलीय मैदान, अन्तः सागरीय कैनियन, गहरे समुद्र के गर्त, गयोट (सपाट शीर्ष वाला समुद्री पर्वत), सीमाउंट, मध्य-महासागर रिज, इत्यादि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. In which of the following states of India is the festival Bonalu celebrated ?</p>",
                    question_hi: "<p>28. भारत के निम्नलिखित में से किस राज्य में बोनालु पर्व मनाया जाता है ?</p>",
                    options_en: [
                        "<p>West Bengal</p>",
                        "<p>Tamil Nadu</p>",
                        "<p>Assam</p>",
                        "<p>Telangana</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिम बंगाल</p>",
                        "<p>तमिलनाडु</p>",
                        "<p>असम</p>",
                        "<p>तेलंगाना</p>"
                    ],
                    solution_en: "<p>28.(d) <strong>Telangana. </strong>Bonalu is a Hindu festival dedicated to the worship of Goddess Mahakali. It is celebrated annually in Hyderabad, Secunderabad, and other parts of Telangana. Other festivals of Telangana : Peerla Panduga, Sammakka Saarakka Jaathara, and Bathukamma.</p>",
                    solution_hi: "<p>28.(d) <strong>तेलंगाना।</strong> बोनालु एक हिंदू त्योहार है, जिसमें देवी महाकाली की पूजा की जाती है। यह प्रतिवर्ष हैदराबाद, सिकंदराबाद और तेलंगाना के अन्य हिस्सों में मनाया जाता है। तेलंगाना के अन्य त्योहार : पीरला पांडुगा, सम्मक्का सारक्का जथारा, और बथुकम्मा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. NALCO is an Indian government enterprise, dealing with which of the following minerals?</p>",
                    question_hi: "<p>29. नाल्को (NALCO) एक भारतीय सरकारी उद्यम है, जो निम्नलिखित में से किस खनिज से संबंधित है?</p>",
                    options_en: [
                        "<p>Bauxite</p>",
                        "<p>Iron</p>",
                        "<p>Mica</p>",
                        "<p>Copper</p>"
                    ],
                    options_hi: [
                        "<p>बॉक्साइट</p>",
                        "<p>लोहा</p>",
                        "<p>अभ्रक</p>",
                        "<p>तांबा</p>"
                    ],
                    solution_en: "<p>29.(a)<strong> Bauxite. </strong>National Aluminium Company Limited (NALCO), a &lsquo;Navratna&rsquo; public sector undertaking (PSU) established in 1981. Headquarters - Bhubaneswar, Odisha. Odisha has the largest bauxite reserves in India.</p>",
                    solution_hi: "<p>29.(a) <strong>बॉक्साइट। </strong>नेशनल एल्युमिनियम कंपनी लिमिटेड (NALCO), 1981 में स्थापित एक &lsquo;नवरत्न&rsquo; सार्वजनिक क्षेत्र का उपक्रम (PSU) है। मुख्यालय - भुवनेश्वर, ओडिशा। ओडिशा में भारत का सबसे बड़ा बॉक्साइट भंडार है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which type of title can be conferred by the state as per Article 18 of the Constitution of India?</p>",
                    question_hi: "<p>30. भारत के संविधान के अनुच्छेद 18 के अनुसार राज्य द्वारा किस प्रकार की उपाधि प्रदान की जा सकती है?</p>",
                    options_en: [
                        "<p>Sports and Music</p>",
                        "<p>Religious and Music</p>",
                        "<p>Military and Academic</p>",
                        "<p>Military and Social</p>"
                    ],
                    options_hi: [
                        "<p>खेल और संगीत</p>",
                        "<p>धार्मिक और संगीत</p>",
                        "<p>सैन्य और शैक्षणिक</p>",
                        "<p>सैन्य और सामाजिक</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>Military and Academic.</strong> Article 18 of the constitution of India abolishes titles. It prohibits the state from conferring any title on any citizen or a foreigner (except a military or academic distinction). No citizen of India shall receive any title from a foreign state. The Fundamental Rights are enshrined in Part III (described as the Magna Carta of India) of the Constitution (Articles 12-35).</p>",
                    solution_hi: "<p>30.(c) <strong>सैन्य और शैक्षणिक। </strong>भारत के संविधान का अनुच्छेद 18 उपाधियों को समाप्त करता है। यह राज्य को किसी भी नागरिक या विदेशी को कोई भी उपाधि (सैन्य या शैक्षणिक विशिष्टता को छोड़कर) प्रदान करने से रोकता है। भारत का कोई नागरिक विदेशी राज्य से कोई उपाधि प्राप्त नहीं करेगा। मौलिक अधिकार संविधान के भाग III (भारत के मैग्ना कार्टा के रूप में वर्णित) (अनुच्छेद 12-35) में निहित हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who founded the Bombay Presidency Association in 1885 along with Pherozeshah Mehta and KT Telang?</p>",
                    question_hi: "<p>31. फिरोजशाह मेहता और के.टी. तेलंग के साथ 1885 में बॉम्बे प्रेसीडेंसी एसोसिएशन (Bombay Presidency Association) की स्थापना किसने की थी?</p>",
                    options_en: [
                        "<p>AK Fazlul Haq</p>",
                        "<p>Badruddin Tyabji</p>",
                        "<p>Behramji M Malabari</p>",
                        "<p>Zakir Husain</p>"
                    ],
                    options_hi: [
                        "<p>ए. के. फजलुल हक (AK Fazlul Haq)</p>",
                        "<p>बदरुद्दीन तैयबजी (Badruddin Tyabji)</p>",
                        "<p>बेहरामजी एम मालाबारी (Behramji M Malabari)</p>",
                        "<p>जाकिर हुसैन (Zakir Hussain)</p>"
                    ],
                    solution_en: "<p>31.(b)<strong> Badruddin Tyabji </strong>was the third President (in 1887) of the Indian National Congress and the first Muslim to hold that position. Other Political organisations and founder: London Indian Society (1865) - Dadabhai Naoroji. East India Association (1866) - Dadabhai Naoroji. Poona Sarvajanik Sabha (1870) - G.V. Joshi, S.H. Sathe, S.H. Chiplonkar and Mahadev Govind Ranade.</p>",
                    solution_hi: "<p>31.(b) <strong>बदरुद्दीन तैयबजी</strong> भारतीय राष्ट्रीय कांग्रेस के तीसरे अध्यक्ष (1887 में) थे और इस पद पर आसीन होने वाले पहले मुस्लिम थे। अन्य राजनीतिक संगठन और संस्थापक: लंदन इंडियन सोसाइटी (1865) - दादाभाई नौरोजी। ईस्ट इंडिया एसोसिएशन (1866) - दादाभाई नौरोजी। पूना सार्वजनिक सभा (1870) - जी.वी. जोशी, एस.एच. साठे, एस.एच. चिपलोंकर और महादेव गोविंद रानाडे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. The Rangaswami Cup is associated with sports _______.</p>",
                    question_hi: "<p>32. रंगास्वामी कप (Rangaswami Cup) का संबंध __________ खेल से है।</p>",
                    options_en: [
                        "<p>Football</p>",
                        "<p>Hockey</p>",
                        "<p>Tennis</p>",
                        "<p>Badminton</p>"
                    ],
                    options_hi: [
                        "<p>फुटबॉल</p>",
                        "<p>हॉकी</p>",
                        "<p>टेनिस</p>",
                        "<p>बैडमिंटन</p>"
                    ],
                    solution_en: "<p>32.(b)<strong> Hockey. </strong>The Rangaswami Cup was first held in 1928 as an Inter-Provincial Tournament to select players for the national team for the Olympics. Other Hockey Trophies and Cups - Agha Khan Cup, Dhyan Chand Trophy, Azlan Shah Cup, Indira Gandhi Gold Cup.</p>",
                    solution_hi: "<p>32.(b) <strong>हॉकी।</strong> रंगास्वामी कप पहली बार 1928 में ओलंपिक के लिए राष्ट्रीय टीम के लिए खिलाड़ियों का चयन करने के लिए एक अंतर-प्रांतीय टूर्नामेंट के रूप में आयोजित किया गया था। अन्य हॉकी ट्रॉफी और कप - आगा खां कप, ध्यान चंद ट्रॉफी, अजलान शाह कप, इंदिरा गांधी गोल्ड कप।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. How many state Legislative Council members are elected by graduates of three years standing and residing within the state?</p>",
                    question_hi: "<p>33. राज्य विधान परिषद के कितने सदस्य राज्य के भीतर तीन वर्ष से निवास करने वाले स्नातकों द्वारा चुने जाते हैं?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>th</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>th</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>th</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>rd</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>th</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>th</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>th</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>rd</p>"
                    ],
                    solution_en: "<p>33.(c) <strong><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>th.</strong> In the legislative council, members are elected as follows: One-sixth by the governor from individuals with knowledge in science, art, literature, the co-operative movement, and social service; One-third by local body members (corporations, municipalities, and Zila Parishads); one-third shall be elected by the members of the Legislative Assembly of the State from amongst persons who are not members of the Assembly; one-twelfth by graduates residing in the state for three years; and One twelfth are elected by teachers who had spent at least three years in teaching in educational institutions within the state not lower than secondary schools, including colleges and universities.</p>",
                    solution_hi: "<p>33.(c) <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math><strong>th.</strong> विधान परिषद में सदस्यों का निर्वाचन इस प्रकार होता है: एक-छठा भाग राज्यपाल द्वारा विज्ञान, कला, साहित्य, सहकारी आंदोलन और सामाजिक सेवा में ज्ञान रखने वाले व्यक्तियों में से चुना जाता है; एक-तिहाई स्थानीय निकाय सदस्यों (निगमों, नगर पालिकाओं और जिला परिषदों) द्वारा चुना जाता है; एक तिहाई सदस्य राज्य विधान सभा के सदस्यों द्वारा ऐसे व्यक्तियों में से चुने जाएंगे जो विधान सभा के सदस्य नहीं हैं; एक-बारहवां भाग का चुनाव उन शिक्षकों द्वारा किया जाता है, जिन्होंने राज्य के शैक्षणिक संस्थानों में अध्यापन में कम से कम तीन वर्ष बिताए हों, जो महाविद्यालयों और विश्वविद्यालयों सहित माध्यमिक विद्यालयों से नीचे नहीं हों।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. What happens in case of market equilibrium:<br>(A) Market demand = market supply<br>(B) There is no excess supply in the market</p>",
                    question_hi: "<p>34. बाजार संतुलन की स्थिति में क्या होता है:<br>(A) बाजार मांग = बाजार आपूर्ति<br>(B) बाजार में कोई अतिरिक्त आपूर्ति नहीं है</p>",
                    options_en: [
                        "<p>Neither (A) nor (B)</p>",
                        "<p>Only (B)</p>",
                        "<p>Only (A)</p>",
                        "<p>Both (a) and (b)</p>"
                    ],
                    options_hi: [
                        "<p>न तो (A) और न ही (B)</p>",
                        "<p>केवल (B)</p>",
                        "<p>केवल (A)</p>",
                        "<p>दोनों (A) और (B)</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Both (A) and (B).</strong> Market equilibrium is a market state where the supply in the market is equal to the demand in the market. The equilibrium price is the price of a good or service when the supply of it is equal to the demand for it in the market. supply and demand, in economics, relationship between the quantity of a commodity that producers wish to sell at various prices and the quantity that consumers wish to buy.</p>",
                    solution_hi: "<p>34.(d) <strong>दोनों (A) और (B)।</strong> बाजार संतुलन एक बाजार की स्थिति है जहाँ बाजार में आपूर्ति बाजार में मांग के बराबर होती है। संतुलन मूल्य किसी वस्तु या सेवा की कीमत है जब इसकी आपूर्ति बाजार में इसकी मांग के बराबर होती है। अर्थशास्त्र में आपूर्ति और मांग, किसी वस्तु की उस मात्रा के बीच का संबंध है जिसे उत्पादक विभिन्न कीमतों पर बेचना चाहते हैं और वह मात्रा जिसे उपभोक्ता खरीदना चाहते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following was the last dynasty of the Vijayanagara Empire?</p>",
                    question_hi: "<p>35. निम्नलिखित में से कौन विजयनगर साम्राज्य का अंतिम राजवंश था?</p>",
                    options_en: [
                        "<p>Tuluva dynasty</p>",
                        "<p>Saluva dynasty</p>",
                        "<p>Sangam dynasty</p>",
                        "<p>Aravidu dynasty</p>"
                    ],
                    options_hi: [
                        "<p>तुलुव राजवंश (Tuluva dynasty)</p>",
                        "<p>सलुव राजवंश (Saluva dynasty)</p>",
                        "<p>संगम राजवंश (Sangam dynasty)</p>",
                        "<p>अराविडु राजवंश (Aravidu dynasty)</p>"
                    ],
                    solution_en: "<p>35.(d) <strong>Aravidu dynasty. </strong>The Vijayanagara Empire was a late medieval Hindu empire that ruled much of southern India. It was established in 1336 by the brothers Harihara I and Bukka Raya I of the Sangama dynasty. The Vijayanagara Empire had four dynasties: Sangama dynasty (1336-1485 CE), Saluva dynasty (1485-1505 CE), Tuluva dynasty (1505-1570 CE), Aravidu dynasty (1570-1646 CE).</p>",
                    solution_hi: "<p>35.(d) <strong>अराविडु राजवंश।</strong> विजयनगर साम्राज्य एक मध्यकालीन हिंदू साम्राज्य था जिसने दक्षिण भारत के अधिकांश भाग पर शासन किया था। इसकी स्थापना 1336 में संगम राजवंश के भाइयों हरिहर प्रथम और बुक्का राय प्रथम ने की थी। विजयनगर साम्राज्य में चार राजवंश थे: संगम राजवंश (1336-1485 ई.), सलुव राजवंश (1485-1505 ई.), तुलुव राजवंश (1505-1570 ई.), अराविडु राजवंश (1570-1646 ई.)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. The five-year plans for India with their duration are given below. Which of the following is correctly matched?</p>\n<table style=\"border-collapse: collapse; width: 60.5072%; height: 64.3751px;\" border=\"1\"><colgroup><col style=\"width: 9.6%;\"><col style=\"width: 45.297%;\"><col style=\"width: 45.003%;\"></colgroup>\n<tbody>\n<tr style=\"height: 25.1875px;\">\n<td style=\"height: 25.1875px;\">(a)</td>\n<td style=\"height: 25.1875px;\">Second five-year plan</td>\n<td style=\"height: 25.1875px;\">1956-57 to 1960-61</td>\n</tr>\n<tr style=\"height: 19.5938px;\">\n<td style=\"height: 19.5938px;\">(b)</td>\n<td style=\"height: 19.5938px;\">Third five-year plan</td>\n<td style=\"height: 19.5938px;\">1961-62 to 1965-66</td>\n</tr>\n<tr style=\"height: 19.5938px;\">\n<td style=\"height: 19.5938px;\">(c)</td>\n<td style=\"height: 19.5938px;\">Fourth five-year plan</td>\n<td style=\"height: 19.5938px;\">1966-67 to 1970-71</td>\n</tr>\n</tbody>\n</table>",
                    question_hi: "<p>36. नीचे भारत की पंचवर्षीय योजनाएं उनकी अवधि के साथ दी गई हैं। निम्नलिखित में से कौन-सी सही से सुमेलित हैं?</p>\n<table style=\"border-collapse: collapse; width: 62.3188%; height: 58.7814px;\" border=\"1\"><colgroup><col style=\"width: 9.02913%;\"><col style=\"width: 41.646%;\"><col style=\"width: 49.5191%;\"></colgroup>\n<tbody>\n<tr style=\"height: 19.5938px;\">\n<td style=\"height: 19.5938px;\">(a)</td>\n<td style=\"height: 19.5938px;\">द्वितीय पंचवर्षीय योजना</td>\n<td style=\"height: 19.5938px;\">1956-57 से 1960-61 तक</td>\n</tr>\n<tr style=\"height: 19.5938px;\">\n<td style=\"height: 19.5938px;\">(b)</td>\n<td style=\"height: 19.5938px;\">तृतीय पंचवर्षीय योजना</td>\n<td style=\"height: 19.5938px;\">1961-62 से 1965-66 तक</td>\n</tr>\n<tr style=\"height: 19.5938px;\">\n<td style=\"height: 19.5938px;\">(c)</td>\n<td style=\"height: 19.5938px;\">चतुर्थ पंचवर्षीय योजना</td>\n<td style=\"height: 19.5938px;\">1966-67 से 1970-71 तक</td>\n</tr>\n</tbody>\n</table>",
                    options_en: [
                        "<p>Both b and c</p>",
                        "<p>Both a and b</p>",
                        "<p>Both a and c</p>",
                        "<p>Only a</p>"
                    ],
                    options_hi: [
                        "<p>b और c, दोनों</p>",
                        "<p>a और b, दोनों</p>",
                        "<p>a और c, दोनों</p>",
                        "<p>केवल a</p>"
                    ],
                    solution_en: "<p>36.(b) <strong>Both a and b.</strong> The First Five-Year Plan was launched in 1951. The Second Plan (1956-1961), known as the Mahalanobis Plan, had a target growth rate of 4.5% and achieved 4.3%. The Third Plan (1961-1966) aimed to make the economy self-sufficient. Due to the Indo-China war, Indo-Pakistan war, and severe droughts, a \"Plan Holiday\" was observed from 1966 to 1969. The Fourth Plan (1969-1974), under Indira Gandhi, focused on growth and included Family Planning Programmes.</p>",
                    solution_hi: "<p>36.(b) <strong>a और b, दोनों।</strong> प्रथम पंचवर्षीय योजना 1951 में शुरू की गई थी। दूसरी पंचवर्षीय योजना (1956-1961), जिसे महालनोबिस योजना के नाम से भी जाना जाता है, इस योजना का लक्ष्य 4.5% की वृद्धि दर हासिल करना था, जबकि वास्तविक वृद्धि दर 4.3% रही। तीसरी पंचवर्षीय योजना (1961-1966) का उद्देश्य अर्थव्यवस्था को आत्मनिर्भर बनाना था। भारत-चीन युद्ध, भारत-पाकिस्तान युद्ध और गंभीर सूखे की स्थिति के कारण 1966 से 1969 तक \"योजना अवकाश\" के रूप में जाना जाता है। इंदिरा गांधी के नेतृत्व में चौथी पंचवर्षीय योजना (1969-1974) में आर्थिक विकास पर ध्यान केंद्रित किया गया और साथ ही परिवार नियोजन कार्यक्रमों को भी शामिल किया गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. From where do most of the refracted light rays enter the eyes?</p>",
                    question_hi: "<p>37. अधिकांश अपवर्तित प्रकाश किरणें आँखों में कहाँ से प्रवेश करती हैं?</p>",
                    options_en: [
                        "<p>From the outer surface of the cornea</p>",
                        "<p>From the outer surface of the retina</p>",
                        "<p>From the outer surface of the iris</p>",
                        "<p>From the inner surface of the cornea</p>"
                    ],
                    options_hi: [
                        "<p>कॉर्निया की बाहरी सतह से</p>",
                        "<p>रेटिना की बाहरी सतह से</p>",
                        "<p>आईरिस की बाहरी सतह से</p>",
                        "<p>कॉर्निया की भीतरी सतह से</p>"
                    ],
                    solution_en: "<p>37.(a) <strong>From the outer surface of the cornea.</strong> Cornea: It is the clear, transparent, anterior portion of the external coat of the eyeball. The rays of light enter this layer and it accounts for two-thirds of the total optical power of the eye. Three layers of human eye - External fibrous coat, Middle vascular coat, and Internal nervous coat. Parts of the human eye - Pupil, Iris, Lens, Retina, Sclera.</p>",
                    solution_hi: "<p>37.(a) <strong>कॉर्निया की बाहरी पटल से ।</strong> कॉर्निया: यह नेत्रगोलक के बाहरी आवरण का स्पष्ट, पारदर्शी, अग्रिम भाग है। प्रकाश की किरणें इस परत में प्रवेश करती हैं और यह आंख की कुल ऑप्टिकल शक्ति का दो-तिहाई हिस्सा होती है। मानव आँख की तीन परतें - बाहरी रेशेदार आवरण, मध्य संवहनी आवरण और आंतरिक तंत्रिका आवरण। मानव आँख के भाग - पुतली, आइरिस, लेंस, रेटिना, स्केलेरा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Geeta Mahalik won the Sangeet Natak Akademi Award for which form of dance?</p>",
                    question_hi: "<p>38. गीता महालिक ने किस प्रकार के नृत्य के लिए संगीत नाटक अकादमी पुरस्कार जीता?</p>",
                    options_en: [
                        "<p>Odissi</p>",
                        "<p>Kathakali</p>",
                        "<p>Bharatanatyam</p>",
                        "<p>Kuchipudi</p>"
                    ],
                    options_hi: [
                        "<p>ओडिसी</p>",
                        "<p>कथकली</p>",
                        "<p>भरतनाट्यम</p>",
                        "<p>कुचिपुड़ी</p>"
                    ],
                    solution_en: "<p>38.(a) <strong>Odissi.</strong> Geeta Mahalik received the Padma Shri (2014) and Sangeet Natak Akademi Award in 2009. Dance Exponents: Odissi - Deba Prasad Das, Mayadhar Raut, Priyambada Mohanty Hejmadi, and Kiran Segal. Kathakali - Chenganoor Raman Pillai, Kottakkal Sivaraman, Vazhenkada Vijayan. Bharatanatyam - Tanjore Balasaraswati, Rukmini Devi Arundale. Kuchipudi - Sobha Naidu, Veernala Jayarama Rao and Vyjayanthi Kashi.</p>",
                    solution_hi: "<p>38.(a) <strong>ओडिसी।</strong> गीता महालिक को पद्म श्री (2014) और 2009 में संगीत नाटक अकादमी पुरस्कार प्रदान किया गया था। नृत्य प्रतिपादक: ओडिसी - देबा प्रसाद दास, मायाधर राउत, प्रियंबदा मोहंती हेजमाडी, और किरण सहगल। कथकली - चेंगानूर रमन पिल्लई, कोट्टक्कल शिवरामन, वाझेनकाडा विजयन। भरतनाट्यम - तंजौर बालासरस्वती, रुक्मिणी देवी अरुंडेल। कुचिपुड़ी - शोभा नायडू, वीरनाला जयराम राव और वैजयंती काशी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which element has the highest atomic number and is the heaviest naturally occurring element ?</p>",
                    question_hi: "<p>39. किस तत्व की परमाणु संख्या सबसे अधिक है और यह प्राकृतिक रूप से पाया जाने वाला सबसे भारी तत्व है?</p>",
                    options_en: [
                        "<p>Thorium (Th)</p>",
                        "<p>Plutonium (Pu)</p>",
                        "<p>Neptunium (Np)</p>",
                        "<p>Uranium (U)</p>"
                    ],
                    options_hi: [
                        "<p>थोरियम (Thorium)</p>",
                        "<p>प्लूटोनियम (Plutonium)</p>",
                        "<p>नेपट्यूनियम (Neptunium)</p>",
                        "<p>यूरेनियम (Uranium)</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>Uranium (U). </strong>The heaviest naturally occurring element is uranium, with an atomic number of 92 and an atomic weight of 238 u. The heaviest element discovered is Ununoctium or Oganesson with atomic number 118 but it is a man-made element.</p>",
                    solution_hi: "<p>39.(d) <strong>यूरेनियम (U)। </strong>प्राकृतिक रूप से पाया जाने वाला सबसे भारी तत्व यूरेनियम है, जिसकी परमाणु क्रमांक 92 और परमाणु भार 238 u है। खोजा गया सबसे भारी तत्व यूनुनोक्टियम या ओगेनेसन है जिसका परमाणु क्रमांक 118 है, लेकिन यह एक मानव निर्मित तत्व है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. President Sukarno of _______ was the Guest of Honour during India\'s first Republic Day event in 1950.</p>",
                    question_hi: "<p>40. वर्ष 1950 में भारत के पहले गणतंत्रता दिवस कार्यक्रम के दौरान विशिष्ट अतिथि रहे राष्ट्रपति सुकर्णो (Sukarno) किस देश के थे?</p>",
                    options_en: [
                        "<p>Indonesia</p>",
                        "<p>Malaysia</p>",
                        "<p>Maldives</p>",
                        "<p>Singapore</p>"
                    ],
                    options_hi: [
                        "<p>इंडोनेशिया (Indonesia)</p>",
                        "<p>मलेशिया (Malaysia)</p>",
                        "<p>मालदीव (Maldives)</p>",
                        "<p>सिंगापुर (Singapore)</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Indonesia.</strong> Famous Personalities included as chief guests in Republic Day Parade : 1955 - Malik Muhammad (Pakistan), 1960 - Kliment Voroshilov (Soviet Union), 2015 - Barack Obama (United States), 2019 - Cyril Ramaphosa (South Africa), 2020 - Jair Bolsonaro (Brazilian), 2023 - Abdel Fattah al-sisi (Egypt) and 2024 - Emmanuel Macron (France).</p>",
                    solution_hi: "<p>40.(a) <strong>इंडोनेशिया।</strong> गणतंत्र दिवस परेड में मुख्य अतिथि के तौर पर शामिल हुईं प्रसिद्ध हस्तियां : 1955 - मलिक मुहम्मद (पाकिस्तान), 1960 - क्लिमेंट वोरोशिलोव (सोवियत संघ), 2015 - बराक ओबामा (संयुक्त राज्य अमेरिका), 2019 - सिरिल रामफोसा (दक्षिण अफ्रीका), 2020 - जायर बोल्सोनारो (ब्राज़ीलियाई) ), 2023 - अब्देल फतह अल-सिसी (मिस्र) और 2024 - इमैनुएल मैक्रॉन (फ्रांस)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Consider the following statements:<br>A) Calcium is present in S - block of the periodic table.<br>B) Metals comprise more than 78% of all known elements and appear on the left side of the Periodic Table. <br>C) d- Block elements are also known as inner-transition electrons.</p>",
                    question_hi: "<p>41. निम्नलिखित कथनों को पढ़िए।<br>A) आवर्त सारणी के S - ब्लॉक में कैल्शियम मौजूद होता है।<br>B) सभी ज्ञात तत्वों में 78% से अधिक तत्व धातु हैं और ये आवर्त सारणी के बाईं ओर दिखाई देते हैं।<br>C) d-ब्लॉक के तत्वों को आंतरिक संक्रमण इलेक्ट्रॉनों के रूप में भी जाना जाता है।</p>",
                    options_en: [
                        "<p>A, B, C are true</p>",
                        "<p>Both A and B is true</p>",
                        "<p>Only B and C is true</p>",
                        "<p>Both A and C is true</p>"
                    ],
                    options_hi: [
                        "<p>A, B और C तीनों सत्य हैं</p>",
                        "<p>A और B दोनों सत्य हैं</p>",
                        "<p>केवल B और C सत्य हैं</p>",
                        "<p>A और C दोनों सत्य हैं</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>Both A and B is true.</strong> The d-block elements are also known as transition metals and are located in groups 3 to 12 of the periodic table. Examples of d-Block Elements: Scandium (Sc), Titanium (Ti), Vanadium (V), Chromium (Cr), Manganese (Mn), and Iron (Fe). Inner-transition elements - It refers to the elements in the f-block of the periodic table. These include the Lanthanides and Actinides.</p>",
                    solution_hi: "<p>41.(b) <strong>A और B दोनों सत्य हैं।</strong> d-ब्लॉक तत्वों को संक्रमण धातु के रूप में भी जाना जाता है और ये आवर्त सारणी के समूह 3 से 12 में स्थित हैं। d-ब्लॉक तत्वों के उदाहरण: स्कैंडियम (Sc), टाइटेनियम (Ti), वैनेडियम (V), क्रोमियम (Cr), मैंगनीज (Mn) और आयरन (Fe)। आंतरिक संक्रमण तत्व - यह आवर्त सारणी के f-ब्लॉक में तत्वों को संदर्भित करता है। इनमें लैंथेनाइड्स और एक्टिनाइड्स शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Raja Reddy is associated with which of the following dance forms?</p>",
                    question_hi: "<p>42. राजा रेड्डी निम्न में से किस नृत्य शैली से संबंधित हैं?</p>",
                    options_en: [
                        "<p>Bharatanatyam</p>",
                        "<p>Kuchipudi</p>",
                        "<p>Kathakali</p>",
                        "<p>Kathak</p>"
                    ],
                    options_hi: [
                        "<p>भरतनाट्यम</p>",
                        "<p>कुचिपुड़ी</p>",
                        "<p>कथकली</p>",
                        "<p>कथक</p>"
                    ],
                    solution_en: "<p>42.(b) <strong>Kuchipudi </strong>(Andhra Pradesh). It is a dance form in the worship of Hindu God Krishna. Raja and Radha Reddy are a legendary Kuchipudi dancing couple. Awards of Raja Reddy :- Padma Shri (1984), Padma Bhushan (2000), and Sangeet Natak Akademi (1991). Other famous Exponents : Yamini Reddy, Kaushalya Reddy, Bhavana Reddy, Lakshmi Narayn Shastri, and Swapana Sundari. </p>",
                    solution_hi: "<p>42.(b) <strong>कुचिपुड़ी</strong> (आंध्र प्रदेश)। यह हिंदू भगवान कृष्ण की पूजा में एक नृत्य रूप है। राजा और राधा रेड्डी एक प्रसिद्ध कुचिपुड़ी नृत्य जोड़ी हैं। राजा रेड्डी के पुरस्कार:- पद्म श्री (1984), पद्म भूषण (2000), और संगीत नाटक अकादमी (1991)। अन्य प्रसिद्ध प्रतिपादक: यामिनी रेड्डी, कौशल्या रेड्डी, भावना रेड्डी, लक्ष्मी नारायण शास्त्री, और स्वप्न सुंदरी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. A person having stunted growth and discoloured hair is most probably suffering from _____________.</p>",
                    question_hi: "<p>43. जिस व्यक्ति का विकास अवरुद्ध हो गया हो और बालों का रंग फीका पड़ गया हो, वह संभवतः ________ से पीड़ित हो सकता है।</p>",
                    options_en: [
                        "<p>Protein Deficiency</p>",
                        "<p>Lipid Deficiency</p>",
                        "<p>Glucose Deficiency</p>",
                        "<p>Vitamin A Deficiency</p>"
                    ],
                    options_hi: [
                        "<p>प्रोटीन की कमी</p>",
                        "<p>वसा (लिपिड) की कमी</p>",
                        "<p>ग्लूकोज की कमी</p>",
                        "<p>विटामिन A की कमी</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>Protein deficiency</strong> can cause stunted growth, muscle loss, and weakened immunity. Lipid deficiency leads to dry skin, hair loss, and hormonal issues. Glucose deficiency (hypoglycemia) results in dizziness, weakness, and fatigue. Vitamin A deficiency causes night blindness, dry skin, and increased infections.</p>",
                    solution_hi: "<p>43.(a) <strong>प्रोटीन की कमी</strong> से विकास में अवरुद्ध, मांसपेशियों का नुकसान और रोग प्रतिकारक क्षमता में कमज़ोरी हो सकती है। लिपिड की कमी से त्वचा शुष्क हो जाती है, बाल झड़ते हैं और हार्मोनल समस्याएँ होती हैं। ग्लूकोज की कमी (हाइपोग्लाइसीमिया) के कारण चक्कर आना, कमज़ोरी और थकान होती है। विटामिन-A की कमी से रतौंधी, शुष्क त्वचा और संक्रमण में वृद्धि होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Identify the artist who is NOT associated with the musical instrument sarangi.</p>",
                    question_hi: "<p>44. उस कलाकार की पहचान कीजिए, जो वाद्य यंत्र सारंगी से संबंधित नहीं है।</p>",
                    options_en: [
                        "<p>Pandit Ram Narayan</p>",
                        "<p>Ustad Binda Khan</p>",
                        "<p>Pandit Vishwa Mohan Bhatt</p>",
                        "<p>Shakoor Khan</p>"
                    ],
                    options_hi: [
                        "<p>पंडित राम नारायण</p>",
                        "<p>उस्ताद बिंदा खान</p>",
                        "<p>पंडित विश्व मोहन भट्ट</p>",
                        "<p>शकूर खान</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Pandit Vishwa Mohan Bhatt </strong>(inventor of Mohan Veena). His Awards: Grammy Award (1994), Padma Shri (2002), Padma Bhushan (2017). Musical Instruments and associated persons: Saarangi - Shakoor Khan, Ramesh Mishra, Sultan Khan. Vocalist (Carnatic Music) - MS Subbulakshami, Radhakrishna Srinivasa Iyer. Rudra Veena - Asad Ali Khan.</p>",
                    solution_hi: "<p>44.(c) <strong>पडित विश्व मोहन भट्ट</strong> (मोहन वीणा के आविष्कारक) ।उनके पुरस्कार: ग्रैमी अवार्ड (1993), पद्म श्री (2002), पद्म भूषण (2017)। संगीत वाद्ययंत्र और संबंधित व्यक्ति: सारंगी - शकूर खान, रमेश मिश्रा, सुल्तान खान। गायक (कर्नाटक संगीत) - एमएस सुब्बुलक्ष्मी, राधाकृष्ण श्रीनिवास अय्यर। रुद्र वीणा - असद अली खान।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. To get relief from constipation, which of the following is prescribed to be eaten in large amounts?</p>",
                    question_hi: "<p>45. कब्ज (constipation) से राहत पाने के लिए निम्नलिखित में से किस चीज का अधिक मात्रा में सेवन करने की सलाह दी जाती है?</p>",
                    options_en: [
                        "<p>Protein</p>",
                        "<p>Meat</p>",
                        "<p>Fish</p>",
                        "<p>Fibre</p>"
                    ],
                    options_hi: [
                        "<p>प्रोटीन (Protein)</p>",
                        "<p>मांस (Meat)</p>",
                        "<p>मछली (Fish)</p>",
                        "<p>फाइबर (Fibre)</p>"
                    ],
                    solution_en: "<p>45.(d) <strong>Fibre</strong> is essential for maintaining healthy digestion. It adds bulk to the stool and helps it pass more easily through the intestines, thus providing relief from constipation. Foods high in fibre include fruits, vegetables, whole grains, and legumes.</p>",
                    solution_hi: "<p>45.(d) <strong>फाइबर (Fibre)</strong> स्वस्थ पाचन को बनाए रखने के लिए आवश्यक है। यह मल को भारी बनाता है और आंतों से इसे आसानी से गुजरने में मदद करता है, जिससे कब्ज से राहत मिलती है। फाइबर से भरपूर खाद्य पदार्थों में फल, सब्जियाँ, साबुत अनाज और फलियाँ शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who did Karnataka defeat to win their fifth Vijay Hazare Trophy title in the 2024-25 season?</p>",
                    question_hi: "<p>46.कर्नाटक ने 2024-25 सीजन में अपनी पांचवीं विजय हजारे ट्रॉफी जीतने के लिए किस टीम को हराया?</p>",
                    options_en: [
                        "<p>Tamil Nadu</p>",
                        "<p>Vidarbha</p>",
                        "<p>Saurashtra</p>",
                        "<p>Mumbai</p>"
                    ],
                    options_hi: [
                        "<p>तमिलनाडु</p>",
                        "<p>विदर्भ</p>",
                        "<p>सौराष्ट्र</p>",
                        "<p>मुंबई</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>Vidarbha.</strong> Karnataka clinched their fifth Vijay Hazare Trophy title by defeating Vidarbha in the 2024-25 season final held on January 19, 2025, at the Kotambi Stadium, Vadodara. Karnataka secured a 36-run victory over Vidarbha.</p>",
                    solution_hi: "<p>46.(b)<strong> विदर्भ </strong>। कर्नाटक ने 2024-25 सीजन के फाइनल में विदर्भ को हराकर अपनी पांचवीं विजय हजारे ट्रॉफी का खिताब जीता। यह फाइनल 19 जनवरी 2025 को कोटंबी स्टेडियम, वडोदरा में आयोजित हुआ। कर्नाटक ने विदर्भ पर 36 रनों से जीत दर्ज की।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. \'Lone Fox Dancing\' is an autobiography by which of the following novelists?</p>",
                    question_hi: "<p>47. \'लोन फॉक्स डांसिंग\' निम्नलिखित में से किस उपन्यासकार की आत्मकथा है?</p>",
                    options_en: [
                        "<p>Arvind Adiga</p>",
                        "<p>Amitav Ghosh</p>",
                        "<p>Ruskin Bond</p>",
                        "<p>Khushwant Singh</p>"
                    ],
                    options_hi: [
                        "<p>अरविंद अडिग</p>",
                        "<p>अमिताभ घोष</p>",
                        "<p>रस्किन बॉन्&zwj;ड</p>",
                        "<p>खुशवंत सिंह</p>"
                    ],
                    solution_en: "<p>47.(c) <strong>Ruskin Bond.</strong> His other books: &ldquo;The Blue Umbrella&rdquo;, &ldquo;Delhi is not far&rdquo;, &ldquo;The Room on the Roof&rdquo;, \"The Girl on the Train&rdquo;. Other famous Authors and Books: Aravind Adiga - &ldquo;The White Tiger&rdquo; and &ldquo;Selection Day&rdquo;. Amitav Ghosh - &ldquo;Sea of Poppies&rdquo; and &ldquo;River of Smoke&rdquo;. Khushwant Singh - &ldquo;Train to Pakistan&rdquo; and &ldquo;The company of women&rdquo;.</p>",
                    solution_hi: "<p>47.(c) <strong>रस्किन बांड।</strong> उनकी अन्य पुस्तकें: \"द ब्लू अम्ब्रेला\", \"डेल्ही इज नॉट फार\", \"द रूम ऑन द रूफ\", \"द गर्ल ऑन द ट्रेन\"। अन्य प्रसिद्ध लेखक एवं पुस्तकें: अरविंद अडिगा - \"द व्हाइट टाइगर\" और \"सिलेक्शन डे\" । अमिताव घोष - \"सी ऑफ़ पॉपीज़\" और \"रिवर ऑफ़ स्मोक\"। खुशवंत सिंह - \"ट्रेन टू पाकिस्तान\" और \"द कंपनी ऑफ़ वीमेन\"।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Who assumed the role of Chief Financial Officer (CFO) of Apple on January 1, 2025?</p>",
                    question_hi: "<p>48. 1 जनवरी, 2025 को Apple के मुख्य वित्तीय अधिकारी (CFO) की भूमिका किसने संभाली?</p>",
                    options_en: [
                        "<p>Tim Cook</p>",
                        "<p>Kevan Parekh</p>",
                        "<p>Luca Maestri</p>",
                        "<p>John Sculley</p>"
                    ],
                    options_hi: [
                        "<p>टिम कुक</p>",
                        "<p>केवन पारेख</p>",
                        "<p>लुका मेस्त्री</p>",
                        "<p>जॉन स्कली</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>Kevan Parekh,</strong> a first-generation Indian-origin American, has led global financial teams for over 20 years. He joined Apple in June 2013 and has held roles such as Vice President of Financial Planning and Analysis and Vice President of Finance for Sales, Marketing, and Retail. Before joining Apple, Parekh held senior positions at Thomson Reuters and General Motors. Apple was established in 1976 in Los Altos, California. Steve Jobs, Steve Wozniak, and Ronald Wayne founded the company as a partnership.</p>",
                    solution_hi: "<p>48.(b) <strong>केवन पारेख,</strong> पहली पीढ़ी के भारतीय मूल के अमेरिकी हैं, जिन्होंने 20 से अधिक वर्षों तक वैश्विक वित्तीय टीमों का नेतृत्व किया है। वे जून 2013 में Apple में शामिल हुए और उन्होंने वित्तीय योजना और विश्लेषण के उपाध्यक्ष और बिक्री, विपणन और खुदरा के लिए वित्त के उपाध्यक्ष जैसे पद संभाले। Apple में शामिल होने से पहले, पारेख ने थॉमसन रॉयटर्स और जनरल मोटर्स में वरिष्ठ पदों पर कार्य किया। एप्पल की स्थापना 1976 में लॉस अल्टोस, कैलिफोर्निया में हुई थी। स्टीव जॉब्स, स्टीव वोज़नियाक और रोनाल्ड वेन ने साझेदारी के तौर पर इस कंपनी की स्थापना की थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Amaravati Stupa is located in which of these states?</p>",
                    question_hi: "<p>49. अमरावती स्तूप निम्न में से किस राज्य में स्थित है ?</p>",
                    options_en: [
                        "<p>West Bengal</p>",
                        "<p>Andhra Pradesh</p>",
                        "<p>Gujarat</p>",
                        "<p>Madhya Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिम बंगाल</p>",
                        "<p>आंध्र प्रदेश</p>",
                        "<p>गुजरात</p>",
                        "<p>मध्य प्रदेश</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>Andhra Pradesh.</strong> Amaravati Stupa: A ruined Buddhist stupa, it was built by Ashoka. The site is under the protection of the Archaeological Survey of India. It has a brick built circular Vedika or drum with projecting rectangular Ayaka platforms in four cardinal directions measuring 7.20 X 2.10 meters each.</p>",
                    solution_hi: "<p>49.(b) <strong>आंध्र प्रदेश। </strong>अमरावती स्तूप: एक खंडहर बौद्ध स्तूप, इसे अशोक ने बनवाया था। यह स्थल भारतीय पुरातत्व सर्वेक्षण के संरक्षण में है। इसमें ईंट से निर्मित गोलाकार वेदिका या ड्रम है जिसमें चार प्रमुख दिशाओं में 7.20 X 2.10 मीटर मापन वाले आयताकार अयाका मंच हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. When is World Statistics Day celebrated?</p>",
                    question_hi: "<p>50. विश्व सांख्यिकी दिवस कब मनाया जाता है?</p>",
                    options_en: [
                        "<p>15th October</p>",
                        "<p>20th October</p>",
                        "<p>25th October</p>",
                        "<p>30th October</p>"
                    ],
                    options_hi: [
                        "<p>15 अक्टूबर</p>",
                        "<p>20 अक्टूबर</p>",
                        "<p>25 अक्टूबर</p>",
                        "<p>30 अक्टूबर</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>20th October.</strong> World Statistics Day is observed every five years, and the theme \"Harnessing Data for Better Decisions\" underscores the critical role of accurate, timely, and reliable data in decision-making processes.</p>",
                    solution_hi: "<p>50.(b)<strong> 20 अक्टूबर</strong>। विश्व सांख्यिकी दिवस हर पाँच साल में मनाया जाता है, और इसका थीम \"बेहतर निर्णयों के लिए डेटा का उपयोग करना\" है, जो निर्णय लेने की प्रक्रियाओं में सटीक, समय पर और विश्वसनीय डेटा की महत्वपूर्ण भूमिका को रेखांकित करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The greatest value of sin<sup>4</sup>&theta; + cos<sup>4</sup>&theta; is:</p>",
                    question_hi: "<p>51. sin<sup>4</sup>&theta; + cos<sup>4</sup>&theta; का अधिकतम मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>3</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>51.(b)<br>sin<sup>4</sup>&theta; + cos<sup>4</sup>&theta;<br>(sin<sup>2</sup>&theta; + Cos<sup>2</sup>&theta;)<sup>2</sup> - 2Sin<sup>2</sup>&theta; Cos<sup>2</sup>&theta;<br>1 - 2sin<sup>2</sup>&theta; Cos<sup>2</sup>&theta;<br>As 2sin<sup>2</sup>&theta; Cos<sup>2</sup>&theta; &ge; 0 <br>hence, sin<sup>4</sup>&theta; + Cos<sup>4</sup>&theta; &le; 1</p>",
                    solution_hi: "<p>51.(b)<br>sin<sup>4</sup>&theta; + cos<sup>4</sup>&theta;<br>(sin<sup>2</sup>&theta; + Cos<sup>2</sup>&theta;)<sup>2</sup> - 2Sin<sup>2</sup>&theta; Cos<sup>2</sup>&theta;<br>1 - 2sin<sup>2</sup>&theta; Cos<sup>2</sup>&theta;<br>जैसे 2sin<sup>2</sup>&theta; Cos<sup>2</sup>&theta; &ge; 0<br>इसलिए, sin<sup>4</sup>&theta; + Cos<sup>4</sup>&theta; &le; 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Difference of (smallest common multiple of 4, 5 and 6) and (smallest common multiple of 5, 6 and 9) is</p>",
                    question_hi: "<p>52. (4, 5 और 6 का सबसे छोटा सार्व गुणज) और (5,6 और 9 का सबसे छोटा सार्व गुणज) का अंतर है</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>45</p>",
                        "<p>48</p>",
                        "<p>60</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>45</p>",
                        "<p>48</p>",
                        "<p>60</p>"
                    ],
                    solution_en: "<p>52.(a) smallest common multiple of 4, 5 and 6 = 60<br>smallest common multiple of 5, 6 and 9 = 90<br>Difference = 90 - 60 = 30.</p>",
                    solution_hi: "<p>52.(a) 4, 5 और 6 का सबसे छोटा सार्व गुणज = 60<br>5, 6 और 9 का सबसे छोटा सार्व गुणज = 90<br>अंतर = 90 - 60 = 30.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Chords AB and CD of a circle, when produced, meet at a point P outside the circle. If AB = 6 cm, CD = 3 cm and PD = 5 cm, then PB is equal to:</p>",
                    question_hi: "<p>53. एक वृत्त की जीवाएँ AB और CD, जब बढ़ाई जाती हैं, तो वृत्त के बाहर एक बिंदु P पर मिलती हैं। यदि AB = 6 cm, CD = 3 cm और PD = 5 cm है, तो PB किसके बराबर है?</p>",
                    options_en: [
                        "<p>9 cm</p>",
                        "<p>4 cm</p>",
                        "<p>8 cm</p>",
                        "<p>6 cm</p>"
                    ],
                    options_hi: [
                        "<p>9 cm</p>",
                        "<p>4 cm</p>",
                        "<p>8 cm</p>",
                        "<p>6 cm</p>"
                    ],
                    solution_en: "<p>53.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756091076.png\" alt=\"rId41\" width=\"219\" height=\"138\"><br>Using intersecting secant theorem ,<br>PB &times; PA = PD &times; PC<br>x(x + 6) = 5 &times; 8<br>x<sup>2 </sup>+ 6x = 40<br>x<sup>2</sup> + 6x - 40 = 0<br>x<sup>2</sup> + 10x - 4x - 40 = 0<br>x(x + 10) - 4(x + 10) = 0<br>(x&nbsp;+ 10)(x - 4) = 0<br>x = 4 cm</p>",
                    solution_hi: "<p>53.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756091076.png\" alt=\"rId41\" width=\"215\" height=\"135\"><br>प्रतिच्छेदी सेकेंट प्रमेय का उपयोग करना,<br>PB &times; PA = PD &times; PC<br>x(x + 6) = 5 &times; 8<br>x<sup>2 </sup>+ 6x = 40<br>x<sup>2</sup> + 6x - 40 = 0<br>x<sup>2</sup> + 10x - 4x - 40 = 0<br>x(x + 10) - 4(x + 10) = 0<br>(x&nbsp;+ 10)(x - 4) = 0<br>x = 4 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. In a 2 km linear race, if P completes the race in 200 second and Q in 220 seconds, then the distance by which P beats Q is:</p>",
                    question_hi: "<p>54. 2 km की रैखिक दौड़ में, यदि P, 200 सेकंड में और Q 220 सेकंड में दौड़ पूरी करता है, तो P, Q को कितनी दूरी से हराता है?</p>",
                    options_en: [
                        "<p>181<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> metres</p>",
                        "<p>167<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> metres</p>",
                        "<p>191<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> metres</p>",
                        "<p>173<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> metres</p>"
                    ],
                    options_hi: [
                        "<p>181<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> मीटर</p>",
                        "<p>167<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> मीटर</p>",
                        "<p>191<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> मीटर</p>",
                        "<p>173<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> मीटर</p>"
                    ],
                    solution_en: "<p>54.(a)<br>Distance covered by Q in 200 seconds = <math display=\"inline\"><mfrac><mrow><mn>2000</mn></mrow><mrow><mn>220</mn></mrow></mfrac></math> &times; 200 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20000</mn><mn>11</mn></mfrac></math> metres <br>Hence, desired distance = 2000 - <math display=\"inline\"><mfrac><mrow><mn>20000</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2000</mn><mn>11</mn></mfrac></math> = 181<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>11</mn></mfrac></math> metres</p>",
                    solution_hi: "<p>54.(a)<br>Q द्वारा 200 सेकंड में तय की गई दूरी = <math display=\"inline\"><mfrac><mrow><mn>2000</mn></mrow><mrow><mn>220</mn></mrow></mfrac></math> &times; 200 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20000</mn><mn>11</mn></mfrac></math>&nbsp;मीटर<br>अत: वांछित दूरी = 2000 - <math display=\"inline\"><mfrac><mrow><mn>20000</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2000</mn><mn>11</mn></mfrac></math> = 181<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>11</mn></mfrac></math>&nbsp;मीटर</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. My younger brother is flying a kite with an area of 15 cm&sup2; and the sum of its diagonals is 11 cm. Find the lengths of its diagonals in cm.</p>",
                    question_hi: "<p>55. मेरा छोटा भाई एक पतंग उड़ा रहा है जिसका क्षेत्रफल 15 cm<sup>2</sup> है और इसके विकर्णो का योगफल 11 cm है । इसके विकर्णो की लम्बाई cm में ज्ञात कीजिए ।</p>",
                    options_en: [
                        "<p>5.25, 5.75</p>",
                        "<p>5.5, 5.5</p>",
                        "<p>6, 5</p>",
                        "<p>7, 4</p>"
                    ],
                    options_hi: [
                        "<p>5.25, 5.75</p>",
                        "<p>5.5, 5.5</p>",
                        "<p>6, 5</p>",
                        "<p>7, 4</p>"
                    ],
                    solution_en: "<p>55.(c)<br>Let diagonals of kite be x&nbsp;and 11 - x<br>We know, Area of kite = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; d<sub>1</sub> &times; d<sub>2</sub><br>&rArr; 15 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; x &times; (11 - x)<br>&rArr; 15 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; x &times; (11 - x)<br>x<sup>2</sup> - 11x + 30 = 0<br>x(x - 6) - 5(x - 6) = 0<br>(x&nbsp;- 6)(x - 5) = 0<br>x = 5 , 6<br>Hence, the diagonal of kites are 5cm and 6cm.</p>",
                    solution_hi: "<p>55.(c)<br>माना , पतंग के विकर्ण क्रमश : x और (11 - x) है । <br>हम जानते हैं, पतंग का क्षेत्र = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; d<sub>1</sub> &times; d<sub>2</sub><br>&rArr; 15 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; x &times; (11 - x)<br>&rArr; 15 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; x &times; (11 - x)<br>x<sup>2</sup> - 11x + 30 = 0<br>x(x - 6) - 5(x - 6) = 0<br>(x&nbsp;- 6)(x - 5) = 0<br>x = 5 , 6<br>इसलिए, पतंग के विकर्ण 5 सेमी और 6 सेमी है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A die is cast twice and a coin is tossed thrice. Find the probability that the die will&nbsp;turn a 5 each time and the coin will turn a head every time.</p>",
                    question_hi: "<p>56. एक पासे को दो बार फेंका जाता है और एक सिक्के को तीन बार उछाला जाता है। पासे पर प्रत्येक बार 5 आने और सिक्के पर प्रत्येक बार चित आने की प्रायिकता ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>288</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>288</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>56.(c) Probability of getting 5 on rolling dice once = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math><br>Probability of getting 5 on rolling dice Twice = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>36</mn></mfrac></math><br>Probability of getting head on tossing a coin once = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>Probability of getting head on tossing a coin thrice = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>Combined probability of getting 5 and head = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>288</mn></mfrac></math></p>",
                    solution_hi: "<p>56.(c) एक बार पासा उछालने पर 5 आने की प्रायिकता = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math><br>दो बार पासा उछालने पर 5 आने की प्रायिकता = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>36</mn></mfrac></math><br>एक बार सिक्का उछालने पर चित आने की प्रायिकता = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>एक सिक्के को तीन बार उछालने पर चित आने की प्रायिकता = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>5 और चित आने की संयुक्त प्रायिकता = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>288</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The graphs of two linear equations, x + 2y = 15 and 4x + 8y = 13 will be:</p>",
                    question_hi: "<p>57. दो रैखिक समीकरण, x + 2y = 15 और 4x + 8y = 13 के ग्राफ ________ होंगे।</p>",
                    options_en: [
                        "<p>intersecting at one point</p>",
                        "<p>parallel</p>",
                        "<p>intersecting at two points</p>",
                        "<p>coincident</p>"
                    ],
                    options_hi: [
                        "<p>एक बिंदुपर प्रतिच्छेदित</p>",
                        "<p>समांतर</p>",
                        "<p>दो बिंदुओं पर प्रतिच्छेदित</p>",
                        "<p>संपाती</p>"
                    ],
                    solution_en: "<p>57.(b) <br>x + 2y = 15 <br>y = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>2</mn></mfrac></math> ------(i)<br>Equation of slope, y = mx + c<br>Comparing with the equation (i)<br>Slope (m) = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>Now, 4x + 8y = 13<br>y = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>4</mn></mfrac></math> ------(ii)<br>Equation of slope, y = mx + c<br>Comparing with the equation (ii)<br>Slope (m) = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>Here, slope of both the equations are same <br>Hence, lines are parallel.</p>",
                    solution_hi: "<p>57.(b) <br>x + 2y = 15 <br>y = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>2</mn></mfrac></math> ------(i)<br>प्रवणता (slope) का समीकरण, y = mx + c<br>समीकरण (i) के साथ तुलना करने पर<br>प्रवणता (m) = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>अब, 4x + 8y = 13<br>y = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>4</mn></mfrac></math> ------(ii)<br>प्रवणता (slope) का समीकरण,y = mx + c<br>समीकरण (ii) के साथ तुलना करने पर<br>प्रवणता (m) = - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>यहां, दोनों समीकरणों की प्रवणता (slope) समान है <br>अत: रेखाएँ समान्तर हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. If cosec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>, then evaluate (sec<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (1 + cot<sup>2</sup>&theta;)</p>",
                    question_hi: "<p>58.यदि cosec&theta; = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> है , तो (sec<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (1 + cot<sup>2</sup>&theta;) का मान ज्ञात कीजिए</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>58.(c) <strong>Given :</strong> cosec&theta; = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>&rArr; (sec<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (1 + cot<sup>2</sup>&theta;)<br>We know,<br>sec<sup>2</sup>&theta; = 1 + tan<sup>2</sup>&theta;, cosec<sup>2</sup>&theta; = 1 + cot<sup>2</sup>&theta; and tan<sup>2</sup>&theta; &times; cot<sup>2</sup>&theta; = 1<br>Then, (1 + tan<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (cosec<sup>2</sup>&theta; )<br>= tan<sup>2</sup>&theta; &times; cot<sup>2</sup>&theta; &times; cosec<sup>2</sup>&theta;<br>= cosec<sup>2</sup>&theta; = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>9</mn></mfrac></math></p>",
                    solution_hi: "<p>58.(c)<strong> दिया है :</strong> cosec&theta; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>&rArr; (sec<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (1 + cot<sup>2</sup>&theta;)<br>हम जानते हैं,<br>sec<sup>2</sup>&theta; = 1 + tan<sup>2</sup>&theta;, cosec<sup>2</sup>&theta; = 1 + cot<sup>2</sup>&theta; and tan<sup>2</sup>&theta; &times; cot<sup>2</sup>&theta; = 1<br>Then, (1 + tan<sup>2</sup>&theta; - 1) &times; cot<sup>2</sup>&theta; &times; (cosec<sup>2</sup>&theta; )<br>= tan<sup>2</sup>&theta; &times; cot<sup>2</sup>&theta; &times; cosec<sup>2</sup>&theta;<br>= cosec<sup>2</sup>&theta; = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>9</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Study the given table and answer the question that follows.<br>The following table shows the production of printers by four plants of a company over the five years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756091348.png\" alt=\"rId42\" width=\"259\" height=\"197\"> <br>What is the difference between the average production of plant 1 and plant 2 during five years?</p>",
                    question_hi: "<p>59. दी गई तालिका का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>निम्नलिखित तालिका पाँच वर्षों में एक कंपनी के चार संयंत्रों द्वारा प्रिंटरों के उत्पादन को दर्शाती है।</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756091518.png\" alt=\"rId43\" width=\"261\" height=\"198\"> <br>पाँच वर्षों के दौरान संयंत्र 1 और संयंत्र 2 के औसत उत्पादन के बीच क्या अंतर है?</p>",
                    options_en: [
                        "<p>1600</p>",
                        "<p>3000</p>",
                        "<p>3840</p>",
                        "<p>1400</p>"
                    ],
                    options_hi: [
                        "<p>1600</p>",
                        "<p>3000</p>",
                        "<p>3840</p>",
                        "<p>1400</p>"
                    ],
                    solution_en: "<p>59.(a) Average production of plant 1 of 5 years <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>43</mn><mo>+</mo><mn>48</mn><mo>+</mo><mn>32</mn><mo>+</mo><mn>42</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>5</mn></mfrac></math> = 40<br>Average production of plant 2 of 5 years <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>44</mn><mo>+</mo><mn>39</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>44</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>192</mn><mn>5</mn></mfrac></math> = 38.4<br>Required difference = 40 - 38.4 = 1.6 &times; 1000 = 1600</p>",
                    solution_hi: "<p>59.(a) 5 वर्षों में से संयंत्र 1 का औसत उत्पादन <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>43</mn><mo>+</mo><mn>48</mn><mo>+</mo><mn>32</mn><mo>+</mo><mn>42</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mn>5</mn></mfrac></math> = 40<br>5 वर्षों में संयंत्र 2 का औसत उत्पादन <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>44</mn><mo>+</mo><mn>39</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>44</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>192</mn><mn>5</mn></mfrac></math> = 38.4<br>आवश्यक अंतर = 40 - 38.4 = 1.6 &times; 1000 = 1600</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A tree broke at a height of 8m from its foot and the broken upper part touches the ground at a point of 6 m from its foot. Find the height of the tree?</p>",
                    question_hi: "<p>60. एक पेड़ अपने पैर से 8 मीटर की ऊंचाई पर टूट गया और टूटा हुआ ऊपरी हिस्सा पैर से 6 मीटर की दूरी पर जमीन को छूता है। पेड़ की ऊंचाई ज्ञात कीजिये ?</p>",
                    options_en: [
                        "<p>15 m</p>",
                        "<p>16 m</p>",
                        "<p>18 m</p>",
                        "<p>24 m</p>"
                    ],
                    options_hi: [
                        "<p>15 m</p>",
                        "<p>16 m</p>",
                        "<p>18 m</p>",
                        "<p>24 m</p>"
                    ],
                    solution_en: "<p>60.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756091617.png\" alt=\"rId44\" width=\"101\" height=\"161\"><br>AB = 8<br>BC = 6<br>By Pythagoras Theorem<br>CA<sup>2</sup> = AB<sup>2</sup> + BC<sup>2</sup><br>CA<sup>2</sup> = 6<sup>2</sup> + 8<sup>2</sup><br>CA = 10<br>So, Total length of the tree = AB + AC = 8 + 10 = 18 m</p>",
                    solution_hi: "<p>60.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756091617.png\" alt=\"rId44\" width=\"84\" height=\"134\"><br>AB = 8<br>BC = 6<br>पाइथागोरस प्रमेय द्वारा<br>CA<sup>2</sup> = AB<sup>2</sup> + BC<sup>2</sup><br>CA<sup>2</sup> = 6<sup>2</sup> + 8<sup>2</sup><br>CA = 10<br>तो, पेड़ की कुल लंबाई = AB + AC = 8 + 10 = 18 m</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The bar graph shows the number of students present in a class on four days. The average number of students present on Monday, Tuesday and Wednesday is:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756091787.png\" alt=\"rId45\" width=\"252\" height=\"202\"></p>",
                    question_hi: "<p>61. दंड आलेख चार दिनों में एक कक्षा में उपस्थित छात्रों की संख्या दर्शाता है। सोमवार, मंगलवार और बुधवार को उपस्थित छात्रों की औसत संख्या ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756091889.png\" alt=\"rId46\" width=\"271\" height=\"217\"></p>",
                    options_en: [
                        "<p>24</p>",
                        "<p>20</p>",
                        "<p>30</p>",
                        "<p>25</p>"
                    ],
                    options_hi: [
                        "<p>24</p>",
                        "<p>20</p>",
                        "<p>30</p>",
                        "<p>25</p>"
                    ],
                    solution_en: "<p>61.(d) Average of Monday, Tuesday and Wednesday <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>30</mn><mo>+</mo><mn>20</mn></mrow><mn>3</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>3</mn></mfrac></math> = 25</p>",
                    solution_hi: "<p>61.(d) सोमवार, मंगलवार और बुधवार का औसत<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>30</mn><mo>+</mo><mn>20</mn></mrow><mn>3</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>3</mn></mfrac></math> = 25</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The average monthly expenditure of a family was ₹8,600 during the first three months, ₹9,750 during the next four months and ₹9,920 during the last five months of a year. If the total savings during the year was ₹65,300, the average monthly income (in ₹) of the family was:</p>",
                    question_hi: "<p>62. एक परिवार का वर्ष के पहले तीन महीनों के दौरान औसत मासिक व्यय ₹8,600 था, अगले चार महीनों के दौरान ₹9,750 था और आखिरी पांच महीनों के दौरान ₹9,920 था। यदि वर्ष के दौरान कुल बचत ₹65,300 थी, तो परिवार की औसत मासिक आय (₹ में) कितनी थी?</p>",
                    options_en: [
                        "<p>14,975</p>",
                        "<p>15,794</p>",
                        "<p>15,479</p>",
                        "<p>14,597</p>"
                    ],
                    options_hi: [
                        "<p>14,975</p>",
                        "<p>15,794</p>",
                        "<p>15,479</p>",
                        "<p>14,597</p>"
                    ],
                    solution_en: "<p>62.(a)<br>Yearly expenditure of family = 8600 &times; 3 + 9750 &times; 4 + 9920 &times; 5 = ₹1,14,400<br>Yearly savings of family = ₹65,300<br>Income = Expenditure + Savings<br>Yearly Income of family = 1,14,400 + 65,300 = ₹1,79,700<br>Hence, average monthly income = <math display=\"inline\"><mfrac><mrow><mn>179700</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = ₹14,975</p>",
                    solution_hi: "<p>62.(a)<br>परिवार का वार्षिक खर्च = 8600 &times; 3 + 9750 &times; 4 + 9920 &times; 5 = ₹1,14,400<br>परिवार की वार्षिक बचत = ₹65,300<br>आय = व्यय + बचत<br>परिवार की वार्षिक आय = 1,14,400 + 65,300 = ₹1,79,700<br>अत:, औसत मासिक आय = <math display=\"inline\"><mfrac><mrow><mn>179700</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = ₹14,975</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. The average of the squares of the first 48 natural numbers is</p>",
                    question_hi: "<p>63. प्रथम 48 प्राकृतिक संख्याओं के वर्गों का औसत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>793.17</p>",
                        "<p>791.17</p>",
                        "<p>794.17</p>",
                        "<p>792.17</p>"
                    ],
                    options_hi: [
                        "<p>793.17</p>",
                        "<p>791.17</p>",
                        "<p>794.17</p>",
                        "<p>792.17</p>"
                    ],
                    solution_en: "<p>63.(d)<br>Sum of square of n&nbsp;natural number = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">n</mi><mo>(</mo><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>6</mn></mfrac></math><br>Sum of square of <math display=\"inline\"><mn>48</mn></math> natural number = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>&#215;</mo><mn>49</mn><mo>&#215;</mo><mn>97</mn></mrow><mn>6</mn></mfrac></math><br>Average of square of <math display=\"inline\"><mn>48</mn></math> natural number = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>&#215;</mo><mn>49</mn><mo>&#215;</mo><mn>97</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>48</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>49</mn><mo>&#215;</mo><mn>97</mn></mrow><mn>6</mn></mfrac></math> = 792.17</p>",
                    solution_hi: "<p>63.(d)<br>n प्राकृत संख्या के वर्ग का योग = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">n</mi><mo>(</mo><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>6</mn></mfrac></math><br>48 प्राकृत संख्या के वर्ग का योग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>&#215;</mo><mn>49</mn><mo>&#215;</mo><mn>97</mn></mrow><mn>6</mn></mfrac></math> <br>48 प्राकृत संख्या के वर्ग का औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>48</mn><mo>&#215;</mo><mn>49</mn><mo>&#215;</mo><mn>97</mn></mrow><mrow><mn>6</mn><mo>&#215;</mo><mn>48</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>49</mn><mo>&#215;</mo><mn>97</mn></mrow><mn>6</mn></mfrac></math> = 792.17</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A person borrowed some money at the rate of 8% per annum for the first two years, at&nbsp;the rate of 11% per annum for the next three years, and at the rate of 16% per annum for the period beyond five years. If he pays a total interest of ₹21,400 at the end of nine years, how much money did he borrow (round to the nearest unit) ?</p>",
                    question_hi: "<p>64. एक व्यक्ति ने पहले दो वर्षों के लिए 8% वार्षिक की दर से, अगले तीन वर्षों के लिए 11% वार्षिक की दर&nbsp;से और पांच वर्षों से अधिक की अवधि के लिए 16% वार्षिक की दर से कुछ धनराशि ऋण पर ली। यदि वह नौ वर्ष के अंत में ब्याज के रूप में कुल ₹21,400 का भुगतान करता है, तो उसने कितनी धनराशि ऋण पर ली थी (निकटतम इकाई तक पूर्णांकित)?</p>",
                    options_en: [
                        "<p>₹15,938</p>",
                        "<p>₹11,938</p>",
                        "<p>₹18,938</p>",
                        "<p>₹18,738</p>"
                    ],
                    options_hi: [
                        "<p>₹15,938</p>",
                        "<p>₹11,938</p>",
                        "<p>₹18,938</p>",
                        "<p>₹18,738</p>"
                    ],
                    solution_en: "<p>64.(c)<br>SI of 2 years = 2 &times; 8 = 16%<br>SI of next 3 years = 3 &times; 11 = 33%<br>SI of next 4 years = 4 &times; 16 = 64%<br>Total interest = 16% + 33% + 64% = 113%<br>113% = ₹ 21400<br>(borrowed money) 100% = <math display=\"inline\"><mfrac><mrow><mn>21400</mn></mrow><mrow><mn>113</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 = ₹ 18,938 (approx)</p>",
                    solution_hi: "<p>64.(c)<br>दो वर्ष का साधारण ब्याज = 2 &times; 8 = 16%<br>अगले तीन वर्ष का साधारण ब्याज = 3 &times; 11 = 33%<br>अगले चार वर्ष का साधारण ब्याज = 4 &times; 16 = 64%<br>कुल ब्याज = 16% + 33% + 64% = 113%<br>113% = ₹ 21400 <br>(उधार ली गई धनराशि ) 100% = <math display=\"inline\"><mfrac><mrow><mn>21400</mn></mrow><mrow><mn>113</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 = ₹ 18,938 (लगभग)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. The list price of a hand mixer at a showroom is ₹2,000 and it is being sold at successive discounts of 15% and 10%. What is its net selling price in rupees ?</p>",
                    question_hi: "<p>65. एक शोरूम में एक हैंड मिक्सर का अंकित मूल्य ₹2,000 है और इसे 15% और 10% की क्रमिक छूट पर बेचा जा रहा है। रुपए में इसका निवल विक्रय मूल्य क्या है?</p>",
                    options_en: [
                        "<p>1,530</p>",
                        "<p>1,560</p>",
                        "<p>1,440</p>",
                        "<p>1,600</p>"
                    ],
                    options_hi: [
                        "<p>1,530</p>",
                        "<p>1,560</p>",
                        "<p>1,440</p>",
                        "<p>1,600</p>"
                    ],
                    solution_en: "<p>65.(a) <br>Net selling price = 2000 &times; <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = ₹ 1530</p>",
                    solution_hi: "<p>65.(a) <br>निवल विक्रय मूल्य = 2000 &times; <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> = ₹ 1530</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A shopkeeper sold <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> of his articles at a gain of 20% and the remaining at the cost price. What is his gain percentage in the whole transaction?</p>",
                    question_hi: "<p>66. एक दुकानदार ने अपनी <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> वस्तुओं को 20% के लाभ पर और शेष को क्रय मूल्य पर बेचा। पूरे सौदे में उसका लाभ प्रतिशत कितना है?</p>",
                    options_en: [
                        "<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>",
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>",
                        "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>16<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>",
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>",
                        "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> %</p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>66.(d) Let the total no. of article be 8 <br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> article sold At the profit of 20% = (8 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>) &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 6 units<br>SP of 5 articles = 6 units<br>Remaining article sold at the cost price = 3 units<br>SP of 3 articles = 3 units<br>Total SP of the article = 9 units<br>Required profit = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>-</mo><mn>8</mn></mrow><mn>8</mn></mfrac></math> &times; 100 = 12<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    solution_hi: "<p>66.(d) माना वस्तुओं की कुल संख्या 8 हैं<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> वस्तुएँ 20% के लाभ पर बेची गईं = (8 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>) &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math> = 6 इकाई<br>5 वस्तुओं का विक्रय मूल्य = 6 इकाई<br>शेष वस्तु लागत मूल्य पर बेची गई = 3 इकाई<br>3 वस्तुओं का विक्रय मूल्य = 3 इकाई<br>कुल वस्तुओं का विक्रय मूल्य = 9 इकाई<br>आवश्यक लाभ = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>-</mo><mn>8</mn></mrow><mn>8</mn></mfrac></math> &times; 100 = 12<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A metallic sphere of volume 3136<math display=\"inline\"><mi>&#960;</mi></math> cm&sup3; is melted and then recast into smaller cones, each of radius 3.5 cm and height 4 cm. How many cones are obtained ?</p>",
                    question_hi: "<p>67. 3136<math display=\"inline\"><mi>&#960;</mi></math> cm&sup3; आयतन के एक धातु के गोले को पिघलाकर प्रत्येक 3.5 cm त्रिज्या और 4 cm ऊंचाई वाले छोटे-छोटे शंकुओं में ढाला जाता है। इससे कितने शंकु प्राप्त किए जा सकते हैं?</p>",
                    options_en: [
                        "<p>172</p>",
                        "<p>148</p>",
                        "<p>192</p>",
                        "<p>196</p>"
                    ],
                    options_hi: [
                        "<p>172</p>",
                        "<p>148</p>",
                        "<p>192</p>",
                        "<p>196</p>"
                    ],
                    solution_en: "<p>67.(c)<br>Volume of cone = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math><br>According to question,<br>Let the number of cones = n<br>3136<math display=\"inline\"><mi>&#960;</mi></math> = n &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;(3.5)<sup>2</sup>&times;4<br>n = <math display=\"inline\"><mfrac><mrow><mn>3136</mn><mi>&#960;</mi></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 192</p>",
                    solution_hi: "<p>67.(c)<br>शंकु का आयतन = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math><br>प्रश्न के अनुसार,<br>माना कि शंकुओ का संख्या = n<br>3136<math display=\"inline\"><mi>&#960;</mi></math> = n &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi;(3.5)<sup>2</sup>&times;4<br>n = <math display=\"inline\"><mfrac><mrow><mn>3136</mn><mi>&#960;</mi></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mo>(</mo><mn>3</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 192</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. In an election between two candidates, 10% of the voters did not cast their votes and 75 votes were found invalid. The winner got 50% of the total votes expected and won by 170 votes. How many voters were enrolled in the voters\' list?</p>",
                    question_hi: "<p>68. दो उम्मीदवारों के बीच एक चुनाव में 10% मतदाताओं ने वोट नहीं डाले और 75 वोट अमान्य पाए गए। विजेता को अपेक्षित कुल वोटों के 50% वोट मिले और वह 170 वोटों से जीत गया। मतदाता सूची में कितने मतदाताओं का नाम दर्ज है?</p>",
                    options_en: [
                        "<p>800</p>",
                        "<p>855</p>",
                        "<p>850</p>",
                        "<p>950</p>"
                    ],
                    options_hi: [
                        "<p>800</p>",
                        "<p>855</p>",
                        "<p>850</p>",
                        "<p>950</p>"
                    ],
                    solution_en: "<p>68.(d)<br>Let total votes = 100<math display=\"inline\"><mi>x</mi></math>%<br>Voters cast their vote = 90<math display=\"inline\"><mi>x</mi></math>%<br>Valid votes = 90<math display=\"inline\"><mi>x</mi></math>% - 75<br>Votes winner got = 50<math display=\"inline\"><mi>x</mi></math>%<br>Votes Loser got = (90<math display=\"inline\"><mi>x</mi></math>% - 75) - 50x% = (40x% - 75)<br>According to question,<br>50<math display=\"inline\"><mi>x</mi></math>% - (40x% - 75) = 170<br>(10<math display=\"inline\"><mi>x</mi></math>% + 75) = 170 votes<br>100% (vote cast) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>170</mn><mo>-</mo><mn>75</mn></mrow><mn>10</mn></mfrac></math> &times; 100 = 950</p>",
                    solution_hi: "<p>68.(d)<br>माना कुल वोट = 100<math display=\"inline\"><mi>x</mi></math>%<br>मतदाताओं ने अपना वोट डाला = 90<math display=\"inline\"><mi>x</mi></math>%<br>वैध वोट = 90<math display=\"inline\"><mi>x</mi></math>% - 75<br>विजेता को मिले वोट = 50<math display=\"inline\"><mi>x</mi></math>%<br>हारने वाले को मिले वोट = (90<math display=\"inline\"><mi>x</mi></math>% - 75) - 50x% = (40x% - 75)<br>प्रश्न के अनुसार,<br>50<math display=\"inline\"><mi>x</mi></math>% - (40x% - 75) = 170<br>(10<math display=\"inline\"><mi>x</mi></math>% + 75) = 170 वोट<br>100% (वोट पड़े) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>170</mn><mo>-</mo><mn>75</mn></mrow><mn>10</mn></mfrac></math> &times; 100 = 950</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. While travelling from A to B, Raghav travels a quarter of the distance at 10 km/h, the next quarter of the distance at 15 km/h, the third quarter of the distance at 20 km/h, and the final quarter of the distance at 30 km/h. While travelling on exactly the same route, Manish travels for a quarter of the total time taken for his journey at 10 km/h, the next quarter of the total time taken for his journey at 15 km/h, the third quarter of the total time taken for his journey at 20 km/h, and the final quarter of the total time taken for his journey at 30 km/h. If the overall average speed of Raghav during his journey is given as y km/h, and that of Manish is given as z km/h, find the value of (z &ndash; y).</p>",
                    question_hi: "<p>69. A से B तक की यात्रा करते समय राघव एक चौथाई दूरी 10 Km/h की चाल से तय करता है, अगली चौथाई दूरी 15 Km/h से, अगली चौथाई दूरी 20 Km/h से, और अंतिम चौथाई दूरी 30 Km/h की चाल से तय करता है। ठीक उसी मार्ग पर यात्रा करते समय मनीष अपनी यात्रा में लगने वाले कुल समय का एक चौथाई भाग 10 Km/h की चाल से यात्रा करता है, अगला चौथाई भाग 15 Km/h की चाल से, कुल समय का अगला चौथाई भाग 20 Km/h की चाल से और उसकी यात्रा हेतु लिये गए कुल समय का अंतिम चौथाई भाग 30 Km/h की चाल से पूरा करता है। यदि अपनी यात्रा के दौरान राघव की कुल औसत चाल y Km/h और मनीष की z Km/h के रूप में दी गई है, तो (z &ndash; y) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>1.5</p>",
                        "<p>2.75</p>",
                        "<p>2.5</p>",
                        "<p>0</p>"
                    ],
                    options_hi: [
                        "<p>1.5</p>",
                        "<p>2.75</p>",
                        "<p>2.5</p>",
                        "<p>0</p>"
                    ],
                    solution_en: "<p>69.(b)<br>Let the distance between A and B be 240 km<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756092065.png\" alt=\"rId47\" width=\"349\" height=\"35\"><br>Average speed of Raghav(y) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mfrac><mn>60</mn><mn>10</mn></mfrac><mo>+</mo><mfrac><mn>60</mn><mn>15</mn></mfrac><mo>+</mo><mfrac><mn>60</mn><mn>20</mn></mfrac><mo>+</mo><mfrac><mn>60</mn><mn>30</mn></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mn>6</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>15</mn></mfrac></math>= 16 km/hr<br>Let time taken by Manish to cover the journey be 16 hrs<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756092259.png\" alt=\"rId48\" width=\"349\" height=\"35\"><br>Distance travelled by Manish = (10+15+20+30)&times;4 = 300 km<br>Average speed of Manish(z) = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 18.75 km/hr<br>Then, z - y = 18.75 - 16 = 2.75 km/hr</p>",
                    solution_hi: "<p>69.(b)<br>माना A और B के बीच की दूरी 240 किमी है<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756092065.png\" alt=\"rId47\" width=\"349\" height=\"35\"><br>राघव की औसत गति (y) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mfrac><mn>60</mn><mn>10</mn></mfrac><mo>+</mo><mfrac><mn>60</mn><mn>15</mn></mfrac><mo>+</mo><mfrac><mn>60</mn><mn>20</mn></mfrac><mo>+</mo><mfrac><mn>60</mn><mn>30</mn></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mn>6</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>15</mn></mfrac></math>&nbsp;= 16 किमी/घंटा<br>माना कि मनीष को यात्रा तय करने में 16 घंटे का समय लगा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741756092259.png\" alt=\"rId48\" width=\"349\" height=\"35\"><br>मनीष द्वारा तय की गई दूरी = (10+15+20+30)&times;4 = 300 km<br>मनीष की औसत गति(z) = <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 18.75 किमी/घंटा<br>फिर, z - y = 18.75 - 16 = 2.75 किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Which digits should come in place * and $, respectively, if the number 72864*$ is divisible by both 8 and 5 ?</p>",
                    question_hi: "<p>70. यदि 72864*$ एक ऐसी संख्या है जो 8 और 5 दोनों से विभाज्य है, तो क्रमशः * और $ के स्थान पर कौन-से अंक आने चाहिए?</p>",
                    options_en: [
                        "<p>4 and 0</p>",
                        "<p>2 and 0</p>",
                        "<p>2 and 5</p>",
                        "<p>4 and 5</p>"
                    ],
                    options_hi: [
                        "<p>4 और 0</p>",
                        "<p>2 और 0</p>",
                        "<p>2 और 5</p>",
                        "<p>4 और 5</p>"
                    ],
                    solution_en: "<p>70.(a) <br>72864*$<br>Divisibility rule of 8: last three digits should be divisible by 8.<br>Divisibility rule of 5: last digit is either 0 or 5.<br>If last digit is 5:<br>Then 72864*5 is not divisible by 8.<br>If last digit is 0:<br>72864*0<br>The value of * can be 0, 4, 8.<br>By checking options, option (a) satisfied the given condition.</p>",
                    solution_hi: "<p>70.(a) <br>72864*$<br>8 की विभाज्यता नियम :- अंतिम तीन अंक 8 से विभाज्य होने चाहिए।<br>5 की विभाज्यता नियम: अंतिम अंक या तो 0 या 5 हो ।<br>यदि , अंतिम अंक = 5 :- तो 72864*5, 8 से विभाज्य नहीं है।<br>यदि , अंतिम अंक = 0 :- तो 72864*0 , * का मान 0, 4, 8 हो सकता है । <br>विकल्पों की जाँच करके, विकल्प (a) दी गई शर्त को पूरा करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Rajesh, in his printing press, got an order to print some books, out of which he completed <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> of the order in 34 days. In how many days did he complete the entire order of printing?</p>",
                    question_hi: "<p>71. राजेश को उसके प्रिंटिंग प्रेस में कुछ किताबें छापने का ऑर्डर मिला, जिसमें से उसने ऑर्डर के <math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> को 34 दिनों में पूरा किया। उसने छपाई का पूरा ऑर्डर कितने दिनों में पूरा किया ?</p>",
                    options_en: [
                        "<p>60</p>",
                        "<p>20</p>",
                        "<p>54</p>",
                        "<p>56</p>"
                    ],
                    options_hi: [
                        "<p>60</p>",
                        "<p>20</p>",
                        "<p>54</p>",
                        "<p>56</p>"
                    ],
                    solution_en: "<p>71.(c) According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> units = 34 days<br>1 units = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>34</mn><mo>&#215;</mo><mn>27</mn></mrow><mn>17</mn></mfrac></math> = 54 days</p>",
                    solution_hi: "<p>71.(c) प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> इकाइयाँ = 34 दिन<br>1 इकाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>34</mn><mo>&#215;</mo><mn>27</mn></mrow><mn>17</mn></mfrac></math>&nbsp;= 54 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A&rsquo;s income and expenditure are in the ratio 5 : 4. If his total savings are ₹56,000, what is his expenditure in ₹?</p>",
                    question_hi: "<p>72. A की आय और व्यय का अनुपात 5 : 4 है। यदि उसकी कुल बचत ₹56,000 है, तो उसका व्यय (₹ में) कितना है?</p>",
                    options_en: [
                        "<p>4,42,000</p>",
                        "<p>2,42,000</p>",
                        "<p>2,24,000</p>",
                        "<p>2,44,000</p>"
                    ],
                    options_hi: [
                        "<p>4,42,000</p>",
                        "<p>2,42,000</p>",
                        "<p>2,24,000</p>",
                        "<p>2,44,000</p>"
                    ],
                    solution_en: "<p>72.(c)<br>A <math display=\"inline\"><mo>&#8594;</mo></math> Income : Expenditure<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 4 <br>Savings = income - expenditure<br>= 5 - 4 = 1 unit<br>1 unit = ₹ 56000<br>Expenditure (4 unit) = 4 <math display=\"inline\"><mo>&#215;</mo></math> 56000 = ₹ 224000</p>",
                    solution_hi: "<p>72.(c)<br>A <math display=\"inline\"><mo>&#8594;</mo></math> आय : व्यय<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp;:&nbsp; 4 <br>बचत = आय - व्यय<br>= 5 - 4 = 1 इकाई<br>1 इकाई = ₹ 56000<br>व्यय (4 इकाई) = 4 <math display=\"inline\"><mo>&#215;</mo></math> 56000 = ₹ 224000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>256</mn><msup><mo>)</mo><mrow><mn>0</mn><mo>.</mo><mn>16</mn></mrow></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>256</mn><mo>)</mo></mrow><mrow><mn>0</mn><mo>.</mo><mn>09</mn></mrow></msup></math> is</p>",
                    question_hi: "<p>73. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>256</mn><msup><mo>)</mo><mrow><mn>0</mn><mo>.</mo><mn>16</mn></mrow></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>256</mn><mo>)</mo></mrow><mrow><mn>0</mn><mo>.</mo><mn>09</mn></mrow></msup></math>&nbsp;का मान क्या है ?</p>",
                    options_en: [
                        "<p>64</p>",
                        "<p>256</p>",
                        "<p>16</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>64</p>",
                        "<p>256</p>",
                        "<p>16</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>73.(d)<br><math display=\"inline\"><mo>(</mo><mn>256</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>0</mn><mo>.</mo><mn>16</mn></mrow></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>256</mn><mo>)</mo></mrow><mrow><mn>0</mn><mo>.</mo><mn>09</mn></mrow></msup></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mfrac><mn>16</mn><mn>100</mn></mfrac></msup></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mfrac><mn>9</mn><mn>100</mn></mfrac></msup></math>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mrow><mfrac><mn>16</mn><mn>100</mn></mfrac><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>9</mn><mn>100</mn></mfrac></mstyle></mrow></msup></math>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mfrac><mn>25</mn><mn>100</mn></mfrac></msup></math>= (2)<sup>2</sup> = 4</p>",
                    solution_hi: "<p>73.(d)<br><math display=\"inline\"><mo>(</mo><mn>256</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>0</mn><mo>.</mo><mn>16</mn></mrow></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>256</mn><mo>)</mo></mrow><mrow><mn>0</mn><mo>.</mo><mn>09</mn></mrow></msup></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mfrac><mn>16</mn><mn>100</mn></mfrac></msup></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mfrac><mn>9</mn><mn>100</mn></mfrac></msup></math>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mrow><mfrac><mn>16</mn><mn>100</mn></mfrac><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>9</mn><mn>100</mn></mfrac></mstyle></mrow></msup></math>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>8</mn></msup><mo>)</mo></mrow><mfrac><mn>25</mn><mn>100</mn></mfrac></msup></math>= (2)<sup>2</sup> = 4</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Find the quotient, when the mean proportional of 7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> and 245 is divided by an even prime number</p>",
                    question_hi: "<p>74. 245 और 7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> के माध्यानुपाती को एक सम अभाज्य संख्या से विभाजित किए जाने पर प्राप्त भागफल ज्ञात करें।</p>",
                    options_en: [
                        "<p>49</p>",
                        "<p>42</p>",
                        "<p>21</p>",
                        "<p>36</p>"
                    ],
                    options_hi: [
                        "<p>49</p>",
                        "<p>42</p>",
                        "<p>21</p>",
                        "<p>36</p>"
                    ],
                    solution_en: "<p>74.(c)<br>Even prime number = 2<br>Mean proportional of <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &amp; 245 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>36</mn><mn>5</mn></mfrac><mo>&#215;</mo><mn>245</mn></msqrt></math> = 42<br>Then, the quotient when 42 is divisible by 2 = 21</p>",
                    solution_hi: "<p>74.(c)<br>सम अभाज्य संख्या = 2<br><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> और 245 का माध्यानुपाती = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>36</mn><mn>5</mn></mfrac><mo>&#215;</mo><mn>245</mn></msqrt></math> = 42<br>फिर, 42 होने पर भागफल 2 = 21 से विभाज्य है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. If the equations 4x + (k &minus; 2)y + 3 = 0 and (k &minus; 2)x + 9y &minus; 5 = 0 (k &gt; 0) are parallel, then find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>k</mi><mn>2</mn></msup><mo>+</mo><mn>6</mn></math>.</p>",
                    question_hi: "<p>75. यदि समीकरण 4x + (k &minus; 2)y + 3 = 0 और (k &minus; 2)x + 9y &minus; 5 = 0 (k &gt; 0) समांतर हैं, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>k</mi><mn>2</mn></msup><mo>+</mo><mn>6</mn></math>&nbsp;का मान ज्ञात करें।</p>",
                    options_en: [
                        "<p>68</p>",
                        "<p>70</p>",
                        "<p>72</p>",
                        "<p>64</p>"
                    ],
                    options_hi: [
                        "<p>68</p>",
                        "<p>70</p>",
                        "<p>72</p>",
                        "<p>64</p>"
                    ],
                    solution_en: "<p>75.(b)<br>For the two parallel lines:- <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math><br>According to the question,<br><math display=\"inline\"><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = 4 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>2</mn></msub></math>= (k - 2) ,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>b</mi><mn>1</mn></msub></math> = (k - 2) , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>b</mi><mn>2</mn></msub></math>= 9<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><mo>(</mo><mi>k</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>k</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>9</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 36 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>k</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 6 = k - 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>(as k &gt; 0)</strong><br><math display=\"inline\"><mo>&#8658;</mo></math> k = 8<br>Now, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>k</mi><mn>2</mn></msup></math>+ 6 = 64 + 6 = 70</p>",
                    solution_hi: "<p>75.(b)<br>दो समानांतर रेखाओं के लिए :- <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\"><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = 4 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>2</mn></msub></math>= (k - 2) ,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>b</mi><mn>1</mn></msub></math> = (k - 2) , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>b</mi><mn>2</mn></msub></math>= 9<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><mo>(</mo><mi>k</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>k</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>9</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 36 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>k</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 6 = k - 2&nbsp; &nbsp; <strong>&nbsp; &nbsp;(जैसा कि K &gt; 0)</strong><br><math display=\"inline\"><mo>&#8658;</mo></math> k = 8<br>अब, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>k</mi><mn>2</mn></msup></math>+ 6 = 64 + 6 = 70</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has an error in its tense. Identify the error and select the correct sentence from the given options. <br>I will be arrange all the necessary materials for the programme.</p>",
                    question_hi: "<p>76. The following sentence has an error in its tense. Identify the error and select the correct sentence from the given options. <br>I will be arrange all the necessary materials for the programme.</p>",
                    options_en: [
                        "<p>I is arrange all the necessary materials for the programme.</p>",
                        "<p>I was arrange all the necessary materials for the programme.</p>",
                        "<p>I would arranging all the necessary materials for the programme.</p>",
                        "<p>I will be arranging all the necessary materials for the programme.</p>"
                    ],
                    options_hi: [
                        "<p>I is arrange all the necessary materials for the programme.</p>",
                        "<p>I was arrange all the necessary materials for the programme.</p>",
                        "<p>I would arranging all the necessary materials for the programme.</p>",
                        "<p>I will be arranging all the necessary materials for the programme.</p>"
                    ],
                    solution_en: "<p>76.(d) I will be arranging all the necessary materials for the programme.<br>The given sentence is an example of &lsquo;Future continuous Tense&rsquo; and follows the structure, &ldquo;Will/shall + be + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mrow><mi>i</mi><mi>n</mi><mi>g</mi></mrow></msub></math>&rdquo;. Hence, the sentence given in option (d) is correct.</p>",
                    solution_hi: "<p>76.(d) I will be arranging all the necessary materials for the programme.<br>दिया गया sentence &lsquo;Future Continuous Tense का example है और \"Will/shall + be + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mrow><mi>i</mi><mi>n</mi><mi>g</mi></mrow></msub></math>\" structure का पालन करता है। इसलिए, अतः option (d) में दिया गया sentence सही है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Choose the most appropriate synonym for the word given..<br>Supercilious</p>",
                    question_hi: "<p>77. Choose the most appropriate synonym for the word given..<br>Supercilious</p>",
                    options_en: [
                        "<p>defamatory</p>",
                        "<p>contemptuous</p>",
                        "<p>superfluous</p>",
                        "<p>irrelevant</p>"
                    ],
                    options_hi: [
                        "<p>defamatory</p>",
                        "<p>contemptuous</p>",
                        "<p>superfluous</p>",
                        "<p>irrelevant</p>"
                    ],
                    solution_en: "<p>77. (b) <strong>contemptuous</strong><br><strong>Supercilious</strong> - behaving or looking as though one thinks one is superior to others.<br>(a) <strong>Defamatory</strong> - (of remarks, writing, etc.) damaging the good reputation of someone; slanderous or libellous.<br>(c) <strong>Superfluous</strong> - unnecessary, especially through being more than enough.<br>(d) <strong>Irrelevant</strong> - not connected with or relevant to something.</p>",
                    solution_hi: "<p>77.(b) <strong>contemptuous</strong><br><strong>Supercilious</strong> -ऐसा व्यवहार करना जैसे कि कोई सोचता है कि वह दूसरों से श्रेष्ठ है।<br>(a) <strong>Defamatory</strong> - (टिप्पणी, लेखन, आदि) किसी की अच्छी प्रतिष्ठा को नुकसान पहुँचाना; बदनामी करनेवाला या बदनाम करनेवाला।<br>(c) <strong>Superfluous</strong> - अनावश्यक, विशेष रूप से पर्याप्त से अधिक होने के कारण।<br>(d) <strong>Irrelevant</strong> - किसी चीज से ना जुड़ा या अप्रासंगिक।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>The manager, decided at last to <span style=\"text-decoration: underline;\">bring the stenographer to book</span><strong> </strong>for her illegal money transactions after trying to ignore it several times.</p>",
                    question_hi: "<p>78. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>The manager, decided at last to <span style=\"text-decoration: underline;\">bring the stenographer to book</span> for her illegal money transactions after trying to ignore it several times.</p>",
                    options_en: [
                        "<p>bring her for a detailed investigation</p>",
                        "<p>take her into great confidence</p>",
                        "<p>make her accountable for her conduct</p>",
                        "<p>place her in a safe supportive position</p>"
                    ],
                    options_hi: [
                        "<p>bring her for a detailed investigation</p>",
                        "<p>take her into great confidence</p>",
                        "<p>make her accountable for her conduct</p>",
                        "<p>place her in a safe supportive position</p>"
                    ],
                    solution_en: "<p>78.(c) make her accountable for her conduct<br>&lsquo;Bring someone to book&rsquo; is an idiom which means to hold someone accountable for something they have done. Hence, &lsquo;make her accountable for her conduct&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(c) make her accountable for her conduct<br>&lsquo;Bring someone to book&rsquo; एक idiom है जिसका अर्थ है किसी को उसके किए की सजा देना। इसलिए, &lsquo;make her accountable for her conduct&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Which word in the given sentence is the ANTONYM of &ndash; exonerate?<br>It was difficult to convict him of the falsity of his beliefs.</p>",
                    question_hi: "<p>79. Which word in the given sentence is the ANTONYM of &ndash; exonerate?<br>It was difficult to convict him of the falsity of his beliefs.</p>",
                    options_en: [
                        "<p>falsity</p>",
                        "<p>convict</p>",
                        "<p>beliefs</p>",
                        "<p>difficult</p>"
                    ],
                    options_hi: [
                        "<p>falsity</p>",
                        "<p>convict</p>",
                        "<p>beliefs</p>",
                        "<p>difficult</p>"
                    ],
                    solution_en: "<p>79.(b) <strong>Exonerate</strong> - to say officially that somebody was not responsible for something bad that happened<br><strong>Convict</strong> - to say officially in a court of law that somebody is guilty of a crime<br><strong>Falsity</strong> - the fact of being untrue, incorrect, or insincere.<br><strong>Beliefs</strong> - a feeling that somebody/something is true, morally good or right, or that somebody/something really exists<br><strong>Difficult</strong> - not easy to do or understand</p>",
                    solution_hi: "<p>79.(b) <strong>Exonerate (दोषमुक्त करना)</strong> - to say officially that somebody was not responsible for something bad that happened<br><strong>Convict (अपराधी ठहराना)</strong> - to say officially in a court of law that somebody is guilty of a crime<br><strong>Falsity (झूठ)</strong> - the fact of being untrue, incorrect, or insincere.<br><strong>Beliefs (विश्&zwj;वास)</strong> - a feeling that somebody/something is true, morally good or right, or that somebody/something really exists<br><strong>Difficult (कठिन)</strong> - not easy to do or understand</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate meaning of the given idiom.<br>Yeoman\'s service</p>",
                    question_hi: "<p>80. Select the most appropriate meaning of the given idiom.<br>Yeoman\'s service</p>",
                    options_en: [
                        "<p>Fraudulent service</p>",
                        "<p>Working under a cruel master</p>",
                        "<p>Excellent work done</p>",
                        "<p>A thankless and tedious job</p>"
                    ],
                    options_hi: [
                        "<p>Fraudulent service</p>",
                        "<p>Working under a cruel master</p>",
                        "<p>Excellent work done</p>",
                        "<p>A thankless and tedious job</p>"
                    ],
                    solution_en: "<p>80.(c) <strong>Yeoman&rsquo;s service</strong>- excellent work done.<br>Ex. - The teacher did yeoman\'s service by staying late to help students with their projects.</p>",
                    solution_hi: "<p>80.(c) <strong>Yeoman&rsquo;s service</strong> - excellent work done./बहुत बढ़िया काम किया<br>Ex. - The teacher did yeoman\'s service by staying late to help students with their projects.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence. <br>She said to the children, &lsquo;You mustn&rsquo;t play with fire.&rsquo;</p>",
                    question_hi: "<p>81. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence. <br>She said to the children, &lsquo;You mustn&rsquo;t play with fire.&rsquo;</p>",
                    options_en: [
                        "<p>She told the children they were not to play with fire.</p>",
                        "<p>She told the children they mustn&rsquo;t play with fire.</p>",
                        "<p>She told the children not to play with fire.</p>",
                        "<p>She told the children they are not to play with fire.</p>"
                    ],
                    options_hi: [
                        "<p>She told the children they were not to play with fire.</p>",
                        "<p>She told the children they mustn&rsquo;t play with fire.</p>",
                        "<p>She told the children not to play with fire.</p>",
                        "<p>She told the children they are not to play with fire.</p>"
                    ],
                    solution_en: "<p>81.(b) She told the children they mustn&rsquo;t play with fire. <br>a.She told the children they <strong>were not to</strong> play with fire .(Incorrect words) <br>c.She told the children <strong>not to</strong> play with fire.(Incorrect words)<br>d.She told the children they are <strong>not to play</strong> with fire .(Incorrect words)</p>",
                    solution_hi: "<p>81.(b) She told the children they mustn&rsquo;t play with fire. <br>a.She told the children they <strong>were not to</strong> play with fire .(गलत शब्द) <br>c.She told the children <strong>not to</strong> play with fire.(गलत शब्द)<br>d.She told the children they <strong>are not to</strong> play with fire .(गलत शब्द)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the option that expresses the following sentence in passive voice.</p>\n<p>My father had to attend the meeting on Saturday.</p>",
                    question_hi: "<p>82.&nbsp;Select the option that expresses the following sentence in passive voice.</p>\n<p>My father had to attend the meeting on Saturday.</p>",
                    options_en: [
                        "<p>The meeting had been attended by my father on Saturday.</p>",
                        "<p>The meeting has to be attended by my father on Saturday.</p>",
                        "<p>The meeting had been to be attended by my father on&nbsp; Saturday.</p>",
                        "<p>The meeting had to be attended by my father on Saturday.</p>"
                    ],
                    options_hi: [
                        "<p>The meeting had been attended by my father on Saturday.</p>",
                        "<p>The meeting has to be attended by my father on Saturday.</p>",
                        "<p>The meeting had been to be attended by my father on Saturday.</p>",
                        "<p>The meeting had to be attended by my father on Saturday.</p>"
                    ],
                    solution_en: "<p>82.(d) The meeting had to be attended by my father on Saturday.<br>(a) The meeting <span style=\"text-decoration: underline;\">had been</span> attended by my father on Saturday. (Incorrect Sentence structure)<br>(b) The meeting <span style=\"text-decoration: underline;\">has to be</span> attended by my father on Saturday. (Incorrect Tense)<br>(c) The meeting <span style=\"text-decoration: underline;\">had been</span> to be attended by my father on Saturday. (Incorrect Sentence structure)</p>",
                    solution_hi: "<p>82.(d) The meeting had to be attended by my father on Saturday.<br>(a) The meeting <span style=\"text-decoration: underline;\">had been</span> attended by my father on Saturday. (गलत Sentence structure)<br>(b) The meeting <span style=\"text-decoration: underline;\">has to be</span> attended by my father on Saturday. (गलत Tense)<br>(c) The meeting <span style=\"text-decoration: underline;\">had been</span> to be attended by my father on Saturday. (गलत Sentence structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.<br>1.The freedom fighters had shaped <br>A. caste and language would<br>B. shining promise that India would <br>C. the freedom struggle around the <br>D. be a country where people of every religion <br>6. be equal citizens in every way</p>",
                    question_hi: "<p>83. Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.<br>1.The freedom fighters had shaped <br>A. caste and language would<br>B. shining promise that India would <br>C. the freedom struggle around the <br>D. be a country where people of every religion <br>6. be equal citizens in every way</p>",
                    options_en: [
                        "<p>BDAC</p>",
                        "<p>CDAB</p>",
                        "<p>CBAD</p>",
                        "<p>CBDA</p>"
                    ],
                    options_hi: [
                        "<p>BDAC</p>",
                        "<p>CDAB</p>",
                        "<p>CBAD</p>",
                        "<p>CBDA</p>"
                    ],
                    solution_en: "<p>83. (d) CBDA<br>The sentence starts with the given phrase &lsquo;The freedom fighters had shaped&rsquo;. Part C mentions the object i.e. had shaped the freedom struggle. So, C will follow 1. However, Part B contains the noun &lsquo;shining promise&rsquo; which will follow the article &lsquo;the&rsquo;, mentioned in Part C. Further, D contains the verb &lsquo;be&rsquo; which follows the verb &lsquo;would&rsquo;, mentioned in Part B and Part A continues the subject in Part D i.e. people of every religion, caste and language&rsquo;. So, A will follow D. Going through the options, option (d) has the correct sequence.</p>",
                    solution_hi: "<p>83. (d) CBDA<br>वाक्य दिए गए phrase &lsquo;The freedom fighters had shaped&rsquo; से शुरू होता है। Part C में object का उल्लेख है अर्थात स्वतंत्रता संग्राम को आकार दिया था। तो, 1 के बाद C आएगा । Part B में noun &lsquo;shining promise&rsquo; शामिल है, जो Part C में वर्णित article &lsquo;the&rsquo; के बाद आएगा । इसके अलावा, D में verb &lsquo;be&rsquo; है, जो Part B मे उल्लिखित verb &lsquo;would\' के बाद आएगा और Part A में Part D का subject यानी people of every religion, caste and language जारी है । इसलिए, D के बाद A,आएगा । विकल्पों के माध्यम से, option (d) में सही क्रम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the word which means the same as the group of words given.<br>Showing deep sorrow for wrong doing.</p>",
                    question_hi: "<p>84. Select the word which means the same as the group of words given.<br>Showing deep sorrow for wrong doing.</p>",
                    options_en: [
                        "<p>Convalescence</p>",
                        "<p>Guilty</p>",
                        "<p>Criminal</p>",
                        "<p>Contrite</p>"
                    ],
                    options_hi: [
                        "<p>Convalescence</p>",
                        "<p>Guilty</p>",
                        "<p>Criminal</p>",
                        "<p>Contrite</p>"
                    ],
                    solution_en: "<p>84.(d) <strong>Contrite</strong> - feeling or expressing remorse at the recognition that one has done wrong.<br>(a) <strong>Convalescence</strong> - time spent recovering from an illness or medical treatment; recuperation.<br>(b) <strong>Guilty</strong> - culpable of or responsible for a specified wrongdoing.<br>(c) <strong>Criminal</strong> - a person who has committed a crime.</p>",
                    solution_hi: "<p>84.(d) <strong>Contrite</strong> - यह महसूस करना या पश्चाताप व्यक्त करना कि किसी ने गलत किया है।<br>(a) <strong>Convalescence</strong> - बीमारी या चिकित्सा उपचार से ठीक होने में लगने वाला समय।<br>(b) <strong>Guilty</strong> - एक गलत काम के लिए दोषी या जिम्मेदार।<br>(c) <strong>Criminal</strong> - एक व्यक्ति जिसने अपराध किया है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the sentence that has the correct use of words and spellings from the options given below.</p>",
                    question_hi: "<p>85. Select the sentence that has the correct use of words and spellings from the options given below.</p>",
                    options_en: [
                        "<p>The skilled surgeon delicately performed the intricate procedure, saving the patient\'s life with his steady hands and unwavering focus.</p>",
                        "<p>The skiled surgeon delicatly performed the intricate procedure, saving the patient\'s life with his stedy hands and unwavering focus.</p>",
                        "<p>The skilled surgeon delicately performed the intrecate procedure, saving the patient\'s life with his steady hands and unwavering focus.</p>",
                        "<p>The skilled surgeon delicatly performed the intricate procedure, saving the patient\'s life with his stedy hands and unwavering focus.</p>"
                    ],
                    options_hi: [
                        "<p>The skilled surgeon delicately performed the intricate procedure, saving the patient\'s life with his steady hands and unwavering focus.</p>",
                        "<p>The skiled surgeon delicatly performed the intricate procedure, saving the patient\'s life with his stedy hands and unwavering focus.</p>",
                        "<p>The skilled surgeon delicately performed the intrecate procedure, saving the patient\'s life with his steady hands and unwavering focus.</p>",
                        "<p>The skilled surgeon delicatly performed the intricate procedure, saving the patient\'s life with his stedy hands and unwavering focus.</p>"
                    ],
                    solution_en: "<p>85.(a) The <span style=\"text-decoration: underline;\">skilled</span> surgeon <span style=\"text-decoration: underline;\">delicately</span> performed the <span style=\"text-decoration: underline;\">intricate</span> procedure, saving the patient\'s life with his <span style=\"text-decoration: underline;\">steady</span> hands and unwavering focus.<br>&lsquo;Skilled&rsquo;, &lsquo;delicately&rsquo;, &lsquo;intricate&rsquo; and &lsquo;steady&rsquo; are the correct spellings.</p>",
                    solution_hi: "<p>85.(a) The <span style=\"text-decoration: underline;\">skilled</span> surgeon <span style=\"text-decoration: underline;\">delicately</span> performed the <span style=\"text-decoration: underline;\">intricate</span> procedure, saving the patient\'s life with his <span style=\"text-decoration: underline;\">steady</span> hands and unwavering focus.<br>&lsquo;Skilled&rsquo;, &lsquo;delicately&rsquo;, &lsquo;intricate&rsquo; और &lsquo;steady&rsquo; सही spellings हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86.Choose the best alternative which best expresses the meaning of the idiom/ phrase. <br>Hit a brick wall</p>",
                    question_hi: "<p>86.Choose the best alternative which best expresses the meaning of the idiom/ phrase. <br>Hit a brick wall</p>",
                    options_en: [
                        "<p>Demolish a brick wall</p>",
                        "<p>Not able to make any progress</p>",
                        "<p>Use physical force</p>",
                        "<p>Fight a powerful foe</p>"
                    ],
                    options_hi: [
                        "<p>Demolish a brick wall</p>",
                        "<p>Not able to make any progress</p>",
                        "<p>Use physical force</p>",
                        "<p>Fight a powerful foe</p>"
                    ],
                    solution_en: "<p>86.(b) Hit a brick wall - Not able to make any progress.<br>E.g.- The project will eventually hit a brick wall as we are having a shortage of funds.</p>",
                    solution_hi: "<p>86.(b) Hit a brick wall - Not able to make any progress (कोई प्रगति नहीं कर पाना).<br>E.g.- The project will eventually hit a brick wall as we are having a shortage of funds. </p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>He was advised to be more cautious lest he is robbed again.</p>",
                    question_hi: "<p>87. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>He was advised to be more cautious lest he is robbed again.</p>",
                    options_en: [
                        "<p>He was advised</p>",
                        "<p>to be more cautious</p>",
                        "<p>lest he is robbed again</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>He was advised</p>",
                        "<p>to be more cautious</p>",
                        "<p>lest he is robbed again</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>87.(c) lest he is robbed again.<br>&lsquo;Lest&rsquo; should be followed by &lsquo;should&rsquo;; here, &lsquo;lest he should be robbed again&rsquo; is correct.</p>",
                    solution_hi: "<p>87(c) lest he is robbed again.<br>&lsquo;Lest&rsquo; के बाद &lsquo;should&rsquo; होना चाहिए; यहाँ, &lsquo;lest he should be robbed again&rsquo; सही है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the option that can be used as a one-word substitute for the given group of words. <br>Feeling of being in a place before having already experienced the present situation</p>",
                    question_hi: "<p>88. Select the option that can be used as a one-word substitute for the given group of words. <br>Feeling of being in a place before having already experienced the present situation</p>",
                    options_en: [
                        "<p>Spirituality</p>",
                        "<p>Delusion</p>",
                        "<p>Deja-vu</p>",
                        "<p>Illusion</p>"
                    ],
                    options_hi: [
                        "<p>Spirituality</p>",
                        "<p>Delusion</p>",
                        "<p>Deja-vu</p>",
                        "<p>Illusion</p>"
                    ],
                    solution_en: "<p>88.(c) <strong>Deja-vu</strong> - Feeling of being in a place before having already experienced the present situation.<br><strong>Spirituality-</strong> the quality of being concerned with the human spirit or soul as opposed to material or physical things.<br><strong>Delusion-</strong> belief in something that is not true.<br><strong>Illusion-</strong> a false idea, belief or impression.</p>",
                    solution_hi: "<p>88.(c) <strong>Deja-vu</strong> (पूर्वाभास)- Feeling of being in a place before having already experienced the present situation.<br><strong>Spirituality</strong> (आध्यात्मिकता)- the quality of being concerned with the human spirit or soul as opposed to material or physical things.<br><strong>Delusion</strong> (भ्रम)- belief in something that is not true.<br><strong>Illusion</strong> (भ्रांति)- a false idea, belief or impression.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. A major ________ of modern times is that we have come to assume that all creation is ________ to the material interests of us, the homo-sapiens inhabiting earth, which has cut the _________ relationship we otherwise enjoyed with other constituents of creation</p>",
                    question_hi: "<p>89. A major ________ of modern times is that we have come to assume that all creation is ________ to the material interests of us, the homo-sapiens inhabiting earth, which has cut the _________ relationship we otherwise enjoyed with other constituents of creation</p>",
                    options_en: [
                        "<p>fallacy, subservient, symbiotic</p>",
                        "<p>frankness, superior, disadvantageous</p>",
                        "<p>accuracy, domineering, encumbering</p>",
                        "<p>felicity, disobedient, hindering</p>"
                    ],
                    options_hi: [
                        "<p>fallacy, subservient, symbiotic</p>",
                        "<p>frankness, superior, disadvantageous</p>",
                        "<p>accuracy, domineering, encumbering</p>",
                        "<p>felicity, disobedient, hindering</p>"
                    ],
                    solution_en: "<p>89.(a) fallacy, subservient, symbiotic<br>The first blank will be filled by the word fallacy. Fallacy means a mistaken belief. The word assume is used in the first part of the sentence which hints at the word fallacy. Assume means to suppose something without proper facts and this leads to a fallacy.So a is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(a) fallacy, subservient, symbiotic<br>पहला रिक्त स्थान fallacy शब्द से भरा जाएगा। fallacy का अर्थ है गलत धारणा(भ्रम) । Assume शब्द का प्रयोग वाक्य के पहले भाग में किया जाता है जो कि fallacy शब्द पर संकेत करता है। Assume का अर्थ है &lsquo;उचित तथ्यों के बिना कुछ मान लेना और इससे भ्रम पैदा होता है।&rsquo; इसलिए (a) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate synonym of the underlined word in the given sentence. <br>Mr. Satterthwaite was <span style=\"text-decoration: underline;\">shrewd</span> enough to penetrate her meaning.</p>",
                    question_hi: "<p>90. Select the most appropriate synonym of the underlined word in the given sentence. <br>Mr. Satterthwaite was <span style=\"text-decoration: underline;\">shrewd</span> enough to penetrate her meaning.</p>",
                    options_en: [
                        "<p>Aged</p>",
                        "<p>Smart</p>",
                        "<p>Funny</p>",
                        "<p>Innocent</p>"
                    ],
                    options_hi: [
                        "<p>Aged</p>",
                        "<p>Smart</p>",
                        "<p>Funny</p>",
                        "<p>Innocent</p>"
                    ],
                    solution_en: "<p>90.(b) <strong>Smart-</strong> having or showing quick-witted intelligence.<br><strong>Shrewd-</strong> having sharp powers of judgment; astute.<br><strong>Aged-</strong> being old or elderly.<br><strong>Funny-</strong> causing laughter or amusement.<br><strong>Innocent-</strong> free from guilt or blame.</p>",
                    solution_hi: "<p>90.(b) <strong>Smart</strong> (बुद्धिमान) - having or showing quick-witted intelligence.<br><strong>Shrewd</strong> (चतुर) - having sharp powers of judgment; astute.<br><strong>Aged</strong> (वृद्ध) - being old or elderly.<br><strong>Funny</strong> (हास्यजन) - causing laughter or amusement.<br><strong>Innocent</strong> (निर्दोष) - free from guilt or blame.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. Photography, recording,microphones and even coughing had not been allowed in her concerts. <br>B. But the producer of the record wasn\'t going to give up easily.<br>C. It was possible to find traces of a singer who hated being recorded.<br>D. Kesturi had stopped singing in public in 1965.</p>",
                    question_hi: "<p>91. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. Photography, recording,microphones and even coughing had not been allowed in her concerts. <br>B. But the producer of the record wasn\'t going to give up easily.<br>C. It was possible to find traces of a singer who hated being recorded.<br>D. Kesturi had stopped singing in public in 1965.</p>",
                    options_en: [
                        "<p>DCBA</p>",
                        "<p>ABCD</p>",
                        "<p>ACBD</p>",
                        "<p>DACB</p>"
                    ],
                    options_hi: [
                        "<p>DCBA</p>",
                        "<p>ABCD</p>",
                        "<p>ACBD</p>",
                        "<p>DACB</p>"
                    ],
                    solution_en: "<p>91.(d) DACB<br>Sentence D will be the starting line as it contains the main subject of the parajumble i.e. Kesturi, who had stopped singing. And, Sentence A states about the restrictions in her concerts. So, A will follow D. Further, Sentence C states that the singer showed traces who hated being recorded and Sentence B states about the producer who didn&rsquo;t want to give up on her. So, B will follow C. Going through the options, option (d) has the correct sequence.</p>",
                    solution_hi: "<p>91.(d) DACB<br>Sentence D starting line होगी क्योंकि इसमें parajumble का मुख्य विषय है - कस्तूरी जिसने गाना बंद कर दिया था। Sentence A उसके संगीत कार्यक्रमों में प्रतिबंधों के बारे में बताता है। तो, D के बाद A आएगा। आगे, Sentence C कहता है कि गायिका ने ऐसे traces दिखाए जो रिकॉर्ड किए जाने से नफरत करते थे और Sentence B उस निर्माता के बारे में बताता है । तो, C के बाद B आएगा। options के माध्यम से जाने पर, option (d) में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Identify the most appropriate antonym of the given word.<br>Freezing</p>",
                    question_hi: "<p>92. Identify the most appropriate antonym of the given word.<br>Freezing</p>",
                    options_en: [
                        "<p>brisk</p>",
                        "<p>tropical</p>",
                        "<p>summery</p>",
                        "<p>gnawing</p>"
                    ],
                    options_hi: [
                        "<p>brisk</p>",
                        "<p>tropical</p>",
                        "<p>summery</p>",
                        "<p>gnawing</p>"
                    ],
                    solution_en: "<p>92.(b) <strong>Tropical-</strong> very hot and humid, typical of the tropics.<br><strong>Freezing-</strong> extremely cold or below the temperature of ice formation.<br><strong>Brisk-</strong> quick, energetic, or cool.<br><strong>Summery-</strong> warm or characteristic of summer.<br><strong>Gnawing-</strong> persistently worrying or distressing.</p>",
                    solution_hi: "<p>92.(b) <strong>Tropical</strong> (उष्ण कटिबंधीय) - very hot and humid, typical of the tropics.<br><strong>Freezing</strong> (हिमीकरण) - extremely cold or below the temperature of ice formation.<br><strong>Brisk</strong> (तेज) - quick, energetic, or cool.<br><strong>Summery</strong> (ग्रीष्मकालिन) - warm or characteristic of summer.<br><strong>Gnawing</strong> (कुतरना) - persistently worrying or distressing.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the option that expresses the following sentence in passive voice.<br>Why would Celia burn the letter?</p>",
                    question_hi: "<p>93. Select the option that expresses the following sentence in passive voice.<br>Why would Celia burn the letter?</p>",
                    options_en: [
                        "<p>Why is the letter burned by Celia?</p>",
                        "<p>Why the letter burned by Celia?</p>",
                        "<p>Why Celia would burn the letter?</p>",
                        "<p>Why would the letter be burned by Celia?</p>"
                    ],
                    options_hi: [
                        "<p>Why is the letter burned by Celia?</p>",
                        "<p>Why the letter burned by Celia?</p>",
                        "<p>Why Celia would burn the letter?</p>",
                        "<p>Why would the letter be burned by Celia?</p>"
                    ],
                    solution_en: "<p>93.(d) Why would the letter be burned by Celia?(Correct)<br>(a) Why <span style=\"text-decoration: underline;\">is</span> the letter burned by Celia? (Incorrect Tense)<br>(b) Why the letter <span style=\"text-decoration: underline;\">burned</span> by Celia? (Incorrect Tense)<br>(c) Why <span style=\"text-decoration: underline;\">Celia would</span> burn the letter? (Incorrect sentence structure)</p>",
                    solution_hi: "<p>93.(d) Why would the letter be burned by Celia? (Correct)<br>(a) Why <span style=\"text-decoration: underline;\">is</span> the letter burned by Celia? (गलत Tense)<br>(b) Why the letter <span style=\"text-decoration: underline;\">burned</span> by Celia? (गलत Tense)<br>(c) Why <span style=\"text-decoration: underline;\">Celia would</span> burn the letter? (गलत sentence structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>The officer _____ the car to speak to the driver last night.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>The officer _____ the car to speak to the driver last night.</p>",
                    options_en: [
                        "<p>stops</p>",
                        "<p>has stopped</p>",
                        "<p>stopped</p>",
                        "<p>have stopped</p>"
                    ],
                    options_hi: [
                        "<p>stops</p>",
                        "<p>has stopped</p>",
                        "<p>stopped</p>",
                        "<p>have stopped</p>"
                    ],
                    solution_en: "<p>94.(c) stopped <br>The given sentence is in the past tense so it must have a verb in the past form(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>). Hence, &lsquo;stopped(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>94.(c) stopped <br>दिया गया वाक्य past tense में है इसलिए इसमें verb भी past form(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>) में होनी चाहिए । इसलिए, &lsquo;stopped(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br><span style=\"text-decoration: underline;\"><strong>His powerful desire</strong></span> brought about his downfall.</p>",
                    question_hi: "<p>95. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br><span style=\"text-decoration: underline;\"><strong>His powerful desire</strong></span> brought about his downfall.</p>",
                    options_en: [
                        "<p>His intense desire</p>",
                        "<p>His desire for power</p>",
                        "<p>His fatal desire</p>",
                        "<p>No improvement.</p>"
                    ],
                    options_hi: [
                        "<p>His intense desire</p>",
                        "<p>His desire for power</p>",
                        "<p>His fatal desire</p>",
                        "<p>No improvement.</p>"
                    ],
                    solution_en: "<p>95.(b) His desire for power</p>",
                    solution_hi: "<p>95.(b) His desire for power</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong> Cloze test :</strong><br>A little bird was flying South for the winter. It was so cold the bird froze and (96)_______to the ground into a large field. While she was (97)________there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)_______of cow dung, she began to realize how warm she was. The dung was actually (99)______her out! She lay there all warm and happy, and&nbsp;soon began to sing for joy. A cat heard the bird singing and (100)_________ the bird under the cow dung. The cat dug her out and ate her.<br>Choose the word from the given options which fits in the blank labelled 96 and gives correct meaning</p>",
                    question_hi: "<p>96. <strong>Cloze test :</strong><br>A little bird was flying South for the winter. It was so cold the bird froze and (96)________to the ground into a large field. While she was (97)_______there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)________of cow dung, she began to realize how warm she was. The dung was actually (99)_______her out! She lay there all warm and happy, and&nbsp;soon began to sing for joy. A cat heard the bird singing and (100)_______ the bird under the cow dung. The cat dug her out and ate her.<br>Choose the word from the given options which fits in the blank labelled 96 and gives correct meaning</p>",
                    options_en: [
                        "<p>Soared</p>",
                        "<p>Went up</p>",
                        "<p>Rose</p>",
                        "<p>Fell</p>"
                    ],
                    options_hi: [
                        "<p>Soared</p>",
                        "<p>Went up</p>",
                        "<p>Rose</p>",
                        "<p>Fell</p>"
                    ],
                    solution_en: "<p>96.(d) fell<br>&lsquo;Fell&rsquo; means came down. The given passage states that it was so cold the bird froze and fell to the ground into a large field. Hence, &lsquo;fell&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) fell<br>&lsquo;Fell&rsquo; का अर्थ है नीचे आना। दिए गए passage में कहा गया है कि इतनी ठंड थी कि पक्षी freez हो गया और एक बड़े मैदान में ज़मीन पर गिर गया। अतः, &lsquo;fell&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze test :</strong><br>A little bird was flying South for the winter. It was so cold the bird froze and (96)_______to the ground into a large field. While she was (97)_______there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)_______of cow dung, she began to realize how warm she was. The dung was actually (99)_______her out! She lay there all warm and happy, and&nbsp;soon began to sing for joy. A cat heard the bird singing and (100)_______ the bird under the cow dung. The cat dug her out and ate her.<br>Choose the word from the given options which fits in the blank labelled 97 and gives correct meaning.</p>",
                    question_hi: "<p>97. <strong>Cloze test :</strong><br>A little bird was flying South for the winter. It was so cold the bird froze and (96)_______to the ground into a large field. While she was (97)______there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)_______of cow dung, she began to realize how warm she was. The dung was actually (99)_______her out! She lay there all warm and happy, and&nbsp;soon began to sing for joy. A cat heard the bird singing and (100)________ the bird under the cow dung. The cat dug her out and ate her.<br>Choose the word from the given options which fits in the blank labelled 97 and gives correct meaning.</p>",
                    options_en: [
                        "<p>Sleeping</p>",
                        "<p>Napping</p>",
                        "<p>Lying</p>",
                        "<p>Walking</p>"
                    ],
                    options_hi: [
                        "<p>Sleeping</p>",
                        "<p>Napping</p>",
                        "<p>Lying</p>",
                        "<p>Walking</p>"
                    ],
                    solution_en: "<p>97.(c) lying<br>&lsquo;Lying&rsquo; means resting on a surface. The given passage states that while she was lying there, a cow came by and dropped some dung on her. Hence, &lsquo;lying&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(c) lying<br>&lsquo;Lying&rsquo; का अर्थ है किसी सतह पर आराम करना। दिए गए passage में कहा गया है कि जब वह वहाँ लेटी हुई थी, तो एक गाय आई और उसने उस पर कुछ गोबर गिरा दिया। अतः, &lsquo;lying&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze test :</strong><br>A little bird was flying South for the winter. It was so cold the bird froze and (96)________to the ground into a large field. While she was (97)_______there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)_______of cow dung, she began to realize how warm she was. The dung was actually (99)________her out! She lay there all warm and happy, and soon began to sing for joy. A cat heard the bird singing and (100)________ the bird under the cow dung. The cat dug her out and ate her.<br>Choose the word from the given options which fits in the blank labelled 98 and gives correct meaning</p>",
                    question_hi: "<p>98. <strong>Cloze test :</strong><br>A little bird was flying South for the winter. It was so cold the bird froze and (96)________to the ground into a large field. While she was (97)________there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)________of cow dung, she began to realize how warm she was. The dung was actually (99)________her out! She lay there all warm and happy, and soon began to sing for joy. A cat heard the bird singing and (100)________ the bird under the cow dung. The cat dug her out and ate her.<br>Choose the word from the given options which fits in the blank labelled 98 and gives correct meaning</p>",
                    options_en: [
                        "<p>Crowd</p>",
                        "<p>Box</p>",
                        "<p>Pile</p>",
                        "<p>Row</p>"
                    ],
                    options_hi: [
                        "<p>Crowd</p>",
                        "<p>Box</p>",
                        "<p>Pile</p>",
                        "<p>Row</p>"
                    ],
                    solution_en: "<p>98.(c) pile<br>&lsquo;Pile&rsquo; means a heap of something. The given passage states that as the frozen bird lay there in the pile of cow dung, she began to realise how warm she was. Hence, &lsquo;pile&rsquo; is the most appropriate answer..</p>",
                    solution_hi: "<p>98.(c) pile<br>&lsquo;Pile&rsquo; का अर्थ है किसी चीज़ का ढेर। दिए गए passage में कहा गया है कि जब frozen bird गोबर के ढेर में लेटी, तो उसे एहसास हुआ कि वह कितनी गर्म हो गई है। अतः, &lsquo;pile&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze test :</strong><br>A little bird was flying South for the winter. It was so cold the bird froze and (96)________to the ground into a large field. While she was (97)________there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)_______of cow dung, she began to realize how warm she was. The dung was actually (99)________her out! She lay there all warm and happy, and<br>soon began to sing for joy. A cat heard the bird singing and (100)________ the bird under the cow dung. The cat dug her out and ate her.<br>Choose the word from the given options which fits in the blank labelled 99 and gives correct meaning</p>",
                    question_hi: "<p>99. <strong>Cloze test :</strong><br>A little bird was flying South for the winter. It was so cold the bird froze and (96)________to the ground into a large field. While she was (97)_______there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)_______of cow dung, she began to realize how warm she was. The dung was actually (99)________her out! She lay there all warm and happy, and soon began to sing for joy. A cat heard the bird singing and (100)_______ the bird under the cow dung. The cat dug her out and ate her.<br>Choose the word from the given options which fits in the blank labelled 99 and gives correct meaning</p>",
                    options_en: [
                        "<p>Thawing</p>",
                        "<p>Chilling</p>",
                        "<p>Drawing</p>",
                        "<p>Killing</p>"
                    ],
                    options_hi: [
                        "<p>Thawing</p>",
                        "<p>Chilling</p>",
                        "<p>Drawing</p>",
                        "<p>Killing</p>"
                    ],
                    solution_en: "<p>99.(a) thawing<br>&lsquo;Thawing&rsquo; means to become warm. The given passage states that the dung was actually thawing her out. Hence, &lsquo;thawing&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) thawing<br>&lsquo;Thawing&rsquo; का अर्थ है गर्म होना। दिए गए passage में कहा गया है कि गोबर वास्तव में उसे पिघला रहा था। अतः, &lsquo;thawing&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. Cloze test :<br>A little bird was flying South for the winter. It was so cold the bird froze and (96)________to the ground into a large field. While she was (97)________there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)________of cow dung, she began to realize how warm she was. The dung was actually (99)________her out! She lay there all warm and happy, and soon began to sing for joy. A cat heard the bird singing and (100)________ the bird under the cow dung. The cat dug her out and ate her.<br>Identify the phrasal verb which fits in the blank labelled as 100 and gives correct meaning.</p>",
                    question_hi: "<p>100.<strong> Cloze test :</strong><br>A little bird was flying South for the winter. It was so cold the bird froze and (96)________to the ground into a large field. While she was (97)________there, a cow came by and dropped some dung on her. As the frozen bird lay there in the (98)______of cow dung, she began to realize how warm she was. The dung was actually (99)_________her out! She lay there all warm and happy, and soon began to sing for joy. A cat heard the bird singing and (100)________ the bird under the cow dung. The cat dug her out and ate her.<br>Identify the phrasal verb which fits in the blank labelled as 100 and gives correct meaning.</p>",
                    options_en: [
                        "<p>hit</p>",
                        "<p>covered</p>",
                        "<p>discovered</p>",
                        "<p>hid</p>"
                    ],
                    options_hi: [
                        "<p>hit</p>",
                        "<p>covered</p>",
                        "<p>discovered</p>",
                        "<p>hid</p>"
                    ],
                    solution_en: "<p>100.(c) discovered<br>&lsquo;Discovered&rsquo; means found something. The given passage states that a cat heard the bird singing and discovered the bird under the cow dung. Hence, &lsquo;discovered&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) discovered<br>&lsquo;Discovered&rsquo; का अर्थ है कुछ पाया। दिए गए passage में कहा गया है कि एक बिल्ली ने पक्षी को गाते हुए सुना और उसे गोबर के नीचे पक्षी पाया। अतः, &lsquo;discovered&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>