<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 1.</p>",
                    question_hi: "<p>1. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 1.</p>",
                    options_en: ["<p>at</p>", "<p>for</p>", 
                                "<p>in</p>", "<p>to</p>"],
                    options_hi: ["<p>at</p>", "<p>for</p>",
                                "<p>in</p>", "<p>to</p>"],
                    solution_en: "<p>1.(a) <strong>at</strong><br>&lsquo;At&rsquo; is used to indicate the specific place or an event. Similarly, in the given passage, the Global Wildlife Programme conference is the specific event. Hence, &lsquo;at&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(a)<strong> at</strong><br>&lsquo;At&rsquo; का प्रयोग किसी विशिष्ट स्थान या कार्यक्रम को इंगित करने के लिए किया जाता है। इसी प्रकार, दिए गए passage में, Global Wildlife Programme conference एक विशिष्ट कार्यक्रम है। अतः, &lsquo;at&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 2.</p>",
                    question_hi: "<p>2.<strong> Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 2.</p>",
                    options_en: ["<p>to</p>", "<p>and</p>", 
                                "<p>in</p>", "<p>for</p>"],
                    options_hi: ["<p>to</p>", "<p>and</p>",
                                "<p>in</p>", "<p>for</p>"],
                    solution_en: "<p>2.(b) <strong>and</strong><br>&lsquo;And&rsquo; is used to connect to similar parts of speech. Similarly, in the given passage, &lsquo;and&rsquo; has been used to connect two verbs &lsquo;recognises&rsquo; &amp; &lsquo;addresses&rsquo;. Hence, &lsquo;and&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(b) <strong>and</strong><br>&lsquo;And&rsquo; का प्रयोग same parts of speech को जोड़ने के लिए किया जाता है। इसी तरह, दिए गए passage में, &lsquo;and&rsquo; का प्रयोग दो verb &lsquo;recognises&rsquo; और &lsquo;addresses&rsquo; को जोड़ने के लिए किया गया है। अतः, &lsquo;and&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 3.</p>",
                    question_hi: "<p>3.<strong> Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 3.</p>",
                    options_en: ["<p>group</p>", "<p>human</p>", 
                                "<p>species</p>", "<p>breed</p>"],
                    options_hi: ["<p>group</p>", "<p>human</p>",
                                "<p>species</p>", "<p>breed</p>"],
                    solution_en: "<p>3.(b) <strong>human</strong><br>The given passage states that the plan details the importance of ecosystems for food production, health and other aspects of human survival and sustainable development. Hence, \'human\' is the most appropriate answer.</p>",
                    solution_hi: "<p>3.(b) <strong>human</strong><br>दिए गए passage में कहा गया है कि योजना खाद्य उत्पादन, स्वास्थ्य और मानव अस्तित्व एवं सतत विकास(sustainable development) के अन्य पहलुओं के लिए पारिस्थितिकी तंत्र (ecosystems) के महत्व का विवरण देती है। अतः , \'human\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 4.</p>",
                    question_hi: "<p>4. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 4.</p>",
                    options_en: ["<p>conflict</p>", "<p>diversity</p>", 
                                "<p>focus</p>", "<p>rejection</p>"],
                    options_hi: ["<p>conflict</p>", "<p>diversity</p>",
                                "<p>focus</p>", "<p>rejection</p>"],
                    solution_en: "<p>4.(b) <strong>diversity</strong><br>&lsquo;Diversity&rsquo; means the state of being different from one another. The given passage states that it also emphasises preservation of genetic diversity and sustainable utilisation of species and ecosystems. Hence, &lsquo;diversity&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(b) <strong>diversity</strong><br>&lsquo;Diversity&rsquo; का अर्थ है एक दूसरे से अलग होने की अवस्था। दिए गए passage में कहा गया है कि यह आनुवंशिक विविधता (genetic diversity) के संरक्षण और प्रजातियों (species) एवं पारिस्थितिकी तंत्र (ecosystems) के सतत उपयोग पर भी जोर देता है। अतः, &lsquo;diversity&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 5.</p>",
                    question_hi: "<p>5. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (1)______ the Global Wildlife Programme conference. The plan recognises (2)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (3)______ survival and sustainable development. It also emphasises preservation of genetic (4)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (5)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 5.</p>",
                    options_en: ["<p>including</p>", "<p>gathering</p>", 
                                "<p>counting</p>", "<p>mixing</p>"],
                    options_hi: ["<p>including</p>", "<p>gathering</p>",
                                "<p>counting</p>", "<p>mixing</p>"],
                    solution_en: "<p>5.(a) <strong>including</strong><br>&lsquo;Including&rsquo; means having something as a part. According to the passage, inland aquatic, coastal and marine ecosystems are also a part of the rehabilitation plan. Hence, &lsquo;including&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(a) <strong>including</strong><br>&lsquo;Including&rsquo; का अर्थ है कोई चीज़ शामिल होना। दिए गए passage के अनुसार, अंतर्देशीय(inland) जलीय, तटीय और समुद्री पारिस्थितिकी तंत्र भी पुनर्वास योजना (rehabilitation plan) का एक हिस्सा हैं। अतः, &lsquo;including&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 6</p>",
                    question_hi: "<p>6. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 6</p>",
                    options_en: ["<p>restrictions</p>", "<p>intemperance</p>", 
                                "<p>advantages</p>", "<p>handicap</p>"],
                    options_hi: ["<p>restrictions</p>", "<p>intemperance</p>",
                                "<p>advantages</p>", "<p>handicap</p>"],
                    solution_en: "<p>6.(a) <strong>restrictions</strong><br>&lsquo;Restriction&rsquo; means a law or rule that limits or controls something. The given passage states that Americans have been arguing about pandemic restrictions for two years. Hence, &lsquo;restrictions&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(a) <strong>restrictions</strong><br>&lsquo;Restriction&rsquo; का अर्थ है ऐसा कानून या नियम जो किसी चीज़ को सीमित या नियंत्रित करता है। दिए गए passage में कहा गया है कि Americans दो वर्षों से महामारी प्रतिबंधों के बारे में बहस कर रहे हैं। अतः, &lsquo;restrictions&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 7</p>",
                    question_hi: "<p>7. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 7</p>",
                    options_en: ["<p>brandings</p>", "<p>measures</p>", 
                                "<p>models</p>", "<p>sluggishness</p>"],
                    options_hi: ["<p>brandings</p>", "<p>measures</p>",
                                "<p>models</p>", "<p>sluggishness</p>"],
                    solution_en: "<p>7.(b) <strong>measures</strong><br>&lsquo;Measures&rsquo; means steps taken to deal with a situation. The given passage states that measures such as masking and isolation mean temporary discomfort or inconvenience for most people. Hence, \'measures\' is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(b) <strong>measures</strong><br>&lsquo;Measures&rsquo; का अर्थ है किसी स्थिति से निपटने के लिए उठाए गए कदम। दिए गए passage में कहा गया है कि mask लगाने और अलगाव (isolation) जैसे उपायों का मतलब ज्यादातर लोगों के लिए अस्थायी परेशानी या असुविधा है। अतः, \'measures\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 8</p>",
                    question_hi: "<p>8. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 8</p>",
                    options_en: ["<p>marginalisation</p>", "<p>foundations</p>", 
                                "<p>disapproval</p>", "<p>consequences</p>"],
                    options_hi: ["<p>marginalisation</p>", "<p>foundations</p>",
                                "<p>disapproval</p>", "<p>consequences</p>"],
                    solution_en: "<p>8.(d) <strong>consequences</strong><br>&lsquo;Consequence&rsquo; means a result or effect, typically one that is unwelcome or unpleasant. The given passage states that their consequences for still-developing young children are more mysterious, and possibly more significant and lasting. Hence, \'consequences\' is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(d) <strong>consequences</strong><br>&lsquo;Consequence&rsquo; का अर्थ है परिणाम या प्रभाव, आम तौर पर वह जो अवांछित या अप्रिय होता है। दिए गए passage में कहा गया है कि अभी भी developing young children के लिए उनके परिणाम अधिक रहस्यमय हैं, और संभवतः अधिक महत्वपूर्ण एवं स्थायी हैं। अतः, \'consequences\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 9.</p>",
                    question_hi: "<p>9. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 9.</p>",
                    options_en: ["<p>clearest</p>", "<p>vague</p>", 
                                "<p>indistinct</p>", "<p>nebulous</p>"],
                    options_hi: ["<p>clearest</p>", "<p>vague</p>",
                                "<p>indistinct</p>", "<p>nebulous</p>"],
                    solution_en: "<p>9.(a) <strong>clearest</strong><br>&lsquo;Clearest&rsquo; means the most obvious. The given passage states that children with speech or language disorders offer perhaps the clearest example of these murky trade-offs. Hence, \'clearest\' is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(a) <strong>clearest</strong><br>&lsquo;Clearest&rsquo; का अर्थ है सबसे स्पष्ट। दिए गए passage में कहा गया है कि speech या language disorders वाले बच्चे शायद इन अंधकारमय संतुलन (murky trade-offs) का सबसे स्पष्ट उदाहरण प्रस्तुत करते हैं। अतः, \'clearest\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10.<strong> Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no.10.</p>",
                    question_hi: "<p>10. <strong>Cloze Test:</strong><br>Americans have been arguing about pandemic (6) _______for two years, and the debate is particularly fraught among parents of small children, for good reasons. While (7)_______such as masking and isolation mean temporary discomfort or inconvenience for most people, their (8) ______for still-developing young children are more mysterious, and possibly more significant and lasting. <br>Children with speech or language disorders offer perhaps the (9) ______ example of these murky trade-offs. Pandemic restrictions vary by state, county and school district, but I spoke with parents in California, New York, Massachusetts, Washington, New Jersey, lowa and Maryland, who said their children\'s speech therapy has been (10) _______: first by the loss of in-person therapy and then by masking requirements, in places that have them.<br>Select the most appropriate option to fill in blank no. 10.</p>",
                    options_en: ["<p>disrupted</p>", "<p>regulated</p>", 
                                "<p>measured</p>", "<p>normalised</p>"],
                    options_hi: ["<p>disrupted</p>", "<p>regulated</p>",
                                "<p>measured</p>", "<p>normalised</p>"],
                    solution_en: "<p>10.(a) <strong>disrupted</strong><br>&lsquo;Disrupted&rsquo; means prevent something from continuing. The given passage states that their children&rsquo;s speech therapy has been disrupted. Hence, \'disrupted\' is the most appropriate answer.</p>",
                    solution_hi: "<p>10.(a) <strong>disrupted</strong><br>&lsquo;Disrupted&rsquo; का अर्थ है किसी चीज़ को जारी रहने से रोकना। दिए गए passage में कहा गया है कि उनके बच्चों की speech therapy बाधित हो गई है। अतः, \'disrupted\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 11.</p>",
                    question_hi: "<p>11. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 11.</p>",
                    options_en: ["<p>end</p>", "<p>transition</p>", 
                                "<p>set</p>", "<p>stagnation</p>"],
                    options_hi: ["<p>end</p>", "<p>transition</p>",
                                "<p>set</p>", "<p>stagnation</p>"],
                    solution_en: "<p>11.(b) <strong>transition</strong><br>&lsquo;Transition&rsquo; means change from one stage or state to another. The given passage states that as a result of Covid-19, the quick transition to digital training has exposed the hazards of student disengagement. Hence, \'transition\' is the most appropriate answer.</p>",
                    solution_hi: "<p>11.(b) <strong>transition</strong><br>&lsquo;Transition&rsquo; का अर्थ है एक चरण या अवस्था से दूसरे चरण या अवस्था में परिवर्तन। दिए गए passage में कहा गया है कि कोविड-19 के परिणामस्वरूप, digital training में त्वरित परिवर्तन ने छात्रों की गैरदिलचस्पी (student disengagement) के खतरों को उजागर किया है। अतः , \'transition\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 12.</p>",
                    question_hi: "<p>12. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 12.</p>",
                    options_en: ["<p>numerous</p>", "<p>fewer</p>", 
                                "<p>particular</p>", "<p>limit</p>"],
                    options_hi: ["<p>numerous</p>", "<p>fewer</p>",
                                "<p>particular</p>", "<p>limit</p>"],
                    solution_en: "<p>12.(a) <strong>numerous</strong><br>&lsquo;Numerous&rsquo; means great in number. The given passage states that as a result of Covid-19, the quick transition to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across numerous courses. Hence, \'numerous\' is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(a) <strong>numerous</strong><br>&lsquo;Numerous&rsquo; का अर्थ है अधिक संख्या में। दिए गए passage में कहा गया है कि कोविड-19 के परिणामस्वरूप, digital training में त्वरित परिवर्तन ने छात्रों की गैरदिलचस्पी के खतरों और कई पाठ्यक्रमों(courses) में छात्रों के कम प्रदर्शन के परिणाम को उजागर किया है। अतः, \'numerous\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 13.</p>",
                    question_hi: "<p>13. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 13.</p>",
                    options_en: ["<p>local</p>", "<p>endemic</p>", 
                                "<p>epidemic</p>", "<p>pandemic</p>"],
                    options_hi: ["<p>local</p>", "<p>endemic</p>",
                                "<p>epidemic</p>", "<p>pandemic</p>"],
                    solution_en: "<p>13.(d) <strong>pandemic</strong><br>&lsquo;Pandemic&rsquo; means a widespread occurrence of an infectious disease over a whole country or the world at a particular time. The given passage states that the latest pandemic has altered the education of millions of people around the world. Hence, \'pandemic\' is the most appropriate answer.</p>",
                    solution_hi: "<p>13.(d) <strong>pandemic</strong><br>&lsquo;Pandemic&rsquo; का अर्थ है किसी विशेष समय में पूरे देश या दुनिया में किसी संक्रामक बीमारी का व्यापक प्रसार। दिए गए passage में कहा गया है कि नवीनतम महामारी ने दुनिया भर के लाखों लोगों की शिक्षा में परिवर्तन लाया है। अतः, \'pandemic\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 14.</p>",
                    question_hi: "<p>14. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 14.</p>",
                    options_en: ["<p>suspend</p>", "<p>resume</p>", 
                                "<p>reopened</p>", "<p>carry on</p>"],
                    options_hi: ["<p>suspend</p>", "<p>resume</p>",
                                "<p>reopened</p>", "<p>carry on</p>"],
                    solution_en: "<p>14.(a) <strong>suspend</strong><br>&lsquo;Suspend&rsquo; means to stop an activity for a while. The given passage states that more than 500 million students, at various levels, around the world were forced to suspend their studies. Hence, \'suspend\' is the most appropriate answer.</p>",
                    solution_hi: "<p>14.(a) <strong>suspend</strong><br>&lsquo;Suspend&rsquo; का अर्थ है किसी गतिविधि को कुछ समय के लिए रोकना। दिए गए passage में बताया गया है कि दुनिया भर में विभिन्न स्तरों पर 500 मिलियन से अधिक छात्रों को अपनी पढ़ाई स्थगित(suspend) करने के लिए मजबूर होना पड़ा। अतः, \'suspend\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 15.</p>",
                    question_hi: "<p>15. <strong>Cloze Test:</strong><br>As a result of Covid-19, the quick (11)______to digital training has exposed the hazards of student disengagement and the resulting consequences of lower student performance across (12) ______courses.<br>The latest (13) ______has altered the education of millions of people around the world. New approaches and improvements in the school system are urgently required. Considering the increasing digital divide, the strategy may exacerbate existing inequality disparities. More than 500 million students, at various levels, around the world were forced to (14) ______ their studies. These changes have caused and will continue to cause some inconvenience, but they will also foster innovation and future reforms inside this education system.<br>During a global Covid-19 pandemic, the \'pile-on effect\' of the coronavirus can have long-term consequences for schooling, notably for the most vulnerable. Children, (15)_______basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. During Covid-19, millions of children whose right to education has already been violated, particularly girls, are exposed to greater health and well-being hazards (both psychosocial and physical).<br>Select the most appropriate option to fill in blank number 15.</p>",
                    options_en: ["<p>which</p>", "<p>whose</p>", 
                                "<p>whom</p>", "<p>who</p>"],
                    options_hi: ["<p>which</p>", "<p>whose</p>",
                                "<p>whom</p>", "<p>who</p>"],
                    solution_en: "<p>15.(b) <strong>whose</strong><br>&lsquo;Whose&rsquo; is an adjective used to indicate possession. The given passage states that children, whose basic, core learning (reading, math, languages, etc.) was weak to begin with, face a significant risk of regression. Hence, &lsquo;whose&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(b) <strong>whose</strong><br>&lsquo;Whose&rsquo; एक adjective है जिसका प्रयोग possession को दर्शाने के लिए किया जाता है। दिए गए passage में कहा गया है कि जिन बच्चों की मूलभूत, मुख्य शिक्षा (reading, math, languages, etc) शुरू से ही कमज़ोर थी, उन्हें प्रतिगमन (regression) के एक महत्वपूर्ण जोखिम का सामना करना पड़ा। अतः, &lsquo;whose&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>