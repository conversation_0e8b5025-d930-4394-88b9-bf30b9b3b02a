<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The average of five numbers, given in a particular order, is 32. The average of the first three numbers is 28, while that of the last three numbers is 34. What is the average of the first two numbers?</p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2381;&#2352;&#2350; &#2350;&#2375;&#2306; &#2342;&#2368; &#2327;&#2312; &#2346;&#2366;&#2306;&#2330; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; 32 &#2361;&#2376;&#2404; &#2346;&#2361;&#2354;&#2368; &#2340;&#2368;&#2344; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; 28 &#2361;&#2376;, &#2340;&#2341;&#2366; &#2309;&#2306;&#2340;&#2367;&#2350; &#2340;&#2368;&#2344; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; 34 &#2361;&#2376;&#2404; &#2346;&#2361;&#2354;&#2368; &#2342;&#2379; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>29</p>\n", "<p>30</p>\n", 
                                "<p>27</p>\n", "<p>28</p>\n"],
                    options_hi: ["<p>29</p>\n", "<p>30</p>\n",
                                "<p>27</p>\n", "<p>28</p>\n"],
                    solution_en: "<p>1.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Average of 5 no&rsquo;s = 32</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Deviation in the average of last 3 no&rsquo;s = +2 &times; 3 = + 6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Deviation in the average of 1st 2 no&rsquo;s = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = - 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So the average of 1st 2 no&rsquo;s = 32 - 3 = 29</span></p>\n",
                    solution_hi: "<p>1.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">5 &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; = 32</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2306;&#2340;&#2367;&#2350; 3 &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; &#2324;&#2360;&#2340; &#2350;&#2375;&#2306; &#2357;&#2367;&#2330;&#2354;&#2344; (deviation) = +2 &times; 3 = +6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2361;&#2354;&#2368; 2 &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2375; &#2324;&#2360;&#2340; &#2350;&#2375;&#2306; &#2357;&#2367;&#2330;&#2354;&#2344; (deviation)= -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = - 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319; &#2346;&#2361;&#2354;&#2368; 2 &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; = 32 - 3 = 29</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Palanquin Dark;\">Select the correct identity from the following options.</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Palanquin Dark;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2360;&#2361;&#2368; &#2360;&#2352;&#2381;&#2357;&#2360;&#2350;&#2367;&#2325;&#2366; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">cos</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">Sin</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", "<p><span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">sin</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">cos</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">tan</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">sec</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", "<p><span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">sec</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">tan</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">cos</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">Sin</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", "<p><span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">sin</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">cos</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n",
                                "<p><span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">tan</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">sec</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", "<p><span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">sec</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">tan</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">On observing the options, we find that option(c) is correct i.e <span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">tan</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">sec</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span></span></p>\n",
                    solution_hi: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2325;&#2366; &#2309;&#2357;&#2354;&#2379;&#2325;&#2344; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350; &#2346;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (c) &#2360;&#2361;&#2368; &#2361;&#2376; &#2309;&#2352;&#2381;&#2341;&#2366;&#2340; <span style=\"font-weight: 400;\">1+</span><span style=\"font-weight: 400;\">tan</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A=</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">sec</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">A</span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-weight: 400;\">3.Simplify :11.5 &minus; [5 + 0.5 &times; (9 &minus; 3 &times; 2)]</span></p>\n",
                    question_hi: "<p><span style=\"font-weight: 400;\">3.&#2360;&#2352;&#2354; &#2325;&#2368;&#2332;&#2367;&#2319; : 11.5 &minus; [5 + 0.5 &times; (9 &minus; 3 &times; 2)]</span></p>\n",
                    options_en: ["<p>5</p>\n", "<p>4</p>\n", 
                                "<p>3</p>\n", "<p>6</p>\n"],
                    options_hi: ["<p>5</p>\n", "<p>4</p>\n",
                                "<p>3</p>\n", "<p>6</p>\n"],
                    solution_en: "<p>3.(a)</p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">11.5 &minus; [5 + 0.5 &times; (9 &minus; 3 &times; 2)]</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr;11.5 - [<span style=\"font-weight: 400;\">5 +0.5&times;(9-6)]</span></span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr;11.5 - [<span style=\"font-weight: 400;\">5 +0.5 &times; 3]</span></span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr;11.5 -[ <span style=\"font-weight: 400;\">5 +1.5]</span></span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr;11.5 - 6.5 = 5</span></p>\n",
                    solution_hi: "<p>3.(a)</p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">11.5 &minus; [5 + 0.5 &times; (9 &minus; 3 &times; 2)]</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr;11.5 - [<span style=\"font-weight: 400;\">5 +0.5 &times; (9-6)]</span></span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr;11.5 -[<span style=\"font-weight: 400;\">5 +0.5 &times; 3]</span>&nbsp;</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr;11.5 - [<span style=\"font-weight: 400;\">5 +1.5]</span></span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&rArr;11.5 - 6.5 = 5</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Arial Unicode MS;\"> Find the principal amount if the interest compounded annually at the rate of 10% for two years is &#8377;4,200.</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Palanquin Dark;\"> &#2351;&#2342;&#2367; &#2342;&#2379; &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; 10% &#2325;&#2368; &#2342;&#2352; &#2360;&#2375; &#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; &#8377;4,200 &#2361;&#2376;, &#2340;&#2379; &#2350;&#2370;&#2354;&#2343;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>&#8377;17,500</p>\n", "<p>&#8377;20,000</p>\n", 
                                "<p>&#8377;24,000</p>\n", "<p>&#8377;16,000</p>\n"],
                    options_hi: ["<p>&#8377;17,500</p>\n", "<p>&#8377;20,000</p>\n",
                                "<p>&#8377;24,000</p>\n", "<p>&#8377;16,000</p>\n"],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the principal be P.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P &times;[ (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math>)&sup2;</span><span style=\"font-family: Palanquin Dark;\"> - 1] = 4200</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P &times; [</span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>100</mn></mfrac></math>- 1] = 4200</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;= 4200 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4200</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></mrow><mn>21</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = &#8377;20,000</span></p>\n",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366;, &#2350;&#2370;&#2354;&#2343;&#2344; P &#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P &times;[(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math>)&sup2;&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> - 1] = 4200</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P &times; [</span><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>100</mn></mfrac></math>- 1] = 4200</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 4200 </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4200</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn></mrow><mn>21</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = &#8377;20,000</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Palanquin Dark;\"> Which of the following numbers is NOT divisible by 75?</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Palanquin Dark;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 75 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>117975</p>\n", "<p>275475</p>\n", 
                                "<p>163750</p>\n", "<p>666600</p>\n"],
                    options_hi: ["<p>117975</p>\n", "<p>275475</p>\n",
                                "<p>163750</p>\n", "<p>666600</p>\n"],
                    solution_en: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">75 = 5&sup2; </span><span style=\"font-family: Palanquin Dark;\">&times; 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">For the given no&rsquo;s to be divisible by 75, </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">First we have to check the divisibility of 3 throughout the options, i.e sum of the digits of the numbers must divisible by 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">117975 = 1+1+7+9+7+5 = 30, which is divisible by 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">275475 = 2+7+5+4+7+5 = 30, which is divisible by 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">163750 = 1+6+3+7+5+0 = 22, which is not divisible by 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">666600 = 6+6+6+6+0+0 = 24, which is divisible by 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, 163750 is the only no which is not divisible by 75</span></p>\n",
                    solution_hi: "<p>5.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">75 = 5&sup2;</span><span style=\"font-family: Palanquin Dark;\"> &times; 3</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2368; &#2327;&#2312; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; 75 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2379;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319;,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2361;&#2354;&#2375; &#2361;&#2350;&#2375;&#2306; &#2360;&#2349;&#2368; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; 3 &#2325;&#2368; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;&#2340;&#2366; &#2325;&#2368; &#2332;&#2366;&#2306;&#2330; &#2325;&#2352;&#2344;&#2368; &#2361;&#2379;&#2327;&#2368; &#2351;&#2366;&#2344;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2375; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2366; &#2351;&#2379;&#2327; 3 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2379;&#2344;&#2366; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">117975 = 1+1+7+9+7+5 = 30, &#2332;&#2379; 3 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">275475 = 2+7+5+4+7+5 = 30, &#2332;&#2379; 3 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">163750 = 1+6+3+7+5+0 = 22, &#2332;&#2379; 3 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">666600 = 6+6+6+6+0+0 = 24, &#2332;&#2379; 3 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319;, 163750 &#2319;&#2325;&#2350;&#2366;&#2340;&#2381;&#2352; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2361;&#2376; &#2332;&#2379; 75 &#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Palanquin Dark;\">Tim is thrice as good a workman as Joya and together they finish a piece of work in 75 days. In how many days will Tim alone finish the work?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Palanquin Dark;\">&#2335;&#2367;&#2350;, &#2332;&#2379;&#2351;&#2366; &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; &#2340;&#2368;&#2344; &#2327;&#2369;&#2344;&#2366; &#2309;&#2343;&#2367;&#2325; &#2325;&#2369;&#2358;&#2354; &#2325;&#2366;&#2352;&#2368;&#2327;&#2352; &#2361;&#2376; &#2324;&#2352; &#2342;&#2379;&#2344;&#2379;&#2306; &#2350;&#2367;&#2354;&#2325;&#2352; &#2319;&#2325; &#2325;&#2366;&#2350; &#2325;&#2379; 75 &#2342;&#2367;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2335;&#2367;&#2350; &#2309;&#2325;&#2375;&#2354;&#2366; &#2313;&#2360; &#2325;&#2366;&#2350; &#2325;&#2379; &#2325;&#2367;&#2340;&#2344;&#2375; &#2342;&#2367;&#2344;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2375;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>25 days</p>\n", "<p>100 days</p>\n", 
                                "<p>50 days</p>\n", "<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>100</mn></mfrac></math>days</span></p>\n"],
                    options_hi: ["<p>25 &#2342;&#2367;&#2344;</p>\n", "<p>100 &#2342;&#2367;&#2344;</p>\n",
                                "<p>50 &#2342;&#2367;&#2344;</p>\n", "<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>100</mn></mfrac></math>&#2342;&#2367;&#2344; </span></p>\n"],
                    solution_en: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the efficiency of Joya = 1 unit</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Then efficiency of Tim = 3 unit</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total work = 75 &times; (3 + 1) = 75 &times; 4 = 300 unit</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Time taken by Tim alone to complete the work =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>3</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 100 days</span></p>\n",
                    solution_hi: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366;, &#2332;&#2379;&#2351;&#2366; &#2325;&#2368; &#2342;&#2325;&#2381;&#2359;&#2340;&#2366; = 1 &#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2348; &#2335;&#2367;&#2350; &#2325;&#2368; &#2342;&#2325;&#2381;&#2359;&#2340;&#2366; = 3 &#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2369;&#2354; &#2325;&#2366;&#2352;&#2381;&#2351; = 75 &times; (3 + 1) = 75 &times; 4 = 300 &#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2325;&#2375;&#2354;&#2375; &#2335;&#2367;&#2350; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2379; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2354;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2360;&#2350;&#2351; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 100 &#2342;&#2367;&#2344;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Palanquin Dark;\">While selling an item, a businessman allows a 40% discount on the marked price and incurs a loss of 30%. If the item is sold at the marked price, profit will be _______.</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Palanquin Dark;\">&#2325;&#2367;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2379; &#2313;&#2360;&#2325;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; 40% &#2325;&#2368; &#2331;&#2370;&#2335; &#2342;&#2375;&#2340;&#2375; &#2361;&#2369;&#2319; &#2348;&#2375;&#2330;&#2344;&#2375; &#2360;&#2375; &#2319;&#2325; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368; &#2325;&#2379; 30% &#2325;&#2368; &#2361;&#2366;&#2344;&#2367; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2357;&#2361; &#2311;&#2360;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; &#2361;&#2368; &#2348;&#2375;&#2330;&#2340;&#2366; &#2340;&#2379; &#2313;&#2360;&#2375; &#2325;&#2367;&#2340;&#2344;&#2375; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2354;&#2366;&#2349; &#2361;&#2379;&#2340;&#2366;?</span></p>\n",
                    options_en: ["<p>10%</p>\n", "<p>16%</p>\n", 
                                "<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</span></p>\n", "<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</span></p>\n"],
                    options_hi: ["<p>10%</p>\n", "<p>16%</p>\n",
                                "<p><span style=\"font-family: Palanquin Dark;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</span></p>\n", "<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math> %</span></p>\n"],
                    solution_en: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let MP of an item = &#8377;100</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SP of an item = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = &#8377;60</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">CP of an item = 60 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>70</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = &#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>7</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Since the item is sold at MP. So,. New SP = &#8377;100</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">CP : SP = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> : 100 = 600 : 700 or 6 : 7</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>-</mo><mn>6</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>6</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">= 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> %</span></p>\n",
                    solution_hi: "<p>7.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366;, &#2325;&#2367;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = &#8377;100</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2367;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 100 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> = &#8377;60</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 60 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>70</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = &#8377;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>7</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2330;&#2370;&#2306;&#2325;&#2367; &#2357;&#2360;&#2381;&#2340;&#2369; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; &#2348;&#2375;&#2330;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404; </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319;, &#2344;&#2351;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = &#8377;100</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; : &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> : 100 = 600 : 700 &#2351;&#2366; 6 : 7</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>-</mo><mn>6</mn></mrow><mn>6</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>6</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">= 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> %</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Palanquin Dark;\">If P(x) =<span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">- 8)</span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">x </span><span style=\"font-weight: 400;\">+ 1)</span>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> and Q(x) = &nbsp;<span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\">&sup3; +</span><span style=\"font-weight: 400;\"> 1)</span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">x -</span><span style=\"font-weight: 400;\"> 2)</span></span><span style=\"font-family: Palanquin Dark;\">the LCM of P(x) and Q(x) is:</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Palanquin Dark;\">&#2351;&#2342;&#2367; P(x) = <span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\">&sup3; </span><span style=\"font-weight: 400;\">- 8)</span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">x </span><span style=\"font-weight: 400;\">+ 1) </span></span><span style=\"font-family: Palanquin Dark;\">&#2324;&#2352; Q(x) = <span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">x</span><span style=\"font-weight: 400;\">&sup3; +</span><span style=\"font-weight: 400;\"> 1)</span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">x -</span><span style=\"font-weight: 400;\"> 2)</span></span><span style=\"font-family: Palanquin Dark;\"> &#2361;&#2376;, &#2340;&#2379; P(x) &#2324;&#2352; Q(x) &#2325;&#2366; &#2354;&#2328;&#2369;&#2340;&#2381;&#2340;&#2350; &#2360;&#2350;&#2366;&#2346;&#2357;&#2352;&#2381;&#2340;&#2381;&#2351; (LCM) &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+2x+4)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+ 4x+1)</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", "<p><span style=\"font-weight: 400;\">(x+1)</span><span style=\"font-weight: 400;\">(x-2)</span><span style=\"font-weight: 400;\">(x&sup2;</span><span style=\"font-weight: 400;\">+2x+4)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">-x+1)</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", 
                                "<p><span style=\"font-weight: 400;\">(x+1)</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">(x-2)</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+2x+4)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+4x+1)</span></p>\n", "<p><span style=\"font-weight: 400;\">(x-2)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(x+1)</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+2x+4)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+ 4x+1)</span></p>\n", "<p><span style=\"font-weight: 400;\">(x+1)</span><span style=\"font-weight: 400;\">(x-2)</span><span style=\"font-weight: 400;\">(x&sup2;</span><span style=\"font-weight: 400;\">+2x+4)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">-x+1)</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n",
                                "<p><span style=\"font-weight: 400;\">(x+1)</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">(x-2)</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+2x+4)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(x</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">+4x+1)</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n", "<p><span style=\"font-weight: 400;\">(x-2)</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">(x+1)</span><span style=\"font-family: Palanquin Dark;\"> </span></p>\n"],
                    solution_en: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">p(x) = (x&sup3;</span><span style=\"font-family: Palanquin Dark;\">- 8) </span><span style=\"font-family: Palanquin Dark;\">(x + 1) = (x - 2) (</span><span style=\"font-family: Palanquin Dark;\"> x&sup2; + 2x + 4) (x +1)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Q(x) = (x&sup3;</span><span style=\"font-family: Palanquin Dark;\"> +1) (x - 2) = (x + 1) (x&sup2;</span><span style=\"font-family: Palanquin Dark;\"> - x + 1) (x - 2)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">LCM of P(x) and Q(x) = (x + 1) (x - 2) (x&sup2;</span><span style=\"font-family: Palanquin Dark;\"> + 2x + 4) (x&sup2;</span><span style=\"font-family: Palanquin Dark;\"> - x + 1)</span></p>\n",
                    solution_hi: "<p>8.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">p(x) = (x&sup3;</span><span style=\"font-family: Palanquin Dark;\">- 8) </span><span style=\"font-family: Palanquin Dark;\">(x + 1) = (x - 2) (</span><span style=\"font-family: Palanquin Dark;\"> x&sup2; + 2x + 4) (x +1)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Q(x) = (x&sup3;</span><span style=\"font-family: Palanquin Dark;\"> +1) (x - 2) = (x + 1) (x&sup2;</span><span style=\"font-family: Palanquin Dark;\"> -x + 1) (x - 2)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P(x) &#2324;&#2352; Q(x) &#2325;&#2366; LCM= (x + 1) (x - 2) (x&sup2;</span><span style=\"font-family: Palanquin Dark;\"> + 2x + 4) (</span><span style=\"font-family: Palanquin Dark;\"> x&sup2; - x + 1)</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p><strong>&nbsp;</strong><span style=\"font-weight: 400;\">ABCD is a square of side 21 cm. A circle is inscribed in the square, which touches the sides of the square at P, Q , R , and S as shown below in the figure . What is the area (in </span><span style=\"font-weight: 400;\">cm</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\">) of the non-shaded region? [figure is not drawn to scale and take &pi; </span><span style=\"font-weight: 400;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math><span style=\"font-weight: 400;\">]</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image4.png\" width=\"198\" height=\"200\"></p>\n",
                    question_hi: "<p><span style=\"font-weight: 400;\">ABCD, 21 cm &#2349;&#2369;&#2332;&#2366; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325; &#2357;&#2352;&#2381;&#2327; &#2361;&#2376;&#2404; &#2357;&#2352;&#2381;&#2327; &#2350;&#2375;&#2306; &#2319;&#2325; &#2357;&#2371;&#2340;&#2381;&#2340; &#2309;&#2306;&#2325;&#2367;&#2340; &#2361;&#2376;, &#2332;&#2379; &#2357;&#2352;&#2381;&#2327; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366;&#2323;&#2306; &#2325;&#2379; P, Q, R &#2324;&#2352; S &#2346;&#2352; &#2360;&#2381;&#2346;&#2352;&#2381;&#2358; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2376;&#2360;&#2366; &#2325;&#2367; &#2344;&#2368;&#2330;&#2375; &#2330;&#2367;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2342;&#2367;&#2326;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2327;&#2376;&#2352;-&#2331;&#2366;&#2351;&#2366;&#2306;&#2325;&#2367;&#2340; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; (</span><span style=\"font-weight: 400;\">cm</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306;) &#2325;&#2368; &#2327;&#2339;&#2344;&#2366; &#2325;&#2352;&#2375;&#2306;&#2404; [&#2310;&#2325;&#2371;&#2340;&#2367; &#2346;&#2376;&#2350;&#2366;&#2344;&#2375; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352; &#2344;&#2361;&#2368;&#2306; &#2348;&#2344;&#2366;&#2312; &#2327;&#2312; &#2361;&#2376; &#2324;&#2352; &pi;</span><span style=\"font-weight: 400;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-weight: 400;\">&nbsp;&#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2352;&#2375;&#2306;&#2404;]</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image4.png\" width=\"201\" height=\"203\"></p>\n",
                    options_en: ["<p>88.4</p>\n", "<p>84.6</p>\n", 
                                "<p>90.7</p>\n", "<p>94.5</p>\n"],
                    options_hi: ["<p>88.4</p>\n", "<p>84.6</p>\n",
                                "<p>90.7</p>\n", "<p>94.5</p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Area of square = 21 &times; 21 = 441<span style=\"font-weight: 400;\">cm</span><span style=\"font-weight: 400;\">&sup2;</span></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Length of the side of square = diameter of the circle </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Area of circle = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>693</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 346.5 <span style=\"font-weight: 400;\">cm</span><span style=\"font-weight: 400;\">&sup2;</span></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, the area of non- shaded region = 441 - 346.5 = 94.5 <span style=\"font-weight: 400;\">cm</span><span style=\"font-weight: 400;\">&sup2;</span></span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2357;&#2352;&#2381;&#2327; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = 21 &times; 21 = 441 <span style=\"font-weight: 400;\">cm</span><span style=\"font-weight: 400;\">&sup2;</span></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2357;&#2352;&#2381;&#2327; &#2325;&#2368; &#2349;&#2369;&#2332;&#2366; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; = &#2357;&#2371;&#2340;&#2381;&#2340; &#2325;&#2366; &#2357;&#2381;&#2351;&#2366;&#2360;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2357;&#2371;&#2340;&#2381;&#2340; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>693</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 346.5 <span style=\"font-weight: 400;\">cm</span><span style=\"font-weight: 400;\">&sup2;</span></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;: &#2309;&#2331;&#2366;&#2351;&#2366;&#2306;&#2325;&#2367;&#2340; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = 441 - 346.5 = 94.5 <span style=\"font-weight: 400;\">cm</span><span style=\"font-weight: 400;\">&sup2;</span></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Palanquin Dark;\">The area of two similar triangles XYZ and ABC are 361<span style=\"font-weight: 400;\">cm&sup2;</span>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> and 225 <span style=\"font-weight: 400;\">cm&sup2;</span> </span><span style=\"font-family: Arial Unicode MS;\">, respectively. If the longest side of the larger &#8710; XYZ be 38 cm, then what is the length (in cm) of the longest side of the smaller &#8710; ABC?</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: Palanquin Dark;\">&#2342;&#2379; &#2360;&#2350;&#2352;&#2370;&#2346; &#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332; XYZ &#2324;&#2352; ABC &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; 361<span style=\"font-weight: 400;\">cm&sup2;</span>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> &#2324;&#2352; 225<span style=\"font-weight: 400;\">cm&sup2;</span>&nbsp;</span><span style=\"font-family: Arial Unicode MS;\"> &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2348;&#2337;&#2364;&#2375; &#8710;XYZ &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2354;&#2306;&#2348;&#2368; &#2349;&#2369;&#2332;&#2366; 38 cm &#2361;&#2376;, &#2340;&#2379; &#2331;&#2379;&#2335;&#2368; &#8710;ABC &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2354;&#2306;&#2348;&#2368; &#2349;&#2369;&#2332;&#2366; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; (cm &#2350;&#2375;&#2306;) &#2325;&#2381;&#2351;&#2366; &#2361;&#2379;&#2327;&#2368;?</span></p>\n",
                    options_en: ["<p>35</p>\n", "<p>28</p>\n", 
                                "<p>32</p>\n", "<p>30</p>\n"],
                    options_hi: ["<p>35</p>\n", "<p>28</p>\n",
                                "<p>32</p>\n", "<p>30</p>\n"],
                    solution_en: "<p>10.(d)</p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Since &Delta;</span><span style=\"font-family: Arial Unicode MS;\">XYZ &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>~</mo></math> &nbsp;&Delta;</span><span style=\"font-family: Arial Unicode MS;\">ABC,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>a</mi><mi>r</mi><mo>(</mo><mi>X</mi><mi>Y</mi><mi>Z</mi><mo>)</mo></mrow><mrow><mi>a</mi><mi>r</mi><mo>(</mo><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Arial Unicode MS;\"> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math>)&sup2;&nbsp;</span><span style=\"font-family: Arial Unicode MS;\"> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mi>Z</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math>)&sup2;</span><span style=\"font-family: Arial Unicode MS;\">= (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Z</mi><mi>X</mi></mrow><mrow><mi>C</mi><mi>A</mi></mrow></mfrac></math>)&sup2;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image2.png\" width=\"300\" height=\"175\"></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Let the length of the longest side of the smaller &#8710; ABC be x cm</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\"> </span><span style=\"font-family: Arial Unicode MS;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>361</mn><mn>225</mn></mfrac></math>=(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>38</mn><mi>x</mi></mfrac></math>)&sup2;</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\"> </span><span style=\"font-family: Arial Unicode MS;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>15</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>38</mn><mi>x</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\"> x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>38</mn></mrow><mn>19</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Arial Unicode MS;\"> = 30 cm</span></p>\n",
                    solution_hi: "<p>10.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2330;&#2370;&#2305;&#2325;&#2367; <span style=\"font-family: Arial Unicode MS;\">&Delta;</span></span><span style=\"font-family: Palanquin Dark;\">XYZ<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>~</mo></math> <span style=\"font-family: Arial Unicode MS;\">&Delta;</span></span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">ABC,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>X</mi><mi>Y</mi><mi>Z</mi><mo>)</mo><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mo>(</mo><mi>A</mi><mi>B</mi><mi>C</mi><mo>)</mo><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> =(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mi>Y</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math>)<span style=\"font-family: Arial Unicode MS;\">&sup2;</span> </span><span style=\"font-family: Palanquin Dark;\"> =(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Y</mi><mi>Z</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math>)<span style=\"font-family: Arial Unicode MS;\">&sup2;</span> </span><span style=\"font-family: Palanquin Dark;\"> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Z</mi><mi>X</mi></mrow><mrow><mi>C</mi><mi>A</mi></mrow></mfrac></math>)<span style=\"font-family: Arial Unicode MS;\">&sup2;</span></span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image2.png\" width=\"254\" height=\"148\"></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">&#2350;&#2366;&#2344;&#2366; &#8710;ABC &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2354;&#2306;&#2348;&#2368; &#2349;&#2369;&#2332;&#2366; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; x cm &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\"> </span><span style=\"font-family: Arial Unicode MS;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>361</mn><mn>225</mn></mfrac></math>= (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>38</mn><mi>x</mi></mfrac></math>)&sup2;</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\"> </span><span style=\"font-family: Arial Unicode MS;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>15</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>38</mn><mi>x</mi></mfrac></math></span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\"> x =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>38</mn></mrow><mn>19</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Arial Unicode MS;\">= 30 cm</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Arial Unicode MS;\"> \"The difference between the CI and the SI on a sum of money lent for 2 years at 20% interest per annum is &#8377; 80. The sum is:\"</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Palanquin Dark;\"> \"2 &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2354;&#2367;&#2319; 20% &#2325;&#2368; &#2348;&#2381;&#2351;&#2366;&#2332; &#2342;&#2352; &#2360;&#2375; &#2313;&#2343;&#2366;&#2352; &#2342;&#2368; &#2327;&#2312; &#2343;&#2344;&#2352;&#2366;&#2358;&#2367; &#2346;&#2352; &#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; &#2324;&#2352; &#2360;&#2366;&#2343;&#2366;&#2352;&#2339; &#2348;&#2381;&#2351;&#2366;&#2332; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352; &#8377; 80 &#2361;&#2376;&#2404; &#2357;&#2361; &#2343;&#2344;&#2352;&#2366;&#2358;&#2367; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; </span></p>\n",
                    options_en: ["<p>&#8377;1,200</p>\n", "<p>&#8377;2,000</p>\n", 
                                "<p>&#8377;1,500</p>\n", "<p>&#8377;1,000</p>\n"],
                    options_hi: ["<p>&#8377;1,200</p>\n", "<p>&#8377;2,000</p>\n",
                                "<p>&#8377;1,500</p>\n", "<p>&#8377;1,000</p>\n"],
                    solution_en: "<p>11.(b)</p>\r\n<p><span style=\"font-weight: 400;\">(CI - SI) </span>for 2 yrs at 20% per annum =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> = 4% of principal</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">80 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">P</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = &#8377;2,000 </span></p>\n",
                    solution_hi: "<p>11.(b)</p>\r\n<p><span style=\"font-weight: 400;\">(&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2348;&#2381;&#2351;&#2366;&#2332; - &#2360;&#2366;&#2343;&#2366;&#2352;&#2339; &#2348;&#2381;&#2351;&#2366;&#2332;) </span>2 &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2354;&#2367;&#2319; 20% &#2346;&#2381;&#2352;&#2340;&#2367; &#2357;&#2352;&#2381;&#2359; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> = &#2350;&#2370;&#2354;&#2343;&#2344; (P) &#2325;&#2366; 4%</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">80 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">P</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">P = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>80</mn></mrow><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = &#8377;2,000 </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Palanquin Dark;\">The price of a book is first increased by 8% and later decreased by 8%. What is the percentage change in the price of the book?</span></p>\n",
                    question_hi: "<p>12. <span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2325;&#2368; &#2350;&#2370;&#2354;&#2381;&#2351; &#2350;&#2375;&#2306; &#2346;&#2361;&#2354;&#2375; 8% &#2325;&#2368; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2325;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376; &#2324;&#2352; &#2348;&#2366;&#2342; &#2350;&#2375;&#2306; 8% &#2325;&#2368; &#2325;&#2350;&#2368; &#2325;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2325;&#2375; &#2350;&#2370;&#2354;&#2381;&#2351; &#2350;&#2375;&#2306; &#2325;&#2367;&#2340;&#2344;&#2375; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2361;&#2369;&#2310; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>No change</p>\n", "<p>decrease by 0.64%</p>\n", 
                                "<p>increase by 0.064%</p>\n", "<p>increase by 0.64%</p>\n"],
                    options_hi: ["<p>&#2325;&#2379;&#2312; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2344;&#2361;&#2368;&#2306;</p>\n", "<p>0.64% &#2325;&#2368; &#2325;&#2350;&#2368;</p>\n",
                                "<p>0.064% &#2325;&#2368; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</p>\n", "<p>0.64% &#2325;&#2368; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</p>\n"],
                    solution_en: "<p>12.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Net percentage change in the price of the book = 8 - 8 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = - 0.64 % </span></p>\n",
                    solution_hi: "<p>12.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2325;&#2375; &#2350;&#2370;&#2354;&#2381;&#2351; &#2350;&#2375;&#2306; &#2358;&#2369;&#2342;&#2381;&#2343; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; = 8 - 8 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn></mrow><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> = - 0.64 % </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Palanquin Dark;\">A book with a marked price of &#8377;600 is available at a discount of 18%. What is the discount given?</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Arial Unicode MS;\">&#8377;600 &#2325;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2357;&#2366;&#2354;&#2368; &#2319;&#2325; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; 18% &#2325;&#2368; &#2331;&#2370;&#2335; &#2346;&#2352; &#2313;&#2346;&#2354;&#2348;&#2381;&#2343; &#2361;&#2376;&#2404; &#2325;&#2367;&#2340;&#2344;&#2375; &#2325;&#2368; &#2331;&#2370;&#2335; &#2342;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>&#8377;180</p>\n", "<p>&#8377;110</p>\n", 
                                "<p>&#8377;108</p>\n", "<p>&#8377;72</p>\n"],
                    options_hi: ["<p>&#8377;180</p>\n", "<p>&#8377;110</p>\n",
                                "<p>&#8377;108</p>\n", "<p>&#8377;72</p>\n"],
                    solution_en: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">MP of a book = &#8377;600</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">SP of a book = 600 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>41</mn></mrow><mn>50</mn></mfrac></math></span><span style=\"font-family: Arial Unicode MS;\">&nbsp;= &#8377;492</span></p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Discount given on book = 600 - 492 = &#8377;108 </span></p>\n",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2325;&#2366; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = &#8377;600</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2325;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 600 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&nbsp;</mo><mn>41</mn></mrow><mn>50</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = &#8377;492</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325; &#2346;&#2352; &#2342;&#2368; &#2327;&#2312; &#2331;&#2370;&#2335; = 600 - 492 = &#8377;108 </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Palanquin Dark;\">The price of a pen is first increased by 15% and later on the price was decreased by 20% due to reduction in sales. Find the net percentage change in the final price of a pen.</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2346;&#2375;&#2344; &#2325;&#2368; &#2325;&#2368;&#2350;&#2340; &#2350;&#2375;&#2306; &#2346;&#2361;&#2354;&#2375; 15% &#2325;&#2368; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2325;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376; &#2324;&#2352; &#2348;&#2366;&#2342; &#2350;&#2375;&#2306; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368; &#2350;&#2375;&#2306; &#2325;&#2350;&#2368; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2325;&#2368;&#2350;&#2340; &#2350;&#2375;&#2306; 20% &#2325;&#2368; &#2325;&#2350;&#2368; &#2325;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404; &#2346;&#2375;&#2344; &#2325;&#2375; &#2309;&#2306;&#2340;&#2367;&#2350; &#2350;&#2370;&#2354;&#2381;&#2351; &#2350;&#2375;&#2306; &#2358;&#2369;&#2342;&#2381;&#2343; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>25%</p>\n", "<p>8%</p>\n", 
                                "<p>&minus;8%</p>\n", "<p>&minus;16%</p>\n"],
                    options_hi: ["<p>25%</p>\n", "<p>8%</p>\n",
                                "<p>&minus;8%</p>\n", "<p>&minus;16%</p>\n"],
                    solution_en: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Arial Unicode MS;\">Net percentage change in the price of pen = 15 - 20 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Arial Unicode MS;\"> = -5 - 3 = - 8%</span></p>\n",
                    solution_hi: "<p>14.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2346;&#2375;&#2344; &#2325;&#2368; &#2325;&#2368;&#2350;&#2340; &#2350;&#2375;&#2306; &#2358;&#2369;&#2342;&#2381;&#2343; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344; = 15 - 20 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = -5 - 3 = - 8%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Palanquin Dark;\"> Simplify the following expression : </span><span style=\"font-family: Palanquin Dark;\"><span style=\"font-weight: 400;\">(0.14&times;0.14)-(2&times;0.14&times;5.14)+(5.14&times;5.14)</span></span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Palanquin Dark;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2357;&#2381;&#2351;&#2306;&#2332;&#2325; &#2325;&#2379; &#2360;&#2352;&#2354; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><span style=\"font-weight: 400;\">(0.14&times;0.14)-(2&times;0.14&times;5.14)+(5.14&times;5.14)</span></span></p>\n",
                    options_en: ["<p>5.18</p>\n", "<p>4</p>\n", 
                                "<p>16</p>\n", "<p>25</p>\n"],
                    options_hi: ["<p>5.18</p>\n", "<p>4</p>\n",
                                "<p>16</p>\n", "<p>25</p>\n"],
                    solution_en: "<p>15.(d)</p>\r\n<p><span style=\"font-weight: 400;\">(0.14 &times; 0.14) - (2 &times; 0.14 &times; 5.14) + (5.14 &times; 5.14)</span></p>\r\n<p><span style=\"font-weight: 400;\">0.1</span><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> - (2&times;0.14&times;5.14) + </span><span style=\"font-weight: 400;\">5.1</span><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">&sup2;</span></p>\r\n<p><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">0.14 - 5.14</span><span style=\"font-weight: 400;\">)</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> = 25</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>15.(d)</p>\r\n<p><span style=\"font-weight: 400;\">(0.14 &times; 0.14) - (2 &times; 0.14 &times; 5.14) + (5.14 &times; 5.14)</span></p>\r\n<p><span style=\"font-weight: 400;\">0.1</span><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> - (2&times;0.14&times;5.14) + </span><span style=\"font-weight: 400;\">5.1</span><span style=\"font-weight: 400;\">4</span><span style=\"font-weight: 400;\">&sup2;</span></p>\r\n<p><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">0.14 - 5.14</span><span style=\"font-weight: 400;\">)</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> = </span><span style=\"font-weight: 400;\">5</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> = 25</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16.<span style=\"font-family: Palanquin Dark;\"> An athlete crosses a distance of 3600 m in 12 minutes. What is his speed (in km/h)?</span></p>\n",
                    question_hi: "<p>16. <span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2319;&#2341;&#2354;&#2368;&#2335; 12 &#2350;&#2367;&#2344;&#2335; &#2350;&#2375;&#2306; 3600 m &#2325;&#2368; &#2342;&#2370;&#2352;&#2368; &#2346;&#2366;&#2352; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2360;&#2325;&#2368; &#2330;&#2366;&#2354; (km/h &#2350;&#2375;&#2306;) &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; </span></p>\n",
                    options_en: ["<p>15</p>\n", "<p>17</p>\n", 
                                "<p>18</p>\n", "<p>16</p>\n"],
                    options_hi: ["<p>15</p>\n", "<p>17</p>\n",
                                "<p>18</p>\n", "<p>16</p>\n"],
                    solution_en: "<p>16.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Speed of the athlete =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3600</mn><mrow><mn>12</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>60</mn></mrow></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\">= 5 m/sec = 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 18 km/hr</span></p>\n",
                    solution_hi: "<p>16.(c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2319;&#2341;&#2354;&#2368;&#2335; &#2325;&#2368; &#2330;&#2366;&#2354; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3600</mn><mrow><mn>12</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>60</mn></mrow></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 5 m/sec = 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 18 km/hr</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. <span style=\"font-family: Palanquin Dark;\">The base of a hemispherical bowl has a circumference of 132 cm. Find the capacity of the bowl.(&pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">)</span></p>\n",
                    question_hi: "<p>17. <span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2366;&#2325;&#2366;&#2352; &#2325;&#2335;&#2379;&#2352;&#2375; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2325;&#2368; &#2346;&#2352;&#2367;&#2343;&#2367; 132 cm &#2361;&#2376;&#2404; &#2325;&#2335;&#2379;&#2352;&#2375; &#2325;&#2368; &#2325;&#2381;&#2359;&#2350;&#2340;&#2366; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; (&pi; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2375;&#2306;)</span></p>\n",
                    options_en: ["<p>18240 cm&sup3;</p>\n", "<p>19404 cm&sup3;</p>\n", 
                                "<p>19708 cm&sup3;</p>\n", "<p>16540 cm&sup3;</p>\n"],
                    options_hi: ["<p>18240 cm&sup3;</p>\n", "<p>19404 cm&sup3;</p>\n",
                                "<p>19708 cm&sup3;</p>\n", "<p>16540 cm&sup3;</p>\n"],
                    solution_en: "<p>17.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Circumference of the base of hemispherical bowl = 132 cm</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2 &times;&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; r = 132</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>7</mn></mfrac></math>&nbsp;&times; r = 132</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">r = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>132</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn></mrow><mn>44</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 21 cm</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">The capacity of the bowl = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\">&times; 21 &times; 21 &times; 21 = 19404 cm&sup3;&nbsp;</span></p>\n",
                    solution_hi: "<p>17.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2352;&#2381;&#2343;&#2327;&#2379;&#2354;&#2366;&#2325;&#2366;&#2352; &#2325;&#2335;&#2379;&#2352;&#2375; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2325;&#2368; &#2346;&#2352;&#2367;&#2343;&#2367; = 132 cm</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">2 &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\">&times; r = 132</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>7</mn></mfrac></math>&nbsp;&times; r = 132</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">r = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>132</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7</mn></mrow><mn>44</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 21 cm</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2335;&#2379;&#2352;&#2375; &#2325;&#2368; &#2343;&#2366;&#2352;&#2367;&#2340;&#2366; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\">&times; 21 &times; 21 &times; 21 = 19404 cm&sup3;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. <span style=\"font-family: Palanquin Dark;\">Which of the following ratios is the smallest?</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">3 : 4, 1 : 2 , 2 : 5, 1 : 3</span></p>\n",
                    question_hi: "<p>18. <span style=\"font-family: Palanquin Dark;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2360;&#2348;&#2360;&#2375; &#2331;&#2379;&#2335;&#2366; &#2361;&#2376;?</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">3 : 4, 1 : 2 , 2 : 5, 1 : 3</span></p>\n",
                    options_en: ["<p>1:3</p>\n", "<p>2:5</p>\n", 
                                "<p>3:4</p>\n", "<p>1:2</p>\n"],
                    options_hi: ["<p>1:3</p>\n", "<p>2:5</p>\n",
                                "<p>3:4</p>\n", "<p>1:2</p>\n"],
                    solution_en: "<p>18.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A : B : C : D = </span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><mn>5</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">LCM of (4, 2, 5, 3) = 60</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, A : B : C : D = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">&times; 60 :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">&times; 60 :<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 60 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 60</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> A : B : C : D = 45 : 30 : 24 : 20</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, the smallest ratio among all of them = D =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span></p>\n",
                    solution_hi: "<p>18.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A : B : C : D = </span><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>,</mo><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn></mrow><mn>5</mn></mfrac><mo>,</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><span style=\"font-weight: 400;\">(4, 2, 5, 3) </span>&#2325;&#2366; &#2354;&#2328;&#2369;&#2340;&#2381;&#2340;&#2350; &#2360;&#2350;&#2366;&#2346;&#2357;&#2352;&#2381;&#2340;&#2381;&#2351; (LCM) = 60</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2379;, A : B : C : D =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">&times; 60 :<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times; 60 :<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 60 : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> &times; 60</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> A : B : C : D = 45 : 30 : 24 : 20</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;: &#2313;&#2344; &#2360;&#2349;&#2368; &#2350;&#2375;&#2306; &#2360;&#2348;&#2360;&#2375; &#2331;&#2379;&#2335;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; = D =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. <span style=\"font-family: Palanquin Dark;\">Solve </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>5</mn><mo>.</mo><mn>8</mn></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow></mfrac></math></p>\n",
                    question_hi: "<p>19. <span style=\"font-family: Palanquin Dark;\">&#2361;&#2354; &#2325;&#2352;&#2375;&#2306;:&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>5</mn><mo>.</mo><mn>8</mn></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow></mfrac></math></span></p>\n",
                    options_en: ["<p>15.1</p>\n", "<p>15.3</p>\n", 
                                "<p>15.2</p>\n", "<p>15.4</p>\n"],
                    options_hi: ["<p>15.1</p>\n", "<p>15.3</p>\n",
                                "<p>15.2</p>\n", "<p>15.4</p>\n"],
                    solution_en: "<p>19.(d)</p>\r\n<p>&rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>5</mn><mo>.</mo><mn>8</mn></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow></mfrac></math></p>\r\n<p>&rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>5</mn><mo>.</mo><msup><mn>8</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>)</mo></mrow><mrow><mo>(</mo><mn>6</mn><mo>.</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>5</mn><mo>.</mo><msup><mn>8</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>)</mo></mrow></mfrac></math></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;6.9 + 2.7 + 5.8&nbsp; = 15.4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>19.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p>&rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>5</mn><mo>.</mo><mn>8</mn></mrow><mrow><msup><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo></mrow></mfrac></math></p>\r\n<p>&rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>5</mn><mo>.</mo><msup><mn>8</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>)</mo></mrow><mrow><mo>(</mo><mn>6</mn><mo>.</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>.</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mn>5</mn><mo>.</mo><msup><mn>8</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>7</mn><mo>)</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>5</mn><mo>.</mo><mn>8</mn><mo>)</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>9</mn><mo>)</mo><mo>)</mo></mrow></mfrac></math></p>\r\n<p><span style=\"font-weight: 400;\">&rArr;6.9 + 2.7 + 5.8&nbsp; = 15.4</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. <span style=\"font-family: Palanquin Dark;\">Study the given table and answer the questions that follow. </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">The following table given the month-wise number of different types of scooters produced by a company during the first six months of 1992. </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image5.png\" width=\"597\" height=\"156\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">In which month did the company produce the lowest aggregate number of scooters of all the four types taken together?</span></p>\n",
                    question_hi: "<p>20. <span style=\"font-family: Palanquin Dark;\">&#2342;&#2368; &#2327;&#2312; &#2340;&#2366;&#2354;&#2367;&#2325;&#2366; &#2325;&#2366; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2379;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2340;&#2352; &#2342;&#2375;&#2306;&#2404; </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2340;&#2366;&#2354;&#2367;&#2325;&#2366; &#2350;&#2375;&#2306; 1992 &#2325;&#2375; &#2346;&#2361;&#2354;&#2368; &#2331;&#2350;&#2366;&#2361;&#2368; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2319;&#2325; &#2325;&#2306;&#2346;&#2344;&#2368; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2360;&#2381;&#2325;&#2370;&#2335;&#2352;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image5.png\" width=\"601\" height=\"157\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2367;&#2360; &#2350;&#2361;&#2368;&#2344;&#2375; &#2360;&#2349;&#2368; &#2330;&#2366;&#2352; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2360;&#2381;&#2325;&#2370;&#2335;&#2352;&#2379;&#2306; &#2325;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2360;&#2348;&#2360;&#2375; &#2325;&#2350; &#2361;&#2369;&#2310;?</span></p>\n",
                    options_en: ["<p>June</p>\n", "<p>March</p>\n", 
                                "<p>February</p>\n", "<p>January</p>\n"],
                    options_hi: ["<p>&#2332;&#2370;&#2344;</p>\n", "<p>&#2350;&#2366;&#2352;&#2381;&#2330;</p>\n",
                                "<p>&#2347;&#2352;&#2357;&#2352;&#2368;</p>\n", "<p>&#2332;&#2344;&#2357;&#2352;&#2368;</p>\n"],
                    solution_en: "<p>20.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">On studying the table, we find that the company produces the lowest aggregate no of scooter in the month of June.</span></p>\n",
                    solution_hi: "<p>20.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366; &#2325;&#2366; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2325;&#2352;&#2344;&#2375; &#2346;&#2352;, &#2361;&#2350; &#2346;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2325;&#2306;&#2346;&#2344;&#2368; &#2332;&#2370;&#2344; &#2325;&#2375; &#2350;&#2361;&#2368;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2348;&#2360;&#2375; &#2325;&#2350; &#2325;&#2369;&#2354; &#2360;&#2381;&#2325;&#2370;&#2335;&#2352; &#2325;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. <span style=\"font-family: Palanquin Dark;\">A shopkeeper sells rice at cost price but uses a faulty weighing machine and thus gains a profit of 25%. Find how many grams rice he is giving in 1 kg ?</span></p>\n",
                    question_hi: "<p>21. <span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352; &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; &#2330;&#2366;&#2357;&#2354; &#2348;&#2375;&#2330;&#2340;&#2366; &#2361;&#2376; &#2354;&#2375;&#2325;&#2367;&#2344; &#2357;&#2361; &#2327;&#2354;&#2340; &#2357;&#2332;&#2344; &#2340;&#2380;&#2354;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2350;&#2358;&#2368;&#2344; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2311;&#2360; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; 25% &#2325;&#2366; &#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2325;&#2367; &#2357;&#2361; 1 &#2325;&#2367;&#2354;&#2379; &#2350;&#2375;&#2306; &#2325;&#2367;&#2340;&#2344;&#2375; &#2327;&#2381;&#2352;&#2366;&#2350; &#2330;&#2366;&#2357;&#2354; &#2342;&#2375; &#2352;&#2361;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>800gm</p>\n", "<p>700gm</p>\n", 
                                "<p>900gm</p>\n", "<p>750 gm</p>\n"],
                    options_hi: ["<p>800gm</p>\n", "<p>700gm</p>\n",
                                "<p>900gm</p>\n", "<p>750 gm</p>\n"],
                    solution_en: "<p>21.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the CP of rice for the shopkeeper be 4 units. </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Since he uses faulty weight, So, the SP of rice for him </span><span style=\"font-family: Palanquin Dark;\">be 5</span><span style=\"font-family: Palanquin Dark;\"> units.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Hence, it is represented as CP : SP = 4 : 5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, the quantity of rice he is giving to customer =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 4 = 800 gm</span></p>\n",
                    solution_hi: "<p>21.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366;, &#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352; &#2325;&#2375; &#2354;&#2367;&#2319; &#2330;&#2366;&#2357;&#2354; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; 4 &#2311;&#2325;&#2366;&#2312; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2330;&#2370;&#2306;&#2325;&#2367; &#2357;&#2361; &#2327;&#2354;&#2340; &#2357;&#2332;&#2344; &#2340;&#2380;&#2354;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2350;&#2358;&#2368;&#2344; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;, &#2311;&#2360;&#2354;&#2367;&#2319; &#2313;&#2360;&#2325;&#2375; &#2354;&#2367;&#2319; &#2330;&#2366;&#2357;&#2354; &#2325;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; 5 &#2311;&#2325;&#2366;&#2312; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360;&#2354;&#2367;&#2319;, &#2311;&#2360;&#2375; &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; : &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 4 : 5 &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2379;, &#2357;&#2361; &#2327;&#2381;&#2352;&#2366;&#2361;&#2325; &#2325;&#2379; &#2330;&#2366;&#2357;&#2354; &#2325;&#2368; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> &times; 4 = 800 gm &#2342;&#2375; &#2352;&#2361;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. <span style=\"font-family: Palanquin Dark;\">Study the given graph carefully and answer the questions that follows. </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">The Graph shows the marks scored by the five students in the subjects of Math, English , </span><span style=\"font-family: \'Palanquin Dark\';\">and physics</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image3.png\" width=\"400\" height=\"272\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">What is the difference between the total marks obtained by B and the total marks obtained by A? </span></p>\n",
                    question_hi: "<p>22. <span style=\"font-family: Palanquin Dark;\">&#2342;&#2367;&#2319; &#2327;&#2319; &#2327;&#2381;&#2352;&#2366;&#2347; &#2325;&#2366; &#2343;&#2381;&#2351;&#2366;&#2344;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2379;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2340;&#2352; &#2342;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2327;&#2381;&#2352;&#2366;&#2347; &#2346;&#2366;&#2306;&#2330; &#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2327;&#2339;&#2367;&#2340;, &#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368; &#2324;&#2352; &#2349;&#2380;&#2340;&#2367;&#2325;&#2368; &#2325;&#2375; &#2357;&#2367;&#2359;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image3.png\" width=\"402\" height=\"273\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">B &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2369;&#2354; &#2309;&#2306;&#2325;&#2379;&#2306; &#2324;&#2352; A &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2369;&#2354; &#2309;&#2306;&#2325;&#2379;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352; &#2325;&#2367;&#2340;&#2344;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>80 marks</p>\n", "<p>90 marks</p>\n", 
                                "<p>95 marks</p>\n", "<p>85 marks</p>\n"],
                    options_hi: ["<p>80 &#2309;&#2306;&#2325;</p>\n", "<p>90 &#2309;&#2306;&#2325;</p>\n",
                                "<p>95 &#2309;&#2306;&#2325;</p>\n", "<p>85 &#2309;&#2306;&#2325;</p>\n"],
                    solution_en: "<p>22.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total marks obtained by B = 70 + 60 + 80 = 210</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total marks obtained by A = 30 + 40 + 50 = 120</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Required difference = 210 - 120 = 90</span></p>\n",
                    solution_hi: "<p>22.(b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">B &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2369;&#2354; &#2309;&#2306;&#2325; = 70 + 60 + 80 = 210</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2369;&#2354; &#2309;&#2306;&#2325; = 30 + 40 + 50 = 120</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2349;&#2368;&#2359;&#2381;&#2335; &#2309;&#2306;&#2340;&#2352; = 210 - 120 = 90</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23.<span style=\"font-family: Palanquin Dark;\"> Calculate the area of the shaded region in the following diagram.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image1.png\" width=\"207\" height=\"165\"></p>\n",
                    question_hi: "<p>23.<span style=\"font-family: Palanquin Dark;\"> &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2310;&#2352;&#2375;&#2326; &#2350;&#2375;&#2306; &#2331;&#2366;&#2351;&#2366;&#2306;&#2325;&#2367;&#2340; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; &#2325;&#2368; &#2327;&#2339;&#2344;&#2366; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image1.png\" width=\"202\" height=\"162\"></p>\n",
                    options_en: ["<p>45 cm&sup2;</p>\n", "<p>42 cm&sup2;</p>\n", 
                                "<p>48 cm&sup2;</p>\n", "<p>50 cm&sup2;</p>\n"],
                    options_hi: ["<p>45 cm&sup2;</p>\n", "<p>42 cm&sup2;</p>\n",
                                "<p>48 cm&sup2;</p>\n", "<p>50 cm&sup2;</p>\n"],
                    solution_en: "<p>23.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">In this figure we can clearly see five squares of side 3 cm is formed when a line is produced</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">So, area of shaded region = 5 &times; 3&sup2;&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> = 45 cm&sup2;</span></p>\n",
                    solution_hi: "<p>23.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2311;&#2360; &#2310;&#2325;&#2371;&#2340;&#2367; &#2350;&#2375;&#2306; &#2361;&#2350; &#2360;&#2381;&#2346;&#2359;&#2381;&#2335; &#2352;&#2370;&#2346; &#2360;&#2375; &#2342;&#2375;&#2326; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2319;&#2325; &#2352;&#2375;&#2326;&#2366; &#2348;&#2344;&#2344;&#2375; &#2346;&#2352; 3 cm &#2349;&#2369;&#2332;&#2366; &#2357;&#2366;&#2354;&#2375; &#2346;&#2366;&#2305;&#2330; &#2357;&#2352;&#2381;&#2327; &#2348;&#2344;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2309;&#2340;: &#2331;&#2366;&#2351;&#2366;&#2306;&#2325;&#2367;&#2340; &#2349;&#2366;&#2327; &#2325;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354; = 5 &times; 3&sup2;&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> = 45 cm&sup2;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. <span style=\"font-family: Palanquin Dark;\">The average salary per head of all the workers in a workshop is &#8377;8,850. If the average salary per head of 9 technicians is &#8377;10,000, and the average salary per head of the rest is &#8377;7,780, find the total number of workers in the workshop.(Consider integral part only)</span></p>\n",
                    question_hi: "<p>24. <span style=\"font-family: Palanquin Dark;\">&#2319;&#2325; &#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2349;&#2368; &#2358;&#2381;&#2352;&#2350;&#2367;&#2325;&#2379;&#2306; &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2324;&#2360;&#2340; &#2357;&#2375;&#2340;&#2344; &#8377;8,850 &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; 9 &#2340;&#2325;&#2344;&#2368;&#2358;&#2367;&#2351;&#2344;&#2379;&#2306; &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2324;&#2360;&#2340; &#2357;&#2375;&#2340;&#2344; &#8377;10,000 &#2361;&#2376;, &#2324;&#2352; &#2348;&#2366;&#2325;&#2368; &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2324;&#2360;&#2340; &#2357;&#2375;&#2340;&#2344; &#8377;7,780 &#2361;&#2376;, &#2340;&#2379; &#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2375; &#2350;&#2375;&#2306; &#2358;&#2381;&#2352;&#2350;&#2367;&#2325;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404; (&#2344;&#2367;&#2325;&#2335;&#2340;&#2350; &#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325; &#2350;&#2375;&#2306;)</span></p>\n",
                    options_en: ["<p>18</p>\n", "<p>22</p>\n", 
                                "<p>24</p>\n", "<p>16</p>\n"],
                    options_hi: ["<p>18</p>\n", "<p>22</p>\n",
                                "<p>24</p>\n", "<p>16</p>\n"],
                    solution_en: "<p>24.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Let the no of the remaining technicians be x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">No of technicians&nbsp; &nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Average&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10,000&nbsp; &nbsp; &nbsp; &nbsp; 7780</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Average salary of all workers =&nbsp;&nbsp;</span><span style=\"font-family: Palanquin Dark;\">&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>,</mo><mn>000</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7780</mn></mrow><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>90</mn><mo>,</mo><mn>000</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>7780</mn><mi>x</mi></mrow><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">8850 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>,</mo><mn>000</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>7780</mn><mi>x</mi></mrow><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">8850 (9 + x) = 90,000 + 7780x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">79650 + 8850x = 90000 + 7780x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">1070x = 10350</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1035</mn><mn>107</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\">= 9.67 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&asymp;</mo></math></span><span style=\"font-family: Palanquin Dark;\">&nbsp;9</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total no of technicians in the workshop = 9 + 9 = 18</span></p>\n",
                    solution_hi: "<p>24.(a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2350;&#2366;&#2344;&#2366;, &#2358;&#2375;&#2359; &#2340;&#2325;&#2344;&#2368;&#2358;&#2367;&#2351;&#2344;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; x &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2340;&#2325;&#2344;&#2368;&#2358;&#2367;&#2351;&#2344;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2324;&#2360;&#2340;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10,000&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7780</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2360;&#2349;&#2368; &#2325;&#2352;&#2381;&#2350;&#2330;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2324;&#2360;&#2340; &#2357;&#2375;&#2340;&#2344; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn><mo>,</mo><mn>000</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>7780</mn></mrow><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>90</mn><mo>,</mo><mn>000</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>7780</mn><mi>x</mi></mrow><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">8850 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>,</mo><mn>000</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>7780</mn><mi>x</mi></mrow><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>x</mi></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">8850 (9 + x) = 90,000 + 7780x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">79650 + 8850x = 90000 + 7780x</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">1070x = 10350</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1035</mn><mn>107</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 9.67 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&asymp;</mo></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\">9</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2325;&#2366;&#2352;&#2326;&#2366;&#2344;&#2375; &#2350;&#2375;&#2306; &#2340;&#2325;&#2344;&#2368;&#2358;&#2367;&#2351;&#2344;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 9 + 9 = 18</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <span style=\"font-family: Palanquin Dark;\">Study the given table and answer the questions that follows.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Given that the total number of students is 100.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image6.png\" width=\"448\" height=\"151\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">The percentage of the number of students getting at least 40% marks in A over those getting at least 20% marks in C, is approximately_____.</span></p>\n",
                    question_hi: "<p>25. <span style=\"font-family: Palanquin Dark;\">&#2342;&#2368; &#2327;&#2312; &#2340;&#2366;&#2354;&#2367;&#2325;&#2366; &#2325;&#2366; &#2309;&#2343;&#2381;&#2351;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#2344;&#2368;&#2330;&#2375; &#2342;&#2367;&#2319; &#2327;&#2319; &#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2379;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2340;&#2352; &#2342;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; 100 &#2361;&#2376;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669365414/word/media/image6.png\" width=\"450\" height=\"152\"></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">C &#2350;&#2375;&#2306; &#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350; 20% &#2309;&#2306;&#2325; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2379;&#2306; &#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306;, A &#2350;&#2375;&#2306; &#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350; 40% &#2309;&#2306;&#2325; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306; &#2325;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2354;&#2327;&#2349;&#2327; _____ &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>90%</p>\n", "<p>89%</p>\n", 
                                "<p>83%</p>\n", "<p>93%</p>\n"],
                    options_hi: ["<p>90%</p>\n", "<p>89%</p>\n",
                                "<p>83%</p>\n", "<p>93%</p>\n"],
                    solution_en: "<p>25.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total no of students getting at least 40% marks in A = 81</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Total no of students getting at least 20% marks in C = 87</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Required percentage =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>87</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8100</mn><mn>87</mn></mfrac></math> </span><span style=\"font-family: Palanquin Dark;\"> = 93.1&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&asymp;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">93% </span></p>\n",
                    solution_hi: "<p>25.(d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">A &#2350;&#2375;&#2306; &#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350; 40% &#2309;&#2306;&#2325; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 81</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">C &#2350;&#2375;&#2306; &#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350; 20% &#2309;&#2306;&#2325; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306; &#2325;&#2368; &#2325;&#2369;&#2354; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; = 87</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>87</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\">&times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8100</mn><mn>87</mn></mfrac></math></span><span style=\"font-family: Palanquin Dark;\"> = 93.1&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&asymp;</mo></math> </span><span style=\"font-family: Palanquin Dark;\">93% </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>