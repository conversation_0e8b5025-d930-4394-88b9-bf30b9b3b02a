<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. A computer program that converts an entire program into machine language is called a(n):</p>",
                    question_hi: "<p>1. एक कंप्यूटर प्रोग्राम जो पूरे प्रोग्राम को मशीनी भाषा में परिवर्तित करता है,__________ कहलाता है</p>",
                    options_en: ["<p>Interpreter</p>", "<p>Simulator</p>", 
                                "<p>Compiler</p>", "<p>Converter</p>"],
                    options_hi: ["<p>भाषांतरकार</p>", "<p>अनुरूपक</p>",
                                "<p>संकलक</p>", "<p>संपरिवर्तित्र</p>"],
                    solution_en: "<p>1.(c) <strong>Compiler</strong>. <strong>Interpreter </strong>takes one line, converts it into executable code if the line is syntactically correct and then it repeats these steps for all lines in the source code. The translator used to convert the code written in assembly language to machine language is called <strong>assembler</strong>.</p>",
                    solution_hi: "<p>1.(c) <strong>संकलक (Compiler)</strong>। <strong>भाषांतरकार (Interpreter )</strong> एक पंक्ति लेता है, यदि पंक्ति वाक्यात्मक रूप से सही है तो इसे निष्पादन योग्य कोड में परिवर्तित करता है और फिर यह स्रोत कोड में सभी पंक्तियों के लिए इन चरणों को दोहराता है। असेंबली भाषा में लिखे कोड को मशीनी भाषा में बदलने के लिए प्रयोग किये जाने वाले अनुवादक को <strong>असेंबलर </strong>कहा जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. The first mechanical computer designed by Charles Babage was called:</p>",
                    question_hi: "<p>2. चार्ल्स बैबेज द्वारा डिजाइन किए गए पहले यान्त्रिक कंप्यूटर को क्या कहा जाता था?</p>",
                    options_en: ["<p>Abacus</p>", "<p>Analytical Engine</p>", 
                                "<p>Calculator</p>", "<p>Processor</p>"],
                    options_hi: ["<p>अबेकस</p>", "<p>विश्लेषणात्मक इंजन</p>",
                                "<p>कैलकुलेटर</p>", "<p>प्रोसेसर</p>"],
                    solution_en: "<p>2.(b) <strong>Analytical Engine.</strong> It was first described in 1837 as the successor to Babbage\'s difference engine, which was a design for a simpler mechanical computer. <strong>The abacus</strong> (counting frame) is the ancestor of the modern calculating machine and computer. <strong>Calculators </strong>are used for arithmetic operations and some mathematical operations.The modern calculator was a mechanical digital calculating device developed by Blaise Pascal in 1642.</p>",
                    solution_hi: "<p>2.(b) <strong>विश्लेषणात्मक इंजन। </strong>इसे पहली बार 1837 में बैबेज के डिफरेंस इंजन के आधुनिक प्रतिरूप के रूप में विकसित किया गया था, जो एक सरल यांत्रिक कंप्यूटर के लिए एक डिज़ाइन था। <strong>अबेकस </strong>(कॉउन्टिंग फ्रेम) आधुनिक गणना मशीन और कंप्यूटर की एक पीढ़ी है। <strong>कैलकुलेटर</strong> अंकगणितीय संक्रियाएँ और कुछ गणितीय गणनाओं के लिए प्रयोग किये जाते है। आधुनिक कैलकुलेटर ब्लेज़ पास्कल द्वारा एक यांत्रिक अंकीय गणना यंत्र सन् 1642 में विकसित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. What is the full form of DDL in terms of computer jargon?</p>",
                    question_hi: "<p>3. कंप्यूटर शब्दावली के संदर्भ में DDL का पूर्ण रूप क्या है?</p>",
                    options_en: ["<p>Data Definition Language</p>", "<p>Digital Data Logic</p>", 
                                "<p>Dynamic Data Language</p>", "<p>Direct Data Language</p>"],
                    options_hi: ["<p>Data Definition Language</p>", "<p>Digital Data Logic</p>",
                                "<p>Dynamic Data Language</p>", "<p>Direct Data Language</p>"],
                    solution_en: "<p>3.(a) <strong>Data Definition Language</strong>. DDL is used to create and modify database objects like tables, indexes, views, and constraints. For example, to build a new table using SQL syntax, the Create command is used, followed by parameters for the table name and column definitions.</p>",
                    solution_hi: "<p>3.(a) <strong>डेटा डेफिनिशन लैंग्वेज</strong>। DDL का प्रयोग टेबल, इंडेक्स, व्यू और कंस्ट्रैंट्स जैसे डेटाबेस ऑब्जेक्ट को बनाने और संशोधित करने के लिए किया जाता है। उदाहरण के लिए, SQL सिंटैक्स का प्रयोग करके एक नई टेबल बनाने के लिए, Create कमांड का उपयोग किया जाता है, जिसके बाद टेबल नेम और कॉलम डेफिनिशन के लिए पैरामीटर का प्रयोग किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. What are the three constituents of a CPU in a computer?</p>",
                    question_hi: "<p>4. कंप्यूटर में CPU के तीन घटक क्या हैं?</p>",
                    options_en: ["<p>Control unit, ALU, keyboard</p>", "<p>Control unit, ALU, memory</p>", 
                                "<p>Monitor, memory, control unit</p>", "<p>Monitor, ALU, memory</p>"],
                    options_hi: ["<p>Control unit, ALU, keyboard</p>", "<p>Control unit, ALU, memory</p>",
                                "<p>Monitor, memory, control unit</p>", "<p>Monitor, ALU, memory</p>"],
                    solution_en: "<p>4.(b) <strong>Control unit, ALU, memory</strong>. The central processing unit (CPU) consists of six main components: control unit (CU), arithmetic logic unit (ALU), registers, cache, buses, clock.</p>",
                    solution_hi: "<p>4.(b) <strong>कंट्रोल यूनिट, ALU, मेमोरी</strong>। सेंट्रल प्रोसेसिंग यूनिट (CPU) में छह मुख्य घटक होते हैं: कंट्रोल यूनिट (CU), अर्थमेटिक लॉजिक यूनिट (ALU), रजिस्टर, कैश (cache), बसेस (buses), घड़ी(clock)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. __________ refers to unsolicited commercial emails that flood the Internet.</p>",
                    question_hi: "<p>5. ________ अवांछित वाणिज्यिक ईमेल को संदर्भित करता है जो इंटरनेट पर बहुतायत में होता है।</p>",
                    options_en: ["<p>Spyware</p>", "<p>Trojan Horse</p>", 
                                "<p>Malware</p>", "<p>Spam</p>"],
                    options_hi: ["<p>स्पाइवेयर</p>", "<p>ट्रोजन हॉर्स</p>",
                                "<p>मैलवेयर</p>", "<p>स्पैम</p>"],
                    solution_en: "<p>5.(d) <strong>Spam. Spyware</strong> is any software that installs itself on your computer and starts covertly monitoring your online behavior without your knowledge or permission. <strong>Trojan horse</strong> is a type of malicious code or software that looks legitimate but can take control of your computer. <strong>Malware </strong>is a file or code, typically delivered over a network, that infects, explores, steals or conducts virtually any behavior an attacker wants.</p>",
                    solution_hi: "<p>5.(d) <strong>स्पैम। स्पाइवेयर</strong> कोई भी सॉफ़्टवेयर है जो आपके कंप्यूटर पर स्वयं इंस्टॉल हो जाता है और आपकी जानकारी या अनुमति के बिना गुप्त रूप से आपके ऑनलाइन व्यवहार की निगरानी करना शुरू कर देता है। <strong>ट्रोजन हॉर्स </strong>एक प्रकार का दुर्भावनापूर्ण कोड या सॉफ़्टवेयर है जो वैध (legitimate) दिखता है लेकिन आपके कंप्यूटर को नियंत्रित कर सकता है। <strong>मैलवेयर </strong>एक फ़ाइल या कोड है, जो आम तौर पर एक नेटवर्क सिस्टम पर चलाया जाता है, जिसका उद्देश्य संचालक की अनुमति के बिना किसी भी गति-विधि को प्रभावित (infected) करना, खोजना, चुराना या संचालित करना होता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which of the following is NOT a microblogging site?</p>",
                    question_hi: "<p>6. निम्न में से कौन एक माइक्रोब्लॉगिंग साइट नहीं है?</p>",
                    options_en: ["<p>Pinterest</p>", "<p>Twitter</p>", 
                                "<p>Plurk</p>", "<p>None of these<br></p>"],
                    options_hi: ["<p>पिनटेरेस्ट</p>", "<p>ट्विटर</p>",
                                "<p>प्लर्क</p>", "<p>इनमें से कोई नहीं<br></p>"],
                    solution_en: "<p>6.(d) <strong>None of these</strong><br><br></p>",
                    solution_hi: "<p>6.(d) <strong>इनमें से कोई नहीं</strong></p>\n<p dir=\"ltr\">&nbsp;</p>\n<p><strong id=\"docs-internal-guid-240adad2-7fff-a99d-a3f1-0835b32c6463\"></strong><br><br></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Who invented HTML in 1990?</p>",
                    question_hi: "<p>7. 1990 में HTML की खोज किसने की ?</p>",
                    options_en: ["<p>Tim Berners - Lee</p>", "<p>David Noble</p>", 
                                "<p>Kane Kramer</p>", "<p>Niklaus Wirth</p>"],
                    options_hi: ["<p>टिम बर्नर्स- ली</p>", "<p>डेविड नोबल</p>",
                                "<p>केन क्रामर</p>", "<p>निकलॉस विर्थ</p>"],
                    solution_en: "<p>7.(a) <strong>Tim Berners - Lee.</strong> The first version of HTML (Hypertext Markup Language) was written by Tim Berners-Lee in 1993. HTML is the set of markup symbols or codes inserted into a file intended for display on the Internet. The markup tells web browsers how to display a web page\'s words and images.</p>",
                    solution_hi: "<p>7.(a) <strong>टिम बर्नर्स - ली।</strong> HTML (हाइपरटेक्स्ट मार्कअप लैंग्वेज) का पहला संस्करण 1993 में टिम बर्नर्स-ली द्वारा लिखा गया था। HTML इंटरनेट पर प्रदर्शित करने के लिए बनाई गई फ़ाइल में डाले गए मार्कअप प्रतीकों या कोड का सेट है। मार्कअप वेब ब्राउज़र को बताता है कि वेब पेज के शब्दों और छवियों को कैसे प्रदर्शित किया जाए।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. MS-DOS is an operating system that has a/an:</p>",
                    question_hi: "<p>8. MS DOS एक ऑपरेटिंग सिस्टम है जिसमें एक______ है।</p>",
                    options_en: ["<p>Command Line Interface</p>", "<p>Voice Line Interface</p>", 
                                "<p>Open source Origin</p>", "<p>Graphical User Interface</p>"],
                    options_hi: ["<p>कमांड लाइन इंटरफेस</p>", "<p>वॉयस लाइन इंटरफेस</p>",
                                "<p>ओपन सोर्स ओरिजिन</p>", "<p>ग्राफिकल यूज़र इंटरफ़ेस</p>"],
                    solution_en: "<p>8.(a) <strong>Command Line Interface (CLI)</strong> connects a user to a computer program or operating system. CLI users interact with a system or application by typing in the text (commands). <strong>Voice user interface (VUI)</strong> using a mobile application or a website by using voice in addition to or instead of touch, keyboard or a mouse. <strong>Open source origin</strong> - The source code is available to the general public for use or modification from its original design. Some popular, modern graphical user interface examples include Microsoft Windows, macOS, Ubuntu Unity and Android, Apple\'s iOS, Palm OS-WebOS. <strong>MS-DOS</strong> is a completely user-dependent operating system (OS). MS-DOS is a non-graphical, line-oriented, command-driven OS.</p>",
                    solution_hi: "<p>8.(a) <strong>कमांड लाइन इंटरफ़ेस (CLI)</strong> उपयोगकर्ता को कंप्यूटर प्रोग्राम या ऑपरेटिंग सिस्टम से जोड़ता है। CLI उपयोगकर्ता टेक्स्ट (कमांड) टाइप करके किसी सिस्टम या एप्लिकेशन के साथ इंटरैक्ट करते हैं। वॉयस <strong>(Voice) यूजर इंटरफेस (VUI)</strong> मोबाइल एप्लिकेशन या वेबसाइट का उपयोग करके टच, कीबोर्ड या माउस के अलावा या इसके बजाय आवाज का उपयोग करता है। <strong>ओपन सोर्स ओरिजिन</strong> - सोर्स कोड अपने मूल डिज़ाइन से उपयोग या संशोधन के लिए आम जनता के लिए उपलब्ध है। कुछ लोकप्रिय, आधुनिक ग्राफिकल यूजर इंटरफेस उदाहरणों में माइक्रोसॉफ्ट विंडोज, macOS, Ubuntu यूनिटी और एंड्रॉइड, ऐप्पल का iOS, Palm OS-WebOS शामिल हैं। <strong>MS-DOS</strong> पूरी तरह से उपयोगकर्ता पर निर्भर ऑपरेटिंग सिस्टम (OS) है। MS-DOS एक नॉन-ग्राफ़िकल, लाइन-ओरिएंटेड, कमांड-ड्राइवेन OS है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which was the first social media site?</p>",
                    question_hi: "<p>9. पहली सोशल मीडिया साइट कौन सी थी</p>",
                    options_en: ["<p>LinkedIn</p>", "<p>Six Degrees</p>", 
                                "<p>Myspace</p>", "<p>Friendster</p>"],
                    options_hi: ["<p>लिंक्डइन</p>", "<p>सिक्स डिग्री</p>",
                                "<p>माय स्पेस</p>", "<p>फ्रेंडस्टर</p>"],
                    solution_en: "<p>9.(b) <strong>Six Degrees - </strong>MacroView (later renamed to SixDegrees Inc.) was founded by CEO Andrew Weinreich in May 1996 and was based in New York City. <strong>LinkedIn - </strong>Social media platform that works through websites and mobile apps, <strong>launched - </strong>5 May 2003, <strong>owned (now) by - </strong>Microsoft. <strong>Myspace - </strong>the first social network to reach a global audience. <strong>Friendster -</strong> a social network game based in Mountain View (California) founded by Jonathan Abrams and launched in March 2003.</p>",
                    solution_hi: "<p>9.(b) <strong>सिक्स डिग्री -</strong> मैक्रोव्यू (बाद में इसका नाम बदलकर सिक्सडिग्री इंक (Inc.) कर दिया गया) की स्थापना CEO एंड्रयू वेनरिच ने मई 1996 में की थी और यह न्यूयॉर्क शहर में स्थित था। <strong>लिंक्डइन</strong> (<strong>LinkedIn </strong>)<strong> -</strong> सोशल मीडिया प्लेटफ़ॉर्म है जो वेबसाइटों और मोबाइल ऐप्स के माध्यम से काम करता है, जो 5 मई 2003 को <strong>लॉन्च</strong> किया गया था, <strong>स्वामित्व (अब) - </strong>माइक्रोसॉफ्ट के पास। <strong>मायस्पेस- </strong>वैश्विक दर्शकों तक पहुंचने वाला प्रथम सोशल नेटवर्क। <strong>फ्रेंडस्टर -</strong> माउंटेन व्यू (कैलिफ़ोर्निया) में स्थित एक सोशल नेटवर्क गेम जो जोनाथन अब्राम्स द्वारा स्थापित किया गया और मार्च 2003 में लॉन्च किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. In Computer terminology, what is a nibble?</p>",
                    question_hi: "<p>10. कंप्यूटर शब्दावली में, निबल क्या है?</p>",
                    options_en: ["<p>Half a byte</p>", "<p>A kilobyte</p>", 
                                "<p>A gigabyte</p>", "<p>A terabyte</p>"],
                    options_hi: ["<p>हाफ बाइट</p>", "<p>एक किलोबाइट</p>",
                                "<p>एक गीगाबाइट</p>", "<p>एक टेराबाइट</p>"],
                    solution_en: "<p>10.(a) <strong>Half a byte (4 bit).</strong> A bit (binary digit) is the smallest unit of data that a computer can process and store.1 byte = 8 bit. 1 Kilobyte= 1024 byte. 1 Gigabyte (GB)= 1,024 megabyte (MB). 1 Terabyte (TB) = 1,024 gigabytes (GB).</p>",
                    solution_hi: "<p>10.(a) <strong>हाफ बाइट (4 बिट)।</strong> बिट (बाइनरी डिजिट) डेटा की सबसे छोटी इकाई है जिसे कंप्यूटर प्रोसेस और स्टोर कर सकता है। 1 बाइट = 8 बिट। 1 किलोबाइट = 1024 बाइट। 1 गीगाबाइट (GB) = 1,024 मेगाबाइट (MB)। 1 टेराबाइट (TB) = 1,024 गीगाबाइट (GB)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Trojan Horse is a form of :</p>",
                    question_hi: "<p>11. ट्रोजन हॉर्स ________का एक रूप है |</p>",
                    options_en: ["<p>Virus attack</p>", "<p>Melissa worm</p>", 
                                "<p>Slammer worm</p>", "<p>Service attack</p>"],
                    options_hi: ["<p>वायरस अटैक</p>", "<p>मेलिसा वर्म</p>",
                                "<p>स्लैमर वर्म</p>", "<p>सर्विस अटैक</p>"],
                    solution_en: "<p>11.(a) <strong>Virus attack</strong>. <strong>Trojan Horse Virus</strong>: A malware that downloads onto a computer disguised as a legitimate program. <strong>Melissa</strong>: A fast-spreading macro virus that is distributed as an e-mail attachment. <strong>Slammer</strong>: Generates a damaging level of network traffic with very high speed to create internet interruption. <strong>Service attack</strong>: An attack meant to shut down a machine or network, making it inaccessible to its users.</p>",
                    solution_hi: "<p>11.(a) <strong>वायरस अटैक। ट्रोजन हॉर्स वायरस</strong>: एक मैलवेयर जो एक वैध प्रोग्राम के रूप में दिशग्युसड़ (disguised) कंप्यूटर पर डाउनलोड होता है। <strong>मेलिसा</strong>: एक तेजी से फैलने वाला मैक्रो वायरस जो ई-मेल अटैचमेंट के रूप में भेजा जाता है। <strong>स्लैमर</strong>: इंटरनेट में रुकावट पैदा करने के लिए बहुत तेज़ गति के साथ नेटवर्क ट्रैफ़िक मे डैमेजिंग लेवल उत्पन्न करता है। <strong>सर्विस अटैक</strong> : इस हमले का मतलब किसी मशीन या नेटवर्क को बंद करना है, जिससे यह अपने उपयोगकर्ताओं के पहुंच से बाहर हो जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. A/An __________ is an interface between the user and hardware.</p>",
                    question_hi: "<p>12. _________ एक उपयोगकर्ता और हार्डवेयर के बीच एक इंटरफ़ेस है |</p>",
                    options_en: ["<p>memory</p>", "<p>command</p>", 
                                "<p>screen</p>", "<p>operating system</p>"],
                    options_hi: ["<p>मेमोरी</p>", "<p>कमांड</p>",
                                "<p>स्क्रीन</p>", "<p>ऑपरेटिंग सिस्टम</p>"],
                    solution_en: "<p>12.(d) <strong>Operating system</strong> - The Program that manages all the softwares and hardware and performs basic tasks, such as file, memory and process management, handling input and output, and controlling peripheral devices. <strong>Examples</strong>: Mac OS, Microsoft Windows, Google\'s Android OS, Linux OS, Ubuntu etc.</p>",
                    solution_hi: "<p>12.(d) <strong>ऑपरेटिंग सिस्टम</strong> - वह प्रोग्राम जो सभी सॉफ्टवेयर और हार्डवेयर का प्रबंधन करता है और बुनियादी कार्य करता है, जैसे फ़ाइल, मेमोरी और प्रोसेस को मैनेजमेंट करना, इनपुट और आउटपुट को हैंडल करना और परिधीय (peripheral) उपकरणों को नियंत्रित करना। <strong>उदाहरण</strong>: Mac OS, माइक्रोसॉफ्ट विंडोज, Google\'s Android OS, Linux OS, Ubuntu आदि।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. What was India&rsquo;s first supercomputer called?</p>",
                    question_hi: "<p>13. भारत के प्रथम सुपर कंप्यूटर को क्या कहा जाता था ?</p>",
                    options_en: ["<p>ENIAC</p>", "<p>SAGA 220</p>", 
                                "<p>PARAM 8000</p>", "<p>PARAM 6000</p>"],
                    options_hi: ["<p>ENIAC</p>", "<p>SAGA 220</p>",
                                "<p>PARAM 8000</p>", "<p>PARAM 6000</p>"],
                    solution_en: "<p>13.(c) <strong>PARAM 8000</strong> (1991). Developed by C-DAC (Centre for Development of advanced Computing). <strong>Other Supercomputers</strong>: PARAM Siddhi-AI, Pratyush, PARAM Kamarupa <strong>(India);</strong> Frontier<strong> (US)</strong>; Fugaku <strong>(Japan); </strong>Tianhe-2A<strong> (China). National Supercomputing Mission</strong> (2015) - Increasing Research capabilities in the country by connecting them to form a Supercomputing grid.</p>",
                    solution_hi: "<p>13.(c) <strong>PARAM 8000</strong> (1991)। C-DAC (उन्नत कंप्यूटिंग के विकास केंद्र) द्वारा विकसित। <strong>अन्य सुपर कंप्यूटर</strong>: PARAM सिद्धि-AI, प्रत्यूष, PARAM कामरूप <strong>(भारत);</strong> फ्रंटियर <strong>(अमेरिका);</strong> फुगाकु<strong> (जापान);</strong> तियान्हे-2A <strong>(चीन)</strong>। <strong>राष्ट्रीय सुपरकंप्यूटिंग मिशन</strong> (2015) - इन्हें आपस में जोड़कर सुपरकंप्यूटिंग ग्रिड बनाकर देश में अनुसंधान क्षमताओं को बढ़ाना।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. The technology that provides high internet speed over telephone wiring is called:</p>",
                    question_hi: "<p>14. टेलीफोन वायरिंग पर उच्च इंटरनेट स्पीड प्रदान करने वाली तकनीक क्या कहलाती है?</p>",
                    options_en: ["<p>ADSL</p>", "<p>ALSD</p>", 
                                "<p>ASLD</p>", "<p>ADLS</p>"],
                    options_hi: ["<p>ADSL</p>", "<p>ALSD</p>",
                                "<p>ASLD</p>", "<p>ADLS</p>"],
                    solution_en: "<p>14.(a) <strong>ADSL </strong>(Asymmetric Digital Subscriber Line): A broadband connection that facilitates fast data transmission at a high bandwidth on existing copper wire telephone lines to homes and businesses. <strong>Working Principle</strong>: A DSL filter is used to isolate the bands with higher frequencies so that the landline and the ADSL modem can be used at the same time.</p>",
                    solution_hi: "<p>14.(a) <strong>ADSL </strong>(एसिमेट्रिक डिजिटल सब्सक्राइबर लाइन): एक ब्रॉडबैंड कनेक्शन है जो घरों और व्यवसायों के लिए मौजूदा तांबे के तार टेलीफोन लाइनों पर उच्च बैंडविड्थ पर तेजी से डेटा ट्रांसमिशन की सुविधा प्रदान करता है। <strong>कार्य सिद्धांत</strong>: एक DSL फ़िल्टर का उपयोग उच्च आवृत्तियों वाले बैंड को अलग करने के लिए किया जाता है जिससे लैंडलाइन और ADSL मॉडेम का एक ही समय में उपयोग किया जा सके।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. SAGA-220 was developed by the Indian Space Research Organization. What is SAGA- 220?</p>",
                    question_hi: "<p>15. SAGA-220 को भारतीय अंतरिक्ष अनुसंधान संगठन द्वारा विकसित किया गया था। SAGA- 220 क्या है?</p>",
                    options_en: ["<p>Supercomputer</p>", "<p>Missile</p>", 
                                "<p>Satellite</p>", "<p>Pacemaker</p>"],
                    options_hi: ["<p>सुपर कंप्यूटर</p>", "<p>मिसाइल</p>",
                                "<p>उपग्रह</p>", "<p>पेसमेकर</p>"],
                    solution_en: "<p>15.(a) <strong>Supercomputer. SAGA-220</strong> (Supercomputer for Aerospace with GPU Architecture-220 teraflops): Unveiled in 2011; Used to solve complex aeronautical problems, Design future space launch vehicles and for other Space Projects. <strong>Established </strong>at Vikram Sarabhai Space Centre, Thiruvananthapuram.</p>",
                    solution_hi: "<p>15.(a)<strong> सुपर कंप्यूटर।</strong> <strong>SAGA-220</strong> (GPU आर्किटेक्चर-220 टेराफ्लॉप्स के साथ एयरोस्पेस के लिए सुपरकंप्यूटर): 2011 में अनावरण किया गया; जटिल वैमानिक समस्याओं को हल करने, भविष्य के अंतरिक्ष प्रक्षेपण वाहनों को डिजाइन करने और अन्य अंतरिक्ष परियोजनाओं के लिए उपयोग किया जाता है। विक्रम साराभाई अंतरिक्ष केंद्र, तिरुवनंतपुरम में <strong>स्थापित है </strong>।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>