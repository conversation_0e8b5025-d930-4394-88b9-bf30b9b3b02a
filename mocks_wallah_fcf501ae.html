<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">From which country\'s constitution no feature has been taken in the constitution of India?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>United States of America</p>\n", "<p>Britain</p>\n", 
                                "<p>China</p>\n", "<p>France</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2350;&#2375;&#2352;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2352;&#2367;&#2335;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2330;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2347;&#2381;&#2352;&#2366;&#2306;&#2360;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\"> <strong>China</strong><span style=\"font-weight: 400;\">. Borrowed Features of the Indian Constitution: </span><strong>Britain</strong><span style=\"font-weight: 400;\"> - Law-making procedures/Legislative procedure, Single citizenship, Bicameral Parliamentary system, Parliamentary form of Government. </span><strong>USA</strong><span style=\"font-weight: 400;\"> -&nbsp; Impeachment of the president, Fundamental rights, Preamble, Removal of Supreme Court and High Court judges, Independence of judiciary, Judicial review.</span><strong> France - </strong><span style=\"font-weight: 400;\">Republican System, Principles of Liberty, Equality, and Fraternity.</span><strong> Ireland</strong><span style=\"font-weight: 400;\"> - Directive Principles of State policy, Method of presidential elections.</span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>&#2330;&#2368;&#2344;</strong><span style=\"font-weight: 400;\">&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2368; &#2313;&#2343;&#2366;&#2352; &#2354;&#2368; &#2327;&#2312; &#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;&#2319;&#2306;:</span><strong> &#2348;&#2381;&#2352;&#2367;&#2335;&#2375;&#2344;</strong><span style=\"font-weight: 400;\"> - &#2325;&#2366;&#2344;&#2370;&#2344; &#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2368; &#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366; / &#2357;&#2367;&#2343;&#2366;&#2351;&#2368; &#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;, &#2319;&#2325;&#2354; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366;, &#2342;&#2381;&#2357;&#2367;&#2360;&#2342;&#2344;&#2368;&#2351; &#2360;&#2306;&#2360;&#2342;&#2368;&#2351; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368;, &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2360;&#2306;&#2360;&#2342;&#2368;&#2351; &#2360;&#2381;&#2357;&#2352;&#2370;&#2346;&#2404; </span><strong>USA</strong><span style=\"font-weight: 400;\">- &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2325;&#2366; &#2350;&#2361;&#2366;&#2349;&#2367;&#2351;&#2379;&#2327;, &#2350;&#2380;&#2354;&#2367;&#2325; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;, &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;&#2344;&#2366;, &#2360;&#2352;&#2381;&#2357;&#2379;&#2330;&#2381;&#2330; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; &#2324;&#2352; &#2313;&#2330;&#2381;&#2330; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; &#2325;&#2375; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2343;&#2368;&#2358;&#2379;&#2306; &#2325;&#2379; &#2361;&#2335;&#2366;&#2344;&#2366;, &#2344;&#2381;&#2351;&#2366;&#2351;&#2346;&#2366;&#2354;&#2367;&#2325;&#2366; &#2325;&#2368; &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366;, &#2344;&#2381;&#2351;&#2366;&#2351;&#2367;&#2325; &#2360;&#2350;&#2368;&#2325;&#2381;&#2359;&#2366;&#2404; </span><strong>&#2347;&#2381;&#2352;&#2366;&#2306;&#2360;</strong><span style=\"font-weight: 400;\"> - &#2327;&#2339;&#2340;&#2366;&#2306;&#2340;&#2381;&#2352;&#2367;&#2325; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368;, &#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366;, &#2360;&#2350;&#2366;&#2344;&#2340;&#2366; &#2324;&#2352; &#2348;&#2306;&#2343;&#2369;&#2340;&#2381;&#2357; &#2325;&#2375; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340;&#2404; </span><strong>&#2310;&#2351;&#2352;&#2354;&#2376;&#2306;&#2337;</strong><span style=\"font-weight: 400;\"> - &#2352;&#2366;&#2332;&#2381;&#2351; &#2344;&#2368;&#2340;&#2367; &#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2325; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340;, &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2330;&#2369;&#2344;&#2366;&#2357; &#2325;&#2368; &#2357;&#2367;&#2343;&#2367;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Anant\'s mother advised him to not sleep under the trees at night as trees release _______at night.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2354;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> _______ </span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2337;&#2364;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Methane</p>\n", "<p>Carbon Dioxide</p>\n", 
                                "<p>Nitrogen</p>\n", "<p>Oxygen</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2350;&#2368;&#2341;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2381;&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2366;&#2311;&#2321;&#2325;&#2381;&#2360;&#2366;&#2311;&#2337;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2311;&#2335;&#2381;&#2352;&#2379;&#2332;&#2344;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2321;&#2325;&#2381;&#2360;&#2368;&#2332;&#2344;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> <strong>Carbon Dioxide(CO</strong><strong>2</strong><strong>)</strong><span style=\"font-weight: 400;\">.</span><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">During the day plants take in carbon dioxide (CO</span><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">) and release oxygen through photosynthesis and at night carbon dioxide (CO</span><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">) is released through respiration. </span><strong>Exception</strong><span style=\"font-weight: 400;\">: Snake plant,&nbsp; Areca palm plant, Aloe Vera plant, Tulsi, Neem plant, Peepal Tree, etc. </span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>&#2325;&#2366;&#2352;&#2381;&#2348;&#2344; &#2337;&#2366;&#2311;&#2321;&#2325;&#2381;&#2360;&#2366;&#2311;&#2337;(CO</strong><strong>2</strong><strong>)</strong><span style=\"font-weight: 400;\">&#2404; &#2342;&#2367;&#2344; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2346;&#2380;&#2343;&#2375; &#2325;&#2366;&#2352;&#2381;&#2348;&#2344; &#2337;&#2366;&#2311;&#2321;&#2325;&#2381;&#2360;&#2366;&#2311;&#2337; (CO</span><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">) &#2327;&#2381;&#2352;&#2361;&#2339; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2346;&#2381;&#2352;&#2325;&#2366;&#2358; &#2360;&#2306;&#2358;&#2381;&#2354;&#2375;&#2359;&#2339; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2321;&#2325;&#2381;&#2360;&#2368;&#2332;&#2344; &#2350;&#2369;&#2325;&#2381;&#2340; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306; &#2340;&#2341;&#2366; &#2352;&#2366;&#2340; &#2350;&#2375;&#2306; &#2358;&#2381;&#2357;&#2360;&#2344; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2346;&#2380;&#2343;&#2375; &#2325;&#2366;&#2352;&#2381;&#2348;&#2344; &#2337;&#2366;&#2311;&#2321;&#2325;&#2381;&#2360;&#2366;&#2311;&#2337; (CO</span><span style=\"font-weight: 400;\">2</span><span style=\"font-weight: 400;\">) &#2331;&#2379;&#2337;&#2364;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2309;&#2346;&#2357;&#2366;&#2342;</strong><span style=\"font-weight: 400;\"> : &#2360;&#2381;&#2344;&#2375;&#2325; &#2346;&#2381;&#2354;&#2366;&#2306;&#2335;, &#2360;&#2369;&#2346;&#2366;&#2352;&#2368; &#2325;&#2366; &#2346;&#2380;&#2343;&#2366;, &#2319;&#2354;&#2379;&#2357;&#2375;&#2352;&#2366; &#2325;&#2366; &#2346;&#2380;&#2343;&#2366;, &#2340;&#2369;&#2354;&#2360;&#2368;, &#2344;&#2368;&#2350; &#2325;&#2366; &#2346;&#2380;&#2343;&#2366;, &#2346;&#2368;&#2346;&#2354; &#2325;&#2366; &#2346;&#2375;&#2337;&#2364; &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">In</span><span style=\"font-family: Cambria Math;\"> which year was the Pitt\'s India Act passed?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">&#2346;&#2367;&#2335;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> (Pitt\'s India Act) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>1819</p>\n", "<p>1901</p>\n", 
                                "<p>1857</p>\n", "<p>1784</p>\n"],
                    options_hi: ["<p>1819</p>\n", "<p>1901</p>\n",
                                "<p>1857</p>\n", "<p>1784</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span><strong>1784</strong><span style=\"font-weight: 400;\">. Also called the East India Company Act of 1784. It was passed by the British Parliament to correct the defects of the Regulating Act of 1773. </span><strong>Provisions of the Act - </strong><span style=\"font-weight: 400;\">For political matters, the Board of Control (comprised of 6 people) was created and for commercial affairs, the Court of Directors was appointed. The Governor-General&rsquo;s council&rsquo;s strength was reduced to</span><strong> three members</strong><span style=\"font-weight: 400;\">. For the first time, the term &lsquo;British possessions in India&rsquo; was used.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">d) </span><strong>1784 </strong><span style=\"font-weight: 400;\">&#2404; &#2311;&#2360;&#2375; 1784 &#2325;&#2366; &#2312;&#2360;&#2381;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; &#2325;&#2306;&#2346;&#2344;&#2368; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2349;&#2368; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2375; 1773 &#2325;&#2375; &#2357;&#2367;&#2344;&#2367;&#2351;&#2350;&#2344; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2325;&#2375; &#2325;&#2350;&#2367;&#2351;&#2379;&#2306; &#2325;&#2379; &#2336;&#2368;&#2325; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2360;&#2306;&#2360;&#2342; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2366;&#2352;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; </span><strong>&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344;</strong><span style=\"font-weight: 400;\">- &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2325; &#2350;&#2366;&#2350;&#2354;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319;, &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2339; &#2348;&#2379;&#2352;&#2381;&#2337; (6 &#2354;&#2379;&#2327;&#2379;&#2306; &#2360;&#2375; &#2351;&#2369;&#2325;&#2381;&#2340;) &#2348;&#2344;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366; &#2324;&#2352; &#2357;&#2366;&#2339;&#2367;&#2332;&#2381;&#2351;&#2367;&#2325; &#2350;&#2366;&#2350;&#2354;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319;, &#2344;&#2367;&#2342;&#2375;&#2358;&#2325; &#2350;&#2306;&#2337;&#2354; &#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2327;&#2357;&#2352;&#2381;&#2344;&#2352;-&#2332;&#2344;&#2352;&#2354; &#2325;&#2368; &#2346;&#2352;&#2367;&#2359;&#2342; &#2325;&#2368; &#2358;&#2325;&#2381;&#2340;&#2367; &#2325;&#2379; &#2328;&#2335;&#2366;&#2325;&#2352; </span><strong>&#2340;&#2368;&#2344; &#2360;&#2342;&#2360;&#2381;&#2351;&#2368;&#2351;&nbsp; </strong><span style=\"font-weight: 400;\">&#2325;&#2352; &#2342;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;&#2404; &#2346;&#2361;&#2354;&#2368; &#2348;&#2366;&#2352;, \'&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;\' &#2358;&#2348;&#2381;&#2342; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Which among the following issues are abolished by the Article 17 of t</span><span style=\"font-family: Cambria Math;\">he Indian constitution?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2342;&#2381;&#2342;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> 17 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2342;&#2381;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>Untouchability</p>\n", "<p>Racism</p>\n", 
                                "<p>Slavery</p>\n", "<p>Colourism</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2309;&#2360;&#2381;&#2346;&#2371;&#2358;&#2381;&#2351;&#2340;&#2366; (Untouchability)</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2360;&#2381;&#2354;&#2357;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> (Racism) </span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2327;&#2369;&#2354;&#2366;&#2350;&#2368; (Slavery)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2352;&#2306;&#2327;&#2357;&#2366;&#2342; (Colourism)</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Untouchability. </strong><span style=\"font-weight: 400;\">Article 12 to 35 contained in Part III of the Constitution deals with Fundamental Rights. </span><strong>Right to Equalit</strong><span style=\"font-weight: 400;\">y (14 to 18):- </span><strong>Article 14</strong><span style=\"font-weight: 400;\"> - Equality before law. </span><strong>Article 15</strong><span style=\"font-weight: 400;\">- Prohibition of discrimination on grounds of religion, race, caste, sex or place of birth. </span><strong>Article 16</strong><span style=\"font-weight: 400;\"> - Equality of opportunity in matters of public employment.</span><strong> Article 17</strong><span style=\"font-weight: 400;\">- Abolition of Untouchability. </span><strong>Article18</strong><span style=\"font-weight: 400;\">- Abolition of all titles except military and academic.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a) <strong>&#2309;&#2360;&#2381;&#2346;&#2371;&#2358;&#2381;&#2351;&#2340;&#2366; </strong><span style=\"font-weight: 400;\">&#2404; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2375; </span><strong>&#2349;&#2366;&#2327; III</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; &#2344;&#2367;&#2361;&#2367;&#2340; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 12 &#2360;&#2375; 35, </strong><span style=\"font-weight: 400;\">&#2350;&#2380;&#2354;&#2367;&#2325; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2379;&#2306; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2350;&#2366;&#2344;&#2340;&#2366; &#2325;&#2366; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> (14 &#2360;&#2375; 18):- </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 14</strong><span style=\"font-weight: 400;\">- &#2325;&#2366;&#2344;&#2370;&#2344; &#2325;&#2375; &#2360;&#2350;&#2325;&#2381;&#2359; &#2360;&#2350;&#2366;&#2344;&#2340;&#2366;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 15</strong><span style=\"font-weight: 400;\"> - &#2343;&#2352;&#2381;&#2350;, &#2350;&#2370;&#2354;&#2357;&#2306;&#2358;, &#2332;&#2366;&#2340;&#2367;, &#2354;&#2367;&#2306;&#2327; &#2351;&#2366; &#2332;&#2344;&#2381;&#2350; &#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2375; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; &#2349;&#2375;&#2342;&#2349;&#2366;&#2357; &#2325;&#2366; &#2344;&#2367;&#2359;&#2375;&#2343;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 16 -</strong><span style=\"font-weight: 400;\"> &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2352;&#2379;&#2332;&#2327;&#2366;&#2352; &#2325;&#2375; &#2350;&#2366;&#2350;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2309;&#2357;&#2360;&#2352; &#2325;&#2368; &#2360;&#2350;&#2366;&#2344;&#2340;&#2366;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 17</strong><span style=\"font-weight: 400;\">- &#2309;&#2360;&#2381;&#2346;&#2371;&#2358;&#2381;&#2351;&#2340;&#2366; &#2325;&#2366; &#2313;&#2344;&#2381;&#2350;&#2370;&#2354;&#2344;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 18</strong><span style=\"font-weight: 400;\">- &#2360;&#2376;&#2344;&#2381;&#2351; &#2324;&#2352; &#2358;&#2376;&#2325;&#2381;&#2359;&#2339;&#2367;&#2325; &#2325;&#2379; &#2331;&#2379;&#2337;&#2364;&#2325;&#2352; &#2360;&#2349;&#2368; &#2313;&#2346;&#2366;&#2343;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2313;&#2344;&#2381;&#2350;&#2370;&#2354;&#2344;&#2404;</span></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">By which of the following process does a solid substance is directly converted into gas?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2336;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;&#2366;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;&#2343;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2376;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Deposition</p>\n", "<p>Sublimation</p>\n", 
                                "<p>Vaporisation</p>\n", "<p>Fusion</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2325;&#2381;&#2359;&#2375;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2314;&#2352;&#2381;&#2343;&#2381;&#2357;&#2346;&#2366;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2359;&#2381;&#2346;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2354;&#2351;&#2344;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> <strong>Sublimation</strong><span style=\"font-weight: 400;\">. It is an endothermic phase transition process. The substances that undergo sublimation are: ammonium chloride, iodine, camphor, naphthalene, dry ice, etc.</span><strong> Fusion- </strong><span style=\"font-weight: 400;\">It is the process by which two light atomic nuclei combine to form a single heavier one while releasing massive amounts of energy. </span><strong>Vaporization</strong><span style=\"font-weight: 400;\"> is a process in which, the liquid is converted into vapour at its boiling point.</span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>&#2314;&#2352;&#2381;&#2343;&#2381;&#2357;&#2346;&#2366;&#2340;&#2344;</strong><span style=\"font-weight: 400;\">&#2404;</span><strong> </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2319;&#2325; &#2314;&#2359;&#2381;&#2350;&#2366;&#2358;&#2379;&#2359;&#2368; &#2330;&#2352;&#2339; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2339; &#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404; &#2314;&#2352;&#2381;&#2343;&#2381;&#2357;&#2346;&#2366;&#2340;&#2344; &#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2360;&#2375; &#2327;&#2369;&#2332;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; : &#2309;&#2350;&#2379;&#2344;&#2367;&#2351;&#2350; &#2325;&#2381;&#2354;&#2379;&#2352;&#2366;&#2311;&#2337;, &#2310;&#2351;&#2379;&#2337;&#2368;&#2344;, &#2325;&#2346;&#2370;&#2352;, &#2344;&#2376;&#2347;&#2364;&#2381;&#2341;&#2364;&#2354;&#2368;&#2344;, &#2358;&#2369;&#2359;&#2381;&#2325; &#2348;&#2352;&#2381;&#2347;, &#2310;&#2342;&#2367;&#2404;&#2404; </span><strong>&#2360;&#2306;&#2354;&#2351;&#2344;-</strong><span style=\"font-weight: 400;\"> &#2357;&#2361; &#2309;&#2349;&#2367;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2361;&#2366;&#2311;&#2337;&#2381;&#2352;&#2379;&#2332;&#2344; &#2325;&#2375; &#2342;&#2379; &#2346;&#2352;&#2350;&#2366;&#2339;&#2369; &#2310;&#2346;&#2360; &#2350;&#2375;&#2306; &#2332;&#2369;&#2337;&#2364;&#2325;&#2352; &#2361;&#2368;&#2354;&#2367;&#2351;&#2350; &#2325;&#2366; &#2319;&#2325; &#2346;&#2352;&#2350;&#2366;&#2339;&#2369; &#2348;&#2344;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2357;&#2366;&#2359;&#2381;&#2346;&#2368;&#2325;&#2352;&#2339;-</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2320;&#2360;&#2368; &#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2340;&#2352;&#2354; &#2309;&#2346;&#2344;&#2375; &#2325;&#2381;&#2357;&#2341;&#2344;&#2366;&#2306;&#2325; &#2346;&#2352; &#2357;&#2366;&#2359;&#2381;&#2346; &#2350;&#2375;&#2306; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2367;&#2340; &#2361;&#2379; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> Sam wants to share his presentation with larger audience via emails. Which feature of MS PowerPoint 365 can he use for it?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2376;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2375;&#2332;&#2375;&#2306;&#2335;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2312;&#2350;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2333;&#2366;</span><span style=\"font-family: Cambria Math;\"> (share) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> MS PowerPoint 365 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Save As</p>\n", "<p>Print</p>\n", 
                                "<p>Share</p>\n", "<p>I<span style=\"font-family: Cambria Math;\">nfo</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2367;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2347;&#2379;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Shares</strong><span style=\"font-weight: 400;\">. Features of Microsoft Powerpoint 365 - Designer, Smart Lookup, QuickStarter, New charts, Morphing, Real-time collab, Version History, Zoom for non-sequential presentations. Short keys (Close PowerPoint - Ctrl+Q, Add a new slide - Ctrl+M, Insert a hyperlink - Ctrl+K, Open a presentation - Ctrl+O, Close a presentation - Ctrl+D, italic formatting - Ctrl+I, Save As - Ctrl+S).</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c) <strong>&#2358;&#2375;&#2351;&#2352;&#2404; </strong><span style=\"font-weight: 400;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2360;&#2377;&#2347;&#2381;&#2335; &#2346;&#2366;&#2357;&#2352;&#2346;&#2377;&#2311;&#2306;&#2335; 365 &#2325;&#2368; &#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;&#2319;&#2306; - &#2337;&#2367;&#2332;&#2366;&#2311;&#2344;&#2352;, &#2360;&#2381;&#2350;&#2366;&#2352;&#2381;&#2335; &#2354;&#2369;&#2325;&#2309;&#2346;, &#2325;&#2381;&#2357;&#2367;&#2325;&#2360;&#2381;&#2335;&#2366;&#2352;&#2381;&#2335;&#2352;, &#2344;&#2381;&#2351;&#2370; &#2330;&#2366;&#2352;&#2381;&#2335;, &#2350;&#2377;&#2352;&#2381;&#2347;&#2367;&#2306;&#2327;, &#2352;&#2367;&#2351;&#2354;-&#2335;&#2366;&#2311;&#2350; &#2325;&#2379;&#2354;&#2366;&#2348;(Real-time collab), &#2357;&#2352;&#2381;&#2395;&#2344;&nbsp; &#2361;&#2367;&#2360;&#2381;&#2335;&#2381;&#2352;&#2368; , &#2327;&#2376;&#2352;-&#2309;&#2344;&#2369;&#2325;&#2381;&#2352;&#2350;&#2367;&#2325; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2369;&#2340;&#2367;&#2351;&#2379;&#2306; (non-sequential presentations) &#2325;&#2375; &#2354;&#2367;&#2319; &#2332;&#2364;&#2370;&#2350;(Zoom) &#2325;&#2352;&#2344;&#2366; &#2404; &#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335; &#2325;&#2369;&#2306;&#2332;&#2368; (&#2346;&#2366;&#2357;&#2352;&#2346;&#2377;&#2311;&#2306;&#2335; &#2348;&#2306;&#2342; &#2325;&#2352;&#2375;&#2306; - Ctrl + Q, &#2344;&#2312; &#2360;&#2381;&#2354;&#2366;&#2311;&#2337; &#2332;&#2379;&#2337;&#2364;&#2375;&#2306; - Ctrl + M, &#2361;&#2366;&#2311;&#2346;&#2352;&#2354;&#2367;&#2306;&#2325; &#2337;&#2366;&#2354;&#2375;&#2306; - Ctrl + K, &#2346;&#2381;&#2352;&#2375;&#2360;&#2375;&#2306;&#2335;&#2375;&#2358;&#2344;&#2381;&#2360; &#2326;&#2379;&#2354;&#2375;&#2306; - Ctrl + O, &#2346;&#2381;&#2352;&#2375;&#2360;&#2375;&#2306;&#2335;&#2375;&#2358;&#2344;&#2381;&#2360; &#2348;&#2306;&#2342; &#2325;&#2352;&#2375;&#2306; - Ctrl + D, &#2311;&#2335;&#2376;&#2354;&#2367;&#2325; &#2347;&#2377;&#2352;&#2381;&#2350;&#2376;&#2335; - Ctrl + I, &#2347;&#2366;&#2311;&#2354; &#2360;&#2375;&#2357; &#2325;&#2352;&#2344;&#2366; - Ctrl + S)&#2404; </span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> Which of the following is not an important river of Malwa plateau?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2336;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Chambal <span style=\"font-family: Cambria Math;\">river</span></p>\n", "<p>Mahanadi <span style=\"font-family: Cambria Math;\">river</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>Ken <span style=\"font-family: Cambria Math;\">river</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Betwa <span style=\"font-family: Cambria Math;\">river</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2330;&#2306;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Chambal river) </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Mahanadi river) </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> (ken river) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2348;&#2375;&#2340;&#2357;&#2366; &#2344;&#2342;&#2368; (Betwa river)</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Mahanadi. </strong><span style=\"font-weight: 400;\">This river rises from a place in Sihawa, near Raipur in the state of Chhattisgarh to the south of Amarkantak and the river flows through the states of Chhattisgarh and Odisha before finally ending in the Bay of Bengal. It is also known for the </span><strong>Hirakud Dam. River of Malwa plateau</strong><span style=\"font-weight: 400;\"> - Mahi River, Ken rivers, Shipra River, Chambal River, Betwa River.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>&#2350;&#2361;&#2366;&#2344;&#2342;&#2368;&#2404; </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2344;&#2342;&#2368; &#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364; &#2352;&#2366;&#2332;&#2381;&#2351; &#2350;&#2375;&#2306; &#2352;&#2366;&#2351;&#2346;&#2369;&#2352; &#2325;&#2375; &#2346;&#2366;&#2360; &#2360;&#2367;&#2361;&#2366;&#2357;&#2366; &#2325;&#2375; &#2319;&#2325; &#2360;&#2381;&#2341;&#2366;&#2344; &#2360;&#2375; &#2309;&#2350;&#2352;&#2325;&#2306;&#2335;&#2325; &#2325;&#2375; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2350;&#2375;&#2306; &#2344;&#2367;&#2325;&#2354;&#2340;&#2368; &#2361;&#2376; &#2324;&#2352; &#2351;&#2361; &#2344;&#2342;&#2368; &#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364; &#2324;&#2352; &#2323;&#2337;&#2367;&#2358;&#2366; &#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306; &#2360;&#2375; &#2361;&#2379;&#2325;&#2352; &#2348;&#2361;&#2340;&#2368; &#2361;&#2376; &#2340;&#2341;&#2366; &#2309;&#2306;&#2340; &#2350;&#2375;&#2306; &#2348;&#2306;&#2327;&#2366;&#2354; &#2325;&#2368; &#2326;&#2366;&#2337;&#2364;&#2368; &#2350;&#2375;&#2306; &#2350;&#2367;&#2354; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404; &#2351;&#2361; </span><strong>&#2361;&#2368;&#2352;&#2366;&#2325;&#2369;&#2306;&#2337; &#2348;&#2366;&#2306;&#2343; </strong><span style=\"font-weight: 400;\">&#2325;&#2375; &#2354;&#2367;&#2319; &#2349;&#2368; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2366;&#2354;&#2357;&#2366; &#2346;&#2336;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> &#2325;&#2368; &#2344;&#2342;&#2368; - &#2350;&#2366;&#2361;&#2368; &#2344;&#2342;&#2368;, &#2325;&#2375;&#2344; &#2344;&#2342;&#2368;, &#2358;&#2367;&#2346;&#2381;&#2352;&#2366; &#2344;&#2342;&#2368;, &#2330;&#2306;&#2348;&#2354; &#2344;&#2342;&#2368;, &#2348;&#2375;&#2340;&#2357;&#2366; &#2344;&#2342;&#2368;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">In</span><span style=\"font-family: Cambria Math;\"> India, trees of which forest type shed their leaves for about six to eight weeks in dry summer?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2359;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2368;&#2359;&#2381;&#2350;&#2325;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2349;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2346;&#2381;&#2340;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2340;&#2367;&#2351;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2367;&#2352;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Tropical Deciduous Forests</p>\n", "<p>Tropical Evergreen Forests</p>\n", 
                                "<p>Montane Forests</p>\n", "<p>Tropical Thorn Forests and Scrubs</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2359;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2381;&#2339;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2359;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2342;&#2366;&#2348;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2381;&#2357;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2359;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2335;&#2368;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2333;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Tropical Deciduous Forests (monsoon forests).</strong><span style=\"font-weight: 400;\"> These forests spread over regions which receive rainfall between 70-200 cm. Tendu, palas, amaltas, bel, khair, axlewood, etc. are the common trees of these forests.</span><strong>Tropical Evergreen Forests</strong><span style=\"font-weight: 400;\"> - These forests are found in the western slope of the Western Ghats, hills of the northeastern region and the Andaman and Nicobar Islands which rainfall above 200 cm and temperature above 22</span><span style=\"font-weight: 400;\">o</span><span style=\"font-weight: 400;\">C.</span><strong>Tropical thorn forests</strong><span style=\"font-weight: 400;\"> occur in the areas which receive rainfall less than 70 cm. </span><strong>Montane forests</strong><span style=\"font-weight: 400;\"> are found on mountain slopes and it is a type of mountain ecosystem. It gets affected by colder climate at moderate elevations. In these areas dense forests are common.</span></p>\n",
                    solution_hi: "<p>8.(a)&nbsp;<strong>&#2313;&#2359;&#2381;&#2339;&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351; &#2346;&#2352;&#2381;&#2339;&#2346;&#2366;&#2340;&#2368; &#2357;&#2344;&#2379;&#2306;</strong><span style=\"font-weight: 400;\"> &#2325;&#2379; &#2350;&#2366;&#2344;&#2360;&#2370;&#2344;&#2368; &#2357;&#2344; &#2349;&#2368; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2375; &#2357;&#2344; &#2313;&#2344; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2347;&#2376;&#2354;&#2375; &#2361;&#2369;&#2319; &#2361;&#2376;&#2306; &#2332;&#2361;&#2366;&#2305; 70-200 &#2360;&#2375;&#2350;&#2368; &#2325;&#2375; &#2348;&#2368;&#2330; &#2357;&#2352;&#2381;&#2359;&#2366; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; &#2340;&#2375;&#2306;&#2342;&#2370;, &#2346;&#2354;&#2366;&#2360;, &#2309;&#2350;&#2354;&#2340;&#2366;&#2360;, &#2348;&#2375;&#2354;, &#2326;&#2376;&#2352;, &#2319;&#2325;&#2381;&#2360;&#2354;&#2357;&#2369;&#2337; &#2310;&#2342;&#2367; &#2311;&#2344; &#2357;&#2344;&#2379;&#2306; &#2325;&#2375; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351; &#2357;&#2371;&#2325;&#2381;&#2359; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2313;&#2359;&#2381;&#2339;&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351; &#2360;&#2342;&#2366;&#2348;&#2361;&#2366;&#2352; &#2357;&#2344; </strong><span style=\"font-weight: 400;\">- &#2351;&#2375; &#2357;&#2344; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2328;&#2366;&#2335; &#2325;&#2375; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2338;&#2354;&#2366;&#2344;, &#2346;&#2370;&#2352;&#2381;&#2357;&#2379;&#2340;&#2381;&#2340;&#2352; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2368; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2324;&#2352; &#2309;&#2306;&#2337;&#2350;&#2366;&#2344; &#2324;&#2352; &#2344;&#2367;&#2325;&#2379;&#2348;&#2366;&#2352; &#2342;&#2381;&#2357;&#2368;&#2346; &#2360;&#2350;&#2370;&#2361; &#2350;&#2375;&#2306; &#2346;&#2366;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2332;&#2361;&#2366;&#2306; 200 &#2360;&#2375;&#2350;&#2368; &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2357;&#2352;&#2381;&#2359;&#2366; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376; &#2324;&#2352; &#2340;&#2366;&#2346;&#2350;&#2366;&#2344; 22&deg;C &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2313;&#2359;&#2381;&#2339;&#2325;&#2335;&#2367;&#2348;&#2306;&#2343;&#2368;&#2351; &#2325;&#2306;&#2335;&#2368;&#2354;&#2375; &#2357;&#2344; </strong><span style=\"font-weight: 400;\">&#2313;&#2344; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2346;&#2366;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306; &#2332;&#2361;&#2366;&#2305;</span><strong> 70 &#2360;&#2375;&#2350;&#2368;</strong><span style=\"font-weight: 400;\"> &#2360;&#2375; &#2325;&#2350; &#2357;&#2352;&#2381;&#2359;&#2366; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2346;&#2352;&#2381;&#2357;&#2340;&#2368;&#2351; &#2357;&#2344;,</strong><span style=\"font-weight: 400;\"> &#2346;&#2352;&#2381;&#2357;&#2340;&#2368;&#2351; &#2338;&#2366;&#2354;&#2379;&#2306; &#2346;&#2352; &#2346;&#2366;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2351;&#2361; &#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2346;&#2352;&#2381;&#2357;&#2340;&#2368;&#2351; &#2346;&#2366;&#2352;&#2367;&#2340;&#2306;&#2340;&#2381;&#2352; &#2361;&#2376;&#2404; &#2351;&#2361; &#2350;&#2343;&#2381;&#2351;&#2350; &#2314;&#2306;&#2330;&#2366;&#2312; &#2346;&#2352; &#2336;&#2306;&#2337;&#2375; &#2350;&#2380;&#2360;&#2350; &#2360;&#2375; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2367;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2344; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2307;&nbsp; &#2328;&#2344;&#2375; &#2332;&#2306;&#2327;&#2354; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">Sanchi</span><span style=\"font-family: Cambria Math;\"> stupa is located in which states?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2306;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2340;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;</span><span style=\"font-family: Nirmala UI;\">&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;</span><span style=\"font-family: Nirmala UI;\">&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Maharashtra</p>\n", "<p>Bihar</p>\n", 
                                "<p>Madhya Pradesh</p>\n", "<p>Gujarat</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2332;&#2352;&#2366;&#2340;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>Madhya Pradesh</strong><span style=\"font-weight: 400;\">. Sanchi Stupa was built by Mauryan emperor </span><strong>Ashoka</strong><span style=\"font-weight: 400;\">. It&nbsp; became the World Heritage site by UNESCO in </span><strong>1989</strong><span style=\"font-weight: 400;\">. </span><strong>Other Famous Stupas in India</strong><span style=\"font-weight: 400;\"> - Mahabodhi Stupa, Bodh Gaya (Bihar); Shanti Stupa, Leh(Ladakh); Dhamekh Stupa, Sarnath (Uttar Pradesh); Amaravati Stupa (Andhra Pradesh); Dro-dul Chorten Stupa (Sikkim).</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>&#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</strong><span style=\"font-weight: 400;\">&#2404; &#2360;&#2366;&#2306;&#2330;&#2368; &#2360;&#2381;&#2340;&#2370;&#2346; &#2350;&#2380;&#2352;&#2381;&#2351; &#2360;&#2350;&#2381;&#2352;&#2366;&#2335; </span><strong>&#2309;&#2358;&#2379;&#2325;</strong><span style=\"font-weight: 400;\"> &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2348;&#2344;&#2357;&#2366;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2351;&#2361; </span><strong>1989</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; UNESCO &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2357;&#2367;&#2358;&#2381;&#2357; &#2343;&#2352;&#2379;&#2361;&#2352; &#2360;&#2381;&#2341;&#2354; &#2348;&#2344; &#2327;&#2351;&#2366;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2309;&#2344;&#2381;&#2351; &#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343; &#2360;&#2381;&#2340;&#2370;&#2346;</strong><span style=\"font-weight: 400;\"> -</span><strong> &#2350;&#2361;&#2366;&#2348;&#2379;&#2343;&#2367; &#2360;&#2381;&#2340;&#2370;&#2346;</strong><span style=\"font-weight: 400;\">, &#2348;&#2379;&#2343;&#2327;&#2351;&#2366; (&#2348;&#2367;&#2361;&#2366;&#2352;)&#2404; </span><strong>&#2358;&#2366;&#2306;&#2340;&#2367; &#2360;&#2381;&#2340;&#2370;&#2346;</strong><span style=\"font-weight: 400;\">, &#2354;&#2375;&#2361; (&#2354;&#2342;&#2381;&#2342;&#2366;&#2326;)&#2404; </span><strong>&#2343;&#2350;&#2375;&#2326; &#2360;&#2381;&#2340;&#2370;&#2346;,</strong><span style=\"font-weight: 400;\"> &#2360;&#2366;&#2352;&#2344;&#2366;&#2341; (&#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;)&#2404; </span><strong>&#2309;&#2350;&#2352;&#2366;&#2357;&#2340;&#2368; &#2360;&#2381;&#2340;&#2370;&#2346; (</strong><span style=\"font-weight: 400;\">&#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;)&#2404; </span><strong>&#2337;&#2379;-&#2337;&#2381;&#2352;&#2369;&#2354; &#2330;&#2379;&#2352;&#2381;&#2335;&#2375;&#2344; &#2360;&#2381;&#2340;&#2370;&#2346;</strong><span style=\"font-weight: 400;\"> (&#2360;&#2367;&#2325;&#2381;&#2325;&#2367;&#2350;)&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which among the following has written the book \'The Pregnancy Bible\'?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Nirmala UI;\">&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> \'</span><span style=\"font-family: Nirmala UI;\">&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2375;&#2327;&#2381;&#2344;&#2375;&#2306;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2311;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Vidhya Balan</p>\n", "<p>Shilpa Shetty</p>\n", 
                                "<p>Rani Mukherjee</p>\n", "<p>Kareena Kapoor</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2342;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2367;&#2354;&#2381;&#2346;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2335;&#2381;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2352;&#2381;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2368;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2346;&#2370;&#2352;</span></p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(d)&nbsp;</span><strong>Kareena Kapoor</strong><span style=\"font-weight: 400;\">. Shilpa Shetty - The Great Indian Diet, and Diary Of A Domestic Diva. Priyanka Chopra - Unfinished. Michael Jackson &ndash; MoonWalk. Shashi Kapoor &ndash; the Householder.</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(d)&nbsp;</span><strong>&#2325;&#2352;&#2368;&#2344;&#2366; &#2325;&#2346;&#2370;&#2352;</strong><span style=\"font-weight: 400;\">&#2404;</span><strong> &#2358;&#2367;&#2354;&#2381;&#2346;&#2366; &#2358;&#2375;&#2335;&#2381;&#2335;&#2368;</strong><span style=\"font-weight: 400;\"> - &#2342; &#2327;&#2381;&#2352;&#2375;&#2335; &#2311;&#2306;&#2337;&#2367;&#2351;&#2344; &#2337;&#2366;&#2311;&#2335;, &#2319;&#2306;&#2337; &#2337;&#2366;&#2351;&#2352;&#2368; &#2321;&#2347;&#2364; &#2319; &#2337;&#2379;&#2350;&#2375;&#2360;&#2381;&#2335;&#2367;&#2325; &#2342;&#2367;&#2357;&#2366;&#2404; </span><strong>&#2346;&#2381;&#2352;&#2367;&#2351;&#2306;&#2325;&#2366; &#2330;&#2379;&#2346;&#2337;&#2364;&#2366; </strong><span style=\"font-weight: 400;\">- &#2309;&#2344;&#2347;&#2367;&#2344;&#2367;&#2358;&#2381;&#2337;&#2404; </span><strong>&#2350;&#2366;&#2311;&#2325;&#2354; &#2332;&#2376;&#2325;&#2381;&#2360;&#2344; </strong><span style=\"font-weight: 400;\">- &#2350;&#2370;&#2344;&#2357;&#2377;&#2325;&#2404; </span><strong>&#2358;&#2358;&#2367; &#2325;&#2346;&#2370;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2342; &#2361;&#2366;&#2313;&#2360;&#2361;&#2379;&#2354;&#2381;&#2337;&#2352;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Who</span><span style=\"font-family: Cambria Math;\"> won the women\'s singles title at Wimbledon 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2357;</span><span style=\"font-family: Nirmala UI;\">&#2367;&#2350;&#2381;&#2348;&#2354;&#2337;&#2344;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2367;&#2340;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Danielle Collins</p>\n", "<p>Elena Rybakina</p>\n", 
                                "<p>Ons Jabeur</p>\n", "<p>Ashleigh Barty</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2337;&#2375;&#2344;&#2367;&#2351;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2377;&#2354;&#2367;&#2344;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2319;&#2354;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2367;&#2348;&#2366;&#2325;&#2367;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2323;&#2344;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2375;&#2348;&#2381;&#2351;&#2369;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2319;&#2358;&#2381;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;&#2381;&#2335;&#2368;</span></p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(b)<strong>Elena Rybakina</strong><span style=\"font-weight: 400;\"> (player, Kazakhstani). Men&rsquo;s singles title at Wimbledon 2022- </span><strong>Novak Djokovic </strong><span style=\"font-weight: 400;\">(Player, Serbia). </span><strong>Roger Federer</strong><span style=\"font-weight: 400;\"> holds an all-time record of eight Wimbledon titles. Types of courts which are used in tennis :- </span><strong>Hard Court -</strong><span style=\"font-weight: 400;\"> The US Open and Australian Open, </span><strong>Grass Court - </strong><span style=\"font-weight: 400;\">Wimbledon,</span><strong> Clay Court -</strong><span style=\"font-weight: 400;\"> French Open.</span></span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(b)</span><strong>&#2319;&#2354;&#2375;&#2344;&#2366; &#2352;&#2351;&#2348;&#2325;&#2367;&#2344;&#2366; (</strong><span style=\"font-weight: 400;\">&#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;- &#2325;&#2332;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;)&#2404; &#2357;&#2367;&#2350;&#2381;&#2348;&#2354;&#2337;&#2344; 2022 &#2350;&#2375;&#2306; &#2346;&#2369;&#2352;&#2369;&#2359; &#2319;&#2325;&#2354; &#2326;&#2367;&#2340;&#2366;&#2348; - </span><strong>&#2344;&#2379;&#2357;&#2366;&#2325; &#2332;&#2379;&#2325;&#2379;&#2357;&#2367;&#2330;</strong><span style=\"font-weight: 400;\"> (&#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;- &#2360;&#2352;&#2381;&#2348;&#2367;&#2351;&#2366;)&#2404; </span><strong>&#2352;&#2379;&#2332;&#2352; &#2347;&#2375;&#2337;&#2352;&#2352;</strong><span style=\"font-weight: 400;\"> &#2325;&#2375; &#2344;&#2366;&#2350; &#2310;&#2336; &#2357;&#2367;&#2306;&#2348;&#2354;&#2337;&#2344; &#2326;&#2367;&#2340;&#2366;&#2348; &#2325;&#2366; &#2360;&#2352;&#2381;&#2357;&#2325;&#2366;&#2354;&#2367;&#2325; &#2352;&#2367;&#2325;&#2377;&#2352;&#2381;&#2337; &#2361;&#2376;&#2404; &#2335;&#2375;&#2344;&#2367;&#2360; &#2350;&#2375;&#2306; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2319; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2325;&#2379;&#2352;&#2381;&#2335; &#2325;&#2375; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;:- &#2361;&#2366;&#2352;&#2381;&#2337; &#2325;&#2379;&#2352;&#2381;&#2335; - &#2342; &#2351;&#2370;&#2319;&#2360; &#2323;&#2346;&#2344; &#2324;&#2352; &#2321;&#2360;&#2381;&#2335;&#2381;&#2352;&#2375;&#2354;&#2367;&#2351;&#2344; &#2323;&#2346;&#2344;, &#2327;&#2381;&#2352;&#2366;&#2360; &#2325;&#2379;&#2352;&#2381;&#2335; - &#2357;&#2367;&#2306;&#2348;&#2354;&#2337;&#2344;, &#2325;&#2381;&#2354;&#2375; &#2325;&#2379;&#2352;&#2381;&#2335; - &#2347;&#2381;&#2352;&#2375;&#2306;&#2330; &#2323;&#2346;&#2344;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">. Who founded Trinamool Congress in 1998 and became its Chairperson, after separating from the Indian National Congress?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Nirmala UI;\">&#2325;</span><span style=\"font-family: Nirmala UI;\">&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 1998 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2371;&#2339;&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Jayaram Jayalalithaa</p>\n", "<p>Kanimozhi</p>\n", 
                                "<p>Supriya Sule</p>\n", "<p>Mamata Banerjee</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2351;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2351;&#2354;&#2354;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2344;&#2367;&#2350;&#2379;&#2333;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2346;&#2381;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2350;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2352;&#2381;&#2332;&#2368;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Mamata Banerjee</strong><span style=\"font-weight: 400;\"> (Founder and first chairperson). Symbol of the Trinamool Congress (TMC) - Jora Ghas Phul. Slogan of TMC - Ma Mati Manush. </span><strong>Jayaram Jayalalithaa</strong><span style=\"font-weight: 400;\"> was an Indian politician and actress who served as Chief Minister of Tamil Nadu for more than fourteen years over six terms between 1991 and 2016. </span><strong>Supriya Sule </strong><span style=\"font-weight: 400;\">is a Nationalist Congress Party leader who has been elected for three consecutive terms from Baramati Lok Sabha Constituency (Maharashtra).</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(d) <strong>&#2350;&#2350;&#2340;&#2366; &#2348;&#2344;&#2352;&#2381;&#2332;&#2368;</strong><span style=\"font-weight: 400;\"> (&#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2346;&#2325; &#2324;&#2352; &#2346;&#2361;&#2354;&#2368; &#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;)&#2404;&nbsp; &#2340;&#2371;&#2339;&#2350;&#2370;&#2354; &#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360; (TMC) &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325; - &#2332;&#2379;&#2352;&#2366; &#2328;&#2366;&#2360; &#2347;&#2370;&#2354;&#2404; TMC &#2325;&#2366; &#2344;&#2366;&#2352;&#2366; - &#2350;&#2366; &#2350;&#2366;&#2335;&#2368; &#2350;&#2366;&#2344;&#2369;&#2359;&#2404; </span><strong>&#2332;&#2351;&#2352;&#2366;&#2350; &#2332;&#2351;&#2354;&#2354;&#2367;&#2340;&#2366;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2352;&#2366;&#2332;&#2344;&#2368;&#2340;&#2367;&#2332;&#2381;&#2334; &#2324;&#2352; &#2309;&#2349;&#2367;&#2344;&#2375;&#2340;&#2381;&#2352;&#2368; &#2341;&#2368;&#2306;, &#2332;&#2367;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; 1991 &#2324;&#2352; 2016 &#2325;&#2375; &#2348;&#2368;&#2330; &#2331;&#2361; &#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2366;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2330;&#2380;&#2342;&#2361; &#2357;&#2352;&#2381;&#2359;&#2379;&#2306; &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2360;&#2350;&#2351; &#2340;&#2325; &#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369; &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2367;&#2351;&#2366;&#2404; </span><strong>&#2360;&#2369;&#2346;&#2381;&#2352;&#2367;&#2351;&#2366; &#2360;&#2369;&#2354;&#2375;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2357;&#2366;&#2342;&#2368; &#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360; &#2346;&#2366;&#2352;&#2381;&#2335;&#2368; &#2325;&#2368; &#2344;&#2375;&#2340;&#2366; &#2361;&#2376;&#2306;, &#2332;&#2379; &#2348;&#2366;&#2352;&#2366;&#2350;&#2340;&#2368; &#2354;&#2379;&#2325;&#2360;&#2349;&#2366; &#2344;&#2367;&#2352;&#2381;&#2357;&#2366;&#2330;&#2344; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; (&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;) &#2360;&#2375; &#2354;&#2327;&#2366;&#2340;&#2366;&#2352; &#2340;&#2368;&#2344; &#2348;&#2366;&#2352; &#2344;&#2367;&#2352;&#2381;&#2357;&#2366;&#2330;&#2367;&#2340; &#2361;&#2369;&#2312; &#2361;&#2376;&#2306;&#2404;</span></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">Which</span><span style=\"font-family: Cambria Math;\"> of the following pairs of \"tournament -format\" is correct? </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. Syed Mushtaq Ali Trophy - T20 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. Vijay Hazare Trophy - 50 o</span><span style=\"font-family: Cambria Math;\">vers</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> \"</span><span style=\"font-family: Nirmala UI;\">&#2326;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2351;&#2379;&#2327;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Nirmala UI;\">&#2347;</span><span style=\"font-family: Nirmala UI;\">&#2377;&#2352;&#2381;&#2350;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\">\" </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2327;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? - </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2376;&#2351;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2358;&#2381;&#2340;&#2366;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2377;&#2347;&#2368;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\">20</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2405;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2332;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2332;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2377;&#2347;&#2368;</span><span style=\"font-family: Cambria Math;\"> - 50 </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2357;&#2352;</span></p>\n",
                    options_en: ["<p>Only I</p>\n", "<p>Neither I nor II</p>\n", 
                                "<p>Only II</p>\n", "<p>Both I and II</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2405;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(d)<strong>Both I and II</strong><span style=\"font-weight: 400;\">. Syed Mushtaq Ali Trophy - T20 (20 overs), Vijay Hazare Trophy - One day (50 overs), </span><strong>Vijay Hazare Trophy </strong><span style=\"font-weight: 400;\">(Ranji One Day Trophy): It was started in 2002-03 as a limited-overs cricket domestic competition involving state teams from the Ranji Trophy plates. It is named after the famous Indian cricketer Vijay Hazare. &nbsp; &nbsp; &nbsp; </span></span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>I &#2324;&#2352; II &#2342;&#2379;&#2344;&#2379;&#2306;</strong><span style=\"font-weight: 400;\">&#2404; &#2360;&#2376;&#2351;&#2342; &#2350;&#2369;&#2358;&#2381;&#2340;&#2366;&#2325; &#2309;&#2354;&#2368; &#2335;&#2381;&#2352;&#2377;&#2347;&#2368; - &#2335;&#2368;20 (20 &#2323;&#2357;&#2352;), &#2357;&#2367;&#2332;&#2351; &#2361;&#2332;&#2366;&#2352;&#2375; &#2335;&#2381;&#2352;&#2377;&#2347;&#2368; - &#2357;&#2344; &#2337;&#2375; (50 &#2323;&#2357;&#2352;)&#2404; </span><strong>&#2357;&#2367;&#2332;&#2351; &#2361;&#2332;&#2366;&#2352;&#2375; &#2335;&#2381;&#2352;&#2377;&#2347;&#2368; </strong><span style=\"font-weight: 400;\">(&#2352;&#2339;&#2332;&#2368; &#2357;&#2344; &#2337;&#2375; &#2335;&#2381;&#2352;&#2377;&#2347;&#2368;):&#2311;&#2360;&#2375; 2002-03 &#2350;&#2375;&#2306; &#2360;&#2368;&#2350;&#2367;&#2340; &#2323;&#2357;&#2352;&#2379;&#2306; &#2325;&#2368; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2328;&#2352;&#2375;&#2354;&#2370; &#2346;&#2381;&#2352;&#2340;&#2367;&#2351;&#2379;&#2327;&#2367;&#2340;&#2366; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2358;&#2369;&#2352;&#2370; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2352;&#2339;&#2332;&#2368; &#2335;&#2381;&#2352;&#2377;&#2347;&#2368; &#2346;&#2381;&#2354;&#2375;&#2335;&#2381;&#2360; &#2360;&#2375; &#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2368; &#2335;&#2368;&#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2341;&#2368;&#2306;&#2404; &#2311;&#2360;&#2325;&#2366; &#2344;&#2366;&#2350; &#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335;&#2352; &#2357;&#2367;&#2332;&#2351; &#2361;&#2332;&#2366;&#2352;&#2375; &#2325;&#2375; &#2344;&#2366;&#2350; &#2346;&#2352; &#2352;&#2326;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The National Payments Co</span><span style=\"font-family: Cambria Math;\">rporation of India was set up under the provisions of which act?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2327;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2361;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;</span><span style=\"font-family: Nirmala UI;\">&#2368;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>The SARFAESI Act, 2002</p>\n", "<p>Payment and Settlement Systems Act, 2007</p>\n", 
                                "<p>Government Securities Act, 2006</p>\n", "<p>Companies Act, 2013</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2347;&#2375;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> (SARFAESI) </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\">, 2002 </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2346;&#2335;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\">, 2007 </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\">, 2006 </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\">, 2013</span></p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-weight: 400;\">&nbsp;</span><strong>Payment and Settlement Systems Act, 2007</strong><span style=\"font-weight: 400;\">: It is an Act to provide for the regulation and supervision of payment systems in India and to designate the Reserve Bank of India as the authority for that purpose and for matters connected therewith or incidental thereto. This organisation was founded in the year 2008 under this Act of 2007.&nbsp;</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>&#2349;&#2369;&#2327;&#2340;&#2366;&#2344; &#2324;&#2352; &#2344;&#2367;&#2346;&#2335;&#2366;&#2344; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;, 2007: </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2349;&#2369;&#2327;&#2340;&#2366;&#2344; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2357;&#2367;&#2344;&#2367;&#2351;&#2350;&#2344; &#2324;&#2352; &#2346;&#2352;&#2381;&#2351;&#2357;&#2375;&#2325;&#2381;&#2359;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2324;&#2352; &#2313;&#2360; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2325;&#2375; &#2354;&#2367;&#2319; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2352;&#2367;&#2332;&#2352;&#2381;&#2357; &#2348;&#2376;&#2306;&#2325; &#2325;&#2379; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2344;&#2366;&#2350;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2313;&#2360;&#2360;&#2375; &#2332;&#2369;&#2337;&#2364;&#2375; &#2351;&#2366; &#2346;&#2381;&#2352;&#2366;&#2360;&#2306;&#2327;&#2367;&#2325; &#2350;&#2366;&#2350;&#2354;&#2379;&#2306; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2361;&#2376;&#2404; &#2311;&#2360; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;, &#2357;&#2352;&#2381;&#2359; 2008 &#2350;&#2375;&#2306; 2007 &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; &#2325;&#2375; &#2340;&#2361;&#2340; &#2325;&#2368; &#2327;&#2312; &#2341;&#2368;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which of the following statements is correct regarding the socialist society in the economy?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. The government decides what goods are to be produced.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. Distribution of goods under socialism is supposed to be based on what people need.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;</span><span style=\"font-family: Nirmala UI;\">&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2332;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2342;</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2339;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2405;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2332;&#2357;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2340;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>Only I</p>\n", "<p>Neither I nor II</p>\n", 
                                "<p>Both I and II</p>\n", "<p>Only II</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2405;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>I <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2405;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\">I</span></p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Both I and II</strong><span style=\"font-weight: 400;\">. </span><strong>Socialist Economy</strong><span style=\"font-weight: 400;\"> decides on the products and services manufactured in line with society&rsquo;s requirements. It also have complete control over the factors of production. The main motive for producing goods and services in this economy is social welfare and not profit.</span><strong> Example</strong><span style=\"font-weight: 400;\"> - China and Russia.In India -Mix Economy.</span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(c)<strong>&nbsp;</strong></span><strong>&nbsp;I &#2324;&#2352; II &#2342;&#2379;&#2344;&#2379;&#2306;&#2404; &#2360;&#2350;&#2366;&#2332;&#2357;&#2366;&#2342;&#2368; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; </strong><span style=\"font-weight: 400;\">&#2360;&#2350;&#2366;&#2332; &#2325;&#2368; &#2310;&#2357;&#2358;&#2381;&#2351;&#2325;&#2340;&#2366;&#2323;&#2306; &#2325;&#2375; &#2309;&#2344;&#2369;&#2352;&#2370;&#2346; &#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2379;&#2306; &#2324;&#2352; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2346;&#2352; &#2344;&#2367;&#2352;&#2381;&#2339;&#2351; &#2354;&#2375;&#2340;&#2368; &#2361;&#2376;&#2404; &#2311;&#2360;&#2325;&#2375; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2375; &#2325;&#2366;&#2352;&#2325;&#2379;&#2306; &#2346;&#2352; &#2349;&#2368; &#2346;&#2370;&#2352;&#2381;&#2339; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2339; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2350;&#2375;&#2306; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2324;&#2352; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2360;&#2366;&#2350;&#2366;&#2332;&#2367;&#2325; &#2325;&#2354;&#2381;&#2351;&#2366;&#2339; &#2361;&#2376; &#2344; &#2325;&#2367; &#2354;&#2366;&#2349; &#2313;&#2336;&#2366;&#2344;&#2366;&#2404; </span><strong>&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</strong><span style=\"font-weight: 400;\"> - &#2330;&#2368;&#2344; &#2324;&#2352; &#2352;&#2370;&#2360;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; </strong><span style=\"font-weight: 400;\">- &#2350;&#2367;&#2358;&#2381;&#2352;&#2367;&#2340; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Natural language processing (NLP) is a branch of artificial intelligence</span><span style=\"font-family: Cambria Math;\"> that helps computers_______ human language.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. understand</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. Interpret</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">III. </span><span style=\"font-family: Cambria Math;\">manipulate</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">16. </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Nirmala UI;\">&#2375;&#2330;&#2369;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> (NLP) </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2381;&#2335;&#2367;&#2347;&#2367;&#2358;&#2367;&#2351;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2335;&#2375;&#2354;&#2367;&#2332;&#2375;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> _______. </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2342;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2333;&#2344;&#2375;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2405;.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">III. </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2375;&#2352;&#2347;&#2375;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span></p>\n",
                    options_en: ["<p>I, II and III</p>\n", "<p>only I and III</p>\n", 
                                "<p>only I and II</p>\n", "<p>only II and III</p>\n"],
                    options_hi: ["<p>I, II <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> III </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> I </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> III </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\">I </span></p>\n", "<p>II <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> III</span></p>\n"],
                    solution_en: "<p>16<span style=\"font-family: Cambria Math;\">.(a)<strong> &nbsp;I, II and III. <span style=\"font-weight: 400;\">Natural language processing has the ability to interrogate the data with natural language text or voice. Other computer languages:- COBOL (Dr. Grace Murray Hopper- 1959), BASIC (John G. Kemeny and Thomas E Kurtz -1964), C++ ( Bjarne Stroustrup -&nbsp; 1983), JavaScript (Brendan Eich - 1995). </span></strong></span></p>\n",
                    solution_hi: "<p>16<span style=\"font-family: Cambria Math;\">.(a)&nbsp;</span><strong>I, II and III </strong><span style=\"font-weight: 400;\">&#2404; &#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325; &#2349;&#2366;&#2359;&#2366; &#2325;&#2375; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360; &#2350;&#2375;&#2306; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325; &#2349;&#2366;&#2359;&#2366;, &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2351;&#2366; &#2357;&#2377;&#2351;&#2360; &#2325;&#2375; &#2360;&#2366;&#2341; &#2346;&#2370;&#2331;&#2340;&#2366;&#2331; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2325;&#2381;&#2359;&#2350;&#2340;&#2366; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; &#2309;&#2344;&#2381;&#2351; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2349;&#2366;&#2359;&#2366;&#2319;&#2305;: - COBOL (&#2337;&#2377;. &#2327;&#2381;&#2352;&#2375;&#2360; &#2350;&#2352;&#2375; &#2361;&#2377;&#2346;&#2352;- 1959), BASIC (&#2332;&#2377;&#2344; &#2332;&#2368;. &#2325;&#2375;&#2350;&#2375;&#2344;&#2368; &#2324;&#2352; &#2341;&#2377;&#2350;&#2360; &#2312; &#2325;&#2352;&#2381;&#2335;&#2332;&#2364; -1964)&#2404;&nbsp; C++ (&#2348;&#2332;&#2364;&#2381;&#2344;&#2375; &#2360;&#2381;&#2335;&#2381;&#2352;&#2377;&#2360;&#2381;&#2335;&#2381;&#2352;&#2369;&#2346; - 1983), JavaScript (&#2348;&#2381;&#2352;&#2375;&#2306;&#2337;&#2344; &#2312;&#2330; - 1995)&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Which</span><span style=\"font-family: Cambria Math;\"> among the following teams was defeated in the Asia Cup Finals 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">17. </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Nirmala UI;\">&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2368;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2358;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2366;&#2311;&#2344;&#2354;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Pakistan</p>\n", "<p>Sri Lanka</p>\n", 
                                "<p>Afghanistan</p>\n", "<p>India</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2368;&#2354;&#2306;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2347;&#2364;&#2327;&#2364;&#2366;&#2344;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span></p>\n"],
                    solution_en: "<p>17<span style=\"font-family: Cambria Math;\">.(a)</span><span style=\"font-family: Cambria Math;\"> <strong>Pakistan.</strong><span style=\"font-weight: 400;\"> </span><strong>Asia cup 2022</strong><span style=\"font-weight: 400;\"> (15th edition), Host(s) - Sri Lanka, Venue - UAE (United Arab Emirates). Winner - Sri Lanka,&nbsp; </span><strong>Asia cup 2018</strong><span style=\"font-weight: 400;\"> (Winner - India, Runner-up - Bangladesh). </span><strong>Asian cricket council</strong><span style=\"font-weight: 400;\">, established - 1983,&nbsp; Headquarter - Colombo, Sri Lanka.</span></span></p>\n",
                    solution_hi: "<p>17<span style=\"font-family: Cambria Math;\">.(a)</span><strong>&#2346;&#2366;&#2325;&#2367;&#2360;&#2381;&#2340;&#2366;&#2344;</strong><span style=\"font-weight: 400;\">&#2404; &#2319;&#2358;&#2367;&#2351;&#2366; &#2325;&#2346; 2022 (15&#2357;&#2366;&#2306; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339;), &#2350;&#2375;&#2332;&#2348;&#2366;&#2344; - &#2358;&#2381;&#2352;&#2368;&#2354;&#2306;&#2325;&#2366;, &#2360;&#2381;&#2341;&#2366;&#2344; - UAE(&#2360;&#2306;&#2351;&#2369;&#2325;&#2381;&#2340; &#2309;&#2352;&#2348; &#2309;&#2350;&#2368;&#2352;&#2366;&#2340;)&#2404; </span><strong>&#2357;&#2367;&#2332;&#2375;&#2340;&#2366; </strong><span style=\"font-weight: 400;\">- &#2358;&#2381;&#2352;&#2368;&#2354;&#2306;&#2325;&#2366;&#2404; </span><strong>&#2319;&#2358;&#2367;&#2351;&#2366; &#2325;&#2346; 2018 </strong><span style=\"font-weight: 400;\">(&#2357;&#2367;&#2332;&#2375;&#2340;&#2366; - &#2349;&#2366;&#2352;&#2340;, &#2313;&#2346;&#2357;&#2367;&#2332;&#2375;&#2340;&#2366; - &#2348;&#2366;&#2306;&#2327;&#2381;&#2354;&#2366;&#2342;&#2375;&#2358;)&#2404; &#2319;&#2358;&#2367;&#2351;&#2366;&#2312; &#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335; &#2346;&#2352;&#2367;&#2359;&#2342; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; - </span><strong>1983</strong><span style=\"font-weight: 400;\">, </span><strong>&#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; </strong><span style=\"font-weight: 400;\">- &#2325;&#2379;&#2354;&#2306;&#2348;&#2379;, &#2358;&#2381;&#2352;&#2368;&#2354;&#2306;&#2325;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Harnaaz Kaur Sandhu</span><span style=\"font-family: Cambria Math;\"> is an Indian model and beauty pageant titleholder who was crowned_______ 2021.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;&#2344;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2343;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2377;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2380;&#2306;&#2342;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2351;&#2379;&#2327;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2366;&#2311;&#2335;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2366;&#2352;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">______. </span><span style=\"font-family: Cambria Math;\">2021 </span><span style=\"font-family: Nirmala UI;\">&#2325;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span></p>\n",
                    options_en: ["<p>Miss Asia</p>\n", "<p>Miss World</p>\n", 
                                "<p>Miss Universe</p>\n", "<p>Miss Teen Worldwide</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2358;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2354;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2354;&#2381;&#2337;&#2357;&#2366;&#2311;&#2337;</span></p>\n"],
                    solution_en: "<p>18<span style=\"font-family: Cambria Math;\">.(c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Miss Universe 2021.</strong><span style=\"font-weight: 400;\"> Harnaaz Kaur Sandhu is the third entrant from India to win Miss Universe. Before Harnaaz Sandhu, the Miss Universe title was won by two Indians - Sushmita Sen (1994) and Lara Dutta (2000). Miss Universe 2022 (Winner - R\'Bonney Gabriel, United States). Femina Miss India 2022 (Winner - Sini Sadanand Shetty, Karnataka).</span></p>\n",
                    solution_hi: "<p>18<span style=\"font-family: Cambria Math;\">.(c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2350;&#2367;&#2360; &#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360; 2021 </strong><span style=\"font-weight: 400;\">&#2404; &#2361;&#2352;&#2344;&#2366;&#2332; &#2325;&#2380;&#2352; &#2360;&#2306;&#2343;&#2370; &#2350;&#2367;&#2360; &#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360; &#2325;&#2366; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2340;&#2368;&#2360;&#2352;&#2368; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2366;&#2327;&#2368; &#2361;&#2376;&#2306;&#2404; &#2361;&#2352;&#2344;&#2366;&#2332; &#2360;&#2306;&#2343;&#2370; &#2360;&#2375; &#2346;&#2361;&#2354;&#2375; &#2350;&#2367;&#2360; &#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360; &#2325;&#2366; &#2326;&#2367;&#2340;&#2366;&#2348; &#2342;&#2379; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351;&#2379;&#2306; - &#2360;&#2369;&#2359;&#2381;&#2350;&#2367;&#2340;&#2366; &#2360;&#2375;&#2344; (1994) &#2324;&#2352; &#2354;&#2366;&#2352;&#2366; &#2342;&#2340;&#2381;&#2340;&#2366; (2000) &#2344;&#2375; &#2332;&#2368;&#2340;&#2366; &#2341;&#2366;&#2404; </span><strong>&#2350;&#2367;&#2360; &#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360; 2022 </strong><span style=\"font-weight: 400;\">(&#2357;&#2367;&#2332;&#2375;&#2340;&#2366; - &#2310;&#2352;\'&#2348;&#2379;&#2344;&#2368; &#2327;&#2375;&#2348;&#2381;&#2352;&#2367;&#2351;&#2354;, &#2351;&#2370;&#2344;&#2366;&#2311;&#2335;&#2375;&#2337; &#2360;&#2381;&#2335;&#2375;&#2335;)&#2404;</span><strong> &#2347;&#2375;&#2350;&#2367;&#2344;&#2366; &#2350;&#2367;&#2360; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; 2022</strong><span style=\"font-weight: 400;\"> (&#2357;&#2367;&#2332;&#2375;&#2340;&#2366; - &#2360;&#2367;&#2344;&#2368; &#2360;&#2342;&#2366;&#2344;&#2306;&#2342; &#2358;&#2375;&#2335;&#2381;&#2335;&#2368;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;)&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which of the following statement is NOT correct?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Nirmala UI;\">&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Mahendra giri is the highest peak in th<span style=\"font-family: Cambria Math;\">e Eastern Ghats.</span></p>\n", "<p>The height of the Western Ghats progressively decreases from north to south.</p>\n", 
                                "<p>The Western Ghats cause orographic rain.</p>\n", "<p>The Eastern Ghats stretch from the Mahanadi Valley to the Nilgiris in the south.</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2305;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2367;&#2326;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2375;&#2306;&#2342;&#2381;&#2352;&#2327;&#2367;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2381;&#2357;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2360;&#2381;&#2340;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2366;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2325;&#2381;&#2359;&#2367;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2354;&#2327;&#2367;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n"],
                    solution_en: "<p>19<span style=\"font-family: Cambria Math;\">.(b)&nbsp;</span><strong>Option (b) is not correct. </strong><span style=\"font-weight: 400;\">The height of the Western Ghats progressively increases from north to south. The highest peaks of Western Ghats include the Anai Mudi (2,695 metres) and the Doda Betta (2,637 metres).</span><span style=\"font-weight: 400;\">The Western Ghats, also known as the Sahyadri Hills, Mainly traverse the States of Kerala, Tamil Nadu, Karnataka, Goa, Maharashtra and Gujarat.&nbsp;</span></p>\n",
                    solution_hi: "<p>19<span style=\"font-family: Cambria Math;\">.(b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2357;&#2367;&#2325;&#2354;&#2381;&#2346; (b) &#2360;&#2361;&#2368; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</strong><span style=\"font-weight: 400;\"> &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2328;&#2366;&#2335; &#2325;&#2368; &#2314;&#2306;&#2330;&#2366;&#2312; &#2313;&#2340;&#2381;&#2340;&#2352; &#2360;&#2375; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339; &#2325;&#2368; &#2323;&#2352; &#2348;&#2338;&#2364;&#2340;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2328;&#2366;&#2335; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2314;&#2306;&#2330;&#2368; &#2330;&#2379;&#2335;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2309;&#2344;&#2366;&#2312; &#2350;&#2369;&#2337;&#2364;&#2368; (2,695 &#2350;&#2368;&#2335;&#2352;) &#2324;&#2352; &#2337;&#2379;&#2337;&#2366; &#2348;&#2375;&#2335;&#2381;&#2335;&#2366; (2,637 &#2350;&#2368;&#2335;&#2352;) &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306;&#2404; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350;&#2368; &#2328;&#2366;&#2335;, &#2332;&#2367;&#2360;&#2375; &#2360;&#2361;&#2381;&#2351;&#2366;&#2342;&#2381;&#2352;&#2368; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2349;&#2368; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2350;&#2369;&#2326;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; &#2325;&#2375;&#2352;&#2354;, &#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;, &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;, &#2327;&#2379;&#2357;&#2366;, &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2324;&#2352; &#2327;&#2369;&#2332;&#2352;&#2366;&#2340; &#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306; &#2360;&#2375; &#2361;&#2379;&#2325;&#2352; &#2327;&#2369;&#2332;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">India is the______producer of pulses and the_______consumer of pulses.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Nirmala UI;\">&#2349;</span><span style=\"font-family: Nirmala UI;\">&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> _______ </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> _______</span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2349;&#2379;&#2325;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>second largest, second largest</p>\n", "<p>largest, second largest</p>\n", 
                                "<p>second largest, largest</p>\n", "<p>largest, largest</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span></p>\n"],
                    solution_en: "<p>20<span style=\"font-family: Cambria Math;\">.(d) </span><strong>largest, largest.</strong><span style=\"font-weight: 400;\"> </span><strong>India</strong><span style=\"font-weight: 400;\"> is the</span><strong> largest</strong><span style=\"font-weight: 400;\"> producer (25% of global production), consumer (27% of world consumption) and importer (14%) of pulses in the world. </span><strong>Highest&nbsp; Pulse producing state in India -</strong><span style=\"font-weight: 400;\"> Madhya Pradesh, Rajasthan, Maharashtra, Uttar Pradesh, respectively. 2016 as the International Year of Pulses (IYP) marked by the United Nation. </span><strong>World Pulses Day</strong><span style=\"font-weight: 400;\"> - 10 February.&nbsp;</span></p>\n",
                    solution_hi: "<p>20<span style=\"font-family: Cambria Math;\">.(d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366;, &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366;&#2404; </strong><span style=\"font-weight: 400;\">&#2357;&#2367;&#2358;&#2381;&#2357; &#2350;&#2375;&#2306; </span><strong>&#2349;&#2366;&#2352;&#2340; </strong><span style=\"font-weight: 400;\">&#2342;&#2366;&#2354;&#2379;&#2306; &#2325;&#2366; </span><strong>&#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366;</strong><span style=\"font-weight: 400;\"> &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325; (&#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2366; 25%), &#2313;&#2346;&#2349;&#2379;&#2325;&#2381;&#2340;&#2366; (&#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2326;&#2346;&#2340; &#2325;&#2366; 27%) &#2324;&#2352; &#2310;&#2351;&#2366;&#2340;&#2325; (14%) &#2361;&#2376;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2360;&#2348;&#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2342;&#2354;&#2361;&#2344; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325; &#2352;&#2366;&#2332;&#2381;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2325;&#2381;&#2352;&#2350;&#2358;&#2307; &#2350;&#2343;&#2381;&#2351; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;, &#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344;, &#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;, &#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;&#2404; 2016 &#2325;&#2379; &#2360;&#2306;&#2351;&#2369;&#2325;&#2381;&#2340; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2342;&#2354;&#2361;&#2344; &#2357;&#2352;&#2381;&#2359; (IYP) &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2330;&#2367;&#2361;&#2381;&#2344;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366;&#2404; </span><strong>&#2357;&#2367;&#2358;&#2381;&#2357; &#2342;&#2354;&#2361;&#2344; &#2342;&#2367;&#2357;&#2360;</strong><span style=\"font-weight: 400;\"> - 10 &#2347;&#2352;&#2357;&#2352;&#2368;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. <span style=\"font-family: Cambria Math;\">Which</span><span style=\"font-family: Cambria Math;\"> of the following is not a subgroup of plant kingdom?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21. </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Nirmala UI;\">&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2342;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2360;&#2350;&#2370;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Thallophyta</p>\n", "<p>Angiosperms</p>\n", 
                                "<p>Pteridophyta</p>\n", "<p>Hemichordata</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2341;&#2376;&#2354;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Thallophyta) </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2319;&#2306;&#2332;&#2367;&#2351;&#2379;&#2360;&#2381;&#2346;&#2352;&#2381;&#2350;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> (Angiosperms) </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Pteridophyta) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2361;&#2375;&#2350;&#2368;&#2325;&#2379;&#2352;&#2381;&#2337;&#2375;&#2335;&#2366; (Hemichordata)</span></p>\n"],
                    solution_en: "<p>21<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>Hemichordata</strong><span style=\"font-weight: 400;\">. Kingdom - Animalia, Subkingdom - Eumetazoa. Plant Kingdom is further classified into subgroups of Thallophyta, Bryophyta, Pteridophyta, Gymnosperms and Angiosperms. </span><strong>Thallophyta</strong><span style=\"font-weight: 400;\"> - Plants that do not have well-differentiated body design fall in this group. The plants in this group are commonly called algae. </span><strong>Angiosperms </strong><span style=\"font-weight: 400;\">are a plant group with flowers that produce seeds enclosed within a carpel. </span><strong>Pteridophytes</strong><span style=\"font-weight: 400;\"> are seedless, vascular cryptogams.</span></p>\n",
                    solution_hi: "<p>21<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">(d)<strong>&nbsp;</strong></span><strong>&#2361;&#2375;&#2350;&#2368;&#2325;&#2379;&#2352;&#2381;&#2337;&#2375;&#2335;&#2366;</strong><span style=\"font-weight: 400;\">&#2404; </span><span style=\"font-weight: 400;\">&#2332;&#2327;&#2340; </span><span style=\"font-weight: 400;\">- </span><strong>&#2319;&#2344;&#2367;&#2350;&#2375;&#2354;&#2367;&#2351;&#2366;</strong><span style=\"font-weight: 400;\">, &#2313;&#2346;&#2332;&#2327;&#2340; - </span><strong>&#2351;&#2370;&#2350;&#2375;&#2335;&#2366;&#2332;&#2364;&#2379;&#2310;&#2404; </strong><span style=\"font-weight: 400;\">&#2346;&#2366;&#2342;&#2346; &#2332;&#2327;&#2340; &#2325;&#2379; &#2341;&#2376;&#2354;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;, &#2348;&#2381;&#2352;&#2366;&#2351;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;, &#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366;, &#2332;&#2367;&#2350;&#2381;&#2344;&#2379;&#2360;&#2381;&#2346;&#2352;&#2381;&#2350; &#2324;&#2352; &#2319;&#2306;&#2332;&#2367;&#2351;&#2379;&#2360;&#2381;&#2346;&#2352;&#2381;&#2350; &#2325;&#2375; &#2313;&#2346;&#2360;&#2350;&#2370;&#2361;&#2379;&#2306; &#2350;&#2375;&#2306; &#2357;&#2352;&#2381;&#2327;&#2368;&#2325;&#2371;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2341;&#2376;&#2354;&#2379;&#2347;&#2364;&#2366;&#2311;&#2335;&#2366; </strong><span style=\"font-weight: 400;\">- &#2357;&#2375; &#2346;&#2380;&#2343;&#2375; &#2332;&#2367;&#2344;&#2325;&#2375; &#2358;&#2352;&#2368;&#2352; &#2325;&#2368; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2309;&#2330;&#2381;&#2331;&#2368; &#2340;&#2352;&#2361; &#2360;&#2375; &#2357;&#2367;&#2349;&#2375;&#2342;&#2367;&#2340; &#2344;&#2361;&#2368;&#2306; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;, &#2311;&#2360; &#2360;&#2350;&#2370;&#2361; &#2350;&#2375;&#2306; &#2310;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2311;&#2360; &#2360;&#2350;&#2370;&#2361; &#2325;&#2375; &#2346;&#2380;&#2343;&#2379;&#2306; &#2325;&#2379; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2307; &#2358;&#2376;&#2357;&#2366;&#2354; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2319;&#2306;&#2332;&#2367;&#2351;&#2379;&#2360;&#2381;&#2346;&#2352;&#2381;&#2350;, </strong><span style=\"font-weight: 400;\">&#2347;&#2370;&#2354;&#2379;&#2306; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325; &#2346;&#2380;&#2343;&#2366; &#2360;&#2350;&#2370;&#2361; &#2361;&#2376; &#2332;&#2379; &#2319;&#2325; &#2309;&#2306;&#2337;&#2346; &#2325;&#2375; &#2349;&#2368;&#2340;&#2352; &#2360;&#2306;&#2354;&#2327;&#2381;&#2344; &#2348;&#2368;&#2332; &#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2335;&#2375;&#2352;&#2367;&#2337;&#2379;&#2347;&#2366;&#2311;&#2335;&#2366; </strong><span style=\"font-weight: 400;\">&#2348;&#2368;&#2332; &#2352;&#2361;&#2367;&#2340;, &#2360;&#2306;&#2357;&#2361;&#2344;&#2368; &#2325;&#2381;&#2352;&#2367;&#2346;&#2381;&#2335;&#2379;&#2327;&#2376;&#2350; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;&nbsp;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">In India, National Statistics Day </span><span style=\"font-family: Cambria Math;\">is celebrated on</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Nirmala UI;\">&#2349;</span><span style=\"font-family: Nirmala UI;\">&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2306;&#2326;&#2381;&#2351;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2357;&#2360;</span><span style=\"font-family: Cambria Math;\">_______ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>22nd June</p>\n", "<p>15th June</p>\n", 
                                "<p>28th June</p>\n", "<p>29th June</p>\n"],
                    options_hi: ["<p>22 <span style=\"font-family: Nirmala UI;\">&#2332;&#2370;&#2344;</span></p>\n", "<p>15 <span style=\"font-family: Nirmala UI;\">&#2332;&#2370;&#2344;</span></p>\n",
                                "<p>28 <span style=\"font-family: Nirmala UI;\">&#2332;&#2370;&#2344;</span></p>\n", "<p>29 <span style=\"font-family: Nirmala UI;\">&#2332;&#2370;&#2344;</span></p>\n"],
                    solution_en: "<p>22<span style=\"font-family: Cambria Math;\">.(d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>29th June</strong><span style=\"font-weight: 400;\"> (Celebrated as the birth anniversary of </span><strong>Prof. P. C. Mahalanobis </strong><span style=\"font-weight: 400;\">who&nbsp; worked in the field of Statistics and planning commission). </span><strong>1 June</strong><span style=\"font-weight: 400;\"> - World Milk Day, </span><strong>5 June</strong><span style=\"font-weight: 400;\"> - World Environment Day, </span><strong>7 June</strong><span style=\"font-weight: 400;\"> - World Food Safety Day, </span><strong>20 June</strong><span style=\"font-weight: 400;\"> - World Refugee Day , </span><strong>23 June</strong><span style=\"font-weight: 400;\"> - International Olympic Day and United Nations Public Service Day. </span><strong>15 June</strong><span style=\"font-weight: 400;\"> - World Wind Day.</span></p>\n",
                    solution_hi: "<p>22<span style=\"font-family: Cambria Math;\">.(d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>29 &#2332;&#2370;&#2344;</strong><span style=\"font-weight: 400;\"> (&#2360;&#2366;&#2306;&#2326;&#2381;&#2351;&#2367;&#2325;&#2368; &#2324;&#2352; &#2351;&#2379;&#2332;&#2344;&#2366; &#2310;&#2351;&#2379;&#2327; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2325;&#2366;&#2352;&#2381;&#2351;&#2352;&#2340; </span><strong>&#2346;&#2381;&#2352;&#2379;&#2347;&#2375;&#2360;&#2352; P. C. &#2350;&#2361;&#2366;&#2354;&#2344;&#2379;&#2348;&#2367;&#2360;</strong><span style=\"font-weight: 400;\"> &#2325;&#2368; &#2332;&#2351;&#2306;&#2340;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2350;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;)&#2404; </span><strong>1 &#2332;&#2370;&#2344; - </strong><span style=\"font-weight: 400;\">&#2357;&#2367;&#2358;&#2381;&#2357; &#2342;&#2369;&#2327;&#2381;&#2343; &#2342;&#2367;&#2357;&#2360;, </span><strong>5 &#2332;&#2370;&#2344;</strong><span style=\"font-weight: 400;\"> - &#2357;&#2367;&#2358;&#2381;&#2357; &#2346;&#2352;&#2381;&#2351;&#2366;&#2357;&#2352;&#2339; &#2342;&#2367;&#2357;&#2360;</span><strong>, 7 &#2332;&#2370;&#2344; - </strong><span style=\"font-weight: 400;\">&#2357;&#2367;&#2358;&#2381;&#2357; &#2326;&#2366;&#2342;&#2381;&#2351; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; &#2342;&#2367;&#2357;&#2360;, </span><strong>20 &#2332;&#2370;&#2344;</strong><span style=\"font-weight: 400;\"> - &#2357;&#2367;&#2358;&#2381;&#2357; &#2358;&#2352;&#2339;&#2366;&#2352;&#2381;&#2341;&#2368; &#2342;&#2367;&#2357;&#2360;, </span><strong>23 &#2332;&#2370;&#2344; -</strong><span style=\"font-weight: 400;\"> &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2323;&#2354;&#2306;&#2346;&#2367;&#2325; &#2342;&#2367;&#2357;&#2360; &#2324;&#2352; &#2360;&#2306;&#2351;&#2369;&#2325;&#2381;&#2340; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2354;&#2379;&#2325; &#2360;&#2375;&#2357;&#2366; &#2342;&#2367;&#2357;&#2360;&#2404; </span><strong>15 &#2332;&#2370;&#2344; -&nbsp; </strong><span style=\"font-weight: 400;\">&#2357;&#2367;&#2358;&#2381;&#2357; &#2346;&#2357;&#2344; &#2342;&#2367;&#2357;&#2360;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The Bureau of Indian Standard is a statutory body working unde</span><span style=\"font-family: Cambria Math;\">r the aegis of which union ministry?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2370;&#2352;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2340;&#2381;&#2357;&#2366;&#2357;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2325;&#2366;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Ministry of Commerce and Industry</p>\n", "<p>Ministry of Micro, Small &amp; Medium Enterprises</p>\n", 
                                "<p>Ministry of Corporate Affairs</p>\n", "<p>Ministry of Consumer Affairs, Food and Public Distribution</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2339;&#2367;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2381;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2325;&#2381;&#2359;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2328;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2377;&#2352;&#2381;&#2346;&#2379;&#2352;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2349;&#2379;&#2325;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2350;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2366;&#2342;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2340;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2327;</span></p>\n"],
                    solution_en: "<p>23<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">(d)&nbsp;</span><strong>The Ministry of Consumer Affairs, Food and Public Distribution</strong><span style=\"font-weight: 400;\">. It operates various schemes like Product Certification (ISI mark), Management Systems Certification, Hallmarking of Gold and Silver Jewellery/Artefacts and Laboratory Services. Piyush Goyal, (Cabinet Minister). Sadhvi Niranjan Jyoti, (Minister of State). </span><strong>Bureau of Indian Standards (BIS)</strong><span style=\"font-weight: 400;\">, earlier known as the Indian Standards Institution (ISI), was founded in 1947. The BIS Act 2016, established the BIS as the National Standards Body of India. BIS recently celebrated its 75th anniversary.</span></p>\n",
                    solution_hi: "<p>23.<span style=\"font-family: Cambria Math;\">(d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&nbsp;&#2313;&#2346;&#2349;&#2379;&#2325;&#2381;&#2340;&#2366; &#2350;&#2366;&#2350;&#2354;&#2375;, &#2326;&#2366;&#2342;&#2381;&#2351; &#2324;&#2352; &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2357;&#2367;&#2340;&#2352;&#2339; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</strong><span style=\"font-weight: 400;\">&#2404; &#2351;&#2361; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342; &#2346;&#2381;&#2352;&#2350;&#2366;&#2339;&#2344; (ISI &#2350;&#2366;&#2352;&#2381;&#2325;), &#2346;&#2381;&#2352;&#2348;&#2306;&#2343;&#2344; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368; &#2346;&#2381;&#2352;&#2350;&#2366;&#2339;&#2344;, &#2360;&#2379;&#2344;&#2375; &#2324;&#2352; &#2330;&#2366;&#2306;&#2342;&#2368; &#2325;&#2375; &#2310;&#2349;&#2370;&#2359;&#2339;&#2379;&#2306; &#2325;&#2368; &#2361;&#2377;&#2354;&#2350;&#2366;&#2352;&#2381;&#2325;&#2367;&#2306;&#2327; / &#2325;&#2354;&#2366;&#2325;&#2371;&#2340;&#2367;&#2351;&#2379;&#2306; &#2324;&#2352; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327;&#2358;&#2366;&#2354;&#2366; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2332;&#2376;&#2360;&#2368; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2351;&#2379;&#2332;&#2344;&#2366;&#2323;&#2306; &#2325;&#2366; &#2360;&#2306;&#2330;&#2366;&#2354;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2346;&#2368;&#2351;&#2370;&#2359; &#2327;&#2379;&#2351;&#2354;</strong><span style=\"font-weight: 400;\"> (&#2325;&#2376;&#2348;&#2367;&#2344;&#2375;&#2335; &#2350;&#2306;&#2340;&#2381;&#2352;&#2368;)&#2404; &#2360;&#2366;&#2343;&#2381;&#2357;&#2368; &#2344;&#2367;&#2352;&#2306;&#2332;&#2344; &#2332;&#2381;&#2351;&#2379;&#2340;&#2367; (&#2352;&#2366;&#2332;&#2381;&#2351; &#2350;&#2306;&#2340;&#2381;&#2352;&#2368;)&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2350;&#2366;&#2344;&#2325; &#2348;&#2381;&#2351;&#2370;&#2352;&#2379; (</span><strong>BIS</strong><span style=\"font-weight: 400;\">), &#2332;&#2367;&#2360;&#2375; &#2346;&#2361;&#2354;&#2375; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2350;&#2366;&#2344;&#2325; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2344; (ISI) &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2341;&#2366;, &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; </span><strong>1947 </strong><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306; &#2361;&#2369;&#2312; &#2341;&#2368;&#2404; BIS &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 2016 &#2344;&#2375; BIS &#2325;&#2379; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2350;&#2366;&#2344;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366;&#2404; BIS &#2344;&#2375; &#2361;&#2366;&#2354; &#2361;&#2368; &#2350;&#2375;&#2306; &#2309;&#2346;&#2344;&#2368; 75&#2357;&#2368;&#2306; &#2357;&#2352;&#2381;&#2359;&#2327;&#2366;&#2306;&#2336; &#2350;&#2344;&#2366;&#2312; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Ruler of which dynasty built the famous Iron pillar in Delhi?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2380;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2340;&#2306;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2306;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2360;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2357;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Gupta</p>\n", "<p>Pallava</p>\n", 
                                "<p>Maurya</p>\n", "<p>Pushyabhuti</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2354;&#2381;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2380;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2359;&#2381;&#2351;&#2349;&#2370;&#2340;&#2367;</span></p>\n"],
                    solution_en: "<p>24<span style=\"font-family: Cambria Math;\">.(a) <strong>Gupta dynasty.</strong><span style=\"font-weight: 400;\"> Mehrauli (Delhi) iron pillar constructed by the famous king Chandragupta II, India, notable for the rust-resistant composition of the metal used in its construction. The inscription on the pillar is in Sanskrit, about Gupta emperor Chandragupta II.</span></span></p>\n",
                    solution_hi: "<p>24<span style=\"font-family: Cambria Math;\">.(a)&nbsp;</span><strong>&#2327;&#2369;&#2346;&#2381;&#2340; &#2357;&#2306;&#2358;</strong><span style=\"font-weight: 400;\">&#2404;&nbsp; &#2350;&#2361;&#2352;&#2380;&#2354;&#2368; (&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;) &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343; &#2352;&#2366;&#2332;&#2366; &#2330;&#2306;&#2342;&#2381;&#2352;&#2327;&#2369;&#2346;&#2381;&#2340; &#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340; &#2354;&#2380;&#2361; &#2360;&#2381;&#2340;&#2306;&#2349;, &#2311;&#2360;&#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340; &#2343;&#2366;&#2340;&#2369;, &#2332;&#2306;&#2327; &#2346;&#2381;&#2352;&#2340;&#2367;&#2352;&#2379;&#2343;&#2368; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2354;&#2381;&#2354;&#2375;&#2326;&#2344;&#2368;&#2351; &#2361;&#2376;&#2404; &#2351;&#2361; &#2360;&#2381;&#2340;&#2306;&#2349; &#2358;&#2367;&#2354;&#2366;&#2354;&#2375;&#2326; &#2327;&#2369;&#2346;&#2381;&#2340; &#2360;&#2350;&#2381;&#2352;&#2366;&#2335; &#2330;&#2306;&#2342;&#2381;&#2352;&#2327;&#2369;&#2346;&#2381;&#2340; &#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351; &#2325;&#2375; &#2348;&#2366;&#2352;&#2375; &#2350;&#2375;&#2306; &#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340; &#2350;&#2375;&#2306; &#2361;&#2376; &#2404;&nbsp;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which among the following festivals marks the birthday of a great Buddhist saint Je Tsong</span><span style=\"font-family: Cambria Math;\">khapa?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">25</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2351;&#2379;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2380;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2379;&#2306;&#2326;&#2366;&#2346;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Je Tsongkhapa) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2381;&#2350;&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Sarhul</p>\n", "<p>Losar</p>\n", 
                                "<p>Tusu</p>\n", "<p>Galdan Namchot</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2361;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2360;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2335;&#2369;&#2360;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2354;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2350;&#2330;&#2379;&#2335;</span></p>\n"],
                    solution_en: "<p>25<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">(d) <strong>Galdan Namchot</strong><span style=\"font-weight: 400;\"> is often celebrated in Tibet, Mongolia and Ladakh. This Festival marks the beginning of the New Year in Ladakh. </span><strong>Sarhul</strong><span style=\"font-weight: 400;\"> is the festival of the New Year celebrated in the state of Jharkhand by the tribal communities as part of the local Sarna religion. </span><strong>Losar,</strong><span style=\"font-weight: 400;\"> (Tibetan New Year), is a festival in Tibetan Buddhism. </span><strong>Tusu,</strong><span style=\"font-weight: 400;\"> a harvest festival, is one of the three major festivals of the Kurmi community in West Bengal, Jharkhand and Odisha</span></span></p>\n",
                    solution_hi: "<p>25<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">(d)</span><strong>&#2327;&#2354;&#2342;&#2366;&#2344; &#2344;&#2366;&#2350;&#2330;&#2379;&#2335;</strong><span style=\"font-weight: 400;\"> &#2309;&#2325;&#2381;&#2360;&#2352; &#2340;&#2367;&#2348;&#2381;&#2348;&#2340;, &#2350;&#2306;&#2327;&#2379;&#2354;&#2367;&#2351;&#2366; &#2324;&#2352; &#2354;&#2342;&#2381;&#2342;&#2366;&#2326; &#2350;&#2375;&#2306; &#2350;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357; &#2354;&#2342;&#2381;&#2342;&#2366;&#2326; &#2350;&#2375;&#2306; &#2344;&#2319; &#2360;&#2366;&#2354; &#2325;&#2368; &#2358;&#2369;&#2352;&#2369;&#2310;&#2340; &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2368;&#2325; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2352;&#2361;&#2369;&#2354; </strong><span style=\"font-weight: 400;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2368;&#2351; &#2360;&#2352;&#2344;&#2366; &#2343;&#2352;&#2381;&#2350; &#2325;&#2375; &#2361;&#2367;&#2360;&#2381;&#2360;&#2375; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2310;&#2342;&#2367;&#2357;&#2366;&#2360;&#2368; &#2360;&#2350;&#2369;&#2342;&#2366;&#2351;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2333;&#2366;&#2352;&#2326;&#2306;&#2337; &#2352;&#2366;&#2332;&#2381;&#2351; &#2350;&#2375;&#2306; &#2350;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2344;&#2351;&#2366; &#2360;&#2366;&#2354; &#2325;&#2366; &#2340;&#2381;&#2351;&#2379;&#2361;&#2366;&#2352; &#2361;&#2376;&#2404; </span><strong>&#2354;&#2379;&#2360;&#2352;</strong><span style=\"font-weight: 400;\"> -&#2351;&#2361; &#2340;&#2367;&#2348;&#2381;&#2348;&#2340;&#2368; &#2344;&#2357; &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2350;&#2344;&#2366;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2340;&#2367;&#2348;&#2381;&#2348;&#2340;&#2368; &#2348;&#2380;&#2342;&#2381;&#2343; &#2343;&#2352;&#2381;&#2350; &#2325;&#2366; &#2319;&#2325; &#2340;&#2381;&#2351;&#2379;&#2361;&#2366;&#2352; &#2361;&#2376;&#2404; </span><strong>&#2340;&#2369;&#2360;&#2370; </strong><span style=\"font-weight: 400;\">&#2347;&#2360;&#2354; &#2313;&#2340;&#2381;&#2360;&#2357; &#2346;&#2358;&#2381;&#2330;&#2367;&#2350; &#2348;&#2306;&#2327;&#2366;&#2354;, &#2333;&#2366;&#2352;&#2326;&#2306;&#2337; &#2324;&#2352; &#2323;&#2337;&#2367;&#2358;&#2366; &#2350;&#2375;&#2306; &#2325;&#2369;&#2352;&#2381;&#2350;&#2368; &#2360;&#2350;&#2369;&#2342;&#2366;&#2351; &#2325;&#2375; &#2340;&#2368;&#2344; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2340;&#2381;&#2351;&#2379;&#2361;&#2366;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>