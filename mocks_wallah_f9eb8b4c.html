<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, LDWR\' is coded as \'NGUQ\' and \'BFKZ\" is coded as \'DIIY\". What is the code for \'YTGX\' in the given code language?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, \'LDWR\' को \'NGUQ\' लिखा जाता है और BFKZ\' को DIIY\' लिखा जाता है। उस कूट भाषा में \'YTGX\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>AWEW</p>", "<p>BWFV</p>", 
                                "<p>YWFV</p>", "<p>ZVFV</p>"],
                    options_hi: ["<p>AWEW</p>", "<p>BWFV</p>",
                                "<p>YWFV</p>", "<p>ZVFV</p>"],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602407794.png\" alt=\"rId4\" width=\"120\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602407946.png\" alt=\"rId5\" width=\"120\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408145.png\" alt=\"rId6\" width=\"120\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602407794.png\" alt=\"rId4\" width=\"120\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602407946.png\" alt=\"rId5\" width=\"120\">&nbsp;<br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408145.png\" alt=\"rId6\" width=\"120\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. From the option figures given below, select the figure in which the question figure is embedded. (rotation is not allowed) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408290.png\" alt=\"rId7\" width=\"70\" height=\"69\"></p>",
                    question_hi: "<p>2. नीचे दी गई विकल्प आकृतियों में से, उस आकृति का चयन कीजिए जिसमें प्रश्न आकृति छिपी/निहित है। (घूर्णन की अनुमति नहीं है)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408290.png\" alt=\"rId7\" width=\"70\" height=\"69\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408386.png\" alt=\"rId8\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408513.png\" alt=\"rId9\" width=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408616.png\" alt=\"rId10\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408716.png\" alt=\"rId11\" width=\"80\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408386.png\" alt=\"rId8\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408513.png\" alt=\"rId9\" width=\"80\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408616.png\" alt=\"rId10\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408716.png\" alt=\"rId11\" width=\"80\"></p>"],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408845.png\" alt=\"rId12\" width=\"80\"></p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408845.png\" alt=\"rId12\" width=\"80\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>All pencils are remotes.<br>Some pencils are ACs.<br><strong>Conclusions:</strong><br>[1] Some ACs are remotes.<br>[2] All remotes are Acs.</p>",
                    question_hi: "<p>3. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय कीजिए कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन</strong>:<br>सभी पेंसिलें, रिमोट हैं।<br>कुछ पेंसिलें, एसी हैं।<br><strong>निष्कर्ष:</strong><br>[1] कुछ एसी, रिमोट हैं।<br>[2] सभी रिमोट, एसी हैं।</p>",
                    options_en: ["<p>Only conclusion [1] follows</p>", "<p>Neither conclusion [1] nor [2] follows</p>", 
                                "<p>Both conclusions [1] and [2] follow</p>", "<p>Only conclusion [2] follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष [1] अनुसरण करता है</p>", "<p>न तो निष्कर्ष [1] और न ही [2] अनुसरण करता है</p>",
                                "<p>निष्कर्ष [1] और [2] दोनों अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष [2] अनुसरण करता है</p>"],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602408996.png\" alt=\"rId13\" width=\"165\" height=\"71\"><br>Only conclusion [1] follows.</p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409143.png\" alt=\"rId14\" width=\"163\" height=\"70\"><br>केवल निष्कर्ष [1] अनुसरण करता है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language <br>A + B means &lsquo;A is the son of B&rsquo; <br>A &ndash; B means &lsquo;A is the brother of B&rsquo; <br>A &times; B means &lsquo;A is the wife of B&rsquo; <br>A &divide; B means &lsquo;A is the father of B&rsquo; <br>Based on the above, how is P related to T if &lsquo;P &times; Q &minus; R &divide; S + T&rsquo;?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में<br>A + B का अर्थ A, B का बेटा है <br>A &ndash; B का अर्थ A, B का भाई है <br>A &times; B का अर्थ A, B की पत्नी है <br>A &divide; B का अर्थ A , B का पिता है <br>उपरोक्त के आधार पर, यदि P &times; Q - R &divide; S + T है, तो P का T से क्या संबंध है?</p>",
                    options_en: ["<p>Husband&rsquo;s brother&rsquo;s daughter</p>", "<p>Husband&rsquo;s sister</p>", 
                                "<p>Husband&rsquo;s brother&rsquo;s wife</p>", "<p>Husband&rsquo;s mother</p>"],
                    options_hi: ["<p>पति के भाई की बेटी</p>", "<p>पति की बहन</p>",
                                "<p>पति के भाई की पत्नी</p>", "<p>पति की मां</p>"],
                    solution_en: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409312.png\" alt=\"rId15\" width=\"235\" height=\"140\"><br>P is the wife of T&rsquo;s husband&rsquo;s brother.</p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409312.png\" alt=\"rId15\" width=\"235\" height=\"140\"><br>P, T के पति के भाई की पत्नी है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option that arrangement of words in a logical and meaningful orderit shows.<br>1. Quadrillion <br>2. Quintillion <br>3. Million <br>4. Octillion <br>5. Trillion</p>",
                    question_hi: "<p>5. उस विकल्प का चयन करें जो निम्&zwj;नलिखित शब्दों की व्&zwj;यवस्&zwj;था को तार्किक और सार्थक क्रम में दर्शाता है।<br>1. क्वाड्रिलियन <br>2. क्विंटिलियन <br>3. मिलियन <br>4. ऑक्टिलियन <br>5. ट्रिलियन</p>",
                    options_en: ["<p>3 - 5 - 1 - 4 - 2</p>", "<p>4 - 3 - 5 - 1 - 2</p>", 
                                "<p>3 - 5 - 1 - 2 - 4</p>", "<p>2 - 3 - 5 - 1 - 4</p>"],
                    options_hi: ["<p>3 - 5 - 1 - 4 - 2</p>", "<p>4 - 3 - 5 - 1 - 2</p>",
                                "<p>3 - 5 - 1 - 2 - 4</p>", "<p>2 - 3 - 5 - 1 - 4</p>"],
                    solution_en: "<p>5.(c) The logical &amp; meaningful order of the given words is: 3 - 5 - 1 - 2 - 4<br>Million &lt; Trillion &lt; Quadrillion &lt; Quintillion &lt; Octillion</p>",
                    solution_hi: "<p>5.(c) दिए गए शब्दों का तार्किक और अर्थपूर्ण क्रम है: 3 - 5 - 1 - 2 - 4<br>मिलियन &lt; ट्रिलियन &lt; क्वाड्रिलियन &lt; क्विंटिलियन &lt; ऑक्टिलियन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which two signs should be interchanged to make the given equation correct ?<br>12 + 6 &times; 2 - 24 &divide;&nbsp;8 = 3</p>",
                    question_hi: "<p>6. दिए गए विकल्पों में से किन दो चिह्नों को आपस में बदलने पर निम्नलिखित समीकरण सही होगा ?<br>12 + 6 &times; 2 - 24 &divide; 8 = 3</p>",
                    options_en: ["<p>&times; and -</p>", "<p>- and =</p>", 
                                "<p>&divide; and -</p>", "<p>+ and -</p>"],
                    options_hi: ["<p>&times; और -</p>", "<p>- और =</p>",
                                "<p>&divide; और -</p>", "<p>+ और -</p>"],
                    solution_en: "<p>6.(d)<br><strong>Given</strong> :- 12 + 6 &times; 2 - 24 &divide; 8 = 3<br>After going through all the options, option (d) satisfies. <br>After interchanging + and - we get<br>12 - 6 &times; 2 + 24 &divide; 8 <br>12 - 12 + 3 = 3<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>6.(d)<br><strong>दिया गया :- </strong>12 + 6 &times; 2 - 24 &divide; 8 = 3<br>सभी विकल्पों की जांच करने पर विकल्प (d) संतुष्ट करता है। <br>+ और - को आपस में बदलने पर हमें प्राप्त होता है<br>12 - 6 &times; 2 + 24 &divide; 8 <br>12 - 12 + 3 = 3<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the option that is related to the fifth word in the same way as the fourth word is related to the third word, and the second word is related to the first word.<br>KASHMIR : PARADISE ON EARTH :: KULLU : VALLEY OF GODS :: DARJEELING : ?</p>",
                    question_hi: "<p>7. निम्नलिखित विकल्पों में से उस विकल्प का चयन कीजिए जो पांचवें शब्द से ठीक उसी प्रकार संबंधित है जिस प्रकार चौथा शब्द तीसरे शब्द से संबंधित है और दूसरा शब्द पहले शब्द से संबंधित है।<br>कश्मीर : धरती पर स्वर्ग : : कुल्लू : देवताओं की घाटी : : दार्जिलिंग : ?</p>",
                    options_en: ["<p>CITY OF TEMPLES</p>", "<p>CITY OF HILLS</p>", 
                                "<p>CITY OF BEACHES</p>", "<p>CITY OF LAKES</p>"],
                    options_hi: ["<p>मंदिरों का शहर</p>", "<p>पहाड़ों का शहर</p>",
                                "<p>समुद्रतटों का शहर</p>", "<p>झीलों का शहर</p>"],
                    solution_en: "<p>7.(b)<br>As Kashmir is called Paradise On Earth, Kullu is called Valleys Of God, similarly Darjeeling is called the City Of Hills.</p>",
                    solution_hi: "<p>7.(b)<br>जैसे कश्मीर को धरती का स्वर्ग कहा जाता है, कुल्लू को देवताओं की घाटी कहा जाता है, वैसे ही दार्जिलिंग को पहाड़ों का शहर कहा जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What should come in place of the question mark (?) in the given series?<br>1, 2, 5, 12, ?, 58, 121</p>",
                    question_hi: "<p>8. दी गई श्रृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए?<br>1, 2, 5, 12, ?, 58, 121</p>",
                    options_en: ["<p>20</p>", "<p>25</p>", 
                                "<p>15</p>", "<p>27</p>"],
                    options_hi: ["<p>20</p>", "<p>25</p>",
                                "<p>15</p>", "<p>27</p>"],
                    solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409416.png\" alt=\"rId16\" width=\"370\" height=\"61\"></p>",
                    solution_hi: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409416.png\" alt=\"rId16\" width=\"370\" height=\"61\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language, \'DRAPE\' is coded as \'65432\' and \'TAPED\' is coded as \'24596\'. What is the code for \'R\' in that language?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में, \'DRAPE\' को \'65432\' के रूप में कूटबद्ध किया जाता है और \'TAPED\' को \'24596\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'R\' के लिए क्या कूट होगा?</p>",
                    options_en: ["<p>2</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>9</p>"],
                    options_hi: ["<p>2</p>", "<p>6</p>",
                                "<p>3</p>", "<p>9</p>"],
                    solution_en: "<p>9.(c)<br>D R A P E <math display=\"inline\"><mo>&#8594;</mo></math> 6 5 4 3 2<br>T A P E D <math display=\"inline\"><mo>&#8594;</mo></math> 2 4 5 9 6<br>From above code for &lsquo; R&rsquo; is 3.</p>",
                    solution_hi: "<p>9.(c)<br>D R A P E <math display=\"inline\"><mo>&#8594;</mo></math> 6 5 4 3 2<br>T A P E D <math display=\"inline\"><mo>&#8594;</mo></math> 2 4 5 9 6<br>उपरोक्त कोड से \'R\' के लिए कोड \'3\' है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter-cluster that is different. <br><strong>Note:</strong> The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>10. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस अक्षर-समूह का चयन करें जो भिन्न हो। <br><strong>नोट</strong>: अक्षर समूह में, भिन्न व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: ["<p>UOW</p>", "<p>IAK</p>", 
                                "<p>PJR</p>", "<p>LFN</p>"],
                    options_hi: ["<p>UOW</p>", "<p>IAK</p>",
                                "<p>PJR</p>", "<p>LFN</p>"],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409518.png\" alt=\"rId17\">&nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409641.png\" alt=\"rId18\">&nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409751.png\" alt=\"rId19\"><br>but<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409886.png\" alt=\"rId20\"></p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409518.png\" alt=\"rId17\">&nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409641.png\" alt=\"rId18\">&nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409751.png\" alt=\"rId19\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409886.png\" alt=\"rId20\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language,<br>A + B means \'A is the daughter of B\'<br>A - B means \'A is the wife of B\'<br>A <math display=\"inline\"><mo>&#215;</mo></math> B means \'A is the brother of B\'<br>A &divide; B means \'A is the father of B\'.<br>Based on the above, if P + Q <math display=\"inline\"><mo>&#215;</mo></math> R - S &divide; T\' which of the following is true?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में,<br>A + B का अर्थ \'A, B की बेटी है\' <br>A - B का अर्थ \'A, B की पत्नी है\' <br>A &times; B का अर्थ \'A, B का भाई है\' <br>A &divide; B का अर्थ \'A, B का पिता है\'।<br>उपरोक्त के आधार पर, यदि \'P + Q <math display=\"inline\"><mo>&#215;</mo></math> R - S &divide; T\' तो निम्न में से कौन-सा सत्य है?</p>",
                    options_en: ["<p>T is S\'s Son,</p>", "<p>P is the daughter of T\'s Mother\'s Brother.</p>", 
                                "<p>P is the sister of T.</p>", "<p>Q is the brother of S.</p>"],
                    options_hi: ["<p>T, S का बेटा है।</p>", "<p>P, T की मां के भाई की बेटी है।</p>",
                                "<p>P, T की बहन है।</p>", "<p>Q, S का भाई है।</p>"],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409992.png\" alt=\"rId21\" width=\"181\" height=\"102\"><br>P is the daughter of T&rsquo;s Mother&rsquo;s Brother</p>",
                    solution_hi: "<p>11.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602409992.png\" alt=\"rId21\" width=\"181\" height=\"102\"><br>P, T की माँ के भाई की बेटी है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. By Interchanging the given two numbers (not digits) which of the following equation will be not correct?<br>7 and 3</p>",
                    question_hi: "<p>12. नीचे दी गई दो संख्याओं (न कि अंकों) को आपस में बदलने से निम्नलिखित में से कौन-सा समीकरण सही नहीं होगा?<br>7 और 3</p>",
                    options_en: ["<p>9 &times; 7 + 3 - 4 &divide; 2 = 32</p>", "<p>8 &divide; 2 + 9 &times; 3 - 7 = 64</p>", 
                                "<p>7 + 3 &times; 8 - 8 &divide; 2 = 55</p>", "<p>7 &times; 8 - 9 + 3 &divide; 1= 30</p>"],
                    options_hi: ["<p>9 &times; 7 + 3 - 4 &divide; 2 = 32</p>", "<p>8 &divide; 2 + 9 &times; 3 - 7 = 64</p>",
                                "<p>7 + 3 &times; 8 - 8 &divide; 2 = 55</p>", "<p>7 &times; 8 - 9 + 3 &divide; 1= 30</p>"],
                    solution_en: "<p>12.(d) by interchanging the numbers 7 and 3 (not digits) as per the question in every option, we get:<br>(a) 9 &times; 7 + 3 - 4 &divide; 2 = 32<br><math display=\"inline\"><mo>&#8658;</mo></math> 9 &times; 3 + 7 - 4 &divide; 2 = 32<br><math display=\"inline\"><mo>&#8658;</mo></math> 27 + 7 - 2 = 32<br><math display=\"inline\"><mo>&#8658;</mo></math> 32 = 32 (correct)<br><br>(b) 8 &divide; 2 + 9 &times; 3 - 7 = 64<br><math display=\"inline\"><mo>&#8658;</mo></math> 8 &divide; 2 + 9 &times; 7 - 3 = 64<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 + 63 - 3 = 64<br><math display=\"inline\"><mo>&#8658;</mo></math> 64 = 64 (correct)<br><br>(c) 7 + 3 &times; 8 - 8 &divide; 2 = 55<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 7 &times; 8 - 8 &divide; 2 = 55<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 56 - 4 = 55<br><math display=\"inline\"><mo>&#8658;</mo></math> 55 = 55 (correct)<br><br>(d) 7 &times; 8 - 9 + 3 &divide; 1= 30<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 &times; 8 - 9 + 7 &divide; 1= 30<br><math display=\"inline\"><mo>&#8658;</mo></math> 24 - 9 + 7= 30<br><math display=\"inline\"><mo>&#8658;</mo></math> 22 &ne; 30<br><math display=\"inline\"><mo>&#8658;</mo></math> Here LHS &ne; RHS<br>Therefore option (d) is incorrect.</p>",
                    solution_hi: "<p>12.(d) प्रत्येक विकल्प में प्रश्न के अनुसार संख्या 7 और 3 (अंक नहीं) को आपस में बदलने पर, हमें प्राप्त होता है:<br>(a) 9 &times; 7 + 3 - 4 &divide; 2 = 32<br><math display=\"inline\"><mo>&#8658;</mo></math> 9 &times; 3 + 7 - 4 &divide; 2 = 32<br><math display=\"inline\"><mo>&#8658;</mo></math> 27 + 7 - 2 = 32<br><math display=\"inline\"><mo>&#8658;</mo></math> 32 = 32 (सत्य)<br><br>(b) 8 &divide; 2 + 9 &times; 3 - 7 = 64<br><math display=\"inline\"><mo>&#8658;</mo></math> 8 &divide; 2 + 9 &times; 7 - 3 = 64<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 + 63 - 3 = 64<br><math display=\"inline\"><mo>&#8658;</mo></math> 64 = 64 (सत्य)<br><br>(c) 7 + 3 &times; 8 - 8 &divide; 2 = 55<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 7 &times; 8 - 8 &divide; 2 = 55<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 56 - 4 = 55<br><math display=\"inline\"><mo>&#8658;</mo></math> 55 = 55 (सत्य)<br><br>(d) 7 &times; 8 - 9 + 3 &divide; 1 = 30<br><math display=\"inline\"><mo>&#8658;</mo></math> 3 &times; 8 - 9 + 7 &divide; 1 = 30<br><math display=\"inline\"><mo>&#8658;</mo></math> 24 - 9 + 7= 30<br><math display=\"inline\"><mo>&#8658;</mo></math> 22 &ne; 30<br><math display=\"inline\"><mo>&#8658;</mo></math> यहाँ LHS &ne; RHS<br>इसलिए विकल्प (d) ग़लत है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. CDFG is related to FGIJ in a certain way based on the English alphabetical order. In the same way, MNPQ is related to PQST. To which of the following is HIKL related, following the same logic?</p>",
                    question_hi: "<p>13. अंग्रेजी वर्णमाला क्रम के आधार पर CDFG एक निश्चित प्रकार से FGIJ से संबंधित है। ठीक उसी प्रकार, MNPQ, PQST से संबंधित है। समान तर्क का अनुसरण करते हुए, HIKL निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: ["<p>KLNP</p>", "<p>KLNO</p>", 
                                "<p>JKNO</p>", "<p>LKNO</p>"],
                    options_hi: ["<p>KLNP</p>", "<p>KLNO</p>",
                                "<p>JKNO</p>", "<p>LKNO</p>"],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410116.png\" alt=\"rId22\" width=\"200\"> &nbsp; &nbsp; and&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410261.png\" alt=\"rId23\" width=\"200\"><br>&nbsp;Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410390.png\" alt=\"rId24\" width=\"200\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410116.png\" alt=\"rId22\" width=\"200\"> &nbsp; &nbsp;और &nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410261.png\" alt=\"rId23\" width=\"200\"><br>&nbsp;इसी प्रकार<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410390.png\" alt=\"rId24\" width=\"200\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Arrange the given words in English alphabetical order.<br>1. CELEBRATE<br>2. CELESTIAL<br>3. CELLO<br>4. CELEBRITY<br>5. CELLULOID</p>",
                    question_hi: "<p>14. दिए गए शब्दों को अंग्रेज़ी वर्णमाला के क्रम में व्यवस्थित कीजिए।<br>1. CELEBRATE<br>2. CELESTIAL<br>3. CELLO<br>4. CELEBRITY<br>5. CELLULOID</p>",
                    options_en: ["<p>1, 2, 3, 4, 5</p>", "<p>1, 4, 2, 3, 5</p>", 
                                "<p>1, 4, 5, 2, 3</p>", "<p>1, 4, 3, 2, 5</p>"],
                    options_hi: ["<p>1, 2, 3, 4, 5</p>", "<p>1, 4, 2, 3, 5</p>",
                                "<p>1, 4, 5, 2, 3</p>", "<p>1, 4, 3, 2, 5</p>"],
                    solution_en: "<p>14.(b)<br>The correct order is :- <br>Celebrate(1) &rarr; Celebrity(4) &rarr; Celestial(2) &rarr; Cello(3) &rarr; Celluloid(5)</p>",
                    solution_hi: "<p>14.(b)<br>सही क्रम है:-<br>Celebrate(1) &rarr; Celebrity(4) &rarr; Celestial(2) &rarr; Cello(3) &rarr; Celluloid(5)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. What should come in place of question mark (?) in the given series based on the English alphabetical order?<br>AYF, FUI, KQL, PMO, ?</p>",
                    question_hi: "<p>15. अंग्रेजी वर्णानुक्रम के आधार पर दी गई शृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए?<br>AYF, FUI, KQL, PMO, ?</p>",
                    options_en: ["<p>VIQ</p>", "<p>VIR</p>", 
                                "<p>UJR</p>", "<p>UIR</p>"],
                    options_hi: ["<p>VIQ</p>", "<p>VIR</p>",
                                "<p>UJR</p>", "<p>UIR</p>"],
                    solution_en: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410601.png\" alt=\"rId26\" width=\"293\" height=\"110\"></p>",
                    solution_hi: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410601.png\" alt=\"rId26\" width=\"293\" height=\"110\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br><strong>(NOTE:</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(8, 64, 2)<br>(12, 12, 1)</p>",
                    question_hi: "<p>16. उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्&zwj;चयों की संख्याएं संबंधित हैं।<br>(<strong>ध्यान दें:</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(8, 64, 2)<br>(12, 12, 1)</p>",
                    options_en: ["<p>(10, 3426, 7)</p>", "<p>(8, 722, 9)</p>", 
                                "<p>(6, 214, 6)</p>", "<p>(4, 256, 4)</p>"],
                    options_hi: ["<p>(10, 3426, 7)</p>", "<p>(8, 722, 9)</p>",
                                "<p>(6, 214, 6)</p>", "<p>(4, 256, 4)</p>"],
                    solution_en: "<p>16.(d) <strong>Logic</strong> :- (1st number)<sup>3rd number</sup> = 2nd number<br>(8, 64, 2) :- (8)<sup>2</sup> = 64<br>(12, 12, 1) :- (12)<sup>1</sup> = 12<br>Similarly,<br>(4, 256, 4) :- (4)<sup>4</sup> = 256</p>",
                    solution_hi: "<p>16.(d) <strong>तर्क:-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mo>&#160;</mo><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi>&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo><mo>)</mo></mrow><mrow><mi>&#2340;&#2368;&#2360;&#2352;&#2368;</mi><mi>&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></msup></math> = दूसरी संख्या<br>(8, 64, 2) :- (8)<sup>2</sup> = 64<br>(12, 12, 1) :- (12)<sup>1</sup> = 12<br>इसी प्रकार,<br>(4, 256, 4) :- (4)<sup>4</sup> = 256</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Read the given statement(s) and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statement(s). <br><strong>Statements:</strong> <br>No teacher is a male. <br>No male is an Engineer. <br><strong>Conclusions:</strong> <br>I. No teacher is an Engineer. <br>II. Some male are teachers.</p>",
                    question_hi: "<p>17. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, चाहे वह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो और निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन</strong>: <br>कोई शिक्षक, पुरुष नहीं है। <br>कोई पुरुष, इंजीनियर नहीं है। <br><strong>निष्कर्ष:</strong> <br>I. कोई शिक्षक, इंजीनियर नहीं है। <br>II. कुछ पुरुष, शिक्षक हैं।</p>",
                    options_en: ["<p>Neither conclusion I nor II follows.</p>", "<p>Both conclusions I and II follow.</p>", 
                                "<p>Only conclusion II follows.</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>न तो निष्कर्ष (I) और न ही (II) अनुसरण करता है |</p>", "<p>निष्कर्ष (I) और (II) दोनों अनुसरण करते है |</p>",
                                "<p>केवल निष्कर्ष (II) अनुसरण करता है |</p>", "<p>केवल निष्कर्ष (I) अनुसरण करता है |</p>"],
                    solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410774.png\" alt=\"rId27\" width=\"254\" height=\"47\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>17.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602410935.png\" alt=\"rId28\" width=\"292\" height=\"54\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18.Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?<br><strong>(NOTE:</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>18. निम्नलिखित चार विकल्पों में से तीन एक निश्चित तरीके से एकसमान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन सा विकल्प है जो उस समूह से संबंधित नहीं है?<br>(<strong>ध्यान दें</strong>: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 लें - 13 पर विभिन्&zwj;न गणितीय संक्रियाएं जैसे 13 को जोड़ना / घटाना / गुणा करना आदि की जा सकती हैा । 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>56 &ndash; 31 &ndash; 205</p>", "<p>88 &ndash; 23 &ndash; 245</p>", 
                                "<p>22 &ndash; 13 &ndash; 85</p>", "<p>18 &ndash; 12 &ndash; 72</p>"],
                    options_hi: ["<p>56 &ndash; 31 &ndash; 205</p>", "<p>88 &ndash; 23 &ndash; 245</p>",
                                "<p>22 &ndash; 13 &ndash; 85</p>", "<p>18 &ndash; 12 &ndash; 72</p>"],
                    solution_en: "<p>18.(c)<br><strong>Logic:-</strong> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>1</mn><mi>st</mi></msup><mi>no</mi><mo>.</mo></math> + 2<sup>nd</sup> no.) &times; 2 + 2<sup>nd</sup> no.. = 3<sup>rd</sup> no.<br>(56 - 31 - 205) :- (56 + 31) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 31 &rArr; 174 + 31 = 205<br>(88 - 23 - 245) :- (88 + 23) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 23 &rArr; 222 + 23 = 245<br>(18 - 12 - 72) :- (18 + 12) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 12 &rArr; 60 + 12 = 72<br>But, <br>(22 - 13 - 85) :- (22 + 13) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 13 &rArr; 70 + 13 = 83 &ne; 85</p>",
                    solution_hi: "<p>18.(c)<br><strong>तर्क </strong>:- (पहली संख्या + दूसरी संख्या ) <math display=\"inline\"><mo>&#215;</mo></math> 2 + दूसरी संख्या = तीसरी संख्या <br>(56 - 31 - 205) :- (56 + 31) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 31 &rArr; 174 + 31 = 205<br>(88 - 23 - 245) :- (88 + 23) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 23 &rArr; 222 + 23 =245<br>(18 - 12 - 72) :- (18 + 12) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 12 &rArr; 60 + 12 = 72<br>लेकिन , <br>(22 - 13 - 85) :- (22 + 13) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 13 &rArr; 70 + 13 = 83 &ne; 85</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In the following question, select the figure which can be placed at the sign of question mark (?) from the given alternatives.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411128.png\" alt=\"rId29\" width=\"318\" height=\"62\"></p>",
                    question_hi: "<p>19. निम्नलिखित प्रश्न में, दिए गए विकल्पों में से उस आकृति का चयन कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखा जा सकता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411128.png\" alt=\"rId29\" width=\"318\" height=\"62\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411217.png\" alt=\"rId30\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411320.png\" alt=\"rId31\" width=\"70\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411422.png\" alt=\"rId32\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411585.png\" alt=\"rId33\" width=\"70\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411217.png\" alt=\"rId30\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411320.png\" alt=\"rId31\" width=\"70\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411422.png\" alt=\"rId32\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411585.png\" alt=\"rId33\" width=\"70\"></p>"],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411422.png\" alt=\"rId32\" width=\"70\"></p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411422.png\" alt=\"rId32\" width=\"70\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option that is related to the fifth letter cluster in the same way as the fourth letter cluster is related to the third letter cluster and the second letter cluster is related to the first letter cluster.<br>AFFECT : FAFETC :: BRIGHT : RBIGTH :: EITHER : ?</p>",
                    question_hi: "<p>20. निम्नलिखित विकल्पों में से उस विकल्प का चयन कीजिए जो पांचवें अक्षर-समूह से ठीक उसी प्रकार संबंधित है जिस प्रकार चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है, और दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है।<br>AFFECT : FAFETC :: BRIGHT : RBIGTH :: EITHER : ?</p>",
                    options_en: ["<p>IEHTRE</p>", "<p>IETHRE</p>", 
                                "<p>TEIERH</p>", "<p>ERHTEI</p>"],
                    options_hi: ["<p>IEHTRE</p>", "<p>IETHRE</p>",
                                "<p>TEIERH</p>", "<p>ERHTEI</p>"],
                    solution_en: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411726.png\" alt=\"rId34\" width=\"184\" height=\"114\">&nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411841.png\" alt=\"rId35\" width=\"184\"><br>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411934.png\" alt=\"rId36\" width=\"184\"></p>",
                    solution_hi: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411726.png\" alt=\"rId34\" width=\"184\" height=\"114\">&nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411841.png\" alt=\"rId35\" width=\"184\"><br>इसी प्रकार&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602411934.png\" alt=\"rId36\" width=\"184\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21.&nbsp;In a certain code language, \'try something new\' is coded as \'db kl jk\' and \'something hurt him\' is coded as \'db pt uk. How is \'something\' coded in that language?</p>\n<p>&nbsp;</p>",
                    question_hi: "<p>21. एक निश्चित कूट भाषा में, \'try something new\' को \'db kl jk\' लिखा जाता है और \'something hurt him\' को \'db pt uk लिखा जाता है। है। उस कूट भाषा में something\' को कैसे लिखा जाएगा?</p>\n<p>&nbsp;</p>",
                    options_en: ["<p>jk</p>", "<p>db</p>", 
                                "<p>uk</p>", "<p>kl</p>"],
                    options_hi: ["<p>jk</p>", "<p>db</p>",
                                "<p>uk</p>", "<p>kl</p>"],
                    solution_en: "<p>21.(b) try something new &rarr; db kl jk&hellip;&hellip;(i)<br>something hurt him &rarr; db pt uk&hellip;.(ii)<br>From (i) and (ii) &lsquo;something&rsquo; and &lsquo;db&rsquo; are common. The code of &lsquo;something&rsquo; = &lsquo;db&rsquo;.</p>",
                    solution_hi: "<p>21.(b) try something new &rarr; db kl jk&hellip;&hellip;(i)<br>something hurt him &rarr; db pt uk&hellip;.(ii)<br>(i) और (ii) से \'something\' और \'db\' उभयनिष्ठ हैं। \'something\' का कोड = \'db\'।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Below are two positions of a die. Which of the following options will appear on the opposite face to the face with \'1\' ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412119.png\" alt=\"rId37\" width=\"111\" height=\"57\"></p>",
                    question_hi: "<p>22. नीचे एक पासे की दो स्थितियां दी गई हैं। \'1\' वाले फलक के विपरीत फलक पर निम्नलिखित विकल्पों में से क्या आएगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412119.png\" alt=\"rId37\" width=\"111\" height=\"57\"></p>",
                    options_en: ["<p>4</p>", "<p>5</p>", 
                                "<p>3</p>", "<p>6</p>"],
                    options_hi: ["<p>4</p>", "<p>5</p>",
                                "<p>3</p>", "<p>6</p>"],
                    solution_en: "<p>22.(b)<br>From both the dice the opposite faces are <br>4 &harr; 3 , 1 &harr; 5</p>",
                    solution_hi: "<p>22.(b)<br>दोनों पासों के विपरीत फलक हैं<br>4 &harr; 3 , 1 &harr; 5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In a certain code language, \'FIVE\' is written as \'12184410\' and FOUR is written as \'12304236\'. How will \'THREE\' be written in that language?</p>",
                    question_hi: "<p>23. एक निश्चित कोड भाषा में, \'FIVE\' को \'12184410\' और \'FOUR\' को \'12304236\' लिखा जाता है। उसी भाषा में \'THREE\' कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>4016361110</p>", "<p>4016361310</p>", 
                                "<p>4016361212</p>", "<p>4016361010</p>"],
                    options_hi: ["<p>4016361110</p>", "<p>4016361310</p>",
                                "<p>4016361212</p>", "<p>4016361010</p>"],
                    solution_en: "<p>23.(d)<br><strong id=\"docs-internal-guid-6d8f178f-7fff-d54b-3ef7-73f37cca1c57\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdSx9DlXTgc_DdRKvVbQbsCz9NUQDMOTqIBJe-V0Mt26aOWyMGd7HssRFDguurFQeuBhPY0UgyBDkC35olPtLG5oJvFTeigBUhSRgmO9EA5fOWJ-tveLKtCpMlIlDT-Kr_DU16a?key=-IIZWABzPtCaFIvQZ4t2CQ3T\" width=\"161\" height=\"122\"></strong>&nbsp; &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412352.png\" alt=\"rId39\" width=\"170\" height=\"119\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412444.png\" alt=\"rId40\" width=\"178\"></p>",
                    solution_hi: "<p>23.(d)<br><strong id=\"docs-internal-guid-6d8f178f-7fff-d54b-3ef7-73f37cca1c57\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdSx9DlXTgc_DdRKvVbQbsCz9NUQDMOTqIBJe-V0Mt26aOWyMGd7HssRFDguurFQeuBhPY0UgyBDkC35olPtLG5oJvFTeigBUhSRgmO9EA5fOWJ-tveLKtCpMlIlDT-Kr_DU16a?key=-IIZWABzPtCaFIvQZ4t2CQ3T\" width=\"161\" height=\"122\"></strong>&nbsp; &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412352.png\" alt=\"rId39\" width=\"166\" height=\"116\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412444.png\" alt=\"rId40\" width=\"178\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Which of the following set of letters when arranged sequentially from left to right will complete the below series ?<br>xy_yzxyyyzxyy_zxyyyz_yyy_</p>",
                    question_hi: "<p>24. निम्नलिखित में से अक्षरों का कौन-सा समूह क्रमबद्ध रूप से बाईं से दाईं ओर रखे जाने पर नीचे दी गई श्रृंखला को पूरा करेगा ?<br>xy_yzxyyyzxyy_zxyyyz_yyy_</p>",
                    options_en: ["<p>yyxz</p>", "<p>yzyy</p>", 
                                "<p>ухух</p>", "<p>zzyy</p>"],
                    options_hi: ["<p>yyxz</p>", "<p>yzyy</p>",
                                "<p>ухух</p>", "<p>zzyy</p>"],
                    solution_en: "<p>24.(a)<br>x y <span style=\"text-decoration: underline;\"><strong>y</strong></span> y z / x y y y z / x y y <span style=\"text-decoration: underline;\"><strong>y</strong></span> z / xyyyz / <span style=\"text-decoration: underline;\"><strong>x</strong></span><strong> </strong>y y y <span style=\"text-decoration: underline;\"><strong>z</strong></span></p>",
                    solution_hi: "<p>24.(a)<br>x y <span style=\"text-decoration: underline;\"><strong>y</strong></span> y z / x y y y z / x y y <span style=\"text-decoration: underline;\"><strong>y</strong></span> z / xyyyz / <span style=\"text-decoration: underline;\"><strong>x</strong></span><strong> </strong>y y y <span style=\"text-decoration: underline;\"><strong>z</strong></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the correct mirror image of the given figure when the mirror MN is placed to the right side of the figure.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412535.png\" alt=\"rId41\" width=\"77\" height=\"82\"></p>",
                    question_hi: "<p>25. दी गई आकृति के उस सही दर्पण प्रतिबिंब का चयन कीजिए, जो दर्पण MN को उस आकृति के दाईं ओर रखने पर बनेगा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412535.png\" alt=\"rId41\" width=\"77\" height=\"82\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412641.png\" alt=\"rId42\" width=\"70\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412817.png\" alt=\"rId43\" width=\"70\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412933.png\" alt=\"rId44\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602413028.png\" alt=\"rId45\" width=\"70\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412641.png\" alt=\"rId42\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412817.png\" alt=\"rId43\" width=\"70\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412933.png\" alt=\"rId44\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602413028.png\" alt=\"rId45\" width=\"70\"></p>"],
                    solution_en: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412641.png\" alt=\"rId42\" width=\"70\"></p>",
                    solution_hi: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602412641.png\" alt=\"rId42\" width=\"70\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following Fundamental Rights CANNOT be suspended even when &lsquo;Emergency&rsquo; is declared in the country?</p>",
                    question_hi: "<p>26. निम्नलिखित में से कौन सा मौलिक अधिकार देश में \'आपातकाल\' घोषित होने पर भी निलंबित नहीं किया जा सकता है?</p>",
                    options_en: ["<p>Articles 22 and 23</p>", "<p>Article 20 and 21</p>", 
                                "<p>Article 19 and 20</p>", "<p>Articles 21 and 22</p>"],
                    options_hi: ["<p>अनुच्छेद 22 और 23</p>", "<p>अनुच्छेद 20 और 21</p>",
                                "<p>अनुच्छेद 19 और 20</p>", "<p>अनुच्छेद 21 और 22</p>"],
                    solution_en: "<p>26.(b) <strong>Articles 20 and 21</strong> cannot be suspended even in case of an emergency. <strong>Article 20:</strong> Protection in respect of conviction for offenses. <strong>Article 21</strong>: Protection of life and personal liberty.</p>",
                    solution_hi: "<p>26.(b) आपात स्थिति में भी <strong>अनुच्छेद 20 और 21</strong> को निलंबित नहीं किया जा सकता है। <strong>अनुच्छेद 20:</strong> अपराधों के लिए सजा के संबंध में संरक्षण। <strong>अनुच्छेद 21:</strong> जीवन और व्यक्तिगत स्वतंत्रता का संरक्षण।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Between which two cities does India&rsquo;s first semi high - speed train &lsquo;Vande Bharat Express&rsquo; run?</p>",
                    question_hi: "<p>27. भारत की पहली सेमी उच्च गति वाली रेलगाड़ी \"वंदे भारत एक्सप्रेस\" किन दो शहरों के बीच चलती है?</p>",
                    options_en: ["<p>Puri and Howrah Junction</p>", "<p>Ahmedabad and Mumbai central</p>", 
                                "<p>Hazrat Nizamuddin and Jhansi Junction</p>", "<p>New Delhi and Varanasi Junction</p>"],
                    options_hi: ["<p>पुरी और हावड़ा जंक्शन</p>", "<p>अहमदाबाद और मुंबई सेंट्रल</p>",
                                "<p>हजरत निजामुद्दीन और झांसी जंक्शन</p>", "<p>नई दिल्ली और वाराणसी जंक्शन</p>"],
                    solution_en: "<p>27.(d) <strong>New Delhi and Varanasi Junction:</strong> It is also known as Train - 18, which was designed and manufactured by Integral Coach Factory at Perambur, Chennai under the Indian Government&rsquo;s Make in India Initiative, over a span of 18 months (2019 - 21). <strong>2nd Vande Bharat Express-</strong> New Delhi - Shri Mata Vaishno Devi Katra, <strong>3rd Vande Bharat Express </strong>- Mumbai Central - Gandhinagar, <strong>4th Vande Bharat Express </strong>- New Delhi - Amb Andaura (Himachal pradesh).</p>",
                    solution_hi: "<p>27.(d) <strong>नई दिल्ली और वाराणसी जंक्शन:</strong> वंदे भारत एक्सप्रेस को ट्रेन - 18 के रूप में भी जाना जाता है, जिसे 18 महीने (2019 - 21) की अवधि में भारत सरकार की मेक इन इंडिया पहल के तहत पेरंबूर, चेन्नई में इंटीग्रल कोच फैक्ट्री द्वारा डिजाइन और निर्मित किया गया था। <strong>दूसरी वंदे भारत एक्सप्रेस-</strong> नई दिल्ली-श्री माता वैष्णो देवी कटरा, <strong>तीसरी वंदे भारत एक्सप्रेस</strong> - मुंबई सेंट्रल - गांधीनगर,<strong> चौथी वंदे भारत एक्सप्रेस-</strong>नई दिल्ली-अम्ब अंदौरा (हिमाचल प्रदेश).</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Match the following dance forms with their respective states:-<br>(P) Dumhal&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(i) Jammu &amp; Kashmir<br>(Q) Chharhi&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (ii) Himachal pradesh<br>(R) Spao&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iii) Ladakh<br>(S) Raut Nacha&nbsp; &nbsp; &nbsp; &nbsp; (iv) Chhattisgarh</p>",
                    question_hi: "<p>28. निम्नलिखित नृत्य रूपों को उनके संबंधित राज्यों के साथ सुमेलित करें:-<br>(P) दुमहल&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(i) जम्मू कश्मीर<br>(Q) छरही&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (ii) हिमाचल प्रदेश<br>(R) स्पाओ&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(iii) लद्दाख<br>(S) राउत नाचा&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(iv) छत्तीसगढ़</p>",
                    options_en: ["<p>P- (i), Q- (ii), R- (iii), S- (iv)</p>", "<p>P- (ii), Q- (i), R- (iii), S- (iv)</p>", 
                                "<p>P- (ii), Q- (iv), R- (i), S- (iii)</p>", "<p>P- (iv), Q- (i), R- (ii), S- (iii)</p>"],
                    options_hi: ["<p>P- (i), Q- (ii), R- (iii), S- (iv)</p>", "<p>P- (ii), Q- (i), R- (iii), S- (iv)</p>",
                                "<p>P- (ii), Q- (iv), R- (i), S- (iii)</p>", "<p>P- (iv), Q- (i), R- (ii), S- (iii)</p>"],
                    solution_en: "<p>28.(a) <strong>P- (i) , Q- (ii) , R- (iii) , S- (iv). Dumhal </strong>(Jammu &amp; Kashmir). Only men from the watal tribe can perform this dance. More dances of <strong>Kashmir</strong> are- Rouf, Bhand Pather, Hafiza Dance, Bhand Jashan, Bacha Nagma,Wuegi-Nachun. <strong>Himachal Pradesh</strong> &rarr; Jhora, Jhali, Dhaman, Chhapeli, Mahasu, Nati, Dangi. <strong>Spao Dance</strong> (\'the Mahabharata epic of Central Asia\'), Shondol Dance &rarr; Ladakh. Spao means warrior in Ladakhi language. <strong>Raut Nacha</strong> (performed by yadavas) &rarr; Chhattisgarh.</p>",
                    solution_hi: "<p>28.(a) <strong>P- (i) , Q- (ii) , R- (iii) , S- (iv). दुमहाल &rarr; जम्मू कश्मीर: </strong>केवल वाटल जनजाति के पुरुष ही इस नृत्य को कर सकते हैं। कश्मीर के और नृत्य हैं- रौफ, भांड पाथेर, हफीजा नृत्य, भांड जशन, बच्चा नगमा, वुगी-नचुन। <strong>हिमाचल</strong> प्रदेश &rarr; झोरा, झाली, धामन, छपेली, महासू, नाटी, डांगी। <strong>स्पाओ नृत्य</strong> (\'मध्य एशिया का महाभारत महाकाव्य\'), शोंडोल नृत्य &rarr; लद्दाख। स्पाओ का मतलब लद्दाखी भाषा में योद्धा होता है। <strong>राउत नाचा</strong> (यादवों द्वारा प्रस्तुत) &rarr; छत्तीसगढ़।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following is the youngest percussionist to be awarded Padma Shri and Padma Bhushan awards by the Government of India?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन भारत सरकार द्वारा पद्म श्री और पद्म भूषण पुरस्कार से सम्मानित होने वाले सबसे कम उम्र के तालवादक हैं?</p>",
                    options_en: ["<p>T H Vinayakram</p>", "<p>Sivamani</p>", 
                                "<p>Sarvar Sabri</p>", "<p>Zakir Hussain</p>"],
                    options_hi: ["<p>टी एच विनायक राम</p>", "<p>शिवमणि</p>",
                                "<p>सरवर सबरीक</p>", "<p>जाकिर हुसैन</p>"],
                    solution_en: "<p>29.(d) <strong>Zakir Hussain:</strong> He is the youngest percussionist to be awarded Padma Shri (1988) and Padma Bhushan (2002) awarded by the Government of India. In 1999, he was awarded the United States National Endowment for the Arts&rsquo; National Heritage Fellowship, the highest award given to traditional artists &amp; musicians. Examples of Percussion Instruments -Piano, Timpani, Xylophone, Cymbals, Triangle, Drums, Tambourine, Maracas, Gong, Chimes, Castanets, Celeste etc.</p>",
                    solution_hi: "<p>29.(d) <strong>जाकिर हुसैन:</strong> इन्हें भारत सरकार द्वारा पद्म श्री (1988) और पद्म भूषण (2002) से सम्मानित होने वाले सबसे कम उम्र के तालवादक हैं। 1999 में, उन्हें यूनाइटेड स्टेट्स नेशनल एंडॉमेंट फॉर द आर्ट्स नेशनल हेरिटेज फेलोशिप से सम्मानित किया गया, जो पारंपरिक कलाकारों को दिया जाने वाला सर्वोच्च पुरस्कार है। तालवाद्य के उदाहरण हैं - पियानो, टिमपनी, जाइलोफोन, झांझ, त्रिकोण, ड्रम, टैम्बोरिन, माराकास, गोंग, चाइम्स, कास्टानेट्स, सेलेस्टे आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Match the artist with their respective musical instruments;-<br>(P) Shiv kumar sharma&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (i) Ghatam<br>(Q) EM Subramaniam&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (ii) Santoor<br>(R) Hariprasad Chaurasia&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iii) Bansuri<br>(S) Pandit Bhawani Shankar&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iv) Pakhawaj</p>",
                    question_hi: "<p>30. कलाकार को उनके संबंधित वाद्य यंत्रों से सुमेलित करें;-<br>(P) शिव कुमार शर्मा&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (i) घटम<br>(Q) ईएम सुब्रमण्यम&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (ii) संतूर<br>(R) हरिप्रसाद चौरसिया&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iii) बांसुरी<br>(S) पंडित भवानी शंकर&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iv) पखावाज</p>",
                    options_en: ["<p>P- (i), Q- (ii), R- (iii), S- (iv)</p>", "<p>P- (ii), Q- (i), R- (iii), S- (iv)</p>", 
                                "<p>P- (ii), Q- (iv), R- (i), S- (iii)</p>", "<p>P- (iv), Q- (i), R- (ii), S- (iii)</p>"],
                    options_hi: ["<p>P- (i), Q- (ii), R- (iii), S- (iv)</p>", "<p>P- (ii), Q- (i), R- (iii), S- (iv)</p>",
                                "<p>P- (ii), Q- (iv), R- (i), S- (iii)</p>", "<p>P- (iv), Q- (i), R- (ii), S- (iii)</p>"],
                    solution_en: "<p>30.(b) <strong>P- (ii), Q- (i), R- (iii), S- (iv). Shiv Kumar Sharma</strong> won the Global Indian Music Award in 2010 and Padma Vibhushan in 2001. Bhajan Sopori, Rahul Sharma, Ulhas Bapat &rarr; <strong>Santoor Player. Pakhawaj Player-</strong> Lala Bhagwandas. Nanasaheb Panse, Kudau Singh, Pandit Rama Kant Pathak Ji.</p>",
                    solution_hi: "<p>30.(b) <strong>P- (ii) , Q- (i) , R- (iii) , S- (iv). शिव कुमार शर्मा </strong>ने 2010 में ग्लोबल इंडियन म्यूजिक अवार्ड और 2001 में पद्म विभूषण जीता। भजन सोपोरी, राहुल शर्मा, उल्हास बापट&rarr; <strong>संतूर प्लेयर। पखावज वादक </strong>- लाला भगवानदास ,नानासाहेब पांसे, कुदौ सिंह, पंडित रामकांत पाठक जी</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who was the first ever female secretary general of SAARC (South Asian Association for Regional Cooperation)?</p>",
                    question_hi: "<p>31. सार्क (दक्षिण एशियाई क्षेत्रीय सहयोग संघ) की पहली महिला महासचिव कौन थीं ?</p>",
                    options_en: ["<p>Antonio Guterres</p>", "<p>Fathimath Dhiyana Saeed</p>", 
                                "<p>Jeremiah Nyamane Kingsley</p>", "<p>Madeleine Albright</p>"],
                    options_hi: ["<p>एंटोनियो गुटेरस</p>", "<p>फतिमाथ धियाना सईद</p>",
                                "<p>जेरेमिया न्यामाने किंग्सले</p>", "<p>मैडलीन अलब्राइट</p>"],
                    solution_en: "<p>31.(b) <strong>Fathimath Dhiyana Saeed:</strong> She is a Maldivian diplomat, and was the Secretary-General of the South Asian Association for Regional Cooperation (SAARC). The South Asian Association for Regional Cooperation (SAARC) was established in Dhaka on 8 December 1985. Originally there were 7 members in the SAARC.</p>",
                    solution_hi: "<p>31.(b) <strong>फातिमथ धियाना सईद:</strong> यह मालदीव के राजनयिक हैं, और दक्षिण एशियाई क्षेत्रीय सहयोग संघ (SAARC) के महासचिव थीं. वह 1985 में संगठन की स्थापना के बाद से इस पद को संभालने वाली पहली महिला थीं। दक्षिण एशियाई क्षेत्रीय सहयोग संघ (सार्क) की स्थापना 8 दिसंबर 1985 को ढाका में हुई थी। मूल रूप से सार्क में 7 सदस्य थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Who among the following was India\'s first world champion in any sport after independence?</p>",
                    question_hi: "<p>32. स्वतंत्रता के बाद किसी भी खेल में भारत का पहला विश्व चैंपियन निम्नलिखित में से कौन था?</p>",
                    options_en: ["<p>Milkha Singh</p>", "<p>Lila Ram</p>", 
                                "<p>Ramanathan Krishnan</p>", "<p>Wilson Jones</p>"],
                    options_hi: ["<p>मिल्खा सिंह</p>", "<p>लीला राम</p>",
                                "<p>रामनाथन कृष्णन</p>", "<p>विल्सन जोन्स</p>"],
                    solution_en: "<p>32.(d) <strong>Wilson Jones:</strong> He won both the national billiards and snooker titles in 1952 and 1954. In London, he played his first World Billiards Championship in 1951.</p>",
                    solution_hi: "<p>32.(d) <strong>विल्सन जोन्स:</strong> यह स्वतंत्रता के बाद किसी भी खेल में भारत के पहले विश्व चैंपियन थे। उन्होंने 1952 और 1954 में राष्ट्रीय बिलियर्ड्स और स्नूकर दोनों खिताब जीते। लंदन में, उन्होंने 1951 में अपनी पहली विश्व बिलियर्ड्स चैंपियनशिप खेली।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which Haryana Cabinet Minister introduced the \'Samman Sanjeevani\' app in January 2025 to enhance the \'Mahila Evam Kishori Samman Yojana\'?</p>",
                    question_hi: "<p>33. हरियाणा के किस कैबिनेट मंत्री ने जनवरी 2025 में \'महिला एवं किशोरी सम्मान योजना\' को बढ़ाने के लिए \'सम्मान संजीवनी\' ऐप पेश किया?</p>",
                    options_en: ["<p>Kanwar Pal Gujjar</p>", "<p>Shruti Choudhry</p>", 
                                "<p>Anoop Dhanak</p>", "<p>Mool Chand Sharma</p>"],
                    options_hi: ["<p>कंवर पाल गुज्जर</p>", "<p>श्रुति चौधरी</p>",
                                "<p>अनूप धानक</p>", "<p>मूल चंद शर्मा</p>"],
                    solution_en: "<p>33.(b) <strong>Shruti Choudhry.</strong> It was introduced on National Girl Child Day, celebrated annually on January 24 in India. The initiative is designed to provide essential services, particularly sanitary napkins, to Below Poverty Line (BPL) women and girls aged 10 to 45.</p>",
                    solution_hi: "<p>33.(b) <strong>श्रुति चौधरी।</strong> इसे भारत में प्रतिवर्ष 24 जनवरी को मनाए जाने वाले राष्ट्रीय बालिका दिवस पर पेश किया गया था। इस पहल का उद्देश्य गरीबी रेखा से नीचे (बीपीएल) रहने वाली महिलाओं और 10 से 45 वर्ष की लड़कियों को आवश्यक सेवाएँ, विशेष रूप से सैनिटरी नैपकिन प्रदान करना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. What is the name of India&rsquo;s second multi-purpose vessel unveiled in January 2025 ?</p>",
                    question_hi: "<p>34. जनवरी 2025 में अनावरण किए जाने वाले भारत के दूसरे बहुउद्देश्यीय पोत का नाम क्या है?</p>",
                    options_en: ["<p>Utkash</p>", "<p>Vikram</p>", 
                                "<p>Mahavir</p>", "<p>Garuda</p>"],
                    options_hi: ["<p>उत्कर्ष</p>", "<p>विक्रम</p>",
                                "<p>महावीर</p>", "<p>गरुड़</p>"],
                    solution_en: "<p>34.(a) <strong>Utkash.</strong> On January 13, 2025, the second Multi-Purpose Vessel (MPV), built by M/s L&amp;T Shipyard for the Indian Navy, was launched at the L&amp;T Shipyard in Kattupalli, Chennai. The event was attended by Defence Secretary Rajesh Kumar Singh, along with senior officials from the Indian Navy and L&amp;T Shipyard. The contract for the two Multi-Purpose Vessels was signed on March 25, 2022, between the Ministry of Defence and M/s L&amp;T Shipyard. The first MPV was launched earlier.</p>",
                    solution_hi: "<p>34.(a) <strong>उत्कर्ष।</strong> 13 जनवरी, 2025 को भारतीय नौसेना के लिए मेसर्स लार्सन एंड टुब्रो (L&amp;T) शिपयार्ड द्वारा निर्मित दूसरा बहुउद्देश्यीय पोत (MPV) चेन्नई के कट्टुपल्ली में L&amp;T शिपयार्ड में लॉन्च किया गया। इस कार्यक्रम में रक्षा सचिव राजेश कुमार सिंह के साथ-साथ भारतीय नौसेना और L&amp;T शिपयार्ड के वरिष्ठ अधिकारी भी शामिल हुए। दो बहुउद्देश्यीय पोतों के लिए अनुबंध पर 25 मार्च, 2022 को रक्षा मंत्रालय और मेसर्स L&amp;T शिपयार्ड के बीच हस्ताक्षर किए गए थे। पहले एमपीवी को पहले लॉन्च किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. When was the Backward and Minority Communities Employees Federation (BAMCEF) established in India?</p>",
                    question_hi: "<p>35. भारत में पिछड़ा तथा अल्पसंख्यक समुदायों के कर्मचारियों का संघ(BAMCEF) कब स्थापित किया गया था?</p>",
                    options_en: ["<p>1979</p>", "<p>1980</p>", 
                                "<p>1978</p>", "<p>1989</p>"],
                    options_hi: ["<p>1979</p>", "<p>1980</p>",
                                "<p>1978</p>", "<p>1989</p>"],
                    solution_en: "<p>35.(c) <strong>1978:</strong> The origin of BAMCEF lies in the organization for employees of oppressed communities that was established in 1971 by Kanshi Ram, D. K. Khaparde and Dinabhai.</p>",
                    solution_hi: "<p>35.(c) <strong>1978:</strong> BAMCEF की उत्पत्ति उत्पीड़ित समुदायों के कर्मचारियों के लिए संगठन में निहित है जिसे 1971 में कांशी राम, डी.के. खापर्डे और दीनाभाई द्वारा स्थापित किया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Who authored the book &ldquo;The Scientist Entrepreneur: Empowering Millions of Women&rdquo;, released by Dr. Soumya Swaminathan?</p>",
                    question_hi: "<p>36. डॉ. सौम्या स्वामीनाथन द्वारा जारी पुस्तक &ldquo;द साइंटिस्ट एंटरप्रेन्योर: एम्पावरिंग मिलियन्स ऑफ वीमेन&rdquo; के लेखक कौन हैं?</p>",
                    options_en: ["<p>Dr. Kalpana Sankar</p>", "<p>Dr. Raghuram Rajan</p>", 
                                "<p>Dr. Bibhab Kumar Talukdar</p>", "<p>Dr. S. L. Bhyrappa</p>"],
                    options_hi: ["<p>डॉ. कल्पना शंकर</p>", "<p>डॉ. रघुराम राजन</p>",
                                "<p>डॉ. बिभब कुमार तालुकदार</p>", "<p>डॉ. एस. एल. भैरप्पा</p>"],
                    solution_en: "<p>36.(a) <strong>Dr. Kalpana Sankar.</strong> The book chronicles Dr. Sankar\'s journey from a nuclear scientist to a pioneering entrepreneur empowering millions of women.</p>",
                    solution_hi: "<p>36.(a) <strong>डॉ. कल्पना शंकर।</strong> यह पुस्तक डॉ. शंकर की परमाणु वैज्ञानिक से लेकर लाखों महिलाओं को सशक्त बनाने वाली अग्रणी उद्यमी बनने की यात्रा का वर्णन करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Traveling from the Arctic Circle to the Equator, what sequence of biomes would one pass through?</p>",
                    question_hi: "<p>37. आर्कटिक सर्कल से भूमध्य रेखा तक यात्रा करते हुए जैवक्षेत्र (biomes) के किस क्रम से गुजरना होगा?</p>",
                    options_en: ["<p>Tropical Rain Forests, Temperate Forests, Taiga, Tundra</p>", "<p>Taiga, Tundra, Temperate Forests, Tropical Rain Forests</p>", 
                                "<p>Tundra, Taiga, Temperate Forests, Tropical Rain Forests</p>", "<p>Tundra, Temperate Forests, Taiga, Tropical Rain Forests</p>"],
                    options_hi: ["<p>उष्णकटिबंधीय वर्षा वन, समशीतोष्ण वन, टैगा, टुंड्रा</p>", "<p>टैगा, टुंड्रा, समशीतोष्ण वन, उष्णकटिबंधीय वर्षा वन</p>",
                                "<p>टुंड्रा, टैगा, समशीतोष्ण वन, उष्णकटिबंधीय वर्षा वन</p>", "<p>टुंड्रा, समशीतोष्ण वन, टैगा, उष्णकटिबंधीय वर्षा वन</p>"],
                    solution_en: "<p>37.(c) Tundra, Taiga, Temperate Forests, Tropical Rain Forests. There are five major types of <strong>biomes:</strong> aquatic, grassland, forest, desert, and tundra.</p>",
                    solution_hi: "<p>37.(c) टुंड्रा, टैगा, समशीतोष्ण वन, उष्णकटिबंधीय वर्षा वन: पांच प्रमुख प्रकार के <strong>जैवक्षेत्र(biomes)</strong> हैं: जलीय, घास का मैदान, जंगल, रेगिस्तान और टुंड्रा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Under which Governor General did Britishers adopt &lsquo;Policy of Paramountcy&rsquo; ?</p>",
                    question_hi: "<p>38. किस गवर्नर जनरल के नेतृत्व में अंग्रेजों ने &lsquo;सर्वोपरिता की नीति&rsquo; अपनाई थी ?</p>",
                    options_en: ["<p>Lord Dalhousie</p>", "<p>Lord Clive</p>", 
                                "<p>Lord Cornwallis</p>", "<p>Lord Hastings</p>"],
                    options_hi: ["<p>लॉर्ड डलहौजी</p>", "<p>लॉर्ड क्लाइव</p>",
                                "<p>लॉर्ड कार्नवालिस</p>", "<p>लॉर्ड हेस्टिंग्स</p>"],
                    solution_en: "<p>38.(d) <strong>Lord Hastings:</strong> He was the first Governor General of India from 1813. Under Lord Hasting a new policy of \"paramountcy\" was introduced in India, the Company claimed that its authority was paramount or supreme, hence its power was greater than that of Indian states. <strong>Lord Clive</strong> was the first British Governor of the Bengal Presidency. The Dual System of Government in Bengal was the brainchild of <strong>Robert Clive</strong> in 1765. <strong>Lord Dalhousie</strong> is most remembered for the Doctrine of Lapse policy. <strong>Lord Cornwallis</strong> introduced the Permanent Settlement of land revenue in Bengal and other parts of India.</p>",
                    solution_hi: "<p>38.(d) <strong>लॉर्ड हेस्टिंग्स:</strong> यह 1813 से भारत के पहले गवर्नर-जनरल थे। लॉर्ड हेस्टिंग के तहत, भारत में \"सर्वोच्चता\" की एक नई नीति पेश की गई, जिसके तहत कंपनी ने दावा किया कि उसका अधिकार सर्वोपरि या सर्वोच्च था, इसलिए उसकी शक्ति भारतीय राज्यों की तुलना में अधिक थी। <strong>लॉर्ड क्लाइव,</strong> बंगाल प्रेसीडेंसी के पहले ब्रिटिश गवर्नर थे, बंगाल में सरकार की दोहरी प्रणाली 1765 में रॉबर्ट क्लाइव के दिमाग की उपज थी। <strong>लॉर्ड डलहौजी,</strong> को डॉक्ट्रिन ऑफ लैप्स(हड़प नीति) के लिए सबसे ज्यादा याद किया जाता है। <strong>लॉर्ड कॉर्नवालिस,</strong> ने बंगाल और भारत के अन्य हिस्सों में भू-राजस्व के स्थायी बंदोबस्त की शुरुआत की।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. When is World Suicide Prevention Day observed?</p>",
                    question_hi: "<p>39. &lsquo;विश्व आत्महत्या रोकथाम दिवस&rsquo; कब मनाया जाता है?</p>",
                    options_en: ["<p>September 12</p>", "<p>September 11</p>", 
                                "<p>September 9</p>", "<p>September 10</p>"],
                    options_hi: ["<p>12 सितंबर</p>", "<p>11 सितंबर</p>",
                                "<p>9 सितंबर</p>", "<p>10 सितंबर</p>"],
                    solution_en: "<p>39.(d) <strong>September 10:</strong> It is observed to raise awareness to prevent cases of suicide. It was started from 2003 by World Health Organization (WHO) and International Association for Suicide Prevention(IASP).</p>",
                    solution_hi: "<p>39.(d) <strong>10 सितंबर:</strong> यह 2003 से विश्व स्वास्थ्य संगठन (WHO) और इंटरनेशनल एसोसिएशन फॉर सुसाइड प्रिवेंशन (IASP) द्वारा आत्महत्या के मामलों को रोकने के लिए जागरूकता बढ़ाने के लिए शुरू किया गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. The Atharvaveda is a collection of ______ khandas.</p>",
                    question_hi: "<p>40. अथर्ववेद ______ खण्डों का एक संग्रह है।</p>",
                    options_en: ["<p>20</p>", "<p>15</p>", 
                                "<p>10</p>", "<p>5</p>"],
                    options_hi: ["<p>20</p>", "<p>15</p>",
                                "<p>10</p>", "<p>5</p>"],
                    solution_en: "<p>40.(a) <strong>20 khands.</strong> Atharvveda (book of magic and charms) is a collection of 730 hymns with about 6,000 mantras. Other 3 vedas are Rigveda (earliest form of Veda), Yazurveda (book of prayers) and Samveda (earliest reference for singing).</p>",
                    solution_hi: "<p>40.(a) <strong>20 खंड।</strong> अथर्ववेद (जादू और आकर्षण की पुस्तक) लगभग 6,000 मंत्रों के साथ 730 भजनों का संग्रह है। अन्य 3 वेद ऋग्वेद (वेद का सबसे पुराना रूप), यजुर्वेद (प्रार्थना की किताब) और सामवेद (गायन के लिए सबसे पुराना संदर्भ) हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following Articles of the Constitution of India lays down the duties and functions of the Union and State Public Service Commissions in India?</p>",
                    question_hi: "<p>41. भारत के संविधान के निम्नलिखित में से कौन सा अनुच्छेद भारत में संघ और राज्य लोक सेवा आयोगों के कर्तव्यों और कार्यों को निर्धारित करता है?</p>",
                    options_en: ["<p>Article 312</p>", "<p>Article 320</p>", 
                                "<p>Article 308</p>", "<p>Article 316</p>"],
                    options_hi: ["<p>अनुच्छेद 312</p>", "<p>अनुच्छेद 320</p>",
                                "<p>अनुच्छेद 308</p>", "<p>अनुच्छेद 316</p>"],
                    solution_en: "<p>41.(b) <strong>Article 320. Article 312-</strong> All India Services. <strong>Articles (308</strong> &ndash; <strong>314)-</strong> Contain provisions with regard to All India Services, Central services and state services. <strong>Article-316</strong> - Appointment and term of office of members.</p>",
                    solution_hi: "<p>41.(b) <strong>अनुच्छेद 320: अनुच्छेद 312-</strong> अखिल भारतीय सेवाएं। <strong>अनुच्छेद (308</strong> - <strong>314)</strong> - अखिल भारतीय सेवाओं, केंद्रीय सेवाओं और राज्य सेवाओं के संबंध में प्रावधान। <strong>अनुच्छेद-316</strong> - सदस्यों की नियुक्ति और कार्यकाल।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. ____is known as the &lsquo;Father of Indian Ornithology&rsquo;</p>",
                    question_hi: "<p>42. ______ को भारतीय &lsquo;पक्षी विज्ञान का जनक&rsquo; कहा जाता है।</p>",
                    options_en: ["<p>Salim Ali</p>", "<p>John James Audubon</p>", 
                                "<p>Konrad Lorenz</p>", "<p>AO Hume</p>"],
                    options_hi: ["<p>सालिम अली</p>", "<p>जॉन जेम्स औडुबन</p>",
                                "<p>कोनार्ड लोरेंज</p>", "<p>एलन ओक्टावियन ह्यूम</p>"],
                    solution_en: "<p>42.(d) <strong>Allan Octavian Hume:</strong> - He was a Scottish civil servant, political reformer and the founder of the Indian National Congress. He was described by Dr Salim Ali as \'Father\' of Indian Ornithology. <strong>Salim Ali</strong> is known as the &ldquo;birdman of India&rdquo;. <strong>Konrad Lorenz</strong> was the founder of modern ethology.</p>",
                    solution_hi: "<p>42.(d) <strong>एलन ऑक्टेवियन ह्यूम:</strong> यह एक स्कॉटिश सिविल सेवक, राजनीतिक सुधारक और भारतीय राष्ट्रीय कांग्रेस के संस्थापक थे। उन्हें डॉ सलीम अली द्वारा भारतीय पक्षीविज्ञान के \'पिता\' के रूप में वर्णित किया गया था। <strong>सलीम अली</strong> को \"भारत का बर्डमैन\" कहा जाता है। <strong>कोनराड लोरेंज</strong> आधुनिक इथोलॉजी के संस्थापक थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. What new initiative was launched by the government in January 2025, coinciding with Army Day celebrations?</p>",
                    question_hi: "<p>43. जनवरी 2025 में सेना दिवस समारोह के साथ सरकार द्वारा कौन सी नई पहल शुरू की गई?</p>",
                    options_en: ["<p>Bharat Raksha App</p>", "<p>Bharat Ranbhoomi Darshan app and website</p>", 
                                "<p>National Defense History Portal</p>", "<p>Indian Military Heritage App</p>"],
                    options_hi: ["<p>भारत रक्षा ऐप</p>", "<p>भारत रणभूमि दर्शन ऐप और वेबसाइट</p>",
                                "<p>राष्ट्रीय रक्षा इतिहास पोर्टल</p>", "<p>भारतीय सैन्य विरासत ऐप</p>"],
                    solution_en: "<p>43.(b) <strong>Bharat Ranbhoomi Darshan app and website.</strong> In a significant initiative to connect civilians with the valorous history of India&rsquo;s armed forces, the government has launched the &ldquo;Bharat Ranbhoomi Darshan&rdquo; app and website on January 15, 2025, coinciding with Army Day celebrations in Pune, Maharashtra. Developed jointly by the Ministries of Defence, Tourism, and the Indian Army, the app allows civilians to visit key battlefield locations.</p>",
                    solution_hi: "<p>43.(b) <strong>भारत रणभूमि दर्शन ऐप और वेबसाइट</strong>। नागरिकों को भारत के सशस्त्र बलों के वीरतापूर्ण इतिहास से जोड़ने की एक महत्वपूर्ण पहल में, सरकार ने 15 जनवरी, 2025 को पुणे, महाराष्ट्र में सेना दिवस समारोह के साथ &ldquo;भारत रणभूमि दर्शन&rdquo; ऐप और वेबसाइट लॉन्च की है। रक्षा, पर्यटन और भारतीय सेना मंत्रालयों द्वारा संयुक्त रूप से विकसित, यह ऐप नागरिकों को प्रमुख युद्धक्षेत्र स्थानों पर जाने की अनुमति देता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. When were the first Lok Sabha elections held?</p>",
                    question_hi: "<p>44. पहला लोकसभा चुनाव कब हुआ था</p>",
                    options_en: ["<p>1949 - 50</p>", "<p>1951 - 52</p>", 
                                "<p>1948 - 49</p>", "<p>1953 - 54</p>"],
                    options_hi: ["<p>1949 - 50</p>", "<p>1951 - 52</p>",
                                "<p>1948 - 49</p>", "<p>1953 - 54</p>"],
                    solution_en: "<p>44.(b) <strong>1951-52.</strong> The maximum size of the Lok Sabha as outlined in the Constitution of India is 550 members. G.V. Mavalankar was the first Lok Sabha Speaker.</p>",
                    solution_hi: "<p>44.(b) <strong>1951-52:</strong> भारत के संविधान में उल्लिखित लोकसभा का अधिकतम आकार 550 सदस्यों का है। जी.वी. मावलंकर पहले लोकसभा अध्यक्ष थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following events occurred before the arrival of the Simon Commission in India?</p>",
                    question_hi: "<p>45. भारत में साइमन कमीशन के आने से पहले निम्नलिखित में से कौन सी घटना घटी थी?</p>",
                    options_en: ["<p>The First Round Table Conference</p>", "<p>Gandhi-Irwin Pact</p>", 
                                "<p>Quit India Movement</p>", "<p>Kakori Conspiracy</p>"],
                    options_hi: ["<p>पहला गोलमेज सम्मेलन</p>", "<p>गांधी-इरविन समझौता</p>",
                                "<p>भारत छोड़ो आंदोलन</p>", "<p>काकोरी कांड</p>"],
                    solution_en: "<p>45.(d) The <strong>Kakori Train Robbery</strong> was a train robbery at Kakori, Lucknow, on 9 August <strong>1925.</strong> The Uttar Pradesh government has renamed the \'Kakori Kand\' to \'Kakori Train action\'. <strong>Simon Commission</strong> arrived in British India in <strong>1928,</strong> It was the Indian Statutory Commission, which was a group of seven Members of Parliament under the chairmanship of Sir John Simon. First Round Table Conference (1930 &ndash; 1931); Gandhi-Irwin Pact (1931); Quit India Movement (1942).</p>",
                    solution_hi: "<p>45.(d) <strong>काकोरी ट्रेन डकैती</strong> 9 अगस्त 1925 को काकोरी, लखनऊ में एक ट्रेन डकैती थी। उत्तर प्रदेश सरकार ने \'काकोरी कांड\' का नाम बदलकर \'काकोरी ट्रेन एक्शन\' कर दिया है। साइमन कमीशन 1928 में ब्रिटिश भारत में आया, यह भारतीय वैधानिक आयोग था, जो <strong>सर जॉन साइमन</strong> की अध्यक्षता में संसद के सात सदस्यों का एक समूह था। पहला गोलमेज सम्मेलन (1930 - 1931); गांधी-इरविन समझौता (1931); भारत छोड़ो आंदोलन (1942)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following Indian states are situated on the Bangladesh border?</p>",
                    question_hi: "<p>46. निम्नलिखित में से कौन से भारतीय राज्य बांग्लादेश सीमा पर स्थित है?</p>",
                    options_en: ["<p>Mizoram, Tripura, Sikkim, Manipur, West Bengal</p>", "<p>West Bengal, Assam, Sikkim, Nagaland, Meghalaya</p>", 
                                "<p>Sikkim, Nagaland, Meghalaya, West Bengal, Mizoram</p>", "<p>West Bengal, Assam, Meghalaya, Tripura, Mizoram</p>"],
                    options_hi: ["<p>मिजोरम, त्रिपुरा, सिक्किम, मणिपुर, पश्चिम बंगाल</p>", "<p>पश्चिम बंगाल, असम, सिक्किम, नागालैंड, मेघालय</p>",
                                "<p>सिक्किम, नागालैंड, मेघालय, पश्चिम बंगाल, मिजोरम</p>", "<p>पश्चिम बंगाल, असम, मेघालय, त्रिपुरा, मिजोरम</p>"],
                    solution_en: "<p>46.(d) <strong>West Bengal, Assam, Meghalaya, Tripura, Mizoram.</strong> Other neighboring countries of India: Afghanistan, Bhutan, China, Maldives, Myanmar, Nepal, Pakistan, and Sri Lanka.</p>",
                    solution_hi: "<p>46.(d) <strong>पश्चिम बंगाल, असम, मेघालय, त्रिपुरा, मिजोरम।</strong> भारत के अन्य पड़ोसी देश: अफगानिस्तान, भूटान, चीन, मालदीव, म्यांमार, नेपाल, पाकिस्तान और श्रीलंका है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Electron-volt is a unit of ______.</p>",
                    question_hi: "<p>47. इलेक्ट्रॉन-वोल्ट _____ की इकाई है।</p>",
                    options_en: ["<p>current</p>", "<p>energy</p>", 
                                "<p>power</p>", "<p>potential difference</p>"],
                    options_hi: ["<p>वर्तमान</p>", "<p>ऊर्जा</p>",
                                "<p>शक्ति</p>", "<p>संभावित अंतर</p>"],
                    solution_en: "<p>47.(b) <strong>Electron-volt:</strong> An electron volt is defined as the amount of kinetic energy acquired by an electron when it is accelerated in an electric field produced by a potential difference of one volt. One electron volt is equal to 1.602 &times; 10<sup>-12</sup>&nbsp;erg or 1.602 &times; 10<sup>-19</sup> joule. The SI unit of Current is Ampere, Power is Watt and Potential difference is Joules per coulomb.</p>",
                    solution_hi: "<p>47.(b) <strong>ऊर्जा: इलेक्ट्रॉन-वोल्ट</strong> आमतौर पर परमाणु और परमाणु भौतिकी में प्रयोग किया जाता है। एक इलेक्ट्रॉन वोल्ट 1.602 &times; 10<sup>-12</sup>&nbsp;अर्ग या 1.602 &times; 10<sup>-19</sup> जूल के बराबर होता है। करंट का SI मात्रक एम्पीयर है, पावर का वाट है और विभवांतर का जूल पर कूलम्ब है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following hormone is secreted by the brain that helps to regulate sleep-wake cycles?</p>",
                    question_hi: "<p>48. निम्नलिखित में से कौन सा हार्मोन मस्तिष्क द्वारा स्रावित होता है जो नींद-जागने के चक्र को नियंत्रित करने में मदद करता है?</p>",
                    options_en: ["<p>Insulin</p>", "<p>Melatonin</p>", 
                                "<p>Oxytocin</p>", "<p>Aldosterone</p>"],
                    options_hi: ["<p>इंसुलिन</p>", "<p>मेलाटोनिन</p>",
                                "<p>ऑक्सीटोसिन</p>", "<p>एल्डोस्टेरोन</p>"],
                    solution_en: "<p>48.(b) <strong>Melatonin hormone.</strong> It is a peptide hormone secreted by the pineal gland, a small endocrine gland in the brain. <strong>Insulin</strong> is a hormone created by our pancreas that controls the amount of glucose in our bloodstream at any given moment. <strong>Oxytocin (love hormone)</strong> is a hormone that\'s produced in the hypothalamus and released into the bloodstream by the pituitary gland. <strong>Aldosterone</strong> is the steroid hormone secreted by the adrenal cortex, promotes retention of sodium and excretion of potassium by the kidneys.</p>",
                    solution_hi: "<p>48.(b) <strong>मेलाटोनिन:</strong> यह हार्मोन मस्तिष्क द्वारा स्रावित होता है जो नींद - जागने के चक्र को विनियमित करने में मदद करता है। मेलाटोनिन का उत्पादन सेरोटोनिन (ट्रिप्टोफैन से बना) से होता है, जो एंजाइमी अभिक्रियाओं के एक कैस्केड के माध्यम से होता है। इसका उत्पादन शाम के अंधेरे के साथ बढ़ता है, स्वस्थ नींद को बढ़ावा देता है और हमारे सर्कैडियन लय को उन्मुख करने में मदद करता है। <strong>इंसुलिन</strong> आपके अग्न्याशय द्वारा निर्मित एक हार्मोन है जो किसी भी समय आपके रक्तप्रवाह में ग्लूकोज की मात्रा को नियंत्रित करता है। <strong>ऑक्सीटोसिन</strong> एक हार्मोन और एक स्नायुसंचारी है जो बच्चे के जन्म और स्तनपान में शामिल होता है। इसे \"लव हार्मोन\" भी कहा जाता है। <strong>एल्डोस्टेरोन</strong> एक मिनरलोकॉर्टिकॉइड हार्मोन है जो अधिवृक्क प्रांतस्था के जोना ग्लोमेरुलोसा में निर्मित होता है जो शरीर में पानी और नमक के नियमन को प्रभावित करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Where was the 45th International Chess Federation (FIDE) Chess Olympiad, held in September 2024?</p>",
                    question_hi: "<p>49. 45वीं अंतर्राष्ट्रीय शतरंज महासंघ (FIDE) शतरंज ओलंपियाड, सितंबर 2024 में कहां आयोजित हुई?</p>",
                    options_en: ["<p>Hungary, Budapest</p>", "<p>India, Chennai</p>", 
                                "<p>Spain, Madrid</p>", "<p>Armenia, Yerevan</p>"],
                    options_hi: ["<p>हंगरी, बुडापेस्ट</p>", "<p>भारत, चेन्नई</p>",
                                "<p>स्पेन, मैड्रिड</p>", "<p>आर्मेनिया, येरेवान</p>"],
                    solution_en: "<p>49.(a) <strong>Hungary, Budapest.</strong> Team India achieved a historic feat by winning the Gold medal in both the Open and Women&rsquo;s sections of the 45th International Chess Federation (FIDE) Chess Olympiad held in Budapest, Hungary, in September 2024. This marked the first time India won gold in both sections of the Chess Olympiad. The Open section victory awarded India the prestigious Hamilton-Russell Cup, the original Olympiad trophy created in 1927.</p>",
                    solution_hi: "<p>49.(a) <strong>हंगरी, बुडापेस्ट। </strong>टीम इंडिया ने सितंबर 2024 में हंगरी के बुडापेस्ट में आयोजित 45वें अंतर्राष्ट्रीय शतरंज महासंघ (FIDE) शतरंज ओलंपियाड के ओपन और महिला दोनों वर्गों में स्वर्ण पदक जीतकर ऐतिहासिक उपलब्धि हासिल की। ​​यह पहली बार है जब भारत ने शतरंज ओलंपियाड के दोनों वर्गों में स्वर्ण पदक जीता। ओपन वर्ग में जीतने पर भारत को प्रतिष्ठित हैमिल्टन-रसेल कप से सम्मानित किया गया, जो 1927 में बनाया गया मूल ओलंपियाड ट्रॉफी है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. How many bones are there in the human hand?</p>",
                    question_hi: "<p>50. मनुष्य के हाथ में कितनी हड्डियाँ होती हैं?</p>",
                    options_en: ["<p>27</p>", "<p>21</p>", 
                                "<p>14</p>", "<p>17</p>"],
                    options_hi: ["<p>27</p>", "<p>21</p>",
                                "<p>14</p>", "<p>17</p>"],
                    solution_en: "<p>50.(a) <strong>27 bones:</strong> The human hand has 27 bones: the carpals or wrist accounts for 8; the metacarpals or palm contains 5; the remaining 14 are digital bones in fingers and thumbs.</p>",
                    solution_hi: "<p>50(a) <strong>27 हड्डियां:</strong> मानव हाथ में 27 हड्डियां होती हैं: कार्पल्स या कलाई में 8 होती हैं, मेटाकार्पल्स या हथेली में 5 होती हैं, शेष 14 डिजिटल हड्डियां हैं, जो कि उंगलियां और अंगूठा में होती हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mn>2</mn><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> at &theta; = 45&deg;</p>",
                    question_hi: "<p>51. &theta; = 45&deg; पर <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mn>2</mn><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>1</p>", "<p>0</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>1</p>", "<p>0</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>51.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mn>2</mn><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>4</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math></p>\n<p>=&nbsp; <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>0</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 0</p>",
                    solution_hi: "<p>51.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mn>2</mn><msup><mi>cos</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>4</mn></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><mi>cos</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>4</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mo>(</mo><mi>sin</mi><mi mathvariant=\"normal\">&#160;</mi><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math></p>\n<p>=&nbsp; <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup></mrow><mrow><mn>2</mn><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>)</mo></mrow><mrow><mn>4</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>0</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 0</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. What must be added to (6 + 0.6 + 0.06 + 0.006) to obtain the number 7?</p>",
                    question_hi: "<p>52. (6 + 0.6 + 0.06 + 0.006) में क्या जोड़ा जाए कि संख्या 7 प्राप्त हो जाए ?</p>",
                    options_en: ["<p>0.034</p>", "<p>0.334</p>", 
                                "<p>0.340</p>", "<p>0.343</p>"],
                    options_hi: ["<p>0.034</p>", "<p>0.334</p>",
                                "<p>0.340</p>", "<p>0.343</p>"],
                    solution_en: "<p>52.(b)&nbsp;(6 + 0.6 + 0.06 + 0.006) = 6.666<br>According to question,<br>Required number = 7 - 6.666 = 0.334</p>",
                    solution_hi: "<p>52.(b)&nbsp;(6 + 0.6 + 0.06 + 0.006) = 6.666<br>प्रश्न के अनुसार,<br>आवश्यक संख्या = 7 - 6.666 = 0.334</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Bhavani spends 68% of her income. Her income increases by 15% and her expenditure increases by 10%. How much is the increase in the percentage of saving? (Use decimal up to 2 digits)</p>",
                    question_hi: "<p>53. भवानी अपनी आय का 68% खर्च करती है। उसकी आय में 15% की वृद्धि होती है और उसके व्यय में 10% की वृद्धि होती है। उसकी बचत में कितने प्रतिशत की वृद्धि हुई? (दो दशमलव अंकों तक ज्ञात कीजिए)</p>",
                    options_en: ["<p>20.57%</p>", "<p>25.62%</p>", 
                                "<p>28.42%</p>", "<p>24.80%</p>"],
                    options_hi: ["<p>20.57%</p>", "<p>25.62%</p>",
                                "<p>28.42%</p>", "<p>24.80%</p>"],
                    solution_en: "<p>53.(b)&nbsp;Let income of Bhavani = 100<br>Ratio &rarr;&nbsp; &nbsp; &nbsp; &nbsp;income&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; expenditure&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; saving<br>Before &rarr;&nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;68&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 32<br>After &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 115&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;74.8&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 40.2<br>Percentage change in saving <br>= <math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>2</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 25.62%</p>",
                    solution_hi: "<p>53.(b) माना कि भवानी की आय = 100<br>अनुपात &rarr; &nbsp; आय&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; व्यय&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; : &nbsp; बचत<br>पहले&nbsp; &nbsp; &rarr; &nbsp; 100&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 68&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 32<br>बाद मे&nbsp; &rarr;&nbsp; &nbsp; 115&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 74.8 &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 40.2<br><strong id=\"docs-internal-guid-ac3e7083-7fff-5220-17f6-4788b2ed4431\"></strong>बचत में प्रतिशत परिवर्तन =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>40</mn><mo>.</mo><mn>2</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>32</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>.</mo><mn>2</mn></mrow><mn>32</mn></mfrac></math> &times; 100 = 25.62%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Akshara and Siddharth travel from point P to Q, a distance of 72 km at a rate of 8 km/h and 10 km/h, respectively. Siddharth reaches Q first and returns immediately and meets Akshara at R. Find the distance from P to R.</p>",
                    question_hi: "<p>54. अक्षरा और सिद्धार्थ क्रमशः 8 km/h और 10 km/h की दर से स्&zwj;थल P से Q तक 72 km की दूरी तय करते हैं। सिद्धार्थ स्&zwj;थल Q पर पहले पहुंचता है और तुरंत लौटता है और अक्षरा से स्&zwj;थल R पर मिलता है। P से R की दूरी ज्ञात कीजिए।</p>",
                    options_en: ["<p>65 km</p>", "<p>63 km</p>", 
                                "<p>64 km</p>", "<p>66 km</p>"],
                    options_hi: ["<p>65 km</p>", "<p>63 km</p>",
                                "<p>64 km</p>", "<p>66 km</p>"],
                    solution_en: "<p>54.(c) PQ = 72 km<br>They are meeting at point R, means within same time Akshara travels PR and Siddharth travels PQ + QR,<br>When time is the same, distance travelled is proportional to speed. <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PR</mi><mrow><mi>PQ</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>QR</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>PQ</mi><mo>-</mo><mi>RQ</mi></mrow><mrow><mi>PQ</mi><mo>+</mo><mi>QR</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br>&rArr; 10PQ - 10RQ = 8PQ + 8QR<br>&rArr; 2PQ = 18RQ<br>&rArr; 2 &times; 72 = 18RQ<br>&rArr; RQ = 8 km<br>So, the distance between P to R = PQ - RQ = 72 - 8 = 64 km</p>",
                    solution_hi: "<p>54.(c) PQ = 72 km<br>सिद्धार्थ और अक्षरा बिंदु R पर मिल रहे हैं, इसका मतलब है कि उसी समय अक्षरा PR दुरी तय करती है और सिद्धार्थ PQ + QR,<br>दुरी तय करता है, जब समय समान होता है, तो तय की गई दूरी गति के समानुपाती होती है। <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PR</mi><mrow><mi>PQ</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>QR</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>PQ</mi><mo>-</mo><mi>RQ</mi></mrow><mrow><mi>PQ</mi><mo>+</mo><mi>QR</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>10</mn></mfrac></math><br>&rArr; 10PQ - 10RQ = 8PQ + 8QR<br>&rArr; 2PQ = 18RQ<br>&rArr; 2 &times; 72 = 18RQ<br>&rArr; RQ = 8 km<br>तो, P से R के बीच की दूरी = PQ - RQ = 72 - 8 = 64 किमी</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The speed of a boat when travelling downstream is 33 km/h, whereas when travelling upstream it is 27 km/h. What is the speed of the boat in still water?</p>",
                    question_hi: "<p>55. धारा की दिशा में यात्रा करते समय एक नाव की गति 33 km/h है, जबकि धारा के प्रतिकूल दिशा में यात्रा करते समय यह 27 km/h है। शांत जल में नाव की गति क्या है?</p>",
                    options_en: ["<p>6 km/h</p>", "<p>60 km/h</p>", 
                                "<p>12 km/h</p>", "<p>30 km/h</p>"],
                    options_hi: ["<p>6 km/h</p>", "<p>60 km/h</p>",
                                "<p>12 km/h</p>", "<p>30 km/h</p>"],
                    solution_en: "<p>55.(d)&nbsp;x + y = 33 &hellip;(i)<br>x - y = 27 &hellip;(ii)<br>On adding (i) and (ii)<br>2x&nbsp;= 60<br>x = 30 <br>Hence, speed of boat in still water = 30 km/h</p>",
                    solution_hi: "<p>55.(d)&nbsp;x + y = 33 &hellip;(i)<br>x - y = 27 &hellip;(ii)<br>(i) और (ii) जोड़ने पर<br>2x&nbsp;= 60<br>x = 30 <br>अत: शांत जल में नाव की गति = 30 km/h</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. If 40% of A = 60% of B = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> of C, then find the ratio of A : B : C.</p>",
                    question_hi: "<p>56. यदि A का 40% = B का 60% = C का<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> है, तो A : B : C का अनुपात ज्ञात करें।</p>",
                    options_en: ["<p>15 : 10 : 18</p>", "<p>36 : 10 : 15</p>", 
                                "<p>15 : 10 : 36</p>", "<p>10 : 15 : 36</p>"],
                    options_hi: ["<p>15 : 10 : 18</p>", "<p>36 : 10 : 15</p>",
                                "<p>15 : 10 : 36</p>", "<p>10 : 15 : 36</p>"],
                    solution_en: "<p>56.(c)&nbsp;40% of A = 60% of B = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> of C<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> of B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> of C = k (Let)<br><strong>A</strong> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math>K, B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>k, C = 6k<br>A : B : C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math>K : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>k : 6k = 15 : 10 : 36</p>",
                    solution_hi: "<p>56.(c)&nbsp;A का 40% = B का 60% = C का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> <br>A का <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = B का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = C का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> = k (माना )<br><strong>A</strong> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math>K, B = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>k, C = 6k<br>A : B : C = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math>K : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math>k : 6k = 15 : 10 : 36</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. What is the value of -1 + 2 - 3 + 4 - 5 + 6 - 7 + ...+ 1000 ?</p>",
                    question_hi: "<p>57. -1 + 2 - 3 + 4 - 5 + 6 - 7 + ...+ 1000 का मान क्या है ?</p>",
                    options_en: ["<p>2000</p>", "<p>0</p>", 
                                "<p>1</p>", "<p>500</p>"],
                    options_hi: ["<p>2000</p>", "<p>0</p>",
                                "<p>1</p>", "<p>500</p>"],
                    solution_en: "<p>57.(d) <strong>Given series is</strong> <br>-1 + 2 - 3 + 4 - 5 + 6 - 7 + ...+ 1000 <br>There are 1000 term and we see that the odd and even term are minus and plus sign respectively , now the series <br>(-1 + 2) +( - 3 + 4) + ( - 5 + 6) + ...+ (-999 + 1000 )<br>We can see , sum of pairs of number is one and there are 500 terms <br>1 + 1 + 1 + 1 &hellip;&hellip;&hellip;&hellip;&hellip;&hellip;500 terms<br>So Sum = 500</p>",
                    solution_hi: "<p>57.(d) <strong>दी गई श्रृंखला</strong> <br>-1 + 2 - 3 + 4 - 5 + 6 - 7 + ...+ 1000 <br>यहाँ कुल 1000 पद हैं और हम देखते हैं कि विषम और सम पद पर क्रमशः ऋण और धन चिह्न हैं, अब श्रृंखला <br>(-1 + 2) + (- 3 + 4) + ( - 5 + 6) + ...+ (- 999 + 1000 )<br>हम देख सकते हैं, संख्याओं के युग्मों का योग एक है और 500 पद हैं<br>1 + 1 + 1 + 1 &hellip;&hellip;&hellip;&hellip;&hellip;&hellip;500 पद&nbsp;<br>इसलिए , योग = 500</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Which of the following statements is/are correct ?<br>A. A triangle can have all angles less than 60&deg;.<br>B. A triangle can have one obtuse angle.<br>C. A triangle can have two right angles.<br>D. A triangle can have two acute angles.</p>",
                    question_hi: "<p>58. निम्नलिखित में से कौन सा/से कथन सही है/हैं?<br>A. एक त्रिभुज में सभी कोण 60&deg; से कम हो सकते हैं।<br>B. एक त्रिभुज में एक अधिक कोण हो सकता है।<br>C. एक त्रिभुज में दो समकोण हो सकते हैं।<br>D. एक त्रिभुज में दो न्यून कोण हो सकते हैं।</p>",
                    options_en: ["<p>B</p>", "<p>B and D</p>", 
                                "<p>A and C</p>", "<p>A</p>"],
                    options_hi: ["<p>B</p>", "<p>B और D</p>",
                                "<p>A और C</p>", "<p>A</p>"],
                    solution_en: "<p>58.(b) B and D<br>A triangle can have two acute angles with one obtuse angle.</p>",
                    solution_hi: "<p>58.(b) B और D<br>एक त्रिभुज में एक अधिक कोण के साथ दो न्यून कोण हो सकते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A thief pursued by a policeman was 150 m ahead at the start. If the ratio of the speed of the policeman to that of the thief was 5 : 4, then how far (distance in metres) could the thief go before he is caught by the policeman?</p>",
                    question_hi: "<p>59. एक पुलिसकर्मी द्वारा पीछा किए जाने पर एक चोर शुरुआत में 150 m आगे था। यदि पुलिसकर्मी की चाल और चोर की चाल का अनुपात 5 : 4 था, तो पुलिसकर्मी द्वारा पकड़े जाने से पहले चोर कितनी दूरी (दूरी m में) तय कर चुका था?</p>",
                    options_en: ["<p>350</p>", "<p>200</p>", 
                                "<p>600</p>", "<p>400</p>"],
                    options_hi: ["<p>350</p>", "<p>200</p>",
                                "<p>600</p>", "<p>400</p>"],
                    solution_en: "<p>59.(c) <br>Time taken to catch the thief = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mo>&#160;</mo><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mi mathvariant=\"normal\">x</mi></mfrac></math><br>Distance covered by thief = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>&nbsp;&times; 4x = 600 meters</p>",
                    solution_hi: "<p>59.(c) <br>चोर को पकड़ने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mrow><mn>5</mn><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mo>&#160;</mo><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mi mathvariant=\"normal\">x</mi></mfrac></math><br>चोर द्वारा तय की गई दूरी = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mi mathvariant=\"normal\">x</mi></mfrac></math>&nbsp;&times; 4x = 600 meters</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Aneesh makes a bucket full of lemon drinks to sell at the fair. He has a bucket, small glass and large glass respectively, which can contain 20 litres, 250 ml, and 400ml of lemon drink. If Aneesh has sold 45 small glasses and 21 large glasses of lemon drinks, then how much lemon drink has been left in the bucket?</p>",
                    question_hi: "<p>60. अनीश मेले में बेचने के लिए एक पूरी बाल्टी नींबू पानी बनाता है। उसके पास एक बाल्टी, एक छोटा गिलास और एक बड़ा गिलास है, जिनमें क्रमशः 20 लीटर, 250 मी.ली., 400 मि.ली. नींबू पानी आ सकता है। यदि अनीश ने मेले में 45 छोटे गिलास और 21 बड़े गिलास नींबू पानी बेचे हैं, तो बाल्टी में कितना नींबू पानी बचा है?</p>",
                    options_en: ["<p>250 ml</p>", "<p>2 litres</p>", 
                                "<p>350 ml</p>", "<p>3000 ml</p>"],
                    options_hi: ["<p>250 मिली.</p>", "<p>2 लीटर</p>",
                                "<p>350 मिली.</p>", "<p>3000 मिली.</p>"],
                    solution_en: "<p>60.(c)&nbsp;Bucket = 20 liter<br>45 Small glass = 45 &times; 250 ml = 11250 ml<br>21 Large glass = 21 &times; 400 ml = 8400 ml<br>Remaining lemon drink = 20,000 - (11250 + 8400) = 350 ml</p>",
                    solution_hi: "<p>60.(c)&nbsp;बाल्टी = 20 लीटर<br>45 छोटे गिलास = 45 &times; 250 मिलीलीटर = 11250 मिलीलीटर<br>21 बड़े गिलास = 21 &times; 400 मिलीलीटर = 8400 मिलीलीटर<br>शेष नींबू पानी = 20,000 मिलीलीटर - (11250 + 8400) = 350 मिलीलीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A dishonest merchant sells his grocery using weights that are 12% less than the true weights and makes a profit of 10%. Find his total gain percentage.</p>",
                    question_hi: "<p>61. एक बेईमान व्यापारी अपने किराने का सामान वास्&zwj;तविक बांट से 12% कम बांट का उपयोग करके बेचता है और 10% का लाभ अर्जित करता है। उसका कुल लाभ प्रतिशत ज्ञात करें।</p>",
                    options_en: ["<p>10%</p>", "<p>20%</p>", 
                                "<p>15%</p>", "<p>25%</p>"],
                    options_hi: ["<p>10%</p>", "<p>20%</p>",
                                "<p>15%</p>", "<p>25%</p>"],
                    solution_en: "<p>61.(d)<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; - &nbsp; CP&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;SP<br>Less weight -&nbsp; &nbsp; 22&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;25<br>Due to Profit -&nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;11&nbsp;<br>----------------------------------------------<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Final&nbsp; &nbsp; &nbsp; &nbsp; - &nbsp; 20&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;25<br><strong id=\"docs-internal-guid-8a682d2f-7fff-125b-0929-2b20b7e77520\"></strong>Hence, gain % =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>61.(d)<br>अनुपात&nbsp; &nbsp; &rarr; &nbsp; क्रय मूल्य &nbsp; : &nbsp; विक्रय मूल्य&nbsp;<br>कम वजन &rarr; &nbsp; &nbsp; &nbsp; &nbsp; 22&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 25<br>लाभ के कारण&nbsp; &rarr;&nbsp; 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11&nbsp;<br>-------------------------------------------------<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;अंतिम&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp; 20&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;25<br><strong id=\"docs-internal-guid-92275b45-7fff-e902-bfab-4e7e69369422\"></strong>अत: लाभ % =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The following pie-chart shows the distribution of students at graduate level in different institutes in a town. Study the given chart and answer the question that follows. <br>Distribution of students at graduate level institutes<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602413146.png\" alt=\"rId46\" width=\"351\" height=\"210\"> <br>If the total number of students at the graduate level is 2,46,000, then how many students of institutes A and B are studying at graduate level?</p>",
                    question_hi: "<p>62. निम्नलिखित पाई-चार्ट एक शहर में विभिन्न संस्थानों में स्नातक स्तर पर छात्रों के वितरण को दर्शाता है। दिए गए चार्ट का अध्ययन कीजिए और आगे आने वाले प्रश्न का उत्तर दीजिए। <br>स्नातक स्तर के संस्थानों में छात्रों का वितरण<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602413146.png\" alt=\"rId46\" height=\"210\"> <br>यदि स्नातक स्तर पर छात्रों की कुल संख्या 2,46,000 है, तो संस्थान A और B के कितने छात्र स्नातक स्तर पर पढ़ रहे हैं?</p>",
                    options_en: ["<p>76,260</p>", "<p>74,580</p>", 
                                "<p>86,190</p>", "<p>78,370</p>"],
                    options_hi: ["<p>76,260</p>", "<p>74,580</p>",
                                "<p>86,190</p>", "<p>78,370</p>"],
                    solution_en: "<p>62.(a)<br>Students of A and B = <math display=\"inline\"><mfrac><mrow><mn>17</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>14</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 2,46,000 = 31 &times; 2,460 =76,260</p>",
                    solution_hi: "<p>62.(a)<br>A और B के छात्र = <math display=\"inline\"><mfrac><mrow><mn>17</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>14</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 2,46,000 = 31 &times; 2,460 =76,260</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Find the fourth proportion to 10, 12, and 15.</p>",
                    question_hi: "<p>63. 10, 12 और 15 का चतुर्थानुपाती ज्ञात कीजिए।</p>",
                    options_en: ["<p>16</p>", "<p>20</p>", 
                                "<p>18</p>", "<p>22</p>"],
                    options_hi: ["<p>16</p>", "<p>20</p>",
                                "<p>18</p>", "<p>22</p>"],
                    solution_en: "<p>63.(c)<br>Fourth proportion to 10, 12, and 15 = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>15</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 18</p>",
                    solution_hi: "<p>63.(c)<br>10, 12, और 15 का चौथा अनुपात = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>15</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 18</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. The total surface area of a closed cube is given as 1152 cm<sup>2</sup> . What is the length (in cm) of each side of the cube?</p>",
                    question_hi: "<p>64. एक बंद घन का संपूर्ण पृष्ठीय क्षेत्रफल 1152 cm<sup>2</sup> दिया गया है। घन की प्रत्येक भुजा की लंबाई (cm में) क्या है?</p>",
                    options_en: ["<p>9<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>4<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math></p>", 
                                "<p>8<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>8<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    options_hi: ["<p>9<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>4<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math></p>",
                                "<p>8<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>8<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    solution_en: "<p>64.(c) Total surface area of cube = 6a<sup>2</sup><br>&rArr; 1152 = 6a<sup>2</sup><br>&rArr; a<sup>2</sup> = 192<br>&rArr; a = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>64.(c) घन कि कुल का सतह क्षेत्रफल = 6a<sup>2</sup><br>&rArr; 1152 = 6a<sup>2</sup><br>&rArr; a<sup>2</sup> = 192<br>&rArr; a = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A man travelled four equal distances of 7 km at the speeds of 8 km/hr, 16 km/hr, 24 km/hr and 32 km/hr respectively. What is his average speed?</p>",
                    question_hi: "<p>65. एक आदमी ने क्रमशः 8 km/hr, 16 km/hr, 24 km/hr और 32 km/hr की चाल से 7 km की चार समान दूरियां तय की। उसकी औसत चाल कितनी है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>374</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> km/hr</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>396</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>km/hr</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>352</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> km/hr</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>384</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> km/hr</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>374</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> km/hr</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>396</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>km/hr</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>352</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> km/hr</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>384</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> km/hr</p>"],
                    solution_en: "<p>65.(d)<br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>distance</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi></mrow></mfrac></math><br>According to the question,<br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi></mrow><mrow><mfrac><mn>7</mn><mn>8</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>7</mn><mn>16</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>7</mn><mn>24</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>7</mn><mn>32</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mstyle displaystyle=\"true\"><mfrac><mrow><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow><mn>96</mn></mfrac></mstyle></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mstyle displaystyle=\"true\"><mfrac><mn>175</mn><mn>96</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>28</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>96</mn></mrow><mn>175</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>384</mn><mn>25</mn></mfrac></math> km/hr</p>",
                    solution_hi: "<p>65.(d)<br>औसत गति = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math><br>प्रश्न के अनुसार,<br>औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi></mrow><mrow><mfrac><mn>7</mn><mn>8</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>7</mn><mn>16</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>7</mn><mn>24</mn></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>7</mn><mn>32</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mstyle displaystyle=\"true\"><mfrac><mrow><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>12</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>7</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow><mn>96</mn></mfrac></mstyle></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mstyle displaystyle=\"true\"><mfrac><mn>175</mn><mn>96</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>28</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>96</mn></mrow><mn>175</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>384</mn><mn>25</mn></mfrac></math> किमी/घंटा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Manish, Nakul and Pintoo alone can complete a certain work in 21 days, 28 days and 15 days, respectively. Manish and Pintoo started the work together while Nakul joined them after 5 days and worked with them till the completion of the work. For how many days did Nakul work?</p>",
                    question_hi: "<p>66. मनीष, नकुल और पिंटू अकेले किसी काम को क्रमशः 21 दिन, 28 दिन और 15 दिन में पूरा कर सकते हैं। मनीष और पिंटू ने एक साथ काम शुरू किया जबकि नकुल ने 5 दिन के बाद उनके साथ काम करना शुरू किया और काम पूरा होने तक उनके साथ काम किया। नकुल ने कितने दिन तक काम किया?</p>",
                    options_en: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", 
                                "<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                                "<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602413251.png\" alt=\"rId47\" height=\"150\"><br>5 days work of Manish and Pintu = 5 &times;&nbsp;(20 + 28) = 240 unit<br>Remaining work = 420 - 240 = 180 unit<br>Time taken by all three to complete remaining work = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mrow><mn>20</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>15</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>28</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>63</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math> days<br>Hence, number of days Nakul worked for = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602413407.png\" alt=\"rId48\" height=\"155\"><br>मनीष और पिंटू का 5 दिन का कार्य = 5 &times;&nbsp;(20 + 28) = 240 इकाई<br>शेष कार्य = 420 - 240 = 180 इकाई<br>शेष कार्य को पूरा करने में तीनों द्वारा लिया गया समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mrow><mn>20</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>15</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>28</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>63</mn></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math>&nbsp;दिन<br>अतः, नकुल ने जितने दिन काम किया = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. 40 litres of milk are kept in a container. 4 litres of milk were removed from this container and replaced with water. This procedure was performed two more times. How much milk does the container now hold?</p>",
                    question_hi: "<p>67. किसी पात्र में 40 लीटर दूध रखा है। इस पात्र से 4 लीटर दूध निकाल लिया गया और उतना ही पानी उसमें मिला दिया गया। यह प्रक्रिया दो बार और की गई। पात्र में अब कितना दूध है?</p>",
                    options_en: ["<p>30 litres</p>", "<p>34.23 litres</p>", 
                                "<p>29.16 litres</p>", "<p>32 litres</p>"],
                    options_hi: ["<p>30 लीटर</p>", "<p>34.23 लीटर</p>",
                                "<p>29.16 लीटर</p>", "<p>32 लीटर</p>"],
                    solution_en: "<p>67.(c)<br>Left quantity of pure milk = Initial quantity (1 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>quantity</mi><mi mathvariant=\"normal\">&#160;</mi><mi>taken</mi><mi mathvariant=\"normal\">&#160;</mi><mi>out</mi></mrow><mrow><mi>capacity</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>vessel</mi></mrow></mfrac></math>)<sup>no. of process</sup><br>Left quantity of pure milk = 40 &times; (1 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>40</mn></mfrac></math>)<sup>3</sup> = 40 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>729</mn><mn>1000</mn></mfrac></math> = 29.16 litres.</p>",
                    solution_hi: "<p>67.(c)<br>शुद्ध दूध की बची मात्रा = प्रारंभिक मात्रा (1 - <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>&#2344;&#2367;&#2325;&#2366;&#2354;&#2368;</mi><mi>&#160;</mi><mi>&#2327;&#2312;</mi><mi>&#160;</mi><mi>&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</mi></mrow><mrow><mi>&#2346;&#2366;&#2340;&#2381;&#2352;</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mi>&#160;</mi><mi>&#2325;&#2369;&#2354;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2350;&#2340;&#2366;</mi></mrow></mfrac></math>)<sup>प्रक्रिया की संख्या</sup><br>शुद्ध दूध की बची मात्रा = 40 &times; (1 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>40</mn></mfrac></math>)<sup>3</sup> = 40 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>729</mn><mn>1000</mn></mfrac></math> = 29.16 लीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A policeman is chasing a thief at a speed of 12 km/h, and the thief is running at a speed of 8 km/h. If the policeman started 30 minutes late, find the time taken by the policeman to catch the thief.</p>",
                    question_hi: "<p>68. एक पुलिसकर्मी 12 km/h की चाल से एक चोर का पीछा कर रहा है, और चोर 8 km/h की चाल से भाग रहा है। यदि पुलिसकर्मी 30 मिनट देरी से पीछा करना शुरू करता है, तो चोर को पकड़ने में पुलिसकर्मी द्वारा लिया गया समय ज्ञात कीजिए।</p>",
                    options_en: ["<p>60 minutes</p>", "<p>120 minutes</p>", 
                                "<p>90 minutes</p>", "<p>100 minutes</p>"],
                    options_hi: ["<p>60 मिनट</p>", "<p>120 मिनट</p>",
                                "<p>90 मिनट</p>", "<p>100 मिनट</p>"],
                    solution_en: "<p>68.(a)&nbsp;Distance covered by thief in 30 min. = 4 km<br>Relative speed = 12 - 8 = 4 km/h<br>Time taken by the policeman to cover 4 km = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 1 hrs or 60 min.</p>",
                    solution_hi: "<p>68.(a)&nbsp;चोर द्वारा 30 मिनट में तय की गई दूरी = 4 किमी<br>सापेक्ष गति = 12 - 8 = 4 किमी/घंटा<br>पुलिसकर्मी को 4 किमी की दूरी तय करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 1 घंटा या 60 मिनट</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The simple interest on a certain sum of money at rate of interest 6% per annum for 2 years is Rs. 960. What is the compound interest (compounding annually) on the same sum for the same period and at the same rate of interest ?</p>",
                    question_hi: "<p>69. एक निश्चित धनराशि पर 6% वार्षिक की ब्याज दर पर, 2 वर्षों के लिए साधारण ब्याज 960 रुपए है। समान अवधि और समान ब्याज दर पर समान धनराशि पर चक्रवृद्धि ब्याज (वार्षिक रूप से संयोजित) कितना होगा?</p>",
                    options_en: ["<p>Rs. 985.4</p>", "<p>Rs. 988.8</p>", 
                                "<p>Rs.1122.2</p>", "<p>Rs.1025.1</p>"],
                    options_hi: ["<p>985.4 रुपए</p>", "<p>988.8 रुपए</p>",
                                "<p>1122.2 रुपए</p>", "<p>1025.1 रुपए</p>"],
                    solution_en: "<p>69.(b)&nbsp;Let principal be 100%<br>6% &times; 2 = 12% ------------- ₹960<br>100% ------------- <math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 100 = ₹8,000<br>Successive rate for 2 years = 6 + 6 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> = 12.36%<br>CI on the same sum for the same period at the same rate = 8000 &times; 12.36% = ₹ 988.8.</p>",
                    solution_hi: "<p>69.(b)&nbsp;मान लीजिए मूलधन 100% है<br>6% &times; 2 = 12% ------------- ₹960<br>100% ------------- <math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 100 = ₹8,000<br>2 वर्षों के लिए क्रमिक दर = 6 + 6 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> = 12.36%<br>समान राशि पर समान अवधि के लिए समान दर पर चक्रवृद्धि ब्याज = 8000 &times; 12.36% = ₹ 988.8.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Akhil bought two articles at ₹4,500 each. He sold one article at a 12% gain but had to sell the second article at a loss. If he had suffered a loss of ₹395 on the whole transaction, then what was the selling price of the second article?</p>",
                    question_hi: "<p>70. अखिल ने ₹4,500 प्रत्&zwj;येक की दर से दो वस्तुएँ खरीदीं। उसने एक वस्तु को 12% लाभ पर बेचा लेकिन दूसरी वस्तु को हानि पर बेचना पड़ा। यदि उसे पूरे लेन-देन पर ₹395 की हानि हुई, तो दूसरी वस्तु का विक्रय मूल्य कितना था?</p>",
                    options_en: ["<p>₹ 3,500</p>", "<p>₹ 3,595</p>", 
                                "<p>₹ 3,565</p>", "<p>₹ 3,550</p>"],
                    options_hi: ["<p>₹ 3,500</p>", "<p>₹ 3,595</p>",
                                "<p>₹ 3,565</p>", "<p>₹ 3,550</p>"],
                    solution_en: "<p>70.(c) Total CP = 4500 + 4500 = ₹9000<br>SP<sub>1</sub> = 4500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>112</mn><mn>100</mn></mfrac></math> = ₹5040<br>According to question,<br>Net selling price = 9000 - 395 = ₹8605<br>SP<span style=\"font-size: 11.6667px;\">2</span> = 8605 - 5040 = ₹3565</p>",
                    solution_hi: "<p>70.(c)&nbsp; कुल लागत मूल्य = 4500 + 4500 = ₹9000<br>&nbsp;विक्रय मूल्य<sub>1</sub> = &nbsp;4500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>112</mn><mn>100</mn></mfrac></math> = ₹5040<br>प्रश्न के अनुसार,<br>शुद्ध विक्रय मूल्य = 9000 - 395 = ₹8605<br>विक्रय मूल्य<sub>2</sub> = 8605 - 5040 = ₹3565</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Ramnarayan\'s salary was reduced by 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% and then, the reduced salary was increased by 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>%. By what percentage (rounded off to two decimal places) is his new salary more/less as compared to his original salary?</p>",
                    question_hi: "<p>71. रामनारायण के वेतन में 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% की कमी की गई, और फिर, कम किए गए वेतन में 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math>% की वृद्धि की गई। उसका नया वेतन उसके मूल वेतन की तुलना में कितने प्रतिशत (दशमलव के दो स्थानों तक पूर्णांकित) अधिक/कम है?</p>",
                    options_en: ["<p>3.03% less</p>", "<p>4.04% more</p>", 
                                "<p>4.04% less</p>", "<p>3.03% more</p>"],
                    options_hi: ["<p>3.03% कम</p>", "<p>4.04% अधिक</p>",
                                "<p>4.04% कम</p>", "<p>3.03% अधिक</p>"],
                    solution_en: "<p>71.(b) <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>5</mn></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>500</mn></mfrac></math><br>Ratio &nbsp; &nbsp; &rarr;&nbsp; &nbsp;Initial&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Final<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;32&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;31<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;500&nbsp; &nbsp; :&nbsp; &nbsp; 537&nbsp;&nbsp;&nbsp;&nbsp;<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;---------------------------<br>Net change &rarr; 16000 : 16647<br><strong id=\"docs-internal-guid-54133070-7fff-d3b2-c3aa-035a6b7e8d3b\"></strong>Required % =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>647</mn></mrow><mrow><mn>16000</mn></mrow></mfrac></math> &times; 100 = 4.04% increase</p>",
                    solution_hi: "<p>71.(b) <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>5</mn></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>500</mn></mfrac></math><br>अनुपात &rarr; आरंभिक&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; अंतिम<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 32&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 31<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 500&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 537 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ----------------------------<br>शुद्ध परिवर्तन &rarr;&nbsp;16000 : 16647<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>647</mn></mrow><mrow><mn>16000</mn></mrow></mfrac></math> &times; 100 = 4.04% वृद्धि</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The given table shows the percentage of marks obtained by three students in three different subjects in an institute. (maximum marks are given in brackets)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602413563.png\" alt=\"rId49\" height=\"121\"> <br>If in order to pass the exam, a minimum of 105 marks are needed in History, how many students pass in the exam?</p>",
                    question_hi: "<p>72. दी गई तालिका में एक संस्थान में तीन अलग-अलग विषयों में तीन विद्यार्थियों द्वारा प्राप्त अंकों का प्रतिशत दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744602413681.png\" alt=\"rId50\" height=\"121\"> <br>यदि परीक्षा उत्तीर्ण करने के लिए इतिहास में न्यूनतम 105 अंक चाहिए, तो कितने विद्यार्थी परीक्षा में उत्तीर्ण हुए हैं?</p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>0</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>0</p>", "<p>2</p>"],
                    solution_en: "<p>72.(b) According to the question,<br>Passing marks of Ram in History = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 94.5<br>Passing marks of Mohan in History = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 89.25<br>Passing marks of Shyam in History = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 131.25<br>Hence, only 1 student passed the examination.</p>",
                    solution_hi: "<p>72.(b) प्रश्न के अनुसार,<br>इतिहास विषय में राम के उत्तीर्ण अंक = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 94.5<br>इतिहास विषय में मोहन के उत्तीर्ण अंक = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 89.25<br>इतिहास विषय में श्याम के उत्तीर्ण अंक = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 131.25<br>अतः, केवल 1 छात्र परीक्षा में उत्तीर्ण हुआ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A certain loan was returned in two equal half yearly installments each of Rs 6,760. If the rate of interest was 8% p.a., compounded yearly, how much was the interest paid on the loan?</p>",
                    question_hi: "<p>73. एक निश्चित ऋण को 6,760 रुपये की दो बराबर अर्धवार्षिक किस्तों में वापस किया गया | यदि वार्षिक चक्रवृद्धि ब्याज की दर 8% प्रति वर्ष है, तो ऋण पर कितने ब्याज का भुगतान किया गया ?</p>",
                    options_en: ["<p>₹ 750</p>", "<p>₹ 810</p>", 
                                "<p>₹ 790</p>", "<p>₹ 770</p>"],
                    options_hi: ["<p>750 रु</p>", "<p>810 रु</p>",
                                "<p>790 रु</p>", "<p>770 रु</p>"],
                    solution_en: "<p>73.(d) Effective Rate = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> % = 4%<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Principal&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Installment<br>1st yr &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;26 ) <strong>&times; 26</strong> <br>2nd yr &rarr;&nbsp; &nbsp; &nbsp; 25<sup>2</sup>&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;26<sup>2</sup><br>-------------------------------------------------<br>Total &rarr;&nbsp; &nbsp; &nbsp; &nbsp; 1275&nbsp; &nbsp; :&nbsp; &nbsp; 1352<br>According to the question ,<br>Each installment (676 units) = 6760 <br>Interest paid (77 units) = 770 Rs.</p>",
                    solution_hi: "<p>73.(d) प्रभावी दर =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>2</mn></mfrac></math> % = 4%<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;मूलधन&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; किस्त <br>&nbsp;पहला वर्ष &rarr;&nbsp; 25&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 26 ) <strong>&times; 26</strong> <br>दूसरा वर्ष &rarr;&nbsp; 25<sup>2</sup>&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 26<sup>2</sup><br>--------------------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; कुल &rarr; 1275&nbsp; &nbsp; :&nbsp; &nbsp; 1352<br>प्रश्न के अनुसार ,<br>प्रत्येक किस्त (676 इकाई) = 6760 <br>कुल ब्याज (77 इकाई) = 770 Rs.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A solid metallic sphere of radius 15 cm is melted and recast into a cone having diameter of the base as 15 cm. What is the height of the cone?</p>",
                    question_hi: "<p>74. 15 cm त्रिज्या वाले एक ठोस धातु के गोले को पिघलाकर 15 cm आधार व्यास वाले एक शंकु में ढाला जाता है। शंकु की ऊंचाई कितनी है?</p>",
                    options_en: ["<p>212 cm</p>", "<p>186 cm</p>", 
                                "<p>284 cm</p>", "<p>240 cm</p>"],
                    options_hi: ["<p>212 cm</p>", "<p>186 cm</p>",
                                "<p>284 cm</p>", "<p>240 cm</p>"],
                    solution_en: "<p>74.(d)&nbsp;Let the height of cone be &lsquo;h&rsquo;<br>ATQ,<br>Volume of sphere = Volume of cone <br>&rArr;<strong id=\"docs-internal-guid-ea72a015-7fff-f2f0-ca55-a8b699164d89\"> </strong><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi; &times; 15<sup>3</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>2</mn></mfrac></math> &times; h<br>&rArr; <strong id=\"docs-internal-guid-ea72a015-7fff-f2f0-ca55-a8b699164d89\"></strong>4500 = <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>h<br>&rArr; <strong id=\"docs-internal-guid-ea72a015-7fff-f2f0-ca55-a8b699164d89\"></strong>h = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4500</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>75</mn></mfrac></math> = 240 cm</p>",
                    solution_hi: "<p>74.(d)&nbsp;माना शंकु की ऊँचाई \'h\' है<br>प्रश्न के अनुसार,<br>गोले का आयतन = शंकु का आयतन <br>&rArr;<strong id=\"docs-internal-guid-ea72a015-7fff-f2f0-ca55-a8b699164d89\"> </strong><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi; &times; 15<sup>3</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&pi; &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>2</mn></mfrac></math> &times; h<br>&rArr; <strong id=\"docs-internal-guid-ea72a015-7fff-f2f0-ca55-a8b699164d89\"></strong>4500 = <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>h<br>&rArr; <strong id=\"docs-internal-guid-ea72a015-7fff-f2f0-ca55-a8b699164d89\"></strong>h = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4500</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>75</mn></mfrac></math> = 240 सेमी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. If A is an acute angle, then <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cosA</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow></mfrac></msqrt></math> is equal to :</p>",
                    question_hi: "<p>75. यदि A एक न्यून कोण है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cosA</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow></mfrac></msqrt></math>का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>2 cosecA</p>", "<p>2 cosA</p>", 
                                "<p>2 secA</p>", "<p>2 sinA</p>"],
                    options_hi: ["<p>2 cosecA</p>", "<p>2 cosA</p>",
                                "<p>2 secA</p>", "<p>2 sinA</p>"],
                    solution_en: "<p>75.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cosA</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow></mfrac></msqrt></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mi>cosA</mi></mrow></mfrac><mo>&#160;</mo><mo>&#215;</mo><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi></mrow></mfrac></msqrt></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></msqrt></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></msqrt></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math> = 2cosecA</p>",
                    solution_hi: "<p>75.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>cosA</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow></mfrac></msqrt></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mi>cosA</mi></mrow></mfrac><mo>&#160;</mo><mo>&#215;</mo><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi></mrow></mfrac></msqrt></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></msqrt></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msup><mrow><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mo>)</mo></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></msqrt></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cosA</mi></mrow><mi>sinA</mi></mfrac></math> = 2cosecA</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Choose the option that best describes the meaning of the idioms and phrases underlined in the given sentence. <br>You can best identify the <span style=\"text-decoration: underline;\">fair weather friends</span> at the time of adversity.</p>",
                    question_hi: "<p>76. Choose the option that best describes the meaning of the idioms and phrases underlined in the given sentence. <br>You can best identify the <span style=\"text-decoration: underline;\">fair weather friends</span> at the time of adversity.</p>",
                    options_en: ["<p>Friend only at the time of prosperity</p>", "<p>Best friends</p>", 
                                "<p>Friends who come in good weather conditions</p>", "<p>Friends who bring good rain</p>"],
                    options_hi: ["<p>Friend only at the time of prosperity</p>", "<p>Best friends</p>",
                                "<p>Friends who come in good weather conditions</p>", "<p>Friends who bring good rain</p>"],
                    solution_en: "<p>76.(a) <strong>Fair weather friend-</strong> friend only at the time of prosperity.</p>",
                    solution_hi: "<p>76.(a) <strong>Fair weather friend-</strong> friend only at the time of prosperity./केवल समृद्धि के समय मित्र/सुख के साथी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options.<br>Mrs. Rani / works for / a international / school.</p>",
                    question_hi: "<p>77. The following sentence has been divided into parts. One of them contains an error. Select the part that contains the error from the given options.<br>Mrs. Rani / works for / a international / school.</p>",
                    options_en: ["<p>school</p>", "<p>Mrs. Rani</p>", 
                                "<p>works for</p>", "<p>a international</p>"],
                    options_hi: ["<p>school</p>", "<p>Mrs. Rani</p>",
                                "<p>works for</p>", "<p>a international</p>"],
                    solution_en: "<p>77.(d) a international<br>Article &lsquo;an&rsquo; is used before the words beginning with a vowel sound (an apple, an umbrella, an American). Similarly, in the given sentence, the word &lsquo;international&rsquo; begins with a vowel sound. Hence, &lsquo;an international&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(d) a international<br>Vowel sound (an apple, an umbrella, an American) से प्रारम्भ होने वाले शब्दों से पहले Article &lsquo;an&rsquo; का प्रयोग किया जाता है। इसी तरह, दिए गए sentence में, word &lsquo;international&rsquo; स्वर ध्वनि (vowel sound) से प्रारम्भ होता है। इसलिए, &lsquo;an international&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the option that can be used as a one-word substitute for the given group of words.<br>The list of subjects to be discussed at a meeting</p>",
                    question_hi: "<p>78. Select the option that can be used as a one-word substitute for the given group of words.<br>The list of subjects to be discussed at a meeting</p>",
                    options_en: ["<p>Minutes</p>", "<p>Objectives</p>", 
                                "<p>Agenda</p>", "<p>Menu</p>"],
                    options_hi: ["<p>Minutes</p>", "<p>Objectives</p>",
                                "<p>Agenda</p>", "<p>Menu</p>"],
                    solution_en: "<p>78.(c) <strong>Agenda-</strong> the list of subjects to be discussed at a meeting.<br><strong>Minutes-</strong> a period equal to 60 seconds.<br><strong>Objectives-</strong> something you plan to achieve.<br><strong>Menu-</strong> a list of dishes available in a restaurant.</p>",
                    solution_hi: "<p>78.(c) <strong>Agenda</strong> (कार्यसूची)- the list of subjects to be discussed at a meeting.<br><strong>Minutes</strong> (मिनट)- a period equal to 60 seconds.<br><strong>Objectives</strong> (लक्ष्य)- something you plan to achieve.<br><strong>Menu</strong> (व्यंजन सूची)- a list of dishes available in a restaurant.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>The dealer said, \"Either make your purchases or walk out of my shop.\"</p>",
                    question_hi: "<p>79. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>The dealer said, \"Either make your purchases or walk out of my shop.\"</p>",
                    options_en: ["<p>The dealer told the customer that he would either make his purchases or walk out of his shop.</p>", "<p>The dealer ordered the customer to make his purchases and walk out of his shop.</p>", 
                                "<p>The dealer told the customer that he should either make his purchases, or walk out of his shop.</p>", "<p>The dealer requested the customer to make his purchases or walk out of his shop.</p>"],
                    options_hi: ["<p>The dealer told the customer that he would either make his purchases or walk out of his shop.</p>", "<p>The dealer ordered the customer to make his purchases and walk out of his shop.</p>",
                                "<p>The dealer told the customer that he should either make his purchases, or walk out of his shop.</p>", "<p>The dealer requested the customer to make his purchases or walk out of his shop.</p>"],
                    solution_en: "<p>79.(c)<strong> The dealer told the customer that he should either make his purchases, or walk out of his shop. (correct)</strong><br>a. The dealer told the customer that he <strong>would</strong> either make his purchases or walk out of his shop. (Incorrect word)<br>b. The dealer <strong>ordered</strong> the customer to make his purchases and walk out of his shop. (Incorrect word)<br>d. The dealer <strong>requested</strong> the customer to make his purchases or walk\' out of his shop. (Incorrect tense)</p>",
                    solution_hi: "<p>79.(c) <strong>The dealer told the customer that he should either make his purchases, or walk out of his shop. (सही वाक्य)</strong><br>a. The dealer told the customer that he <strong>would</strong> either make his purchases&nbsp;or walk out of his shop. (गलत word)<br>b. The dealer <strong>ordered</strong> the customer to make his purchases and walk out of his shop. (गलत word)<br>d. The dealer <strong>requested</strong> the customer to make his purchases or walk\' out of his shop. (गलत tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate synonym of the given word. <br>Affirm</p>",
                    question_hi: "<p>80. Select the most appropriate synonym of the given word. <br>Affirm</p>",
                    options_en: ["<p>Correlate</p>", "<p>Confirm</p>", 
                                "<p>Convey</p>", "<p>Deliver</p>"],
                    options_hi: ["<p>Correlate</p>", "<p>Confirm</p>",
                                "<p>Convey</p>", "<p>Deliver</p>"],
                    solution_en: "<p>80.(b) <strong>Confirm-</strong> to establish the truth or accuracy of something.<br><strong>Affirm-</strong> to state something as true.<br><strong>Correlate-</strong> to have a mutual relationship or connection.<br><strong>Convey-</strong> to communicate or make known.<br><strong>Deliver-</strong> to bring and hand over to a recipient.</p>",
                    solution_hi: "<p>80.(b) <strong>Confirm</strong> (पुष्टि करना) - to establish the truth or accuracy of something.<br><strong>Affirm</strong> (पुष्टि करना) - to state something as true.<br><strong>Correlate</strong> (परस्पर सम्बन्ध होना) - to have a mutual relationship or connection.<br><strong>Convey</strong> (संप्रेषित करना) - to communicate or make known.<br><strong>Deliver</strong> (पहुँचाना) - to bring and hand over to a recipient.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: In 1908 she went back to London which she felt to be her spiritual home.<br>Q: Katherine Mansfield was sent to Queen\'s College School, London.<br>R: She remained there for four years.<br>S: Soon after returning to New Zealand, she became dissatisfied.</p>",
                    question_hi: "<p>81. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P: In 1908 she went back to London which she felt to be her spiritual home.<br>Q: Katherine Mansfield was sent to Queen\'s College School, London.<br>R: She remained there for four years.<br>S: Soon after returning to New Zealand, she became dissatisfied.</p>",
                    options_en: ["<p>PQRS</p>", "<p>PSRQ</p>", 
                                "<p>QRSP</p>", "<p>RSQP</p>"],
                    options_hi: ["<p>PQRS</p>", "<p>PSRQ</p>",
                                "<p>QRSP</p>", "<p>RSQP</p>"],
                    solution_en: "<p>81.(c) QRSP<br>Sentence Q is the starting line of the parajumble because it tells the subject of the parajumble that is about Katherine Mansfield. Then, Sentence R tells about her years she spent there. So we get to know that R follows Q, Going through the options, only option C shows the link of R follows Q of the pattern of the parajumble that shows Option C is the correct answer.</p>",
                    solution_hi: "<p>81.(c) QRSP<br>वाक्य Q parajumble की शुरुआती लाइन है क्योंकि यह parajumble के विषय को बताता है जो Katherine Mansfield के बारे में है। फिर, वाक्य R उसके द्वारा वहां बिताए गये वर्षों के बारे में बताता है। इससे हमें पता चलता है कि Q के बाद R आएगा। विकल्पों के माध्यम से जाने पर केवल विकल्प C सही क्रम में है। इसलिए विकल्प C सही उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Identify the incorrectly spelt word and select its correct spelling.<br>Wether we want to admit it or not, we all wish everyone would like us.</p>",
                    question_hi: "<p>82. Identify the incorrectly spelt word and select its correct spelling.<br>Wether we want to admit it or not, we all wish everyone would like us.</p>",
                    options_en: ["<p>whether</p>", "<p>addmit</p>", 
                                "<p>whither</p>", "<p>admitt</p>"],
                    options_hi: ["<p>whether</p>", "<p>addmit</p>",
                                "<p>whither</p>", "<p>admitt</p>"],
                    solution_en: "<p>82.(a) Wether<br>&lsquo;Whether&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>82.(a) Wether<br>&lsquo;Whether&rsquo; सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate meaning of the underlined idiom. <br>Prakash could not <span style=\"text-decoration: underline;\">make up his mind</span> about what to do with the project.</p>",
                    question_hi: "<p>83. Select the most appropriate meaning of the underlined idiom. <br>Prakash could not <span style=\"text-decoration: underline;\">make up his mind</span> about what to do with the project.</p>",
                    options_en: ["<p>Abide</p>", "<p>Decide</p>", 
                                "<p>Think</p>", "<p>Abandon</p>"],
                    options_hi: ["<p>Abide</p>", "<p>Decide</p>",
                                "<p>Think</p>", "<p>Abandon</p>"],
                    solution_en: "<p>83.(b) <strong>Make up his mind-</strong> decide.</p>",
                    solution_hi: "<p>83.(b) <strong>Make up his mind-</strong> decide./निश्चय करना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate option that can substitute the underlined words in the given sentence.<br>My son will <span style=\"text-decoration: underline;\">carry out my business</span> in my absence.</p>",
                    question_hi: "<p>84. Select the most appropriate option that can substitute the underlined words in the given sentence.<br>My son will <span style=\"text-decoration: underline;\">carry out my business</span> in my absence.</p>",
                    options_en: ["<p>carry for</p>", "<p>carry on</p>", 
                                "<p>carry in</p>", "<p>carry at</p>"],
                    options_hi: ["<p>carry for</p>", "<p>carry on</p>",
                                "<p>carry in</p>", "<p>carry at</p>"],
                    solution_en: "<p>84.(b) carry on<br>&lsquo;Carry on&rsquo; means to continue a task or an activity. The given sentence states that my son will carry on my business in my absence. Hence, &lsquo;carry on&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>84.(b) carry on<br>&lsquo;Carry on&rsquo; का अर्थ है किसी task या activity को जारी रखना। दिए गए sentence में कहा गया है कि मेरा बेटा मेरी अनुपस्थिति (absence) में मेरे व्यवसाय (business) को आगे बढ़ाएगा। इसलिए, &lsquo;carry on&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>One should avoid honking the horn unnecessarily.</p>",
                    question_hi: "<p>85. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>One should avoid honking the horn unnecessarily.</p>",
                    options_en: ["<p>Unnecessary honking of horn ought to be avoided.</p>", "<p>Unnecessary honking of horn can be avoided.</p>", 
                                "<p>Unnecessary honking of horn should be avoided.</p>", "<p>Unnecessary honking of horn must be avoided.</p>"],
                    options_hi: ["<p>Unnecessary honking of horn ought to be avoided.</p>", "<p>Unnecessary honking of horn can be avoided.</p>",
                                "<p>Unnecessary honking of horn should be avoided.</p>", "<p>Unnecessary honking of horn must be avoided.</p>"],
                    solution_en: "<p>85.(c) <strong>Unnecessary honking of horn should be avoided. (Correct)</strong><br>a. Unnecessary honking of horn <strong>ought</strong> to be avoided.(Incorrect word)<br>b. Unnecessary honking of horn <strong>can</strong> be avoided.(Incorrect word)<br>d. Unnecessary honking of horn <strong>must</strong> be avoided.(incorrect word)</p>",
                    solution_hi: "<p>85.(c) <strong>Unnecessary honking of horn should be avoided. (सही वाक्य )</strong><br>a. Unnecessary honking of horn <strong>ought</strong> to be avoided.(गलत word)<br>b. Unnecessary honking of horn <strong>can</strong> be avoided.(गलत word)<br>d. Unnecessary honking of horn <strong>must</strong> be avoided.(गलत word)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that can be used as a one-word substitute for the given group of words. <br>Failing to give proper care and attention</p>",
                    question_hi: "<p>86. Select the option that can be used as a one-word substitute for the given group of words. <br>Failing to give proper care and attention</p>",
                    options_en: ["<p>Lax</p>", "<p>Cautious</p>", 
                                "<p>Wild</p>", "<p>Provident</p>"],
                    options_hi: ["<p>Lax</p>", "<p>Cautious</p>",
                                "<p>Wild</p>", "<p>Provident</p>"],
                    solution_en: "<p>86.(c) <strong>Clumsy-</strong> awkward in movement or manner.<br><strong>Alert-</strong> quick to notice any unusual and potentially dangerous or difficult circumstances.<br><strong>Imbalanced-</strong> a situation in which two or more things are not equal in size, power, importance, etc.<br><strong>Clownish-</strong> someone who entertains people by wearing funny clothes with a painted face, and making people laugh.</p>",
                    solution_hi: "<p>86.(c) <strong>Clumsy</strong> (अनाड़ी/बेढंगा)- awkward in movement or manner.<br><strong>Alert</strong> (सतर्क)- quick to notice any unusual and potentially dangerous or difficult circumstances.<br><strong>Imbalanced</strong> (असंतुलित)- a situation in which two or more things are not equal in size, power, importance, etc.<br><strong>Clownish</strong> (मसख़रा)- someone who entertains people by wearing funny clothes with a painted face, and making people laugh.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate synonym of the given word. <br>Peculiar</p>",
                    question_hi: "<p>87. Select the most appropriate synonym of the given word. <br>Peculiar</p>",
                    options_en: ["<p>Revolutionary</p>", "<p>Frequent</p>", 
                                "<p>Strange</p>", "<p>Usual</p>"],
                    options_hi: ["<p>Revolutionary</p>", "<p>Frequent</p>",
                                "<p>Strange</p>", "<p>Usual</p>"],
                    solution_en: "<p>87.(c) <strong>Strange-</strong> unusual or unexpected.<br><strong>Peculiar-</strong> unusual or strange in a way that\'s different from what is normal or expected. <br><strong>Revolutionary-</strong> involving or causing complete or dramatic change.<br><strong>Frequent-</strong> occurring or done many times at short intervals.<br><strong>Usual-</strong> not different or special.</p>",
                    solution_hi: "<p>87.(c) <strong>Strange</strong> (अजीब) - unusual or unexpected.<br><strong>Peculiar</strong> (विचित्र) - unusual or strange in a way that\'s different from what is normal or expected. <br><strong>Revolutionary</strong> (क्रांतिकारी) - involving or causing complete or dramatic change.<br><strong>Frequent</strong> (बारंबार) - occurring or done many times at short intervals.<br><strong>Usual</strong> (सामान्य) - not different or special.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate ANTONYM of the underlined word in the following sentence.<br>You should never visit her without her <span style=\"text-decoration: underline;\">consent.</span></p>",
                    question_hi: "<p>88. Select the most appropriate ANTONYM of the underlined word in the following sentence.<br>You should never visit her without her <span style=\"text-decoration: underline;\">consent.</span></p>",
                    options_en: ["<p>Knowledge</p>", "<p>Approval</p>", 
                                "<p>Refusal</p>", "<p>Permission</p>"],
                    options_hi: ["<p>Knowledge</p>", "<p>Approval</p>",
                                "<p>Refusal</p>", "<p>Permission</p>"],
                    solution_en: "<p>88.(b) <strong>Refusal-</strong> the act of saying that you will not do or accept something.<br><strong>Consent-</strong> permission for something to happen.<br><strong>Knowledge-</strong> information and understanding that you have in your mind.<br><strong>Approval-</strong> the feeling that something is good or acceptable.</p>",
                    solution_hi: "<p>88.(b) <strong>Refusal</strong> (इंकार) - the act of saying that you will not do or accept something.<br><strong>Consent</strong> (सहमति) - permission for something to happen.<br><strong>Knowledge</strong> (ज्ञान) - information and understanding that you have in your mind.<br><strong>Approval</strong> (स्वीकृति) - the feeling that something is good or acceptable.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option to fill in the blank. <br>She could easily eat the _________ biryani by herself.</p>",
                    question_hi: "<p>89. Select the most appropriate option to fill in the blank. <br>She could easily eat the _________ biryani by herself.</p>",
                    options_en: ["<p>hole</p>", "<p>haul</p>", 
                                "<p>whole</p>", "<p>hall</p>"],
                    options_hi: ["<p>hole</p>", "<p>haul</p>",
                                "<p>whole</p>", "<p>hall</p>"],
                    solution_en: "<p>89.(c) whole<br>&lsquo;Whole&rsquo; means all of something. The given sentence states that she could easily eat the whole biryani by herself. Hence, &lsquo;whole&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(c) whole<br>&lsquo;Whole&rsquo; का अर्थ है किसी चीज़ का पूरा हिस्सा। दिए गए sentence में कहा गया है कि वह पूरी बिरयानी (biryani) अकेले ही आसानी से खा सकती है। अतः, &lsquo;whole&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate ANTONYM of the given word.<br>Sacred</p>",
                    question_hi: "<p>90. Select the most appropriate ANTONYM of the given word.<br>Sacred</p>",
                    options_en: ["<p>Profane</p>", "<p>Grave</p>", 
                                "<p>Narrow</p>", "<p>Holy</p>"],
                    options_hi: ["<p>Profane</p>", "<p>Grave</p>",
                                "<p>Narrow</p>", "<p>Holy</p>"],
                    solution_en: "<p>90.(a) <strong>Profane-</strong> showing disrespect toward sacred things.<br><strong>Sacred-</strong> regarded with great respect and reverence.<br><strong>Grave-</strong> serious or solemn in manner.<br><strong>Narrow-</strong> limited in width or scope.<br><strong>Holy-</strong> dedicated to religious or spiritual purposes.</p>",
                    solution_hi: "<p>90.(a) <strong>Profane</strong> (अपवित्र) - showing disrespect toward sacred things.<br><strong>Sacred</strong> (पवित्र) - regarded with great respect and reverence.<br><strong>Grave</strong> (गंभीर) - serious or solemn in manner.<br><strong>Narrow</strong> (संकीर्ण) - limited in width or scope.<br><strong>Holy</strong> (पवित्र) - dedicated to religious or spiritual purposes.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. <strong>Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans. <br>Select the most appropriate option to fill in the blank no. 91</p>",
                    question_hi: "<p>91. <strong>Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans. <br>Select the most appropriate option to fill in the blank no. 91</p>",
                    options_en: ["<p>impossible</p>", "<p>absurd</p>", 
                                "<p>hopeless</p>", "<p>useless</p>"],
                    options_hi: ["<p>impossible</p>", "<p>absurd</p>",
                                "<p>hopeless</p>", "<p>useless</p>"],
                    solution_en: "<p>91.(a) <strong>Impossible</strong> - not able to occur, exist, or be done.<br><strong>Absurd</strong> - wildly unreasonable, illogical, or inappropriate. <br><strong>Hopeless</strong> - feeling or causing despair.<br><strong>Useless</strong> - not fulfilling or not expected to achieve the intended purpose or desired outcome.</p>",
                    solution_hi: "<p>91.(a) <strong>Impossible</strong> - not able to occur, exist, or be done.<br><strong>Absurd</strong> - अतार्किक ।<br><strong>Hopeless</strong> - निराशाजनक।<br><strong>Useless</strong> - अपेक्षित उद्देश्य या वांछित परिणाम न पूरा कर पाने या हो पाने की स्थिति ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. <strong>Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans. <br>Select the most appropriate option to fill in the blank no. 92</p>",
                    question_hi: "<p>92. <strong>Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans. <br>Select the most appropriate option to fill in the blank no. 92</p>",
                    options_en: ["<p>one</p>", "<p>the</p>", 
                                "<p>a</p>", "<p>an</p>"],
                    options_hi: ["<p>one</p>", "<p>the</p>",
                                "<p>a</p>", "<p>an</p>"],
                    solution_en: "<p>92.(b) the .<br>Here &ldquo;during the day&rdquo; means &ldquo;during the daytime&rdquo;</p>",
                    solution_hi: "<p>92.(b) the .<br>यहाँ \"during the day\" का अर्थ है \"दिन के समय&rsquo;&rsquo;।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93<strong>. Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans. <br>Select the most appropriate option to fill in the blank no. 93</p>",
                    question_hi: "<p>93. <strong>Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans<br>Select the most appropriate option to fill in the blank no. 93</p>",
                    options_en: ["<p>come across</p>", "<p>come up</p>", 
                                "<p>come out</p>", "<p>come away</p>"],
                    options_hi: ["<p>come across</p>", "<p>come up</p>",
                                "<p>come out</p>", "<p>come away</p>"],
                    solution_en: "<p>93.(a) <strong>Come across</strong> - meet or find by chance.<br><strong>Come up</strong> - occur or present itself, especially unexpectedly.<br><strong>Come out</strong> - emerge; become known.<br><strong>Come away</strong> - be left with a specified feeling, impression, or result after doing something.<br><strong>Pinnacle note-</strong> All these are phrasal verbs.</p>",
                    solution_hi: "<p>93.(a) <strong>Come across</strong> - meet or find by chance.<br><strong>Come up</strong> - किसी के बीच में आना (बिना बुलाए)।<br><strong>Come out</strong> -बाहर आना ; ज्ञात होना । <br><strong>Come away</strong> -कुछ करने के बाद एक निर्दिष्ट भावना, प्रभाव या परिणाम के साथ रह जाना।<br><strong>Pinnacle note-</strong> ये सभी वाक्यांश क्रिया (phrasal verbs) हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94<strong>. Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans<br>Select the most appropriate option to fill in the blank no. 94</p>",
                    question_hi: "<p>94. <strong>Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans <br>Select the most appropriate option to fill in the blank no. 94</p>",
                    options_en: ["<p>in</p>", "<p>on</p>", 
                                "<p>for</p>", "<p>with</p>"],
                    options_hi: ["<p>in</p>", "<p>on</p>",
                                "<p>for</p>", "<p>with</p>"],
                    solution_en: "<p>94.(d) with<br>The preposition &ldquo;with&rsquo; should be used here which is used in the sense of &ldquo;having or carrying something&rdquo;</p>",
                    solution_hi: "<p>94.(d) with<br>Preposition \"with\" का यहाँ प्रयोग किया जाना चाहिए जिसका प्रयोग \"कुछ रखने या ले जाने\" के भाव में होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95.<strong> Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans<br>Select the most appropriate option to fill in the blank no. 95</p>",
                    question_hi: "<p>95.<strong> Cloze Test :-</strong><br>Arabia is mostly a desert. It is (91)______ to walk over it during (92)______ day. Occasionally, one may (93)______ a fertile patch called an oasis (94)______ springs and tall fig and palm trees. The Arabs are nomads (95)______ live in tents and travel in caravans<br>Select the most appropriate option to fill in the blank no. 95</p>",
                    options_en: ["<p>whom</p>", "<p>who</p>", 
                                "<p>what</p>", "<p>which</p>"],
                    options_hi: ["<p>whom</p>", "<p>who</p>",
                                "<p>what</p>", "<p>which</p>"],
                    solution_en: "<p>95.(b) who<br>Relative pronoun &ldquo;who&rdquo; should be used for the people.</p>",
                    solution_hi: "<p>95.(b) who<br>&ldquo; People&rsquo;&rsquo; के लिए Relative pronoun &ldquo;who&rdquo; का उपयोग किया जाना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>Which of the following statements concerning the effects of scientific advancements may be derived from the passage?</p>",
                    question_hi: "<p>96. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>Which of the following statements concerning the effects of scientific advancements may be derived from the passage?</p>",
                    options_en: ["<p>The development of renewable energy technologies has led to a complete elimination of greenhouse gas emissions.</p>", "<p>Scientific innovations have the potential to revolutionise various sectors and improve efficiency in society.</p>", 
                                "<p>There is no longer any need for human intelligence since AI has taken over every field.</p>", "<p>The only way to solve problems with food security and renewable energy is through genetic engineering.</p>"],
                    options_hi: ["<p>The development of renewable energy technologies has led to a complete elimination of greenhouse gas emissions.</p>", "<p>Scientific innovations have the potential to revolutionise various sectors and improve efficiency in society.</p>",
                                "<p>There is no longer any need for human intelligence since AI has taken over every field.</p>", "<p>The only way to solve problems with food security and renewable energy is through genetic engineering.</p>"],
                    solution_en: "<p>96.(b) Scientific innovations have the potential to revolutionise various sectors and improve efficiency in society.<br>The given passage explains how scientific advancements like artificial intelligence, genetic engineering, and renewable energy technologies have greatly impacted various fields such as communication, energy, and manufacturing. It also stresses the need to consider ethical implications due to the significant societal effects of these technologies.</p>",
                    solution_hi: "<p>96.(b) Scientific innovations have the potential to revolutionise various sectors and improve efficiency in society.<br>दिया गया passage बताता है कि कैसे artificial intelligence, genetic engineering और renewable energy technologies जैसी वैज्ञानिक प्रगति ने संचार, ऊर्जा एवं विनिर्माण जैसे विभिन्न क्षेत्रों को बहुत प्रभावित किया है। यह इन technologies के महत्वपूर्ण सामाजिक प्रभावों(significant societal effects) के कारण नैतिक प्रभावों(ethical implications) पर विचार करने की आवश्यकता पर भी बल देता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>Which of the following best captures the underlying theme of the passage?</p>",
                    question_hi: "<p>97. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>Which of the following best captures the underlying theme of the passage?</p>",
                    options_en: ["<p>The connection between renewable energy and greenhouse gas emissions</p>", "<p>The ethical considerations of artificial intelligence (AI) in society</p>", 
                                "<p>The impact of scientific advancements on medical treatment</p>", "<p>The transformative power of technology in tourism sector</p>"],
                    options_hi: ["<p>The connection between renewable energy and greenhouse gas emissions</p>", "<p>The ethical considerations of artificial intelligence (AI) in society</p>",
                                "<p>The impact of scientific advancements on medical treatment</p>", "<p>The transformative power of technology in tourism sector</p>"],
                    solution_en: "<p>97.(b) The ethical considerations of artificial intelligence (AI) in society<br>The given passage talks about carefully considering technologies such as artificial intelligence from an ethical standpoint, due to their tremendous impact on society. Hence, option (b) best captures the underlying theme of the passage.</p>",
                    solution_hi: "<p>97.(b) The ethical considerations of artificial intelligence (AI) in society<br>दिए गए passage में artificial intelligence जैसी technologie पर नैतिक दृष्टिकोण(ethical standpoint) से सावधानीपूर्वक विचार करने की बात की गई है, क्योंकि इनका society पर बहुत अधिक प्रभाव पड़ता है। अतः, option (b) passage के underlying theme को सर्वोत्तम रूप से दर्शाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>According to the paragraph, which phrase describes computers capacity to undertake human-intelligence-based tasks?</p>",
                    question_hi: "<p>98. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>According to the paragraph, which phrase describes computers capacity to undertake human-intelligence-based tasks?</p>",
                    options_en: ["<p>Profound</p>", "<p>Revolution</p>", 
                                "<p>Artificial Intelligence</p>", "<p>Communication</p>"],
                    options_hi: ["<p>Profound</p>", "<p>Revolution</p>",
                                "<p>Artificial Intelligence</p>", "<p>Communication</p>"],
                    solution_en: "<p>98.(c) Artificial Intelligence<br>(Line/s from the passage - The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors.)</p>",
                    solution_hi: "<p>98.(c) Artificial Intelligence<br>(Passage से ली गई line/s - The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors./कृत्रिम बुद्धिमत्ता, जिसे कभी-कभी AI भी कहा जाता है, ने कई अलग-अलग क्षेत्रों में क्रांति ला दी है, क्योंकि यह उन कार्यों को पूरा करने में सक्षम है जिसे पहले मानव बुद्धि की आवश्यकता होती थी।)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>Which technologies are stated in the text as utilising renewable or green energy sources?</p>",
                    question_hi: "<p>99. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>Which technologies are stated in the text as utilising renewable or green energy sources?</p>",
                    options_en: ["<p>Solar panels and wind turbines</p>", "<p>Medical treatment and communication</p>", 
                                "<p>Genetic engineering and 3D printing</p>", "<p>Artificial Intelligence (AI) and the Internet of Things (IoT)</p>"],
                    options_hi: ["<p>Solar panels and wind turbines</p>", "<p>Medical treatment and communication</p>",
                                "<p>Genetic engineering and 3D printing</p>", "<p>Artificial Intelligence (AI) and the Internet of Things (IoT)</p>"],
                    solution_en: "<p>99.(a) Solar panels and wind turbines<br>(Line/s from the passage- Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases.)</p>",
                    solution_hi: "<p>99.(a) Solar panels and wind turbines<br>(Passage से ली गई line/s- Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases./सोलर पैनल और विंड टर्बाइन जैसे नवीकरणीय ऊर्जा स्रोतों का उपयोग करने वाली Technologies ग्रीनहाउस गैसों के उत्सर्जन को कम करने में मदद करती हैं।)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>According to the passage, by linking which sort of products do the Internet of Things (IoT) provide improved resource management?</p>",
                    question_hi: "<p>100. <strong>Comprehension:</strong><br>The advancement of science has brought about profound changes in every aspect of our lives, from medical treatment to the way we communicate. The capacity of artificial intelligence, sometimes known as AI, to accomplish jobs that previously required human intellect has caused a revolution in a number of different sectors. The altering of organisms\' genetic composition, which is made possible through genetic engineering, provides answers for issues relating to food security and renewable energy. Technologies that utilise renewable energy sources, such as solar panels and wind turbines, help to lower the emissions of greenhouse gases. Manufacturing has been radically altered with the introduction of 3D printing, and the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products. Because of the tremendous effects these technologies have on society, their use must be carefully considered from an ethical standpoint.<br>According to the passage, by linking which sort of products do the Internet of Things (IoT) provide improved resource management?</p>",
                    options_en: ["<p>3D printing technologies</p>", "<p>Previously disconnected ordinary products</p>", 
                                "<p>Renewable energy technologies</p>", "<p>Artificial Intelligence (AI) technologies</p>"],
                    options_hi: ["<p>3D printing technologies</p>", "<p>Previously disconnected ordinary products</p>",
                                "<p>Renewable energy technologies</p>", "<p>Artificial Intelligence (AI) technologies</p>"],
                    solution_en: "<p>100.(b) Previously disconnected ordinary products<br>(Line/s from the passage- the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products.)</p>",
                    solution_hi: "<p>100.(b) Previously disconnected ordinary products<br>(Passage से ली गई line/s- the Internet of Things (IoT) has enabled better resource management via the connection of previously disconnected ordinary products./इंटरनेट ऑफ थिंग्स (IoT) ने पहले से डिस्कनेक्ट सामान्य उत्पादों के कनेक्शन के माध्यम से बेहतर संसाधन प्रबंधन को सक्षम बनाया है।)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>