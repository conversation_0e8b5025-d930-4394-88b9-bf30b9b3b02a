<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. In an election between two candidates, one got 65% of the total valid votes. 20% of the votes were invalid. If the total number of votes was 7,500, the number of valid votes that the other candidate got was:</p>",
                    question_hi: "<p>1. दो उम्मीदवारों के बीच एक चुनाव में एक उम्मीदवार को कुल वैध मतों के 65% मत प्राप्त हुए। 20% मत अवैध थे। यदि मतों की कुल संख्या 7,500 थी, तो दूसरे उम्मीदवार को मिले वैध मतों की संख्या ______ थी।</p>",
                    options_en: ["<p>4,300</p>", "<p>1,900</p>", 
                                "<p>2,100</p>", "<p>1,700</p>"],
                    options_hi: ["<p>4,300</p>", "<p>1,900</p>",
                                "<p>2,100</p>", "<p>1,700</p>"],
                    solution_en: "<p>1.(c)<br>Total number of votes = 7500<br>Total number of valid votes = 7500 &times; 80% = 6000 <br>Required number of votes = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2100</p>",
                    solution_hi: "<p>1.(c)<br>कुल वोटों की संख्या = 7500<br>वैध मतों की कुल संख्या = 7500 &times; 80% = 6000 <br>आवश्यक वोटों की संख्या = 6000 &times; <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 2100</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. In ∆PQR, PR = 10 cm. Find the length of PT, where ST∥QR. Given that PS = 6 cm and QS = 14 cm.</p>",
                    question_hi: "<p>2. ∆PQR में, PR = 10 cm है। PT की लंबाई ज्ञात कीजिए, जहाँ ST|| QR है। दिया गया है कि PS = 6 cm और QS = 14 cm है।</p>",
                    options_en: ["<p>2 cm</p>", "<p>4 cm</p>", 
                                "<p>1.5 cm</p>", "<p>3 cm</p>"],
                    options_hi: ["<p>2 cm</p>", "<p>4 cm</p>",
                                "<p>1.5 cm</p>", "<p>3 cm</p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdceUAT3nLqAHR6sSt6poYhr4JWG6G_RxzCvE7-h5Suhm1T6NzM262-_DU8lXVUpdkpQnyZCyQkX1wjFgdO3Jbf2XiDxMXiyuhSYxjiByeHZBltg6hROvBd6z_jY4xL4tmBslkY?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"215\" height=\"176\"><br><strong>Thales theorem : -</strong> <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>S</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>T</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></math><br>Now, the length of PT <math display=\"inline\"><mo>&#8594;</mo></math><br><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>T</mi></mrow><mn>10</mn></mfrac></math>, PT = 3 cm</p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdceUAT3nLqAHR6sSt6poYhr4JWG6G_RxzCvE7-h5Suhm1T6NzM262-_DU8lXVUpdkpQnyZCyQkX1wjFgdO3Jbf2XiDxMXiyuhSYxjiByeHZBltg6hROvBd6z_jY4xL4tmBslkY?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"215\" height=\"176\"><br><strong>थेल्स प्रमेय: -</strong> <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>S</mi></mrow><mrow><mi>P</mi><mi>Q</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>T</mi></mrow><mrow><mi>P</mi><mi>R</mi></mrow></mfrac></math><br>अब, PT की लंबाई <math display=\"inline\"><mo>&#8594;</mo></math><br><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>14</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>T</mi></mrow><mn>10</mn></mfrac></math>, PT = 3 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Simplify the following : <br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math> = ?</p>",
                    question_hi: "<p>3. निम्नलिखित व्यंजक को सरल कीजिए।<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math> = ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>3.(b)<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>33</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>3.(b)<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>44</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>) &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><mn>15</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>33</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>5</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A, B and C can complete a work in 12, 15 and 20 days, respectively. How many days are required to finish the work if they work together?</p>",
                    question_hi: "<p>4. A, B और C एक काम को क्रमशः 12, 15 और 20 दिनों में पूरा कर सकते हैं। यदि वे एक साथ मिलकर काम करते हैं तो काम को पूरा करने में उन्हे कितने दिन का समय लगेगा?</p>",
                    options_en: ["<p>4 days</p>", "<p>3 days</p>", 
                                "<p>5 days</p>", "<p>6 days</p>"],
                    options_hi: ["<p>4 दिन</p>", "<p>3 दिन</p>",
                                "<p>5 दिन</p>", "<p>6 दिन</p>"],
                    solution_en: "<p>4.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe8L4I91NMRLljTrpLmNyfCcLK9_t6sKJ6b_FJ2NATqy361_idy0vzUg3yxBNOxg9A84U0PFI6IcAH2Ea-An8EdEw4_-Me5n8mZlhfc433UK8vjWABslHZuueIiZwz9N2HEsgaiTQ?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"275\" height=\"139\"><br>Efficiency of A, B and C = 5 + 4 + 3 = 12 units<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5 days</p>",
                    solution_hi: "<p>4.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfRnqYP80gHW0nGgtBybJ0SGj4v6o7jC6cGbIGUrw-pv3r6bykq0HFVm9rjN1alI-DYginya6KYi_1pN7mjBOXLXRtqkLIHT6TtHCugTNRKD79qgjUsywWhkzFaVyY_p9ID8kmi?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"297\" height=\"162\"><br>A, B और C की दक्षता = 5 + 4 + 3 = 12 इकाई<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 5 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. The given line graph shows the number of scooters manufactured (in thousands) by companies X and Z, over the years. <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXftBXH2uop5vS7JCy-bYVYdzRiOtnEqu1HiPTxeAY5tFIRjGUE_AB3q4zuKnMC75KNwwL954S5sXST8NdGH06QlSu39IyDACyGkOFDQtsFvLJQ4i0lj3mPYGZJcMw0XQAtqKAdK4Q?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"419\" height=\"276\"><br>What is the average number of scooters manufactured by company X over the given period?</p>",
                    question_hi: "<p>5. दिया गया लाइन ग्राफ पिछले वर्षों में कंपनी x और z द्वारा निर्मित स्कूटरों की संख्या (हजार में) दर्शाता है।<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfcFqSvOI165mmzwC-tNJTJMLm7w1ulSryweM8zOedPERJuyd_DgoihZox1eyJEZmT3fXuX9hUUZSORM0_pUCJ5tBIn0j37Yhkb-XCzAUrXyFe7siCJHgDnMUzVsv77_DyUZs5y?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"443\" height=\"294\"><br>संदर्भ: Number of scooters (in thousands) - स्कूटरों की संख्या (हजार में), Number of scooters manufactured (in thousands) by companies X and Z, over the years - पिछले वर्षों में कंपनी X और Z द्वारा निर्मित स्कूटरों की संख्या (हजार में)<br>दी गई अवधि में कंपनी X द्वारा निर्मित स्कूटरों की औसत संख्या कितनी है?</p>",
                    options_en: ["<p>256000</p>", "<p>213000</p>", 
                                "<p>116500</p>", "<p>126500</p>"],
                    options_hi: ["<p>256000</p>", "<p>213000</p>",
                                "<p>116500</p>", "<p>126500</p>"],
                    solution_en: "<p>5.(c)<br>Required average = <math display=\"inline\"><mfrac><mrow><mn>128</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>110</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>102</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>80</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>109</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>170</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 116500 (because all values in thousands)</p>",
                    solution_hi: "<p>5.(c)<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>128</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>110</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>102</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>80</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>109</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>170</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 116500 (क्योंकि सभी मान हजारों में हैं)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A sum becomes ₹6,600 in 4 years at simple interest at a yearly interest rate of 5% per annum. What is the sum?</p>",
                    question_hi: "<p>6. एक धनराशि 5% प्रति वर्ष की वार्षिक ब्याज दर पर साधारण ब्याज पर 4 वर्षों में ₹6,600 हो जाती है। वह धनराशि क्या है?</p>",
                    options_en: ["<p>₹6,000</p>", "<p>₹3,300</p>", 
                                "<p>₹5,500</p>", "<p>₹4,400</p>"],
                    options_hi: ["<p>₹6,000</p>", "<p>₹3,300</p>",
                                "<p>₹5,500</p>", "<p>₹4,400</p>"],
                    solution_en: "<p>6.(c)<br>Amount = principal + SI<br>SI = <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>r</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>t</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>amount = p + <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>r</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>t</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>6600 = p + <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>6600 = p + <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>6600 = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>p</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>p</mi><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>6600 = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>p</mi><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>p = 5500</p>",
                    solution_hi: "<p>6.(c)<br>राशि = मूलधन + साधारण ब्याज <br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi>&#160;</mi><mo>&#215;</mo><mo>&#160;</mo><mi>&#2342;&#2352;</mi><mo>&#160;</mo><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>मिश्रधन (राशि) = मूलधन + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi>&#160;</mi><mo>&#215;</mo><mo>&#160;</mo><mi>&#2342;&#2352;</mi><mo>&#160;</mo><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>6600 = मूलधन +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#160;</mo><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math><br>6600 = मूलधन + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#160;</mo></mrow><mn>5</mn></mfrac></math><br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>5</mn></mfrac></math><br>6600 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi>&#160;</mi></mrow><mn>5</mn></mfrac></math><br>मूलधन = 5500</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "7. If the point of intersection of lines x + y = 2 and 2x - y = 1  lies on y = Kx + 5, what is the value of  K ?",
                    question_hi: "7.  यदि रेखाओं x + y = 2 और 2x - y = 1 का प्रतिच्छेद बिंदु y = Kx + 5 पर स्थित है, तो K का मान ज्ञात कीजिए",
                    options_en: ["  3", "  -4", 
                                "  4", " -3"],
                    options_hi: ["  3", "  -4",
                                "  4", " -3"],
                    solution_en: "7.(b) <br />x + y = 2……(i)<br />2x - y = 1…….(ii)<br />On solving both equation we get,<br />x = 1, y = 1<br />According to the question,<br />1 = K <math display=\"inline\"><mo>×</mo></math> 1 + 5<br />K = 1 - 5 = - 4",
                    solution_hi: "7.(b) <br />x + y = 2……(i)<br />2x - y = 1…….(ii)<br />दोनों समीकरणों को हल करने पर हमें प्राप्त होता है,<br />x = 1, y = 1<br />प्रश्न के अनुसार,<br />1 = K <math display=\"inline\"><mo>×</mo></math> 1 + 5<br />K = 1 - 5 = - 4",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Two offers were being made to Swaroop for a watch with a marked price of ₹1,600. Either two successive discounts of 20%, or two discounts of 30% and 10% after each other is offered. If Swaroop opted for the better plan over the other, how much more must he have saved?</p>",
                    question_hi: "<p>8. स्वरूप को ₹1,600 अंकित मूल्य वाली एक घड़ी के लिए दो ऑफर दिए जाते हैं। या तो वह 20% की दो क्रमिक छूट ले सकता है, या एक के बाद एक 30% और 10% की दो छूट ले सकता है। यदि स्वरूप उनमें से बेहतर प्लान का चुनाव करता है, तो वह कितनी अधिक बचत कर सकता है?</p>",
                    options_en: ["<p>₹22</p>", "<p>₹35</p>", 
                                "<p>₹16</p>", "<p>₹30</p>"],
                    options_hi: ["<p>₹22</p>", "<p>₹35</p>",
                                "<p>₹16</p>", "<p>₹30</p>"],
                    solution_en: "<p>8.(c)<br>Two successive discount of 20% = 20 + 20 - <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36%<br>Two successive discount of 30% and 10% = 30 + 10 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 37%<br>Required amount = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>37</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>36</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 16</p>",
                    solution_hi: "<p>8.(c)<br>20% की दो क्रमिक छूट = 20 + 20 - <math display=\"inline\"><mfrac><mrow><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36%<br>30% और 10% की दो क्रमिक छूट = 30 + 10 - <math display=\"inline\"><mfrac><mrow><mn>30</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 37%<br>आवश्यक राशि = 1600 &times; <math display=\"inline\"><mfrac><mrow><mn>37</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>36</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 16</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Study the given bar-graph and answer the question that follows.<br>The bar-graph shows the production (in lakh) of kitchen appliances manufactured by three companies A, B, and C over a period of six years from 2012 to 2017.<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdfF1z4hyDOMRSLDXKeyA6MMHd1md4nkBjuAfo0JgamF6_8LqPZGv2H4grNGBSqjoz5YnkArCUsk1oUq4UMhLPByXvor269KouaL50dAwSE21-p9Lx2DQLnmce1eJ5vGyLfunbRSw?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"449\" height=\"270\"><br>80% of the combined average production of companies B and C is what percentage (to the nearest integer) more/less than 90% of the average production of company A. for the given period of 2012 to 2017?</p>",
                    question_hi: "<p>9. दिए गए बार ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br>बार ग्राफ में 2012 से 2017 तक छः वर्षों की अवधि में तीन कंपनियों A, B और C द्वारा निर्मित रसोई उपकरणों के उत्पादन (लाख में) को दर्शाया गया है।<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcYto9Bubz_Jh3cxtrb_Cd5GjOGsIiSZ2OPYBtffBVpOEfQblxEp4eLmLtzyE8amYtJmz31mfg5is_OsNA7GpsSerKhXu1gtNamrv3t9OmcAGnNm8ciSjJzTg50RzWi4vBAPgoXtw?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"428\" height=\"255\"><br>वर्ष 2012 से 2017 की दी गई अवधि में कंपनी B और C के संयुक्त औसत उत्पादन का 80%, कंपनी A के औसत उत्पादन के 90% से कितना प्रतिशत (निकटतम पूर्णांक तक) अधिक / कम है?</p>",
                    options_en: ["<p>21% more</p>", "<p>19% more</p>", 
                                "<p>19% less</p>", "<p>21% less</p>"],
                    options_hi: ["<p>21% अधिक</p>", "<p>19% अधिक</p>",
                                "<p>19% कम</p>", "<p>21% कम</p>"],
                    solution_en: "<p>9.(c)<br>Production of company B = 130 + 80 + 70 + 90 + 180 + 80 = 630<br>Production of company C = 70 + 90 + 120 + 80 + 110 + 140 = 610<br>Average production of company B and C = <math display=\"inline\"><mfrac><mrow><mn>630</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>610</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 103.33<br>80% of average production of company B and C = 103.33 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 82.66<br>Average production of company A = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>160</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>70</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>110</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 113.33<br>90% of average production of company A = 113.33 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 102<br>required% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>102</mn><mo>-</mo><mn>82</mn><mo>.</mo><mn>66</mn></mrow><mn>102</mn></mfrac></math> &times; 100 = 18.95 (approx 19% less)</p>",
                    solution_hi: "<p>9.(c)<br>कंपनी B का उत्पादन = 130 + 80 + 70 + 90 + 180 + 80 = 630<br>कंपनी C का उत्पादन = 70 + 90 + 120 + 80 + 110 + 140 = 610<br>कंपनी B और C का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>630</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>610</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 103.33<br>कंपनी B और C के औसत उत्पादन का 80% = 103.33 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 82.66<br>कंपनी A का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>160</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>70</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>110</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 113.33<br>कंपनी A के औसत उत्पादन का 90% = 113.33 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 102<br>आवश्यक% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>102</mn><mo>-</mo><mn>82</mn><mo>.</mo><mn>66</mn></mrow><mn>102</mn></mfrac></math>&nbsp;&times; 100 = 18.95 (लगभग 19% कम)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "10. What is the number of digits required for numbering a book with 428 pages?",
                    question_hi: "10.  428 पृष्ठों वाली एक पुस्तक को क्रमांकित करने के लिए आवश्यक अंकों की संख्या कितनी है?",
                    options_en: [" 1500 ", " 2000 ", 
                                " 988 ", " 1176"],
                    options_hi: [" 1500 ", " 2000 ",
                                " 988 ", " 1176"],
                    solution_en: "10.(d)<br />According to the question,<br />Number of digits required = (9 × 1) + (90 × 2) + (329 × 3) = 1176",
                    solution_hi: "10.(d)<br />प्रश्न के अनुसार,<br />आवश्यक अंकों की संख्या = (9 × 1) + (90 × 2) + (329 × 3) = 1176",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The outer surface of a sphere having diameter 10 m is painted at the rate of ₹ <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mi>&#960;</mi></mrow></mfrac></math> per m&sup2;. What is the cost of painting?</p>",
                    question_hi: "<p>11. 10 m व्यास वाले एक गोले के बाह्य पृष्ठ को ₹ <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mi>&#960;</mi></mrow></mfrac></math> प्रति वर्ग मीटर की दर से पेंट किया गया। तो पेंटिंग की लागत कितनी है?</p>",
                    options_en: ["<p>₹8,000</p>", "<p>₹9,000</p>", 
                                "<p>₹4,000</p>", "<p>₹6,000</p>"],
                    options_hi: ["<p>₹8,000</p>", "<p>₹9,000</p>",
                                "<p>₹4,000</p>", "<p>₹6,000</p>"],
                    solution_en: "<p>11.(a)<br>TSA of the sphere = 4<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>Required cost = 4 &times; <math display=\"inline\"><mi>&#960;</mi></math> &times; 5 &times; 5 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi>&#960;</mi></mfrac></math> = ₹ 8000</p>",
                    solution_hi: "<p>11.(a)<br>गोले का सम्पूर्ण पृष्ठाय क्षेत्रफल = 4<math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>आवश्यक लागत = 4 &times; <math display=\"inline\"><mi>&#960;</mi></math> &times; 5 &times; 5 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mi>&#960;</mi></mfrac></math> = ₹ 8000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Study the given table and answer the question that follows.<br>The table shows the number of students appeared and qualified (in thousands) district-wise in the EMCET examination in a state.<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf_lNzRDGTxbIvmhDspXsBGOeX_9P8LLnntWyQkc0FRtpzfFHK-6haVVNK4X9XDHeAddkCwlI219xskKbtnaVsS7x1-Ssnv3nGDdC_R_TmDNJYT6Ouf9Z2tqLTnn0DsQNdptWZkDw?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"554\" height=\"111\"><br>The total percentage of students who qualified in the year 2016 (correct up to two decimals) is:</p>",
                    question_hi: "<p>12. निम्नलिखित तालिका का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>तालिका में एक राज्य में EMCET परीक्षा में जिलेवार उपस्थित होने वाले और उत्तीर्ण (हजार में) होने वाले विद्यार्थियों की संख्या को दर्शाया गया है।<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcUGG8Kd8SMwtS42G6pY0n9jcQO1uieKWde7O9DCDSFGXbIcjlGk1Aq8fmbxn3PW1FFtxlWleJwkERrOvBuvxiboJ9E_zTnMKISGTt6ZuAKFwf4AyMKL_earaA0Q0vsCtkHlfxvkA?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"513\" height=\"92\"><br>वर्ष 2016 में उत्तीर्ण होने वाले विद्यार्थियों का कुल प्रतिशत क्या है (केवल दशमलव के दो स्थान तक)?</p>",
                    options_en: ["<p>52.82%</p>", "<p>55.18%</p>", 
                                "<p>44.72%</p>", "<p>54.78%</p>"],
                    options_hi: ["<p>52.82%</p>", "<p>55.18%</p>",
                                "<p>44.72%</p>", "<p>54.78%</p>"],
                    solution_en: "<p>12.(d)<br>Total number of students in 2016 = 55 + 35 + 48 + 50 + 55 + 60 = 303<br>Qualified students = 42 + 22 + 26 + 30 + 27 + 19 = 166<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>166</mn></mrow><mrow><mn>303</mn></mrow></mfrac></math> &times; 100 = 54.78%</p>",
                    solution_hi: "<p>12.(d)<br>2016 में छात्रों की कुल संख्या = 55 + 35 + 48 + 50 + 55 + 60 = 303<br>उत्तीर्ण छात्र = 42 + 22 + 26 + 30 + 27 + 19 = 166<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>166</mn></mrow><mrow><mn>303</mn></mrow></mfrac></math> &times; 100 = 54.78%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. In ∆EFG, XY ∥ FG, area of the quadrilateral XFGY = 44 m<sup>2</sup> . If EX : XF = 2 : 3, then find the area of ∆EXY (in m<sup>2</sup> ).</p>",
                    question_hi: "<p>13. △EFG में, XY || FG है, चतुर्भुज XFGY का क्षेत्रफल = 44 m&sup2; है। यदि EX : XF = 2: 3 है, तो △EXY का क्षेत्रफल (m&sup2; में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>7.28</p>", "<p>8.10</p>", 
                                "<p>8.38</p>", "<p>9.46</p>"],
                    options_hi: ["<p>7.28</p>", "<p>8.10</p>",
                                "<p>8.38</p>", "<p>9.46</p>"],
                    solution_en: "<p>13.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe1zF-CMam4_Aq6utXqnSOD-A99X8lBUoV_5WltAW2ubFnnPjIark_-eJPkuzpN-fpL51ixGbKGD8CwstRxBaIfuEnvmUc89n7MAoU4mYtIhmACrgHOK0HKNpRVWYJ_xqk0ki5n0g?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"184\" height=\"153\"><br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>E</mi><mi>X</mi><mi>Y</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>E</mi><mi>F</mi><mi>G</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math>)<sup>2</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>25</mn></mfrac></math><br>Area of quadrilateral XFGY = 25 - 4 = 21 units<br>21 units = 44 m<sup>2</sup><br>(area of <math display=\"inline\"><mi>&#916;</mi></math>EXY) 2 units = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>21</mn></mfrac></math> &times; 4 = 8.38 m<sup>2</sup></p>",
                    solution_hi: "<p>13.(c)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe1zF-CMam4_Aq6utXqnSOD-A99X8lBUoV_5WltAW2ubFnnPjIark_-eJPkuzpN-fpL51ixGbKGD8CwstRxBaIfuEnvmUc89n7MAoU4mYtIhmACrgHOK0HKNpRVWYJ_xqk0ki5n0g?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"184\" height=\"153\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;</mi><mi>E</mi><mi>X</mi><mi>Y</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;</mi><mi>E</mi><mi>F</mi><mi>G</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi><mi>&#160;</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow></mfrac></math>)<sup>2 </sup>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>25</mn></mfrac></math><br>चतुर्भुज XFGY का क्षेत्रफल = 25 - 4 = 21 इकाई <br>21 इकाई = 44 m<sup>2</sup><br>(<math display=\"inline\"><mi>&#916;</mi></math>EXY का क्षेत्रफल) 2 इकाई= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>21</mn></mfrac></math> &times; 4 = 8.38 m<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. If <math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>tan</mi><mi>&#952;</mi><mo>+</mo><msqrt><mn>13</mn></msqrt><mi>sin</mi><mi>&#952;</mi></mrow><mrow><msqrt><mn>13</mn><mo>&#160;</mo></msqrt><mi>cos</mi><mi>&#952;</mi><mo>-</mo><mn>3</mn><mi>tan</mi><mi>&#952;</mi></mrow></mfrac></math> is :</p>",
                    question_hi: "<p>14. यदि <math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2 है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>tan</mi><mi>&#952;</mi><mo>+</mo><msqrt><mn>13</mn></msqrt><mi>sin</mi><mi>&#952;</mi></mrow><mrow><msqrt><mn>13</mn><mo>&#160;</mo></msqrt><mi>cos</mi><mi>&#952;</mi><mo>-</mo><mn>3</mn><mi>tan</mi><mi>&#952;</mi></mrow></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>0</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>0</p>", "<p>4</p>"],
                    solution_en: "<p>14.(d)<br><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2<br>sin<math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac></math><br>b = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math>= 3<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>-</mo><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = 4</p>",
                    solution_hi: "<p>14.(d)<br><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2<br>sin<math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac></math><br>b = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math>= 3<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>-</mo><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = 4</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. At the beginning of day 1 of a month, Rajesh has 500 eggs. He sells 20% of the eggs by the end of the day and added b% of the eggs at the beginning of the next day and sold 20% of the eggs at the end of the next day. This pattern continued up to the end of the third day of the month when he is left with 1024 eggs. The value of b is equal to:</p>",
                    question_hi: "<p>15. महीने के पहले दिन की शुरुआत में राजेश के पास 500 अंडे थे। वह दिन के अंत तक 20% अंडे बेच देता है और अगले दिन की शुरुआत में उपलब्ध अंडों में b% अंडे और मिला लेता है और अगले दिन के अंत तक 20% अंडे बेच देता है। यह पैटर्न महीने के तीसरे दिन के अंत तक जारी रहता है और तब उसके पास 1024 अंडे बचते है। निम्न में से b का मान किसके बराबर है?</p>",
                    options_en: ["<p>500</p>", "<p>20</p>", 
                                "<p>100</p>", "<p>10</p>"],
                    options_hi: ["<p>500</p>", "<p>20</p>",
                                "<p>100</p>", "<p>10</p>"],
                    solution_en: "<p>15.(c)<br>According to the question,<br>500 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = 1024<br>4 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>= 1024<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1024</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 16 &times; 25 &times; 100<br>100 + b = 4 &times; 5 &times; 10 = 200<br>b = 100</p>",
                    solution_hi: "<p>15.(c)<br>प्रश्न के अनुसार,<br>500 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = 1024<br>4 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math>= 1024<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1024</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>4</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = 16 &times; 25 &times; 100<br>100 + b = 4 &times; 5 &times; 10 = 200<br>b = 100</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. Divide some money in the ratio Ravi, Reeta and Rahul that 5 (Part of Ravi) = 3 (Part of Reeta) =11 (Part of Rahul). The money ratio of Ravi : Reeta : Rahul is equal to:</p>",
                    question_hi: "<p>16. कुछ धनराशि को रवि, रीता और राहुल के बीच इस अनुपात में बांटिए कि 5 (रवि का हिस्सा) = 3 (रीता का हिस्सा) = 11 (राहुल का हिस्सा) हो। रवि : रीता : राहुल की धनराशि का अनुपात _________ के बराबर है।</p>",
                    options_en: ["<p>11 : 5 : 3</p>", "<p>11 : 33 : 15</p>", 
                                "<p>5 : 11 : 3</p>", "<p>33 : 55 : 15</p>"],
                    options_hi: ["<p>11 : 5 : 3</p>", "<p>11 : 33 : 15</p>",
                                "<p>5 : 11 : 3</p>", "<p>33 : 55 : 15</p>"],
                    solution_en: "<p>16.(d)<br>5 (Part of Ravi) = 3 (Part of Reeta) =11 (Part of Rahul)<br><math display=\"inline\"><mfrac><mrow><mi>r</mi><mi>a</mi><mi>v</mi><mi>i</mi></mrow><mrow><mi>r</mi><mi>e</mi><mi>e</mi><mi>t</mi><mi>a</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>r</mi><mi>e</mi><mi>e</mi><mi>t</mi><mi>a</mi></mrow><mrow><mo>&#160;</mo><mi>r</mi><mi>a</mi><mi>h</mi><mi>u</mi><mi>l</mi><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math><br>Ratio - ravi : reeta : rahul<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; &nbsp; <strong>5</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong>11&nbsp; &nbsp;</strong>:&nbsp; &nbsp; &nbsp;11&nbsp; &nbsp;:&nbsp; &nbsp; 3<br>-------------------------------------<br>Final - 33&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;55&nbsp; &nbsp;:&nbsp; &nbsp;15<br><strong>Short tricks :-</strong> 5 (Part of Ravi) = 3 (Part of Reeta) =11 (Part of Rahul)<br>LCM of 5, 3, 11 = 5 &times; 3 &times; 11<br>Required ratio = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn></mrow><mn>3</mn></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn></mrow><mn>11</mn></mfrac></math> = 33 : 55 : 15</p>",
                    solution_hi: "<p>16.(d)<br>5 (रवि का भाग) = 3 (रीता का भाग) =11 (राहुल का भाग)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2352;</mi><mi>&#2357;&#2367;</mi></mrow><mi>&#2352;&#2368;&#2340;&#2366;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2352;&#2368;&#2340;&#2366;</mi><mrow><mi>&#2352;&#2366;&#2361;&#2369;&#2354;</mi><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math><br>अनुपात - रवि : रीता : राहुल<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; :&nbsp; &nbsp;5&nbsp; &nbsp;:&nbsp; &nbsp;<strong>5</strong> <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<strong>11&nbsp; &nbsp; </strong>:&nbsp; 11&nbsp; :&nbsp; &nbsp;3<br>-------------------------------------<br>अंतिम -&nbsp; &nbsp;33&nbsp; &nbsp;:&nbsp; &nbsp;55&nbsp; : 15<br><strong>शॉर्ट ट्रिक्स :-</strong> 5 (रवि का हिस्सा) = 3 (रीता का हिस्सा) = 11 (राहुल का हिस्सा)<br>5, 3, 11 का LCM = 5 &times; 3 &times; 11<br>आवश्यक अनुपात = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn></mrow><mn>3</mn></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>11</mn></mrow><mn>11</mn></mfrac></math> = 33 : 55 : 15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. The difference between the cost price and the selling price of an article is ₹1,800. If there is a profit of 20%, then the cost price of the article is:</p>",
                    question_hi: "<p>17. एक वस्तु के क्रय मूल्य और विक्रय मूल्य के बीच का अंतर ₹1,800 है। यदि 20% का लाभ होता है, तो वस्तु का क्रय मूल्य क्या है?</p>",
                    options_en: ["<p>₹7,000</p>", "<p>₹11,000</p>", 
                                "<p>₹8,000</p>", "<p>₹9,000</p>"],
                    options_hi: ["<p>₹7,000</p>", "<p>₹11,000</p>",
                                "<p>₹8,000</p>", "<p>₹9,000</p>"],
                    solution_en: "<p>17.(d) <br>Ratio - CP : SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5 : 6<br>Difference = 6 - 5 = 1 unit<br>1 unit = 1800<br>(CP) 5 units = 1800 &times; 5 = ₹ 9000</p>",
                    solution_hi: "<p>17.(d) <br>अनुपात - क्रय मूल्य : विक्रय मूल्य<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5 :&nbsp; 6<br>अंतर = 6 - 5 = 1 इकाई<br>1 इकाई = 1800<br>(क्रय मूल्य) 5 इकाई = 1800 &times; 5 = ₹ 9000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. Two trains of lengths 310 m and 330 m, respectively, are 160 m apart. They start moving towards each other on parallel tracks, at speeds 130 km/h and 158 km/h, respectively. In how much time (in seconds) will the trains cross each other?</p>",
                    question_hi: "<p>18. क्रमशः 310 m और 330 m लंबी दो रेलगाड़ियां एक-दूसरे से 160 m की दूरी पर हैं। वे समानांतर पटरियों पर क्रमशः 130 km/h और 158 km/h की चाल से एक दूसरे की ओर बढ़ना शुरू करती हैं। दोनों रेलगाड़ियां एक-दूसरे को कितने समय में (सेकंड में) पार करेंगी?</p>",
                    options_en: ["<p>10</p>", "<p>18</p>", 
                                "<p>8</p>", "<p>12</p>"],
                    options_hi: ["<p>10</p>", "<p>18</p>",
                                "<p>8</p>", "<p>12</p>"],
                    solution_en: "<p>18.(a) <br>Relative speed = 130 + 158 = 288 km/h or 80 m/s<br>Time = <math display=\"inline\"><mfrac><mrow><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>s</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math><br>Time = <math display=\"inline\"><mfrac><mrow><mn>310</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>330</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>160</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> = 10 seconds</p>",
                    solution_hi: "<p>18.(a) <br>सापेक्ष गति = 130 + 158 = 288 km/h या 80 m/s<br>समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#160;</mi></mrow><mi>&#2327;&#2340;&#2367;</mi></mfrac></math><br>समय = <math display=\"inline\"><mfrac><mrow><mn>310</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>330</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>160</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> = 10 सेकंड</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. A man spends 35% of his monthly income on food and four-thirteenths of the remaining income on transport. He incurs some other expenses, but also saves Rs.6,300 per month, the latter being equal to 20% of the balance remaining just after spending on food and transport. What is his monthly income (in Rs.)?</p>",
                    question_hi: "<p>19. एक आदमी अपनी मासिक आय का 35% भोजन पर और शेष आय का 4/13 भाग परिवहन पर खर्च करता है। वह कुछ अन्य खर्च भी करता है, लेकिन प्रति माह Rs.6,300 बचाता है, जो कि भोजन और परिवहन पर खर्च करने के बाद शेष राशि के 20% के बराबर है। उसकी मासिक आय (Rs. में) कितनी है?</p>",
                    options_en: ["<p>63000</p>", "<p>72,000</p>", 
                                "<p>67500</p>", "<p>70,000</p>"],
                    options_hi: ["<p>63000</p>", "<p>72,000</p>",
                                "<p>67500</p>", "<p>70,000</p>"],
                    solution_en: "<p>19.(d) <br>Let the total income = 100<math display=\"inline\"><mi>x</mi></math><br>E<math display=\"inline\"><mi>x</mi></math>penditure of food = 100x &times; 35% = 35x<br>Remaining amount = 100<math display=\"inline\"><mi>x</mi></math> - 35x = 65x<br>E<math display=\"inline\"><mi>x</mi></math>penditure of transport = 65x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>13</mn></mfrac></math> = 20x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>100</mn><mi>x</mi><mo>-</mo><mo>(</mo><mn>35</mn><mi>x</mi><mo>+</mo><mn>20</mn><mi>x</mi><mo>)</mo><mo>)</mo></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 6300<br>100<math display=\"inline\"><mi>x</mi></math> - 55x = 6300 &times; 5<br>45<math display=\"inline\"><mi>x</mi></math> = 6300 &times; 5, x = 700<br>So, his monthly income = 100 &times; 700 = 70000</p>",
                    solution_hi: "<p>19.(d) <br>माना कुल आय = 100<math display=\"inline\"><mi>x</mi></math><br>भोजन का व्यय = 100<math display=\"inline\"><mi>x</mi></math> &times; 35% = 35x<br>शेष राशि = 100<math display=\"inline\"><mi>x</mi></math> - 35x = 65x<br>परिवहन का व्यय = 65<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>13</mn></mfrac></math> = 20x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>100</mn><mi>x</mi><mo>-</mo><mo>(</mo><mn>35</mn><mi>x</mi><mo>+</mo><mn>20</mn><mi>x</mi><mo>)</mo><mo>)</mo></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 6300<br>100<math display=\"inline\"><mi>x</mi></math> - 55x = 6300 &times; 5<br>45<math display=\"inline\"><mi>x</mi></math> = 6300 &times; 5, x = 700<br>तो, उसकी मासिक आय = 100 &times; 700 = 70000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. L and M are the mid points of sides AB and AC of a triangle ABC, respectively, and BC = 18 cm. If LM || BC, then find the length of LM (in cm).</p>",
                    question_hi: "<p>20. L और M क्रमशः त्रिभुज ABC की भुजाओं AB और AC के मध्य बिंदु हैं, और BC = 18 cm है। यदि LM || BC है, तो LM की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>6</p>", "<p>9</p>", 
                                "<p>3</p>", "<p>12</p>"],
                    options_hi: ["<p>6</p>", "<p>9</p>",
                                "<p>3</p>", "<p>12</p>"],
                    solution_en: "<p>20.(b) <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcBVF_V2TVLrlZ5bwtCtSGwpNoEE-irgKZt3FHXRo25tcpeLiIMQn8FMOS2RiOI2gXTBYya9Dut5Uhi2Yz5Dz2iEWH8IcbmscF4B4r25CP4Vum_VNGOl4XdaH5USXFg4q368Nxs0Q?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"168\" height=\"151\"><br><strong>Thales theorem : -</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>L</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mi>M</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mi>M</mi></mrow><mn>18</mn></mfrac></math><br>LM = 9 cm</p>",
                    solution_hi: "<p>20.(b) <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcBVF_V2TVLrlZ5bwtCtSGwpNoEE-irgKZt3FHXRo25tcpeLiIMQn8FMOS2RiOI2gXTBYya9Dut5Uhi2Yz5Dz2iEWH8IcbmscF4B4r25CP4Vum_VNGOl4XdaH5USXFg4q368Nxs0Q?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"168\" height=\"151\"><br><strong>थेल्स प्रमेय :-</strong> <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">A</mi><mi mathvariant=\"bold-italic\">L</mi></mrow><mrow><mi mathvariant=\"bold-italic\">A</mi><mi mathvariant=\"bold-italic\">B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mi>M</mi></mrow><mrow><mi>B</mi><mi>C</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mi>M</mi></mrow><mn>18</mn></mfrac></math><br>LM = 9 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. In ∆ABC the straight line parallel to the side BC meets AB and AC at the points P and Q, respectively. If AP = QC, the length of AB is 12 cm and the length of AQ is 2 cm, then the length (in cm) of CQ is:</p>",
                    question_hi: "<p>21. △ABC में भुजा BC के समांतर सीधी रेखा, AB और AC से क्रमशः बिंदु P और Q पर मिलती है। यदि AP = QC है, AB की लंबाई 12 cm है और AQ की लंबाई 2 cm है, तो CQ की लंबाई (cm में) कितनी होगी?</p>",
                    options_en: ["<p>2</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>2</p>", "<p>6</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>21.(d) <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf43P9Kq2jFa156jOpPubzUBKhCr4jG-9ARg21CyZ0f2BgLIKwBlnk_ZDlgsE-YdDgq4v1ddK6K9GXALYilC4R_qWo-7nQZdtR4VSeH4YfS369ZJMb-8iWv1dCMagKnvkWd90STDg?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"164\" height=\"140\"><br><strong>Thales theorem : -</strong> <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">A</mi><mi mathvariant=\"bold-italic\">P</mi></mrow><mrow><mi mathvariant=\"bold-italic\">P</mi><mi mathvariant=\"bold-italic\">B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>Q</mi></mrow><mrow><mi>Q</mi><mi>C</mi></mrow></mfrac></math> <br>AP = QC = <math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>12</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi>X</mi></mfrac></math><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 24 - 2x<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 2x - 24 = 0<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 6x - 4x - 24 = 0<br><math display=\"inline\"><mi>x</mi><mo>(</mo><mi>x</mi></math> + 6) - 4(x + 6) = 0<br>&nbsp;(x-4)(x+6)<br><math display=\"inline\"><mi>x</mi></math> = 4, - 6 (can&rsquo;t take -ve value of x)</p>",
                    solution_hi: "<p>21.(d) <br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf43P9Kq2jFa156jOpPubzUBKhCr4jG-9ARg21CyZ0f2BgLIKwBlnk_ZDlgsE-YdDgq4v1ddK6K9GXALYilC4R_qWo-7nQZdtR4VSeH4YfS369ZJMb-8iWv1dCMagKnvkWd90STDg?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"164\" height=\"140\"><br><strong>थेल्स प्रमेय :- </strong><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">A</mi><mi mathvariant=\"bold-italic\">P</mi></mrow><mrow><mi mathvariant=\"bold-italic\">P</mi><mi mathvariant=\"bold-italic\">B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>Q</mi></mrow><mrow><mi>Q</mi><mi>C</mi></mrow></mfrac></math><br>AP = QC = <math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>12</mn><mo>-</mo><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi>X</mi></mfrac></math><br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 24 - 2x<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 2x - 24 = 0<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + 6x - 4x - 24 = 0<br><math display=\"inline\"><mi>x</mi><mo>(</mo><mi>x</mi></math> + 6) - 4(x + 6) = 0<br>&nbsp;(x-4)(x+6)<br><math display=\"inline\"><mi>x</mi></math> = 4, - 6 ( x का मान नकारात्मक नहीं ले सकते)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. The given pie-charts show the distribution of Graduate and Post-Graduate level students in five different colleges A, B, C, D and E.<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdBc0LK6oLQcQ2abrG_nO7F1Bna1J9dIRxBVam4mPgtDDc5ajuEPqMIu61nOYomxOKW8-aIqw1gomDHyAKnb7QqFaExLqDgxC930mAK27O0oh0anzfV14CUJz6Mn9AKHxlARkG3SA?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"300\" height=\"199\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf2tCaArUP52y3-bidYsVGVTDTDFLbyX4SU90EWfxB-_MibUqq_x2BY5tGnzVjqdUG01_YBvCRVkWpdiG2frs_Ewv-kMSKwhxuxfZ1_Olh4ZNEqOe1whF4wXeMAMSgZVgoiawkP?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"306\" height=\"192\"><br>How many students of colleges A and B are studying at Graduate level?</p>",
                    question_hi: "<p>22. दिए गए पाई-चार्ट पांच अलग-अलग महावि&zwnj;द्यालयों A, B, C, D और E में स्नातक और स्नातकोत्तर स्तर के छात्रों के वितरण को दर्शाते हैं।<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcVoLDqHY-3r3vpzKYeVYHSz22dtT-o9PV8oAE-cD40bF2iZyyZ4XrRsRDsI0LsS6eVupd3jqRx5V55WhkHXD93wnWPk41-4k3tu7LwaZwzV9rdGMRxIbc_Aq55RqWsASj93ls-fA?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"298\" height=\"174\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdCBrbBi4xXqSLjLfIvPx5F4FBoZJFwNejjpPmr4cRbjBWlBA5gOMTMz1VaUQb4mMfUb7wE9wqbgUoqEaGO3sqGHdFTeLxK7IahmwgaXpxL6HMTqcv05_4zUmEvQaelXcyYitpv?key=E0dyhx90QJ08so0M4Fuf0fDL\" width=\"305\" height=\"201\"><br>महाविद्यालय A और B के कितने छात्र स्नातक स्तर पर अध्ययन कर रहे हैं?</p>",
                    options_en: ["<p>5,987</p>", "<p>4,520</p>", 
                                "<p>6,993</p>", "<p>7,052</p>"],
                    options_hi: ["<p>5,987</p>", "<p>4,520</p>",
                                "<p>6,993</p>", "<p>7,052</p>"],
                    solution_en: "<p>22.(c)<br>Students who studying at graduate level in both collage = 18% + 19% = 37%<br>100% = 18900<br>37% = 189 &times; 37 = 6993</p>",
                    solution_hi: "<p>22.(c)<br>दोनों महाविद्यालयों में स्नातक स्तर पर अध्ययन करने वाले छात्र = 18% + 19% = 37%<br>100% = 18900<br>37% = 189 &times; 37 = 6993</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. The triangle has sides 3 cm, 4 cm and 5 cm. What is the length of the perpendicular from the opposite vertex to the side whose length is 5 cm?</p>",
                    question_hi: "<p>23. एक त्रिभुज की भुजाएं 3 cm, 4 cm और 5 cm हैं। 5 cm लंबाई वाली भुजा पर विपरीत शीर्ष से डाले गए लंब की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>3.5 cm</p>", "<p>2.4 cm</p>", 
                                "<p>2.2 cm</p>", "<p>1.4 cm</p>"],
                    options_hi: ["<p>3.5 cm</p>", "<p>2.4 cm</p>",
                                "<p>2.2 cm</p>", "<p>1.4 cm</p>"],
                    solution_en: "<p>23.(b)<br>3, 4, 5 are triplet of right angle triangle so,<br>Length of the perpendicular = <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>b</mi></mrow><mrow><mi>h</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>5</mn></mfrac></math> = 2.4 cm</p>",
                    solution_hi: "<p>23.(b)<br>3, 4, 5 समकोण त्रिभुज के त्रिक हैं इसलिए,<br>लम्ब की लंबाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#160;</mi></mrow><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>5</mn></mfrac></math>= 2.4 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. In June, Rohit&rsquo;s bank account balance is ₹5,000 for 25 days, ₹20,000 for 2 days and ₹1,500 for 3 days. What is the average balance (in ₹) in Rohit\'s bank account in June?</p>",
                    question_hi: "<p>24. जून में, रोहित के बैंक खाते का बैलेंस 25 दिनों के लिए ₹5,000, 2 दिनों के लिए ₹20,000 और 3 दिनों के लिए ₹1,500 है। जून में रोहित के बैंक खाते में औसत बैलेंस (₹ में) कितना है?</p>",
                    options_en: ["<p>5650</p>", "<p>5575</p>", 
                                "<p>6000</p>", "<p>5200</p>"],
                    options_hi: ["<p>5650</p>", "<p>5575</p>",
                                "<p>6000</p>", "<p>5200</p>"],
                    solution_en: "<p>24.(a)<br>Average balance = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 5650</p>",
                    solution_hi: "<p>24.(a)<br>औसत बैलेंस = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math>&nbsp;= 5650</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. The radius and height of a cylinder are in the ratio 6 : 7 and its volume is 792 cm<sup>3</sup> . Calculate its curved surface area in cm<sup>2</sup></p>",
                    question_hi: "<p>25. एक बेलन की त्रिज्या और ऊंचाई का अनुपात 6 : 7 है और इसका आयतन 792 cm&sup3; है। इसके वक्र पृष्ठीय क्षेत्रफल की गणना cm&sup2; में कीजिए।</p>",
                    options_en: ["<p>262</p>", "<p>490</p>", 
                                "<p>264</p>", "<p>226</p>"],
                    options_hi: ["<p>262</p>", "<p>490</p>",
                                "<p>264</p>", "<p>226</p>"],
                    solution_en: "<p>25.(c) <br>Volume of the cylinder = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math><br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; (6x)<sup>2</sup>(7x) = 792<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>792</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#215;</mo><mn>36</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> = 1 so x = 1<br>So, the radius and height of the cylinder will be 6 and 7<br>Now, according to the question,<br>CSA of the cylinder = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>h</mi></math><br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6 &times; 7 <br>= 264 cm<sup>2</sup></p>",
                    solution_hi: "<p>25.(c) <br>बेलन का आयतन = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math><br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; (6x)<sup>2</sup>(7x) = 792<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>792</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#215;</mo><mn>36</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> = 1 इसलिए x = 1<br>तो, बेलन की त्रिज्या और ऊंचाई 6 और 7 होगी<br>अब, प्रश्न के अनुसार,<br>बेलन का वक्र पृष्ठीय क्षेत्रफल = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>h</mi></math> = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6 &times; 7 = 264 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>