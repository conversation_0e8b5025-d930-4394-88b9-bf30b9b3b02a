<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">State whether the following statements are true or false.</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(i) In MS Word, all paragraphs must have same line spacing.</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(ii) In MS Word, all paragraphs must have same indentation.</span></p>\\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">&#2348;&#2340;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2354;&#2340;&#2404;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(i) MS Word &#2350;&#2375;&#2306;, &#2360;&#2349;&#2368; &#2346;&#2376;&#2352;&#2366;&#2327;&#2381;&#2352;&#2366;&#2347; &#2350;&#2375;&#2306; &#2360;&#2350;&#2366;&#2344; &#2354;&#2366;&#2311;&#2344; &#2360;&#2381;&#2346;&#2375;&#2360;&#2367;&#2306;&#2327; &#2361;&#2379;&#2344;&#2368; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404;</span></p>\\r\\n<p><span style=\"font-weight: 400;\">(ii) MS Word &#2350;&#2375;&#2306;, &#2360;&#2349;&#2368; &#2346;&#2376;&#2352;&#2366;&#2327;&#2381;&#2352;&#2366;&#2347;&#2379;&#2306; &#2350;&#2375;&#2306; &#2319;&#2325; &#2361;&#2368; &#2311;&#2306;&#2337;&#2375;&#2306;&#2335;&#2375;&#2358;&#2344; &#2361;&#2379;&#2344;&#2366; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>(i) True, (ii) True</p>\\n", "<p>(i) True, (ii) False</p>\\n", 
                                "<p>(i) False, (ii) True</p>\\n", "<p>(i) False, (ii) False</p>\\n"],
                    options_hi: ["<p>(i) <span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\">, (ii) </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;</span><span style=\"font-family: Cambria Math;\">&#2368;</span></p>\\n", "<p>(i) <span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\">, (ii) </span><span style=\"font-family: Cambria Math;\">&#2327;&#2354;&#2340;</span></p>\\n",
                                "<p>(i) <span style=\"font-family: Cambria Math;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\">, (ii) </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span></p>\\n", "<p>(i) <span style=\"font-family: Cambria Math;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\">, (ii) </span><span style=\"font-family: Cambria Math;\">&#2327;&#2354;&#2340;</span></p>\\n"],
                    solution_en: "<p>1.(d)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Line spacing</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>determines the amount of vertical space between lines of text in a paragraph. Paragraph spacing determines the amount of space above or below a paragra</span><span style=\"font-family: Cambria Math;\">ph. </span><strong><span style=\"font-family: Cambria Math;\">Indenting</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>text adds structure to your document by allowing you to separate information. </span></p>\\n",
                    solution_hi: "<p>1.(d)<span style=\"font-family: Cambria Math;\"> <strong>&#2354;&#2366;&#2311;&#2344; &#2360;&#2381;&#2346;&#2375;&#2360;&#2367;&#2306;&#2327;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; &#2350;&#2375;&#2306; &#2346;&#2366;&#2336; &#2325;&#2368; &#2346;&#2306;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2354;&#2306;&#2348;&#2357;&#2340; &#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2368; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366; &#2344;&#2367;&#2352;&#2381;&#2343;&#2366;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2346;&#2376;&#2352;&#2366;&#2327;&#2381;&#2352;&#2366;&#2347; &#2360;&#2381;&#2346;&#2375;&#2360;&#2367;&#2306;&#2327;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; &#2325;&#2375; &#2314;&#2346;&#2352; &#2351;&#2366; &#2344;&#2368;&#2330;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2368; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366; &#2344;&#2367;&#2352;&#2381;&#2343;&#2366;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2311;&#2306;&#2337;&#2375;&#2306;&#2335;&#2367;&#2306;&#2327; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335;</strong><span style=\"font-weight: 400;\"> &#2310;&#2346;&#2325;&#2379; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2325;&#2379; &#2309;&#2354;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2325;&#2352; &#2310;&#2346;&#2325;&#2375; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2350;&#2375;&#2306; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2332;&#2379;&#2337;&#2364;&#2340;&#2366; &#2361;&#2376;&#2404;</span></span></p>\\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: " <p>2. </span><span style=\"font-family:Cambria Math\">SMTP is an email-related protocol. What is the full form of SMTP?</span></p>",
                    question_hi: " <p>2. </span><span style=\"font-family:Cambria Math\">SMTP </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ईमेल</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Cambria Math\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> SMTP </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">फुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">फॉर्म</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">क्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Synchronous Mail Transfer Protocol </span></p>", " <p> Synchronous Mail Transition Protocol </span></p>", 
                                " <p> Simple Mail Transition Protocol </span></p>", " <p> Simple Mail Transfer Protocol </span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">सिंक्रोनस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ट्रांसफर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प</span><span style=\"font-family:Cambria Math\">्रोटोकॉल</span><span style=\"font-family:Cambria Math\">  </span></p>", " <p> </span><span style=\"font-family:Cambria Math\">सिंक्रोनस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ट्रांजीशन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\">  </span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">सिंपल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ट्रांजीशन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\">  </span></p>", " <p> </span><span style=\"font-family:Cambria Math\">सिंपल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ट्रांसफर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\">  </span></p>"],
                    solution_en: " <p>2.(d)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">SMTP or Simple Mail Transfer Protocol is an application that is used to send, receive, and relay outgoing emai</span><span style=\"font-family:Cambria Math\">ls between senders and receivers. Hypertext Transfer Protocol (HTTP) is an application-layer protocol for transmitting hypermedia documents, such as HTML.</span></p>",
                    solution_hi: " <p>2.(d) </span><span style=\"font-family:Cambria Math\">SMTP </span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सिंपल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ट्रांसफर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Cambria Math\">Simple Mail Transfer Protocol</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ऐसा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एप्लिकेशन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ह</span><span style=\"font-family:Cambria Math\">ै</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जिसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रेषक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्राप्तकर्ता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बीच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आउटगोइंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ईमेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भेजने</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">प्राप्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">रिले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हाइपरटेक्स्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ट्रांसफर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\"> (HTTP) </span><span style=\"font-family:Cambria Math\">हाइपरमीडिया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दस्तावेज़ों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रसारित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एप्लिकेशन</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Cambria Math\">लेयर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रोटोकॉल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कि</span><span style=\"font-family:Cambria Math\"> HTML</span><span style=\"font-family:Cambria Math\">।</span></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: " <p>3. </span><span style=\"font-family:Cambria Math\">Whic</span><span style=\"font-family:Cambria Math\">h function is used for setting facing pages in MS Word 2007 document?</span></p>",
                    question_hi: " <p>3. </span><span style=\"font-family:Cambria Math\">MS Word 2007 </span><span style=\"font-family:Cambria Math\">दस्तावेज़</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">फेसिंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पेज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">फ़ंक्शन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> Default margins  </span></p>", " <p> Custom margins  </span></p>", 
                                " <p> Mirror margins </span></p>", " <p> Wider margins</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">डिफ़ॉल्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मार्जिन</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">कस्टम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मार्जिन</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">मिरर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मार्जिन</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">वाईडर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मार्जिन</span></p>"],
                    solution_en: " <p>3.(c) </span><span style=\"font-family:Cambria Math\">Mirror margins are used for setting facing pages in MS Word 2007 document. </span><span style=\"font-family:Cambria Math\">Margins are the blank spaces that line the top, bottom, and left and right sides of a document. </span><span style=\"font-family:Cambria Math\">Default </span><span style=\"font-family:Cambria Math\">margin is the empty space between a document\'s contents and the edges of the page. </span></p>",
                    solution_hi: " <p>3.(c)</span><span style=\"font-family:Cambria Math\"> MS </span><span style=\"font-family:Cambria Math\">वर्ड</span><span style=\"font-family:Cambria Math\"> 2007 </span><span style=\"font-family:Cambria Math\">दस्तावेज़</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">फेसिंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पेज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">म</span><span style=\"font-family:Cambria Math\">िरर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मार्जिन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मार्जिन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">रिक्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">स्थान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दस्तावेज़</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ऊपर</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">नीचे</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बाएँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दाएँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पक्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पंक्तिबद्ध</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">डिफ़ॉल्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मार्जिन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दस्तावेज़</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सामग्री</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पृष्ठ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किनारों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बीच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">खाली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जगह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: " <p>4. </span><span style=\"font-family:Cambria Math\">What will be the result of th</span><span style=\"font-family:Cambria Math\">e following MS Excel formula?</span></p> <p><span style=\"font-family:Cambria Math\">=FLOOR(5, 3)</span></p>",
                    question_hi: " <p>4. </span><span style=\"font-family:Cambria Math\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> MS Excel </span><span style=\"font-family:Cambria Math\">सूत्र</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">परिणाम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">क्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">होगा</span><span style=\"font-family:Cambria Math\">?</span></p> <p><span style=\"font-family:Cambria Math\">=FLOOR(5, 3)</span></p>",
                    options_en: [" <p>  5 </span></p>", " <p>  0 </span></p>", 
                                " <p>  4 </span></p>", " <p>  3</span></p>"],
                    options_hi: [" <p> 5 </span></p>", " <p> 0 </span></p>",
                                " <p> 4 </span></p>", " <p> 3</span></p>"],
                    solution_en: " <p>4.(d) </span><span style=\"font-family:Cambria Math\">The Excel FLOOR function rounds a given number down to the </span><span style=\"font-family:Cambria Math\">nearest specified multiple. </span><span style=\"font-family:Cambria Math\">FLOOR (5, 3) =  3</span></p>",
                    solution_hi: " <p>4.(d) </span><span style=\"font-family:Cambria Math\">एक्सेल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">फ़्लोर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">फ़ंक्शन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नंबर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निकटतम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निर्दिष्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मल्टीपल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">राउंड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">।</span></p> <p><span style=\"font-family:Cambria Math\">FLOOR (5, 3) =  3</span></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Which of the following MS Word features can be used to draw a straight line across selected text in a document?</span></p>\\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> MS Word </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2351;</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;&#2343;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2368;&#2306;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Format Painter</p>\\n", "<p>Eraser</p>\\n", 
                                "<p>Strikethrough</p>\\n", "<p>Replace</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2367;&#2340;&#2381;&#2352;&#2325;&#2366;&#2352;</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">&#2311;&#2352;&#2375;&#2332;&#2364;&#2352;</span></p>\\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2381;&#2352;&#2366;&#2311;&#2325;&#2341;&#2381;&#2352;&#2370;</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">&#2348;&#2342;&#2354;&#2375;&#2306;</span></p>\\n"],
                    solution_en: "<p>5.(c) <strong>Strikethrough</strong><span style=\"font-weight: 400;\">&nbsp; features can be used to draw a straight line across selected text in a document. </span><span style=\"font-weight: 400;\">The </span><strong>Format Painter</strong><span style=\"font-weight: 400;\"> tool is used to copy and paste character and paragraph formats to existing text.&nbsp; </span><strong>Find and Replace</strong><span style=\"font-weight: 400;\"> in Word is a tool that searches a document for a specific word or phrase.</span></p>\\n",
                    solution_hi: "<p>5.(c) <span style=\"font-weight: 400;\">&#2325;&#2367;&#2360;&#2368; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2350;&#2375;&#2306; &#2330;&#2351;&#2344;&#2367;&#2340; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2346;&#2352; &#2319;&#2325; &#2360;&#2368;&#2343;&#2368; &#2352;&#2375;&#2326;&#2366; &#2326;&#2368;&#2306;&#2330;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; </span><strong>&#2360;&#2381;&#2335;&#2381;&#2352;&#2366;&#2311;&#2325;&#2341;&#2381;&#2352;&#2370;</strong><span style=\"font-weight: 400;\"> &#2360;&#2369;&#2357;&#2367;&#2343;&#2366;&#2323;&#2306; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335; &#2346;&#2375;&#2306;&#2335;&#2352;</strong><span style=\"font-weight: 400;\"> &#2335;&#2370;&#2354; &#2325;&#2366; &#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354; &#2350;&#2380;&#2332;&#2370;&#2342;&#2366; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2350;&#2375;&#2306; &#2325;&#2376;&#2352;&#2375;&#2325;&#2381;&#2335;&#2352; &#2324;&#2352; &#2346;&#2376;&#2352;&#2366;&#2327;&#2381;&#2352;&#2366;&#2347; &#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335; &#2325;&#2379; &#2325;&#2377;&#2346;&#2368; &#2324;&#2352; &#2346;&#2375;&#2360;&#2381;&#2335; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2357;&#2352;&#2381;&#2337; &#2350;&#2375;&#2306; </span><strong>&#2347;&#2366;&#2311;&#2306;&#2337; &#2319;&#2306;&#2337; &#2352;&#2367;&#2346;&#2381;&#2354;&#2375;&#2360;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2313;&#2346;&#2325;&#2352;&#2339; &#2361;&#2376; &#2332;&#2379; &#2325;&#2367;&#2360;&#2368; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2358;&#2348;&#2381;&#2342; &#2351;&#2366; &#2357;&#2366;&#2325;&#2381;&#2351;&#2366;&#2306;&#2358; &#2325;&#2375; &#2354;&#2367;&#2319; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2326;&#2379;&#2332;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">What is the default \'view\' option of an MS Word document? </span></p>\\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">MS Word </span><span style=\"font-family: Cambria Math;\">&#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2347;&#2364;&#2377;&#2354;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> \'</span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2370;</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Web layout</p>\\n", "<p>Draft</p>\\n", 
                                "<p>Outline</p>\\n", "<p>Print layout</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2310;&#2313;&#2335;</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">&#2337;&#2381;&#2352;&#2366;&#2347;&#2381;&#2335;</span></p>\\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2310;&#2313;&#2335;&#2354;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2367;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2310;&#2313;&#2335;</span></p>\\n"],
                    solution_en: "<p>6.(d)<strong>&nbsp;Print Layout</strong><span style=\"font-weight: 400;\"> is the default &lsquo;view&rsquo; option of a MS Word document. </span><strong>Web Layout</strong><span style=\"font-weight: 400;\"> view is designed to allow you to easily see how your documents will look if used in an online environment.</span><strong> Draft view</strong><span style=\"font-weight: 400;\"> can be considered a \"pared down\" version of the Print Layout view. The</span><strong> Outline view</strong><span style=\"font-weight: 400;\"> shows the different levels of headings as designated by the styles applied in your document.</span></p>\\n",
                    solution_hi: "<p>6.(d)<strong>&nbsp;&#2346;&#2381;&#2352;&#2367;&#2306;&#2335; &#2354;&#2375;&#2310;&#2313;&#2335;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2319;&#2350;&#2319;&#2360; &#2357;&#2352;&#2381;&#2337; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2325;&#2366; &#2337;&#2367;&#2347;&#2364;&#2377;&#2354;&#2381;&#2335; \'&#2357;&#2381;&#2351;&#2370;\' &#2357;&#2367;&#2325;&#2354;&#2381;&#2346; &#2361;&#2376;&#2404; </span><strong>&#2357;&#2375;&#2348; &#2354;&#2375;&#2310;&#2313;&#2335;</strong><span style=\"font-weight: 400;\"> &#2342;&#2371;&#2358;&#2381;&#2351; &#2310;&#2346;&#2325;&#2379; &#2310;&#2360;&#2366;&#2344;&#2368; &#2360;&#2375; &#2342;&#2375;&#2326;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2367;&#2332;&#2364;&#2366;&#2311;&#2344; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2325;&#2367; &#2321;&#2344;&#2354;&#2366;&#2311;&#2344; &#2357;&#2366;&#2340;&#2366;&#2357;&#2352;&#2339; &#2350;&#2375;&#2306; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2319; &#2332;&#2366;&#2344;&#2375; &#2346;&#2352; &#2310;&#2346;&#2325;&#2375; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2325;&#2376;&#2360;&#2375; &#2342;&#2367;&#2326;&#2375;&#2306;&#2327;&#2375;&#2404;</span><strong> &#2337;&#2381;&#2352;&#2366;&#2347;&#2381;&#2335; &#2357;&#2381;&#2351;&#2370;</strong><span style=\"font-weight: 400;\"> &#2325;&#2379; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335; &#2354;&#2375;&#2310;&#2313;&#2335; &#2357;&#2381;&#2351;&#2370; &#2325;&#2366; \"&#2346;&#2376;&#2352;&#2375;&#2337; &#2337;&#2366;&#2313;&#2344;\" &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; &#2350;&#2366;&#2344;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2310;&#2313;&#2335;&#2354;&#2366;&#2311;&#2344; &#2357;&#2381;&#2351;&#2370;</strong><span style=\"font-weight: 400;\"> &#2310;&#2346;&#2325;&#2375; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2350;&#2375;&#2306; &#2354;&#2366;&#2327;&#2370; &#2358;&#2376;&#2354;&#2367;&#2351;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2344;&#2367;&#2352;&#2381;&#2342;&#2367;&#2359;&#2381;&#2335; &#2358;&#2368;&#2352;&#2381;&#2359;&#2325;&#2379;&#2306; &#2325;&#2375; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2360;&#2381;&#2340;&#2352;&#2379;&#2306; &#2325;&#2379; &#2342;&#2367;&#2326;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Which of the following appears at the bottom of a worksheet in MS Excel?</span></p>\\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">MS Excel </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2326;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Horizontal scrollbar</p>\\n", "<p>Vertical scrollbar</p>\\n", 
                                "<p>Formula bar</p>\\n", "<p>Title bar</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2376;&#2340;&#2367;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2381;&#2352;&#2377;&#2354;&#2348;&#2366;&#2352;</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2357;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2381;&#2352;&#2377;&#2354;&#2348;&#2366;&#2352;</span></p>\\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2347;&#2377;&#2352;&#2381;&#2350;&#2370;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2352;</span></p>\\n", "<p><span style=\"font-family: Cambria Math;\">&#2335;&#2366;&#2311;&#2335;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2352;</span></p>\\n"],
                    solution_en: "<p>7.(a)<span style=\"font-family: Cambria Math;\"> <strong>Horizontal scrollbar</strong><span style=\"font-weight: 400;\">&nbsp; appears at the bottom of a worksheet in MS Excel. </span><span style=\"font-weight: 400;\">A </span><strong>vertical scroll bar </strong><span style=\"font-weight: 400;\">enables the user to scroll the content up or down. The </span><strong>Formula Bar</strong><span style=\"font-weight: 400;\"> in Excel sits directly above the worksheet area, to the right of the Name Box. </span><strong>Title Bar</strong><span style=\"font-weight: 400;\"> lies next to the quick access toolbar or on top of the excel window. It displays the name of the open document.</span></span></p>\\n",
                    solution_hi: "<p>7.(a)<span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">MS &#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2350;&#2375;&#2306; &#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335; &#2325;&#2375; &#2344;&#2368;&#2330;&#2375; </span><strong>&#2325;&#2381;&#2359;&#2376;&#2340;&#2367;&#2332; &#2360;&#2381;&#2325;&#2381;&#2352;&#2377;&#2354;&#2348;&#2366;&#2352; (Horizontal) </strong><span style=\"font-weight: 400;\">&#2342;&#2367;&#2326;&#2366;&#2312; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2319;&#2325; </span><strong>&#2354;&#2306;&#2348;&#2357;&#2340; (Vertical) &#2360;&#2381;&#2325;&#2381;&#2352;&#2377;&#2354; &#2348;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2325;&#2379; &#2360;&#2366;&#2350;&#2327;&#2381;&#2352;&#2368; &#2325;&#2379; &#2314;&#2346;&#2352; &#2351;&#2366; &#2344;&#2368;&#2330;&#2375; &#2360;&#2381;&#2325;&#2381;&#2352;&#2377;&#2354; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2325;&#2381;&#2359;&#2350; &#2348;&#2344;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2350;&#2375;&#2306;</span><strong> &#2347;&#2377;&#2352;&#2381;&#2350;&#2370;&#2354;&#2366; &#2348;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> &#2344;&#2366;&#2350; &#2348;&#2377;&#2325;&#2381;&#2360; &#2325;&#2375; &#2342;&#2366;&#2312;&#2306; &#2323;&#2352; &#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2375; &#2336;&#2368;&#2325; &#2314;&#2346;&#2352; &#2348;&#2376;&#2336;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2335;&#2366;&#2311;&#2335;&#2354; &#2348;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> &#2325;&#2381;&#2357;&#2367;&#2325; &#2319;&#2325;&#2381;&#2360;&#2375;&#2360; &#2335;&#2370;&#2354;&#2348;&#2366;&#2352; &#2325;&#2375; &#2348;&#2327;&#2354; &#2350;&#2375;&#2306; &#2351;&#2366; &#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2357;&#2367;&#2306;&#2337;&#2379; &#2325;&#2375; &#2358;&#2368;&#2352;&#2381;&#2359; &#2346;&#2352; &#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2376;&#2404; &#2351;&#2361; &#2326;&#2369;&#2354;&#2375; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2325;&#2366; &#2344;&#2366;&#2350; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></span></p>\\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">How many cells will be selected when you select C9 cell, press the \'Ctrl\' key, and then click </span><span style=\"font-family: Cambria Math;\">on an A1 cell in an MS Excel worksheet?</span></p>\\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2346;</span><span style=\"font-family: Cambria Math;\"> C9 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2369;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2319;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">, \'Ctrl\' </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2306;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2348;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2354;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306; </span><span style=\"font-family: Cambria Math;\">MS Excel </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> A1 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>1</p>\\n", "<p>12</p>\\n", 
                                "<p>27</p>\\n", "<p>2</p>\\n"],
                    options_hi: ["<p>1</p>\\n", "<p>12</p>\\n",
                                "<p>27</p>\\n", "<p>2</p>\\n"],
                    solution_en: "<p>8.(d) <strong>Two cells </strong><span style=\"font-family: Cambria Math;\">will be selected when you select C9 cell, press the \'Ctrl\' key, and then click </span><span style=\"font-family: Cambria Math;\">on an A1 cell in an MS Excel worksheet. </span></p>\\n",
                    solution_hi: "<p>8.(d) <span style=\"font-family: Cambria Math;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2346;</span><span style=\"font-family: Cambria Math;\"> C9 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2369;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2319;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">, \'Ctrl\' </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2306;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2348;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> MS </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> A1 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2354;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">What is the keyboard shortcut to go to the beginning of an MS Word document? </span></p>\\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">MS Word </span><span style=\"font-family: Cambria Math;\">&#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2369;&#2352;&#2369;&#2310;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Alt + Ctrl + End</p>\\n", "<p>Ctrl + Home</p>\\n", 
                                "<p>Ctr<span style=\"font-family: Cambria Math;\">l + End </span></p>\\n", "<p>Alt + Home</p>\\n"],
                    options_hi: ["<p>Alt + Ctrl + End</p>\\n", "<p>Ctrl + Home</p>\\n",
                                "<p>Ctrl + End</p>\\n", "<p>Alt + Home</p>\\n"],
                    solution_en: "<p>9.(b)&nbsp; <strong>Ctrl + Home</strong><span style=\"font-weight: 400;\"> is the keyboard shortcut to go to the beginning of an MS Word document.&nbsp;</span><strong>Ctrl+Alt+End</strong><span style=\"font-weight: 400;\"> is a keyboard shortcut used in a Remote Desktop Session to display the security dialog box. </span><strong>Ctrl + End</strong><span style=\"font-weight: 400;\"> moves the cursor to the end of the document. </span><strong>Alt+Home</strong><span style=\"font-weight: 400;\"> is a keyboard shortcut most often used to open the homepage in the active tab. </span></p>\\n",
                    solution_hi: "<p>9.(b)&nbsp; <strong>Ctrl + Home</strong><span style=\"font-weight: 400;\"> MS &#2357;&#2352;&#2381;&#2337; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2325;&#2368; &#2358;&#2369;&#2352;&#2369;&#2310;&#2340; &#2350;&#2375;&#2306; &#2332;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337; &#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335; &#2361;&#2376;&#2404; </span><strong>Ctrl+Alt+End</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337; &#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2352;&#2367;&#2350;&#2379;&#2335; &#2337;&#2375;&#2360;&#2381;&#2325;&#2335;&#2377;&#2346; &#2360;&#2340;&#2381;&#2352; &#2350;&#2375;&#2306; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; &#2360;&#2306;&#2357;&#2366;&#2342; &#2348;&#2377;&#2325;&#2381;&#2360; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>Ctrl + End</strong><span style=\"font-weight: 400;\"> &#2325;&#2352;&#2381;&#2360;&#2352; &#2325;&#2379; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2325;&#2375; &#2309;&#2306;&#2340; &#2350;&#2375;&#2306; &#2354;&#2375; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; </span><strong>Alt+Home</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337; &#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2309;&#2325;&#2381;&#2360;&#2352; &#2360;&#2325;&#2381;&#2352;&#2367;&#2351; &#2335;&#2376;&#2348; &#2350;&#2375;&#2306; &#2350;&#2369;&#2326;&#2346;&#2371;&#2359;&#2381;&#2336; &#2326;&#2379;&#2354;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">Which of the following keyboard shortcuts is used to close a curre</span><span style=\"font-family: Cambria Math;\">ntly open tab in a Chrome browser?</span></p>\\n",
                    question_hi: "<p>10. <span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2379;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2369;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2376;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Alt + C</p>\\n", "<p>Ctrl + W</p>\\n", 
                                "<p>Alt + W</p>\\n", "<p>Ctrl + C</p>\\n"],
                    options_hi: ["<p>Alt + C</p>\\n", "<p>Ctrl + W</p>\\n",
                                "<p>Alt + W</p>\\n", "<p>Ctrl + C</p>\\n"],
                    solution_en: "<p>10.(b) <strong>Ctrl + W </strong><span style=\"font-weight: 400;\">&nbsp;is used to close a currently open tab in a Chrome browser.</span><strong> </strong><strong>Alt+C </strong><span style=\"font-weight: 400;\">is a keyboard shortcut most often used to view the favorites in Internet Explorer. </span><strong>Alt+W</strong><span style=\"font-weight: 400;\"> is a keyboard shortcut most often used to open the View tab in the Office programs with the Ribbon. </span><strong>Ctrl + C</strong><span style=\"font-weight: 400;\"> is used to copy text or images.</span></p>\\n",
                    solution_hi: "<p>10.(b) <span style=\"font-weight: 400;\">&nbsp;&#2325;&#2381;&#2352;&#2379;&#2350; &#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352; &#2350;&#2375;&#2306; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2350;&#2375;&#2306; &#2326;&#2369;&#2354;&#2375; &#2335;&#2376;&#2348; &#2325;&#2379; &#2348;&#2306;&#2342; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; </span><strong>Ctrl + W </strong><span style=\"font-weight: 400;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>Alt+C </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337; &#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2309;&#2325;&#2381;&#2360;&#2352; &#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2319;&#2325;&#2381;&#2360;&#2346;&#2381;&#2354;&#2379;&#2352;&#2352; &#2350;&#2375;&#2306; &#2346;&#2360;&#2306;&#2342;&#2368;&#2342;&#2366; &#2342;&#2375;&#2326;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> Alt+W</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337; &#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2309;&#2325;&#2381;&#2360;&#2352; &#2352;&#2367;&#2348;&#2344; &#2325;&#2375; &#2360;&#2366;&#2341; &#2321;&#2347;&#2367;&#2360; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2350;&#2375;&#2306; &#2357;&#2381;&#2351;&#2370; &#2335;&#2376;&#2348; &#2326;&#2379;&#2354;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> Ctrl+C</strong><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2351;&#2366; &#2311;&#2350;&#2375;&#2332; &#2325;&#2379; &#2325;&#2377;&#2346;&#2368; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>