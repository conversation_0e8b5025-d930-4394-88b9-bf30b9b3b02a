<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Times New Roman\">1. No sooner had Kavya started her online class that the web connection was lost.     </span></p>",
                    question_hi: "",
                    options_en: [" <p> started her online class  </span></p>", " <p> that the web connection </span></p>", 
                                " <p> No sooner had Kavya    </span></p>", " <p> was lost </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>1.(b) “No sooner____ than” is a fixed conjunction pair. Hence, </span><span style=\"font-family:Times New Roman\">that</span><span style=\"font-family:Times New Roman\"> will be replaced with </span><span style=\"font-family:Times New Roman\">than </span><span style=\"font-family:Times New Roman\"> to make the sentence grammatically correct.</span></p>",
                    solution_hi: " <p>1.(b) “No sooner____ than” is a fixed conjunction pair. Hence, </span><span style=\"font-family:Times New Roman\">that</span><span style=\"font-family:Times New Roman\"> will be replaced with </span><span style=\"font-family:Times New Roman\">than </span><span style=\"font-family:Times New Roman\"> to make the sentence grammatically correct.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Times New Roman\">2. I am sorry I haven’t learnt the poem at heart. </span></p>",
                    question_hi: "",
                    options_en: [" <p> I haven’t </span></p>", " <p> learnt the poem </span></p>", 
                                " <p> I am sorry </span></p>", " <p> at heart </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>2.(d) We learn something </span><span style=\"font-family:Times New Roman\">by heart.</span><span style=\"font-family:Times New Roman\"> So, it is incorrect to say I learn poems at heart. This error is related to phrasal verbs.</span></p>",
                    solution_hi: " <p>2.(d) We learn something </span><span style=\"font-family:Times New Roman\">by heart.</span><span style=\"font-family:Times New Roman\"> So, it is incorrect to say I learn poems at heart. This error is related to phrasal verbs.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Times New Roman\">3. Bhanu finished </span><span style=\"font-family:Times New Roman\">to read</span><span style=\"font-family:Times New Roman\"> such a thick book in just two days.     </span></p>",
                    question_hi: "",
                    options_en: [" <p> such a thick book </span></p>", " <p> Bhanu finished </span></p>", 
                                " <p> to read </span></p>", " <p> in just two day </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>3.(c) The correct sentence structure using Gerund and Infinitive is as follows:</span></p> <p><span style=\"font-family:Times New Roman\">Gerund: Verb + ing (eating, going, studying)</span></p> <p><span style=\"font-family:Times New Roman\">Infinitive: To + Base Verb (to eat, to go, to study)</span></p> <p><span style=\"font-family:Times New Roman\">Similarly in this sentence,  Bhanu finished(Verb) + reading(Verb + Ing) will be the correct sentence structure.</span></p>",
                    solution_hi: " <p>3.(c) The correct sentence structure using Gerund and Infinitive is as follows:</span></p> <p><span style=\"font-family:Times New Roman\">Gerund: Verb + ing (eating, going, studying)</span></p> <p><span style=\"font-family:Times New Roman\">Infinitive: To + Base Verb (to eat, to go, to study)</span></p> <p><span style=\"font-family:Times New Roman\">Similarly in this sentence,  Bhanu finished(Verb) + reading(Verb + Ing) will be the correct sentence structure.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Times New Roman\">4. A large amount of money he invested were wasted. </span></p>",
                    question_hi: "",
                    options_en: [" <p> A large amount </span></p>", " <p> he invested </span></p>", 
                                " <p> were wasted </span></p>", " <p> of money </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>4.(c) Money is an uncountable noun and with uncountable nouns we use singular helping verbs. Here, Money is also considered as a subject. So. it will take ‘was’ as its helping verb.</span></p>",
                    solution_hi: " <p>4.(c) Money is an uncountable noun and with uncountable nouns we use singular helping verbs. Here, Money is also considered as a subject. So. it will take ‘was’ as its helping verb.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: " <p>5. A tallest man that I have ever seen works in our coal mine.                       </span></p>",
                    question_hi: "",
                    options_en: [" <p> A tallest man </span></p>", " <p> that I have </span></p>", 
                                " <p> works in our coal mine</span></p>", " <p> ever seen </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p> 5.(a) The definite article ‘the’ is used before superlative degree adjectives.Use the tallest man.</span></p>",
                    solution_hi: " <p> 5.(a) The definite article ‘the’ is used before superlative degree adjectives.Use the tallest man.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p>6. My grandmother is quite concerned to my progress at school.      </span></p>",
                    question_hi: "",
                    options_en: [" <p> My grandmother </span></p>", " <p> to my progress </span></p>", 
                                " <p> is quite concerned </span></p>", " <p> at school</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>6.(b) ‘Concerned about’ is the correct phrase, it means to worry or concern about something. So, concern about my progress is the correct structure.</span></p>",
                    solution_hi: " <p>6.(b) ‘Concerned about’ is the correct phrase, it means to worry or concern about something. So, concern about my progress is the correct structure.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Times New Roman\">7. He gave such a long speech but everybody felt bored. </span></p> <p><span style=\"font-family:Times New Roman\">   </span></p>",
                    question_hi: "",
                    options_en: [" <p> felt bored </span></p>", " <p> but everybody </span></p>", 
                                " <p> He gave such </span></p>", " <p> a long speech </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>7.(b) “</span><span style=\"font-family:Times New Roman\">Such - that</span><span style=\"font-family:Times New Roman\">”  is a conjunction pair which is used to give reason. According to the given question, everybody felt bored because the person gave a long boring speech. Hence, </span><span style=\"font-family:Times New Roman\">such a boring speech that everybody gets bored </span><span style=\"font-family:Times New Roman\"> will be the correct structure of the sentence.</span></p>",
                    solution_hi: " <p>7.(b) “</span><span style=\"font-family:Times New Roman\">Such - that</span><span style=\"font-family:Times New Roman\">”  is a conjunction pair which is used to give reason. According to the given question, everybody felt bored because the person gave a long boring speech. Hence, </span><span style=\"font-family:Times New Roman\">such a boring speech that everybody gets bored </span><span style=\"font-family:Times New Roman\"> will be the correct structure of the sentence.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: " <p>8</span><span style=\"font-family:Times New Roman\">. Craze for a thing that are not easily available in our country is a common phenomenon.     </span></p>",
                    question_hi: "",
                    options_en: [" <p> is a common phenomenon </span></p>", " <p> available in our country </span></p>", 
                                " <p> thing that are not easily  </span></p>", " <p> Craze for a </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>8.(c) According to the ‘Subject-Agreement Rule’, a singular subject always takes a singular helping verb. Similarly in the given question, </span><span style=\"font-family:Times New Roman\"> Craze for a thing</span><span style=\"font-family:Times New Roman\">  is considered as a singular subject and it will take ‘is’ as a singular verb. Hence, ‘are’ will be replaced by ‘is’.</span></p>",
                    solution_hi: " <p>8.(c) According to the ‘Subject-Agreement Rule’, a singular subject always takes a singular helping verb. Similarly in the given question, </span><span style=\"font-family:Times New Roman\"> Craze for a thing</span><span style=\"font-family:Times New Roman\">  is considered as a singular subject and it will take ‘is’ as a singular verb. Hence, ‘are’ will be replaced by ‘is’.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p>9</span><span style=\"font-family:Times New Roman\">. A large number of worker have started walking home. </span></p>",
                    question_hi: "",
                    options_en: [" <p> walking home </span></p>", " <p> of worker </span></p>", 
                                " <p> A large number </span></p>", " <p> have started </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>9.(b) </span><span style=\"font-family:Times New Roman\">A large number of</span><span style=\"font-family:Times New Roman\"> will be followed by a plural noun/ pronoun so workers will be used.</span></p>",
                    solution_hi: " <p>9.(b) </span><span style=\"font-family:Times New Roman\">A large number of</span><span style=\"font-family:Times New Roman\"> will be followed by a plural noun/ pronoun so workers will be used.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10<span style=\"font-family: Times New Roman;\">. In this lock-down period every worker in the factory has started bring his own lunch. </span></p>\n",
                    question_hi: "",
                    options_en: ["<p>their own lunch</p>\n", "<p>every worker in the factory</p>\n", 
                                "<p>has started bring</p>\n", "<p>In this lock-down period</p>\n"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>10.(c) As the activity is continuous over a period of time so continuous tense (bringing) will be used.<span style=\"font-family: Times New Roman;\">Has stated bringin</span><span style=\"font-family: Times New Roman;\">g will be the correct sentence structure. </span></p>\n",
                    solution_hi: "<p>10.(c) As the activity is continuous over a period of time so continuous tense (bringing) will be used.<span style=\"font-family: Times New Roman;\">Has stated bringin</span><span style=\"font-family: Times New Roman;\">g will be the correct sentence structure. </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: " <p>11</span><span style=\"font-family:Times New Roman\">. Two flyovers have been propose in the new plan to decongest M.G. Road.     </span></p>",
                    question_hi: "",
                    options_en: [" <p> Two flyovers </span></p>", " <p> in the new plan </span></p>", 
                                " <p> to decongest </span></p>", " <p> have been propose </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>11.(d)“Has/Have been + V (3rd form of the verb)” is the correct structure for any sentence beecause ‘has/have’ will  always take the third form of the verb. So, </span><span style=\"font-family:Times New Roman\">Have been proposed </span><span style=\"font-family:Times New Roman\">is answer.</span></p>",
                    solution_hi: " <p>11.(d)“Has/Have been + V (3rd form of the verb)” is the correct structure for any sentence beecause ‘has/have’ will  always take the third form of the verb. So, </span><span style=\"font-family:Times New Roman\">Have been proposed </span><span style=\"font-family:Times New Roman\">is answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12</span><span style=\"font-family:Times New Roman\">. I want you to complete this work by two days.</span></p>",
                    question_hi: "",
                    options_en: [" <p> by two days</span></p>", " <p> I want you</span></p>", 
                                " <p> this work</span></p>", " <p> to complete</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>12.(a) ‘Within’ is used as a function word to describe the limit to which something can extend and not beyond the quantity or degree or limitations. Within a certain time means before that time length has passed. In this sentence, the time limit is 2 days for completing the work. Hence, </span><span style=\"font-family:Times New Roman\">within</span><span style=\"font-family:Times New Roman\"> will be used in place of </span><span style=\"font-family:Times New Roman\">by</span><span style=\"font-family:Times New Roman\">.</span></p>",
                    solution_hi: " <p>12.(a) ‘Within’ is used as a function word to describe the limit to which something can extend and not beyond the quantity or degree or limitations. Within a certain time means before that time length has passed. In this sentence, the time limit is 2 days for completing the work. Hence, </span><span style=\"font-family:Times New Roman\">within</span><span style=\"font-family:Times New Roman\"> will be used in place of </span><span style=\"font-family:Times New Roman\">by</span><span style=\"font-family:Times New Roman\">.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p>13</span><span style=\"font-family:Times New Roman\">. Neither Sam nor I are interested in attending the meeting.</span></p>",
                    question_hi: "",
                    options_en: [" <p> the meeting</span></p>", " <p> Neither Sam nor I</span></p>", 
                                " <p> in attending</span></p>", " <p> are interested</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>13.(d)Neither-nor is a conjunction pair that is used only for two subjects, and the verb is supplied according to the subject close to the verb (This is called rule of proximity) ’.Hence, the verb will be placed according to the subject “I” which is in singular form. Hence, ‘nor I am’ is answer.</span></p>",
                    solution_hi: " <p>13.(d)Neither-nor is a conjunction pair that is used only for two subjects, and the verb is supplied according to the subject close to the verb (This is called rule of proximity) ’.Hence, the verb will be placed according to the subject “I” which is in singular form. Hence, ‘nor I am’ is answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14</span><span style=\"font-family:Times New Roman\">. Poor people have run down of food supplies during the lockdown                 </span></p>",
                    question_hi: "",
                    options_en: [" <p> food supplies</span></p>", " <p> during the lockdown</span></p>", 
                                " <p> Poor people have</span></p>", " <p> run down of </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>14.(d)‘Run out of’ is a phrase which refers to a condition of shortage of food or lack of food.This question is related to phrasal verbs.</span></p>",
                    solution_hi: " <p>14.(d)‘Run out of’ is a phrase which refers to a condition of shortage of food or lack of food.This question is related to phrasal verbs.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: " <p>15</span><span style=\"font-family:Times New Roman\">. No one of them came to the inauguration of our new factory                              </span></p>",
                    question_hi: "",
                    options_en: [" <p> our new factory</span></p>", " <p> No one of them</span></p>", 
                                " <p> inauguration of</span></p>", " <p> came to the </span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>15.(b)‘None of them have come’ will be the correct sentence structure.Here, </span><span style=\"font-family:Times New Roman\">None</span><span style=\"font-family:Times New Roman\"> is used with a plural verb</span><span style=\"font-family:Times New Roman\"> have</span><span style=\"font-family:Times New Roman\"> because the subject </span><span style=\"font-family:Times New Roman\">them</span><span style=\"font-family:Times New Roman\"> are persons(nouns) who are countable.</span></p>",
                    solution_hi: " <p>15.(b)‘None of them have come’ will be the correct sentence structure.Here, </span><span style=\"font-family:Times New Roman\">None</span><span style=\"font-family:Times New Roman\"> is used with a plural verb</span><span style=\"font-family:Times New Roman\"> have</span><span style=\"font-family:Times New Roman\"> because the subject </span><span style=\"font-family:Times New Roman\">them</span><span style=\"font-family:Times New Roman\"> are persons(nouns) who are countable.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: " <p>16</span><span style=\"font-family:Times New Roman\">. You must be careful about what you say as you meet her.</span></p>",
                    question_hi: "",
                    options_en: [" <p> as you</span></p>", " <p> about what you say</span></p>", 
                                " <p> You must be careful</span></p>", " <p> meet her</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>16.(a)</span><span style=\"font-family:Times New Roman\">When</span><span style=\"font-family:Times New Roman\"> is used for talking about the time at which something happens or happened. Hence, </span><span style=\"font-family:Times New Roman\">when you meet her</span><span style=\"font-family:Times New Roman\"> will be the correct sentence structure.</span></p>",
                    solution_hi: " <p>16.(a)</span><span style=\"font-family:Times New Roman\">When</span><span style=\"font-family:Times New Roman\"> is used for talking about the time at which something happens or happened. Hence, </span><span style=\"font-family:Times New Roman\">when you meet her</span><span style=\"font-family:Times New Roman\"> will be the correct sentence structure.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: " <p>17</span><span style=\"font-family:Times New Roman\">. She has been a member of this club since it’s formation</span></p>",
                    question_hi: "",
                    options_en: [" <p> this club</span></p>", " <p> a member of</span></p>", 
                                " <p> since it’s formation</span></p>", " <p> She has been</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>17.(c)‘Its’ will replace </span><span style=\"font-family:Times New Roman\">It’s</span><span style=\"font-family:Times New Roman\"> because its is the possessive form of the pronoun . Hence, </span><span style=\"font-family:Times New Roman\">since its formation</span><span style=\"font-family:Times New Roman\"> will be the correct sentence structure.</span></p>",
                    solution_hi: " <p>17.(c)‘Its’ will replace </span><span style=\"font-family:Times New Roman\">It’s</span><span style=\"font-family:Times New Roman\"> because its is the possessive form of the pronoun . Hence, </span><span style=\"font-family:Times New Roman\">since its formation</span><span style=\"font-family:Times New Roman\"> will be the correct sentence structure.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: " <p>18</span><span style=\"font-family:Times New Roman\">. The man played the flute and led all the mouse out of the town.                           </span></p>",
                    question_hi: "",
                    options_en: [" <p> all the mouse</span></p>", " <p> out of the town</span></p>", 
                                " <p> The man played</span></p>", " <p> the flute and led</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>18.(a)</span><span style=\"font-family:Times New Roman\">All the mice</span><span style=\"font-family:Times New Roman\"> will be the correct structure because</span><span style=\"font-family:Times New Roman\"> All </span><span style=\"font-family:Times New Roman\">will be used with </span><span style=\"font-family:Times New Roman\">mice </span><span style=\"font-family:Times New Roman\">which is a plural noun.</span></p>",
                    solution_hi: " <p>18.(a)</span><span style=\"font-family:Times New Roman\">All the mice</span><span style=\"font-family:Times New Roman\"> will be the correct structure because</span><span style=\"font-family:Times New Roman\"> All </span><span style=\"font-family:Times New Roman\">will be used with </span><span style=\"font-family:Times New Roman\">mice </span><span style=\"font-family:Times New Roman\">which is a plural noun.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: " <p>19</span><span style=\"font-family:Times New Roman\">. The venue for the wedding have not yet been finalised.</span></p>",
                    question_hi: "",
                    options_en: [" <p> been finalised</span></p>", " <p> have not yet</span></p>", 
                                " <p> for the wedding</span></p>", " <p> The venue</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>19.(b)According to the “</span><span style=\"font-family:Times New Roman\">Subject-Agreement Rule</span><span style=\"font-family:Times New Roman\">”, a </span><span style=\"font-family:Times New Roman\">singular subject </span><span style=\"font-family:Times New Roman\"> takes a </span><span style=\"font-family:Times New Roman\">singular verb.</span><span style=\"font-family:Times New Roman\">Here, </span><span style=\"font-family:Times New Roman\">The venue</span><span style=\"font-family:Times New Roman\"> is considered as a singular subject so it will take </span><span style=\"font-family:Times New Roman\">has</span><span style=\"font-family:Times New Roman\"> as a singular verb.</span></p>",
                    solution_hi: " <p>19.(b)According to the “</span><span style=\"font-family:Times New Roman\">Subject-Agreement Rule</span><span style=\"font-family:Times New Roman\">”, a </span><span style=\"font-family:Times New Roman\">singular subject </span><span style=\"font-family:Times New Roman\"> takes a </span><span style=\"font-family:Times New Roman\">singular verb.</span><span style=\"font-family:Times New Roman\">Here, </span><span style=\"font-family:Times New Roman\">The venue</span><span style=\"font-family:Times New Roman\"> is considered as a singular subject so it will take </span><span style=\"font-family:Times New Roman\">has</span><span style=\"font-family:Times New Roman\"> as a singular verb.</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p>20</span><span style=\"font-family:Times New Roman\">. Laws and rules are made to safeguarding our rights and protect us</span></p>",
                    question_hi: "",
                    options_en: [" <p> Laws and rules</span></p>", " <p> and protect us</span></p>", 
                                " <p> are made to</span></p>", " <p> safeguarding our rights</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>20.(d) </span><span style=\"font-family:Times New Roman\">Made to safeguard</span><span style=\"font-family:Times New Roman\"> will be the correct sentence structure. because made with infinitive(to) will not take Verb + ing  form.</span></p>",
                    solution_hi: " <p>20.(d) </span><span style=\"font-family:Times New Roman\">Made to safeguard</span><span style=\"font-family:Times New Roman\"> will be the correct sentence structure. because made with infinitive(to) will not take Verb + ing  form.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: " <p>21</span><span style=\"font-family:Times New Roman\">. I didn’t knew you had gone to Goa for a vacation.</span></p>",
                    question_hi: "",
                    options_en: [" <p> to goa</span></p>", " <p> for a vacation</span></p>", 
                                " <p> you had gone</span></p>", " <p> I didn’t knew</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>21.(d) “Did” in a sentence always comes with the first form of verb(V</span><span style=\"font-family:Times New Roman\">1</span><span style=\"font-family:Times New Roman\">) like Did come/ Did go/ Did has. Hence, ‘I didn’t know’ is answer.</span></p>",
                    solution_hi: " <p>21.(d) “Did” in a sentence always comes with the first form of verb(V</span><span style=\"font-family:Times New Roman\">1</span><span style=\"font-family:Times New Roman\">) like Did come/ Did go/ Did has. Hence, ‘I didn’t know’ is answer.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: " <p>22</span><span style=\"font-family:Times New Roman\">. The tired and vexed travellers waiting at the airport for a long time        </span></p>",
                    question_hi: "",
                    options_en: [" <p> The tired and</span></p>", " <p> for a long time</span></p>", 
                                " <p> waiting at the airport</span></p>", " <p> vexed travellers</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>22.(c) </span><span style=\"font-family:Times New Roman\">For a long time </span><span style=\"font-family:Times New Roman\"> is used to show an action of the past. However, “has/have been + Verb + ing  “will be the correct structure which is used to show an action that has been running from the past and still going in the present (present perfect continuous tense). So, ‘have been waiting’ is answer.</span></p>",
                    solution_hi: " <p>22.(c) </span><span style=\"font-family:Times New Roman\">For a long time </span><span style=\"font-family:Times New Roman\"> is used to show an action of the past. However, “has/have been + Verb + ing  “will be the correct structure which is used to show an action that has been running from the past and still going in the present (present perfect continuous tense). So, ‘have been waiting’ is answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: " <p>23</span><span style=\"font-family:Times New Roman\">. We must plan how can we cope with the present situation.        </span></p>",
                    question_hi: "",
                    options_en: [" <p> cope with the </span></p>", " <p> present situation</span></p>", 
                                " <p> how can we</span></p>", " <p> We must plan</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>23.(c) “Wh-family(What, When, Why, How) + Subject/object + Verb” is the correct structure, if an interrogative statement comes between a sentence. Hence, </span><span style=\"font-family:Times New Roman\">how we can cope</span><span style=\"font-family:Times New Roman\"> is answer.</span></p>",
                    solution_hi: " <p>23.(c) “Wh-family(What, When, Why, How) + Subject/object + Verb” is the correct structure, if an interrogative statement comes between a sentence. Hence, </span><span style=\"font-family:Times New Roman\">how we can cope</span><span style=\"font-family:Times New Roman\"> is answer.</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: " <p>24</span><span style=\"font-family:Times New Roman\">. The small cafe at the end of the road is her.</span></p>",
                    question_hi: "",
                    options_en: [" <p> of the road</span></p>", " <p> at the end</span></p>", 
                                " <p> The small cafe</span></p>", " <p> is her</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>24.(d) Replace her with hers.Hers is a possessive pronoun.It shows ownership.Here the ownership of the cafe is being referred to so hers will be used.</span></p>",
                    solution_hi: " <p>24.(d) Replace her with hers.Hers is a possessive pronoun.It shows ownership.Here the ownership of the cafe is being referred to so hers will be used.</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25</span><span style=\"font-family:Times New Roman\">. Zoya won the first prize in the race unless she stumbled and fell.        </span></p>",
                    question_hi: "",
                    options_en: [" <p> unless she</span></p>", " <p> stumbled and fell</span></p>", 
                                " <p> prize in the race</span></p>", " <p> Zoya won the first</span></p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>25.(a) The sentence is Verb + ing  a positive sense but </span><span style=\"font-family:Times New Roman\">unless</span><span style=\"font-family:Times New Roman\"> is a negative word and it is generally used to introduce the case in which a statement is being made is not true or valid. So, we will use until in place of unless. Until means up to the point in time or the event mentioned.</span></p>",
                    solution_hi: " <p>25.(a) The sentence is Verb + ing  a positive sense but </span><span style=\"font-family:Times New Roman\">unless</span><span style=\"font-family:Times New Roman\"> is a negative word and it is generally used to introduce the case in which a statement is being made is not true or valid. So, we will use until in place of unless. Until means up to the point in time or the event mentioned.</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>