<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. When the analysis of population density is done by calculating it through net cultivated area, then the measure is termed as :</p>",
                    question_hi: "<p>1. जब जनसंख्या घनत्व का विश्लेषण निवल खेती योग्य क्षेत्र के माध्यम से गणना करके किया जाता है, तो उस माप को क्या कहा जाता है?</p>",
                    options_en: ["<p>Agricultural density</p>", "<p>Physiological density</p>", 
                                "<p>Gross density</p>", "<p>Net density</p>"],
                    options_hi: ["<p>कृषि घनत्व (Agricultural density)</p>", "<p>कार्यिकीय घनत्व (Physiological density)</p>",
                                "<p>सकल घनत्व (Gross density)</p>", "<p>निवल घनत्व (Net density)</p>"],
                    solution_en: "<p>1.(b) <strong>Physiological density.</strong> Density of population is expressed as the number of persons per unit area. Physiological density = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>p</mi><mi>o</mi><mi>p</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi></mrow><mrow><mi>n</mi><mi>e</mi><mi>t</mi><mi>&#160;</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>t</mi><mi>i</mi><mi>v</mi><mi>a</mi><mi>t</mi><mi>e</mi><mi>d</mi><mi>&#160;</mi><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi></mrow></mfrac></math>. Agricultural density is the number of farmers per unit of arable land. Agricultural population includes cultivators and agricultural labourers and their family members.</p>",
                    solution_hi: "<p>1.(b) <strong>कार्यिकीय घनत्व</strong> (Physiological density)। जनसंख्या घनत्व को प्रति इकाई क्षेत्र में व्यक्तियों की संख्या के रूप में व्यक्त किया जाता है। कार्यिकीय घनत्व = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2358;&#2369;&#2342;&#2381;&#2343;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2326;&#2375;&#2340;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2351;&#2379;&#2327;&#2381;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</mi></mrow></mfrac></math>। कृषि घनत्व प्रति इकाई कृषि योग्य भूमि पर किसानों की संख्या है। कृषि जनसंख्या में किसान और कृषि मजदूर तथा उनके परिवार के सदस्य शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which plateaus are very fertile because they are rich in black soil that is very good for farming?</p>",
                    question_hi: "<p>2. कौन-से पठार बहुत उपजाऊ होते हैं क्योंकि वे काली मृदा में समृद्ध होते हैं जो खेती के लिए बहुत अच्छी है?</p>",
                    options_en: ["<p>African plateau</p>", "<p>Ethiopian plateau</p>", 
                                "<p>Katanga plateau</p>", "<p>Deccan lava plateau</p>"],
                    options_hi: ["<p>अफ्रीकी पठार</p>", "<p>इथियोपिया का पठार</p>",
                                "<p>कटंगा पठार</p>", "<p>दक्कन लावा पठार</p>"],
                    solution_en: "<p>2.(d) <strong>Deccan lava plateau</strong> is a volcanic plateau in west-central India that was formed by the solidification of lava from deep within the Earth. It is a triangular landmass that lies to the south of the river Narmada. The African plateau is famous for gold and diamond mining. The Katanga Plateau in the Democratic Republic of the Congo is known for its rich deposits of copper and uranium.</p>",
                    solution_hi: "<p>2.(d) <strong>दक्कन लावा पठार</strong> पश्चिम-मध्य भारत में एक ज्वालामुखीय पठार है जिसका निर्माण पृथ्वी के भीतर लावा के जमने से हुआ था। यह एक त्रिकोणीय भूभाग है जो नर्मदा नदी के दक्षिण में स्थित है। अफ्रीकी पठार सोने और हीरे के खनन के लिए प्रसिद्ध है। कांगो लोकतांत्रिक गणराज्य में कटंगा पठार तांबे और यूरेनियम के समृद्ध भंडार के लिए जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following states is the biggest producer of Pulses?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन-सा राज्य दालों का सबसे बड़ा उत्पादक है?</p>",
                    options_en: ["<p>Madhya Pradesh</p>", "<p>Haryana</p>", 
                                "<p>Punjab</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>मध्य प्रदेश</p>", "<p>हरियाण</p>",
                                "<p>पंजाब</p>", "<p>बिहार</p>"],
                    solution_en: "<p>3.(a) <strong>Madhya Pradesh.</strong> Based on the production estimates for the year 2022-23, Madhya Pradesh, Maharashtra and Rajasthan are the top three pulses producing states in the country. India is the largest producer of pulses in the world.</p>",
                    solution_hi: "<p>3.(a) <strong>मध्य प्रदेश।</strong> वर्ष 2022-23 के उत्पादन अनुमान के आधार पर मध्य प्रदेश, महाराष्ट्र और राजस्थान देश के शीर्ष तीन दलहन उत्पादक राज्य हैं। भारत दुनिया में दालों का सबसे बड़ा उत्पादक देश है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. In which year was Project Tiger launched in India?",
                    question_hi: "4. भारत में प्रोजेक्ट टाइगर (Project Tiger) किस वर्ष शुरू किया गया था?",
                    options_en: [" 1985 ", " 1973 ", 
                                " 1972", " 1970"],
                    options_hi: [" 1985 में ", " 1973 में ",
                                " 1972 में ", " 1970 में "],
                    solution_en: "4.(b) On April 1, 1973, Project Tiger was officially launched at the Corbett Tiger Reserve, initially including nine tiger reserves. International Tiger Day is celebrated annually on July 29. The Wildlife Protection Act was passed in 1972. Projects in India related to animals :Project Hangul (1970), Project Crocodile (1975), Project Elephant (1992), Project Rhino (2005), Project Gangetic Dolphin - (2009), Project Snow Leopard (2009), Project Great Indian Bustard (2012).",
                    solution_hi: "4.(b) 1 अप्रैल, 1973 को कॉर्बेट टाइगर रिज़र्व में आधिकारिक तौर पर प्रोजेक्ट टाइगर की शुरुआत की गई थी, जिसमें शुरुआत में नौ टाइगर रिज़र्व शामिल थे। अंतर्राष्ट्रीय बाघ दिवस प्रत्येक वर्ष  29 जुलाई को मनाया जाता है। वन्यजीव संरक्षण अधिनियम 1972 ईस्वी में पारित किया गया था। भारत में जानवरों से संबंधित परियोजनाएँ: प्रोजेक्ट हंगुल (1970), प्रोजेक्ट क्रोकोडाइल (1975), प्रोजेक्ट एलीफेंट (1992), प्रोजेक्ट राइनो (2005), प्रोजेक्ट गैंगेटिक डॉल्फिन (2009), प्रोजेक्ट स्नो लेपर्ड (2009), प्रोजेक्ट ग्रेट इंडियन बस्टर्ड (2012) ।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following can be represented as a functional unit of nature?</p>",
                    question_hi: "<p>5. निम्नलिखित में से किसे प्रकृति की कार्यात्मक इकाई के रूप में दर्शाया जा सकता है?</p>",
                    options_en: ["<p>Vehicles</p>", "<p>Ecosystem</p>", 
                                "<p>Humans</p>", "<p>Plants</p>"],
                    options_hi: ["<p>वाहन</p>", "<p>पारिस्थितिकी तंत्र</p>",
                                "<p>मनुष्य</p>", "<p>वनस्पति</p>"],
                    solution_en: "<p>5.(b) <strong>Ecosystem</strong>. This is because an ecosystem encompasses all living organisms (plants, animals, and microorganisms) interacting with each other and with the non-living components (like air, water, and soil) within their environment. These interactions allow for energy flow, nutrient cycling, and the maintenance of life-supporting processes, which are critical functions of nature.</p>",
                    solution_hi: "<p>5.(b) <strong>पारिस्थितिकी तंत्र</strong>। ऐसा इसलिए है क्योंकि पारिस्थितिकी तंत्र में सभी जीवित जीव (पौधे, जन्तुओ और सूक्ष्मजीव) शामिल होते हैं जो एक दूसरे के साथ और अपने पर्यावरण के भीतर निर्जीव घटकों (जैसे वायु, जल और मृदा) के साथ अंतःक्रिया करते हैं। ये अंतःक्रियाएँ ऊर्जा प्रवाह, पोषक तत्वों के चक्रण और जीवन को बनाए रखने वाली प्रक्रियाओं के रखरखाव को संभव बनाती हैं, जो प्रकृति के महत्वपूर्ण कार्य हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. In Which state of India is kund or Tanka used for water harvesting ?</p>",
                    question_hi: "<p>6. भारत के किस राज्य में जल संचयन के लिए कुंड या टांका का उपयोग किया जाता है?</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Andhra Pradesh</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>आंध्र प्रदेश</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>6.(a) <strong>Rajasthan</strong>. This region faces extreme arid conditions and low rainfall, so local communities have developed innovative systems to conserve and store water. Water harvesting structures in other States: Kere (large tanks) in Karnataka, Cheruvu in Andhra Pradesh, Bandharas and Tals in Maharashtra, Surangam in Kerala, Eris (tanks) in Tamil Nadu, Bhundis in Madhya Pradesh and Uttar Pradesh.</p>",
                    solution_hi: "<p>6.(a) <strong>राजस्थान</strong>। यह क्षेत्र अत्यधिक शुष्क परिस्थितियों और कम वर्षा का सामना करता है, इसलिए स्थानीय समुदायों ने जल संरक्षण और भंडारण के लिए उन्नत प्रणालियाँ विकसित की हैं। अन्य राज्यों में जल संचयन संरचनाएँ: कर्नाटक में केरे (बड़े टैंक), आंध्र प्रदेश में चेरुवु, महाराष्ट्र में बंधारस और ताल, केरल में सुरंगम, तमिलनाडु में एरिस (टैंक), मध्य प्रदेश और उत्तर प्रदेश में भुंडि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. At what latitude does the easterly jet stream blow over peninsular India during the summer months?</p>",
                    question_hi: "<p>7. गर्मियों के महीनों में पूर्वी जेट धारा प्रायद्वीपीय भारत के ऊपर किस अक्षांश पर बहती है?</p>",
                    options_en: ["<p>24&deg;N</p>", "<p>30&deg;N</p>", 
                                "<p>14&deg;N</p>", "<p>28&deg;N</p>"],
                    options_hi: ["<p>24&deg;N</p>", "<p>30&deg;N</p>",
                                "<p>14&deg;N</p>", "<p>28&deg;N</p>"],
                    solution_en: "<p>7.(c) <strong>14&deg;N.</strong> The Jet Stream is a geostrophic wind blowing horizontally through the upper layers of the troposphere, generally from west to east. The tropical easterly jet stream, located between 8 and 35 degrees north latitude, is connected to the southwest monsoon in India.</p>",
                    solution_hi: "<p>7.(c) <strong>14&deg;N.</strong> जेट स्ट्रीम एक भूस्थैतिक वायु है जो क्षोभमंडल की ऊपरी परतों पर पश्चिम से पूर्व की ओर से होकर क्षैतिज रूप से चलती है। उष्णकटिबंधीय पूर्वी जेट स्ट्रीम, जो 8 से 35 डिग्री उत्तरी अक्षांश के बीच स्थित है, भारत में दक्षिण-पश्चिम मानसून से जुड़ी हुई है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. According to Census of India 2011, in rural India, which state has the maximum female workforce participation rate?</p>",
                    question_hi: "<p>8. भारत की जनगणना 2011 के अनुसार ग्रामीण भारत में, निम्नलिखित में से कौन-से राज्य में, महिला कार्यबल भागीदारी दर सर्वाधिक है?</p>",
                    options_en: ["<p>Arunachal Pradesh</p>", "<p>Uttar Pradesh</p>", 
                                "<p>Himachal Pradesh</p>", "<p>Andhra Pradesh</p>"],
                    options_hi: ["<p>अरुणाचल प्रदेश</p>", "<p>उत्तर प्रदेश</p>",
                                "<p>हिमाचल प्रदेश</p>", "<p>आंध्र प्रदेश</p>"],
                    solution_en: "<p>8.(c) <strong>Himachal Pradesh.</strong> As per Census 2011, the workforce participation rate for females is 25.51% against 53.26% for males. Rural sector has a better female workforce participation rate of 30.02% compared with 53.03% for males whereas for the urban sector. In Rural, Himachal Pradesh has the highest female workforce participation rate (47.4) whereas Tamil Nadu has max (21.8) among major states.</p>",
                    solution_hi: "<p>8.(c)<strong> हिमाचल प्रदेश।</strong> जनगणना 2011 के अनुसार, महिलाओं के लिए कार्यबल भागीदारी दर 25.51% है, जबकि पुरुषों के लिए यह 53.26% है। ग्रामीण क्षेत्र में महिला कार्यबल भागीदारी दर 30.02% बेहतर है, जबकि शहरी क्षेत्र में पुरुषों की भागीदारी दर 53.03% है। ग्रामीण क्षेत्रों में, हिमाचल प्रदेश में महिला कार्यबल भागीदारी दर सबसे अधिक (47.4) है, जबकि प्रमुख राज्यों में तमिलनाडु में यह दर अधिकतम (21.8) है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following statements correctly defines the green revolution?</p>",
                    question_hi: "<p>9. निम्&zwj;न में से कौन-सा कथन हरित क्रांति को सही ढंग से परिभाषित करता है?</p>",
                    options_en: ["<p>It is a new strategy in agriculture to produce food grains, especially wheat and rice.</p>", "<p>It is a new strategy to increase the share of the forest.</p>", 
                                "<p>It is a new strategy to use green colour for all purposes.</p>", "<p>It is a new strategy to use only herbal products.</p>"],
                    options_hi: ["<p>यह कृषि में खाद्यान्न, विशेषकर गेहूं और चावल का उत्पादन करने की एक नई रणनीति है।</p>", "<p>यह वन के योगदान में वृद्धि करने की एक नई रणनीति है।</p>",
                                "<p>यह सभी उद्देश्यों के लिए हरे रंग का उपयोग करने की एक नई रणनीति है।</p>", "<p>यह केवल हर्बल उत्पादों का उपयोग करने की एक नई रणनीति है।</p>"],
                    solution_en: "<p>9.(a) The Green Revolution began in the 1960s, particularly in countries like India, where the government aimed to achieve food security and reduce dependence on food imports. Key elements of the Green Revolution included the introduction of high-yielding variety (HYV) seeds, mechanized farming tools, improved irrigation systems, as well as the use of pesticides and fertilizers.</p>",
                    solution_hi: "<p>9.(a) हरित क्रांति 1960 के दशक में शुरू हुई, खास तौर पर भारत जैसे देशों में, जहाँ सरकार का लक्ष्य खाद्य सुरक्षा हासिल करना और खाद्य आयात पर निर्भरता कम करना था। हरित क्रांति के मुख्य तत्वों में उच्च उपज वाली किस्म (HYV) के बीज, मशीनीकृत कृषि उपकरण, बेहतर सिंचाई प्रणाली, साथ ही कीटनाशकों और उर्वरकों का उपयोग शामिल था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. In which part of India does the hot wind &lsquo;Loo&rsquo; blow?</p>",
                    question_hi: "<p>10. भारत के किस भाग में गर्म हवा \'लू\' चलती है?</p>",
                    options_en: ["<p>West and Southwestern</p>", "<p>North and Northwestern</p>", 
                                "<p>East and Northeastern</p>", "<p>South and Southwestern</p>"],
                    options_hi: ["<p>पश्चिम और दक्षिण-पश्चिम</p>", "<p>उत्तर और उत्तर-पश्चिम</p>",
                                "<p>पूर्व और पूर्वोत्तर</p>", "<p>दक्षिण और दक्षिण-पश्चिम</p>"],
                    solution_en: "<p>10.(b) <strong>North and Northwestern.</strong> Loo is Hot, dry and oppressing winds blowing during the day in the Northern plains from Punjab to Bihar with higher intensity between Delhi and Patna. It is especially strong in the months of May and June. Sometimes they even continue until late in the evening. Direct exposure to these winds may even prove to be fatal.</p>",
                    solution_hi: "<p>10.(b) <strong>उत्तर और उत्तर-पश्चिमी।</strong> लू उत्तरी मैदानों में दिन के समय पंजाब से बिहार तक चलने वाली गर्म, शुष्क और दमनकारी हवाएँ हैं, जो दिल्ली और पटना के बीच अधिक प्रबल होती हैं। यह मई और जून के महीनों में विशेष रूप से तेज़ चलती हैं। कभी-कभी ये देर शाम तक भी चलती रहती हैं। इन हवाओं के सीधे संपर्क में आना जानलेवा भी साबित हो सकता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Hot local wind that flows over north India in summer is known as :</p>",
                    question_hi: "<p>11. गर्मियों में उत्तर भारत में बहने वाली गर्म स्थानीय हवा ___ कहलाती है।</p>",
                    options_en: ["<p>Loo</p>", "<p>Chinook</p>", 
                                "<p>Mango showers</p>", "<p>Purga</p>"],
                    options_hi: ["<p>लू</p>", "<p>चिनूक</p>",
                                "<p>आम्र वर्षा</p>", "<p>पुर्गा</p>"],
                    solution_en: "<p>11.(a) <strong>Loo</strong>. These are strong, gusty, hot, dry winds blowing during the day over the north and northwestern India. In West Bengal, similar storms are known as &lsquo;Kaal Baisakhi&rsquo;. In Kerala and Karnataka, pre-monsoon showers, which help in the early ripening of mangoes, are referred to as &lsquo;mango showers&rsquo;. Chinook is the warm and dry local wind blowing on the leeward side or eastern side of Rockies (Prairies) in North America. The Buran wind blows across eastern Asia, and it is referred to as Purga when it occurs over the tundra.</p>",
                    solution_hi: "<p>11.(a) <strong>लू।</strong> ये उत्तर और उत्तर-पश्चिमी भारत में दिन के समय चलने वाली तेज़, झोंकेदार, गर्म, शुष्क हवाएँ हैं। पश्चिम बंगाल में, इसी तरह के तूफ़ानों को \'काल बैसाखी\' के नाम से जाना जाता है। केरल और कर्नाटक में, प्री-मॉनसून वर्षा, जो आमों को जल्दी पकने में मदद करती है, को \'मैंगो शावर\' (आम्र वर्षा) कहा जाता है। चिनूक उत्तरी अमेरिका में रॉकीज़ (प्रेयरीज़) के पूर्वी हिस्से या हवा के विपरीत दिशा में चलने वाली गर्म एवं शुष्क स्थानीय हवा है। बुरान हवा पूर्वी एशिया में चलती है, और जब यह टुंड्रा पर होती है तो इसे पुर्गा कहा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. As per the definition of Census of India, a ‘marginal worker’ is a person who works for:",
                    question_hi: "12. भारत की जनगणना की परिभाषा के अनुसार, एक सीमांत श्रमिक वह व्यक्ति है, जो _____ काम करता है।",
                    options_en: [" less than 183 days (or six months) in a year ", " more than 183 days (or six months) in a year  ", 
                                " eight months in a year  ", " 83 days in a year "],
                    options_hi: [" एक वर्ष में 183 दिनों (नौ या छह महीने) से कम", " एक वर्ष में 183 दिनों (नौ या छह महीने) से अधिक",
                                " एक वर्ष में आठ महीन", " एक वर्ष में 83 दिन"],
                    solution_en: "12.(a) The population of India according to their economic status is divided into three groups, namely; main workers, marginal workers and  non-workers. A Main Worker is a person who works for at least 183 days (or six months) in a year. Marginal Worker is a person who works for less than 183 days (or six months) in a year. The proportion of workers (both main and marginal) is only 39.8 per cent (2011) leaving a vast majority of about 60 percent as non-workers.",
                    solution_hi: "12.(a) भारत की जनसंख्या को उनकी आर्थिक स्थिति के अनुसार तीन समूहों में विभाजित किया गया है, अर्थात् मुख्य श्रमिक, सीमांत श्रमिक और गैर-श्रमिक। मुख्य श्रमिक वह व्यक्ति है जो एक वर्ष में कम से कम 183 दिन (या छह महीने) काम करता है। सीमांत श्रमिक वह व्यक्ति है जो एक वर्ष में 183 दिन (या छह महीने) से कम काम करता है। श्रमिकों (मुख्य और सीमांत दोनों) का अनुपात केवल 39.8 प्रतिशत (2011) है, जिससे लगभग 60 प्रतिशत गैर-श्रमिक हैं।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. What is the length of Indian Railways network, according to Railway Yearbook 2019-20?</p>",
                    question_hi: "<p>13. रेलवे वार्षिकी 2019-20 के अनुसार भारतीय रेलवे नेटवर्क की लंबाई कितनी है?</p>",
                    options_en: ["<p>67,956 km</p>", "<p>1,604 km</p>", 
                                "<p>63,950 km</p>", "<p>2,402 km</p>"],
                    options_hi: ["<p>67,956 km</p>", "<p>1,604 km</p>",
                                "<p>63,950 km</p>", "<p>2,402 km</p>"],
                    solution_en: "<p>13.(a) <strong>67,956 km. </strong>The length of the Indian Railways network, according to the Railway Yearbook 2019-20 : Broad gauge - The distance between the rails is 1.676 meters, with a total length of 63,950 km. Metre gauge - The distance between the rails is 1 meter, with a total length of 2,402 km. Narrow gauge - The distance between the rails is either 0.762 meters or 0.610 meters, with a total length of 1,604 km.</p>",
                    solution_hi: "<p>13.(a) <strong>67,956 km.</strong> रेलवे वार्षिकी 2019-20 के अनुसार भारतीय रेलवे नेटवर्क की लंबाई: ब्रॉड गेज - रेल के बीच की दूरी 1.676 मीटर है, जिसकी कुल लंबाई 63,950 किमी. है। मीटर गेज - रेल के बीच की दूरी 1 मीटर है, जिसकी कुल लंबाई 2,402 किमी. है। नैरो गेज - रेल के बीच की दूरी या तो 0.762 मीटर या 0.610 मीटर है, जिसकी कुल लंबाई 1,604 किमी. है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following is the largest crustal plate on Earth with an area of over 103,000,000 km<sup>2</sup> ?</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन सी पृथ्वी पर सबसे बड़ी भूपर्पटीय प्लेट है जिसका क्षेत्रफल 103,000,000 km<sup>2</sup> से अधिक है?</p>",
                    options_en: ["<p>South American plate</p>", "<p>African plate</p>", 
                                "<p>Eurasian plate</p>", "<p>Pacific plate</p>"],
                    options_hi: ["<p>दक्षिण अमेरिकी प्लेट</p>", "<p>अफ्रीकी प्लेट</p>",
                                "<p>यूरेशियन प्लेट</p>", "<p>प्रशांत प्लेट</p>"],
                    solution_en: "<p>14.(d) <strong>Pacific plate :</strong> It is the largest oceanic plate. Plate Tectonics theory given by McKenzie, Parker and Morgan in 1967. It proposes that the earth&rsquo;s lithosphere is divided into seven major and some minor plates. Major plates : Antarctica and the surrounding oceanic plate; North American (with western Atlantic floor separated from the South American plate along the Caribbean islands) plate; South American (with western Atlantic floor separated from the North American plate along the Caribbean islands) plate; Pacific plate; India-Australia-New Zealand plate; Africa with the eastern Atlantic floor plate; Eurasia and the adjacent oceanic plate.</p>",
                    solution_hi: "<p>14.(d) <strong>प्रशांत प्लेट : </strong>यह सबसे बड़ी महासागरीय प्लेट है। प्लेट विवर्तनिकी (टेक्टोनिक्स) सिद्धांत मैकेंजी, पार्कर और मॉर्गन द्वारा 1967 में दिया गया था। यह प्रस्तावित करता है कि पृथ्वी का स्थलमंडल सात प्रमुख और कुछ छोटी प्लेटों में विभाजित है। प्रमुख प्लेटें: अंटार्कटिका और आसपास की महासागरीय प्लेट; उत्तरी अमेरिकी प्लेट (कैरिबियन द्वीपों के साथ पश्चिमी अटलांटिक तल, दक्षिण अमेरिकी प्लेट से अलग); दक्षिण अमेरिकी प्लेट (कैरिबियन द्वीपों के साथ पश्चिमी अटलांटिक तल, उत्तरी अमेरिकी प्लेट से अलग); प्रशांत प्लेट; भारत-ऑस्ट्रेलिया-न्यूजीलैंड प्लेट; अफ्रीका के साथ पूर्वी अटलांटिक तल प्लेट; यूरेशिया और उससे संलग्न महासागरीय प्लेट।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following is in geographical proximity to Sri Lanka?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन-सा स्थान भौगोलिक रूप से श्रीलंका के समीप है?</p>",
                    options_en: ["<p>Only Karaikal</p>", "<p>Karaikal and Yanam</p>", 
                                "<p>Only Mahe</p>", "<p>Only Yanam</p>"],
                    options_hi: ["<p>केवल कराईकल</p>", "<p>कराईकल और यानम</p>",
                                "<p>केवल माहे</p>", "<p>केवल यानम</p>"],
                    solution_en: "<p>15.(a) <strong>Only Karaikal.</strong> Karaikal is a town in the Union Territory of Puducherry. Sri Lanka, formerly known as Ceylon, is an island nation in South Asia. Located in the Indian Ocean, southwest of the Bay of Bengal, it is separated from the Indian peninsula by the Gulf of Mannar and the Palk Strait. Sri Lanka shares a maritime border with the Maldives to the southwest and India to the northwest.</p>",
                    solution_hi: "<p>15.(a) <strong>केवल कराईकल।</strong> कराईकल पुडुचेरी के केंद्र शासित प्रदेश का एक शहर है। श्रीलंका, जिसे पहले सीलोन के नाम से जाना जाता था, दक्षिण एशिया में एक द्वीप राष्ट्र है। बंगाल की खाड़ी के दक्षिण-पश्चिम में हिंद महासागर में स्थित, यह मन्नार की खाड़ी और पाक जलडमरूमध्य द्वारा भारतीय प्रायद्वीप से अलग होता है। श्रीलंका दक्षिण-पश्चिम में मालदीव और उत्तर-पश्चिम में भारत के साथ समुद्री सीमा साझा करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>