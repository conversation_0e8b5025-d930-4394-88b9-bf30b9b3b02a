<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. ________expenditure and _________ receipts are shown by the government budget.</p>",
                    question_hi: "<p>1. ________ व्यय और ______________प्राप्तियां सरकारी बजट द्वारा दर्शायी जाती हैं।</p>",
                    options_en: ["<p>Estimated; actual</p>", "<p>Actual; estimated</p>", 
                                "<p>Actual; actual</p>", "<p>Estimated; estimated</p>"],
                    options_hi: ["<p>अनुमानित; वास्तविक</p>", "<p>वास्तविक; अनुमानित</p>",
                                "<p>वास्तविक; वास्तविक</p>", "<p>अनुमानित; अनुमानित</p>"],
                    solution_en: "<p>1.(d) <strong>Estimated; estimated. Budget</strong> {Annual Financial Statement (Article 112)} - A statement of the estimated receipts and expenditure of the Government in a financial year. It contains Estimates of revenue and capital receipts, ways and means to raise the revenue, details of the actual receipts and expenditure of the closing financial year with reasons for any deficit or surplus in that year, the economic and financial policy of the coming year (taxation proposals, prospects of revenue, spending programme and introduction of new schemes).</p>",
                    solution_hi: "<p>1.(d) <strong>अनुमानित; अनुमानित I बजट</strong> {वार्षिक वित्तीय विवरण (अनुच्छेद 112)} - किसी वित्तीय वर्ष में सरकार की अनुमानित आय और व्यय का विवरण। बजट में आय और पूंजीगत प्राप्तियों का अनुमान, आय जुटाने के तरीके और कारक, उस वर्ष में किसी भी घाटे या अधिशेष के कारणों के साथ उस वर्ष की वास्तविक आय और व्यय का विवरण, अगले वर्ष की आर्थिक और वित्तीय नीति (कर प्रस्ताव, आय की संभावनाएं, व्यय कार्यक्रम और नई योजनाओं की शुरूआत) शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. Nikita made two investments; she earns 10% on one investment and lost 20% on the second investment. If the investments were in the ratio 2 : 3, then she got a:</p>",
                    question_hi: "<p>2. निकिता ने दो निवेश किए, वह एक निवेश पर 10% अर्जित करती है और दूसरे निवेश पर उसे 20% की हानि होती है। यदि निवेश 2 : 3 के अनुपात में थे, तो उसका लाभ या हानि प्रतिशत कितना है ?</p>",
                    options_en: ["<p>loss of 8%</p>", "<p>profit of 15%</p>", 
                                "<p>profit of 8%</p>", "<p>loss of 15%</p>"],
                    options_hi: ["<p>8% की हानि</p>", "<p>15% का लाभ</p>",
                                "<p>8% का लाभ</p>", "<p>15% की हानि</p>"],
                    solution_en: "<p>2.(a)<br>Let the investment 200 and 300 units<br>According to the question,<br>200 &times; <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 220 units<br>300 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 240 units<br>Total investment = 200 + 300 = 500 units<br>Total earning = 220 + 240 = 460 units<br>Loss % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mo>-</mo><mn>460</mn></mrow><mn>500</mn></mfrac></math> &times; 100 = 8 %</p>",
                    solution_hi: "<p>2.(a)<br>मान लीजिए निवेश = 200 और 300 इकाई <br>प्रश्न के अनुसार,<br>200 &times; <math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 220 इकाई <br>300 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 240 इकाई <br>कुल निवेश = 200 + 300 = 500 इकाई <br>कुल कमाई = 220 + 240 = 460 इकाई <br>हानि % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mo>-</mo><mn>460</mn></mrow><mn>500</mn></mfrac></math>&nbsp;&times; 100 = 8 %</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>Dog : Guard :: Horse : ?</p>",
                    question_hi: "<p>3. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>कुत्ता : पहरेदार :: घोड़ा : ?</p>",
                    options_en: ["<p>Stable</p>", "<p>Cart</p>", 
                                "<p>Ride</p>", "<p>Saddle</p>"],
                    options_hi: ["<p>अस्तबल</p>", "<p>छकड़ा गाडी</p>",
                                "<p>सवारी</p>", "<p>काठी</p>"],
                    solution_en: "<p>3.(c) Dog : Guard (as trained dog guards the houses)<br>Following the same analogy,<br>Horse : Ride (as horses are trained for riding or racing)</p>",
                    solution_hi: "<p>3.(c) कुत्ता : पहरेदार [ प्रशिक्षित कुत्ता घरों की रखवाली करता है ]<br>इसी प्रकार, घोड़ा : सवारी [ घोड़ों को घुड़सवारी या दौड़ के लिए प्रशिक्षित किया जाता है ]</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. What is the name of a metallic radioactive transuranic element with atomic number 101 in the actinide series, discovered in 1955?</p>",
                    question_hi: "<p>4. 1955 में खोजे गए, एक धात्विक रेडियोधर्मी परायूरेनियम तत्व का नाम क्या है, जिसका एक्टिनाइड श्रृंखला में परमाणु क्रमांक 101 है?</p>",
                    options_en: ["<p>Seaborgium</p>", "<p>Nobelium</p>", 
                                "<p>Mendelevium</p>", "<p>Rutherfordium</p>"],
                    options_hi: ["<p>सीबोर्गियम</p>", "<p>नोबेलियम</p>",
                                "<p>मेंडेलीवियम</p>", "<p>रदरफोर्डियम</p>"],
                    solution_en: "<p>4.(c) <strong>Mendelevium (Md):</strong> It is a highly synthetic and radioactive element, named after Dimtri Mendeleev (Father of the periodic table), formed by bombarding einsteinium (99) with alpha particles. <strong>Transuranic elements</strong> are members of the actinide series beginning with neptunium (93) which are artificially produced in nuclear reactors, accelerators and all have several isotopes that emit alpha rays. <strong>Seaborgium (Sg) </strong>- Transition metal, atomic number (106); <strong>Nobelium </strong>(No, 102) - Actinide, <strong>Rutherfordium</strong> (Rf, 104) - Transition metal.</p>",
                    solution_hi: "<p>4.(c) <strong>मेंडेलीवियम (Md): </strong>यह एक अत्यधिक कृत्रिम और रेडियोधर्मी तत्व है, जिसका नाम दिमित्री मेंडेलीव (आवर्ती सारणी के जनक ) के नाम पर रखा गया है, जो अल्फा कणों के साथ आइंस्टीनियम (99) पर बमबारी करने पर प्राप्त होता है। <strong>यूरेनियमोत्तर तत्व</strong> नेप्टूनियम (93) से प्रारम्भ होने वाली एक्टिनाइड श्रृंखला के सदस्य हैं जो परमाणु रिएक्टरों, त्वरक में कृत्रिम रूप से उत्पादित होते हैं और सभी में कई आइसोटोप होते हैं जो अल्फा किरणें उत्सर्जित करते हैं। <strong>सीबोर्गियम</strong> (Sg, 106) - संक्रमण धातु; <strong>नोबेलियम</strong> (No, 102) - एक्टिनाइड, परमाणु संख्या (102); रदरफोर्डियम (Rf, 104) - संक्रमण धातु ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. Select the pair of letter clusters from among the given options that has the same relationship as the one given below:<br>BQZH : OGXI :: ?</p>",
                    question_hi: "<p>5. दिए गए विकल्पों में से अक्षर समूहों के उस युग्म का चयन कीजिए जिसका संबंध नीचे दिए गए विकल्पों के समान ही हो<br>BQZH : OGXI :: ?</p>",
                    options_en: ["<p>WBLK : RTID</p>", "<p>SQMN : UTWZ</p>", 
                                "<p>FLXR : YESM</p>", "<p>PNML : STWW</p>"],
                    options_hi: ["<p>WBLK : RTID</p>", "<p>SQMN : UTWZ</p>",
                                "<p>FLXR : YESM</p>", "<p>PNML : STWW</p>"],
                    solution_en: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335284.png\" alt=\"rId4\" width=\"143\" height=\"64\"> ,&nbsp;</p>\n<p>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335443.png\" alt=\"rId5\" width=\"146\" height=\"65\"></p>",
                    solution_hi: "<p>5.(c)<br><strong id=\"docs-internal-guid-11e82dd2-7fff-97e8-3cbf-abff2314ddd1\"><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335284.png\" alt=\"rId4\" width=\"143\" height=\"64\"></strong></p>\n<p>उसी प्रकार<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335443.png\" alt=\"rId5\" width=\"146\" height=\"65\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. The principal ____________ reserves are in Neyveli in Tamil Nadu.</p>",
                    question_hi: "<p>6. _________ के भंडार मुख्यतः तमिलनाडु के नैवेली में स्थित हैं।</p>",
                    options_en: ["<p>bauxite</p>", "<p>iron ore</p>", 
                                "<p>copper</p>", "<p>lignite</p>"],
                    options_hi: ["<p>बॉक्साइट</p>", "<p>लौह अयस्क</p>",
                                "<p>ताँबा</p>", "<p>लिग्नाइट</p>"],
                    solution_en: "<p>6.(d) <strong>Lignite</strong>. Deposits occur in the Tertiary sediments in Tamil Nadu, Puducherry, Kerala, Gujarat, Rajasthan and Jammu &amp; Kashmir. <strong>Other reserves: Tamilnadu</strong> - Limestone (Tiruchirappalli, Ariyalur, Perambalur and Thoothukudi), Gypsum (Coimbatore, Tiruppur, Ramanathapuram and Perambalur districts). <strong>Bauxite Reserves</strong> - Odisha (largest, Kalahandi and Sambalpur), Gujarat (Bhavnagar, Junagadh, and Amreli), Chhattisgarh (Surguja, Raigarh, and Bilaspur), Madhya Pradesh (Shahdol, Mandla, and Balaghat districts). <strong>Copper reserves</strong> - Jharkhand (Singhbhum), Madhya Pradesh (Malanjkhand mine), Rajasthan (Aravali range).</p>",
                    solution_hi: "<p>6.(d) <strong>लिग्नाइट</strong> I तृतीयक अवसादों का निक्षेप तमिलनाडु, पुदुचेरी, केरल, गुजरात, राजस्थान और जम्मू एवं कश्मीर में होता है। <strong>अन्य भंडार: तमिलनाडु</strong> - चूना पत्थर (तिरुचिरापल्ली, अरियालुर, पेरम्बलुर और थूथुकुडी), जिप्सम (कोयंबटूर, तिरुप्पुर, रामनाथपुरम और पेरम्बलूर जिले)। <strong>बॉक्साइट भंडार</strong> - ओडिशा (सबसे बड़ा, कालाहांडी और संबलपुर), गुजरात (भावनगर, जूनागढ़ और अमरेली), छत्तीसगढ़ (सरगुजा, रायगढ़ और बिलासपुर), मध्य प्रदेश (शहडोल, मंडला और बालाघाट जिले)। <strong>तांबे के भंडार -</strong> झारखंड (सिंहभूम ), मध्य प्रदेश (मलाजखंड खदान), राजस्थान (अरावली पर्वतमाला)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. If A = 10&deg;, what is the value of<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi><mo>+</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>5</mn><mi>A</mi><mo>-</mo><mn>5</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mfrac><mrow><mn>9</mn><mi>A</mi></mrow><mn>2</mn></mfrac><mo>-</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>5</mn><mi>A</mi><mo>+</mo><mn>10</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> ?</p>",
                    question_hi: "<p>7. यदि A = 10&deg;, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi><mo>+</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>5</mn><mi>A</mi><mo>-</mo><mn>5</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mfrac><mrow><mn>9</mn><mi>A</mi></mrow><mn>2</mn></mfrac><mo>-</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>5</mn><mi>A</mi><mo>+</mo><mn>10</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>&nbsp;का मान होगा:</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>9</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>9</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>6</mn><msqrt><mn>2</mn><mo>-</mo><mn>5</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><mn>9</mn><mo>+</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>9</mn><mo>+</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>9</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>9</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>6</mn><msqrt><mn>2</mn><mo>-</mo><mn>5</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><mn>9</mn><mo>+</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>9</mn><mo>+</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow></mfrac></math></p>"],
                    solution_en: "<p>7.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi><mo>+</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>5</mn><mi>A</mi><mo>-</mo><mn>5</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mfrac><mrow><mn>9</mn><mi>A</mi></mrow><mn>2</mn></mfrac><mo>-</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>5</mn><mi>A</mi><mo>+</mo><mn>10</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>30</mn><mo>&#176;</mo><mo>+</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>50</mn><mo>&#176;</mo><mo>-</mo><mn>5</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>45</mn><mo>&#176;</mo><mo>-</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>50</mn><mo>+</mo><mn>10</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>30</mn><mo>&#176;</mo><mo>+</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mn>45</mn><mo>&#176;</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>45</mn><mo>&#176;</mo><mo>-</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>+</mo><mn>5</mn><mo>&#215;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow><mrow><mn>9</mn><mo>&#215;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>-</mo><mn>4</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mfrac><mn>5</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow><mrow><mi>&#160;</mi><mfrac><mn>9</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>-</mo><mn>2</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mfrac><mrow><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>5</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>9</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><msqrt><mn>2</mn></msqrt></mfrac></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>5</mn></mrow><mrow><mi>&#160;</mi><mn>9</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                    solution_hi: "<p>7.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>3</mn><mi>A</mi><mo>+</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>5</mn><mi>A</mi><mo>-</mo><mn>5</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mfrac><mrow><mn>9</mn><mi>A</mi></mrow><mn>2</mn></mfrac><mo>-</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>5</mn><mi>A</mi><mo>+</mo><mn>10</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>30</mn><mo>&#176;</mo><mo>+</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>50</mn><mo>&#176;</mo><mo>-</mo><mn>5</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>45</mn><mo>&#176;</mo><mo>-</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mo>(</mo><mn>50</mn><mo>+</mo><mn>10</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>30</mn><mo>&#176;</mo><mo>+</mo><mn>5</mn><mi>c</mi><mi>o</mi><mi>s</mi><mn>45</mn><mo>&#176;</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mn>45</mn><mo>&#176;</mo><mo>-</mo><mn>4</mn><mi>c</mi><mi>o</mi><mi>s</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>+</mo><mn>5</mn><mo>&#215;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow><mrow><mn>9</mn><mo>&#215;</mo><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>-</mo><mn>4</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mfrac><mn>5</mn><msqrt><mn>2</mn></msqrt></mfrac></mrow><mrow><mi>&#160;</mi><mfrac><mn>9</mn><msqrt><mn>2</mn></msqrt></mfrac><mo>-</mo><mn>2</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mfrac><mrow><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>5</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></mrow><mrow><mi>&#160;</mi><mfrac><mrow><mn>9</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><msqrt><mn>2</mn></msqrt></mfrac></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn><msqrt><mn>2</mn></msqrt><mo>+</mo><mn>5</mn></mrow><mrow><mi>&#160;</mi><mn>9</mn><mo>-</mo><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. In which of the following Vedas was Dasarajna war (the war of ten kings) mentioned?</p>",
                    question_hi: "<p>8. निम्नलिखित में से किस वेद में दाशराज्ञ युद्ध (दस राजाओं का युद्ध) का उल्लेख है?</p>",
                    options_en: ["<p>Yajurveda</p>", "<p>Samaveda</p>", 
                                "<p>Rigveda</p>", "<p>Atharvaveda</p>"],
                    options_hi: ["<p>यजुर्वेद</p>", "<p>सामवेद</p>",
                                "<p>ऋग्वेद</p>", "<p>अथर्ववेद</p>"],
                    solution_en: "<p>8.(c) <strong>Rigveda</strong> (Book of Mantras). It consists of 1028 hymns, organized into ten books (maṇḍalas). <strong>Veda </strong>means sacred spiritual knowledge which is a compilation of prayers and hymns. <strong>Other Vedas</strong> - The Sama Veda (Book Of Chant), The Yajur Veda (Book Of Ritual) and The Atharva Veda (Book Of Spell).</p>",
                    solution_hi: "<p>8.(c) <strong>ऋग्वेद</strong> (मंत्रों की पुस्तक) इसमें 1028 शूक्त हैं, जो दस पुस्तकों (मण्डलों) में संगठित हैं। <strong>वेद</strong> का अर्थ पवित्र आध्यात्मिक ज्ञान है जो प्रार्थनाओं और मन्त्रों का संकलन है। <strong>अन्य वेद</strong> - सामवेद (मंत्रोच्चार की पुस्तक), यजुर्वेद (अनुष्ठान की पुस्तक) और अथर्ववेद (जादू -टोना की पुस्तक ) ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. Four equipment are listed, out of which three are alike in some manner and one is different. Select the odd one.</p>",
                    question_hi: "<p>9. चार उपकरण सूचीबद्ध हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ?</p>",
                    options_en: ["<p>Test Tube</p>", "<p>Dropper</p>", 
                                "<p>Compass</p>", "<p>Beaker</p>"],
                    options_hi: ["<p>परखनली</p>", "<p>ड्रॉपर</p>",
                                "<p>कंपास</p>", "<p>बीकर</p>"],
                    solution_en: "<p>9.(c) Out of the four equipments, test tube, dropper and beaker are used to do chemical reactions. Only compass is the equipment which is different here.</p>",
                    solution_hi: "<p>9.(c) चार उपकरणों में से टेस्ट ट्यूब, ड्रॉपर और बीकर का उपयोग रासायनिक प्रतिक्रिया करने के लिए किया जाता है। केवल कंपास ही वह उपकरण है जो यहां अलग है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10.A shopkeeper earns a profit of 17% by selling a book at 10% discount on its printed price. If the purchase price is Rs 500, then the printed p</p>",
                    question_hi: "<p>10. एक दुकानदार एक पुस्तक को मुद्रित मूल्य पर 10% की छूट पर बेचने पर 17% का लाभ अर्जित करता है। यदि क्रय मूल्य 500 रुपये है, तो मुद्रित मूल्य (रुपयों में) है:</p>",
                    options_en: ["<p>615</p>", "<p>750</p>", 
                                "<p>585</p>", "<p>650</p>"],
                    options_hi: ["<p>615</p>", "<p>750</p>",
                                "<p>585</p>", "<p>650</p>"],
                    solution_en: "<p>10.(d)<br>Let printed price = y<br>According to the question,<br>y &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math> = 500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>17</mn></mrow><mn>100</mn></mfrac></math><br>y &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> = 500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>117</mn><mn>100</mn></mfrac></math><br>y = ₹650</p>",
                    solution_hi: "<p>10.(d)<br>माना मुद्रित मूल्य = y<br>प्रश्न के अनुसार,<br>y &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math> = 500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>17</mn></mrow><mn>100</mn></mfrac></math><br>y &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> = 500 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>117</mn><mn>100</mn></mfrac></math><br>y = ₹650</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. What is the percentage share of agricultural workers in India as per Census 2011?</p>",
                    question_hi: "<p>11. 2011 की जनगणना के अनुसार भारत में कृषि श्रमिकों का प्रतिशत हिस्सा कितना है?</p>",
                    options_en: ["<p>54.6%</p>", "<p>64.6%</p>", 
                                "<p>44.6%</p>", "<p>55.6%</p>"],
                    options_hi: ["<p>54.6%</p>", "<p>64.6%</p>",
                                "<p>44.6%</p>", "<p>55.6%</p>"],
                    solution_en: "<p>11.(a) <strong>54.6%. Census</strong> - Conducted by the Office of the Registrar General and Census Commissioner, Ministry of Home Affairs, every 10 years. It is a Union subject under the seventh schedule of the constitution.<strong> First Census</strong> (1881) : Census Commissioner - W.C. Plowden. Census year of 1921 is called the year of &ldquo;The Great Divide&rdquo; (decadal population decline of 0.31%). <strong>Census 2011</strong> : Motto - Our Census, Our future; Census Commissioner - C M Chandramauli; Most populous state - Uttar pradesh; Least populous ​state - Sikkim; Literacy rate - 74.04%.</p>",
                    solution_hi: "<p>11.(a) <strong>54.6% I जनगणना</strong> - गृह मंत्रालय के रजिस्ट्रार जनरल और जनगणना आयुक्त के कार्यालय द्वारा प्रत्येक 10 वर्ष में आयोजित किया जाता है। यह संविधान की सातवीं अनुसूची के तहत संघ का विषय है। <strong>प्रथम जनगणना</strong> (1881) : जनगणना आयुक्त - डब्ल्यूसी प्लॉडेन। 1921 के जनगणना वर्ष को \"द ग्रेट डिवाइड\" (0.31% की दशकीय जनसंख्या गिरावट) का वर्ष कहा जाता है। <strong>जनगणना 2011</strong>: आदर्श वाक्य - हमारी जनगणना, हमारा भविष्य; जनगणना आयुक्त - सी एम चंद्रमौली; सर्वाधिक जनसंख्या वाला राज्य - उत्तर प्रदेश; सबसे कम जनसंख्या वाला राज्य - सिक्किम; साक्षरता दर - 74.04% I</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. Four number pairs have been given, out of which three are alike in some manner and one is different. Select the number pair that is different from the rest?</p>",
                    question_hi: "<p>12. चार संख्या युग्म दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। वह संख्या युग्म चुनिए जो शेष से अलग है?</p>",
                    options_en: ["<p>21 : 23</p>", "<p>11 : 13</p>", 
                                "<p>12 : 15</p>", "<p>17 : 19</p>"],
                    options_hi: ["<p>21 : 23</p>", "<p>11 : 13</p>",
                                "<p>12 : 15</p>", "<p>17 : 19</p>"],
                    solution_en: "<p>12.(c) For the number pairs 21 : 23, 11 : 13, 17 : 19 the difference is 2.<br>Only for 12 : 15, the difference is 3.</p>",
                    solution_hi: "<p>12.(c) संख्या युग्मों के लिए 21: 23, 11: 13, 17: 19 का अंतर 2 है।<br>केवल 12:15 के लिए अंतर 3 है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac></math>is:</p>",
                    question_hi: "<p>13. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac></math>&nbsp;का मान है :</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1 + <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> + 3</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1 + <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> + 3</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>"],
                    solution_en: "<p>13. (a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac></math><br>tan30&deg;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>tan60&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>Cos 30&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math><br>Put above values, <br>We get <br><math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>30</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>13. (a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac></math><br>tan30&deg;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math><br>tan60&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>Cos 30&deg; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>2</mn></mfrac></math><br>इन मानों को समीकरण में रखने पर , <br>हमें मिलता है:<br><math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>30</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>30</mn><mo>&#176;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. Which of the following statements is INCORRECT about plaster of Paris?</p>",
                    question_hi: "<p>14. प्लास्टर ऑफ पेरिस के बारे में इनमें से कौन सा कथन गलत है?</p>",
                    options_en: ["<p>It is also known as calcium sulphate dihydrate.</p>", "<p>It is used for setting fractured bones.</p>", 
                                "<p>It is used in making cosmetics and casts for statues.</p>", "<p>It is used for making surfaces smooth before painting.</p>"],
                    options_hi: ["<p>इसे कैल्शियम सल्फेट डाइहाइड्रेट के नाम से भी जाना जाता है।</p>", "<p>इसका उपयोग टूटी हुई हड्डियों को सेट करने के लिए किया जाता है।</p>",
                                "<p>इसका उपयोग सौंदर्य प्रसाधन और मूर्तियों के लिए संचक (cast) बनाने में किया जाता है।</p>", "<p>इसका उपयोग पेंटिंग से पहले सतहों को चिकना बनाने के लिए किया जाता है।</p>"],
                    solution_en: "<p>14. (a) <strong>Option (a) is incorrect</strong>. Gypsum (Calcium sulfate dihydrate, CaSO<sub>4</sub>. 2H<sub>2</sub>O) is heated at 373 &ndash; 393 K or 150&deg; C or 300&deg; F to manufacture Plaster of Paris. CaSO<sub>4</sub>&middot;2H<sub>2</sub>O &rarr; CaSO<sub>4</sub>&middot;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>H<sub>2</sub>O + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>H<sub>2</sub>O.&nbsp;<strong>Types</strong> - Gypsum Plaster (Used for setting fractured bones in hospitals), Clay Plaster (used in making the interiors of the houses), Lime Plaster (used in building materials, such as wall putty), Cement Plaster (mixture of suitable plaster, Portland cement, sand, and water), Heat Resistant Plaster (used for coating walls and chimney breasts).</p>",
                    solution_hi: "<p>14. (a) <strong>विकल्प (a) गलत है।</strong> प्लास्टर ऑफ पेरिस बनाने के लिए जिप्सम (कैल्शियम सल्फेट डाइहाइड्रेट, CaSO<sub>4</sub>. 2H<sub>2</sub>O) को 373 - 393 K या 150&deg; C या 300&deg; F पर गर्म किया जाता है। CaSO<sub>4</sub>&middot;2H<sub>2</sub>O &rarr; CaSO<sub>4</sub>&middot;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>H<sub>2</sub>O + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>H<sub>2</sub>O.।<strong> प्रकार</strong> - जिप्सम प्लास्टर (अस्पतालों में टूटी हड्डियों को जोड़ने के लिए प्रयुक्त), प्लास्टर मिट्टी (घरों के अंदरूनी भाग बनाने में प्रयुक्त), लाइम प्लास्टर (निर्माण सामग्री में उपयोग किया जाता है, जैसे दीवार पुट्टी), सीमेंट प्लास्टर (उपयुक्त प्लास्टर का मिश्रण, पोर्टलैंड सीमेंट, रेत और पानी), उष्ण प्रतिरोधी प्लास्टर (कोटिंग दीवारों और चिमनी के कवच के लिए उपयोग किया जाता है)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. In a certain code language, FRAGRANCE is written as ESZHQBMDD. How will EXCELLENCE be written as in that language?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में FRAGRANCE को ESZHQBMDD लिखा जाता है। EXCELLENCE को उसी भाषा में कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>DYBFKMDOBF</p>", "<p>DYBYMKDOBF</p>", 
                                "<p>DYBFKMDQBF</p>", "<p>DYBFKMDPBF</p>"],
                    options_hi: ["<p>DYBFKMDOBF</p>", "<p>DYBYMKDOBF</p>",
                                "<p>DYBFKMDQBF</p>", "<p>DYBFKMDPBF</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335569.png\" alt=\"rId6\" width=\"172\" height=\"79\"><br>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335684.png\" alt=\"rId7\" width=\"179\" height=\"73\"></p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335569.png\" alt=\"rId6\" width=\"172\" height=\"79\"><br>उसी प्रकार<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335684.png\" alt=\"rId7\" width=\"179\" height=\"73\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. A sum of rupees 17,200 is lent out at a simple interest in two parts for 2 years at 8% per annum and 10% per annum respectively.If the total interest received after 2 years is rupees 3,008, then the money lent at the rate of 8% per annum is:</p>",
                    question_hi: "<p>16. 17,200 रुपये की राशि दो भागों में 2 साल के लिए क्रमशः 8% वार्षिक और 10% वार्षिक की दर से साधारण ब्याज पर उधार दी जाती है। यदि 2 वर्ष के बाद प्राप्त कुल ब्याज 3,008 रुपये है, तो 8% प्रति वर्ष की दर से उधार दिया गया धन है:</p>",
                    options_en: ["<p>6,400</p>", "<p>9,200</p>", 
                                "<p>9,800</p>", "<p>10,800</p>"],
                    options_hi: ["<p>6,400</p>", "<p>9,200</p>",
                                "<p>9,800</p>", "<p>10,800</p>"],
                    solution_en: "<p>16.(d)<br>For two years rate of interest for amount lent at 8% p.a. = 2 &times; 8 = 16%<br>For two years rate of interest for amount lent at 10% p.a. = 2 &times; 10 = 20%<br>For two years rate of interest for total amount = <math display=\"inline\"><mfrac><mrow><mn>3008</mn></mrow><mrow><mn>17200</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>752</mn><mn>43</mn></mfrac></math> %<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335860.png\" alt=\"rId8\" width=\"96\" height=\"113\"><br>So, money lent at 8% per annum <br>= <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>43</mn></mrow></mfrac></math> &times; 17200 = ₹10,800</p>",
                    solution_hi: "<p>16.(d)<br>8% प्रति वर्ष की दर से उधार दी गई राशि के लिए दो वर्षों के लिए ब्याज की दर = 2 &times; 8 = 16%<br>10% प्रति वर्ष की दर से उधार दी गई राशि के लिए दो वर्षों के लिए ब्याज की दर&nbsp;= 2 &times; 10 = 20%<br>दो वर्षों के लिए कुल राशि पर ब्याज दर <br>= <math display=\"inline\"><mfrac><mrow><mn>3008</mn></mrow><mrow><mn>17200</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>752</mn><mn>43</mn></mfrac></math> %<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386335860.png\" alt=\"rId8\" width=\"96\" height=\"113\"><br>इसलिए, 8% प्रति वर्ष की दर से उधार दिया गया धन = <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>43</mn></mrow></mfrac></math> &times; 17200 = ₹10,800</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17.Who introduced the system of Dagh and Huliyah and cash payment to soldiers in the Delhi sultanate?</p>",
                    question_hi: "<p>17. निम्नलिखित में से किसने दिल्ली सल्तनत में सैनिकों के लिए दाग और हुलिया प्रथा तथा नकद भुगतान व्यवस्था की शुरुआत की थी?</p>",
                    options_en: ["<p>Alauddin Khalji</p>", "<p>Firuz Shah Tughluq</p>", 
                                "<p>Jalaluddin Khalji</p>", "<p>Ghiyasuddin Tughluq</p>"],
                    options_hi: ["<p>अलाउद्दीन खिलजी</p>", "<p>फ़िरोज़ शाह तुगलक</p>",
                                "<p>जलालुद्दीन खिलजी</p>", "<p>गयासुद्दीन तुगलक</p>"],
                    solution_en: "<p>17.(a) <strong>Alauddin Khalji (1296 - 1316) </strong>- Lowered and fixed the price of the commodities of daily use; Laid the foundation of capital &lsquo;Siri&rsquo; (1303 AD) and commissioned a minar (Victory Tower) exceeding the Qutub Minar (could not be completed) and constructed a semicircular gateway (Alai Darwaja). <strong>Firuz Shah Tughluq</strong> (1351-1388): Diwan-e-Bandagani (kept Slaves), Diwan-i-Khairat (take care of the orphans and widows). <strong>Jalaluddin Khalji </strong>(1290-1296): Founder of the Khilji dynasty. <strong>Ghiyasuddin Tughlaq</strong> (1320-24) - Founder of Tughlaq Dynasty, Built the Tughlaqabad fort (One of the 7 cities of Delhi).</p>",
                    solution_hi: "<p>17.(a) <strong>अलाउद्दीन खिलजी (1296 - 1316)</strong> - दैनिक उपयोग की वस्तुओं के दाम में कमी और मूल्य निर्धारित किये । राजधानी &lsquo;<strong>सिरी&rsquo; </strong>की नींव रखी (1303 ई.) और कुतुब मीनार से अधिक एक मीनार (विजय टॉवर) का निर्माण कराया (जो पूर्ण नहीं कराया जा सका) और एक अर्धवृत्ताकार प्रवेश द्वार (अलाई दरवाजा) का निर्माण करवाया । <strong>फ़िरोज़ शाह तुगलक </strong>(1351-1388): दीवान-ए-बंदगानी (गुलामों को रखना), दीवान-ए-खैरात (अनाथों और विधवाओं की देखभाल करना)। <strong>जलालुद्दीन खिलजी</strong> (1290-1296): खिलजी वंश के संस्थापक। <strong>गयासुद्दीन तुगलक</strong> (1320-24) - तुगलक वंश के संस्थापक, ने तुगलकाबाद किले का निर्माण कराया (दिल्ली के 7 शहरों में से एक है )।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. In a code language. If <strong>PETROL</strong> is written as <strong>259346</strong> and <strong>COAL </strong>is written as <strong>8416</strong>, then how will <strong>LOCAL</strong> be written as in that language?</p>",
                    question_hi: "<p>18. एक कोड भाषा में यदि <strong>PETROL</strong> को <strong>259346</strong> और <strong>COAL</strong> को <strong>8416</strong> लिखा जाता है, तो <strong>LOCAL</strong> को उसी भाषा में कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>68146</p>", "<p>64186</p>", 
                                "<p>68416</p>", "<p>64816</p>"],
                    options_hi: ["<p>68146</p>", "<p>64186</p>",
                                "<p>68416</p>", "<p>64816</p>"],
                    solution_en: "<p>18.(d)<br>PETROL is written as 259346 and COAL is written as 8416,<br>i.e. L = 6, O = 4, C = 8, A = 1;<br>So, LOCAL will be written as in that language : 64816</p>",
                    solution_hi: "<p>18.(d)<br>पेट्रोल को 259346 और COAL को 8416 लिखा गया है।<br>अर्थात L = 6, O = 4, C = 8, A = 1;<br>तो, LOCAL को उसी भाषा में लिखा जाएगा : 64816</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. Who found an empirical relationship between the half-life of alpha decay and the energy of the emitted alpha particles in 1911?</p>",
                    question_hi: "<p>19. 1911 में, अल्फा क्षय के अर्द्ध- आयुकाल और उत्सर्जित अल्फा कणों की ऊर्जा के बीच एक प्रयोगसिद्ध संबंध किसने खोजा था?</p>",
                    options_en: ["<p>Chadwick and Lawrence</p>", "<p>Fermi and Meitner</p>", 
                                "<p>Geiger and Nuttall</p>", "<p>Soddy and Aston</p>"],
                    options_hi: ["<p>चैडविक और लॉरेंस (Chadwick and Lawrence)</p>", "<p>फर्मी और मीटनर (Fermi and Meitner)</p>",
                                "<p>गीगर और न्यूटॉल (Geiger and Nuttall)</p>", "<p>सोड्डी और एस्टन (Soddy and Aston)</p>"],
                    solution_en: "<p>19.(c) <strong>Geiger and Nuttall.</strong> It states that short-lived isotopes emit more energetic alpha particles than long-lived ones. <strong>Discoveries : Chadwick</strong> - neutron.<strong> Lawrence</strong> - Cyclotron (A device for accelerating nuclear particles to very high velocities without the use of high voltages). <strong>Enrico Fermi</strong> - Demonstrated first self-sustaining nuclear chain reaction. <strong>Lise Meitner</strong> - First to describe and coin the term nuclear fission. <strong>Frederick Soddy -</strong> All radioactive preparations were not unique elements, but rather that some of them were variants of known elements. <strong>Francis Aston</strong> - Isotopes of the light elements.</p>",
                    solution_hi: "<p>19.(c) <strong>गीगर और न्यूटॉल</strong> I इसमें कहा गया है कि अल्पकालिक आइसोटोप दीर्घकालिक की तुलना में अधिक ऊर्जावान अल्फा कणों का उत्सर्जन करते हैं। <strong>खोज : चैडविक </strong>- न्यूट्रॉन I <strong>लॉरेंस </strong>- साइक्लोट्रॉन (उच्च वोल्टेज के उपयोग के बिना परमाणु कणों को बहुत उच्च वेग तक त्वरित करने के लिए एक उपकरण)। <strong>एनरिको फर्मी</strong> - पहली आत्मनिर्भर परमाणु श्रृंखला प्रतिक्रिया का प्रदर्शन किया। <strong>लिसे मीटनर </strong>- परमाणु विखंडन शब्द का वर्णन करने और उसका प्रयोग करने वाले पहले व्यक्ति। <strong>फ्रेडरिक सोड्डी </strong>- सभी रेडियोधर्मी तैयारी अद्वितीय तत्व नहीं थे, बल्कि उनमें से कुछ ज्ञात तत्वों के रूप थे। <strong>फ्रांसिस एस्टन</strong> - प्रकाश तत्वों के समस्थानिक।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. (a + b - c + d)<sup>2</sup> - (a - b + c - d)<sup>2</sup> = ?</p>",
                    question_hi: "<p>20. (a + b - c + d)<sup>2</sup> - (a - b + c - d)<sup>2</sup> = ?</p>",
                    options_en: ["<p>4a(b + d - c)</p>", "<p>4a(b - d + c)</p>", 
                                "<p>2a(b + c - d)</p>", "<p>2a(a + b - c)</p>"],
                    options_hi: ["<p>4a(b + d -c)</p>", "<p>4a(b - d + c)</p>",
                                "<p>2a(b + c - d)</p>", "<p>2a(a + b - c)</p>"],
                    solution_en: "<p>20. (a) In the equation: <br>(a + b - c + d)<sup>2</sup> - (a - b + c - d)<sup>2</sup>&nbsp;<br>Use formula :<br>x<sup>2</sup> - y<sup>2</sup> = (x - y)(x + y)<br>(a + b - c + d)<sup>2 </sup>- (a - b + c - d)<sup>2</sup> =&nbsp;<br>(a + b - c + d + a - b + c - d)(a + b - c + d - a + b - c + d)<br>(2a)(2(b - c + d))<br>4a(b - c + d)</p>",
                    solution_hi: "<p>20. (a) समीकरण में:<br>(a + b - c + d)<sup>2</sup> - (a - b + c - d)<sup>2</sup>&nbsp;<br>सूत्र का उपयोग करने पर :<br>x<sup>2</sup> - y<sup>2</sup> = (x - y)(x + y)<br>(a + b - c + d)<sup>2 </sup>- (a - b + c - d)<sup>2</sup> =&nbsp;<br>(a + b - c + d + a - b + c - d)(a + b - c + d - a + b - c + d)<br>(2a)(2(b - c + d))<br>4a(b - c + d)</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. Which of the following numbers will replace the question mark (?) in the given series?<br>65, 90, 107, 116, 121, ?</p>",
                    question_hi: "<p>21. उस संख्या का चयन कीजिए, जो निम्नलिखित श्रेणी में प्रश्न चिह्न (?) के स्थान पर आएगी।<br>65, 90, 107, 116, 121, ?</p>",
                    options_en: ["<p>123</p>", "<p>125</p>", 
                                "<p>122</p>", "<p>124</p>"],
                    options_hi: ["<p>123</p>", "<p>125</p>",
                                "<p>122</p>", "<p>124</p>"],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386336013.png\" alt=\"rId9\" width=\"259\" height=\"65\"></p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386336013.png\" alt=\"rId9\" width=\"259\" height=\"65\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. Nati is a folk dance from which of the following states of India?</p>",
                    question_hi: "<p>22. नाटी निम्नलिखित में से भारत के किस राज्य का लोक नृत्य है?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Kerala</p>", 
                                "<p>Andhra Pradesh</p>", "<p>Himachal Pradesh</p>"],
                    options_hi: ["<p>नागालैंड</p>", "<p>केरल</p>",
                                "<p>आंध्र प्रदेश</p>", "<p>हिमाचल प्रदेश</p>"],
                    solution_en: "<p>22.(d) <strong>Himachal Pradesh. States and their folk dances: Himachal Pradesh</strong> - Rakshasa (demon), Kayang, Bakayang, the Bnayangchu, the Jataru Kayang, Chohara, Shand and Shabu, Lang-dar-ma, Jhanjhar, Jhoor, Gi, and Rasa. <strong>Nagaland </strong>- Modse, Agurshikukula, Aaluyattu, Sadal Kekai, Changai Dance, Kuki Dance, Leshalaptu, Monyoasho, Rengma, Seecha, Kukui Kucho, Shankai, Moyashai, Zeliang Dance.<strong> Kerala</strong> - Kathakali, Mohiniyattam, Theyyam, Thirvathirakali, Kolkali, Ottamthullal, Padayani, Margamkali, Oppana, Koodiyattam. <strong>Andhra Pradesh</strong> - Kuchipudi, Vilasini Natyam, Andhra Natyam, Bhamakalpam, Veeranatyam, Dappu, Tappeta Gullu, Lambadi, Dhimsa, Kolattam, Butta Bommalu.</p>",
                    solution_hi: "<p>22.(d) <strong>हिमाचल प्रदेश। राज्य और उनके लोक नृत्य: हिमाचल प्रदेश -</strong> राक्षस (डेमन), कायांग, बाकायांग, बनयांगचू, जटारू कायांग, छोहारा, शांड और शाबू, लंग-दार-मा, झांझर, झूर, गी और रासा। <strong>नागालैंड</strong> - मोदसे, अगुर्शिकुकुला, आलुयट्टु, सदल केकाई, चांगाई नृत्य, कुकी नृत्य, लेशालप्तु, मोन्योआशो, रेंगमा, सीचा, कुकुई कुचो, शंकाई, मोयाशाई, ज़ेलियांग नृत्य। <strong>केरल</strong> - कथकली, मोहिनीअट्टम, थेय्यम, तिरुवतिराकली, कोलकाली, ओट्टमथुल्लल, पदयानी, मार्गमकली, ओप्पाना, कूडियाट्टम। <strong>आंध्र प्रदेश </strong>- कुचिपुड़ी, विलासिनी नाट्यम, आंध्र नाट्यम, भामाकल्पम, वीरनाट्यम, दप्पू, तप्पेटा गुल्लू, लंबाडी, ढिम्सा, कोलाट्टम, बुट्टा बोम्मलु।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. Select the combination of letters that when sequentially placed in the blanks will create a repetitive pattern.<br>aba_bca_ac_cab_cbc</p>",
                    question_hi: "<p>23. अक्षरों के संयोजन का चयन कीजिये जिसे रिक्त स्थान में क्रमिक रूप से रखा जाए तो एक दोहराव वाला पैटर्न बन जाएगा<br>aba_bca_ac_cab_cbc</p>",
                    options_en: ["<p>cbba</p>", "<p>cbab</p>", 
                                "<p>cabb</p>", "<p>cbaa</p>"],
                    options_hi: ["<p>cbba</p>", "<p>cbab</p>",
                                "<p>cabb</p>", "<p>cbaa</p>"],
                    solution_en: "<p>23.(a)<br>aba_bca_ac_cab_cbc<br>Now if we part it into small segments of 3 letters :<br>aba / _bc / a_a / c_c / ab_ / cbc<br>Then we can easily find the pattern as :<br>aba / <span style=\"text-decoration: underline;\">c</span>bc / a<span style=\"text-decoration: underline;\">b</span>a / c<span style=\"text-decoration: underline;\">b</span>c / ab<span style=\"text-decoration: underline;\">a</span> / cbc</p>",
                    solution_hi: "<p>23.(a)<br>aba_bca_ac_cab_cbc<br>अब अगर हम इसे 3 अक्षरों के छोटे-छोटे खंडों में विभाजित करते हैं:<br>aba / _bc / a_a / c_c / ab_ / cbc<br>तब हम आसानी से पैटर्न पा सकते हैं:<br>aba / <span style=\"text-decoration: underline;\">c</span>bc / a<span style=\"text-decoration: underline;\">b</span>a / c<span style=\"text-decoration: underline;\">b</span>c / ab<span style=\"text-decoration: underline;\">a</span> / cbc</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. The number of students enrolled in different faculties in a school is as follows:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386336139.png\" alt=\"rId10\" width=\"382\" height=\"71\"> <br>The percentage of students studying in Science or Vocational subjects is:</p>",
                    question_hi: "<p>24. एक विद्यालय में अलग-अलग विषयों में नामांकन लेने वाले छात्रों की संख्या इस प्रकार है : <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386336139.png\" alt=\"rId10\" width=\"382\" height=\"71\"><br>विज्ञान या व्यावसायिक विषयों की पढ़ाई करने वाले छात्रों का प्रतिशत है :</p>",
                    options_en: ["<p>25%</p>", "<p>93%</p>", 
                                "<p>37.2%</p>", "<p>50%</p>"],
                    options_hi: ["<p>25%</p>", "<p>93%</p>",
                                "<p>37.2%</p>", "<p>50%</p>"],
                    solution_en: "<p>24. (c) Total number of students studying in a school = 250<br>Students studying in science or vocational studies = 35 + 18 + 10 + 30 = 93<br>% students studying in science or vocational studies = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>93</mn><mn>250</mn></mfrac></math> &times; 100 = 37.2%</p>",
                    solution_hi: "<p>24. (c) <br>कुल छात्रों की संख्या = 250<br>विज्ञान या व्यावसायिक अध्ययन में पढ़ने वाले छात्रों की संख्या = 35 + 18 + 10 + 30 = 93<br>अब, विज्ञान या व्यावसायिक अध्ययन में पढ़ने वाले छात्रों का प्रतिशत</p>\n<p>=&nbsp;<math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</mi><mo>&#160;</mo><mi>&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2357;&#2381;&#2351;&#2366;&#2357;&#2360;&#2366;&#2351;&#2367;&#2325;</mi><mo>&#160;</mo><mi>&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</mi><mo>&#160;</mo><mi>&#2325;&#2375;</mi><mo>&#160;</mo><mi>&#2331;&#2366;&#2340;&#2381;&#2352;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2331;&#2366;&#2340;&#2381;&#2352;</mi></mrow></mfrac></math></p>\n<p>&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>93</mn><mn>250</mn></mfrac></math> &times; 100 = 37.2%<br>अतः, विज्ञान या व्यावसायिक अध्ययन में पढ़ने वाले छात्रों का प्रतिशत 37.2% है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. Which of the following rivers is INCORRECTLY matched with its respective place of origin?</p>",
                    question_hi: "<p>25. निम्नलिखित में से कौन-सी नदी अपने उद्गम स्थल के साथ गलत सुमेलित है?</p>",
                    options_en: ["<p>Mahanadi - Sihawa Mountain</p>", "<p>Sutlej - Dal Lake</p>", 
                                "<p>Ganga - Gangotri glacier</p>", "<p>Godavari - Trimbakeshwar</p>"],
                    options_hi: ["<p>महानदी - सिहावा पर्वत</p>", "<p>सतलुज - डल झील</p>",
                                "<p>गंगा - गंगोत्री ग्लेशियर</p>", "<p>गोदावरी - त्र्यंबकेश्वर</p>"],
                    solution_en: "<p>25.(b) <strong>Sutlej River</strong> originates near Lake Rakhastal in Tibet, near the Kailash Mountain Range, flows west to southwest, entering India through the <strong>Shipki La Pass</strong> in Himachal Pradesh. <strong>Indus -</strong> Bokhar Chu glacier (Tibet). <strong>Brahmaputra</strong> - Chemayungdung Glacier (Tibet). <strong>Yamuna</strong> - Yamunotri glacier (near Bandarpoonch peaks). <strong>Krishna</strong> - Mahabaleshwar (western Ghats).<strong> Cauvery</strong> - Brahmagiri range (Coorg). <strong>Narmada</strong> - Maikala range (near Amarkantak). <strong>Tapi</strong> - Satpura Range.</p>",
                    solution_hi: "<p>25.(b) <strong>सतलज नदी </strong>की उत्पत्ति तिब्बत में राक्षस ताल झील के पास, कैलाश पर्वत श्रृंखला के पास होती है, यह पश्चिम से दक्षिण-पश्चिम की ओर प्रवाहित होती है, और हिमाचल प्रदेश में शिपकी ला दर्रे के माध्यम से भारत में प्रवेश करती है। <strong>सिन्धु</strong> - बोखार चू ग्लेशियर (तिब्बत)। <strong>ब्रह्मपुत्र </strong>- चेमयुंगडुंग ग्लेशियर (तिब्बत)। <strong>यमुना</strong> - यमुनोत्री ग्लेशियर (बंदरपूंछ चोटियों के पास)। <strong>कृष्णा</strong> - महाबलेश्वर (पश्चिमी घाट)। <strong>कावेरी </strong>- ब्रह्मगिरि श्रेणी (कूर्ग)। <strong>नर्मदा </strong>- मैकला श्रेणी (अमरकंटक के निकट)।<strong> तापी</strong> - सतपुड़ा की पहाड़ियों से ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. Study the given pattern carefully and select the number that can replace the question mark (?) in it.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386336346.png\" alt=\"rId11\" width=\"257\" height=\"97\"></p>",
                    question_hi: "<p>26. दिए गए पैटर्न का ध्यानपूर्वक अध्ययन कीजिये और उस संख्या का चयन कीजिये जो उसमें प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386336346.png\" alt=\"rId11\" width=\"247\" height=\"93\"></p>",
                    options_en: ["<p>9</p>", "<p>11</p>", 
                                "<p>37</p>", "<p>5</p>"],
                    options_hi: ["<p>9</p>", "<p>11</p>",
                                "<p>7</p>", "<p>5</p>"],
                    solution_en: "<p>26.(d)<br>40 - (22 - 12) = 30<br>34 - ( 10 - 16) = 40<br>28 - ( 5 - 20 ) = 43</p>",
                    solution_hi: "<p>26.(d)<br>40 - (22 - 12) = 30<br>34 - ( 10 - 16) = 40<br>28 - ( 5 - 20 ) = 43</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. From the top of a 195m high cliff, the angles of depression of the top and bottom of the tower are 30&deg; and 60&deg; respectively. Find the height of the tower(in m).</p>",
                    question_hi: "<p>27. एक 195 मीटर ऊंची चट्टान की चोटी से, टावर के ऊपर और नीचे के अवनमन कोण क्रमशः 30&deg; और 60&deg; हैं। मीनार की ऊँचाई ज्ञात कीजिए (मीटर में)।</p>",
                    options_en: ["<p>195<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>195</p>", 
                                "<p>130</p>", "<p>65</p>"],
                    options_hi: ["<p>195<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>195</p>",
                                "<p>130</p>", "<p>65</p>"],
                    solution_en: "<p>27.(c)<br>Let ED be a tower and AC be a cliff of height = 195m <br>Let BC = x, so AB = 195 - x <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386336624.png\" alt=\"rId12\" width=\"152\" height=\"131\"><br>In &Delta;ACD,<br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>C</mi><mi>D</mi></mrow></mfrac></math> <br><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>195</mn><mi>y</mi></mfrac></math> <br>y = 65<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m<br>So, BE = CD = 65<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m<br>In &Delta;ABE,<br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>E</mi></mrow></mfrac></math> <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>195</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>65</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math><br>195 - x = 65<br><math display=\"inline\"><mi>x</mi></math> = 130 m<br>So, height of tower = 130 m &hellip;..(ED = BC)</p>",
                    solution_hi: "<p>27.(c)<br>माना ED एक मीनार है और AC ऊँचाई की चट्टान है = 195m<br>माना BC = x, अत: AB = 195 &ndash; x<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386336624.png\" alt=\"rId12\" width=\"152\" height=\"131\"><br>&Delta;ACD में,<br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>C</mi></mrow><mrow><mi>C</mi><mi>D</mi></mrow></mfrac></math> <br><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>195</mn><mi>y</mi></mfrac></math> <br><math display=\"inline\"><mi>y</mi></math> = 65<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m<br>इसलिए, BE = CD = 65<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> m<br>&Delta;ABE में,<br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>E</mi></mrow></mfrac></math> <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>195</mn><mo>-</mo><mi>x</mi></mrow><mrow><mn>65</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math><br>195 - x = 65<br><math display=\"inline\"><mi>x</mi></math> = 130 m<br>इसलिए, टावर की ऊंचाई = 130 m &hellip;..(ED = BC)</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. Vitamins and minerals are helpful for which of the following?</p>",
                    question_hi: "<p>28. विटामिन और खनिज निम्नलिखित में से किसके लिए सहायक होते हैं?</p>",
                    options_en: ["<p>Proper breathing</p>", "<p>Proper sweating</p>", 
                                "<p>Proper fat storage</p>", "<p>Carry out metabolic reactions in our body</p>"],
                    options_hi: ["<p>उचित श्वसन</p>", "<p>उचित पसीना</p>",
                                "<p>उचित वसा भंडारण</p>", "<p>हमारे शरीर में उपापचयी अभिक्रियाओं के लिए</p>"],
                    solution_en: "<p>28.(d) Vitamin A, D, E and K - Fat soluble. Vitamin B and C - Water soluble. <strong>Vitamins and their sources</strong>: Vitamin A - Milk, egg. Vitamin D - Cheese, butter, milk. Vitamin E - vegetable oils, lettuce, turnip leaves. Vitamin K - spinach and soybeans. Vitamin B - Seafood, milk, meat, peas. <strong>Minerals and their sources:</strong> Iron - Meat, fish, liver, eggs, turnip, germinating wheat grains and yeast. Calcium - Milk. Phosphorus - Meat, egg, fish, whole grains. Potassium - Green and yellow vegetables. Sodium - meat and milk. Iodine - Iodised salt, seafood and water. Fluoride - Coffee, spinach, onion and tea. Copper - Grains, nuts and chocolate. Zinc - Meat, eggs and fish. Chloride - Meat, milk and fish.</p>",
                    solution_hi: "<p>28.(d) विटामिन A, D, E और K - वसा में घुलनशील होते है । विटामिन B और C - जल में घुलनशील होते है। <strong>विटामिन और उनके स्रोत</strong>: विटामिन A - दूध, अंडा। विटामिन D - पनीर, मक्खन, दूध। विटामिन E - वनस्पति तेल, सलाद, शलजम के पत्ते। विटामिन K - पालक और सोयाबीन। विटामिन B - समुद्री भोजन, दूध, मांस, मटर। <strong>खनिज और उनके स्रोत: </strong>आयरन - मांस, मछली, लिवर, अंडे, शलजम, अंकुरित गेहूं के दाने और खमीर। कैल्शियम - दूध, फास्फोरस - मांस, अंडा, मछली, साबुत अनाज। पोटैशियम - हरी और पीली सब्जियाँ। सोडियम - मांस और दूध। आयोडीन - आयोडीन युक्त नमक, समुद्री भोजन और जल। फ्लोराइड - कॉफी, पालक, प्याज और चाय। कॉपर (ताँबा) - अनाज, मेवे और चॉकलेट। जिंक - मांस, अंडे और मछली। क्लोराइड - मांस, दूध और मछली।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29.Three statements are followed by three conclusions numbered I, II and III. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow(s) from the given statement.<br><strong>Statements:</strong><br>Some woods are charcoals<br>Some petrol is diesel<br>All diesel are fossils<br><strong>Conclusions:</strong><br>(I) Some petrol are charcoals<br>(II) Some fossils are petrol<br>(III) Some woods are diesel</p>",
                    question_hi: "<p>29. तीन कथन और उनके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए विचार करें, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों, और बताएं कि कौन से निष्कर्ष तार्किक रूप से दिए गए कथनों का पालन करते हैं?<br><strong>कथन:</strong><br>कुछ काष्ठ , चारकोल हैं।<br>कुछ पेट्रोल, डीजल हैं।<br>सभी डीजल, जीवाश्म हैं।<br><strong>निष्कर्ष:</strong><br>(I) कुछ पेट्रोल, चारकोल हैं।<br>(II) कुछ जीवाश्म , पेट्रोल हैं।<br>(III) कुछ काष्ठ , डीजल हैं।</p>",
                    options_en: ["<p>None of the conclusions follow</p>", "<p>Only conclusion II follows</p>", 
                                "<p>Only conclusion I and III follows</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>कोई भी निष्कर्ष पालन नहीं करता है।</p>", "<p>केवल निष्कर्ष ॥ पालन करता है।</p>",
                                "<p>केवल निष्कर्ष I और III पालन करते हैं।</p>", "<p>केवल निष्कर्ष । पालन करता है।</p>"],
                    solution_en: "<p>29.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386336996.png\" alt=\"rId13\" width=\"229\" height=\"85\"><br>From the above diagram we can see that only conclusion II follows.</p>",
                    solution_hi: "<p>29.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386337129.png\" alt=\"rId14\" width=\"230\" height=\"91\"><br>उपरोक्त आरेख से हम देख सकते हैं कि केवल निष्कर्ष II पालन करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. In the given figure, AP and BP are tangents to a circle with centre O. If &ang;APB = 62&deg; then the measure of &ang;AQB is:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386337268.png\" alt=\"rId15\" width=\"144\" height=\"87\"></p>",
                    question_hi: "<p>30. दी गयी आकृति में, AP और BP वृत्त की स्पर्श रेखाएँ हैं जिसका केंद्र O है | यदि &ang;APB = 62&deg; है, तो &ang;AQB का मान क्या होगा ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386337268.png\" alt=\"rId15\" width=\"141\" height=\"85\"></p>",
                    options_en: ["<p>28&deg;</p>", "<p>59&deg;</p>", 
                                "<p>31&deg;</p>", "<p>118&deg;</p>"],
                    options_hi: ["<p>28&deg;</p>", "<p>59&deg;</p>",
                                "<p>31&deg;</p>", "<p>118&deg;</p>"],
                    solution_en: "<p>30. (b) In the given diagram<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386337375.png\" alt=\"rId16\"><br>AP and BP are tangents and &ang;APB = 62&deg;,<br>We know that:<br>sum of angles of a quadrilateral is 360&deg; and Tangent is always perpendicular to the circle at the point of intersection<br>Hence,<br>&ang;APB + &ang;AOB + &ang;OBP + &ang;OAP = 360&deg;<br>62&deg; + &ang;AOB + 90&deg; + 90&deg; = 360&deg;<br>&ang;AOB&nbsp; = 118&deg;<br>Angle formed at the circumference of a circle is half the angle formed at the centre of the circle.<br>&ang;AQB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>A</mi><mi>O</mi><mi>B</mi></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>118</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math> = 59&deg;</p>",
                    solution_hi: "<p>30. (b) दिए गए चित्र में:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386337375.png\" alt=\"rId16\"><br>AP और BP वृत के स्पर्शरेखा (tangents) हैं और &ang;APB = 62&deg; <br>हम जानते हैं कि:किसी चतुर्भुज के कोणों का योग 360&deg; होता है, और स्पर्शरेखा हमेशा वृत के स्पर्श बिंदु पर लंबवत (perpendicular) होती है।<br>इसलिए,<br>&ang;APB + &ang;AOB + &ang;OBP + &ang;OAP = 360&deg;<br>अब, 62&deg; + &ang;AOB + 90&deg; + 90&deg; = 360&deg;<br>&ang;AOB&nbsp; = 118&deg;<br>वृत्त की परिधि पर बना कोण, वृत्त के केंद्र पर बने कोण का आधा होता है।<br>इसलिए, &ang;AQB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8736;</mo><mi>A</mi><mi>O</mi><mi>B</mi></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>118</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math> = 59&deg;</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. Which festival has the meaning \'to boil\' or \'boiling\' in its name ?</p>",
                    question_hi: "<p>31. निम्नलिखित में से किस त्यौहार के नाम में उबालना\' या \'उबलना\' का अर्थ जुड़ा है?</p>",
                    options_en: ["<p>Pongal</p>", "<p>Onam</p>", 
                                "<p>Raja</p>", "<p>Bihu</p>"],
                    options_hi: ["<p>पोंगल</p>", "<p>ओणम</p>",
                                "<p>राजा</p>", "<p>बिहु</p>"],
                    solution_en: "<p>31.(a) <strong>Pongal</strong> (harvest festival) - considered to be one of the major festivals of South India and it is mainly celebrated in Tamil Nadu. Other harvest festivals of India : <strong>Onam </strong>- Kerala. <strong>Raja Parba </strong>or (Mithuna Sankranti) - Odisha. <strong>Bihu </strong>- Assam. Lohri - Punjab. Makar Sankranti - Andhra Pradesh and Telangana. Baisakhi - Punjab and Haryana.</p>",
                    solution_hi: "<p>31.(a) <strong>पोंगल</strong> (फसल उत्सव) - दक्षिण भारत के प्रमुख त्योहारों में से एक माना जाता है और यह मुख्य रूप से तमिलनाडु में मनाया जाता है। भारत के अन्य फसल त्यौहार: <strong>ओणम</strong> - केरल। <strong>राजा परबा</strong> या (मिथुन संक्रांति) - ओडिशा। <strong>बिहू</strong> - असम। लोहड़ी - पंजाब। मकर संक्रांति - आंध्र प्रदेश और तेलंगाना। बैसाखी - पंजाब और हरियाणा में मनाया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. The value of -<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &divide; 6 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> is equal to:</p>",
                    question_hi: "<p>32. - <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &divide; 6 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&nbsp;का मान किसके बराबर है ?</p>",
                    options_en: ["<p>-<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p>-<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p>-<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>-<math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>-<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", "<p>-<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p>-<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>-<math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>32. (d) On applying BODMAS rule in the given equation: <br>We get , -<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &divide; 6 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>= -<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>8</mn></mfrac></math></p>",
                    solution_hi: "<p>32. (d) BODMAS नियम लागू करने पर दिए गए समीकरण में:<br>We get , -<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &divide; 6 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>= -<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>8</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. Which Article of the Constitution of India mentions that law declared by the Supreme Court is binding on all courts?</p>",
                    question_hi: "<p>33. भारतीय संविधान के किस अनुच्छेद में उल्लेख है कि सर्वोच्च न्यायालय द्वारा घोषित कानून सभी न्यायालयों पर बाध्यकारी है?</p>",
                    options_en: ["<p>Article 143</p>", "<p>Article 142</p>", 
                                "<p>Article 144</p>", "<p>Article 141</p>"],
                    options_hi: ["<p>अनुच्छेद 143</p>", "<p>अनुच्छेद 142</p>",
                                "<p>अनुच्छेद 144</p>", "<p>अनुच्छेद 141</p>"],
                    solution_en: "<p>33.(d) <strong>Article 141. Article 143</strong> - Power of the President to consult the Supreme Court. <strong>Article 142</strong> - Enforcement of decrees and orders of the Supreme Court and orders as to discovery. <strong>Article 144</strong> - Civil and judicial authorities to act in aid of the Supreme Court.</p>",
                    solution_hi: "<p>33.(d) <strong>अनुच्छेद 141। अनुच्छेद 143</strong> - सर्वोच्च न्यायालय से परामर्श करने की राष्ट्रपति की शक्ति। <strong>अनुच्छेद 142</strong> - सर्वोच्च न्यायालय के आदेशों और आदेशों की प्रवर्तन और खोज के अनुसार आदेश। <strong>अनुच्छेद 144 -</strong> नागरिक और न्यायिक अधिकारीयों को सर्वोच्च न्यायालय की सहायता के लिए कार्य करना।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. Given below is a statement followed by two possible underlying assumptions I and II. Read the information carefully and select the correct option.<br><strong>Statement:</strong><br>Research has suggested that regular physical activity not only reduces the chances of physical illness but also of mental illnesses.<br>Which of the following can be assumed by the given statement?<br>I. The medicines for mental illness would work faster if the patient also exercises daily.<br>II. Patients who are bedridden have a greater chance of developing some mental illness as compared to those who are not bedridden.</p>",
                    question_hi: "<p>34. नीचे एक कथन दिया गया है जिसके बाद दो संभावित अंतर्निहित धारणाएँ I और II हैं। जानकारी को ध्यान से पढ़ें और सही विकल्प का चयन करें।<br><strong>कथन:</strong><br>अनुसंधान ने सुझाव दिया है कि नियमित शारीरिक गतिविधि न केवल शारीरिक बीमारी बल्कि मानसिक बीमारियों की संभावना को भी कम करती है।<br>दिए गए कथन द्वारा निम्नलिखित में से कौन सा माना जा सकता है?<br>I. यदि रोगी प्रतिदिन व्यायाम भी करे तो मानसिक बीमारी की दवाएँ तेजी से काम करेंगी।<br>II. बिस्तर पर पड़े रहने वाले रोगियों में उन लोगों की तुलना में कुछ मानसिक बीमारी विकसित होने की संभावना अधिक होती है, जो बिस्तर पर नहीं होते हैं।</p>",
                    options_en: ["<p>Neither I nor II can be assumed</p>", "<p>Both I and II can be assumed</p>", 
                                "<p>Only I can be assumed</p>", "<p>Only II can be assumed</p>"],
                    options_hi: ["<p>न तो I और न ही II माना जा सकता है</p>", "<p>I और II दोनों को माना जा सकता है</p>",
                                "<p>केवल I माना जा सकता है</p>", "<p>केवल II माना जा सकता है</p>"],
                    solution_en: "<p>34.(d)<br>Since, assumption <strong>II</strong> is directly related to the statement rather than assumption<strong> I.</strong><br>Hence, <strong>Only II can be assumed.</strong></p>",
                    solution_hi: "<p>34.(d)<br>चूँकि, पूर्वधारणा <strong>II,</strong> पूर्वधारणा <strong>I </strong>के बजाय सीधे कथन से संबंधित है।<br><strong>इसलिए, केवल पूर्वधारणा II को माना जा सकता है है।</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. Identify the animal that does NOT have bilateral symmetry.</p>",
                    question_hi: "<p>35. उस जानवर की पहचान करें जिसमें द्विपक्षीय समरूपता नहीं है।</p>",
                    options_en: ["<p>Annelida</p>", "<p>Molluscs</p>", 
                                "<p>Arthropoda</p>", "<p>Cnidarian</p>"],
                    options_hi: ["<p>एनेलिडा</p>", "<p>मोलस्क</p>",
                                "<p>आर्थ्रोपोडा</p>", "<p>निडारियन</p>"],
                    solution_en: "<p>35.(d) <strong>Cnidarian. Bilateral symmetry </strong>- When the body plan of an animal can be divided along a line that separates the animal\'s body into right and left halves that are nearly identical to each other. Examples - Flatworms, Common worms (ribbon worms), Clams, Snails, Insects, Spiders, Sea urchins, and Vertebrates. <strong>Spherical symmetry </strong>- Found mainly in Protozoa (Volvox, Heliozoa, and Radiolaria). <strong>Radial symmetry</strong> - Found in the cnidarians (including jellyfish, sea anemones, and coral) and echinoderms (such as sea urchins, brittle stars, and sea stars). <strong>Biradial symmetry </strong>- Comb jellies.</p>",
                    solution_hi: "<p>35.(d) <strong>निडारियन I द्विपक्षीय समरूपता</strong> - जब किसी जानवर के शारीरिक संरचना को एक रेखा के साथ दाएं और बाएं आधे हिस्सों में विभाजित किया जा सकता है जो लगभग एक दूसरे के समान होते हैं। उदाहरण - चपटे कृमि, सामान्य कृमि (रिबन कृमि), क्लैम, घोंघे, कीड़े, मकड़ियाँ, समुद्री अर्चिन और कशेरुकी जीव I <strong>गोलाकार समरूपता- </strong>मुख्य रूप से प्रोटोज़ोआ (वोल्वॉक्स, हेलियोज़ोआ और रेडिओलारिया) में पाया जाता है। <strong>रेडियल समरूपता -</strong> निडारियन (जेलीफ़िश, समुद्री एनीमोन और मूंगा सहित) और इचिनोडर्म (जैसे समुद्री अर्चिन, भंगुर तारे और समुद्री तारे) में पाया जाता है। <strong>बिराडियल समरूपता</strong> - कॉम्ब जेली।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. In an election between two candidates, 5% of the registered voters did not caste their vote. 10% of the votes were found to be either invalid or of NOTA. The winning candidate received 60% of the votes in his favour and won the election by 17271 votes. FInd the number of registered voters.</p>",
                    question_hi: "<p>36. दो उम्मीदवारों के बीच एक चुनाव में, पंजीकृत मतदाताओं में से 5% ने अपना वोट नहीं डाला। 10% मतदाताओं को अमान्य या NOTA पाया गया । जीतने वाले उम्मीदवार ने अपने पक्ष में 60% वोट प्राप्त किए और 17271 वोटों से चुनाव जीता। पंजीकृत मतदाताओं की संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>90525</p>", "<p>100000</p>", 
                                "<p>101000</p>", "<p>102500</p>"],
                    options_hi: ["<p>90525</p>", "<p>100000</p>",
                                "<p>101000</p>", "<p>102500</p>"],
                    solution_en: "<p>36.(c)<br>Let total number of registered voters = 100<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386337610.png\" alt=\"rId17\" width=\"178\" height=\"144\"><br>According to the question,<br>Winner got 60% of valid votes and the loser got 40% of valid votes.<br>So, 60% - 40% = 20% which corresponds to 17271 votes.<br>So, total number of valid votes <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17271</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>20</mn></mfrac></math> = 86355<br>Now, 86355 corresponds to 85.5, so total number of registered voters <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>86355</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>85</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 101000</p>",
                    solution_hi: "<p>36.(c)<br>मान लीजिये कि पंजीकृत मतदाताओं की कुल संख्या = 100<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386337610.png\" alt=\"rId17\" width=\"178\" height=\"144\"><br>प्रश्न के अनुसार,<br>विजेता को वैध मतों का 60% और हारने वाले को वैध मतों का 40% प्राप्त हुआ।<br>तो, 60% - 40% = 20% जो 17271 मतों के अनुरूप है।<br>तो, वैध मतों की कुल संख्या<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17271</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>20</mn></mfrac></math> = 86355<br>अब, 86355, 85.5 के तुल्य है, इसलिए पंजीकृत मतदाताओं की कुल संख्या <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>86355</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>85</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 101000</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. Read the given statements and conclusions carefully and decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>In a T-20 cricket match, total wickets taken by a team were 9. Out of these, 6 wickets were taken by spinners.<br><strong>Conclusions:</strong><br>A: 80% of the bowlers in the team were spinners.<br>B: The opening bowlers of the team were spinners.</p>",
                    question_hi: "<p>37. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़ें और बताइये कि कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>एक T-20 क्रिकेट मैच में, एक टीम द्वारा लिए गए कुल विकेट 9 थे। इनमें से 6 विकेट स्पिनरों द्वारा लिए गए थे।<br><strong>निष्कर्ष:</strong><br>A: टीम में 80% गेंदबाज स्पिनर थे।<br>B: टीम के ओपनिंग गेंदबाज स्पिनर थे।</p>",
                    options_en: ["<p>Only Conclusion A follows.</p>", "<p>Only Conclusion B follows.</p>", 
                                "<p>Both Conclusion A and Conclusion B follow.</p>", "<p>Neither conclusion A nor conclusion B follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष A अनुसरण करता है।</p>", "<p>केवल निष्कर्ष B अनुसरण करता है।</p>",
                                "<p>निष्कर्ष A और निष्कर्ष B दोनों अनुसरण करते हैं।</p>", "<p>न तो निष्कर्ष A और न ही निष्कर्ष B अनुसरण करता है।</p>"],
                    solution_en: "<p>37.(d) Here neither conclusion A nor conclusion B follows, because the statement provides no information about the opening bowlers and number of spinner bowlers.</p>",
                    solution_hi: "<p>37.(d) यहाँ न तो निष्कर्ष A और न ही निष्कर्ष B अनुसरण करता है, क्योंकि कथन में सलामी गेंदबाजों और स्पिनर गेंदबाजों की संख्या के बारे में कोई जानकारी नहीं दी गई है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. The length of the pitch in cricket is_________.</p>",
                    question_hi: "<p>38. क्रिकेट में पिच की लंबाई_______होती है।</p>",
                    options_en: ["<p>20 yards</p>", "<p>24 yards</p>", 
                                "<p>22 yards</p>", "<p>18 yards</p>"],
                    options_hi: ["<p>20 यार्ड</p>", "<p>24 यार्ड</p>",
                                "<p>22 यार्ड</p>", "<p>18 यार्ड</p>"],
                    solution_en: "<p>38.(c) <strong>22 yards. </strong>Dimensions of <strong>Cricket Wicket</strong> - Height of 28&rdquo; (71.12 cm) and width of 9&rdquo; (22.86 cm). Bat - Length should be not more than 38 inches (96.5 cm) and the width not more than 4.25 inches (10.8 cm) and made up of Wood only. <strong>Ball</strong> - Weight between 5.5 ounces and 5.75 ounce, and shall measure between 22.4 cm and 22.9 cm in circumference. <strong>Highest Governing Body</strong> - International Cricket Council (ICC), Headquarter - Dubai (UAE); Establishment - 15 June 1909. Board of Control for Cricket in India (BCCI), Headquarter - Mumbai. Establishment - December 1928.</p>",
                    solution_hi: "<p>38.(c)<strong> 22 गज I क्रिकेट विकेट के आयाम-</strong> ऊंचाई 28&rdquo; (71.12 cm) और चौड़ाई 9&rdquo; (22.86 cm), <strong>बल्ला -</strong> लंबाई 38 इंच (96.5 सेमी) से अधिक नहीं होनी चाहिए और चौड़ाई 4.25 इंच (10.8 सेमी) से अधिक नहीं होनी चाहिए और केवल लकड़ी से बना होना चाहिए; <strong>गेंद</strong> - वजन 5.5 औंस और 5.75 औंस के बीच, और परिधि 22.4 सेमी और 22.9 सेमी के बीच होनी चाहिए । <strong>सर्वोच्च शासकीय निकाय</strong> - अंतर्राष्ट्रीय क्रिकेट परिषद (ICC), मुख्यालय - दुबई (UAE); स्थापना - 15 जून 1909। भारतीय क्रिकेट कंट्रोल बोर्ड (BCCI), मुख्यालय - मुंबई; स्थापना - दिसम्बर 1928</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. There are seven members in a family. D is the mother of F. D is G&rsquo;s father&rsquo;s mother. G is the brother of A and C. F is the only son of E. How is E related to C?</p>",
                    question_hi: "<p>39. एक परिवार में सात सदस्य हैं। D, Fकी मां है। D, G के पिता की मां है। G, A और C का भाई है। F, E का इकलौता पुत्र है। E का C से क्या संबंध है?</p>",
                    options_en: ["<p>Brother</p>", "<p>Mother&rsquo;s brother</p>", 
                                "<p>Father</p>", "<p>Father&rsquo;s father</p>"],
                    options_hi: ["<p>भाई</p>", "<p>मामा/मां का भाई</p>",
                                "<p>पिता</p>", "<p>दादा/पिता के पिता</p>"],
                    solution_en: "<p>39.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338002.png\" alt=\"rId18\" width=\"159\" height=\"109\"><br>According to the above diagram, D is C&rsquo;s father&rsquo;s father.</p>",
                    solution_hi: "<p>39.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338002.png\" alt=\"rId18\" width=\"159\" height=\"109\"><br>उपरोक्त आरेख के अनुसार, D, C के पिता का पिता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. Anup can row 33 km downstream and 35km upstream in 8 hours. He can also row 44 km downstream and 28 km upstream in the same time. How much time (in hours) will be take to row 55km downstream and 14 km upstream?</p>",
                    question_hi: "<p>40. अनूप धारा के अनुकूल 33 kmऔर धारा के प्रतिकूल 35 km 8 घंटे में नाव चला सकता है। वह समान समय में धारा के अनुकूल 44 km और धारा के प्रतिकूल 28 km की दूरी तय कर सकता है। धारा के अनुकूल 55 km और धारा के प्रतिकूल 14 km की दूरी तय करने में कितना समय (घंटों में) लगेगा?</p>",
                    options_en: ["<p>9</p>", "<p>6</p>", 
                                "<p>8</p>", "<p>7</p>"],
                    options_hi: ["<p>9</p>", "<p>6</p>",
                                "<p>8</p>", "<p>7</p>"],
                    solution_en: "<p>40.(d)<br>Let speed of boat in downstream = x&nbsp;km/h and speed of boat in upstream = y km/h<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mi>y</mi></mfrac></math>&nbsp;= 8 &hellip;&hellip;(1)<br><math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mi>y</mi></mfrac></math> = 8 &hellip;&hellip;(2)<br>On solving equation (1) and (2), we get<br><math display=\"inline\"><mi>x</mi></math> = 11 km/h and y = 7 km/h<br>Now, required time = <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mi>y</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>7</mn></mfrac></math></p>\n<p>= 5 + 2</p>\n<p>= 7 hrs</p>",
                    solution_hi: "<p>40.(d)<br>माना धारा के अनुकूल नाव की गति = x km/h और <br>धारा के प्रतिकूल नाव की गति = y km/h<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mi>y</mi></mfrac></math>&nbsp;= 8 &hellip;&hellip;(1)<br><math display=\"inline\"><mfrac><mrow><mn>44</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mi>y</mi></mfrac></math> = 8 &hellip;&hellip;(2)<br>समीकरण (1) और (2) को हल करने पर, हम प्राप्त करते हैं<br>x = 11 km/h और y = 7 km/h<br>अब, अभीष्ट समय&nbsp;= <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mi>y</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>7</mn></mfrac></math></p>\n<p>= 5 + 2</p>\n<p>= 7 घंटे</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. Which of the following pairs is INCORRECTLY matched?</p>",
                    question_hi: "<p>41. निम्नलिखित में से कौन सा जोड़ा गलत सुमेलित है?</p>",
                    options_en: ["<p>Cushing\'s syndrome - Cortisol</p>", "<p>Diabetes - Insulin</p>", 
                                "<p>Goitre -Thyroxin</p>", "<p>Acromegaly - Adrenaline</p>"],
                    options_hi: ["<p>कुशिंग सिंड्रोम - कोर्टिसोल</p>", "<p>मधुमेह - इंसुलिन</p>",
                                "<p>घेंघा-थायरोक्सिन</p>", "<p>एक्रोमेगाली - एड्रेनालाईन</p>"],
                    solution_en: "<p>41.(d) <strong>Acromegaly</strong> is a disorder that occurs when the body makes too much growth hormone (GH); Produced mainly in the pituitary gland (a pea-sized gland just below the brain). In adults, too much of this hormone causes bones, cartilage, body organs, and other tissues to increase in size. Adrenal medulla (the inner part of an adrenal gland) secretes adrenaline hormones responsible for initiating the flight or fight response.</p>",
                    solution_hi: "<p>41.(d) <strong>एक्रोमेगाली</strong> एक विकार तब होता है जब होता है जब शरीर बहुत अधिक मात्रा में ग्रोथ हार्मोन (GH) का उत्पादन होता है; मुख्य रूप से पिट्यूटरी ग्रंथि (मस्तिष्क के ठीक नीचे एक मटर के आकार की ग्रंथि) में उत्पादित होता है। वयस्कों में, इस हार्मोन का अधिक मात्रा में होना हड्डियों, उपास्थि, अंगों और अन्य ऊतकों के आकार में वृद्धि का कारण होता है। एड्रेनल मेडूला (एड्रेनल ग्रंथि का आंतरिक भाग) एड्रेनालाईन हार्मोन को स्रावित करता है जो फाइट या फ्लाइट प्रतिक्रिया शुरू करने के लिए उत्तरदायी होता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. Study the given diagram carefully and answer the question. The numbers in different sections indicate the number of persons. The circle represents \'actors\', the square represents \'directors&rsquo;, and the triangle represents \'producers\'. How many actors are there who are directors and also producers?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338143.png\" alt=\"rId19\" width=\"116\" height=\"106\"></p>",
                    question_hi: "<p>42. दिए गए आरेख का ध्यानपूर्वक अध्ययन कीजिये और प्रश्न का उत्तर दीजिये । विभिन्न वर्गों में संख्या व्यक्तियों की संख्या दर्शाती है। वृत्त \'अभिनेताओं\' का प्रतिनिधित्व करता है, वर्ग \'निर्देशकों\' का प्रतिनिधित्व करता है, और त्रिकोण \'निर्माताओं\' का प्रतिनिधित्व करता है। ऐसे कितने अभिनेता हैं जो निर्देशक और निर्माता भी हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338143.png\" alt=\"rId19\" width=\"116\" height=\"106\"></p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>3</p>", "<p>1</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>3</p>", "<p>1</p>"],
                    solution_en: "<p>42.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338143.png\" alt=\"rId19\" width=\"116\" height=\"106\"><br>Number of actors who are directors and also producers = 4</p>",
                    solution_hi: "<p>42.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338143.png\" alt=\"rId19\" width=\"116\" height=\"106\"><br>ऐसे अभिनेता हैं जो निर्देशक और निर्माता हैं = 4</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. Bharatanatyam expresses South Indian religious themes and spiritual ideas of ______.</p>",
                    question_hi: "<p>43. भरतनाट्यम दक्षिण भारत के धार्मिक विषयों और ______ के आध्यात्मिक विचारों को व्यक्त करता है।</p>",
                    options_en: ["<p>Sufism</p>", "<p>Shaivism</p>", 
                                "<p>Buddhism</p>", "<p>Jainism</p>"],
                    options_hi: ["<p>सूफीवाद</p>", "<p>शैव धर्म</p>",
                                "<p>बुद्ध धर्म</p>", "<p>जैन धर्म</p>"],
                    solution_en: "<p>43. (b) <strong>Shaivism.</strong><br>Bharatanatyam expresses South Indian religious themes and spiritual ideas primarily related to Shaivism, which is a Hindu tradition that worships Shiva. This tradition emphasizes ascetic life and the practice of yoga. Followers of Shaivism are known as Shaivas or Shaivites.<br>Bharatanatyam, which originates from Tamil Nadu, traces its roots back to the Natyashastra, an ancient treatise on theatre written by the mythic priest Bharata.<br><strong>Here are some key features of Bharatanatyam:</strong><br><strong>1. Expressive Storytelling (Abhinaya)</strong><br>Bharatanatyam is known for its expressive storytelling through facial expressions (abhinaya). Dancers convey emotions and narratives using their facial expressions, hand gestures (mudras), and body movements.<br><strong>2. Nritta (Pure Dance) and Nritya (Expressive Dance)</strong><br>The dance consists of two primary components:<br>Nritta: This refers to the pure dance movements that focus on rhythm and intricate footwork without any expressiveness.<br>Nritya: This involves expressive movements and gestures, portraying specific themes, emotions, and stories.<br><strong>3. Mudras (Hand Gestures)</strong><br>Bharatanatyam employs a variety of mudras to convey specific meanings and emotions. Each hand gesture can represent different characters, objects, or feelings.<br><strong>4. Footwork (Adavus)</strong><br>The dance form features complex footwork known as adavus, which are basic units of movement. These adavus are rhythmic and often synchronized with accompanying music.<br><strong>5. Costume and Makeup</strong><br>Dancers typically wear traditional costumes that include a sari or a specially designed dance costume. The attire is often adorned with intricate jewelry, and the makeup is elaborate to highlight facial expressions and features.<br><strong>6. Music and Rhythm</strong><br>Bharatanatyam is performed to Carnatic music, which features intricate melodies and rhythms. The music often includes traditional instruments like the mridangam (a percussion instrument) and the flute.<br><strong>7. Stances and Postures</strong><br>The dance features specific stances, including the Aramandi (half-sitting position) and Samapada (standing position), which are fundamental to Bharatanatyam.<br><strong>8. Spiritual and Religious Themes</strong><br>Many Bharatanatyam performances depict themes from Hindu mythology and spirituality, often dedicated to deities like Shiva, Durga, and Krishna.<br><strong>9. Use of Space</strong><br>Bharatanatyam emphasizes the use of space, with dancers often moving in circular patterns, creating a visual representation of the stories they tell.<br><strong>10. Footwear</strong><br>Traditional Bharatanatyam dancers often wear ankle bells (ghungroo) that produce rhythmic sounds during movements, enhancing the musicality of the performance.</p>",
                    solution_hi: "<p>43. (b) <strong>शैव धर्म।</strong><br>भरतनाट्यम दक्षिण भारत के धार्मिक विषयों और आध्यात्मिक विचारों को मुख्य रूप से शैव धर्म से व्यक्त करता है, जो शिव की पूजा करने वाली एक हिंदू परंपरा है। यह परंपरा तपस्वी जीवन और योग के अभ्यास पर जोर देती है। शैव धर्म के अनुयायियों को शैव कहा जाता है।<br>भरतनाट्यम, जो तमिलनाडु से है, इसकी उत्पत्ति नाट्यशास्त्र से हुई है, जो पौराणिक पुजारी भरत द्वारा लिखित रंगमंच पर एक प्राचीन ग्रंथ है।<br><strong>भरतनाट्यम की कुछ प्रमुख विशेषताएँ:</strong><br><strong>1. व्यक्तिगत कहानी कहने की कला (अभिनय)</strong><br>भरतनाट्यम अपनी व्यक्तित्व कहानी कहने की कला के लिए जाना जाता है। नर्तक अपने चेहरे के भाव, हाथों के इशारे (मुद्राएँ) और शरीर की गति का उपयोग करके भावनाओं और कथाओं को व्यक्त करते हैं।<br><strong>2. णरित्ता (Nritta) (शुद्ध नृत्य) और नृत्यया (Nritya) (व्यक्तिगत नृत्य)</strong><br>इस नृत्य में दो मुख्य तत्व शामिल हैं:<br>णरित्ता (Nritta): यह शुद्ध नृत्य गति (movements) को संदर्भित करता है, जो लय और जटिल पैरों की चाल पर ध्यान केंद्रित करता है, बिना किसी अभिव्यक्ति के।<br>नृत्यया (Nritya): इसमें अभिव्यक्तिपूर्ण गति और इशारे शामिल होते हैं, जो विशिष्ट विषयों, भावनाओं और कहानियों को प्रस्तुत करते हैं।<br><strong>3. मुद्राएँ (हाथ के इशारे)</strong><br>भरतनाट्यम विभिन्न मुद्राओं का उपयोग करता है जो विशिष्ट अर्थ और भावनाओं को व्यक्त करने के लिए होती हैं। प्रत्येक हाथ का इशारा विभिन्न पात्रों, वस्तुओं या भावनाओं का प्रतिनिधित्व कर सकता है।<br><strong>4. पैर का काम (अदवु)</strong><br>इस नृत्य रूप में जटिल पैर के काम को अदवु के रूप में जाना जाता है, जो आंदोलन के मूलभूत इकाइयाँ हैं। ये अदवु लयबद्ध होते हैं और अक्सर संगीत के साथ समन्वयित होते हैं।<br><strong>5. पहनावा और मेकअप</strong><br>नर्तक आमतौर पर पारंपरिक वस्त्र पहनते हैं, जिसमें साड़ी या विशेष रूप से डिज़ाइन की गई नृत्य वेशभूषा शामिल होती है। वेशभूषा अक्सर जटिल आभूषणों से सजी होती है, और मेकअप चेहरों के भाव और विशेषताओं को उजागर करने के लिए भव्य होता है।<br><strong>6. संगीत और लय</strong><br>भरतनाट्यम को कर्नाटिक संगीत के साथ प्रस्तुत किया जाता है, जिसमें जटिल राग और लय होती हैं। संगीत में अक्सर पारंपरिक उपकरण जैसे मृदंगम (एक percussion उपकरण) और बांसुरी शामिल होती है।<br><strong>7. अवस्थाएँ और मुद्रा</strong><br>इस नृत्य में विशेष अवस्थाएँ होती हैं, जिसमें अरामंदी (आधा बैठा हुआ पद) और समपादा (खड़ा हुआ पद) शामिल होते हैं, जो भरतनाट्यम के लिए मौलिक होते हैं।<br><strong>8. आध्यात्मिक और धार्मिक विषय</strong><br>कई भरतनाट्यम प्रदर्शन हिंदू पौराणिक कथाओं और आध्यात्मिकता के विषयों को चित्रित करते हैं, जो अक्सर शिव, दुर्गा, और कृष्ण जैसे देवताओं को समर्पित होते हैं।<br><strong>9. स्थान का उपयोग</strong><br>भरतनाट्यम स्थान के उपयोग पर जोर देता है, जिसमें नर्तक अक्सर गोलाकार पैटर्न में चलते हैं, जो वे जो कहानियाँ सुनाते हैं उनका दृश्य प्रतिनिधित्व बनाते हैं।<br><strong>10. पैरों के लिए वस्त्र (Footwear)</strong><br>पारंपरिक भरतनाट्यम नर्तक अक्सर एंकल बेल्स (घुंघरू) पहनते हैं जो आंदोलनों के दौरान लयबद्ध ध्वनियाँ उत्पन्न करते हैं, प्रदर्शन की संगीतता को बढ़ाते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. In <math display=\"inline\"><mi>&#916;</mi></math>ABC, if AB = AC and &ang;BAC = 40&deg;, then the measure of &ang;B is:</p>",
                    question_hi: "<p>44.त्रिभुज ABC में, यदि AB = AC तथा &ang;BAC = 40&deg; है, तो कोण B का मान क्या होगा ?</p>",
                    options_en: ["<p>40&deg;</p>", "<p>60&deg;</p>", 
                                "<p>50&deg;</p>", "<p>70&deg;</p>"],
                    options_hi: ["<p>40&deg;</p>", "<p>60&deg;</p>",
                                "<p>50&deg;</p>", "<p>70&deg;</p>"],
                    solution_en: "<p>44. (d) In the given diagram, AB = AC and &ang;BAC = 40&deg;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338250.png\" alt=\"rId20\" width=\"105\" height=\"119\"><br>For an isosceles triangle, angles opposite to equal sides are equal in magnitude.<br>So, <br>&ang;B = &ang;C<br>Sum of angles of triangle is 180&deg;<br>&ang;A + &ang;B + &ang;C= 180&deg;<br>40&deg; + 2&ang;B = 180&deg;<br>&ang;B = 70&deg;</p>",
                    solution_hi: "<p>44. (d) दिए गए चित्र में, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338250.png\" alt=\"rId20\" width=\"96\" height=\"109\"><br>AB = AC है और &ang;BAC = 40&deg; <br>चूंकि यह एक समद्विबाहु त्रिभुज है, इसलिए समान भुजाओं के विपरीत कोण बराबर होते हैं।<br>इसलिए,<br>&ang;B = &ang;C<br>त्रिभुज के कोणों का योग 180&deg; होता है, इसलिए:<br>&ang;A + &ang;B + &ang;C= 180&deg;<br>40&deg; + 2&ang;B= 180&deg;<br>अतः , &ang;B = 70&deg; है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. When is the &lsquo;International Day against Drug Abuse and Illicit Trafficking&rsquo; observed?</p>",
                    question_hi: "<p>45. \'नशीली दवाओं के दुरुपयोग और अवैध तस्करी के खिलाफ अंतर्राष्ट्रीय दिवस\' कब मनाया जाता है?</p>",
                    options_en: ["<p>June 21</p>", "<p>June 26</p>", 
                                "<p>July 1</p>", "<p>July 5</p>"],
                    options_hi: ["<p>21 जून</p>", "<p>26 जून</p>",
                                "<p>1 जुलाई</p>", "<p>5 जुलाई</p>"],
                    solution_en: "<p>45.(b)&nbsp;The United Nations General Assembly in 1987, decided to observe June 26 as International Day against Drug Abuse and Illicit Trafficking.&nbsp;This year&rsquo;s theme is &lsquo;People first: Stop stigma and discrimination; strengthen prevention&rsquo;. Every year, United Nations Office on Drugs and Crime (UNODC) issues the World Drug Report.</p>",
                    solution_hi: "<p>45.(b)<br>1987 में संयुक्त राष्ट्र महासभा ने 26 जून को नशीली दवाओं के दुरुपयोग और अवैध तस्करी के खिलाफ अंतर्राष्ट्रीय दिवस के रूप में मनाने का निर्णय लिया।&nbsp;इस वर्ष की थीम है \'लोग पहले: कलंक और भेदभाव रोकें; रोकथाम को मजबूत करें\'। हर साल, ड्रग्स और अपराध पर संयुक्त राष्ट्र कार्यालय (यूएनओडीसी) विश्व ड्रग रिपोर्ट जारी करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. In a circle with radius 5cm, a chord is at a distance of 3cm from the centre. The length of the chord is:</p>",
                    question_hi: "<p>46. 5 सेमी त्रिज्या वाले एक वृत्त में, केंद्र से 3 सेमी की दूरी पर एक जीवा है | इस जीवा की लंबाई कितनी है ?</p>",
                    options_en: ["<p>7 cm</p>", "<p>4 cm</p>", 
                                "<p>8 cm</p>", "<p>3 cm</p>"],
                    options_hi: ["<p>7 cm</p>", "<p>4 cm</p>",
                                "<p>8 cm</p>", "<p>3 cm</p>"],
                    solution_en: "<p>46. (c) O is the centre of the circle and AB is the chord.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338401.png\" alt=\"rId21\" width=\"122\" height=\"122\"><br><math display=\"inline\"><mi>&#10178;</mi></math>ar from centre to chord bisect the chord.<br>AE =&nbsp; EB<br>Using pythagoras theorem,<br>AE = 4 cm<br>And AB = 8 cm</p>",
                    solution_hi: "<p>46. (c) O वृत का केंद्र है और AB उसकी जीवा है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338401.png\" alt=\"rId21\" width=\"118\" height=\"118\"><br>वृत्त के केंद्र से जीवा पर खींचा गया लम्ब (⟂) जीवा को समद्विभाजित करता है।इसलिए,<br>AE = EB<br>पाइथागोरस प्रमेय का उपयोग करते हुए, हमें मिलता है:<br>AE = 4 cm<br>और AB = 8 cm<br>अर्थात, जीवा AB की कुल लंबाई 8 सेमी है, और AE तथा EB दोनों 4 सेमी हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. Who is known as the \'Father of Modern Dance in India\'?</p>",
                    question_hi: "<p>47. भारत में आधुनिक नृत्य का जनक किसे जाना जाता है?</p>",
                    options_en: ["<p>Uday Shankar</p>", "<p>Birju Maharaj</p>", 
                                "<p>Gopi Krishna</p>", "<p>Guru Bipin Singh</p>"],
                    options_hi: ["<p>उदय शंकर</p>", "<p>बिरजू महाराज</p>",
                                "<p>गोपी कृष्ण</p>", "<p>गुरु बिपिन सिंह</p>"],
                    solution_en: "<p>47.(a) <strong>Uday Shankar</strong> - Major dancer and choreographer of India whose adaptation of Western theatrical techniques to traditional Hindu dance popularized the ancient art form in India, Europe, and the United States. <strong>Awards</strong> : Sangeet Natak Akademi (1962), Padma Vibhushan in (1971).<strong> Birju Maharaj </strong>(Father of Kathak, Lucknow \"Kalka-Bindadin\" Gharana), Gopi Krishna (Kathak dancer), Guru Bipin Singh (Father of Manipuri Dance).</p>",
                    solution_hi: "<p>47.(a)<strong> उदय शंकर</strong> - भारत के प्रमुख नर्तक और कोरियोग्राफर, जिन्होंने पश्चिमी नाट्य तकनीकों को पारंपरिक हिंदू नृत्य में अपनाकर प्राचीन कला को भारत, यूरोप और संयुक्त राज्य अमेरिका में लोकप्रिय बनाया। <strong>पुरस्कार:</strong> संगीत नाटक अकादमी (1962), पद्म विभूषण (1971)। <strong>बिरजू महाराज</strong> (कथक के जनक, लखनऊ \"कालका-बिंदादीन\" घराना), गोपी कृष्ण (कथक नर्तक), गुरु बिपिन सिंह (मणिपुरी नृत्य के जनक)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. Six friends Richa, Shobhna, Urmila, Swarna, Purnima, and Kavya, are sitting on a bench in the park. They all are facing south. Only four persons are sitting between Purnima and Richa. Swarna is sitting third to the left of Purnima. Richa and Swarna are immediate neighbors of Kavya. Purnima and Shobhna are immediate neighbors of Urmila. Who sits fourth to the left of Urmila?</p>",
                    question_hi: "<p>48. छह सहेलियां - ऋचा, शोभना, उर्मिला, स्वर्णा, पूर्णिमा और काव्या, किसी पार्क में - एक बेंच पर बैठी हैं। उन सभी के मुख दक्षिण की ओर हैं। पूर्णिमा और ऋचा के बीच केवल चार सहेलियां बैठी हैं। स्वर्णा, पूर्णिमा के बाईं ओर तीसरे स्थान पर बैठी है। ऋचा और स्वर्णा, दोनों काव्या के ठीक बगल में बैठी हैं। पूर्णिमा और शोभना दोनों उर्मिला के ठीक बगल में बैठी हैं। निम्न में से कौन उर्मिला के बाईं ओर चौथे स्थान पर बैठी है?</p>",
                    options_en: ["<p>Kavya</p>", "<p>Richa</p>", 
                                "<p>Shobhna</p>", "<p>Swarna</p>"],
                    options_hi: ["<p>काव्या</p>", "<p>ऋचा</p>",
                                "<p>शोभना</p>", "<p>स्वर्ण</p>"],
                    solution_en: "<p>48.(b)<br>Since all friends are facing South.<br>The arrangement will be as follows : <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338607.png\" alt=\"rId22\"><br>Therefore, it can be seen clearly that, Richa sits fourth to the left of Urmila</p>",
                    solution_hi: "<p>48.(b)<br>चूंकि सभी मित्रों का मुख दक्षिण की ओर है।<br>व्यवस्था इस प्रकार होगी :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338725.png\" alt=\"rId23\"><br>इसलिए, यह स्पष्ट रूप से देखा जा सकता है कि ऋचा, उर्मिला के बायें से चौथे स्थान पर बैठी हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. A can do a piece of work in 15 days, while B can do the same work in 21 days. If they work together, then in how many days will the same work be completed?</p>",
                    question_hi: "<p>49. A एक काम को 15 दिनों में कर सकता है, जबकि B उसी काम को 21 दिनों में कर सकता है। यदि वे एक साथ कार्य करते हैं, तो समान कार्य कितने दिनों में पूरा होगा?</p>",
                    options_en: ["<p>7<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p>6<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>7<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p>6<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>49.(d)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; A&nbsp; &nbsp; &nbsp; &nbsp; B <br>Efficiency&nbsp; &nbsp; :&nbsp; (7)&nbsp; &nbsp; &nbsp; (5) <br>Time taken&nbsp; :&nbsp; 15&nbsp; &nbsp; &nbsp; &nbsp;21 <br>Total work = LCM of time taken = LCM (15, 21) = 105 units<br>Time taken to complete the work <br>together = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mrow><mn>7</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>12</mn></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> days</p>",
                    solution_hi: "<p>49.(d)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A&nbsp; &nbsp; &nbsp; &nbsp; B <br>दक्षता&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; (7)&nbsp; &nbsp; &nbsp; (5) <br>लिया गया समय&nbsp; :&nbsp; 15&nbsp; &nbsp; &nbsp; 21 <br>कुल कार्य = लिए गए समय का LCM = LCM (15, 21) = 105 इकाई<br>एक साथ कार्य को पूरा करने में लिया गया समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mrow><mn>7</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>12</mn></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 8<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>दिन</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. The Sirpur National Dance and Music Festival is organised every year in which Indian state?</p>",
                    question_hi: "<p>50. सिरपुर राष्ट्रीय नृत्य और संगीत महोत्सव हर वर्ष भारत के किस राज्य में आयोजित किया जाता है?</p>",
                    options_en: ["<p>Jharkhand</p>", "<p>Chhattisgarh</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>झारखंड</p>", "<p>छत्तीसगढ़</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>50.(b) <strong>Chhattisgarh</strong> (At Lakshman temple). National Tribal Dance Festival is one of Chhattisgarh\'s grand festivals which celebrates diverse tribal communities and their culture not just in India but from across the globe. <strong>Dance and Festival of state:</strong> <strong>Chhattisgarh</strong> - Raut Nacha, Saila Tribal Dance, Panthi. <strong>Jharkhand</strong> - Jhumar, Domkach, Lahasua, Paika, Chhau, Firkal, Mundari and Santali.<strong> Uttar Pradesh </strong>- Mayur Nritya, Charakula, Ramlila, Khyal, Nautanki, Dadra, and Kajri Dance. <strong>Madhya Pradesh </strong>- Gaur Maria, Grida, Maanch, Matki, Phulpati.</p>",
                    solution_hi: "<p>50.(b) <strong>छत्तीसगढ़</strong> (लक्ष्मण मंदिर में)। राष्ट्रीय आदिवासी नृत्य महोत्सव छत्तीसगढ़ के भव्य त्योहारों में से एक है जो न केवल भारत में बल्कि दुनिया भर के विविध आदिवासी समुदायों और उनकी संस्कृति का जश्न मनाता है। <strong>राज्य के नृत्य और त्यौहार: छत्तीसगढ़ </strong>- राउत नाचा, सैला आदिवासी नृत्य, पंथी।<strong> झारखंड</strong> - झूमर, डोमकच, लहसुआ, पाइका, छऊ, फिरकल, मुंडारी और संथाली। <strong>उत्तर प्रदेश </strong>- मयूर नृत्य, चारुकला, रामलीला, ख्याल, नौटंकी, दादरा और कजरी नृत्य।<strong> मध्य प्रदेश -</strong> गौर मारिया, ग्रीडा, मांच, मटकी, फूलपति।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. Four different positions of some dice are shown. Select the number that will be on the face opposite of the one having 5.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338875.png\" alt=\"rId24\" width=\"352\" height=\"79\"></p>",
                    question_hi: "<p>51. कुछ पासों की चार अलग-अलग स्थितियाँ दिखाई गई हैं। उस संख्या का चयन करें जो 5 के विपरीत फलक पर होगी<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386338875.png\" alt=\"rId24\" width=\"361\" height=\"81\"></p>",
                    options_en: ["<p>2</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>3</p>"],
                    options_hi: ["<p>2</p>", "<p>6</p>",
                                "<p>4</p>", "<p>3</p>"],
                    solution_en: "<p>51.(a)<br>Opposite face of 5 are not possible - 1 , 3 , 4 , 6 ( adjacent face )<br>So, only possible face 2</p>",
                    solution_hi: "<p>51.(a)<br>5 का विपरीत फलक संभव नहीं है - 1, 3, 4, 6 (आसन्न फलक)<br>इसीलिए केवल संभव फलक 2 है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. What is the pH of salt containing a mixture of a strong base and a weak acid?</p>",
                    question_hi: "<p>52. प्रबल क्षार और दुर्बल अम्ल के मिश्रण वाले लवण का pH मान कितना होता है?</p>",
                    options_en: ["<p>pH less than 7</p>", "<p>pH equal to 0</p>", 
                                "<p>pH equal to 7</p>", "<p>pH more than 7</p>"],
                    options_hi: ["<p>इसका pH मान 7 से कम होता है ।</p>", "<p>इसका pH मान 0 के बराबर होता है।</p>",
                                "<p>इसका pH मान 7 के बराबर होता है।</p>", "<p>इसका pH मान 7 से अधिक होता है।</p>"],
                    solution_en: "<p>52.(d) <strong>pH more than 7</strong>. Acid-Base Properties of Salts - Salts, when placed in water, will often react with the water to produce H<sub>3</sub>O<sup>+</sup> or OH<sup>&ndash;</sup> i.e. <strong>hydrolysis reaction.</strong> Salts that are from strong bases and strong acids do not hydrolyze. Salts that are from strong bases and weak acids do hydrolyze, which gives it a pH greater than 7. Salts of weak bases and strong acids do hydrolyze, which gives it a pH less than 7. <strong>pH</strong> (Power of Hydrogen) is a scale (0 - 14) used to specify the acidity or basicity of an aqueous solution. Lower pH values represent more acidic in nature, While higher pH values represent more basic or alkaline.</p>",
                    solution_hi: "<p>52.(d) <strong>इसका pH मान 7 से अधिक होता है। लवणों के अम्ल-क्षार गुण</strong> - लवण, जब पानी में रखे जाते हैं, तो अक्सर पानी के साथ अभिक्रिया करके H<sub>3</sub>O<sup>+</sup> या OH<sup>&ndash;</sup> अर्थात जल अपघटन अभिक्रिया उत्पन्न करते हैं। प्रबल क्षारों और प्रबल अम्लों से बने लवण जल-अपघटित नहीं होते हैं। प्रबल क्षारों और दुर्बल अम्ल से बने लवण जल-अपघटित होकर 7 से अधिक pH देते है। दुर्बल क्षार और प्रबल अम्ल के लवण जल-अपघटित होकर 7 से कम pH देते है। pH (पोटेंशियल आफ हाइड्रोजन) एक पैमाना ( 0 - 14) है , जो एक जलीय विलयन की अम्लता या क्षारकता को निर्दिष्ट करने के लिए उपयोग किया जाता है। कम pH मान प्रकृति में अधिक अम्लीयता का प्रतिनिधित्व करती हैं, जबकि उच्च pH मान अधिक क्षारीयता का प्रतिनिधित्व करती हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. Three years ago, the ratio of the age of the father to that of son was 8 : 3. After 4 years, their ages will be in the ratio of 11 : 5. What is the present age of the father ?</p>",
                    question_hi: "<p>53. तीन वर्ष पहले, पिता की आयु का पुत्र की आयु से अनुपात 8:3 था। 4 वर्ष बाद उनकी आयु 11 : 5 के अनुपात में होगी। पिता की वर्तमान आयु क्या है?</p>",
                    options_en: ["<p>52</p>", "<p>51</p>", 
                                "<p>48</p>", "<p>55</p>"],
                    options_hi: ["<p>52</p>", "<p>51</p>",
                                "<p>48</p>", "<p>55</p>"],
                    solution_en: "<p>53.(b)<br>Let present age of father = x&nbsp;and present age of son = y<br>According to the question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>-</mo><mn>3</mn></mrow><mrow><mi>y</mi><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math><br>3x&nbsp;&ndash; 9 = 8y &ndash; 24 <br>3x&nbsp;&ndash; 8y = &ndash; 15 &hellip;.(1)<br>Also, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow><mrow><mi>y</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math><br>5<math display=\"inline\"><mi>x</mi></math> + 20 = 11y + 44<br>5<math display=\"inline\"><mi>x</mi></math> &ndash; 11y = 24 &hellip;.(2)<br>On solving equation (1) and (2), we get<br><math display=\"inline\"><mi>x</mi></math> = 51 <br>So, present age of father = 51 years</p>",
                    solution_hi: "<p>53.(b)<br>माना पिता की वर्तमान आयु = x और पुत्र की वर्तमान आयु = y<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>-</mo><mn>3</mn></mrow><mrow><mi>y</mi><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math><br>3x&nbsp;&ndash; 9 = 8y &ndash; 24 <br>3x &ndash; 8y = &ndash; 15 &hellip;.(1)<br>साथ ही, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow><mrow><mi>y</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math><br>5<math display=\"inline\"><mi>x</mi></math> + 20 = 11y + 44<br>5<math display=\"inline\"><mi>x</mi></math> &ndash; 11y = 24 &hellip;.(2)<br>समीकरण (1) और (2) को हल करने पर, हम प्राप्त करते हैं<br>x = 51<br>अत: पिता की वर्तमान आयु = 51 वर्ष</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. Arattu&rsquo; festival, which was seen in the news, is a famous festival of which state?</p>",
                    question_hi: "<p>54. अरट्टू\' त्योहार, जो खबरों में देखा गया था, किस राज्य का प्रसिद्ध त्योहार है?</p>",
                    options_en: ["<p>Kerala</p>", "<p>Karnataka</p>", 
                                "<p>Odisha</p>", "<p>Bihar</p>"],
                    options_hi: ["<p>केरल</p>", "<p>कर्नाटक</p>",
                                "<p>ओडिशा</p>", "<p>बिहार</p>"],
                    solution_en: "<p>54.(a)<br>The &lsquo;Arattu&rsquo; festival has a rich tradition of being celebrated at the Sree Padmanabha Swamy Temple in Thiruvananthapuram.&nbsp;The uniqueness of the &lsquo;Arattu&rsquo; festival is that the head of the royal family of the erstwhile Travancore kingdom still escorts the idols of deities during the procession in traditional attire. The Aarattu Festival is celebrated twice each year.</p>",
                    solution_hi: "<p>54.(a)<br>तिरुवनंतपुरम के श्री पद्मनाभ स्वामी मंदिर में \'अराट्टू\' उत्सव मनाए जाने की एक समृद्ध परंपरा है।&nbsp;\'अराट्टू\' उत्सव की विशिष्टता यह है कि पूर्ववर्ती त्रावणकोर साम्राज्य के शाही परिवार के मुखिया अभी भी पारंपरिक पोशाक में जुलूस के दौरान देवताओं की मूर्तियों के साथ चलते हैं। आरट्टू महोत्सव हर साल दो बार मनाया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339072.png\" alt=\"rId25\" width=\"117\" height=\"111\"></p>",
                    question_hi: "<p>55. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा |<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339072.png\" alt=\"rId25\" width=\"117\" height=\"111\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339190.png\" alt=\"rId26\" width=\"100\" height=\"23\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339303.png\" alt=\"rId27\" width=\"101\" height=\"23\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339525.png\" alt=\"rId28\" width=\"99\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339662.png\" alt=\"rId29\" width=\"100\" height=\"24\"><br><br></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339190.png\" alt=\"rId26\" width=\"101\" height=\"23\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339303.png\" alt=\"rId27\" width=\"101\" height=\"23\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339525.png\" alt=\"rId28\" width=\"104\" height=\"21\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339662.png\" alt=\"rId29\" width=\"100\" height=\"24\"></p>"],
                    solution_en: "<p>55.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339303.png\" alt=\"rId27\" width=\"109\" height=\"25\"></p>",
                    solution_hi: "<p>55.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339303.png\" alt=\"rId27\" width=\"109\" height=\"25\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. A can finish work in 20 days and B can finish the same work in 25 days. They began together, but B left the work after 5 days. How many more days will A take to finish the remaining work?</p>",
                    question_hi: "<p>56.A 20 दिनों में कार्य समाप्त कर सकता है तथा B इसी कार्य को 25 दिनों में पूरा कर सकता है | उन्होंने एक साथ कार्य करना शुरू किया, लेकिन B ने 5 दिनों के बाद कार्य छोड़ दिया | शेष कार्य पूरा करने में A को कितने अतिरिक्त दिन लगेंगे ?</p>",
                    options_en: ["<p>8</p>", "<p>21</p>", 
                                "<p>16</p>", "<p>11</p>"],
                    options_hi: ["<p>8</p>", "<p>21</p>",
                                "<p>16</p>", "<p>11</p>"],
                    solution_en: "<p>56. (d) A can finish the work in 20 days<br>B can finish the work in 25 days<br>Total work = LCM(20,25) = 100 units<br>Efficiency of A = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 5unit/day<br>Efficiency of B = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = 4unit/day<br>Work done by A and B together in 5 days = (5 + 4) &times; 5 = 45 units<br>Remaining work = 100 - 45 = 55 units<br>A complete remaining work in <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>= 11 days.</p>",
                    solution_hi: "<p>56. (d) <br>A द्वारा कार्य को पूरा करने मे लगा समय = 20 दिन<br>B द्वारा कार्य को पूरा करने मे लगा समय = 25 दिन<br>कुल कार्य = LCM(20,25) = 100 इकाई <br>A की कार्य क्षमता = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>= 5 इकाई /दिन</p>\n<p>B की कार्य क्षमता = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = 4 इकाई /दिन<br>A और B मिलकर 5 दिनों में किया गया कार्य: = (5 + 4) &times; 5 = 45 इकाई&nbsp;<br>बचा हुआ कार्य = 100 - 45 = 55 इकाई <br>A द्वारा बचे हुए कार्य को पूरा करने मे लगा समय = <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 11 दिन</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57. Which of the Five-Year Plans was prepared and launched by D.P. Dhar?</p>",
                    question_hi: "<p>57. डी.पी. धर द्वारा कौन सी पंचवर्षीय योजना तैयार और लॉन्च की गई थी?</p>",
                    options_en: ["<p>Sixth Five-Year Plan</p>", "<p>Seventh Five-Year Plan</p>", 
                                "<p>Fourth Five-Year Plan</p>", "<p>Fifth Five-Year Plan </p>"],
                    options_hi: ["<p>छठी पंचवर्षीय योजना</p>", "<p>सातवीं पंचवर्षीय योजना</p>",
                                "<p>चौथी पंचवर्षीय योजना</p>", "<p>पांचवीं पंचवर्षीय योजना</p>"],
                    solution_en: "<p>57.(d) <strong>Fifth Five-Year Plan</strong> (1974-1979) - Stressed on increasing employment and poverty alleviation (Garibi Hatao); Indian National Highway System; Minimum Needs Programme. <strong>Sixth Five Year Plan</strong> (1980-85) - Beginning of economic liberation by eliminating price controls; Family Planning; National Bank for Agriculture and Rural Development was established (Shivaraman Committee). <strong>Seventh Five Year Plan</strong> (1985-90) - Improving Industrial productivity levels through the use of technology. <strong>Fourth Five-Year Plan</strong> (1969-74) - Based on Gadgil Formula (Growth with stability and progress towards self-reliance); Nationalization of 14 major Indian Banks and the Green Revolution boosted agriculture; Drought Prone Area Programme.</p>",
                    solution_hi: "<p>57.(d) <strong>पांचवीं पंचवर्षीय योजना </strong>(1974-1979) - रोजगार बढ़ाने और गरीबी उन्मूलन (गरीबी हटाओ) पर जोर दिया गया ; भारतीय राष्ट्रीय राजमार्ग प्रणाली; न्यूनतम आवश्यकता कार्यक्रम I <strong>छठी पंचवर्षीय योजना</strong> (1980-85) - मूल्य नियंत्रण समाप्त कर आर्थिक मुक्ति की शुरुआत; परिवार नियोजन; राष्ट्रीय कृषि एवं ग्रामीण विकास बैंक (शिवरामन समिति) की स्थापना की गई। <strong>सातवीं पंचवर्षीय योजना</strong> (1985-90) - प्रौद्योगिकी के उपयोग के माध्यम से औद्योगिक उत्पादकता के स्तर में सुधार। <strong>चौथी पंचवर्षीय योजना</strong> (1969-74) - गाडगिल योजना (स्थिरता के साथ विकास और आत्मनिर्भरता की दिशा में प्रगति के विचारों पर ध्यान दिया गया) पर आधारित; 14 प्रमुख भारतीय बैंकों का राष्ट्रीयकरण और हरित क्रांति ने कृषि को बढ़ावा दिया गया ; सूखा प्रवण क्षेत्र कार्यक्रम I</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. Kabir singh walked from his house 30 metres towards the north , 40 meters towards the east , 25 meters towards the north and 40 metres towards the west . He stopped at this point to buy some flowers at a flower shop.Then , he again walked towards the north. How far and in which direction is kabir singh&rsquo;s house from the flower shop?</p>",
                    question_hi: "<p>58. कबीर सिंह अपने घर से 30 मीटर उत्तर की ओर, 40 मीटर पूर्व की ओर, 25 मीटर उत्तर की ओर और 40 मीटर पश्चिम की ओर चला। वह एक फूल की दुकान पर कुछ फूल खरीदने के लिए यहाँ पर रुक गया। वह फिर से उत्तर की ओर चला गया। फूल की दुकान से कबीर सिंह का घर कितनी दूर और किस दिशा में है?</p>",
                    options_en: ["<p>55 metres , North</p>", "<p>55 metres, South</p>", 
                                "<p>85 metres, South</p>", "<p>85 metres, North</p>"],
                    options_hi: ["<p>55 मीटर, उत्तर</p>", "<p>55 मीटर, दक्षिण</p>",
                                "<p>85 मीटर, दक्षिण</p>", "<p>85 मीटर, उत्तर</p>"],
                    solution_en: "<p>58.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339844.png\" alt=\"rId30\" width=\"130\" height=\"95\"><br>Distance between kabir singh&rsquo;s house from the flower shop = 55 meter<br>Direction = South</p>",
                    solution_hi: "<p>58.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386339988.png\" alt=\"rId31\" width=\"151\" height=\"110\"><br>फूल की दुकान से कबीर सिंह के घर की दूरी = 55 मीटर<br>दिशा = दक्षिण</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. Petr Fiala, the Guest of Honour at the 10th Vibrant Gujarat Global Investors Summit, is the Prime Minister of which country?</p>",
                    question_hi: "<p>59. 10वें वाइब्रेंट गुजरात ग्लोबल इन्वेस्टर्स समिट के सम्मानित अतिथि पेट्र फियाला किस देश के प्रधान मंत्री हैं?</p>",
                    options_en: ["<p>Czech Republic</p>", "<p>Slovakia</p>", 
                                "<p>Hungary</p>", "<p>Austria</p>"],
                    options_hi: ["<p>चेक गणराज्य</p>", "<p>स्लोवाकिया</p>",
                                "<p>हंगरी</p>", "<p>ऑस्ट्रिया</p>"],
                    solution_en: "<p>59.(a)<br>Petr Fiala, serving as the Guest of Honour at the 10th Vibrant Gujarat Global Investors Summit, is the Prime Minister of the Czech Republic. His presence at this significant event in Gandhinagar underscores the strengthening ties and cooperation between the Czech Republic and India. The Summit, known for attracting global leaders and investors, is an important platform for international economic and trade discussions. Fiala&rsquo;s participation, along with other global leaders, highlights the Czech Republic&rsquo;s active engagement in global economic dialogues and its partnership with India in various sectors.</p>",
                    solution_hi: "<p>59.(a)<br>10वें वाइब्रेंट गुजरात ग्लोबल इन्वेस्टर्स समिट में गेस्ट ऑफ ऑनर के रूप में कार्यरत पेट्र फियाला चेक गणराज्य के प्रधान मंत्री हैं। गांधीनगर में इस महत्वपूर्ण कार्यक्रम में उनकी उपस्थिति चेक गणराज्य और भारत के बीच मजबूत होते संबंधों और सहयोग को रेखांकित करती है। वैश्विक नेताओं और निवेशकों को आकर्षित करने के लिए जाना जाने वाला यह शिखर सम्मेलन अंतरराष्ट्रीय आर्थिक और व्यापार चर्चा के लिए एक महत्वपूर्ण मंच है। अन्य वैश्विक नेताओं के साथ फियाला की भागीदारी, वैश्विक आर्थिक संवादों में चेक गणराज्य की सक्रिय भागीदारी और विभिन्न क्षेत्रों में भारत के साथ उसकी साझेदारी को उजागर करती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. A dealer sold 6 sewing machines for ₹63,000 with a profit of 5%. For how much should he sell 8 machines if he intends to earn 15% profit?</p>",
                    question_hi: "<p>60. एक विक्रेता ने 6 सिलाई मशीन 63000 रुपये में बेची तथा उसे 5% का लाभ हुआ | यदि वह 15% लाभ कमाना चाहता है, तो उसे 8 मशीनें किस कीमत पर बेचनी चाहिए ?</p>",
                    options_en: ["<p>₹88,200</p>", "<p>₹92,000</p>", 
                                "<p>₹69,300</p>", "<p>₹92,400</p>"],
                    options_hi: ["<p>₹88,200</p>", "<p>₹92,000</p>",
                                "<p>₹69,300</p>", "<p>₹92,400</p>"],
                    solution_en: "<p>60. (b) Let CP of machine be x<br>At 5% profit, on 6 sewing machines<br>6 &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>100</mn></mfrac></math>of x) = ₹63000<br>X = ₹ 10000<br>Cost price for 8 machines= 8 &times; ₹10000 = ₹80,000<br>To earn 15% profit,<br>Selling price of 8 machines = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>100</mn></mfrac></math> &times; 80,000 = ₹ 92,000</p>",
                    solution_hi: "<p>60. (b) <br>माना , मशीन की लागत मूल्य = x<br>6 सिलाई मशीनों को 5% लाभ पर बेचने पर <br>6 &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>100</mn></mfrac></math> of x) = ₹63000<br>X = ₹ 10000<br>अब, 8 मशीनों की लागत मूल्य = 8 &times; ₹10000 = ₹80,000<br>15% लाभ अर्जित करने के लिए, 8 मशीनों का बिक्री मूल्य:<br>बिक्री मूल्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>100</mn></mfrac></math> &times; 80,000 = ₹ 92,000<br>अतः, 15% लाभ पर 8 मशीनों का बिक्री मूल्य ₹92000 होगा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Which of the following pairs is correctly matched?</p>",
                    question_hi: "<p>61. निम्नलिखित में से कौन-सा युग्म सही सुमेलित है?</p>",
                    options_en: ["<p>Article 15 - Right against exploitation</p>", "<p>Article 17 - Abolition of titles</p>", 
                                "<p>Article 19 - Protection of certain rights regarding freedom of speech</p>", "<p>Article 14 - Abolition of untouchability</p>"],
                    options_hi: ["<p>अनुच्छेद 15 - शोषण के विरुद्ध अधिकार</p>", "<p>अनुच्छेद 17 - उपाधियों का उन्मूलन</p>",
                                "<p>अनुच्छेद 19 - वाक् स्वातंत्र्य संबंधी विशिष्ट अधिकारों का संरक्षण</p>", "<p>अनुच्छेद 14 - अस्पृश्यता का उन्मूलन</p>"],
                    solution_en: "<p>61.(c) <strong>Article 15 </strong>- Prohibition of discrimination on grounds of religion, race, caste, sex or place of birth. <strong>Article 17 </strong>- Abolition of Untouchability. <strong>Article 14 -</strong> Equality before law. <strong>Article 18 -</strong> Abolition of titles. <strong>Article 23 -</strong> Prohibition of traffic in human beings and forced labor. <strong>Article 24</strong> (Prohibition of employment of children in factories, etc).</p>",
                    solution_hi: "<p>61.(c) <strong>अनुच्छेद 15 -</strong> धर्म, नस्ल, जाति, लिंग या जन्म स्थान के आधार पर भेदभाव पर निषेध। <strong>अनुच्छेद 17 -</strong> अस्पृश्यता का उन्मूलन। <strong>अनुच्छेद 14 -</strong> विधि के समक्ष समानता I <strong>अनुच्छेद 18 -</strong> उपाधियों का उन्मूलन। <strong>अनुच्छेद 23 -</strong> मानव तस्करी और बेगार (बलात् श्रम) पर प्रतिबंध। <strong>अनुच्छेद 24</strong> (कारखानों आदि में बच्चों के रोजगार पर प्रतिबंध) I</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. If + denotes &lsquo;multiplication&rsquo;, - denotes &lsquo;addition&rsquo;, &times; denotes &lsquo;division&rsquo; and &divide; denotes &lsquo;subtraction&rsquo;, then which of the following equations is true?</p>",
                    question_hi: "<p>62. यदि +, &lsquo;गुणा&rsquo; को दर्शाता है, -, &lsquo;योग&rsquo; को दर्शाता है, &times;, &lsquo;भाग&rsquo; को दर्शाता है और &divide;, &lsquo;घटा&rsquo; को दर्शाता है, तो निम्नलिखित में से कौन सा समीकरण सत्य है?</p>",
                    options_en: ["<p>11 &divide; 8 &times; 2 - 4 + 1 = 42</p>", "<p>10 - 12 &divide; 18 &times; 6 + 2 = 16</p>", 
                                "<p>15 + 15 &times; 3 - 4 &divide; 5 = 26</p>", "<p>9 + 5 - 16 &times; 4 &divide; 2 = 41</p>"],
                    options_hi: ["<p>11 &divide; 8 &times; 2 - 4 + 1 = 42</p>", "<p>10 - 12 &divide; 18 &times; 6 + 2 = 16</p>",
                                "<p>15 + 15 &times; 3 - 4 &divide; 5 = 26</p>", "<p>9 + 5 - 16 &times; 4 &divide; 2 = 41</p>"],
                    solution_en: "<p>62.(b) After changing the signs in option (B), <br>10 + 12 - 18 &divide; 6 &times; 2 = 16<br>&rarr; 22 - 6 = 16<br>&rarr; 16 = 16</p>",
                    solution_hi: "<p>62.(b) विकल्प (B) में चिन्ह को बदलने के बाद,<br>10 + 12 - 18 &divide; 6 &times; 2 = 16<br>&rarr; 22 - 6 = 16<br>&rarr; 16 = 16</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. The value of 27a<sup>3</sup> - 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b<sup>3</sup> is equal to:</p>",
                    question_hi: "<p>63. 27a<sup>3</sup> - 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b<sup>3</sup>&nbsp;का मान किसके बराबर है ?</p>",
                    options_en: ["<p>(3a - <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> - 2b<sup>2</sup> - 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>", "<p>(3a - <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> - 2b<sup>2</sup> + 6<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>", 
                                "<p>(3a - <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> + 2b<sup>2</sup> + 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>", "<p>(3a - <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> + 2b<sup>2</sup> - 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>"],
                    options_hi: ["<p>(3a - <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> - 2b<sup>2</sup> - 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>", "<p>(3a - <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> - 2b<sup>2</sup> + 6<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>",
                                "<p>(3a - <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> + 2b<sup>2</sup> + 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>", "<p>(3a - <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> + 2b<sup>2</sup> - 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>"],
                    solution_en: "<p>63. (c) We know, <br>x<sup>3</sup> - y<sup>3</sup> = (x - y)(x<sup>2</sup> + y<sup>2</sup> + xy)<br>27a<sup>3</sup> - 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b<sup>3</sup> = (3a)<sup>3</sup> - (<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)<sup>3</sup><br>= (3a -&nbsp;<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> + 2b<sup>2</sup> + 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>",
                    solution_hi: "<p>63. (c) हम जानते हैं,<br>x<sup>3</sup> - y<sup>3</sup> = (x - y)(x<sup>2</sup> + y<sup>2</sup> + xy)<br>27a<sup>3</sup> - 2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b<sup>3</sup> = (3a)<sup>3</sup> - (<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)<sup>3</sup><br>= (3a -&nbsp;<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>b)(9a<sup>2</sup> + 2b<sup>2</sup> + 3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>ab)</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. Which of the following statements is correct about Article 129 of the Constitution of India?</p>",
                    question_hi: "<p>64. भारत के संविधान के अनुच्छेद 129 के बारे में निम्नलिखित में से कौन-सा कथन सही है?</p>",
                    options_en: ["<p>It provides for the Supreme Court to give advisory opinion to the President.</p>", "<p>It provides for the Supreme Court to be a court of original jurisdiction</p>", 
                                "<p>It provides for the Supreme Court to be a court of record.</p>", "<p>It provides for the Supreme Court to be the highest court of appeal.</p>"],
                    options_hi: ["<p>यह सर्वोच्च न्यायालय को राष्ट्रपति को सलाहकारी राय देने का प्रावधान करता है।</p>", "<p>यह सर्वोच्च न्यायालय को मूल क्षेत्राधिकार वाला न्यायालय बनाने का प्रावधान करता है।</p>",
                                "<p>यह सर्वोच्च न्यायालय को अभिलेख न्यायालय होने का प्रावधान करता है।</p>", "<p>यह सर्वोच्च न्यायालय को अपील की सर्वोच्च अदालत बनाने का प्रावधान करता है।</p>"],
                    solution_en: "<p>64.(c) <strong>Article 143 -</strong> Power of the President to consult the Supreme Court. <strong>Article 131 -</strong> Original jurisdiction of the Supreme Court. <strong>Article 132 -</strong> Appellate jurisdiction of the Supreme Court in appeals from High Courts in certain cases. <strong>Article 133 -</strong> Appellate jurisdiction of the Supreme Court in appeals from High Courts in regard to Civil matters. <strong>Article 134 - </strong>Appellate jurisdiction of the Supreme Court in regard to criminal matters.</p>",
                    solution_hi: "<p>64.(c) <strong>अनुच्छेद 143 </strong>- उच्चतम न्यायालय से परामर्श करने की राष्ट्रपति की शक्ति। <strong>अनुच्छेद 131</strong> - सर्वोच्च न्यायालय का मूल क्षेत्राधिकार। <strong>अनुच्छेद 132</strong> - कुछ मामलों में उच्च न्यायालयों से अपील में सर्वोच्च न्यायालय का अपीलीय क्षेत्राधिकार। <strong>अनुच्छेद 133 </strong>- सिविल मामलों के संबंध में उच्च न्यायालयों से अपील में सर्वोच्च न्यायालय का अपीलीय क्षेत्राधिकार। <strong>अनुच्छेद 134</strong> - आपराधिक मामलों के संबंध में सर्वोच्च न्यायालय का अपीलीय क्षेत्राधिकार।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. Which option has the words arranged in the order in which they appear in an English dictionary?</p>",
                    question_hi: "<p>65. किस विकल्प में शब्दों को उसी क्रम में व्यवस्थित किया गया है जिस क्रम में वे अंग्रेजी शब्दकोश में आते हैं?</p>",
                    options_en: ["<p>Antigen &rarr; Antique &rarr; Antipathy &rarr; Apology &rarr; Branch</p>", "<p>Antigen &rarr; Antique &rarr; Apology &rarr; Antipathy &rarr; Branch</p>", 
                                "<p>Antigen &rarr; Antipathy &rarr; Antique &rarr; Apology &rarr; Branch</p>", "<p>Antipathy &rarr; Apology &rarr; Branch &rarr; Antigen &rarr; Antique</p>"],
                    options_hi: ["<p>Antigen &rarr; Antique &rarr; Antipathy &rarr; Apology &rarr; Branch</p>", "<p>Antigen &rarr; Antique &rarr; Apology &rarr; Antipathy &rarr; Branch</p>",
                                "<p>Antigen &rarr; Antipathy &rarr; Antique &rarr; Apology &rarr; Branch</p>", "<p>Antipathy &rarr; Apology &rarr; Branch &rarr; Antigen &rarr; Antique</p>"],
                    solution_en: "<p>65.(c)<br><strong>Antigen &rarr; Antipathy &rarr; Antique &rarr; Apology &rarr; Branch</strong></p>",
                    solution_hi: "<p>65.(c)<br><strong>Antigen &rarr; Antipathy &rarr; Antique &rarr; Apology &rarr; Branch</strong></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#247;</mo><mn>42</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>24</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>18</mn><mo>+</mo><mn>3</mn><mo>&#247;</mo><mo>(</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>3</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>7</mn></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>66. <math display=\"inline\"><mfrac><mrow><mn>36</mn><mo>&#247;</mo><mn>42</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>24</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>18</mn><mo>+</mo><mn>3</mn><mo>&#247;</mo><mo>(</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>3</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>7</mn></mrow></mfrac></math> का मान है :</p>",
                    options_en: ["<p>7</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>7</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p>7<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>66. (a) Apply BODMAS rule in the given equation<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#247;</mo><mn>42</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>24</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>18</mn><mo>+</mo><mn>3</mn><mo>&#247;</mo><mo>(</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>3</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>7</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>36</mn><mo>&#247;</mo><mn>252</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>24</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>18</mn><mo>+</mo><mn>3</mn><mo>&#247;</mo><mo>(</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>21</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>36</mn><mo>&#247;</mo><mn>252</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>24</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>18</mn><mo>+</mo><mn>3</mn><mo>&#247;</mo><mo>(</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>21</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>&#160;</mi><mn>8</mn><mo>-</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>10</mn></mrow><mrow><mn>8</mn></mrow></mfrac></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>21</mn></mrow></mfrac></math></p>\n<p>= 7</p>",
                    solution_hi: "<p>66.(a) BODMAS नियम को दिए गए समीकरण पर लागू करने पर <br>विकल्प (a) के लिए <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>&#247;</mo><mn>42</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>24</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>18</mn><mo>+</mo><mn>3</mn><mo>&#247;</mo><mo>(</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>3</mn><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>7</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>36</mn><mo>&#247;</mo><mn>252</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>24</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>18</mn><mo>+</mo><mn>3</mn><mo>&#247;</mo><mo>(</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>21</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>36</mn><mo>&#247;</mo><mn>252</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mi>&#160;</mi><mn>24</mn><mo>&#215;</mo><mn>6</mn><mo>&#247;</mo><mn>18</mn><mo>+</mo><mn>3</mn><mo>&#247;</mo><mo>(</mo><mn>2</mn><mo>-</mo><mn>6</mn><mo>)</mo><mo>-</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>3</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>&#247;</mo><mn>8</mn></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>21</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>&#160;</mi><mn>8</mn><mo>-</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mo>-</mo><mfrac><mrow><mn>10</mn></mrow><mrow><mn>8</mn></mrow></mfrac></mrow><mrow><mn>21</mn><mo>&#247;</mo><mn>21</mn></mrow></mfrac></math></p>\n<p>= 7</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67.Where was the first All India Kisan Sabha formed?</p>",
                    question_hi: "<p>67. प्रथम अखिल भारतीय किसान सभा का गठन कहां हुआ था?</p>",
                    options_en: ["<p>Lucknow</p>", "<p>Lahore</p>", 
                                "<p>Amritsar</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>लखनऊ</p>", "<p>लाहौर</p>",
                                "<p>अमृतसर</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>67.(a) <strong>Lucknow (</strong>1936) - By Swami Sahajanand Saraswati (First President), NG Ranga was General secretary. It is the farmer&rsquo;s wing of the communist party of India. Swami Sahajanand Saraswati started the Kisan Sabha movement from Bihar and founded Bihar Provincial Kisan Sabha (1929). <strong>Peasant Movements </strong>- Indigo Rebellion (1859-62), Pabna Movement (1870s-80s), Deccan Riots (1875), Champaran Satyagraha (1917), Kheda Satyagraha (1918), Moplah Rebellion (1921), Bardoli Satyagraha (1928).</p>",
                    solution_hi: "<p>67.(a) <strong>लखनऊ</strong> (1936) - स्वामी सहजानंद सरस्वती (प्रथम राष्ट्रपति) द्वारा, एनजी रंगा महासचिव थे। यह भारत की कम्युनिस्ट पार्टी की किसान संगठन से है। स्वामी सहजानंद सरस्वती ने किसान सभा आंदोलन की शुरुआत बिहार से की और बिहार प्रांतीय किसान सभा (1929) की स्थापना की। <strong>किसान आंदोलन </strong>- नील विद्रोह (1859-62), पबना विद्रोह (1870-80 के दशक), दक्कन विद्रोह (1875),चंपारण सत्याग्रह (1917), खेड़ा सत्याग्रह (1918), मोपला विद्रोह (1921), बारदौली सत्याग्रह (1928)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. The curved surface area of a hemisphere with radius 7cm is:</p>",
                    question_hi: "<p>68. 7 सेमी त्रिज्या वाले एक अर्धगोले का वक्र पृष्ठ क्षेत्रफल कितना होगा ?</p>",
                    options_en: ["<p>308 cm<sup>2</sup></p>", "<p>616 cm<sup>2</sup></p>", 
                                "<p>462 cm<sup>2</sup></p>", "<p>385 cm<sup>2</sup></p>"],
                    options_hi: ["<p>308 cm<sup>2</sup></p>", "<p>616 cm<sup>2</sup></p>",
                                "<p>462 cm<sup>2</sup></p>", "<p>385 cm<sup>2</sup></p>"],
                    solution_en: "<p>68. (a) Curved surface of a hemisphere = 2&pi;r<sup>2</sup><br>= 2 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 7 &times; 7<br>= 308 cm<sup>2</sup></p>",
                    solution_hi: "<p>68. (a) एक अर्धगोलाकार (hemisphere) का वक्र सतह क्षेत्रफल (curved surface area) निम्नलिखित है: <br>अर्धगोले वक्र सतह क्षेत्रफल = 2&pi;r<sup>2</sup><br>जहां r अर्धगोलाकार का त्रिज्या (7 सेमी) है।&nbsp;<br>अतः , अर्धगोले वक्र सतह क्षेत्रफल = 2 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 7 &times; 7<br>= 308 cm<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. Which of the following is connected with daily atmospheric weather changes?</p>",
                    question_hi: "<p>69. निम्नलिखित में से कौन-सा दैनिक वायुमंडलीय मौसम परिवर्तन से संबंधित है?</p>",
                    options_en: ["<p>Asthenosphere</p>", "<p>Stratopause</p>", 
                                "<p>Troposphere</p>", "<p>Lithosphere</p>"],
                    options_hi: ["<p>दुर्बलता मंडल (Asthenosphere)</p>", "<p>समताप सीमा (Stratopause)</p>",
                                "<p>क्षोभ मंडल (Troposphere)</p>", "<p>स्थलमंडल (Lithosphere)</p>"],
                    solution_en: "<p>69.(c) <strong>Troposphere</strong> (average, about 12 kilometers from Earth&rsquo;s surface). It is the lowest layer of the Earth&rsquo;s atmosphere. <strong>The stratosphere </strong>(12 and 50 km) is home to Earth&rsquo;s ozone layer, which protects us from the Sun&rsquo;s harmful ultraviolet radiation. The <strong>Mesosphere </strong>(50 and 80 km) - Most meteors burn up in this atmospheric layer. <strong>Thermosphere</strong> (80 and 700 km) - It is both cloud- and water vapor-free. The International Space Station orbits in the thermosphere. <strong>Exosphere</strong> (700 - 10,000 km) - It is the highest layer of Earth&rsquo;s atmosphere and, at its top, merges with the solar wind.</p>",
                    solution_hi: "<p>69.(c) <strong>क्षोभमंडल</strong> (औसतन, पृथ्वी की सतह से लगभग 12 किलोमीटर)। यह पृथ्वी के वायुमंडल की सबसे निचली परत है। <strong>समताप मंडल</strong> (12 और 50 किमी) पृथ्वी की ओजोन परत का घर है, जो हमें सूर्य की हानिकारक पराबैंगनी विकिरण से बचाता है।<strong> मध्यमण्डल</strong> (50 और 80 किमी) - अधिकांश उल्काएँ इसी वायुमंडलीय परत में जलती हैं। <strong>आयनमंडल</strong> (80 और 700 किमी) - यह बादल और जल वाष्प-मुक्त दोनों है। अंतर्राष्ट्रीय अंतरिक्ष स्टेशन आयनमंडल में परिक्रमा करता है।<strong> बहिर्मंडल</strong> (700 - 10,000 किमी) - यह पृथ्वी के वायुमंडल की सबसे ऊंची परत है और इसके शीर्ष पर, सौर हवा के साथ विलीन हो जाती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. How will you write the 9.11 hours in hours, minutes and seconds?</p>",
                    question_hi: "<p>70. आप 9.11 घंटे को घंटे, मिनट और सेकंड में कैसे लिखेंगे?</p>",
                    options_en: ["<p>9 hours, 11 minutes</p>", "<p>9 hours, 49 minutes</p>", 
                                "<p>9 hours, 10 minutes, 10 seconds</p>", "<p>9 hours, 6 minutes, 36 seconds</p>"],
                    options_hi: ["<p>9 घंटे, 11 मिनट</p>", "<p>9 घंटे, 49 मिनट</p>",
                                "<p>9 घंटे, 10 मिनट, 10 सेकंड</p>", "<p>9 घंटे, 6 मिनट, 36 सेकंड</p>"],
                    solution_en: "<p>70.(d)<br>9.11 hours = 9 hours + 0.11&nbsp;&times; 60 minutes<br>= 9 hours + 6 minutes + 0.6 &times; 60 seconds<br>= 9 hours + 6 minutes + 36 seconds</p>",
                    solution_hi: "<p>70.(d)<br>9.11 घंटे = 9 घंटे + 0.11&times; 60&nbsp;मिनट<br>= 9 घंटे + 6 मिनट + 0.6 &times; 60 सेकंड&nbsp;<br>= 9 घंटे + 6 मिनट + 36 सेकंड</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. According to the Census of India 2011, which of the following states has the lowest literacy rate?</p>",
                    question_hi: "<p>71. भारत की जनगणना 2011 के अनुसार निम्नलिखित में से किस राज्य की साक्षरता दर सबसे कम है?</p>",
                    options_en: ["<p>Kerala</p>", "<p>Tripura</p>", 
                                "<p>Goa</p>", "<p>Mizoram</p>"],
                    options_hi: ["<p>केरल</p>", "<p>त्रिपुरा</p>",
                                "<p>गोवा</p>", "<p>मिजोरम</p>"],
                    solution_en: "<p>71.(b) <strong>Tripura. </strong>According to Census 2011: The literacy rate in the country is 74.04 %, 82.14 % for males and 65.46 % for females. Highest literacy - Kerala (94 %), Lakshadweep (91.85 %) and Mizoram (91.33 %). Lowest literacy rate - Bihar (61.80%).</p>",
                    solution_hi: "<p>71.(b) <strong>त्रिपुरा</strong>। जनगणना 2011 के अनुसार: देश में साक्षरता दर 74.04%, पुरुषों के लिए 82.14% और महिलाओं के लिए 65.46% है। उच्चतम साक्षरता - केरल (94%), लक्षद्वीप (91.85%) और मिजोरम (91.33%)। न्यूनतम साक्षरता दर - बिहार (61.80%)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. In a class of 47 students. Anurag&rsquo;s roll number is eighteenth from the end. What is his roll number from the beginning?</p>",
                    question_hi: "<p>72. 47 छात्रों की एक कक्षा में अनुराग का रोल नंबर अंत से अठारहवां है। उसका रोल नंबर शुरू से कितना है?</p>",
                    options_en: ["<p>30</p>", "<p>29</p>", 
                                "<p>27</p>", "<p>28</p>"],
                    options_hi: ["<p>30</p>", "<p>29</p>",
                                "<p>27</p>", "<p>28</p>"],
                    solution_en: "<p>72.(a)<br>Anurag&rsquo;s roll no. from the beginning = 47 - 18 + 1 = 30</p>",
                    solution_hi: "<p>72.(a)<br>अनुराग रोल नं. प्रारंभ से= 47 - 18 + 1 = 30</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. If the difference between 62% and 80% of a number is 198, then the difference between 92% and 56% of the number will be:</p>",
                    question_hi: "<p>73. यदि एक संख्या के 62% और 80% में 198 का अंतर है, तो उस संख्या के 92% और 56% में कितना अंतर होगा ?</p>",
                    options_en: ["<p>360</p>", "<p>1100</p>", 
                                "<p>396</p>", "<p>3564</p>"],
                    options_hi: ["<p>360</p>", "<p>1100</p>",
                                "<p>396</p>", "<p>3564</p>"],
                    solution_en: "<p>73. (c) Let the number be &lsquo;x&rsquo;<br>80% x - 62% x = 198<br>18% x = 198<br>1% x = 11<br>92% x - 56% x = 36% x = 396</p>",
                    solution_hi: "<p>73. (c) माना , संख्या = &lsquo;x&rsquo;<br>80% x - 62% x = 198<br>18% x = 198<br>1% x = 11<br>92% x - 56% x = 36% x = 396</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. In which state is the famous Brihadeshwar temple situated?</p>",
                    question_hi: "<p>74. प्रसिद्ध बृहदेश्वर मंदिर किस राज्य में स्थित है?</p>",
                    options_en: ["<p>Kerala</p>", "<p>Karnataka</p>", 
                                "<p>Tamil Nadu</p>", "<p>Andhra Pradesh</p>"],
                    options_hi: ["<p>केरल</p>", "<p>कर्नाटक</p>",
                                "<p>तमिलनाडु</p>", "<p>आंध्र प्रदेश</p>"],
                    solution_en: "<p>74.(c) <strong>Tamil Nadu. </strong>The Brihadeshwara temple was built by the Chola King, Raja Raj Chola I. The famous deity in the temple is Lord Shiva in a dancing pose (Nataraj). <strong>Other temples in Tamil Nadu - </strong>Meenakshi Temple (Madurai), Sri Laxmi Narayan Golden Temple (Vellore), Ramanathaswamy Temple (Rameshwaram), Kapaleeshwarar Temple (Chennai).</p>",
                    solution_hi: "<p>74.(c) <strong>तमिलनाडु।</strong> बृहदेश्वर मंदिर का निर्माण चोल राजा, राजा राज चोल प्रथम ने करवाया था। मंदिर में प्रसिद्ध देवता नृत्य मुद्रा (नटराज) में भगवान शिव हैं। <strong>तमिलनाडु में अन्य मंदिर</strong> - मीनाक्षी मंदिर (मदुरै), श्री लक्ष्मी नारायण स्वर्ण मंदिर (वेल्लोर), रामनाथस्वामी मंदिर (रामेश्वरम), कपालेश्वर मंदिर (चेन्नई)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "75. What is the smallest number of birds that could ﬂy in the formation described below, assuming that each bird is counted only once to fulfil all the conditions listed?<br />Two birds in front of a bird, two birds behind a bird and a bird between two birds.",
                    question_hi: "75. नीचे वर्णित गठन में पक्षियों की सबसे छोटी संख्या क्या है, यह मानते हुए कि सूचीबद्ध सभी शर्तों को पूरा करने के लिए प्रत्येक पक्षी को केवल एक बार गिना जाता है?<br />एक पंछी के आगे दो पंछी, एक पंछी के पीछे दो पंछी और दो पंछी के बीच एक पंछी ",
                    options_en: [" 5", " 4", 
                                " 3", " 7"],
                    options_hi: [" 5", " 4",
                                " 3", " 7"],
                    solution_en: "75.(a)<br />Smallest no.of birds = 5 ",
                    solution_hi: "75.(a)<br />पक्षियों की सबसे छोटी संख्या= 5",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. Which of the following is an example of capital receipt in the government budget ?</p>",
                    question_hi: "<p>76. निम्नलिखित में से कौन-सा सरकारी बजट में पूंजीगत प्राप्ति (capital receipt) का एक उदाहरण है?</p>",
                    options_en: ["<p>Sale of government bonds</p>", "<p>Income from government-owned enterprises</p>", 
                                "<p>Tax revenue collected from citizens</p>", "<p>Grants received from foreign countries</p>"],
                    options_hi: ["<p>सरकारी बंधपत्र की बिक्री</p>", "<p>सरकारी-स्वामित्व वाले उद्यमों से आय</p>",
                                "<p>नागरिकों से वसूला गया राजस्व कर</p>", "<p>विदेशों से प्राप्त अनुदान</p>"],
                    solution_en: "<p>76.(a) <strong>Sale of government bonds. Capital Receipts</strong> are loans raised from the public (market loans), borrowings from the Reserve Bank and other parties through the sale of Treasury bills, loans received from foreign bodies and governments, and recoveries of loans granted by the Central government. Capital Receipts are long term receipts. Revenue Receipts are short term receipts and money received for a short period.</p>",
                    solution_hi: "<p>76.(a) <strong>सरकारी बंधपत्र की बिक्री। पूंजीगत प्राप्तियां</strong> जनता से लिए गए ऋण (बाजार ऋण), ट्रेजरी बिलों की बिक्री के माध्यम से रिज़र्व बैंक और अन्य पार्टियों से उधार, विदेशी निकायों और सरकारों से प्राप्त ऋण और केंद्र सरकार द्वारा दिए गए ऋणों की वसूली हैं। पूँजीगत प्राप्तियाँ दीर्घकालिक प्राप्तियाँ होती हैं। राजस्व प्राप्तियाँ अल्पावधि प्राप्तियाँ और अल्प अवधि के लिए प्राप्त धन होता हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77. A person marked his goods at a price that would give him 40% profit. But he declared a sale and allowed a 20% discount on the marked price. What is the profit percentage of the person in the whole transaction?</p>",
                    question_hi: "<p>77. एक व्यक्ति ने अपनी वस्तुओं की कीमत इतनी रखी कि उसे 40% का लाभ होता है | लेकिन उसने सेल की घोषणा की तथा अंकित मूल्य पर 20% की छूट देने लगा | पूरे लेन-देन में उस व्यक्ति के लाभ का प्रतिशत कितना रहा ?</p>",
                    options_en: ["<p>12%</p>", "<p>32%</p>", 
                                "<p>20%</p>", "<p>30%</p>"],
                    options_hi: ["<p>12%</p>", "<p>32%</p>",
                                "<p>20%</p>", "<p>30%</p>"],
                    solution_en: "<p>77. (a) Let the CP of article be &lsquo;x&rsquo; <br>Marked Price = <math display=\"inline\"><mfrac><mrow><mn>140</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>of x<br>After 20% discount on MP, SP = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>5</mn></mfrac></mstyle></math>of x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>112</mn><mn>100</mn></mfrac></math> of x<br>% profit in whole transaction = 12%</p>",
                    solution_hi: "<p>77. (a) माना , वस्तु का क्रय मूल्य = &lsquo;x&rsquo; <br>अंकित मूल्य = <math display=\"inline\"><mfrac><mrow><mn>140</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> of x<br>अंकित मूल्य पर 20% छूट के बाद, विक्रय मूल्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>5</mn></mfrac></mstyle></math>of x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>112</mn><mn>100</mn></mfrac></math> of x<br>पूरे लेन-देन में % लाभ = 12%</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. In the following list of numerals, how many 3s are followed by 3, but NOT preceded by 3?<br>2 9 6 3 3 4 5 7 8 3 3 3 4 6 2 3 3 3 3 8 6 2 3</p>",
                    question_hi: "<p>78. निम्नलिखित अंकों की सूची में, कितने 3 के बाद 3 है, लेकिन 3 से पहले नहीं है?<br>2 9 6 3 3 4 5 7 8 3 3 3 4 6 2 3 3 3 3 8 6 2 3</p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>3</p>", "<p>1</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>3</p>", "<p>1</p>"],
                    solution_en: "<p>78.(c) <br>2 9 6 <span style=\"text-decoration: underline;\">3 3 4</span> 5 7 8 3 <span style=\"text-decoration: underline;\">3 3 4</span> 6 2 3 3 <span style=\"text-decoration: underline;\">3 3 8</span> 6 2 3<br>Three 3s are followed by 3, but NOT preceded by 3.</p>",
                    solution_hi: "<p>78.(c) <br>2 9 6 <span style=\"text-decoration: underline;\">3 3 4</span> 5 7 8 3 <span style=\"text-decoration: underline;\">3 3 4</span> 6 2 3 3 <span style=\"text-decoration: underline;\">3 3 8</span> 6 2 3<br>तीन अंक हैं जो 3 के बाद 3 है, लेकिन 3 से पहले नहीं है</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. Which of the abhinayas deals with the use of costumes, jewellery, facial make-up etc?</p>",
                    question_hi: "<p>79. किस अभिनय का संबंध वेशभूषा, आभूषण, चेहरे के श्रृंगार आदि के उपयोग से है?</p>",
                    options_en: ["<p>Vachika Abhinaya</p>", "<p>Aharya Abhinaya</p>", 
                                "<p>Sattvika Abhinaya</p>", "<p>Angika Abhinaya</p>"],
                    options_hi: ["<p>वाचिक अभिनय</p>", "<p>आहार्य अभिनय</p>",
                                "<p>सात्विक अभिनय</p>", "<p>आंगिक अभिनय</p>"],
                    solution_en: "<p>79.(b)<strong> Aharya Abhinaya. </strong>A dancer wears a readymade stitched costume. The lower garment is dhoti and the upper garment is a choli with a davani. The traditional jewelry used is the head ornaments known as &ldquo;tal saman&rdquo;, the sun and moon diadems. There are necklaces, long and short, belt, bangles, rakudi, kunjalam and flowers on the head, namely orange and white. <strong>Vachika Abhinaya: </strong>It constitutes the Kavyas (poems) and natakas (dramas) which are made up of speech. <strong>Saatwikam Abhinayam: </strong>It is performed with the sattvika emotions by those who can represent them proficiently. <strong>Aangika Abhinaya:</strong> It is expressed by the bodily movements where the body becomes the sole medium of expression.</p>",
                    solution_hi: "<p>79.(b) <strong>आहार्य अभिनय</strong>। नर्तक रेडीमेड सिला हुआ पोशाक पहनता है। निचला वस्त्र धोती है और ऊपरी वस्त्र दावनी के साथ चोली है। उपयोग किए जाने वाले पारंपरिक आभूषण सिर के आभूषण हैं जिन्हें \"ताल समान\", सूर्य और चंद्रमा की माला के नाम से जाना जाता है। हार, लंबे और छोटे, बेल्ट, चूड़ियाँ, रकुडी, कुंजालम और सिर पर नारंगी और सफेद रंग के फूल हैं। <strong>वाचिक अभिनय:</strong> इसमें काव्य (कविताएं) और नाटक (नाटक) शामिल हैं जो भाषण से बने होते हैं।<strong> सात्विक अभिनयम:</strong> यह उन लोगों द्वारा सात्विक भावनाओं के साथ किया जाता है जो उन्हें कुशलता से प्रस्तुत कर सकते हैं। <strong>आंगिक अभिनय:</strong> यह शारीरिक गतिविधियों द्वारा व्यक्त किया जाता है जहां शरीर अभिव्यक्ति का एकमात्र माध्यम बन जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. Two racers run at a speed of 100m/min and 120m/min respectively. If the second racer takes 10 minute less than the first to complete the run, then how long is the race?</p>",
                    question_hi: "<p>80. दो धावक क्रमशः 100 मीटर/मिनट तथा 120 मीटर / मिनट की चाल से दौड़ते हैं | यदि दूसरे धावक को पहले धावक की तुलना में दौड़ पूरी करने में 10 मिनट कम लगते हैं, तो दौड़ की लंबाई कितनी है ?</p>",
                    options_en: ["<p>4 km</p>", "<p>6 km</p>", 
                                "<p>1 km</p>", "<p>2 km</p>"],
                    options_hi: ["<p>4 km</p>", "<p>6 km</p>",
                                "<p>1 km</p>", "<p>2 km</p>"],
                    solution_en: "<p>80. (b) Speed of A and B are 100m/min and 120m/min respectively. <br>Distance = LCM(100,120) = 600units.&nbsp;<br>Time taken by A to cover 600 unit = <math display=\"inline\"><mfrac><mrow><mn>600</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 6min<br>Time taken by B to cover 600 unit = <math display=\"inline\"><mfrac><mrow><mn>600</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math>= 5 min<br>Time difference is 1 min which is given as 10 min<br>Thus, 600 unit distance = 6000 m = 6 km</p>",
                    solution_hi: "<p>80. (b) A और B की गति क्रमशः 100 मीटर/मिनट और 120 मीटर/मिनट है। <br>दूरी = LCM(100,120) = 600 इकाइयाँ<br>A द्वारा 600 यूनिट तय करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>600</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 6 मिनट<br>B द्वारा 600 यूनिट तय करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>600</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math> = 5 मिनट<br>समय का अंतर 1 मिनट है जिसे 10 मिनट दिया गया है<br>अत: 600 इकाई दूरी = 6000 मीटर = 6 किमी</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340265.png\" alt=\"rId32\" width=\"118\" height=\"111\"></p>",
                    question_hi: "<p>81. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340265.png\" alt=\"rId32\" width=\"118\" height=\"111\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340484.png\" alt=\"rId33\" width=\"109\" height=\"22\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340589.png\" alt=\"rId34\" width=\"109\" height=\"22\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340699.png\" alt=\"rId35\" width=\"109\" height=\"22\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340812.png\" alt=\"rId36\" width=\"107\" height=\"23\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340484.png\" alt=\"rId33\" width=\"109\" height=\"22\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340589.png\" alt=\"rId34\" width=\"109\" height=\"22\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340699.png\" alt=\"rId35\" width=\"109\" height=\"22\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340812.png\" alt=\"rId36\" width=\"107\" height=\"23\"></p>"],
                    solution_en: "<p>81.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340484.png\" alt=\"rId33\" width=\"109\" height=\"22\"></p>",
                    solution_hi: "<p>81.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340484.png\" alt=\"rId33\" width=\"109\" height=\"22\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. U. Srinivas is a famous player of which of the following instruments?</p>",
                    question_hi: "<p>82. यू. श्रीनिवास निम्नलिखित में से किस वाद्य यंत्र के प्रसिद्ध वादक हैं?</p>",
                    options_en: ["<p>Mandolin</p>", "<p>Violin</p>", 
                                "<p>Sitar</p>", "<p>Guitar</p>"],
                    options_hi: ["<p>मंडोलिन</p>", "<p>वायलिन</p>",
                                "<p>सितार</p>", "<p>गिटार</p>"],
                    solution_en: "<p>82.(a) <strong>Mandolin</strong> - S Balamurali Krishna, Nagen Dey, Khagen Dey. <strong>Violin</strong> - Lalgudi Jayaram, VG Jog, M Chandrasekharan, NR Murlidharan, MS Gopalakrishnan. <strong>Sitar</strong> - Ustad Vilayat Khan, Pt Ravi Shankar, Shujaat Hussain Khan, Shahid Parvez Khan, Anushka Shankar, Nikhil Banerjee, Mustaq Ali Khan, Budhaditya Mukherjee. <strong>Guitar -</strong> Amyt Dutta, Braj Bhushan Kabra. <strong>Uppalapu Srinivas -</strong> Padma Shri (1998), Sangeet Natak Akademi Award (2010), National Citizens Award (1991).</p>",
                    solution_hi: "<p>82.(a) <strong>मैंडोलिन </strong>- एस बालमुरली कृष्णा, नागेन डे, खगेन डे। <strong>वायलिन</strong> - लालगुड़ी जयराम, वीजी जोग, एम चंद्रशेखरन, एनआर मुरलीधरन, एमएस गोपालकृष्णन। <strong>सितार -</strong> उस्ताद विलायत खान, पीटी रवि शंकर, शुजात हुसैन खान, शाहिद परवेज खान, अनुष्का शंकर, निखिल बनर्जी, मुस्तक अली खान, बुद्धादित्य मुखर्जी। <strong>गिटार</strong> - अमित दत्ता, ब्रज भूषण काबरा। <strong>उप्पलापू श्रीनिवास </strong>- पद्मश्री (1998), संगीत नाटक अकादमी पुरस्कार (2010), राष्ट्रीय नागरिक पुरस्कार (1991) ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "<p>83. The simple interest on a certain sum at the end of three years at 5% p.a. is ₹1,200. The compound interest on the same sum for the same period at the same rate is (interest compounded yearly):</p>",
                    question_hi: "<p>83. किसी निश्चित राशि पर 5% प्रति वर्ष की दर से तीन वर्षों के अंत में साधारण ब्याज 1200 रुपये है | इसी राशि पर इसी अवधि के लिए इतने ही ब्याज दर से कितने चक्रवृद्धि ब्याज की प्राप्ति होगी ? ( ब्याज का संयोजन वार्षिक है)</p>",
                    options_en: ["<p>₹1,800</p>", "<p>₹1,260</p>", 
                                "<p>₹820</p>", "<p>₹1,261</p>"],
                    options_hi: ["<p>₹1,800</p>", "<p>₹1,260</p>",
                                "<p>₹820</p>", "<p>₹1,261</p>"],
                    solution_en: "<p>83. (d) Simple interest = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>As per given data, SI = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>= ₹1200<br>P = <math display=\"inline\"><mfrac><mrow><mn>1200</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math>= ₹ 8,000<br>Amount = P(1+ <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)<sup>3</sup><br>= 8000(<math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>)<sup>3</sup><br>= ₹ 9261<br>Compound interest = ₹ (9261 - 8000) = ₹ 1,261</p>",
                    solution_hi: "<p>83. (d) साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>दिए गए डेटा के अनुसार<br>स. ब्याज = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>= ₹1200<br>P = <math display=\"inline\"><mfrac><mrow><mn>1200</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math>= ₹ 8,000<br>A = P(1+&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)<sup>3</sup><br>= 8000(<math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math>)<sup>3</sup><br>= ₹ 9261<br>चक्रवृद्धि ब्याज = ₹ (9261 - 8000) = ₹ 1,261</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. What are the finger-like projections that increase the area of absorption of the small intestine called?</p>",
                    question_hi: "<p>84. छोटी आंत के अवशोषण क्षेत्र में बढ़े हुए अंगुलीनुमा प्रवर्ध क्या कहलाते हैं?</p>",
                    options_en: ["<p>Glands</p>", "<p>Sphincters</p>", 
                                "<p>Cilia</p>", "<p>Villi</p>"],
                    options_hi: ["<p>ग्रंथियां (Glands)</p>", "<p>अवरोधिनी (Sphincters)</p>",
                                "<p>पक्ष्माभ (Cilia)</p>", "<p>अंकुर (Villi)</p>"],
                    solution_en: "<p>84.(d) <strong>Villi</strong> increases the surface area of the small intestine allowing more nutrients from the lumen to be absorbed in the circulatory system. <strong>Gland</strong> is an organ that makes one or more substances, such as hormones, digestive juices, sweat, tears, saliva, or milk. <strong>Sphincter</strong> is a circular muscle, which often closes the various passageways of the body and opens them when necessary so that the functions of the body go on smoothly. <strong>Cilia </strong>are hair-like structures present on the surface of all mammalian cells and play a major role in locomotion.</p>",
                    solution_hi: "<p>84.(d) <strong>अंकुर (Villi)</strong>, छोटी आंत की सतह क्षेत्र को बढ़ाता है जिससे लुमेन से अधिक पोषक तत्वों को परिसंचरण तंत्र में अवशोषित किया जा सकता है। <strong>ग्रंथि </strong>एक ऐसा अंग है जो एक या एक से अधिक पदार्थ बनाता है, जैसे हार्मोन, पाचक रस, पसीना, आंसू, लार या दूध। <strong>अवरोधिनी</strong> (sphincter) वे वृत्ताकार पेशियाँ हैं जो शरीर के विभिन्न मार्गों को प्रायः बन्द रखतीं हैं और आवश्यक होने पर उसे खोल देतीं हैं ताकि शरीर के कार्य सुचारु रूप से चलते रहें। <strong>पक्ष्माभ</strong> (Cilia), सभी स्तनधारी कोशिकाओं की सतह पर मौजूद बाल जैसी संरचनाएं हैं जो गति में प्रमुख भूमिका निभाती हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340935.png\" alt=\"rId37\" width=\"263\" height=\"54\"></p>",
                    question_hi: "<p>85. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386340935.png\" alt=\"rId37\" width=\"263\" height=\"54\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341049.png\" alt=\"rId38\" width=\"86\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341156.png\" alt=\"rId39\" width=\"86\" height=\"84\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341276.png\" alt=\"rId40\" width=\"86\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341391.png\" alt=\"rId41\" width=\"85\" height=\"85\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341049.png\" alt=\"rId38\" width=\"86\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341156.png\" alt=\"rId39\" width=\"86\" height=\"84\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341276.png\" alt=\"rId40\" width=\"84\" height=\"84\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341391.png\" alt=\"rId41\" width=\"85\" height=\"85\"></p>"],
                    solution_en: "<p>85.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341276.png\" alt=\"rId40\" width=\"85\" height=\"85\"><br><strong>Logic: </strong>a new figure appears on top right corner at the alternate position. In the first figure, circle is the new figure, in third figure triangle and in the fifth, there will be rhombus.</p>",
                    solution_hi: "<p>85.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341276.png\" alt=\"rId40\" width=\"85\" height=\"85\"><br><strong>तर्क:</strong> एकान्तर स्थान पर शीर्ष दाएं कोने पर एक नई आकृति दिखाई देती है। पहली आकृति में वृत्त नई आकृति है, तीसरी आकृति में त्रिभुज और पाँचवीं में समचतुर्भुज होगा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. The average of five consecutive even numbers is M. If the next five even numbers are also included, the average of ten numbers will be:</p>",
                    question_hi: "<p>86. पाँच लगातार सम संख्याओं का औसत M है | यदि अगली पाँच सम संख्याओं को भी शामिल कर लिया जाए, तो दस संख्याओं का औसत कितना होगा ?</p>",
                    options_en: ["<p>M + 10</p>", "<p>11</p>", 
                                "<p>M + 5</p>", "<p>10</p>"],
                    options_hi: ["<p>M + 10</p>", "<p>11</p>",
                                "<p>M + 5</p>", "<p>10</p>"],
                    solution_en: "<p>86. (c) For given five consecutive even numbers, average = middle number<br>Thus, M = middle number<br>And their sum = 5 &times; M<br>We can now conclude that first 5 numbers are : M - 4, M - 2, M, M + 2, M + 4<br>And the next five numbers are M + 6, M + 8, M + 10, M + 12, M + 14<br>And their average = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>M</mi><mo>+</mo><mn>50</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>= M + 10<br>now , the average of all the 10 numbers =<math display=\"inline\"><mfrac><mrow><mi>M</mi><mo>+</mo><mi>M</mi><mo>+</mo><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>= M + 5</p>",
                    solution_hi: "<p>86. (c)दी गई पांच लगातार सम संख्याओं का औसत (average) हमेशा मध्य संख्या (middle number) के बराबर होता है।<br>मान लें कि मध्य संख्या M है।<br>इस प्रकार, इन पांच संख्याओं का योग (sum) होगा:<br>योग = 5 &times; M<br>पहली 5 संख्याएँ : M - 4, M - 2, M, M + 2, M + 4<br>और अगली 5 संख्याएँ M + 6, M + 8, M + 10, M + 12, M + 14<br>अब, औसत = <math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>M</mi><mo>+</mo><mn>50</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>= M + 10<br>अतः,सभी 10 संख्याओं का औसत =<math display=\"inline\"><mfrac><mrow><mi>M</mi><mo>+</mo><mi>M</mi><mo>+</mo><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = M + 5</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. Which of the following festivals is mainly celebrated in Vadakkunnathan temple of Kerala?</p>",
                    question_hi: "<p>87. निम्न में से कौन सा त्योहार मुख्य रूप से केरल के वाडक्कुम्नाथन मंदिर में मनाया जाता है?</p>",
                    options_en: ["<p>Thrissur Pooram</p>", "<p>Bastar Dussehra</p>", 
                                "<p>Kullu Dussehra</p>", "<p>Thiruvathira</p>"],
                    options_hi: ["<p>त्रिशूर पूरम</p>", "<p>बस्तर दशहरा</p>",
                                "<p>कुल्लू दशहरा</p>", "<p>तिरुवातिरै</p>"],
                    solution_en: "<p>87.(a) <strong>Thrissur Pooram</strong> is considered one of the greatest gatherings in Asia held in Thrissur, Kerala. Festivals: Kerala - Onam Festival, Boat Festival, Attukal Pongala, Vishu Festival, Makaravilakku, Theyyam etc. <strong>Bastar Dussehra </strong>- Chhattisgarh. <strong>Kullu Dussehra </strong>- Himachal Pradesh. <strong>Thiruvathira </strong>- Kerala and Tamil Nadu.</p>",
                    solution_hi: "<p>87.(a) <strong>त्रिशूर पूरम </strong>को केरल के त्रिशूर में आयोजित एशिया की सबसे बड़ी सभाओं में से एक माना जाता है। त्यौहार: केरल - ओणम महोत्सव, नाव महोत्सव, अटुकल पोंगाला, विशु महोत्सव, मकरविलक्कू, तेय्यम आदि। <strong>बस्तर दशहरा</strong> - छत्तीसगढ़। <strong>कुल्लू दशहरा</strong> - हिमाचल प्रदेश। <strong>तिरुवथिरा</strong> - केरल और तमिलनाडु।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. Two numbers are in the ratio 5:7. If the first number is 20, then the second number will be:</p>",
                    question_hi: "<p>88. दो संख्याएँ 5 : 7 के अनुपात में हैं | यदि पहली संख्या 20 है, तो दूसरी संख्या कितनी है ?</p>",
                    options_en: ["<p>18</p>", "<p>28</p>", 
                                "<p>8</p>", "<p>22</p>"],
                    options_hi: ["<p>18</p>", "<p>28</p>",
                                "<p>8</p>", "<p>22</p>"],
                    solution_en: "<p>88. (b) Ratio of two numbers is 5 : 7<br>First number = 5a<br>Second number = 7a<br>5a = 20</p>\n<p>&rArr; a = 4<br>Second number = 7 &times; 4 = 28</p>",
                    solution_hi: "<p>88. (b) दो संख्याओं का अनुपात 5 : 7 है।<br>माना , पहली संख्या = 5a और दूसरी संख्या 7a <br>अगर पहली संख्या , 5a = 20 <br>तो , a = 4 <br>अतः, दूसरी संख्या = 7 &times; 4 = 28</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. The sequence of folding a piece of paper and the manner in which the folded paper is cut is shown in the following figures. How would this paper look when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341868.png\" alt=\"rId42\" width=\"190\" height=\"98\"></p>",
                    question_hi: "<p>89. निम्नलिखित आकृतिओं में एक कागज के टुकड़े को मोड़ने के क्रम और मुड़े हुए कागज को काटने के तरीके को दर्शाया गया है। खोलने पर यह कागज कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341868.png\" alt=\"rId42\" width=\"190\" height=\"98\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341966.png\" alt=\"rId43\" width=\"67\" height=\"105\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342102.png\" alt=\"rId44\" width=\"86\" height=\"139\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342217.png\" alt=\"rId45\" width=\"87\" height=\"140\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342336.png\" alt=\"rId46\" width=\"84\" height=\"132\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386341966.png\" alt=\"rId43\" width=\"84\" height=\"131\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342102.png\" alt=\"rId44\" width=\"93\" height=\"150\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342217.png\" alt=\"rId45\" width=\"89\" height=\"143\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342336.png\" alt=\"rId46\" width=\"83\" height=\"131\"></p>"],
                    solution_en: "<p>89.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342217.png\" alt=\"rId45\" width=\"88\" height=\"141\"></p>",
                    solution_hi: "<p>89.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342217.png\" alt=\"rId45\" width=\"83\" height=\"134\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>90. Who founded the independent State of Bengal?</p>",
                    question_hi: "<p>90. स्वतंत्र बंगाल राज्य की स्थापना किसने की थी?</p>",
                    options_en: ["<p>Alivardi khan</p>", "<p>Murshid Quli Khan</p>", 
                                "<p>Sarafraz Khan</p>", "<p>Shuja-ud-Din</p>"],
                    options_hi: ["<p>अलीवर्दी खान</p>", "<p>मुर्शिद कुली खान</p>",
                                "<p>सरफराज खान</p>", "<p>शुजा-उद-दीन</p>"],
                    solution_en: "<p>90.(b) <strong>Murshid Quli Khan</strong> (Zamin Ali Quli) was born as Surya Narayan Mishra; the first Nawab of Bengal (1717 to 1727). He built the Katra Masjid mosque (1724) where he was buried after his death. <strong>Nawabs of Bengal</strong> - Alivardi khan- (1740 - 1756); Sarfaraz Khan - (1739-1740); Shuja-ud-Din - (1727-1739). <strong>Siraj-ud-Daulah</strong> (last Independent Nawab of Bengal) and his French allies were defeated in the Battle of Plassey (1757) against British East India Company (Robert Clive).</p>",
                    solution_hi: "<p>90.(b)<strong> मुर्शिद कुली खान</strong> (ज़मीन अली कुली) का जन्म सूर्य नारायण मिश्रा के रूप में हुआ था; बंगाल के पहले नवाब (1717 से 1727)। उन्होंने कटरा मस्जिद (1724) बनवाई जहां उनकी मृत्यु के बाद उन्हें दफनाया गया था। <strong>बंगाल के नवाब </strong>- अलीवर्दी खान- (1740 - 1756); सरफराज खान - (1739-1740); शुजा-उद-दीन - (1727-1739)।<strong> सिराज-उद-दौला</strong> (बंगाल के अंतिम स्वतंत्र नवाब) और उनके फ्रांसीसी सहयोगी ब्रिटिश ईस्ट इंडिया कंपनी (रॉबर्ट क्लाइव) के खिलाफ प्लासी की लड़ाई (1757) में हार गए थे।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p>91. The given bar graph shows the income and expenditure (in crores ₹)of a company over 5 years from 2014 to 2018. Study the Bar graph and answer the following:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342461.png\" alt=\"rId47\" width=\"259\" height=\"155\"> <br>In which of the following years is the ratio of income to expenditure minimum?</p>",
                    question_hi: "<p>91. दिया गया बार ग्राफ 2014 से 2018 तक 5 वर्षों में एक कंपनी की आय और व्यय (करोड़ रुपये में) दिखाता है। बार ग्राफ का अध्ययन करें और निम्नलिखित के उत्तर दें:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342461.png\" alt=\"rId47\" width=\"309\" height=\"185\"> <br>निम्नलिखित में से किस वर्ष में आय और व्यय का अनुपात न्यूनतम है?</p>",
                    options_en: ["<p>2017</p>", "<p>2018</p>", 
                                "<p>2014</p>", "<p>2016</p>"],
                    options_hi: ["<p>2017</p>", "<p>2018</p>",
                                "<p>2014</p>", "<p>2016</p>"],
                    solution_en: "<p>91.(b)<br>Ratio of income to expenditure in 2017 = <math display=\"inline\"><mfrac><mrow><mn>350</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math> = 1.17<br>Ratio of income to expenditure in 2018 = <math display=\"inline\"><mfrac><mrow><mn>350</mn></mrow><mrow><mn>325</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>13</mn></mfrac></math> = 1.08<br>Ratio of income to expenditure in 2014 = <math display=\"inline\"><mfrac><mrow><mn>225</mn></mrow><mrow><mn>175</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>7</mn></mfrac></math> = 1.29<br>Ratio of income to expenditure in 2016 = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>275</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>11</mn></mfrac></math> = 1.18<br>Clearly, we can see that the ratio of income to expenditure is minimum for 2018</p>",
                    solution_hi: "<p>91.(b)<br>2017 में आय का व्यय से अनुपात <br>= <math display=\"inline\"><mfrac><mrow><mn>350</mn></mrow><mrow><mn>300</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math> = 1.17<br>2018 में आय का व्यय से अनुपात <br>= <math display=\"inline\"><mfrac><mrow><mn>350</mn></mrow><mrow><mn>325</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>13</mn></mfrac></math> = 1.08<br>2014 में आय का व्यय से अनुपात <br>= <math display=\"inline\"><mfrac><mrow><mn>225</mn></mrow><mrow><mn>175</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>7</mn></mfrac></math> = 1.29<br>2016 में आय का व्यय से अनुपात <br>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>275</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>11</mn></mfrac></math> = 1.18<br>स्पष्ट रूप से, हम देख सकते हैं कि आय और व्यय का अनुपात 2018 के लिए न्यूनतम है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. Which of the following lines is NOT related to kabaddi?</p>",
                    question_hi: "<p>92. निम्नलिखित में से कौन सी लाइन कबड्डी से संबंधित नहीं है?</p>",
                    options_en: ["<p>Baulk line</p>", "<p>Attack line</p>", 
                                "<p>Bonus line</p>", "<p>End line</p>"],
                    options_hi: ["<p>बाउल्क लाइन</p>", "<p>आक्रमण लाइन</p>",
                                "<p>बोनस लाइन</p>", "<p>अंतिम लाइन</p>"],
                    solution_en: "<p>92.(b) <strong>Attack line. International Kabaddi Federation</strong> (Headquarters - Jaipur, India) : Highest governing body. <strong>Related terms</strong> : Cant (Recitation of word \'Kabaddi\'), Raider (Who enters the court of the opponent), Raid (Entering Opponent\'s court) etc.<strong> Tournaments -</strong> World Cup, Asian Games, Pro Kabaddi League etc. <strong>Players</strong> - Pardeep Narwal, Anup Kumar, Deepak Niwas Hooda, Pawan Sehrawat. </p>",
                    solution_hi: "<p>92.(b) <strong>आक्रमण लाइन I अंतर्राष्ट्रीय कबड्डी महासंघ</strong> (मुख्यालय - जयपुर, भारत): सर्वोच्च शासी निकाय। <strong>संबंधित शब्दावली </strong>: कान्त (कबड्डी शब्द का उच्चारण), रैडर (जो प्रतिद्वंद्वी के मैदान में प्रवेश करता है), रैड (प्रतिद्वंद्वी के मैदान में प्रवेश करना) आदि। <strong>टूर्नामेंट </strong>- विश्व कप, एशियाई खेल, प्रो कबड्डी लीग आदि। <strong>खिलाड़ी</strong> - परदीप नरवाल, अनुप कुमार, दीपक निवास हुड्डा, पवन सेहरावत।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342571.png\" alt=\"rId48\" width=\"103\" height=\"113\"></p>",
                    question_hi: "<p>93. नीचे दी गई आकृति में कितने त्रिभुज है ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342571.png\" alt=\"rId48\" width=\"103\" height=\"113\"></p>",
                    options_en: ["<p>9</p>", "<p>10</p>", 
                                "<p>11</p>", "<p>8</p>"],
                    options_hi: ["<p>9</p>", "<p>10</p>",
                                "<p>11</p>", "<p>8</p>"],
                    solution_en: "<p>93.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342706.png\" alt=\"rId49\" width=\"119\" height=\"123\"><br>There are a total 8 (ABC, DEF, GHI, JKL, MNO, MOP, MNP, NPQ) triangles in the given figure.</p>",
                    solution_hi: "<p>93.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728386342706.png\" alt=\"rId49\" width=\"119\" height=\"123\"><br>दिए गए चित्र में कुल 8 (ABC, DEF, GHI, JKL, MNO, MOP, MNP, NPQ) त्रिभुज हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. A sum of Rs. 18,000 becomes Rs. 21,780 after 2 years on compound interest compounded annually. What will be the compound interest (in Rs.) on the same sum for the same period if the rate of interest increases by 5%?</p>",
                    question_hi: "<p>94. 18,000 रुपये की राशि वार्षिक रूप से चक्रवृद्धि होने वाले चक्रवृद्धि ब्याज पर 2 साल बाद 21,780 रुपये हो जाता है। यदि ब्याज की दर में 5% की वृद्धि होती है, तो उसी राशि पर उसी अवधि के लिए चक्रवृद्धि ब्याज (रुपये में) कितना होगा?</p>",
                    options_en: ["<p>1,845</p>", "<p>4,670</p>", 
                                "<p>5,805</p>", "<p>5,500</p>"],
                    options_hi: ["<p>1,845</p>", "<p>4,670</p>",
                                "<p>5,805</p>", "<p>5,500</p>"],
                    solution_en: "<p>94.(c)<br>For finding rate for 2 years<br>We take square root of ratio of principal and amount<br><strong>Principal&nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;Amount</strong><br>&nbsp;18000&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 21780 (cancel out with 90)<br>&nbsp; &nbsp;200&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 242 (cancel out with 2)<br><math display=\"inline\"><msqrt><mn>100</mn></msqrt></math>&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>121</mn></msqrt></math> .. now take square root<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11<br>Rate of interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>-</mo><mn>10</mn></mrow><mn>10</mn></mfrac></math> &times; 100 = 10%<br>Now according to the question new rate = 10% + 5% = 15%<br>15% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br><strong>Principal&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Amount</strong><br>&nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 23<br>&nbsp; &nbsp;<span style=\"text-decoration: underline;\"> &nbsp;20&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 23&nbsp;&nbsp;</span><br>&nbsp; <span style=\"text-decoration: underline;\">&nbsp; 400&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;529&nbsp;&nbsp;</span><br>CI = amount &ndash; principal = 529 &ndash; 400 = 129 units<br>400 units = 18000<br>1 unit = 45<br>CI = 129 units = 129 &times; 45 = 5805 Rs</p>",
                    solution_hi: "<p>94.(c)<br>2 वर्षों के लिए दर ज्ञात करने के लिए<br>हम मूलधन और राशि के अनुपात का वर्गमूल लेते हैं<br><strong>मूलधन&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;राशि</strong><br>18000&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 21780 (90 के साथ रद्द करने पर)<br>&nbsp; 200&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 242 (2 के साथ रद्द करने पर )<br><math display=\"inline\"><msqrt><mn>100</mn></msqrt></math>&nbsp; &nbsp; : &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>121</mn></msqrt></math> .. अब वर्गमूल लें<br>&nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 11<br>ब्याज दर = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>-</mo><mn>10</mn></mrow><mn>10</mn></mfrac></math> &times; 100 = 10%<br>अब प्रश्न के अनुसार नई दर = 10% + 5% = 15%<br>15% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>&nbsp;मूलधन&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;राशि<br>&nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 23<br>&nbsp; &nbsp;<span style=\"text-decoration: underline;\"> &nbsp;20&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 23&nbsp;&nbsp;</span><br>&nbsp; <span style=\"text-decoration: underline;\">&nbsp; 400&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;529&nbsp;&nbsp;</span><br>चक्रवृद्धि ब्याज = राशि &ndash; मूलधन = 529 &ndash; 400 = 129 इकाई<br>400 इकाई = 18000<br>1 इकाई = 45<br>चक्रवृद्धि ब्याज = 129 इकाई = 129 &times; 45 = 5805 रुपये</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. Which of the following gases is released when zinc oxide reacts with carbon?</p>",
                    question_hi: "<p>95. जिंक ऑक्साइड की कार्बन के साथ अभिक्रिया के फलस्वरूप इनमें से कौन सी गैस मुक्त होती है ?</p>",
                    options_en: ["<p>Carbon dioxide</p>", "<p>Methane</p>", 
                                "<p>Carbon monoxide</p>", "<p>Ozone</p>"],
                    options_hi: ["<p>कार्बन डाईऑक्साइड</p>", "<p>मेथेन</p>",
                                "<p>कार्बन मोनोऑक्साइड</p>", "<p>ओज़ोन</p>"],
                    solution_en: "<p>95.(c) <strong>Carbon monoxide.</strong> ZnO (Zinc Oxide) + C &rarr; Zn + CO <carbon monoxide=\"\"> (redox reaction). Zinc oxide is the oxidizing agent as it oxidizes carbon to carbon monoxide. Carbon is a reducing agent as it reduces zinc oxide to zinc. Zinc - Atomic number (30), Atomic mass (65.38), Discovered by Andreas Marggraf. </carbon><strong id=\"docs-internal-guid-81af4b72-7fff-3d82-1c64-3f1525fde0dc\">Properties</strong><carbon monoxide=\"\">&nbsp;- density (7.13 g/cm&sup3;), melting point (420 &deg;C), boiling point of (906 &deg;C), high heat capacity and heat conductivity. </carbon></p>",
                    solution_hi: "<p>95.(c) <strong>कार्बन मोनोआक्साइड </strong>। ZnO + C &rarr; Zn + CO (रेडॉक्स अभिक्रिया)। जिंक ऑक्साइड एक आक्सीकारक है क्योंकि यह कार्बन को कार्बन मोनोऑक्साइड में आक्सीकृत करता है। कार्बन एक अपचायक है क्योंकि यह जिंक ऑक्साइड को जिंक में अपचयित करता है। जस्ता - परमाणु संख्या (30), परमाणु द्रव्यमान (65.38), एंड्रियास मार्गग्राफ द्वारा खोजा गया।<strong> गुण - </strong>घनत्व (7.13 g/cm&sup3;), गलनांक (420 &deg;C), क्वथनांक (906 &deg;C), उच्च ताप क्षमता और ऊष्मा चालकता।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. If X &ge; Y = P &gt; Q &le; R &gt; Z,&nbsp;Which of the following can be concluded?</p>",
                    question_hi: "<p>96. यदि X &ge; Y = P &gt; Q &le; R &gt; Z, निम्नलिखित में से क्या निष्कर्ष निकाला जा सकता है?</p>",
                    options_en: ["<p>R &ge; Q &ge; P</p>", "<p>P &le; X &gt; Q</p>", 
                                "<p>Z &lt; Q &le; R</p>", "<p>Q &ge; X &le; Y</p>"],
                    options_hi: ["<p>R &ge; Q &ge; P</p>", "<p>P &le; X &gt; Q</p>",
                                "<p>Z &lt; Q &le; R</p>", "<p>Q &ge; X &le; Y</p>"],
                    solution_en: "<p>96.(b) We conclude ,P &le; X &gt; Q<br>Where , P = Y , P &gt; Q</p>",
                    solution_hi: "<p>96.(b) हम निष्कर्ष निकालते हैं, P &le; X &gt; Q<br>जहाँ , P = Y , P &gt; Q</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p><strong id=\"docs-internal-guid-9035f16b-7fff-a631-333c-057d9f6d00a9\">97. </strong>A takes 3 hours more than B to walk &ldquo;d&rdquo; km. If A doubles his speed, then he can make it in 1 hour less than B. How much time (in hours) does A require to walk &ldquo;d&rdquo; km?<strong id=\"docs-internal-guid-9035f16b-7fff-a631-333c-057d9f6d00a9\"><br></strong></p>",
                    question_hi: "<p>97. A, \"d\" किमी चलने में B से 3 घंटे अधिक लेता है। यदि A अपनी गति को दोगुना कर देता है, तो वह इस दुरी को B से 1 घंटे कम समय में तय कर सकता है। A को \"d\" km चलने में कितना समय (घंटों में) चाहिए?</p>",
                    options_en: ["<p>5</p>", "<p>9</p>", 
                                "<p>8</p>", "<p>4</p>"],
                    options_hi: ["<p>5</p>", "<p>9</p>",
                                "<p>8</p>", "<p>4</p>"],
                    solution_en: "<p>97.(c)<br>Initially A takes 3 hour more then B <br>If A double its speed then he take 1 hour less then B <br>difference between time = 3 hour + 1 hour = 4 hour<br><strong>Concept: If distance is fixed then time and speed are inversely proportional</strong> <br>For A <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>&nbsp; initial&nbsp; :&nbsp; &nbsp;final</strong><br>Speed&nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;2<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;1<br>Difference of time = 1 unit = 4 hour<br>So initial time = 2 units = 2 &times; 4 hours = 8 hours</p>",
                    solution_hi: "<p>97.(c)<br>प्रारंभ में A, B से 3 घंटे अधिक लेता है।<br>यदि A अपनी गति को दोगुना कर देता है तो वह B से 1 घंटा कम लेता है।<br>समय के बीच का अंतर = 3 घंटा + 1 घंटा = 4 घंटा<br><strong>सिद्धांत : यदि दूरी निश्चित है तो समय और गति व्युत्क्रमानुपाती होते हैं।</strong><br>A के लिए<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <strong>प्रारंभिक&nbsp; &nbsp;:&nbsp; &nbsp;अंतिम</strong><br>गति&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 2<br>समय&nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;1<br>समय का अंतर = 1 इकाई = 4 घंटा<br>अतः प्रारंभिक समय = 2 इकाई = 2 &times; 4 घंटे = 8 घंटे</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. Where is Birsa Munda International Hockey Stadium located?</p>",
                    question_hi: "<p>98. बिरसा मुंडा अंतरराष्ट्रीय हॉकी स्टेडियम कहां स्थित है?</p>",
                    options_en: ["<p>Ranchi</p>", "<p>Rourkela</p>", 
                                "<p>Bhubaneswar</p>", "<p>Jamshedpur</p>"],
                    options_hi: ["<p>रांची</p>", "<p>राउरकेला</p>",
                                "<p>भुवनेश्वर</p>", "<p>जमशेदपुर</p>"],
                    solution_en: "<p>98. (b) <strong>Rourkela.</strong><br>Birsa Munda International Hockey Stadium is located in Rourkela, making it India&rsquo;s largest hockey stadium. Birsa Munda was a tribal leader who led the Munda Rebellion against British rule and the exploitation of his people in Jharkhand.<br><strong>Stadiums in India Named After Famous Personalities:</strong><br>Indira Gandhi Stadium (New Delhi)<br>Rajiv Gandhi International Cricket Stadium (Hyderabad)<br>Narendra Modi Stadium (Ahmedabad)<br>Lal Bahadur Shastri Stadium (Hyderabad)<br>Arun Jaitley Stadium (New Delhi)<br>Major Dhyan Chand Hockey Stadium (Lucknow)</p>",
                    solution_hi: "<p>98. (b) <strong>राउरकेला।</strong><br>बिरसा मुंडा अंतरराष्ट्रीय हॉकी स्टेडियम राउरकेला में स्थित है, जो भारत का सबसे बड़ा हॉकी स्टेडियम है। बिरसा मुंडा एक आदिवासी नेता थे जिन्होंने झारखंड में ब्रिटिश शासन और अपने लोगों के शोषण के खिलाफ मुंडा विद्रोह का नेतृत्व किया था।<br><strong>भारत में प्रसिद्ध हस्तियों के नाम पर बने स्टेडियम:</strong><br>इंदिरा गांधी स्टेडियम (नई दिल्ली)<br>राजीव गांधी अंतर्राष्ट्रीय क्रिकेट स्टेडियम (हैदराबाद)<br>नरेंद्र मोदी स्टेडियम (अहमदाबाद)<br>लाल बहादुर शास्त्री स्टेडियम (हैदराबाद)<br>अरुण जेटली स्टेडियम (नई दिल्ली)<br>मेजर ध्यानचंद हॉकी स्टेडियम (लखनऊ)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p><strong id=\"docs-internal-guid-011afe91-7fff-d9e0-eb9f-1e7a94d8c5fa\">99.&nbsp;</strong>Of the three numbers, second is one-third of first and is also three-fourth of the third number. If the average of three number is 112, then what is the smallest number?<strong id=\"docs-internal-guid-011afe91-7fff-d9e0-eb9f-1e7a94d8c5fa\"><br></strong></p>",
                    question_hi: "<p>99. तीन संख्याओं में से दूसरी पहली का एक-तिहाई है और तीसरी संख्या का तीन-चौथाई है। यदि तीन संख्याओं का औसत 112 है, तो सबसे छोटी संख्या क्या क्या होनी चाहिए ?</p>",
                    options_en: ["<p>63</p>", "<p>45</p>", 
                                "<p>84</p>", "<p>189</p>"],
                    options_hi: ["<p>63</p>", "<p>45</p>",
                                "<p>84</p>", "<p>189</p>"],
                    solution_en: "<p>99.(a)<br>Let three numbers are a, b and c<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>b</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 112 , a + b + c = 336<br>According to the question<br>b = <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> , c = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>b</mi></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>a</mi></mrow><mn>9</mn></mfrac></math><br>a + b + c = a + <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>a</mi></mrow><mn>9</mn></mfrac></math> = 336<br>a = 189<br>b = <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mn>3</mn></mfrac></math> = 63<br>c = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>a</mi></mrow><mrow><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>189</mn></mrow><mn>9</mn></mfrac></math> = 84<br>Smallest number is b = 63</p>",
                    solution_hi: "<p>99.(a)<br>माना कि तीन संख्याएँ a, b और c हैं।<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>b</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 112 , a + b + c = 336<br>प्रश्न के अनुसार,<br>b = <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> , c = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>b</mi></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>a</mi></mrow><mn>9</mn></mfrac></math><br>a + b + c = a + <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>a</mi></mrow><mn>9</mn></mfrac></math> = 336<br>a = 189<br>b = <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>189</mn><mn>3</mn></mfrac></math> = 63<br>c = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>a</mi></mrow><mrow><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>189</mn></mrow><mn>9</mn></mfrac></math> = 84<br>सबसे छोटी संख्या है, b = 63</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. Which of the following elements have similar properties with Eka-silicon, Eka-boron and Eka- aluminium, respectively?</p>",
                    question_hi: "<p>100. निम्नलिखित में से किस तत्व के गुण क्रमशः एका-सिलिकॉन, एका-बोरॉन और एका-एल्युमीनियम के समान हैं?</p>",
                    options_en: ["<p>Scandium, Gallium, and Germanium</p>", "<p>Germanium, Scandium, and Gallium</p>", 
                                "<p>Carbon, Scandium, and Indium</p>", "<p>Germanium, Carbon, and Gallium</p>"],
                    options_hi: ["<p>स्कैंडियम, गैलियम और जर्मेनियम</p>", "<p>जर्मेनियम, स्कैंडियम और गैलियम</p>",
                                "<p>कार्बन, स्कैंडियम और इंडियम</p>", "<p>जर्मेनियम, कार्बन और गैलियम</p>"],
                    solution_en: "<p>100.(b) <strong>Germanium, Scandium, and Gallium.</strong> Mendeleev named unnamed elements as EKA- Boron EKA- Aluminium and EKA Silicon which were later replaced as Scandium, Gallium, and germanium respectively. Scandium (Sc, atomic number - 21) is mined from one of the rare minerals from Scandinavia. Gallium (Ga, atomic number - 31) is a blue-gray metal having orthorhombic crystalline structure. Germanium (Ge, atomic number 32) is a hard, grey-white, lustrous and brittle metalloid acts in properties between metals and nonmetals.</p>",
                    solution_hi: "<p>100.(b) <strong>जर्मेनियम, स्कैंडियम और गैलियम।</strong> मेंडेलीव ने अज्ञात तत्वों को एका- बोरान, एका- एल्युमिनियम और एका सिलिकॉन नाम दिया, जिन्हें बाद में क्रमशः स्कैंडियम, गैलियम और जर्मेनियम के रूप में बदल दिया गया। स्कैंडियम (Sc, परमाणु संख्या - 21) स्कैंडिनेविया के दुर्लभ खनिजों में से एक से खनन किया जाता है। गैलियम (Ga, परमाणु संख्या - 31) एक नीला-ग्रे धातु है जिसमें समचतुर्भुजी क्रिस्टलीय संरचना होती है। जर्मेनियम (Ge, परमाणु संख्या 32) धातुओं और अधातुओं के बीच गुणों में एक कठोर, ग्रे-सफेद, चमकदार और भंगुर उपधातु है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>