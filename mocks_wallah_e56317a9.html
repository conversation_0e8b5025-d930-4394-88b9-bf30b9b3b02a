<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which is the first nuclear reactor made in India ?</p>",
                    question_hi: "<p>1. भारत में बना पहला परमाणु रिएक्टर कौन सा है ?</p>",
                    options_en: ["<p>Apsara</p>", "<p>CIRUS</p>", 
                                "<p>Dhruva</p>", "<p>KAMINI</p>"],
                    options_hi: ["<p>अप्सरा</p>", "<p>साइरस</p>",
                                "<p>ध्रुव</p>", "<p>कामिनी</p>"],
                    solution_en: "<p>1.(a) Apsara is the oldest of India&rsquo;s research reactors. The reactor was designed by the Bhabha Atomic Research Centre (BARC).</p>",
                    solution_hi: "<p>1.(a) अप्सरा भारत के अनुसंधान रिएक्टरों में सबसे पुराना है। रिएक्टर को भाभा परमाणु अनुसंधान केंद्र (BARC) द्वारा डिजाइन किया गया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Invertebrates do NOT include</p>",
                    question_hi: "<p>2. अकशेरूकीय में_____शामिल नहीं हैं।</p>",
                    options_en: ["<p>molluscs</p>", "<p>arachnids</p>", 
                                "<p>reptiles</p>", "<p>insects</p>"],
                    options_hi: ["<p>सीप</p>", "<p>मकड़ी</p>",
                                "<p>सरीसृप</p>", "<p>कीड़े</p>"],
                    solution_en: "<p>2.(c) Invertebrates means an animal lacking a backbone, such as an arthropod, mollusc, annelid, coelenterate, arachnids, insects etc. Reptiles have backbones, so they are not Invertebrates.</p>",
                    solution_hi: "<p>2.(c) अकशेरुकी का अर्थ है एक ऐसा जानवर जिसमें रीढ़ की हड्डी नहीं होती है, जैसे कि आर्थ्रोपोड, मोलस्क, एनेलिड, कोएलेंटरेट, अरचिन्ड, कीड़े आदि। सरीसृपों में रीढ़ की हड्डी होती है, इसलिए वे अकशेरुकी नहीं हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which of the following is composed of nerve fibres that mediate reflex actions and that transmit impulses to and from the brain ?</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन तंत्रिका तंतुओं से बना है जो प्रतिवर्त क्रियाओं में मध्यस्थता करता है और जो मस्तिष्क से आवेगों को संचारित करता है ?</p>",
                    options_en: ["<p>Heart</p>", "<p>Rib cage</p>", 
                                "<p>Spinal cord</p>", "<p>Muscles</p>"],
                    options_hi: ["<p>हृदय</p>", "<p>पंजर</p>",
                                "<p>मेरुदण्ड</p>", "<p>मांसपेशी</p>"],
                    solution_en: "<p>3.(c) Spinal cord is composed of nerve fibres that mediate reflex actions and that transmit impulses to and from the brain. Spinal cord is a long, thin, tubular structure made up of nervous tissue, which extends from the medulla oblongata in the brainstem to the lumbar region of the vertebral column.</p>",
                    solution_hi: "<p>3.(c) रीढ़ की हड्डी तंत्रिका तंतुओं से बनी होती है जो प्रतिवर्त क्रियाओं में मध्यस्थता करती है और जो मस्तिष्क से आवेगों को संचारित करती है। रीढ़ की हड्डी एक लंबी, पतली, ट्यूबलर संरचना होती है जो तंत्रिका ऊतक से बनी होती है, जो ब्रेनस्टेम में मेडुला ऑबोंगटा से कशेरुक स्तंभ के काठ क्षेत्र तक फैली हुई है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. In the context of computers, tracker balls is a/an ______device.</p>",
                    question_hi: "<p>4. कंप्यूटर के संदर्भ में, ट्रैकर बॉल एक ______उपकरण है।</p>",
                    options_en: ["<p>storage</p>", "<p>processing</p>", 
                                "<p>input</p>", "<p>output</p>"],
                    options_hi: ["<p>स्टोरेज</p>", "<p>प्रोसेसिंग</p>",
                                "<p>इनपुट</p>", "<p>आउटपुट</p>"],
                    solution_en: "<p>4.(c) In the context of computers, tracker balls is an input device. Some other examples of input devices are keyboard, mouse, joystick, light pen, scanner, graphic tablet, microphone, magnetic inc card reader, optical character reader, bar code reader etc.</p>",
                    solution_hi: "<p>4.(c) कंप्यूटर के संदर्भ में, ट्रैकर बॉल एक इनपुट डिवाइस है। इनपुट डिवाइस के कुछ अन्य उदाहरण कीबोर्ड, माउस, जॉय स्टिक, लाइट पेन, स्कैनर, ग्राफिक टैबलेट, माइक्रोफोन, मैग्नेटिक इंक कार्ड रीडर, ऑप्टिकल कैरेक्टर रीडर, बार कोड रीडर आदि हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. The Nipah virus outbreak in 2018 took place in</p>",
                    question_hi: "<p>5. 2018 में निपाह वायरस का प्रकोप कहाँ हुआ था ?</p>",
                    options_en: ["<p>Uttar Pradesh</p>", "<p>Karnataka</p>", 
                                "<p>Tamil Nadu</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>उत्तर प्रदेश</p>", "<p>कर्नाटक</p>",
                                "<p>तमिलनाडु</p>", "<p>केरल</p>"],
                    solution_en: "<p>5.(d) The Nipah virus outbreak in 2018 took place in the Indian state of Kerala. The outbreak was localized in Kozhikode and Malappuram districts of Kerala, and declared to be over on june 10, 2018.</p>",
                    solution_hi: "<p>5.(d) 2018 में निपाह वायरस का प्रकोप भारतीय राज्य केरल में हुआ था। प्रकोप केरल के कोझीकोड और मलप्पुरम जिलों में स्थानीयकृत था, और 10 जून, 2018 को समाप्त होने की घोषणा की गई थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which state is the largest producer of gold in India ?</p>",
                    question_hi: "<p>6. भारत में सोने का सबसे बड़ा उत्पादक राज्य कौन सा है ?</p>",
                    options_en: ["<p>Telangana</p>", "<p>Karnataka</p>", 
                                "<p>Chhattisgarh</p>", "<p>Jharkhand</p>"],
                    options_hi: ["<p>तेलंगाना</p>", "<p>कर्नाटक</p>",
                                "<p>छत्तीसगढ</p>", "<p>झारखंड</p>"],
                    solution_en: "<p>6.(b) Karnataka is the largest producer of gold in India. The gold mine is located in Hutti in Raichur District in Karnataka.</p>",
                    solution_hi: "<p>6.(b) कर्नाटक भारत में सोने का सबसे बड़ा उत्पादक है। कर्नाटक में सोने की खदान रायचूर जिले के हुट्टी में स्थित है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following is classified under kingdom Animalia ?</p>",
                    question_hi: "<p>7. निम्नलिखित में से किसे एनीमेलिया प्रजाति के अंतर्गत वर्गीकृत किया गया है ?</p>",
                    options_en: ["<p>Protozoa</p>", "<p>Metazoa</p>", 
                                "<p>Choanozoa</p>", "<p>Pipiens</p>"],
                    options_hi: ["<p>प्रोटोजोआ</p>", "<p>मेटाज़ोआ</p>",
                                "<p>चोआनोज़ोआ</p>", "<p>पिपियन्स</p>"],
                    solution_en: "<p>7.(b) Metazoa is classified under the kingdom Animalia. This kingdom does not contain prokaryotes or protists.</p>",
                    solution_hi: "<p>7.(b) मेटाजोआ को किंगडम एनीमेलिया के अंतर्गत वर्गीकृत किया गया है। इस किंगडम में प्रोकैरियोट्स या प्रोटिस्ट नहीं हैं |</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. The Bering Strait connects the",
                    question_hi: "<p>8. बेरिंग जलडमरूमध्य ______को जोड़ता है।</p>",
                    options_en: [" Mediterranean Sea and Atlantic Ocean ", " Indian Ocean and Java Sea ", 
                                " Atlantic Ocean and Gulf of Hudson ", " Arctic ocean and Pacific Ocean "],
                    options_hi: [" भूमध्य सागर और अटलांटिक महासागर", " हिंद महासागर और जावा सागर",
                                " अटलांटिक महासागर और हडसन की खाड़ी", " आर्कटिक महासागर और प्रशांत महासागर"],
                    solution_en: "8.(d) The Bering Strait connects the Arctic ocean and Pacific Ocean. It separates the Chukchi Peninsula of the Russian Far east from the Seward Peninsula of Alaska.",
                    solution_hi: "8.(d) बेरिंग जलडमरूमध्य आर्कटिक महासागर और प्रशांत महासागर को जोड़ता है। यह रूसी सुदूर पूर्व के चुक्की प्रायद्वीप को अलास्का के सीवार्ड प्रायद्वीप से अलग करता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. One Kilobyte is equal to ______ bytes.</p>",
                    question_hi: "<p>9. एक किलोबाइट ______बाइट के बराबर होता है।</p>",
                    options_en: ["<p>256</p>", "<p>1024</p>", 
                                "<p>2048</p>", "<p>512</p>"],
                    options_hi: ["<p>256</p>", "<p>1024</p>",
                                "<p>2048</p>", "<p>512</p>"],
                    solution_en: "<p>9.(b) One Kilobyte is equal to 1024 bytes.<br>1 byte = 8 bits<br>1 kilobyte = 1024 bytes<br>1 megabyte = 1024 kilobyte<br>1 gigabyte = 1024 megabyte</p>",
                    solution_hi: "<p>9.(b) एक किलोबाइट = 1024 बाइट<br>1 बाइट = 8 बिट<br>1 किलोबाइट = 1024 बाइट<br>1 मेगाबाइट = 1024 किलोबाइट<br>1 गीगाबाइट = 1024 मेगाबाइट</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following is the administrative capital of South Africa ?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन सी दक्षिण अफ्रीका की प्रशासनिक राजधानी है ?</p>",
                    options_en: ["<p>Cape Town</p>", "<p>Bloemfontein</p>", 
                                "<p>Pretoria</p>", "<p>Durban</p>"],
                    options_hi: ["<p>केप टाउन</p>", "<p>ब्लूमफ़ोन्टीन</p>",
                                "<p>प्रिटोरिया</p>", "<p>डरबन</p>"],
                    solution_en: "<p>10.(c) The administrative capital of South Africa is Pretoria. It is one of South Africa&rsquo;s three capital cities, serving as the seat of the executive branch of government, and as the host of all foreign embassies to South Africa.</p>",
                    solution_hi: "<p>10.(c) दक्षिण अफ्रीका की प्रशासनिक राजधानी प्रिटोरिया है। यह दक्षिण अफ्रीका के तीन राजधानी शहरों में से एक है, जो सरकार की कार्यकारी शाखा की सीट के रूप में और दक्षिण अफ्रीका में सभी विदेशी दूतावासों के मेजबान के रूप में कार्यरत है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following is used in plastics ?</p>",
                    question_hi: "<p>11. निम्नलिखित में से किसका उपयोग प्लास्टिक में किया जाता है ?</p>",
                    options_en: ["<p>Butane</p>", "<p>Ethylene</p>", 
                                "<p>Krypton</p>", "<p>Ammonia</p>"],
                    options_hi: ["<p>ब्यूटेन</p>", "<p>ईथीलीन</p>",
                                "<p>क्रिप्टोन</p>", "<p>अमोनिया</p>"],
                    solution_en: "<p>11.(b) Ethylene is used in plastic making. Polyethene and Polypropylene are two of the major plastic types and these are nothing but polymers of ethylene.</p>",
                    solution_hi: "<p>11.(b) एथिलीन का उपयोग प्लास्टिक बनाने में किया जाता है। पॉलीथीन और पॉलीप्रोपाइलीन दो प्रमुख प्लास्टिक प्रकार हैं और ये एथिलीन के पॉलिमर ही हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Who founded the &lsquo;Slave Dynasty&rsquo; ?</p>",
                    question_hi: "<p>12. गुलाम वंश\' की स्थापना किसने की थी ?</p>",
                    options_en: ["<p>Nasir-up-din Mahmud</p>", "<p>Razia Sultan</p>", 
                                "<p>Qutb-ud-din Aibak</p>", "<p>Ghiyas-ud-din Balban</p>"],
                    options_hi: ["<p>नसीर-उद-दीन महमूद</p>", "<p>रजिया सुल्तान</p>",
                                "<p>कुतुबुद्दीन ऐबक</p>", "<p>गयासुद्दीन बलबन</p>"],
                    solution_en: "<p>12.(c) Qutub-ud-din Aibak founded the Slave Dynasty or Mamluk dynasty and lasted from 1206 to 1290. It was the first of the dynasties to rule as the Delhi Sultanate.</p>",
                    solution_hi: "<p>12.(c) कुतुब-उद-दीन ऐबक ने गुलाम वंश या मामलुक वंश की स्थापना की जो 1206 से 1290 तक चला। यह दिल्ली सल्तनत के रूप में शासन करने वाला पहला राजवंश था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Who is the author of the book &lsquo;Republic&rsquo; ?</p>",
                    question_hi: "<p>13. \'रिपब्लिक\' पुस्तक के लेखक कौन हैं ?</p>",
                    options_en: ["<p>TS Eliot</p>", "<p>Plato</p>", 
                                "<p>John Ruskin</p>", "<p>Leo Tolstoy</p>"],
                    options_hi: ["<p>टी.एस. एलियट</p>", "<p>प्लेटो</p>",
                                "<p>जॉन रस्किन</p>", "<p>लियो टॉल्स्टॉय</p>"],
                    solution_en: "<p>13.(b) The book &lsquo;Republic&rsquo; was authored by Plato around 375 BC. It is concerned with justice, law and order, proven to be one of the world&rsquo;s most influential works of philosophy and political theory.</p>",
                    solution_hi: "<p>13.(b) \'रिपब्लिक\' पुस्तक लगभग 375 ईसा पूर्व प्लेटो द्वारा लिखी गई थी। यह न्याय, कानून और व्यवस्था से संबंधित है, जो दर्शन और राजनीतिक सिद्धांत के दुनिया के सबसे प्रभावशाली कार्यों में से एक साबित हुआ है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. The 2022 Commonwealth Games are scheduled to be held in</p>",
                    question_hi: "<p>14. 2022 के राष्ट्रमंडल खेल कहाँ आयोजित होंगे ?</p>",
                    options_en: ["<p>Perth</p>", "<p>Edinburg</p>", 
                                "<p>Birmingham</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>पर्थ</p>", "<p>एडिनबर्ग</p>",
                                "<p>बर्मिंघम</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>14.(c) The 2022 Commonwealth Games are scheduled to be held in Birmingham, England. It is the XXII commonwealth game.</p>",
                    solution_hi: "<p>14.(c) 2022 राष्ट्रमंडल खेलों का आयोजन इंग्लैंड के बर्मिंघम में होना है। यह XXII वां राष्ट्रमंडल खेल है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. What is the full form of DHCP in a networking system ?</p>",
                    question_hi: "<p>15. नेटवर्किंग सिस्टम में DHCP का पूर्ण रूप क्या है ?</p>",
                    options_en: ["<p>Dynamic Host COnfiguration Protocol</p>", "<p>Data Host Control Panel</p>", 
                                "<p>Dynamic Host COntrol Point</p>", "<p>Display House Control Protocol</p>"],
                    options_hi: ["<p>Dynamic Host COnfiguration Protocol</p>", "<p>Data Host Control Panel</p>",
                                "<p>Dynamic Host COntrol Point</p>", "<p>Display House Control Protocol</p>"],
                    solution_en: "<p>15.(a) The full form of DHCP in a networking system is Dynamic Host Configuration Protocol. It is a network management protocol used on internet protocol networks.</p>",
                    solution_hi: "<p>15.(a) नेटवर्किंग सिस्टम में DHCP का पूर्ण रूप डायनामिक होस्ट कॉन्फ़िगरेशन प्रोटोकॉल है। यह इंटरनेट प्रोटोकॉल नेटवर्क पर उपयोग किया जाने वाला एक नेटवर्क प्रबंधन प्रोटोकॉल है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. India&rsquo;s longest road-cum-rail bridge, connecting Assam and Arunachal Pradesh, is called the</p>",
                    question_hi: "<p>16. असम और अरुणाचल प्रदेश को जोड़ने वाला भारत का सबसे लंबा सड़क-रेल पुल कौन सा है ?</p>",
                    options_en: ["<p>Godavari Bridge</p>", "<p>Pamban Bridge</p>", 
                                "<p>Howrah Bridge</p>", "<p>Bogibeel Bridge</p>"],
                    options_hi: ["<p>गोदावरी पुल</p>", "<p>पम्बन पुल</p>",
                                "<p>हावड़ा पुल</p>", "<p>बोगीबील पुल</p>"],
                    solution_en: "<p>16.(d) India&rsquo;s longest road-cum-rail bridge, connecting Assam and Arunachal Pradesh, is called the Bogibeel Bridge on the Brahmaputra river in Assam between the district of Dhemaji and Dibrugarh.</p>",
                    solution_hi: "<p>16.(d) असम और अरुणाचल प्रदेश को जोड़ने वाला भारत का सबसे लंबा सड़क-सह-रेल पुल, धेमाजी और डिब्रूगढ़ जिले के बीच असम में ब्रह्मपुत्र नदी पर स्थित है जो बोगीबील ब्रिज कहलाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which of the following welfare schemes&rsquo; achievements have been recognised by the Guiness World Records ?</p>",
                    question_hi: "<p>17. निम्नलिखित में से किस कल्याणकारी योजना की उपलब्धियों को गिनीज वर्ल्ड रिकॉर्ड द्वारा मान्यता दी गई है ?</p>",
                    options_en: ["<p>Pradhan Mantri Jan Dhan Yojana</p>", "<p>Pradhan Mantri Suraksha Bima Yojana</p>", 
                                "<p>Pradhan Mantri Kaushal Vikas Yojana</p>", "<p>Pradhan Mantri Krishi Sinchai Yojana</p>"],
                    options_hi: ["<p>प्रधानमंत्री जन धन योजना</p>", "<p>प्रधानमंत्री सुरक्षा बीमा योजना</p>",
                                "<p>प्रधानमंत्री कौशल विकास योजना</p>", "<p>प्रधानमंत्री कृषि सिंचाई योजना</p>"],
                    solution_en: "<p>17.(a) Pradhan Mantri Jan Dhan Yojana welfare scheme&rsquo;s achievements have been recognised by the Guinness World Records. It is the financial inclusion programme of the government of India, that aims to expand affordable access to financial services such as bank accounts, remittances, credit, insurance and pension.</p>",
                    solution_hi: "<p>17.(a) प्रधान मंत्री जन धन योजना की कल्याणकारी उपलब्धियों को गिनीज वर्ल्ड रिकॉर्ड्स द्वारा मान्यता दी गई है। यह भारत सरकार का वित्तीय समावेशन कार्यक्रम है, जिसका उद्देश्य बैंक खातों, प्रेषण, क्रेडिट, बीमा और पेंशन जैसी वित्तीय सेवाओं तक सस्ती पहुंच का विस्तार करना है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which of the following is situated in Jammu and Kashmir ?</p>",
                    question_hi: "<p>18. निम्नलिखित में से कौन जम्मू और कश्मीर में स्थित है ?</p>",
                    options_en: ["<p>Dachigam National Park</p>", "<p>Pakhal Wildlife Sanctuary</p>", 
                                "<p>Balpakram National Park</p>", "<p>Jaldapara National Park</p>"],
                    options_hi: ["<p>दाचीगाम राष्ट्रीय उद्यान</p>", "<p>पाखल वन्यजीव अभयारण्य</p>",
                                "<p>बलपकराम राष्ट्रीय उद्यान</p>", "<p>जलदापारा राष्ट्रीय उद्यान</p>"],
                    solution_en: "<p>18.(a) Dachigam National Park is located 22 km from Srinagar, in Jammu and Kashmir, which literally stands for &ldquo;ten villages&rdquo;.</p>",
                    solution_hi: "<p>18.(a) दाचीगाम राष्ट्रीय उद्यान जम्मू और कश्मीर में श्रीनगर से 22 किमी दूर स्थित है, जिसका शाब्दिक अर्थ \"दस गाँव\" है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Which of the following is NOT an abiotic component ?</p>",
                    question_hi: "<p>19. निम्नलिखित में से कौन एक अजैविक घटक नहीं है ?</p>",
                    options_en: ["<p>Sunlight</p>", "<p>Soil</p>", 
                                "<p>Green Plant</p>", "<p>Water</p>"],
                    options_hi: ["<p>सूरज की रोशनी</p>", "<p>मृदा</p>",
                                "<p>हरे पौधे</p>", "<p>जल</p>"],
                    solution_en: "<p>19.(c) In biology and ecology, abiotic components are non-living chemical and physical parts of the environment. So, green plants are not an abiotic component.</p>",
                    solution_hi: "<p>19.(c) जीव विज्ञान और पारिस्थितिकी में, अजैविक घटक पर्यावरण के निर्जीव रासायनिक और भौतिक भाग हैं, इसलिए हरे पौधे एक अजैविक घटक नहीं हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. Which of the following is an ancient Buddhist text ?",
                    question_hi: "20. निम्नलिखित में से कौन एक प्राचीन बौद्ध ग्रंथ है ?",
                    options_en: [" Raghuvamsam ", " Abhidharma Kosha ", 
                                " Vishnu Purana ", " Ritusamhara "],
                    options_hi: [" रघुवंशम", " अभिधरमा कोश",
                                " विष्णु पुराण", " ऋतुसंहार"],
                    solution_en: "20.(b) Abhidharma Kosha is an ancient Buddhist text on Abhidharma, written in Sanskrit by the Indian Buddhist scholar Vasubandhu in the fourth or fifth century CE.",
                    solution_hi: "20.(b) अभिधरमा कोश भारतीय बौद्ध विद्वान वसुबंधु द्वारा चौथी या पांचवीं शताब्दी CE में संस्कृत में लिखे गए अभिधर्म पर एक प्राचीन बौद्ध ग्रंथ है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. In which year did the disinvestment process in Public Sector Enterprises in India start ?</p>",
                    question_hi: "<p>21. भारत में सार्वजनिक क्षेत्र के उद्यमों में विनिवेश प्रक्रिया किस वर्ष शुरू हुई ?</p>",
                    options_en: ["<p>2000</p>", "<p>1991</p>", 
                                "<p>2018</p>", "<p>1990</p>"],
                    options_hi: ["<p>2000</p>", "<p>1991</p>",
                                "<p>2018</p>", "<p>1990</p>"],
                    solution_en: "<p>21.(b) The disinvestment process in Public Sector Enterprises in India started in 1991 to raise the necessary capital and minimise the nation&rsquo;s fiscal deficit.</p>",
                    solution_hi: "<p>21.(b) भारत में सार्वजनिक क्षेत्र के उद्यमों में विनिवेश प्रक्रिया 1991 में आवश्यक पूंजी जुटाने और देश के राजकोषीय घाटे को कम करने के लिए शुरू हुई थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. &lsquo;Operation Greens&rsquo; is a government scheme for</p>",
                    question_hi: "<p>22. \'ऑपरेशन ग्रीन\'______एक सरकारी योजना है।</p>",
                    options_en: ["<p>General price levels of crops</p>", "<p>Supply stablisation of TOP crops (Tomato Onion Potato)</p>", 
                                "<p>development of bamboo crops</p>", "<p>research and investment in crop education</p>"],
                    options_hi: ["<p>फसलों के सामान्य मूल्य स्तर</p>", "<p>शीर्ष फसलों की आपूर्ति (टमाटर प्याज आलू)</p>",
                                "<p>बांस की फसलों का विकास</p>", "<p>फसल शिक्षा में अनुसंधान और निवेश</p>"],
                    solution_en: "<p>22.(b) &lsquo;Operation Greens\' is a government scheme for Supplying stablisation of TOP crops (Tomato, Onion, Potato). It is a project approved by the Ministry of Food Processing Industries started in the year 2018-2019.</p>",
                    solution_hi: "<p>22.(b) \'ऑपरेशन ग्रीन्स\' TOP फसलों (टमाटर, प्याज, आलू) के स्थिरीकरण की आपूर्ति के लिए एक सरकारी योजना है। यह खाद्य प्रसंस्करण उद्योग मंत्रालय द्वारा अनुमोदित एक परियोजना है जिसे वर्ष 2018-2019 में शुरू किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Which of the following is NOT related to Centre-State relations in India ?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन भारत में केंद्र-राज्य संबंधों से संबंधित नहीं है ?</p>",
                    options_en: ["<p>Sarkaria Commission</p>", "<p>Punchhi Commission</p>", 
                                "<p>Rajamannar Committee</p>", "<p>Kothari Commission</p>"],
                    options_hi: ["<p>सरकारिया आयोग</p>", "<p>पंछी आयोग</p>",
                                "<p>राजमन्नार समिति</p>", "<p>कोठारी आयोग</p>"],
                    solution_en: "<p>23.(d) The Kothari Commission is not related to Centre-State relations in India. The Kothari commission was set up by the government of India in 1964 to examine all aspects of the educational sector in India.</p>",
                    solution_hi: "<p>23.(d) कोठारी आयोग भारत में केंद्र-राज्य संबंधों से संबंधित नहीं है। भारत में शैक्षिक क्षेत्र के सभी पहलुओं की जांच करने के लिए 1964 में भारत सरकार द्वारा कोठारी आयोग की स्थापना की गई थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. The down fold in a rock is known as a/an</p>",
                    question_hi: "<p>24. एक चट्टान में नत वलन को किस रूप में जाना जाता है ?</p>",
                    options_en: ["<p>backline</p>", "<p>anticline</p>", 
                                "<p>crestline</p>", "<p>syncline</p>"],
                    options_hi: ["<p>बैकलाइन</p>", "<p>एंटिकलाइन</p>",
                                "<p>क्रेस्टलाइन</p>", "<p>सिंकलाइन</p>"],
                    solution_en: "<p>24.(d) The down fold in a rock is known as a syncline. In structural geology, a syncline is a fold with younger layers closer to the center of the structure.</p>",
                    solution_hi: "<p>24.(d) एक चट्टान में नीचे की तह को एक सिंकलाइन के रूप में जाना जाता है। संरचनात्मक भूविज्ञान में, एक सिंकलाइन संरचना के केंद्र के करीब छोटी परतों के साथ एक तह है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. Who was the founder of the Vishishtadvaita philosophy ?",
                    question_hi: "25. विशिष्टाद्वैत दर्शन के संस्थापक कौन थे ?",
                    options_en: [" Madhvacharya ", " Ramanujacharya ", 
                                " Vishnu Swami ", " Nimbarka "],
                    options_hi: [" माधवाचार्य", " रामानुजाचार्य",
                                " विष्णु स्वामी", " निम्बार्क"],
                    solution_en: "25.(b) Ramanujacharya is the founder of the Vishishtadvaita philosophy. Ramanujacharya was an Indian philosopher, Hindu theologian, social reformer, and one of the most important exponents of Vaishnavism.",
                    solution_hi: "25.(b) विशिष्टाद्वैत दर्शन के संस्थापक रामानुजाचार्य हैं। रामानुजाचार्य एक भारतीय दार्शनिक, हिंदू धर्मशास्त्री, समाज सुधारक और वैष्णववाद के सबसे महत्वपूर्ण प्रतिपादकों में से एक थे।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Which state does NOT have a Vidhan Parishad (Legislative Council) ?",
                    question_hi: "26. किस राज्य में विधान परिषद (विधान परिषद) नहीं है ?",
                    options_en: [" Maharashtra", " Kerala ", 
                                " Telangana ", " Karnataka "],
                    options_hi: [" महाराष्ट्र", " केरल",
                                " तेलंगाना", " कर्नाटक"],
                    solution_en: "26.(b) Kerala does not have a Vidhan Parishad (Legislative Council). The State Legislative Council i.e. Vidhan Parishad or Saasana Mandali is the upper house in those states of India that have a bicameral state legislature.",
                    solution_hi: "26.(b) केरल में विधान परिषद (विधान परिषद) नहीं है। राज्य विधान परिषद यानी विधान परिषद या सासना मंडली भारत के उन राज्यों में उच्च सदन है जिनमें द्विसदनीय राज्य विधायिका है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which is India&rsquo;s first ever Innovative advanced Earth Observation Satellite launched in 2018 ?</p>",
                    question_hi: "<p>27. 2018 में लॉन्च किया गया भारत का पहला अभिनव उन्नत पृथ्वी अवलोकन उपग्रह कौन सा है ?</p>",
                    options_en: ["<p>APPLE</p>", "<p>GSAT-7</p>", 
                                "<p>HysIS</p>", "<p>GSAT-2</p>"],
                    options_hi: ["<p>APPLE</p>", "<p>GSAT-7</p>",
                                "<p>HysIS</p>", "<p>GSAT-2</p>"],
                    solution_en: "<p>27.(c) India&rsquo;s first-ever Innovative Advanced Earth Observation Satellite launched in 2018 is HysIS. ISRO (Indian Space Research Organization) developed HySIS, a full-fledged hyperspectral EO (Earth Observation).</p>",
                    solution_hi: "<p>27.(c) 2018 में लॉन्च किया गया भारत का पहला इनोवेटिव एडवांस्ड अर्थ ऑब्जर्वेशन सैटेलाइट HysIS है। ISRO (भारतीय अंतरिक्ष अनुसंधान संगठन) ने HySIS विकसित किया, जो एक पूर्ण विकसित हाइपरस्पेक्ट्रल EO (अर्थ ऑब्जर्वेशन) है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which of the following is a satellite based augmentation system of India ?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन सा उपग्रह भारत की आवर्धन प्रणाली पर आधारित है ?</p>",
                    options_en: ["<p>GAGAN</p>", "<p>JATAN</p>", 
                                "<p>NAG</p>", "<p>GAGAN SHAKTI</p>"],
                    options_hi: ["<p>गगन</p>", "<p>जतन</p>",
                                "<p>NAG</p>", "<p>गगन शक्ति</p>"],
                    solution_en: "<p>28.(a) GAGAN is a satellite-based augmentation system of India. The GPS-aided GEO augmented navigation is an implementation of a regional satellite-based augmentation system by the Government of India.</p>",
                    solution_hi: "<p>28.(a) गगन भारत की उपग्रह आधारित वृद्धि प्रणाली है। GPS सहायता प्राप्त GEO संवर्धित नेविगेशन भारत सरकार द्वारा एक क्षेत्रीय उपग्रह-आधारित वृद्धि प्रणाली का कार्यान्वयन है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. The &lsquo;SATH-E&rsquo; project is associated with which of the following fields ?</p>",
                    question_hi: "<p>29. &lsquo;SATH-E&rsquo;\' परियोजना निम्नलिखित में से किस क्षेत्र से संबंधित है ?</p>",
                    options_en: ["<p>Communication</p>", "<p>Education</p>", 
                                "<p>Agriculture</p>", "<p>Transportation</p>"],
                    options_hi: ["<p>संचार</p>", "<p>शिक्षा</p>",
                                "<p>कृषि</p>", "<p>परिवहन</p>"],
                    solution_en: "<p>29.(b) The &lsquo;SATH-E&rsquo; project is associated with the fields of Education. Project SATH-E, &lsquo;Sustainable Action for Transforming Human Capital-Education&rsquo;, was launched in 2017 to identify and build three &lsquo;role model&rsquo; States for the school education sector.</p>",
                    solution_hi: "<p>29.(b) \'SATH-E\' परियोजना शिक्षा के क्षेत्र से जुड़ी है। प्रोजेक्ट SATH-E, \'सस्टेनेबल एक्शन फॉर ट्रांसफॉर्मिंग ह्यूमन कैपिटल-एजुकेशन\', 2017 में स्कूली शिक्षा क्षेत्र के लिए तीन \'रोल मॉडल\' राज्यों की पहचान और निर्माण के लिए शुरू किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which of the following is a metalloid ?</p>",
                    question_hi: "<p>30. निम्नलिखित में से कौन एक उपधातु है ?</p>",
                    options_en: ["<p>Lead</p>", "<p>Gold</p>", 
                                "<p>Silicon</p>", "<p>Bromine</p>"],
                    options_hi: ["<p>शीशा</p>", "<p>सोना</p>",
                                "<p>सिलिकॉन</p>", "<p>ब्रोमीन</p>"],
                    solution_en: "<p>30.(c) Silicon is a metalloid. A metalloid is an element that has properties that are intermediate between those of metals and non-metals. Metalloids can also be called semimetals.</p>",
                    solution_hi: "<p>30.(c) सिलिकॉन एक उपधातु है। उपधातु एक ऐसा तत्व है जिसमें ऐसे गुण होते हैं जो धातुओं और अधातुओं के बीच मध्यवर्ती होते हैं। उपधातुओं को अर्धधातु भी कहा जा सकता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. India&rsquo;s scientific mission to observe and study the solar corona is called.</p>",
                    question_hi: "<p>31. सौर प्रभामंडल का निरीक्षण और अध्ययन करने के लिए भारत के वैज्ञानिक मिशन को_____ कहा जाता है।</p>",
                    options_en: ["<p>Satnav</p>", "<p>Astrostat</p>", 
                                "<p>Aditya -L1</p>", "<p>Chandrayaan</p>"],
                    options_hi: ["<p>सतनव</p>", "<p>एस्ट्रोस्टेट</p>",
                                "<p>आदित्य -L1</p>", "<p>चंद्रयान</p>"],
                    solution_en: "<p>31.(c) India&rsquo;s scientific mission to observe and study the solar corona is called Aditya -L1. Aditya L1 is a planned coronagraphy spacecraft to study the solar atmosphere, currently being designed and developed by the Indian Space Research Organisation and various other Indian Research institutes.</p>",
                    solution_hi: "<p>31.(c) सौर कोरोना का निरीक्षण और अध्ययन करने के लिए भारत के वैज्ञानिक मिशन को आदित्य-L1 कहा जाता है। आदित्य - L1 सौर वातावरण का अध्ययन करने के लिए एक नियोजित कोरोनोग्राफी अंतरिक्ष यान है, जिसे वर्तमान में भारतीय अंतरिक्ष अनुसंधान संगठन और विभिन्न अन्य भारतीय अनुसंधान संस्थानों द्वारा डिजाइन और विकसित किया जा रहा है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. The Big Bang theory was propounded by</p>",
                    question_hi: "<p>32. बिग बैंग सिद्धांत किसके द्वारा प्रतिपादित किया गया था ?</p>",
                    options_en: ["<p>Georges Lema&icirc;tre</p>", "<p>Al-Biruni</p>", 
                                "<p>Thomas Gold</p>", "<p>Dr. Allen Sundes</p>"],
                    options_hi: ["<p>जॉर्जेस लेमाइट्रे</p>", "<p>अल-बिरूनी</p>",
                                "<p>थॉमस गोल्ड</p>", "<p>डॉ एलन सुंडेस</p>"],
                    solution_en: "<p>32.(a) The Big Bang theory was propounded by Georges Lemaitre, Belgian astronomer and cosmologist who formulated the modern big bang theory, which holds that the universe began in a cataclysmic explosion of a small, primeval &ldquo;super-atom&rdquo;.</p>",
                    solution_hi: "<p>32.(a) बिग बैंग सिद्धांत बेल्जियम के खगोलशास्त्री और ब्रह्मांड विज्ञानी जॉर्जेस लेमैत्रे द्वारा प्रतिपादित किया गया था, जिन्होंने आधुनिक बिग बैंग सिद्धांत तैयार किया था, जो मानता है कि ब्रह्मांड एक छोटे, आदिम \"सुपर-परमाणु\" के प्रलयकारी विस्फोट से शुरू हुआ था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Who was the Viceroy when the Royal Commission on Civil Services was formed in 1912 ?</p>",
                    question_hi: "<p>33. 1912 में सिविल सेवा पर रॉयल कमीशन का गठन के समय वायसराय कौन था ?</p>",
                    options_en: ["<p>Lord Dufferin</p>", "<p>Lord Irwin</p>", 
                                "<p>Lord Hardinge</p>", "<p>Lord Curzon</p>"],
                    options_hi: ["<p>लॉर्ड डफरिन</p>", "<p>लॉर्ड इरविन</p>",
                                "<p>लॉर्ड हार्डिंग</p>", "<p>लॉर्ड कर्जन</p>"],
                    solution_en: "<p>33.(c) Lord Hardinge was the then viceroy when the Royal Commission on Civil Services was formed in 1912.</p>",
                    solution_hi: "<p>33.(c) 1912 में जब सिविल सेवा पर रॉयल कमीशन का गठन किया गया था तब लॉर्ड हार्डिंग तत्कालीन वायसराय थे।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Where is the &lsquo;Zojila Tunnel Project&rsquo; located ?</p>",
                    question_hi: "<p>34. \'जोजिला सुरंग परियोजना\' कहाँ स्थित है ?</p>",
                    options_en: ["<p>Uttar Pradesh</p>", "<p>Sikkim</p>", 
                                "<p>Jammu &amp; Kashmir</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>उत्तर प्रदेश</p>", "<p>सिक्किम</p>",
                                "<p>जम्मू और कश्मीर</p>", "<p>उड़ीसा</p>"],
                    solution_en: "<p>34.(c) Zoji La tunnel is a 14.2 km long road tunnel under Zoji La pass in the Himalayas between Sonmarg and Drass town in Kargil district of the Indian Union territory of Ladakh.</p>",
                    solution_hi: "<p>34.(c) ज़ोजी ला सुरंग भारतीय केंद्र शासित प्रदेश लद्दाख के कारगिल जिले में सोनमर्ग और द्रास शहर के बीच हिमालय में ज़ोजी ला दर्रा के नीचे एक 14.2 किमी लंबी सड़क सुरंग है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Where is the headquarters of the OECD located ?</p>",
                    question_hi: "<p>35. OECD का मुख्यालय कहाँ स्थित है ?</p>",
                    options_en: ["<p>Rome</p>", "<p>Geneva</p>", 
                                "<p>Paris</p>", "<p>New York</p>"],
                    options_hi: ["<p>रोम</p>", "<p>जिनेवा</p>",
                                "<p>पेरिस</p>", "<p>न्यूयॉर्क</p>"],
                    solution_en: "<p>35.(c) Paris is the headquarters of the OECD. The Organization for Economic Co-operation and Development is an inter-governmental economic organization with 38 member countries. It was founded in 1961 to stimulate economic progress and world trade.</p>",
                    solution_hi: "<p>35.(c) पेरिस OECD (Organization for Economic Co-operation and Development) का मुख्यालय है। OECD, 38 सदस्य देशों के साथ एक अंतर सरकारी आर्थिक संगठन है, जिसकी स्थापना 1961 में आर्थिक प्रगति और विश्व व्यापार को प्रोत्साहित करने के लिए की गई थी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Lord Mahavira&rsquo;s original name is</p>",
                    question_hi: "<p>36. भगवान महावीर का वास्तविक नाम क्या है ?</p>",
                    options_en: ["<p>Ananda</p>", "<p>Sariputta</p>", 
                                "<p>Vardhamana</p>", "<p>Siddhartha</p>"],
                    options_hi: ["<p>आनंद</p>", "<p>सारिपूत</p>",
                                "<p>वर्धमान</p>", "<p>सिद्धार्थ</p>"],
                    solution_en: "<p>36.(c) The original name of Lord Mahavira is Vardhamana. Lord Mahavira was the last and <math display=\"inline\"><msup><mrow><mn>24</mn></mrow><mrow><mi>t</mi><mi>h</mi></mrow></msup></math> Tirthankara of Jainism and is responsible for reordering the religion and introducing the Jain Sangha.</p>",
                    solution_hi: "<p>36.(c) भगवान महावीर का असली नाम वर्धमान है। भगवान महावीर जैन धर्म के अंतिम और 24वें तीर्थंकर थे। वह धर्म को फिर से व्यवस्थित करने और जैन संघ की शुरुआत करने के लिए जाने जाते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Who wrote the great literary work &lsquo;Mricchakatika&rsquo; ?</p>",
                    question_hi: "<p>37. महान साहित्यिक कृति मृच्छकटिकम् किसने लिखी ?</p>",
                    options_en: ["<p>Harsha</p>", "<p>Shudraka</p>", 
                                "<p>Bhaasa</p>", "<p>Kalidasa </p>"],
                    options_hi: ["<p>हर्ष</p>", "<p>शूद्रक</p>",
                                "<p>भासा</p>", "<p>कालिदास</p>"],
                    solution_en: "<p>37.(b) Shudraka wrote the great literary work &lsquo;Mricchakatika&rsquo;. Shudraka was an Indian king and playwright, to whom three Sanskrit plays are attributed:&nbsp; Mricchakatika, Vinavasavadatta, Padmaprabhritaka.</p>",
                    solution_hi: "<p>37.(b) शूद्रक ने महान साहित्यिक कृति \'मृच्छकटिका\' लिखी। शूद्रक एक भारतीय राजा और नाटककार थे, जिन्हें संस्कृत के तीन नाटकों का श्रेय दिया जाता है: मृच्छकटिका, विनवसवदत्त, पद्मप्रभृतक।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which eminent person is associated with Bardoli ?</p>",
                    question_hi: "<p>38. बारडोली से किस प्रसिद्ध व्यक्ति का संबंध है ?</p>",
                    options_en: ["<p>Sardar Vallabhbhai Patel</p>", "<p>Aurobindo Ghosh</p>", 
                                "<p>Mahavir</p>", "<p>Guru Nanak</p>"],
                    options_hi: ["<p>सरदार वल्लभ भाई पटेल</p>", "<p>अरबिंदो घोष</p>",
                                "<p>महावीर</p>", "<p>गुरु नानक</p>"],
                    solution_en: "<p>38.(a) Sardar Vallabhbhai Patel is associated with Bardoli Satyagraha. The Bardoli Satyagraha, in the state of Gujarat during the British Raj, was a major episode of civil disobedience and revolt. It was led by Sardar Vallabh Bhai Patel on 12 June 1928.</p>",
                    solution_hi: "<p>38.(a) सरदार वल्लभभाई पटेल बारदोली सत्याग्रह से जुड़े हैं। ब्रिटिश राज के दौरान गुजरात राज्य में बारडोली सत्याग्रह, सविनय अवज्ञा और विद्रोह का एक प्रमुख प्रकरण था। विद्रोह का नेतृत्व 12 जून 1928 को सरदार वल्लभ भाई पटेल ने किया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. As of October 2020, who is the Chairman of the Fifteenth Finance Commission of India ?</p>",
                    question_hi: "<p>39. अक्टूबर 2020 तक, भारत के पंद्रहवें वित्त आयोग के अध्यक्ष कौन हैं ?</p>",
                    options_en: ["<p>Shaktikanta Das</p>", "<p>Vijay L Kelkar</p>", 
                                "<p>AM Khusro</p>", "<p>NK Singh</p>"],
                    options_hi: ["<p>शक्तिकांत दास</p>", "<p>विजय एल केलकर</p>",
                                "<p>एएम खुसरो</p>", "<p>एनके सिंहh</p>"],
                    solution_en: "<p>39.(d) As of October 2020, NK Singh is the Chairman of the Fifteenth Finance Commission of India. The finance commission of India was set up under Article 280 of the constitution and its core responsibility is to evaluate the state of finances of the union and state governments.</p>",
                    solution_hi: "<p>39.(d) अक्टूबर 2020 तक, एनके सिंह भारत के पंद्रहवें वित्त आयोग के अध्यक्ष हैं। भारत के वित्त आयोग की स्थापना संविधान के अनुच्छेद 280 के तहत की गई थी, और इसकी मुख्य जिम्मेदारी केंद्र और राज्य सरकारों के वित्त की स्थिति का मूल्यांकन करना है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. Which is the 29th State of India created in 2014 ?</p>",
                    question_hi: "<p>40. 2014 में बनाया गया भारत का 29वां राज्य कौन सा है ?</p>",
                    options_en: ["<p>Uttarakhand</p>", "<p>Jharkhand</p>", 
                                "<p>Telangana</p>", "<p>Sikkim</p>"],
                    options_hi: ["<p>उत्तराखंड</p>", "<p>झारखंड</p>",
                                "<p>तेलंगाना</p>", "<p>सिक्किम</p>"],
                    solution_en: "<p>40.(c) Telangana is the 29th State of India created in 2014. On November 1, 1956, Telangana merged with the state of Andhra Pradesh, carved out of erstwhile Madras, to form Andhra Pradesh. But after several years of protest, the central government decided to bifurcate on February 18, 2014 and Hyderabad is the capital of Telangana.</p>",
                    solution_hi: "<p>40.(c) तेलंगाना 2014 में बनाया गया भारत का 29 वां राज्य है। 1 नवंबर, 1956 को तेलंगाना का आंध्र प्रदेश राज्य में विलय हो गया, जो आंध्र प्रदेश बनाने के लिए तत्कालीन मद्रास से अलग हुआ था। लेकिन कई वर्षों के विरोध के बाद केंद्र सरकार ने 18 फरवरी 2014 को विभाजन का फैसला किया तेलंगाना एक अलग राज्य बना और हैदराबाद, तेलंगाना की राजधानी है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>