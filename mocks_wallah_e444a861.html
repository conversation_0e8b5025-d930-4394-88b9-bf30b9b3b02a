<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> In a certain code language, \'CHILD\' is written as &lsquo;6169248\' and \'GUEST\' is written as \'142153840\'. How will \'BOOK\' be written in that language?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, \'CHILD\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'6169248\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'GUEST\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'142153840\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'BOOK\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;</span><span style=\"font-family: Nirmala UI;\">&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>2303011</p>\n", "<p>41522</p>\n", 
                                "<p>4151522</p>\n", "<p>23011</p>\n"],
                    options_hi: ["<p>2303011</p>\n", "<p>41522</p>\n",
                                "<p>4151522</p>\n", "<p>23011</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :</span><span style=\"font-family: Cambria Math;\"> Each Vowel is replaced by its place v</span><span style=\"font-family: Cambria Math;\">alue in alphabet, while each consonant is replaced by (Place value &times;</span><span style=\"font-family: Cambria Math;\"> 2).</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CHILD &rArr; </span><span style=\"font-family: Cambria Math;\">3-8-9-12-4 &rArr;</span><span style=\"font-family: Cambria Math;\"> (3 &times;</span><span style=\"font-family: Cambria Math;\"> 2)-(8 </span><span style=\"font-family: Cambria Math;\"> 2)-9-(12 &times; </span><span style=\"font-family: Cambria Math;\">2)-(4 &times;</span><span style=\"font-family: Cambria Math;\">&nbsp;2) = 6-16-9-24-8 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">GUEST &rArr;</span><span style=\"font-family: Cambria Math;\"> 7-21-5-19-20 &rArr;</span><span style=\"font-family: Cambria Math;\"> (7 &times;</span><span style=\"font-family: Cambria Math;\"> 2)-21-5-(19 &times;</span><span style=\"font-family: Cambria Math;\"> 2)-(20 &times;</span><span style=\"font-family: Cambria Math;\"> 2) = 14-21-5-38-40 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, BOOK &rArr;</span><span style=\"font-family: Cambria Math;\"> 2-15-15-11 &rArr;</span><span style=\"font-family: Cambria Math;\"> (2 &times; </span><span style=\"font-family: Cambria Math;\">2)-15-15-(11 &times;</span><span style=\"font-family: Cambria Math;\"> 2) = 4-15-15-22 </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2339;&#2350;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> &times;</span><span style=\"font-family: Cambria Math;\"> 2) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">CHILD &rArr; </span><span style=\"font-family: Cambria Math;\">3 - 8 - 9 - 12 - 4 &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> (3 &times; </span><span style=\"font-family: Cambria Math;\">2) - (8 &times;</span><span style=\"font-family: Cambria Math;\"> 2) - 9 - (12 &times;</span><span style=\"font-family: Cambria Math;\">&nbsp;2) - (4&times;</span><span style=\"font-family: Cambria Math;\">&nbsp;2) = </span><span style=\"font-family: Cambria Math;\">6 - 16 - 9 - 24 -8 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">GUEST &rArr;</span><span style=\"font-family: Cambria Math;\"> 7 - 21 - 5 - 19 - 20 &rArr; </span><span style=\"font-family: Cambria Math;\">(7 &times;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">2 )</span><span style=\"font-family: Cambria Math;\"> - 21 -5 -(19 &times; </span><span style=\"font-family: Cambria Math;\">2) - (20 &times; </span><span style=\"font-family: Cambria Math;\">2) = 14 - 21 - 5 - 38 - 40 </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,BOOK &rArr; </span><span style=\"font-family: Cambria Math;\">2 - 15 -15 - 11 </span><span style=\"font-family: Cambria Math;\"> (2 &times;</span><span style=\"font-family: Cambria Math;\"> 2 ) - 15 - 15 - (11 &times; </span><span style=\"font-family: Cambria Math;\">2) = 4 - 15 - 15 - 22 </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the option that is related to the fifth number in the same way as the second </span><span style=\"font-family: Cambria Math;\">number</span><span style=\"font-family: Cambria Math;\"> is related to the first number and the fourth number is related to the third </span><span style=\"font-family: Cambria Math;\">number</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 80 :: 7 : 392 :: 5 : ?</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2305;&#2330;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 80 :: 7 : 392 :: 5 : ?</span></p>\n",
                    options_en: ["<p>148</p>\n", "<p>136</p>\n", 
                                "<p>162</p>\n", "<p>150</p>\n"],
                    options_hi: ["<p>148</p>\n", "<p>136</p>\n",
                                "<p>162</p>\n", "<p>150</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic</span><span style=\"font-family: Cambria Math;\"> :-</span><span style=\"font-family: Cambria Math;\"> n&nbsp;</span><span style=\"font-family: Cambria Math;\">: (</span><span style=\"font-family: Cambria Math;\"> n&sup3; + n&sup2; </span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 80 , 4 </span><span style=\"font-family: Cambria Math;\">: ( 4&sup3; + 4&sup2; )</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 4 : (64 + 16) = 4 : 80</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 392, 7 </span><span style=\"font-family: Cambria Math;\">: ( 7&sup3; + 7&sup2; )</span><span style=\"font-family: Cambria Math;\">= 7 : (343 + 49) = 7 : 392</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, in </span><span style=\"font-family: Cambria Math;\">5 :</span><span style=\"font-family: Cambria Math;\"> (?), 5&nbsp;</span><span style=\"font-family: Cambria Math;\"> : ( 5&sup3; + 5&sup2; )</span><span style=\"font-family: Cambria Math;\">= 5 : (125 + 25) = 5 : </span><span style=\"font-family: Cambria Math;\">150</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\">:- n&nbsp;: ( n&sup3; + n&sup2; )</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 80 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 4 : ( 4&sup3; + 4&sup2; )</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 4 : (64 + 16) = 4 : 80</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 392 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 7 : ( 7&sup3; + 7&sup2; )</span><span style=\"font-family: Cambria Math;\">&nbsp;= 7 : (343 + 49) = 7 : 392</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, 5 : (?) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 5&nbsp; : ( 5&sup3; + 5&sup2; )</span><span style=\"font-family: Cambria Math;\">= 5 : (125 + 25) = 5 : </span><span style=\"font-family: Cambria Math;\">150</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: " <p>3.</span><span style=\"font-family:Cambria Math\"> Which of the following letter-clusters will replace the question mark (?) in the given series to make it logically complete?</span></p> <p><span style=\"font-family:Cambria Math\">YXZ, VUW, SRT, POQ</span><span style=\"font-family:Cambria Math\">, ?</span></p>",
                    question_hi: " <p>3.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">श्रृंखला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तार्किक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पूर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बनाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\">   </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चिह्न</span><span style=\"font-family:Cambria Math\"> (?) </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतिस्थापित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करेगा</span><span style=\"font-family:Cambria Math\">?</span></p> <p><span style=\"font-family:Cambria Math\">YXZ ,</span><span style=\"font-family:Cambria Math\"> VUW , SRT , POQ , ?</span></p>",
                    options_en: [" <p> LMN</span></p>", " <p> MLN</span></p>", 
                                " <p> NML</span></p>", " <p> LNM</span></p>"],
                    options_hi: [" <p> LMN</span></p>", " <p> MLN</span></p>",
                                " <p> NML</span></p>", " <p> LNM</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">3.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Cambria Math\">For first letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> Y - 3 = V, V - 3 = S, S - 3 = P, P - 3 = </span><span style=\"font-family:Cambria Math\">M</span></p> <p><span style=\"font-family:Cambria Math\">For second letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> X - 3 = U, U - 3 = R, R - 3 = O, O - 3 =</span><span style=\"font-family:Cambria Math\"> L</span></p> <p><span style=\"font-family:Cambria Math\">For third letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> Z - 3 = W, W - 3 = T, T - 3 = Q, Q - 3 = </span><span style=\"font-family:Cambria Math\">N</span></p> <p><span style=\"font-family:Cambria Math\">So, we get MLN.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">3.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> : Y - 3 = V , V - 3 = S , S - 3 = P , P -</span><span style=\"font-family:Cambria Math\"> 3 = </span><span style=\"font-family:Cambria Math\">M</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> : X - 3 = U , U - 3 = R , R - 3 = O , O - 3 =</span><span style=\"font-family:Cambria Math\"> L</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> : Z - 3 = W , W - 3 = T , T - 3 = Q , Q - 3 = </span><span style=\"font-family:Cambria Math\">N</span></p> <p><span style=\"font-family:Nirmala UI\">अतः</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हमें</span><span style=\"font-family:Cambria Math\"> MLN </span><span style=\"font-family:Nirmala UI\">मिलता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">How many triangles are there in the given </span><span style=\"font-family: Cambria Math;\">figure ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image1.png\" width=\"100\" height=\"100\"></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2306;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image1.png\" width=\"100\" height=\"100\"></p>\n",
                    options_en: ["<p>25</p>\n", "<p>28</p>\n", 
                                "<p>24</p>\n", "<p>22</p>\n"],
                    options_hi: ["<p>25</p>\n", "<p>28</p>\n",
                                "<p>24</p>\n", "<p>22</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">From the figure given in the question, on counting the number of triangles we find that there are total 24 triangles.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2367;&#2344;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> 24 </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Which of the foll</span><span style=\"font-family: Cambria Math;\">owing numbers will replace the question mark (?) in the given series?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4, 8, 5</span><span style=\"font-family: Cambria Math;\">, ?</span><span style=\"font-family: Cambria Math;\">, 15, 90</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">4 ,</span><span style=\"font-family: Cambria Math;\"> 8 , 5 , ? , </span><span style=\"font-family: Cambria Math;\">15 ,</span><span style=\"font-family: Cambria Math;\"> 90</span></p>\n",
                    options_en: ["<p>18</p>\n", "<p>13</p>\n", 
                                "<p>22</p>\n", "<p>20</p>\n"],
                    options_hi: ["<p>18</p>\n", "<p>13</p>\n",
                                "<p>22</p>\n", "<p>20</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image2.png\" width=\"179\" height=\"59\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image2.png\" width=\"179\" height=\"59\"></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Select the option that is related to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">GREATLY : ERGAYLT :: MIXTURE</span><span style=\"font-family: Cambria Math;\"> : XIMTERU :: TROUBLE : ?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">GREATLY : ERGAYLT :: MIXTURE : XIMTERU :: TR</span><span style=\"font-family: Cambria Math;\">OUBLE : ?</span></p>\n",
                    options_en: ["<p>BORTUEL</p>\n", "<p>ORTUEBL</p>\n", 
                                "<p>TUELBOR</p>\n", "<p>ORTUELB</p>\n"],
                    options_hi: ["<p>BORTUEL</p>\n", "<p>ORTUEBL</p>\n",
                                "<p>TUELBOR</p>\n", "<p>ORTUELB</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image3.png\"><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image4.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image5.png\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image3.png\"><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image4.png\"></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image5.png\"></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Select the correct mirror image of the given combination when the mirror is placed at XY as shown. </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image6.png\" width=\"100\" height=\"92\"></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> XY </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image6.png\" width=\"102\" height=\"94\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image7.png\" width=\"123\" height=\"18\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image8.png\" width=\"116\" height=\"17\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image9.png\" width=\"116\" height=\"17\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image10.png\" width=\"123\" height=\"18\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image7.png\" width=\"116\" height=\"17\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image8.png\" width=\"109\" height=\"16\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image9.png\" width=\"123\" height=\"18\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image10.png\" width=\"116\" height=\"17\"></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image7.png\" width=\"130\" height=\"19\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image7.png\" width=\"123\" height=\"18\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: " <p>8. </span><span style=\"font-family:Cambria Math\">Which letter-cluster will replace the question mark (?) to complete the given series?</span></p> <p><span style=\"font-family:Cambria Math\">NHMS, HNOU</span><span style=\"font-family:Cambria Math\">, ?</span><span style=\"font-family:Cambria Math\">, HNSY, NHUA</span></p>",
                    question_hi: " <p>8. </span><span style=\"font-family:Nirmala UI\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">श्रृंखला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पूरा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चिह्न</span><span style=\"font-family:Cambria Math\"> (?) </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतिस्थापित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करेग</span><span style=\"font-family:Nirmala UI\">ा</span><span style=\"font-family:Cambria Math\"> ?</span></p> <p><span style=\"font-family:Cambria Math\">NHMS ,</span><span style=\"font-family:Cambria Math\"> HNOU , ? , </span><span style=\"font-family:Cambria Math\">HNSY ,</span><span style=\"font-family:Cambria Math\"> NHUA</span></p>",
                    options_en: [" <p> HNOX</span></p>", " <p> NHSX</span></p>", 
                                " <p> HNPY</span></p>", " <p> NHQW</span></p>"],
                    options_hi: [" <p> HNOX</span></p>", " <p> NHSX</span></p>",
                                " <p> HNPY</span></p>", " <p> NHQW</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">For first letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> N - 6 = H, H + 6 = </span><span style=\"font-family:Cambria Math\">N,</span><span style=\"font-family:Cambria Math\"> N - 6 = H, H + 6 = N</span></p> <p><span style=\"font-family:Cambria Math\">For second letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> H + 6 = N, N - 6 = </span><span style=\"font-family:Cambria Math\">H</span><span style=\"font-family:Cambria Math\">, H + 6 = N, N - 6 = H</span></p> <p><span style=\"font-family:Cambria Math\">For third letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> M + 2 = O, O + 2 = </span><span style=\"font-family:Cambria Math\">Q</span><span style=\"font-family:Cambria Math\">, Q + 2 = S, S + 2 = U</span></p> <p><span style=\"font-family:Cambria Math\">For fourth letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> S + 2 = U, U + 2 = </span><span style=\"font-family:Cambria Math\">W</span><span style=\"font-family:Cambria Math\">, W + 2 = Y, Y + 2 = A</span></p> <p><span style=\"font-family:Cambria Math\">So, we get NHQW.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> : N - 6 = H , H + 6 = </span><span style=\"font-family:Cambria Math\">N ,</span><span style=\"font-family:Cambria Math\"> N - 6 = H , H + 6 = N</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> : H + 6 = N , N - 6 = </span><span style=\"font-family:Cambria Math\">H </span><span style=\"font-family:Cambria Math\">, H + 6 = N , N - 6 = H</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> : M + 2 = O , O + 2 = </span><span style=\"font-family:Cambria Math\">Q</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">, Q + 2 = S , S + 2 = U</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चौथे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> : S + 2 = U , U + 2 = </span><span style=\"font-family:Cambria Math\">W</span><span style=\"font-family:Cambria Math\">, W + 2 = Y , Y + 2 = A</span></p> <p><span style=\"font-family:Nirmala UI\">अतः</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हमें</span><span style=\"font-family:Cambria Math\"> NHQW </span><span style=\"font-family:Nirmala UI\">मिलता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Three Statements are given followed by </span><span style=\"font-family: Cambria Math;\">Three</span><span style=\"font-family: Cambria Math;\"> conclusions numbered I, II and III.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Assuming the statements to be true, e</span><span style=\"font-family: Cambria Math;\">ven if they seem to be at variance with </span><span style=\"font-family: Cambria Math;\">commonly</span><span style=\"font-family: Cambria Math;\"> known facts, decide which of the conclusions logically follow(s) from the</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">statements</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Statements:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">All towels are cloths.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">No cloth is a blanket.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Some blankets are cushions.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Conclusions:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. Some towels are c</span><span style=\"font-family: Cambria Math;\">ushions.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II</span><span style=\"font-family: Cambria Math;\">. No</span><span style=\"font-family: Cambria Math;\"> cushion is a cloth.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">III. No towel is a blanket.</span></p>\n",
                    question_hi: " <p>9. </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कथन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">जिसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> I, II </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> III </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कथनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सत्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मानते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हुए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">भले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सामान्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तथ्यों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतीत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हों</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">निर्णय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सा</span><span style=\"font-family:Cambria Math\">/</span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कथनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तार्किक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\">/</span><span style=\"font-family:Nirmala UI\">करते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\">/</span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कथन</span><span style=\"font-family:Cambria Math\">:</span></p> <p><span style=\"font-family:Nirmala UI\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">टॉवल</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">क्लॉथ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Nirmala UI\">कोई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्लॉथ</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">ब्लैंकेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ब्लैंकेट</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">कुशन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\">:</span></p> <p><span style=\"font-family:Cambria Math\">I.  </span><span style=\"font-family:Nirmala UI\">क</span><span style=\"font-family:Nirmala UI\">ुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">टॉवल</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">कुशन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\">  </span></p> <p><span style=\"font-family:Cambria Math\">II. </span><span style=\"font-family:Nirmala UI\">कोई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुशन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्लॉथ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p> <p><span style=\"font-family:Cambria Math\">III.</span><span style=\"font-family:Nirmala UI\">कोई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">टॉवल</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">ब्लैंकेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    options_en: ["<p>Only conclusion III follows.</p>\n", "<p>Only conclusion I <span style=\"font-family: Cambria Math;\">follows</span><span style=\"font-family: Cambria Math;\">.</span></p>\n", 
                                "<p>Both conclusions I and II follow.</p>\n", "<p>Both conclusions I and III follow.</p>\n"],
                    options_hi: [" <p> </span><span style=\"font-family:Nirmala UI\">केवल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> III </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p>", " <p> </span><span style=\"font-family:Nirmala UI\">केवल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> I </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                                " <p> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> I </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> II </span><span style=\"font-family:Nirmala UI\">दोनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span></p>", " <p> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> I </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> III </span><span style=\"font-family:Nirmala UI\">दोनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image11.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Clearly, we can see that only conclusion 3 follows.</span></p>\n",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">9.(</span><span style=\"font-family:Cambria Math\">a)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image11.png\"/></p> <p><span style=\"font-family:Nirmala UI\">स्पष्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">हम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">देख</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सकते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">केवल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> III </span><span style=\"font-family:Nirmala UI\">ही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Two different positions of the same dice are shown, the six faces of which are marked as &euro;, &#8365;, &#8366;, &#8372;, &#8377;, &#332;. Select the symbol that will be on the face opposite to the one showing &#332;. </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image12.png\" width=\"130\" height=\"83\"></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> &euro;, &#8365;, &#8366;, &#8372;, &#8377;, &#332; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#332; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2346;&#2352;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image12.png\" width=\"132\" height=\"84\"></p>\n",
                    options_en: ["<p>&#8365;</p>\n", "<p>&#8372;</p>\n", 
                                "<p>&#8366;</p>\n", "<p>&euro;</p>\n"],
                    options_hi: ["<p>&#8365;</p>\n", "<p>&#8372;</p>\n",
                                "<p>&#8366;</p>\n", "<p>&euro;</p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">From both the dice given in question, we can see that</span><span style=\"font-family: Cambria Math;\"> the symbol that will be on the face opposite to the one showing </span><span style=\"font-family: Cambria Math;\">&#332;</span><span style=\"font-family: Cambria Math;\"> is </span><span style=\"font-family: Cambria Math;\">&#8365;</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2344;&#2381;&#2361;</span><span style=\"font-family: Cambria Math;\"> &#332; </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2346;&#2352;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2344;&#2381;&#2361;</span><span style=\"font-family: Cambria Math;\"> &#8365; </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the correct combination of mathematical </span><span style=\"font-family: Cambria Math;\">signs to replace the * signs and to balance the given equation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">43*11*4*71*16</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> * </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2344;&#2381;&#2361;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2344;&#2381;&#2361;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">43*11*4*71*16</span></p>\n",
                    options_en: ["<p>&minus;,=,+,&times;</p>\n", "<p>=,&minus;,+,&times;</p>\n", 
                                "<p>+,&times;,=,+</p>\n", "<p>&minus;,&times;,=,+</p>\n"],
                    options_hi: ["<p>&minus;,=,+,&times;</p>\n", "<p>=,&minus;,+,&times;</p>\n",
                                "<p>+,&times;,=,+</p>\n", "<p>&minus;,&times;,=,+</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In this type of question, we will check by putting options one by one and doing so option (c) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">43*11*4*71*16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting the values of option (c) in above e</span><span style=\"font-family: Cambria Math;\">xpression, we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">43 + 11 &times; </span><span style=\"font-family: Cambria Math;\">4 = 71 + 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS = 43 + 11 &times;</span><span style=\"font-family: Cambria Math;\">&nbsp;4 = 43 + 44 = 87</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">RHS = 71 + 16 = 87</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, LHS = RHS</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (c) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">43*11*4*71*16</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (c) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">43 + 11 &times; </span><span style=\"font-family: Cambria Math;\">4 = 71 + 16</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS = 43 + 11 &times; </span><span style=\"font-family: Cambria Math;\">4 = 43 + 44 = 87</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">RHS = 71 + 16 = 87</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, LHS = RHS</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the correct combination of mathematical signs to sequentially replace the * </span><span style=\"font-family: Cambria Math;\">signs</span><span style=\"font-family: Cambria Math;\"> and to balance the given equation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57*35*7*14*2*80</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> * </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2344;&#2381;&#2361;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2344;&#2381;&#2361;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 57*35*7*14*2*80</span></p>\n",
                    options_en: ["<p>&ndash;,&divide;,+,&times;, =</p>\n", "<p>&times;, +, &divide;,&ndash;, =</p>\n", 
                                "<p>+,&ndash;, &times;,&divide;, =</p>\n", "<p>+, &ndash;, &divide;,&times;, =</p>\n"],
                    options_hi: ["<p>&ndash;,&divide;,+,&times;, =</p>\n", "<p>&times;, +, &divide;,&ndash;, =</p>\n",
                                "<p>+,&ndash;, &times;,&divide;, =</p>\n", "<p>+, &ndash;, &divide;,&times;, =</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In this type of question, we will check by putting options one by one and doing so option (a) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57*35*7*14*2*80</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting the values of option (a) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57 - 35 &divide; </span><span style=\"font-family: Cambria Math;\">7 + 14 &times;</span><span style=\"font-family: Cambria Math;\"> 2 = 80</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 - 35 &divide;</span><span style=\"font-family: Cambria Math;\"> 7 + 14 &nbsp;&times; </span><span style=\"font-family: Cambria Math;\">2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 - 5 + 14 &times;</span><span style=\"font-family: Cambria Math;\"> 2 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 - 5 + 28</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 85 - 5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 80 = RHS </span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (a) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57*35*7*14*2*80</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (a) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57 - 35 &divide; </span><span style=\"font-family: Cambria Math;\">7 + 14 &times; </span><span style=\"font-family: Cambria Math;\">2 = 80</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS = 57 - 35 &divide;</span><span style=\"font-family: Cambria Math;\"> 7 + 14 &times; </span><span style=\"font-family: Cambria Math;\">2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 -</span><span style=\"font-family: Cambria Math;\"> 5 + 14 &times;</span><span style=\"font-family: Cambria Math;\"> 2 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 - 5 + 28</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 85 - 5 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 80 = RHS </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The second number in the given number-pairs is obtained by performing certain </span><span style=\"font-family: Cambria Math;\">mathematical</span><span style=\"font-family: Cambria Math;\"> operation(s) on the first number. The same operation(s) are followed in </span><span style=\"font-family: Cambria Math;\">all</span><span style=\"font-family: Cambria Math;\"> the number-pairs EXCEPT one. Find that odd number-pair.</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2350;</span><span style=\"font-family: Nirmala UI;\">&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2327;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">16 :</span><span style=\"font-family: Cambria Math;\"> 8</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 5</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">9 :</span><span style=\"font-family: Cambria Math;\"> 6</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">36 :</span><span style=\"font-family: Cambria Math;\"> 9</span></p>\n"],
                    options_hi: ["<p>16 : 8</p>\n", "<p>4 : 5</p>\n",
                                "<p>9 : 6</p>\n", "<p>36 : 9</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">16 :</span><span style=\"font-family: Cambria Math;\"> 8 ,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> +</span><span style=\"font-family: Cambria Math;\"> 4</span><span style=\"font-family: Cambria Math;\"> = 4 + 4 = 8 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 5,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">+ </span><span style=\"font-family: Cambria Math;\">3</span><span style=\"font-family: Cambria Math;\"> = 2 + 3 = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">9 :</span><span style=\"font-family: Cambria Math;\"> 6 ,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>9</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">+ </span><span style=\"font-family: Cambria Math;\">3</span><span style=\"font-family: Cambria Math;\"> = 3 + 3 = 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">36 :</span><span style=\"font-family: Cambria Math;\"> 9,&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>36</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> +</span><span style=\"font-family: Cambria Math;\"> 3</span><span style=\"font-family: Cambria Math;\"> = 6 + 3 = 9</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, we can clearly see that (</span><span style=\"font-family: Cambria Math;\">16 :</span><span style=\"font-family: Cambria Math;\"> 8) is an odd one.</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(a)</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">16 : 8 ,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> +</span><span style=\"font-family: Cambria Math;\"> 4</span><span style=\"font-family: Cambria Math;\"> = 4 + 4 = 8 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 5,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn></msqrt></math> </span><span style=\"font-family: Cambria Math;\">+ </span><span style=\"font-family: Cambria Math;\">3</span><span style=\"font-family: Cambria Math;\"> = 2 + 3 = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">9 :</span><span style=\"font-family: Cambria Math;\"> 6 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>9</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Cambria Math;\">3</span><span style=\"font-family: Cambria Math;\"> = 3 + 3 = 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">36 :</span><span style=\"font-family: Cambria Math;\"> 9, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>36</mn></msqrt></math></span><span style=\"font-family: Cambria Math;\"> +</span><span style=\"font-family: Cambria Math;\"> 3</span><span style=\"font-family: Cambria Math;\"> = 6 + 3 = 9</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> (16 : 8) </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that represents the correct order of the given words as they would appear in an English dictionary.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.Resolve &nbsp; &nbsp; &nbsp; &nbsp;4.Return</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">2.Rodent &nbsp; &nbsp; &nbsp; &nbsp; 5.Respond</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.Resonant &nbsp; &nbsp; 6.Rigidity</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;&#2325;&#2379;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.Resolve &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4.Return</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2.Rodent &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5.Respond</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.Resonant &nbsp; &nbsp; &nbsp; 6.Rigidity</span></p>\r\n<p>&nbsp;</p>\n",
                    options_en: ["<p>1, 3, 5, 4, 2, 6</p>\n", "<p>3, 1, 5, 4, 2, 6</p>\n", 
                                "<p>1, 3, 5, 4, 6, 2</p>\n", "<p>1, 3, 4, 5, 6, 2</p>\n"],
                    options_hi: ["<p>1, 3, 5, 4, 2, 6</p>\n", "<p>3, 1, 5, 4, 2, 6</p>\n",
                                "<p>1, 3, 5, 4, 6, 2</p>\n", "<p>1, 3, 4, 5, 6, 2</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Correct order will </span><span style=\"font-family: Cambria Math;\">be :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Resolve &nbsp;&rarr; </span><span style=\"font-family: Cambria Math;\">Resonant &rarr; </span><span style=\"font-family: Cambria Math;\">Respond &rarr;</span><span style=\"font-family: Cambria Math;\"> Return &rarr;</span><span style=\"font-family: Cambria Math;\"> Rigidity &rarr;</span><span style=\"font-family: Cambria Math;\"> Rodent</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(c) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">: Resolve &rarr;</span><span style=\"font-family: Cambria Math;\"> Resonant &rarr;</span><span style=\"font-family: Cambria Math;\"> Respond &rarr;</span><span style=\"font-family: Cambria Math;\"> Return &rarr;</span><span style=\"font-family: Cambria Math;\"> Rigidity &rarr;</span><span style=\"font-family: Cambria Math;\"> Rodent</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that is embedded in the given figure as its part (rotation is NOT allowed). </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image13.png\" width=\"101\" height=\"93\"></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2344;&#2381;&#2344;&#2367;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2328;&#2369;&#2350;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2350;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image13.png\" width=\"100\" height=\"92\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image14.png\" width=\"56\" height=\"76\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image15.png\" width=\"85\" height=\"47\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image16.png\" width=\"52\" height=\"77\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image17.png\" width=\"54\" height=\"80\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image14.png\" width=\"54\" height=\"74\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image15.png\" width=\"86\" height=\"48\"></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image16.png\" width=\"55\" height=\"82\"><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image17.png\" width=\"57\" height=\"85\"></p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image14.png\" width=\"56\" height=\"77\"></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image14.png\" width=\"65\" height=\"89\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The second number in the given number pairs is obtained by performing certain </span><span style=\"font-family: Cambria Math;\">mathematical</span><span style=\"font-family: Cambria Math;\"> operation(s) on the first number. The same operation(s) are followed in </span><span style=\"font-family: Cambria Math;\">all</span><span style=\"font-family: Cambria Math;\"> the number pairs except one. Find that odd number pair.</span></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2350;</span><span style=\"font-family: Nirmala UI;\">&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2327;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">53 :</span><span style=\"font-family: Cambria Math;\"> 2916</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">41 :</span><span style=\"font-family: Cambria Math;\"> 1765</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">23 :</span><span style=\"font-family: Cambria Math;\"> 576</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">19 :</span><span style=\"font-family: Cambria Math;\"> 400</span></p>\n"],
                    options_hi: ["<p>53 : 2916</p>\n", "<p>41 : 1765</p>\n",
                                "<p>23 : 576</p>\n", "<p>19 : 400</p>\n"],
                    solution_en: "<p>16<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic </span><span style=\"font-family: Cambria Math;\">:-</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">n</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(n+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">53 :</span><span style=\"font-family: Cambria Math;\"> 2916, 53<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(53+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span><span style=\"font-family: Cambria Math;\">= 53 : 54&sup2; </span><span style=\"font-family: Cambria Math;\">= 53 : 2916</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">41 :</span><span style=\"font-family: Cambria Math;\"> 1765, 41<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(41+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span><span style=\"font-family: Cambria Math;\">= 41 : 42&sup2;</span><span style=\"font-family: Cambria Math;\"> = 41 : 1764 </span><span style=\"font-family: Cambria Math;\"> 41 : 1765</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">23 :</span><span style=\"font-family: Cambria Math;\"> 576, 23<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(23+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 23 : 24&sup2;</span><span style=\"font-family: Cambria Math;\"> = 23 : 576</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">19 :</span><span style=\"font-family: Cambria Math;\"> 400, 19<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(19+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span><span style=\"font-family: Cambria Math;\">= 19 : 20&sup2;</span><span style=\"font-family: Cambria Math;\"> = 19 : 400</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Clearly, we can see that (</span><span style=\"font-family: Cambria Math;\">41 :</span><span style=\"font-family: Cambria Math;\"> 1765) is an odd one.</span></p>\n",
                    solution_hi: "<p>16<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :- <span style=\"font-weight: 400;\">n</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(n+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">53 :</span><span style=\"font-family: Cambria Math;\"> 2916 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 53<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(53+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 53 : 54&sup2;</span><span style=\"font-family: Cambria Math;\"> = 53 </span><span style=\"font-family: Cambria Math;\">: 2916</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">41 :</span><span style=\"font-family: Cambria Math;\"> 1765 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 41<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(41+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 41 : 42&sup2;</span><span style=\"font-family: Cambria Math;\"> = 41 : 1764 </span><span style=\"font-family: Cambria Math;\"> 41 : 1765</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">23 :</span><span style=\"font-family: Cambria Math;\"> 576 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 23<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(23+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 23 : 24&sup2;</span><span style=\"font-family: Cambria Math;\"> = 23 : 576</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">19 :</span><span style=\"font-family: Cambria Math;\"> 400 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 19<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(19+1</span><span style=\"font-weight: 400;\">)&sup2;</span></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 19 : 20&sup2;</span><span style=\"font-family: Cambria Math;\"> = 19 : 400</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> (41 : 1765) </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the correct mirror image of the given combination when the mirror is placed at MN as shown. </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image18.png\" width=\"100\" height=\"103\"></p>\n",
                    question_hi: "<p>17<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> MN </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image18.png\" width=\"100\" height=\"103\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image19.png\" width=\"118\" height=\"25\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image20.png\" width=\"118\" height=\"25\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image21.png\" width=\"122\" height=\"26\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image22.png\" width=\"118\" height=\"25\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image19.png\" width=\"118\" height=\"25\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image20.png\" width=\"118\" height=\"25\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image21.png\" width=\"122\" height=\"26\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image22.png\" width=\"118\" height=\"25\"></p>\n"],
                    solution_en: "<p>17<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image21.png\" width=\"118\" height=\"25\"></p>\n",
                    solution_hi: "<p>17<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image21.png\" width=\"118\" height=\"25\"></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which of the following numbers will replace the question mark (?) in the given series?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13, 25, 38, 62, 88, 124, 163, 211</span><span style=\"font-family: Cambria Math;\">, ?</span></p>\n",
                    question_hi: "<p>18<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 , 25 , 38 , 62 , 88 , 124 , 163 , 211 , ?</span></p>\n",
                    options_en: ["<p>253</p>\n", "<p>263</p>\n", 
                                "<p>235</p>\n", "<p>236</p>\n"],
                    options_hi: ["<p>253</p>\n", "<p>263</p>\n",
                                "<p>235</p>\n", "<p>236</p>\n"],
                    solution_en: "<p>18<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image23.png\" width=\"301\" height=\"77\"></p>\n",
                    solution_hi: "<p>18<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image23.png\" width=\"301\" height=\"77\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that is related to the fourth term in the same way as the first term is related to the second term and the fifth term is related to the sixth term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 58 :: ? : </span><span style=\"font-family: Cambria Math;\">370 :</span><span style=\"font-family: Cambria Math;\">: 12 : 153</span></p>\n",
                    question_hi: "<p>19<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Nirmala UI;\">&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2336;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 58 :: ? : </span><span style=\"font-family: Cambria Math;\">370 :</span><span style=\"font-family: Cambria Math;\">: 12 : 153</span></p>\n",
                    options_en: ["<p>20</p>\n", "<p>18</p>\n", 
                                "<p>21</p>\n", "<p>19</p>\n"],
                    options_hi: ["<p>20</p>\n", "<p>18</p>\n",
                                "<p>21</p>\n", "<p>19</p>\n"],
                    solution_en: "<p>19<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic </span><span style=\"font-family: Cambria Math;\">:-</span><span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">n</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">n</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> + 9)</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 58, 7<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(7</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> + 9)</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 7 : (49 + 9) = 7 : 58</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">12 :</span><span style=\"font-family: Cambria Math;\"> 153, 12<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(12</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> + 9)</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 12 : (144 + 9) = 12 : 153</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the missing number </span><span style=\"font-family: Cambria Math;\">be </span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, <span style=\"font-weight: 400;\">&nbsp;y</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> + 9</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 370</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;y<span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> </span>= 361 , </span><span style=\"font-family: Cambria Math;\">y = 19</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, we get </span><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> 370</span></p>\n",
                    solution_hi: "<p>19<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :- </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">n</span><span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(</span><span style=\"font-weight: 400;\">n</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> + 9)</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 58 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 7<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(7</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> + 9)</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 7 : (49 + 9) = 7 : 58</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 :</span><span style=\"font-family: Cambria Math;\"> 153 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 12<span style=\"font-weight: 400;\"> : </span><span style=\"font-weight: 400;\">(12</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> + 9)</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 12 : (144 + 9) = 12 : 153</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2369;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, <span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">(y</span><span style=\"font-weight: 400;\">&sup2;</span><span style=\"font-weight: 400;\"> + 9)</span></span><span style=\"font-family: Cambria Math;\">&nbsp;= 370</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<span style=\"font-weight: 400;\">y</span><span style=\"font-weight: 400;\">&sup2; </span>= 361 , y </span><span style=\"font-family: Cambria Math;\">= 19</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\"> : 370</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the option that is related to the fifth term in t</span><span style=\"font-family: Cambria Math;\">he same way as the second term is related to the first term and fourth term is related to the third term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 :</span><span style=\"font-family: Cambria Math;\"> 130 :: 15 : 180 :: 27 : ?</span></p>\n",
                    question_hi: "<p>20<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 :</span><span style=\"font-family: Cambria Math;\"> 130 :: 15 : 180 :: 27 : ?</span></p>\n",
                    options_en: ["<p>649</p>\n", "<p>658</p>\n", 
                                "<p>650</p>\n", "<p>648</p>\n"],
                    options_hi: ["<p>649</p>\n", "<p>658</p>\n",
                                "<p>650</p>\n", "<p>648</p>\n"],
                    solution_en: "<p>20<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic</span><span style=\"font-family: Cambria Math;\"> :-</span><span style=\"font-family: Cambria Math;\"> n : (n) &times; </span><span style=\"font-family: Cambria Math;\">(n - 3)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">13 :</span><span style=\"font-family: Cambria Math;\"> 130, 13 : (13) &times; </span><span style=\"font-family: Cambria Math;\">(13 - 3) = 13 : 13 &times;</span><span style=\"font-family: Cambria Math;\"> 10 = 13 : 130</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">15 :</span><span style=\"font-family: Cambria Math;\"> 180, 15 : (15) &times;</span><span style=\"font-family: Cambria Math;\"> (15 - 3) = 15 : 15 &times;</span><span style=\"font-family: Cambria Math;\"> 12 = 15 : 180</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, </span><span style=\"font-family: Cambria Math;\">27 :</span><span style=\"font-family: Cambria Math;\"> (?), 27 : (27) &times; </span><span style=\"font-family: Cambria Math;\">(27 - 3) = 27 : 27 &times;</span><span style=\"font-family: Cambria Math;\"> 24 = 27 : </span><span style=\"font-family: Cambria Math;\">648 </span></p>\n",
                    solution_hi: "<p>20<span style=\"font-family: Cambria Math;\">.(d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> :- n : (n) &times;</span><span style=\"font-family: Cambria Math;\"> (n - 3)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 :</span><span style=\"font-family: Cambria Math;\"> 130 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 13 : (13) &times;</span><span style=\"font-family: Cambria Math;\"> (13 - 3) = 13 : 13 &times;</span><span style=\"font-family: Cambria Math;\"> 10 = 13 : 130</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15 :</span><span style=\"font-family: Cambria Math;\"> 180 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 15 : (15) &times;</span><span style=\"font-family: Cambria Math;\"> (15 - 3) = 15 : 15 &times;</span><span style=\"font-family: Cambria Math;\"> 12 = 15 : 180</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\">, 27 : (?) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 27 : (27) &times;</span><span style=\"font-family: Cambria Math;\"> (27 - 3) = 27 : 27 &times;</span><span style=\"font-family: Cambria Math;\"> 24 = 27 : </span><span style=\"font-family: Cambria Math;\">648 </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option that is related to the fourth term in the same way as the fi</span><span style=\"font-family: Cambria Math;\">rst term is related to the second term and the fifth term is related to the sixth term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">64 :</span><span style=\"font-family: Cambria Math;\"> 320 :: ? : </span><span style=\"font-family: Cambria Math;\">70 :</span><span style=\"font-family: Cambria Math;\">: 22 : 110</span></p>\n",
                    question_hi: "<p>21<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2380;&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;</span><span style=\"font-family: Nirmala UI;\">&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2336;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">64 :</span><span style=\"font-family: Cambria Math;\"> 320 :: ? : </span><span style=\"font-family: Cambria Math;\">70 :</span><span style=\"font-family: Cambria Math;\">: 22 : 110</span></p>\n",
                    options_en: ["<p>12</p>\n", "<p>14</p>\n", 
                                "<p>8</p>\n", "<p>17</p>\n"],
                    options_hi: ["<p>12</p>\n", "<p>14</p>\n",
                                "<p>8</p>\n", "<p>17</p>\n"],
                    solution_en: "<p>21<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic</span><span style=\"font-family: Cambria Math;\"> :-</span><span style=\"font-family: Cambria Math;\"> n : n &times;</span><span style=\"font-family: Cambria Math;\"> 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">64 :</span><span style=\"font-family: Cambria Math;\"> 320, 64 : 64 &times;</span><span style=\"font-family: Cambria Math;\"> 5 = 64 : 320</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In </span><span style=\"font-family: Cambria Math;\">22 :</span><span style=\"font-family: Cambria Math;\"> 110, 22 : 22 &times; </span><span style=\"font-family: Cambria Math;\">5 = 22 : 110</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, in (?</span><span style=\"font-family: Cambria Math;\">) :</span><span style=\"font-family: Cambria Math;\"> 70, </span><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\"> : </span><span style=\"font-family: Cambria Math;\">14 &times; </span><span style=\"font-family: Cambria Math;\">5 = </span><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\"> : 70</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the missing number is 14.</span></p>\n",
                    solution_hi: "<p>21<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">:- n : n &times; </span><span style=\"font-family: Cambria Math;\">5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">64 :</span><span style=\"font-family: Cambria Math;\"> 320 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 64 : 64 &times; </span><span style=\"font-family: Cambria Math;\">5 = 64 : 320</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">22 :</span><span style=\"font-family: Cambria Math;\"> 110 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, 22 : 22 &times;</span><span style=\"font-family: Cambria Math;\"> 5 = 22 : 110</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\">, (?) : 70 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;</span><span style=\"font-family: Nirmala UI;\">&#2306;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\"> : </span><span style=\"font-family: Cambria Math;\">14 &times;</span><span style=\"font-family: Cambria Math;\"> 5 = </span><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\"> : 70</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2369;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 14 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">In a certain code language, \'RAIN\' is written as \'MHZQ\' and &lsquo;COAT&rsquo; is written as &lsquo;SZNB&rsquo;. How will \'ROLE\' be written in that language?</span></p>\n",
                    question_hi: "<p>22<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, \'RAIN\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Cambria Math;\">\'MHZQ\' </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'COAT\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'SZNB\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> &nbsp;</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'ROLE\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>DKNQ</p>\n", "<p>QDKN</p>\n", 
                                "<p>KQDN</p>\n", "<p>NDKQ</p>\n"],
                    options_hi: ["<p>DKNQ</p>\n", "<p>QDKN</p>\n",
                                "<p>KQDN</p>\n", "<p>NDKQ</p>\n"],
                    solution_en: "<p>22<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image24.png\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image25.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image26.png\"></p>\n",
                    solution_hi: "<p>22<span style=\"font-family: Cambria Math;\">.(a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image24.png\"><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image25.png\"></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image26.png\"></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> In a certain code language, \'SITAR\' is written as \'RHSZQ\' and &lsquo;BANJO&rsquo; is written as &lsquo;AZMIN&rsquo;. How will \'TABLA\' be written in that language?</span></p>\n",
                    question_hi: "<p>23.<span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, \'SITAR\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'RHSZQ\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'BANJ</span><span style=\"font-family: Cambria Math;\">O\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'AZMIN\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'TABLA\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>SAZKZ</p>\n", "<p>SZAKZ</p>\n", 
                                "<p>SZKAZ</p>\n", "<p>SZAZA</p>\n"],
                    options_hi: ["<p>SAZKZ</p>\n", "<p>SZAKZ</p>\n",
                                "<p>SZKAZ</p>\n", "<p>SZAZA</p>\n"],
                    solution_en: "<p>23<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image27.png\">&nbsp; &nbsp; &nbsp; &nbsp; <img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image28.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Similarly, Similarly, Similarly,</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image29.png\" width=\"124\" height=\"84\"></p>\n",
                    solution_hi: "<p>23<span style=\"font-family: Cambria Math;\">.(b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image27.png\"><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image28.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image29.png\" width=\"129\" height=\"87\"></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the option figure which is embedded in the given figure as its part (rotation is NOT allowed). </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image30.png\" width=\"102\" height=\"65\"></p>\n",
                    question_hi: "<p>24<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2344;&#2381;&#2344;&#2367;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2328;&#2369;&#2350;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2350;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image30.png\" width=\"100\" height=\"64\"></p>\n",
                    options_en: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image31.png\" width=\"75\" height=\"77\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image32.png\" width=\"74\" height=\"71\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image33.png\" width=\"66\" height=\"78\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image34.png\" width=\"79\" height=\"59\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image31.png\" width=\"72\" height=\"74\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image32.png\" width=\"70\" height=\"67\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image33.png\" width=\"60\" height=\"71\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image34.png\" width=\"82\" height=\"61\"></p>\n"],
                    solution_en: "<p>24<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image33.png\" width=\"76\" height=\"90\"></p>\n",
                    solution_hi: "<p>24<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image33.png\" width=\"65\" height=\"77\"></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Pointing towards a man, Rahul said, His son&rsquo;s son is the father of my mother&rsquo;s daughter. How is that person related to </span><span style=\"font-family: Cambria Math;\">Rahul ?</span></p>\n",
                    question_hi: "<p>25<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2358;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;</span><span style=\"font-family: Nirmala UI;\">&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2361;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> - &ldquo;</span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">&rdquo;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2361;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>grandfather</p>\n", "<p>brother of father&rsquo;s father</p>\n", 
                                "<p>great-grandfather</p>\n", "<p>father&rsquo;s brother</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2312;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2342;&#2366;&#2342;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2312;</span></p>\n"],
                    solution_en: "<p>25<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">After arranging the persons as per directions given question, we get following family </span><span style=\"font-family: Cambria Math;\">tree :</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image35.png\" width=\"122\" height=\"161\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Clearly, we can see that the person is either Great Grandfather or Great Grandmother of Rahul.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In options, only Great Grandfather is given, so option (c) is the</span><span style=\"font-family: Cambria Math;\"> correct answer.</span></p>\n",
                    solution_hi: "<p>25<span style=\"font-family: Cambria Math;\">.(c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2306;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669527061/word/media/image35.png\" width=\"120\" height=\"159\"></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2342;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2342;&#2366;&#2342;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2342;&#2366;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;</span><span style=\"font-family: Nirmala UI;\">&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (C) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
           // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${question.question_en}</div>
                        <div class="hi" style="display:none">${question.question_hi}</div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>