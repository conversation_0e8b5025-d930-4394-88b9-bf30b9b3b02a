<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "1. Find out why white silver chloride turns grey in sunlight.",
                    question_hi: "1. पता कीजिए कि श्वेत रंग का सिल्वर क्लोराइड सूर्य के प्रकाश में धूसर रंग का क्यों हो जाता है।",
                    options_en: [" Due to rusting of silver in presence of oxygen", " Due to redox reaction", 
                                " Due to the decomposition of silver chloride into silver and chlorine by light", " Due to the displacement of silver chloride to silver oxide"],
                    options_hi: [" ऑक्सीजन की उपस्थिति में सिल्वर में जंग लगने के कारण", " अपचयोपचय (रेडॉक्स) अभिक्रिया के कारण",
                                " प्रकाश द्वारा सिल्वर क्लोराइड के सिल्वर और क्लोरीन में अपघटन के कारण", " सिल्वर क्लोराइड के सिल्वर ऑक्साइड में विस्थापन के कारण"],
                    solution_en: "1.(c) Silver chloride (AgCl) is a photosensitive compound, which means it reacts to light by decomposing. When silver chloride is exposed to sunlight, the energy from the light causes the compound to break down into silver and chlorine ions. The silver ions combine with other substances in the environment to form a white precipitate, which gives the appearance of the silver chloride turning white.",
                    solution_hi: "1.(c) सिल्वर क्लोराइड  (AgCl) एक प्रकाश-संवेदनशील यौगिक है, जिसका अर्थ है कि  यह प्रकाश के प्रति अपघटन द्वारा अभिक्रिया करता है। जब सिल्वर क्लोराइड सूर्य के प्रकाश के संपर्क में  लाया जाता है, तो प्रकाश से प्राप्त ऊर्जा यौगिक को सिल्वर और क्लोरीन आयनों में विघटित कर देती है। सिल्वर  आयन वातावरण में मौजूद अन्य पदार्थों के साथ संयोजित होकर एक सफेद अवक्षेप बनाते हैं, जिससे ऐसा प्रतीत होता है कि सिल्वर  क्लोराइड सफेद हो गया है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. Why does the milkman add a very small amount of baking soda to fresh milk?",
                    question_hi: "2. दूधवाला ताजे दूध में बहुत कम मात्रा में बेकिंग सोडा क्यों मिलाता है?",
                    options_en: [" To reduce the pH of the fresh milk from 6 to slightly more acidic", " To increase the pH of the fresh milk from 6 to slightly alkaline", 
                                " To maintain the pH of the fresh milk at 6 for a longer time", " To reduce the pH of the fresh milk from 6 to slightly alkaline"],
                    options_hi: [" ताजे दूध का pH, 6 से घटाकर थोड़ा अधिक अम्लीय करने के लिए", " ताजे दूध का pH, 6 से बढ़ाकर थोड़ा क्षारीय करने के लिए",
                                " ताजे दूध का pH लंबे समय तक 6 पर बनाए रखने के लिए", " ताजे दूध का pH, 6 से घटाकर थोड़ा क्षारीय करने के लिए"],
                    solution_en: "<p>2.(b) A milkman adds a small amount of baking soda to fresh milk to prevent its acidification, allowing him to store it for a longer time. This is because milk in an alkaline condition does not curdle easily. Sodium bicarbonate, commonly known as baking soda or cooking soda, has the chemical formula NaHCO<sub>3</sub>. Uses: Cooking - Acts as a leavening agent in baking, helping dough rise. Fire extinguishing: Used in some types of fire extinguishers to put out small grease or electrical fires.</p>",
                    solution_hi: "<p>2.(b) एक दूधवाला ताजे दूध को अम्लीय होने से बचाने के लिए उसमें थोड़ी मात्रा में बेकिंग सोडा मिला देता है, जिससे वह उसे लम्बे समय तक संग्रहीत कर सकता है। ऐसा इसलिए है क्योंकि क्षारीय अवस्था में दूध आसानी से नहीं फटता है। सोडियम बाइकार्बोनेट, जिसे आमतौर पर बेकिंग सोडा या कुकिंग सोडा के रूप में जाना जाता है, का रासायनिक सूत्र NaHCO<sub>3</sub> है। उपयोग: खाना बनाना - बेकिंग में एक खमीर घटक के रूप में कार्य करता है, जिससे आटा फूलने में मदद मिलती है। आग बुझाना : कुछ प्रकार के अग्निशामक यंत्रों में इसका उपयोग छोटी-मोटी ग्रीस या बिजली की आग को बुझाने के लिए किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. In the context of periodicity, a unit called picometre is used to measure the __________.</p>",
                    question_hi: "<p>3. आवर्तता (पीरियोडिसिटी) के संदर्भ में ________ को मापने के लिए पिकोमीटर नामक इकाई का उपयोग किया जाता है।</p>",
                    options_en: ["<p>atomic radius</p>", "<p>molar mass</p>", 
                                "<p>atomic density</p>", "<p>spin quantum number</p>"],
                    options_hi: ["<p>परमाणु त्रिज्या</p>", "<p>मोलर द्रव्यमान</p>",
                                "<p>परमाणु घनत्व</p>", "<p>स्पिन क्वांटम संख्या</p>"],
                    solution_en: "<p>3.(a) <strong>atomic radius</strong> (Atomic Radii). It is the total distance from the nucleus of an atom to the outermost orbital of its electron. In chemistry, the molar mass of a chemical compound is defined as the ratio between the mass and the amount of substance of any sample of the compound. The atomic number density is the number of atoms of a given type per unit volume (V, cm<sup>3</sup>) of the material.</p>",
                    solution_hi: "<p>3.(a) <strong>परमाणु त्रिज्या।</strong> यह किसी परमाणु के नाभिक से उसके इलेक्ट्रॉन की सबसे बाहरी कक्षा तक की कुल दूरी है। रसायन विज्ञान में, किसी रासायनिक यौगिक के मोलर द्रव्यमान को यौगिक के किसी भी सैम्पल के द्रव्यमान और पदार्थ की मात्रा के बीच के अनुपात के रूप में परिभाषित किया जाता है। परमाणु संख्या घनत्व पदार्थ के प्रति इकाई आयतन (V, cm<sup>3</sup>) में किसी दिए गए प्रकार के परमाणुओं की संख्या है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following statements are true based on the 4NH<sub>3</sub>(g) + 5O<sub>2</sub>(g) &rarr; 4NO(g) + 6H<sub>2</sub>O(g) ? <br>(i) N gets oxidized. <br>(ii) O gets oxidized. <br>(iii) N gets reduced. <br>(iv) O gets reduced</p>",
                    question_hi: "<p>4. 4NH<sub>3</sub>(g) + 5O<sub>2</sub>(g) &rarr; 4NO(g) + 6H<sub>2</sub>O(g) के आधार पर, निम्नलिखित में से कौन-से कथन सत्य हैं?<br>(i) N उपचयित हो जाता है।<br>(ii) O उपचयित हो जाता है।<br>(iii) N अपचयित हो जाता है।<br>(iv) O अपचयित हो जाता है।</p>",
                    options_en: ["<p>(i) and (iv)</p>", "<p>(i) and (ii)</p>", 
                                "<p>(iii) and (iv)</p>", "<p>(ii) and (iii)</p>"],
                    options_hi: ["<p>(i) और (iv)</p>", "<p>(i) और (ii)</p>",
                                "<p>(iii) और (iv)</p>", "<p>(ii) और (iii)</p>"],
                    solution_en: "<p>4.(a)<strong> (i) and (iv). </strong>The reaction 4NH<sub>3</sub>(g) + 5O<sub>2</sub>(g) &rarr; 4NO(g) + 6H<sub>2</sub>O(g) is an example of a redox reaction, which means it involves both oxidation and reduction. The nitrogen in ammonia (NH<sub>3</sub>) is oxidized when oxygen is added. The oxygen (O<sub>2</sub>) is reduced when hydrogen is added to form water (H<sub>2</sub>O). Ammonia acts as a reducing agent. Oxygen acts as an oxidizing agent.</p>",
                    solution_hi: "<p>4.(a) <strong>(i) और (iv).</strong> अभिक्रिया 4NH<sub>3</sub>(g) + 5O<sub>2</sub>(g) &rarr; 4NO(g) + 6H<sub>2</sub>O(g) रेडॉक्स अभिक्रिया का एक उदाहरण है, जिसका अर्थ है कि इसमें ऑक्सीकरण और अपचयन दोनों शामिल हैं। अमोनिया (NH<sub>3</sub>) में नाइट्रोजन ऑक्सीजन मिलाने पर ऑक्सीकृत हो जाता है। ऑक्सीजन (O<sub>2</sub>) अपचयित हो जाता है जब हाइड्रोजन जुड़कर जल (H<sub>2</sub>O) बनता है। अमोनिया एक अपचायक एजेंट के रूप में कार्य करता है। ऑक्सीजन एक ऑक्सीकारक के रूप में कार्य करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which number is called Avogadro\'s constant, named after the 19<sup>th </sup>century scientist Amedeo Avogadro?</p>",
                    question_hi: "<p>5. किस संख्या को आवोगाद्रो नियतांक कहा जाता है, जिसका नाम 19वीं शताब्दी के वैज्ञानिक एमेडियो अवोगाद्रो (Amedeo Avogadro) के नाम पर रखा गया है?</p>",
                    options_en: ["<p>6.022 &times; 10<sup>23</sup></p>", "<p>6.020 &times; 10<sup>20</sup></p>", 
                                "<p>6.032 &times; 10<sup>19</sup></p>", "<p>6.012 &times; 10<sup>21</sup></p>"],
                    options_hi: ["<p>6.022 &times; 10<sup>23</sup></p>", "<p>6.020 &times; 10<sup>20</sup></p>",
                                "<p>6.032 &times; 10<sup>19</sup></p>", "<p>6.012 &times; 10<sup>21</sup></p>"],
                    solution_en: "<p>5.(a) <strong>6.022 &times; 10<sup>23</sup>.</strong> The number of particles (atoms, molecules or ions) present in 1 mole of any substance is fixed, with a value of 6.022 &times; 10<sup>23</sup>. This is an experimentally obtained value. This number is called the Avogadro Constant or Avogadro Number (represented by N<sub>A</sub> ), named in honour of the Italian scientist, Amedeo Avogadro.</p>",
                    solution_hi: "<p>5.(a) <strong>6.022 &times; 10<sup>23</sup>.</strong> किसी भी पदार्थ के 1 मोल में मौजूद कणों (परमाणु, अणु या आयन) की संख्या निश्चित होती है, जिसका मान 6.022 &times; 10<sup>23</sup> होता है। यह प्रयोगात्मक रूप से प्राप्त मान है। इस संख्या को आवोगाद्रो स्थिरांक या आवोगाद्रो संख्या (N<sub>A</sub> द्वारा दर्शाया गया) कहा जाता है, जिसका नाम इतालवी वैज्ञानिक, अमेडियो आवोगाद्रो के सम्मान में रखा गया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Identify a monoatomic molecule.</p>",
                    question_hi: "<p>6. एक परमाणुक अणु की पहचान कीजिए।</p>",
                    options_en: ["<p>carbon monoxide</p>", "<p>Oxygen</p>", 
                                "<p>Helium</p>", "<p>Chlorine</p>"],
                    options_hi: ["<p>कार्बन मोनोऑक्साइड</p>", "<p>ऑक्सीजन</p>",
                                "<p>हीलियम</p>", "<p>क्लोरीन</p>"],
                    solution_en: "<p>6.(c) <strong>Helium </strong>(He) is a noble gas (Group 18 in the periodic table). Molecules: These are made up of one or more atoms and can be classified into three types: Monatomic, Diatomic and Triatomic. Monatomic molecules: Contain a single atom without covalent bonding, such as noble gases like Helium (He), Neon (Ne), and Argon (Ar). Diatomic molecule: Contains two atoms bonded together, for example hydrogen and oxygen. Triatomic molecules: These are made up of three atoms, which may be the same or different elements, such as ozone (O<sub>3</sub>).</p>",
                    solution_hi: "<p>6.(c) <strong>हीलियम </strong>(He) एक उत्कृष्ट गैस है (आवर्त सारणी में समूह 18)। अणु: ये एक या एक से अधिक परमाणुओं से बने होते हैं और इन्हें तीन प्रकारों से वर्गीकृत किया जा सकता है: मोनोएटोमिक, डायटोमिक और ट्राइएटोमिक। मोनोएटोमिक अणु: बिना सहसंयोजक बंध के एकल परमाणु होते हैं, जैसे हीलियम (He), नियॉन (Ne), और आर्गन (Ar) जैसी उत्कृष्ट गैसें। डायटोमिक अणु: इसमें दो परमाणु एक साथ बंधे होते हैं, उदाहरण के लिए हाइड्रोजन और ऑक्सीजन। ट्राइएटोमिक अणु: ये तीन परमाणुओं से बने होते हैं, जो एक ही या अलग-अलग तत्व हो सकते हैं, जैसे ओजोन (O<sub>3</sub>)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following chemical reactions takes place when quick lime reacts with water?</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन-सी रासायनिक अभिक्रिया तब होती है जब अनबुझा चूना जल के साथ अभिक्रिया करता है?</p>",
                    options_en: ["<p>CaO(s) + H<sub>2</sub>O(l) &rarr; Ca + H<sub>2</sub> (aq)</p>", "<p>C(s) + O<sub>2</sub> (g) &rarr; CO<sub>2</sub> (g)</p>", 
                                "<p>CaO(s) + H<sub>2</sub>O(l) &rarr; Ca(OH)<sub>2</sub> (aq)</p>", "<p>CH<sub>4</sub> (g) + 2O<sub>2</sub> (g) &rarr; CO<sub>2</sub> (g) + 2H<sub>2</sub>O (g)</p>"],
                    options_hi: ["<p>CaO(s) + H<sub>2</sub>O(l) &rarr; Ca + H<sub>2</sub> (aq)</p>", "<p>C(s) + O<sub>2</sub> (g) &rarr; CO<sub>2</sub> (g)</p>",
                                "<p>CaO(s) + H<sub>2</sub>O(l) &rarr; Ca(OH)<sub>2</sub> (aq)</p>", "<p>CH<sub>4</sub> (g) + 2O<sub>2</sub> (g) &rarr; CO<sub>2</sub> (g) + 2H<sub>2</sub>O (g)</p>"],
                    solution_en: "<p>7.(c) <strong>CaO<sub>(s)</sub> + H<sub>2</sub>O<sub>(l)</sub> &rarr; Ca(OH)<sub>2(aq)</sub>. </strong>इस अभिक्रिया को चूने का बुझना कहते हैं, जिसमें अनबुझा हुआ चूना (कैल्शियम ऑक्साइड) जल के साथ अभिक्रिया करके कैल्शियम हाइड्रॉक्साइड बनाता है।</p>",
                    solution_hi: "<p>7.(c) <strong>CaO<sub>(s)</sub> + H<sub>2</sub>O<sub>(l)</sub> &rarr; Ca(OH)<sub>2(aq)</sub>.</strong> This reaction is known as slaking of lime, where quicklime (calcium oxide) reacts with water to form calcium hydroxide.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which two organic chemists are known for observing the peroxide effect in adding reagents to unsaturated compounds in 1933?</p>",
                    question_hi: "<p>8. 1933 में असंतृप्त यौगिकों में अभिकर्मकों को मिलाने पर परॉक्साइड प्रभाव को देखने के लिए कौन-से दो कार्बनिक रसायनज्ञ जाने जाते हैं?</p>",
                    options_en: ["<p>Morris S Kharasch and Frank R Mayo</p>", "<p>C John Cadogan and Luis M Campos</p>", 
                                "<p>B Steven Bachrach and Roald Hoffmann</p>", "<p>Justus von Liebig and Friedrich W&ouml;hler</p>"],
                    options_hi: ["<p>मॉरिस एस खराश और फ्रैंक आर मेयो (Morris S Kharasch and Frank R Mayo)</p>", "<p>सी जॉन कैडोगन और लुइस एम कैम्पोस (C John Cadogan and Luis M Campos)</p>",
                                "<p>बी स्टीवन बैचराच और रोआल्ड हॉफमैन (B Steven Bachrach and Roald Hoffmann)</p>", "<p>जस्टस वॉन लिबिग और फ्रेडरिक वोहलर (Justus von Liebig and Friedrich W&ouml;hler)</p>"],
                    solution_en: "<p>8.(a) <strong>Morris S Kharasch and Frank R Mayo.</strong> The peroxide effect, or Kharasch effect, is an exception to <br>Markovnikov\'s rule, describing the addition of hydrogen bromide (HBr) to unsymmetrical alkenes in the presence of peroxides. This effect results in products that differ from those predicted by Markovnikov\'s rule. Kharasch and Mayo\'s paper, \"The Peroxide Effect in the Addition of Reagents to Unsaturated Compounds. I. The Addition of Hydrogen Bromide to Allyl Bromide,\" was published in the Journal of the American Chemical Society in 1933.</p>",
                    solution_hi: "<p>8.(a) <strong>मॉरिस एस खराश और फ्रैंक आर मेयो </strong>(Morris S Kharasch and Frank R Mayo)। पेरोक्साइड प्रभाव, या खराश प्रभाव, मार्कोवनिकोव के नियम का एक अपवाद है, जो पेरोक्साइड की उपस्थिति में असममित एल्कीन पर हाइड्रोजन ब्रोमाइड (HBr) के योग का वर्णन करता है। इस प्रभाव के परिणामस्वरूप ऐसे उत्पाद बनते हैं जो मार्कोवनिकोव के नियम द्वारा अनुमानित उत्पादों से भिन्न होते हैं। खराश और मेयो का शोधपत्र, \"असंतृप्त यौगिकों में अभिकर्मकों के योग में पेरोक्साइड प्रभाव। एलिल ब्रोमाइड में हाइड्रोजन ब्रोमाइड का योग,\" जर्नल ऑफ द अमेरिकन केमिकल सोसाइटी में 1933 में प्रकाशित हुआ था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. For a chemical reaction with a rise in temperature by 10&deg;, the rate constant becomes nearly ____________________.</p>",
                    question_hi: "<p>9. तापमान में 10&deg; की वृद्धि के साथ एक रासायनिक अभिक्रिया के लिए, दर स्थिरांक लगभग _____________ हो जाता है।</p>",
                    options_en: ["<p>Double</p>", "<p>Triple</p>", 
                                "<p>one-fourth</p>", "<p>half</p>"],
                    options_hi: ["<p>दोगुना</p>", "<p>तीन गुना</p>",
                                "<p>एक-चौथाई</p>", "<p>आधा</p>"],
                    solution_en: "<p>9.(a) <strong>Double</strong>. ​The rate constant is defined as the proportionality constant which explains the relationship between the molar concentration of reactants and the rate of a chemical reaction.</p>",
                    solution_hi: "<p>9.(a) <strong>दोगुना</strong>। दर स्थिरांक को आनुपातिकता स्थिरांक के रूप में परिभाषित किया जाता है जो अभिकारकों की मोलर सांद्रता और रासायनिक अभिक्रिया की दर के बीच संबंध को स्पष्ट करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. As per Mendeleev&rsquo;s prediction, atomic mass of eka-aluminium was:</p>",
                    question_hi: "<p>10. मेंडलीफ के अनुमान के अनुसार,एलुमिनियम का परमाणु द्रव्यमान कितना था?</p>",
                    options_en: ["<p>100</p>", "<p>44</p>", 
                                "<p>72</p>", "<p>68</p>"],
                    options_hi: ["<p>100</p>", "<p>44</p>",
                                "<p>72</p>", "<p>68</p>"],
                    solution_en: "<p>10.(d)<strong> 68. </strong>Mendeleev left gaps for aluminum and silicon in his periodic table and referred to them as Eka-Aluminum and Eka-Silicon. Mendeleev&rsquo;s Predictions for the Elements Eka-aluminium (Gallium) and Eka-silicon (Germanium): He predicted the atomic weight of Eka-Aluminum to be 68, while the discovered atomic weight of gallium is 69.72. He also predicted the atomic weight of Eka-Silicon to be 72, and the found atomic weight of germanium is 72.6.</p>",
                    solution_hi: "<p>10.(d)<strong> 68. </strong>मेंडलीफ ने अपनी आवर्त सारणी में एल्युमिनियम और सिलिकॉन के लिए रिक्त स्थान छोड़ दिए थे और उन्हें एका-एल्युमिनियम और एका-सिलिकॉन के नाम से संदर्भित किया। एका-एल्युमिनियम (गैलियम) और एका-सिलिकॉन (जर्मेनियम) तत्वों के लिए मेंडलीफ की भविष्यवाणियाँ: उन्होंने एका-एल्युमिनियम का परमाणु द्रव्यमान 68 होने की भविष्यवाणी की, जबकि गैलियम का खोजा गया परमाणु द्रव्यमान 69.72 है। उन्होंने यह भी भविष्यवाणी की कि एका-सिलिकॉन का परमाणु द्रव्यमान 72 है, और जर्मेनियम का पाया गया परमाणु द्रव्यमान 72.6 है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. In Newlands&rsquo; Octaves, the properties of lithium and ________ were found to be the same.</p>",
                    question_hi: "<p>11. न्यूलैंड्स के अष्टक में लीथियम और ________ के गुण समान पाए गए।</p>",
                    options_en: ["<p>sodium</p>", "<p>aluminium</p>", 
                                "<p>magnesium</p>", "<p>beryllium</p>"],
                    options_hi: ["<p>सोडियम</p>", "<p>एलुमिनियम</p>",
                                "<p>मैग्नीशियम</p>", "<p>बेरीलियम</p>"],
                    solution_en: "<p>11.(a) <strong>sodium</strong>. In 1865, the English chemist J.A.R. Newlands proposed the Law of Octaves. It states that when chemical elements are arranged by increasing atomic mass, elements with similar physical and chemical properties appear after every interval of seven. Examples include Lithium (Li) and Sodium (Na), as well as Beryllium (Be) and Magnesium (Mg). Limitations : Only applicable up to calcium, assumed only 56 elements existed in nature, and became irrelevant after the discovery of the Noble gases.</p>",
                    solution_hi: "<p>11.(a) <strong>सोडियम</strong>। 1865 में, अंग्रेज़ रसायनज्ञ जे.ए.आर. न्यूलैंड्स द्वारा अष्टक नियम प्रस्तावित किया गया था। यह नियम बताता है कि जब रासायनिक तत्वों को बढ़ते परमाणु द्रव्यमान के अनुसार व्यवस्थित किया जाता है, तो समान भौतिक और रासायनिक गुणों वाले तत्व प्रत्येक सात के अंतराल के बाद दिखाई देते हैं। उदाहरणों में लिथियम (Li) और सोडियम (Na), साथ ही बेरिलियम (Be) और मैग्नीशियम (Mg) शामिल हैं। सीमाएँ: केवल कैल्शियम तक लागू होती है, माना जाता है कि प्रकृति में केवल 56 तत्व मौजूद थे, और नोबल गैसों की खोज के बाद यह अप्रासंगिक हो गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. A chemical reaction in which the rate of reaction is directly proportional to the first power of the concentration of the reacting substance is called:</p>",
                    question_hi: "<p>12. एक रासायनिक अभिक्रिया, जिसमें अभिक्रिया की दर अभिकारक के सांद्रता की प्रथम घात के अनुक्रमानुपाती होती है, _______कहलाती है।</p>",
                    options_en: ["<p>zero order reaction</p>", "<p>third order reaction</p>", 
                                "<p>second order reaction</p>", "<p>first order reaction</p>"],
                    options_hi: ["<p>शून्य कोटि की अभिक्रिया</p>", "<p>तृतीय कोटि की अभिक्रिया</p>",
                                "<p>द्वितीय कोटि की अभिक्रिया</p>", "<p>प्रथम कोटि की अभिक्रिया</p>"],
                    solution_en: "<p>12.(d)<strong> first order reaction. </strong>Mathematically, this is represented as: Rate = k[A]<br>Where: Rate is the rate of reaction, k is the rate constant, [A] is the concentration of the reacting substance. <br>Zero-order reaction: Rate is independent of concentration (Rate = k)<br>Second-order reaction: Rate is proportional to the second power of concentration (Rate = k[A]<sup>2</sup>)</p>",
                    solution_hi: "<p>12.(d) <strong>प्रथम कोटि की अभिक्रिया। </strong>गणितीय रूप से, इसे इस प्रकार दर्शाया जाता है: दर = k[A]<br>जहाँ: दर अभिक्रिया की दर है, k दर स्थिरांक है, [A] अभिकारक पदार्थ की सांद्रता है।<br>शून्य-कोटि की अभिक्रिया: दर सांद्रता से स्वतंत्र होती है (दर = k)<br>द्वितीय-कोटि की अभिक्रिया: दर सांद्रता की दूसरी घात के समानुपाती होती है (दर = k[A]<sup>2</sup>)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. What is the gas evolved when zinc reacts with sulfuric acid?</p>",
                    question_hi: "<p>13. जिंक के सल्फ्यूरिक अम्&zwj;ल के साथ अभिक्रिया करने पर कौन सी गैस निकलती है?</p>",
                    options_en: ["<p>Hydrogen</p>", "<p>Oxygen</p>", 
                                "<p>Carbon dioxide</p>", "<p>Hydrogen sulfide</p>"],
                    options_hi: ["<p>हाइड्रोजन</p>", "<p>ऑक्सीजन</p>",
                                "<p>कार्बन डाइऑक्साइड</p>", "<p>हाइड्रोजन सल्फाइड</p>"],
                    solution_en: "<p>13.(a) <strong>Hydrogen</strong>. Zinc (Zn) is a metal, and sulfuric acid (H<sub>2</sub>SO<sub>4</sub>) is a strong acid. When they react, the metal (Zinc) displaces the hydrogen from the acid. The reaction can be represented by the following chemical equation:<br>Zn + H<sub>2</sub>SO<sub>4 </sub>&rarr; ZnSO<sub>4</sub> + H<sub>2</sub>.</p>",
                    solution_hi: "<p>13.(a) <strong>हाइड्रोजन</strong>। जिंक (Zn) एक धातु है, और सल्फ्यूरिक अम्ल (H<sub>2</sub>SO<sub>4</sub>) एक प्रबल अम्ल है। जब वे अभिक्रिया करते हैं, तो धातु (जिंक) अम्ल से हाइड्रोजन को विस्थापित कर देती है। अभिक्रिया को निम्नलिखित रासायनिक समीकरण द्वारा दर्शाया जा सकता है:<br>Zn + H<sub>2</sub>SO<sub>4 </sub>&rarr; ZnSO<sub>4</sub> + H<sub>2</sub>.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Who reconciled Dalton\'s atomic hypothesis with Gay-Lussac\'s results on the combination of volumes in 1811 ?</p>",
                    question_hi: "<p>14. 1811 में आयतनों के संयोजन (combination of volumes) पर गे-लुसाक के परिणामों के साथ डाल्टन की परमाण्वीय परिकल्पना का मिलान किसने किया था?</p>",
                    options_en: ["<p>Robert Boyle</p>", "<p>Amadeo Avogadro</p>", 
                                "<p>Fred Hoyle</p>", "<p>Jacques Charles</p>"],
                    options_hi: ["<p>रॉबर्ट बॉयल (Robert Boyle)</p>", "<p>अमेदिओ अवोगाद्रो (Amadeo Avogadro)</p>",
                                "<p>फ़्रेड हॉयल (Fred Hoyle)</p>", "<p>जैक्स चार्ल्स (Jacques Charles)</p>"],
                    solution_en: "<p>14.(b) <strong>Amadeo Avogadro.</strong> Avogadro\'s notable paper sought to reconcile Dalton\'s atomic hypothesis with Gay-Lussac\'s findings on combining volumes. Dalton\'s theory, grounded in the law of conservation of mass and the law of constant composition, was the first comprehensive explanation of matter in terms of atoms and formed the foundation of chemistry. Dalton\'s atomic theory includes the following key points: atoms of the same element are identical; atoms of different elements differ; compounds consist of various types of atoms; and chemical reactions rearrange atoms.</p>",
                    solution_hi: "<p>14.(b) <strong>अमेदिओ अवोगाद्रो। </strong>अवोगाद्रो के उल्लेखनीय शोधपत्र में डाल्टन की परमाणु परिकल्पना को गे-लुसाक के आयतन संयोजन संबंधी निष्कर्षों के साथ सामंजस्य स्थापित करने का प्रयास किया गया था। डाल्टन का सिद्धांत, द्रव्यमान के संरक्षण के नियम और निरंतर संरचना के नियम पर आधारित, परमाणुओं के संदर्भ में पदार्थ की पहली व्यापक व्याख्या थी और इसने रसायन विज्ञान की नींव रखी। डाल्टन के परमाणु सिद्धांत में निम्नलिखित मुख्य बिंदु शामिल हैं: एक ही तत्व के परमाणु समान होते हैं; विभिन्न तत्वों के परमाणु भिन्न-भिन्न होते हैं; यौगिक विभिन्न प्रकार के परमाणुओं से मिलकर बने होते हैं; और रासायनिक अभिक्रियाएँ परमाणुओं को पुनर्व्यवस्थित करती हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following is NOT a physical change?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन-सा भौतिक परिवर्तन नहीं है?</p>",
                    options_en: ["<p>Heating of iron rod to red hot</p>", "<p>Curdling of milk</p>", 
                                "<p>Evaporation of diesel</p>", "<p>Sublimation of NH4Cl</p>"],
                    options_hi: ["<p>लोहे की छड़ को गर्म करके लाल करना</p>", "<p>दूध से दही बनना</p>",
                                "<p>डीजल का वाष्पीकरण</p>", "<p>NH4Cl का उर्ध्वपातन</p>"],
                    solution_en: "<p>15.(b) <strong>Curdling of milk.</strong> It is a chemical change because lactic acid bacteria changes the milk permanently to the curd. A change in which a substance undergoes a change in its physical properties is called a physical change. A physical change is generally reversible. In such a change no new substance is formed. Examples include dissolving sugar in water, sublimation of dry ice, crushing paper, melting wax, and boiling water.</p>",
                    solution_hi: "<p>15.(b) <strong>दूध से दही बनना।</strong> यह एक रासायनिक परिवर्तन है क्योंकि लैक्टिक अम्ल बैक्टीरिया दूध को स्थायी रूप से दही में परिवर्तित कर देता है। ऐसा परिवर्तन जिसमें किसी पदार्थ के भौतिक गुणों में परिवर्तन होता है, उसे भौतिक परिवर्तन कहते हैं। भौतिक परिवर्तन सामान्यतः प्रतिवर्ती होता है। ऐसे परिवर्तन में कोई नया पदार्थ नहीं बनता है। उदाहरणों में जल में चीनी घोलना, सूखी बर्फ का उर्ध्वपातन, कागज को कुचलना, मोम को पिघलाना और जल को उबालना शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>