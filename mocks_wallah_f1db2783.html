<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The income of A is 60% less than that of B, and the expenditure of A is equal to 60% of B&rsquo;s expenditure. If A&rsquo;s income is equal to 70% of B&rsquo;s expenditure, then what is the ratio of the saving of A and B ?</p>",
                    question_hi: "<p>1. A की आय B की आय से 60% कम है, तथा A का व्यय B के व्यय के 60% के बराबर है । यदि A की आय B के व्यय के 70% के बराबर है, तो A और B की बचत का अनुपात क्या है ?</p>",
                    options_en: ["<p>3 : 8</p>", "<p>5 : 9</p>", 
                                "<p>4 : 7</p>", "<p>2 : 15</p>"],
                    options_hi: ["<p>3 : 8</p>", "<p>5 : 9</p>",
                                "<p>4 : 7</p>", "<p>2 : 15</p>"],
                    solution_en: "<p>1.(d) Income of A and B = 40 : 100 = 2 : 5<br>A&rsquo;s income = 2a<br>B&rsquo;s income = 5a<br>Expenditure of A and B = 60 : 100 = 3 : 5&nbsp;<br>A&rsquo;s expenditure = 3b<br>B&rsquo;s expenditure = 5b<br>As per given condition:<br>2a = 70% of 5b<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>a</mi></mrow><mrow><mn>5</mn><mi>b</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>a</mi><mi>b</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math><br>Let a = 7x and b= 4x<br>Thus,<br>A&rsquo;s income = 2a = 14x<br>B&rsquo;s income = 5a = 35x<br>A&rsquo;s expenditure = 3b = 12x <br>B&rsquo;s expenditure = 5b = 20x<br>A&rsquo;s saving = 2x<br>B&rsquo;s saving = 15x<br>Required ratio = 2 : 15</p>",
                    solution_hi: "<p>1.(d) <br>A और B की आय = 40 : 100 = 2 : 5<br>A की आय = 2a<br>B की आय = 5a<br>A और B का व्यय = 60 : 100 = 3 : 5&nbsp;<br>A का व्यय = 3b<br>B का व्यय = 5b<br>दी गई शर्त के अनुसार:<br>2a = 70% of 5b<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>a</mi></mrow><mrow><mn>5</mn><mi>b</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>a</mi><mi>b</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math><br>माना a= 7x तथा b= 4x<br>इस प्रकार,<br>A की आय = 2a = 14x<br>B की आय = 5a = 35x<br>A का व्यय = 3b = 12x <br>B का व्यय = 5b = 20x<br>A की बचत = 2x<br>B की बचत = 15x<br>आवश्यक अनुपात = 2 : 15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A, B and C donate 8%,7% and 9%, of their salaries respectively to a charitable trust. The salaries of A and B are the same and the difference between their donations is ₹259. The total donation of A and B is ₹1,185 more than that of C. The total donation of A and C is what percentage of the total salaries of A,B and C ? (Correct to one decimal place)</p>",
                    question_hi: "<p>2. A, B तथा C अपने वेतन का क्रमशः 8%, 7% तथा 9% भाग एक पुण्यार्थ ट्रस्ट में दान कर देते हैं । A और B के वेतन समान हैं तथा उनके दान में 259 रुपये का अंतर है । A और B का कुल दान C के दान से 1,185 रुपये अधिक है । A और C का कुल दान A, B तथा C के कुल वेतन का कितना प्रतिशत है ?</p>",
                    options_en: ["<p>6.2%</p>", "<p>5.8%</p>", 
                                "<p>6.4%</p>", "<p>7.1%</p>"],
                    options_hi: ["<p>6.2%</p>", "<p>5.8%</p>",
                                "<p>6.4%</p>", "<p>7.1%</p>"],
                    solution_en: "<p>2.(b) Let salaries of A and B = ₹ x<br>As A donated 8% salary and B donated 7% salary and difference between their donation is ₹ 259<br>1% x = ₹ 259<br>Salary of each of A and B = ₹ 25900<br>Total donation of A and B = 15% of ₹ 25900 = ₹ 3885<br>C&rsquo;s donation = ₹ (3885 - 1185) = ₹ 2700<br>C&rsquo;s salary = ₹ 30000<br>A&rsquo;s donation = ₹ 2072<br>B&rsquo;s donation = ₹ 1813<br>Total salary of A,B and C = ₹ 81,800<br>Donation of A and C = ₹ 4772<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4772</mn><mn>81800</mn></mfrac></math> &times; 100 = 5.8%</p>",
                    solution_hi: "<p>2.(b)<br>माना A और B का वेतन = ₹ x<br>चूंकि A ने वेतन का 8% दान दिया और B ने वेतन का 7% दान दिया और उनके दान के बीच का अंतर ₹ 259 है। <br>x का 1% = ₹ 259<br>A और B के प्रत्येक का वेतन = ₹ 25900<br>A और B का कुल दान = 15% of ₹ 25900 = ₹ 3885<br>C का दान = ₹ (3885 - 1185) = ₹ 2700<br>C का वेतन = ₹ 30000<br>A का दान = ₹ 2072<br>B का दान = ₹ 1813<br>A,B और C का कुल वेतन = ₹ 81,800<br>A और C का कुल वेतन = ₹ 4772<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4772</mn><mn>81800</mn></mfrac></math> &times; 100 = 5.8%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. In a school, 4% of the students did not appear for the annual exams. 10% of the students who appeared for the exams could not pass the exam. Out of the remaining students, 50% got distinction marks and 432 students passed the exam but could not get distinction marks. The total number of students in the school is:</p>",
                    question_hi: "<p>3. एक विद्यालय में, 4% छात्र वार्षिक परीक्षा में शामिल नहीं हुए । परीक्षा में शामिल होने वाले 10% छात्र परीक्षा पास नहीं कर सके । शेष छात्रों में से, 50% को डिस्टिंक्शन अंक मिले तथा 432 छात्र परीक्षा तो पास कर गए लेकिन उन्हें डिस्टिंक्शन अंक नहीं मिले । विद्यालय में छात्रों की कुल संख्या कितनी है ?</p>",
                    options_en: ["<p>960</p>", "<p>1000</p>", 
                                "<p>878</p>", "<p>1200</p>"],
                    options_hi: ["<p>960</p>", "<p>1000</p>",
                                "<p>878</p>", "<p>1200</p>"],
                    solution_en: "<p>3.(b) Let the total number of students = x<br>according to the question, students who passed but did not get distinction can be represented as,&nbsp;<br>&rArr; x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math> = 432<br>&rArr; x = 1000</p>",
                    solution_hi: "<p>3.(b) <br>माना विद्यार्थियों की कुल संख्या = x<br>प्रश्न के अनुसार, जो छात्र उत्तीर्ण हुए, लेकिन जिन्हे चीजे नहीं मिली, उन्हें इस प्रकार दर्शाया जा सकता है:<br>&rArr; x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math> = 432<br>&rArr; x = 1000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If the length of a rectangle is increased by 40% and the breadth is decreased by 20%, then the area of the rectangle is increased by x%. The value of x is:</p>",
                    question_hi: "<p>4. यदि किसी आयत की लंबाई 40% बढ़ा दी जाती है तथा चौड़ाई 20% कम कर दी जाती है, तो इस आयत का क्षेत्रफल x % बढ़ जाता है । x का मान है ।</p>",
                    options_en: ["<p>20</p>", "<p>12</p>", 
                                "<p>16</p>", "<p>8</p>"],
                    options_hi: ["<p>20</p>", "<p>12</p>",
                                "<p>16</p>", "<p>8</p>"],
                    solution_en: "<p>4.(b)&nbsp;Let,<br>Length of a rectangle is a<br>Breadth of rectangle is b<br>Area of rectangle is &lsquo;ab&rsquo;<br>After 40% increase, length becomes &lsquo;1.4a&rsquo;<br>After 20% decrease, Breadth becomes &lsquo;0.8b&rsquo;<br>Hence, area of rectangle is (1.4a)(0.8b) = &lsquo;1.12ab&rsquo;<br>Thus, %increase in area of rectangle is &lsquo;12%&rsquo;</p>",
                    solution_hi: "<p>4.(b)&nbsp; माना,<br>एक आयत की लंबाई = a<br>आयत की चौड़ाई = b <br>आयत का क्षेत्रफल = \'ab\' <br>40% की वृद्धि के बाद, लंबाई \'1.4a\' हो जाती है<br>20% की कमी के बाद, चौड़ाई \'0.8b\' हो जाती है<br>अत: आयत का क्षेत्रफल है (1.4a)(0.8b) = \'1.12ab\'<br>अत: आयत के क्षेत्रफल में % वृद्धि \'12%\' है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If the difference between 62% and 80% of a number is 198, then the difference between 92% and 56% of the number will be:</p>",
                    question_hi: "<p>5. यदि एक संख्या के 62% और 80% में 198 का अंतर है, तो उस संख्या के 92% और 56% में कितना अंतर होगा ?</p>",
                    options_en: ["<p>360</p>", "<p>1100</p>", 
                                "<p>396</p>", "<p>3564</p>"],
                    options_hi: ["<p>360</p>", "<p>1100</p>",
                                "<p>396</p>", "<p>3564</p>"],
                    solution_en: "<p>5.(c) <br>Let the number be &lsquo;x&rsquo;<br>80% x - 62% x = 198<br>18% x = 198<br>1% x = 11<br>92% x - 56% x = 36% x = 396</p>",
                    solution_hi: "<p>5.(c)<br>माना संख्या \'x\' है<br>80% x - 62% x = 198<br>18% x = 198<br>1% x = 11<br>92% x - 56% x = 36% x = 396</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Sonu saves 15% of her income. If her income increases by 20% and she still saves the same amount as before, then what is the percentage increase in her expenditure?(correct to one decimal place)</p>",
                    question_hi: "<p>6. सोनू अपनी आय का 15% भाग बचत करती है । यदि उसकी आय 20% बढ़ जाती है तथा तब भी वह पहले जितनी बचत ही कर पाती है, तो उसके व्यय में कितने प्रतिशत की वृद्धि हुई है ? (दशमलव के एक स्थान तक)</p>",
                    options_en: ["<p>22.8</p>", "<p>23.5</p>", 
                                "<p>23.8</p>", "<p>24.2</p>"],
                    options_hi: ["<p>22.8</p>", "<p>23.5</p>",
                                "<p>23.8</p>", "<p>24.2</p>"],
                    solution_en: "<p>6.(b)<br>Let income of sonu = ₹100.<br>Her saving = ₹15<br>Expenditure, E<sub>1 </sub>= ₹ 85<br>Increased income = ₹ 120 <br>Increased expenditure, E<sub>2 </sub>= ₹ 105<br>% increase in expenditure&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>105</mn><mo>-</mo><mn>85</mn></mrow><mn>85</mn></mfrac></math> &times; 100 = 23.5%</p>",
                    solution_hi: "<p>6.(b) <br>माना सोनू की आय = ₹100।<br>उसकी बचत = ₹15<br>व्यय, E<sub>1 </sub>= ₹ 85<br>बढ़ी हुई आय = ₹ 120 <br>बढ़ा हुआ खर्च, E<sub>2 </sub>= ₹ 105<br>व्यय में % वृद्धि = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>105</mn><mo>-</mo><mn>85</mn></mrow><mn>85</mn></mfrac></math> &times; 100 = 23.5%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. In an examination in which the full marks were 500, A scored 25% more marks than B. B scored 60% more marks than C and C scored 20% less marks than D. If A scored 80% marks, then the percentage of marks obtained by D is:</p>",
                    question_hi: "<p>7. एक परीक्षा जिसमें पूर्णांक 500 है, उसमें A को B से 25% अधिक अंक आए हैं । B को C से 60% अधिक अंक मिले हैं तथा C को D से 20% कम अंक मिले हैं । यदि A को 80% अंक प्राप्त हुए हैं, तो D के द्वारा प्राप्त अंक का प्रतिशत ज्ञात कीजिए</p>",
                    options_en: ["<p>60%</p>", "<p>54%</p>", 
                                "<p>65%</p>", "<p>50%</p>"],
                    options_hi: ["<p>60%</p>", "<p>54%</p>",
                                "<p>65%</p>", "<p>50%</p>"],
                    solution_en: "<p>7.(d)&nbsp;According to question:<br>Marks scored by A : B : C : D = 40 : 32 : 20 : 25<br>A = 40 x = 80%<br>Then, D = 50%</p>",
                    solution_hi: "<p>7.(d)&nbsp;प्रश्न के अनुसार:<br>A : B : C : D द्वारा बनाए गए अंक= 40 : 32 : 20 : 25<br>A = 40x = 80%<br>फिर , D = 50%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A and B spent 60% and 75% of their incomes, respectively. If the savings of A are 20% more than that of B, then by what percentage is the income of A less than the income of B ?</p>",
                    question_hi: "<p>8. A तथा B ने अपनी आय का क्रमशः 60% तथा 75% हिस्सा खर्च किया । यदि A की बचत B की बचत से 20% अधिक है, तो A की आय B की आय से कितना प्रतिशत कम है ?</p>",
                    options_en: ["<p>20</p>", "<p>10</p>", 
                                "<p>15</p>", "<p>25</p>"],
                    options_hi: ["<p>20</p>", "<p>10</p>",
                                "<p>15</p>", "<p>25</p>"],
                    solution_en: "<p>8.(d) <br>Ratio of A and B saving = 6x : 5x<br>A saved 40% of his income and B saved 25% of his income. <br>Ratio of income of A and B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi></mrow><mn>40</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>25</mn></mfrac></mstyle></math> = 3 : 4<br>Required % = 25%</p>",
                    solution_hi: "<p>8.(d) <br>A और B की बचत का अनुपात = 6x : 5x<br>A ने अपनी आय का 40% बचाया और B ने अपनी आय का 25% बचाया।<br>A और B की आय का अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mi>x</mi></mrow><mn>40</mn></mfrac></math> :&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>5</mn><mi>x</mi></mrow><mn>25</mn></mfrac></mstyle></math> = 3 : 4<br>आवश्यक % = 25%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The price of sugar is increased by 20%. By what percentage must one cut down on the consumption of sugar, so that no extra amount has to be incurred on sugar ?</p>",
                    question_hi: "<p>9. चीनी की कीमत 20% बढ़ गयी है । व्यक्ति को चीनी की खपत में कितने प्रतिशत की कटौती करनी चाहिए, ताकि चीनी पर ज़रा भी अतिरिक्त राशि खर्च ना हो ?</p>",
                    options_en: ["<p>80%</p>", "<p>20%</p>", 
                                "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>83<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>80%</p>", "<p>20%</p>",
                                "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>83<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>9.(c) <br>Price change in sugar = 10 &rarr; 12<br>Price &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>c</mi><mi>o</mi><mi>n</mi><mi>s</mi><mi>u</mi><mi>m</mi><mi>p</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>P</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>P</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>C</mi><mn>2</mn></msub><msub><mi>C</mi><mn>1</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>12</mn></mfrac></math><br>% cut in consumption =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>12</mn><mo>-</mo><mn>10</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 100 = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>9.(c) <br>चीनी में मूल्य परिवर्तन&nbsp;= 10 &rarr; 12<br>Price &prop;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>C</mi></mfrac></math><br>जंहा, C = उपभोग<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>P</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>P</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>C</mi><mn>2</mn></msub><msub><mi>C</mi><mn>1</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>12</mn></mfrac></math><br>खपत में % कटौती = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>-</mo><mn>10</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 100 = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The population of a city increased 30% in the first year and decreased by 15% in the next year. If the present population is 11,050 then the population 2 years ago was:</p>",
                    question_hi: "<p>10. एक शहर की आबादी पहले साल 30% बढ़ी तथा अगले वर्ष 15% कम हो गयी । यदि वर्तमान आबादी 11,050 है, तो 2 वर्ष पहले कितनी आबादी थी ?</p>",
                    options_en: ["<p>10,000</p>", "<p>10,050</p>", 
                                "<p>99,000</p>", "<p>99,500</p>"],
                    options_hi: ["<p>10,000</p>", "<p>10,050</p>",
                                "<p>99,000</p>", "<p>99,500</p>"],
                    solution_en: "<p>10.(a) <br>Total change% in population <br>= 30 - 15 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 10.5% increase<br>Let two years ago, age = x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>110</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> &times; x = 11050<br>x = 10,000</p>",
                    solution_hi: "<p>10.(a) <br>जनसंख्या में कुल % परिवर्तन<br>= 30-15 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>= 10.5% की वृद्धि<br>माना दो वर्ष पूर्व आयु = x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>110</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> &times; x = 11050<br>x = 10,000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. In an examination, Anita scored 31% marks and failed by 16 marks. Sunita scored 40% marks and obtained 56 marks more than those required to pass. Find the minimum marks required to pass.</p>",
                    question_hi: "<p>11. एक परीक्षा में, अनीता को 31% अंक मिले तथा वह 16 अंकों से फेल हो गयी । सुनीता को 40% अंक मिले तथा उसके अंक उत्तीर्ण होने के लिए आवश्यक अंक से 56 अधिक थे । उत्तीर्ण होने के लिए न्यूनतम कितने अंकों की आवश्यकता है ?</p>",
                    options_en: ["<p>3116</p>", "<p>3944</p>", 
                                "<p>7100</p>", "<p>264</p>"],
                    options_hi: ["<p>3116</p>", "<p>3944</p>",
                                "<p>7100</p>", "<p>264</p>"],
                    solution_en: "<p>11.(d) <br>Let M be the maximum marks<br>According to question: <br>31% of M + 16 = 40% of M - 56<br>9% of M = 72<br>M = 800<br>Passing marks = 40% of M - 56 <br>= 320 - 56 = 264</p>",
                    solution_hi: "<p>11.(d) <br>माना M अधिकतम अंक है<br>प्रश्न के अनुसार:<br>M का 31% +16 = M का 40% - 56<br>M का 9% = 72<br>M = 800<br>उत्तीर्ण अंक = 40% का M - 56&nbsp;<br>= 320 - 56 = 264</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Anu spends 68% of her monthly income. If her monthly income increases by 20% and her monthly saving increase by 9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% then the percentage increase in her monthly expenditure is:</p>",
                    question_hi: "<p>12. अनु अपनी मासिक आय का 68% खर्च करती है । यदि उसकी मासिक आय 20% बढ़ जाती है तथा उसकी मासिक बचत 9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>% बढ़ जाती है, तो उसके मासिक व्यय में कितने प्रतिशत की वृद्धि होगी ?</p>",
                    options_en: ["<p>20%</p>", "<p>25%</p>", 
                                "<p>22%</p>", "<p>32%</p>"],
                    options_hi: ["<p>20%</p>", "<p>25%</p>",
                                "<p>22%</p>", "<p>32%</p>"],
                    solution_en: "<p>12.(b)<br>Income : Expenditure : Saving<br>&nbsp; &nbsp;100&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 68&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;32<br>Income increase by 20%, and saving by 9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac></math>%<br>New ratio, <br>Income : Expenditure : Saving<br>&nbsp; &nbsp; 120&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;85&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;35<br>% increase in monthly expenditure <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>85</mn><mo>-</mo><mn>68</mn></mrow><mn>68</mn></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>12.(b) <br>आय&nbsp; : व्यय : बचत<br>100&nbsp; :&nbsp; 68&nbsp; : 32<br>आय में 20% की वृद्धि, और बचत 9<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac></math>%<br>नया अनुपात,<br>आय&nbsp; : व्यय : बचत<br>&nbsp;120 :&nbsp; 85&nbsp; :&nbsp; 35<br>मासिक खर्च में % वृद्धि <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>85</mn><mo>-</mo><mn>68</mn></mrow><mn>68</mn></mfrac></math> &times; 100 = 25%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. By what number must the given number be multiplied to increase the number by 25%.</p>",
                    question_hi: "<p>13. किसी संख्या में 25% की वृद्धि करने के लिए उस संख्या को किस संख्या से गुणा करना चाहिए ?</p>",
                    options_en: ["<p>3</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>3</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>13.(c)&nbsp;Let x be the number <br>To increase it by 25%, we multiply it by 125 and divide by 100<br>We get, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac></math> &times; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> &times; x</p>",
                    solution_hi: "<p>13.(c)&nbsp;मान लीजिए x संख्या है<br>इसे 25% तक बढ़ाने के लिए, हम इसे 125 से गुणा करते हैं और 100 से विभाजित करते हैं। <br>हम पाते हैं, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac></math> &times; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> &times; x</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Ravi scores 72% marks in examinations. If these are 360 marks, then the maximum marks are:</p>",
                    question_hi: "<p>14. रवि को परीक्षा में 72% अंक आए । यदि ये 360 अंक हैं, तो अधिकतम अंक कितने हैं ?</p>",
                    options_en: ["<p>500</p>", "<p>350</p>", 
                                "<p>450</p>", "<p>400</p>"],
                    options_hi: ["<p>500</p>", "<p>350</p>",
                                "<p>450</p>", "<p>400</p>"],
                    solution_en: "<p>14.(a) <br>72% of Maximum marks = 360<br>Then, maximum marks = 360 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>72</mn></mfrac></math> = 500&nbsp;</p>",
                    solution_hi: "<p>14.(a) <br>अधिकतम अंक का 72% = 360<br>तब, अधिकतम अंक = 360 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>72</mn></mfrac></math> = 500&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The price of cooking oil increased by 25%. Find by how much percentage a family must reduce its consumption in order to maintain the same budget ?</p>",
                    question_hi: "<p>15. खाना पकाने के तेल की कीमत में 25% की वृद्धि हुई। उसी बजट को बनाए रखने के लिए एक परिवार को अपनी खपत में कितने प्रतिशत की कमी करनी चाहिए ?</p>",
                    options_en: ["<p>70%</p>", "<p>80%</p>", 
                                "<p>30%</p>", "<p>20%</p>"],
                    options_hi: ["<p>70%</p>", "<p>80%</p>",
                                "<p>30%</p>", "<p>20%</p>"],
                    solution_en: "<p>15.(d) price &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><mi>n</mi><mi>s</mi><mi>u</mi><mi>m</mi><mi>p</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>\'</mi></mrow><mi>P</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>C</mi><mrow><mi>C</mi><mo>\'</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>C</mi><mrow><mi>C</mi><mo>\'</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>C</mi><mrow><mi>C</mi><mo>\'</mo></mrow></mfrac></math><br>% reduction =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mn>5</mn></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>15.(d) <br>Price &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>C</mi></mfrac></math><br>जंहा, C = उपभोग<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>\'</mi></mrow><mi>P</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>C</mi><mrow><mi>C</mi><mo>\'</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>C</mi><mrow><mi>C</mi><mo>\'</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>C</mi><mrow><mi>C</mi><mo>\'</mo></mrow></mfrac></math><br>% कमी =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>-</mo><mn>4</mn></mrow><mn>5</mn></mfrac></math> &times; 100 = 20%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>