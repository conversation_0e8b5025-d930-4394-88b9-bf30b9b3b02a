<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> The NOR gate is OR gate followed by ........</span></p>\n",
                    question_hi: " <p>1.</span><span style=\"font-family:Cambria Math\"> NOR </span><span style=\"font-family:Cambria Math\">गेट</span><span style=\"font-family:Cambria Math\">, OR </span><span style=\"font-family:Cambria Math\">गेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जिसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बाद</span><span style=\"font-family:Cambria Math\"> _____ </span><span style=\"font-family:Cambria Math\">है।</span></p>",
                    options_en: ["<p>NOT gate</p>\n", "<p>AND gate</p>\n", 
                                "<p>NAND gate</p>\n", "<p>OR gate</p>\n"],
                    options_hi: [" <p> NOT </span><span style=\"font-family:Cambria Math\">गेट</span></p>", " <p> AND </span><span style=\"font-family:Cambria Math\">गेट</span></p>",
                                " <p> NAND </span><span style=\"font-family:Cambria Math\">गेट</span></p>", " <p> OR </span><span style=\"font-family:Cambria Math\">गेट</span></p>"],
                    solution_en: "<p>1.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">The NOR gate is a combination OR gate followed by </span><strong><span style=\"font-family: Cambria Math;\">an inverter i.e. NOT gate</span></strong><span style=\"font-family: Cambria Math;\"><strong>.</strong> Its output is \"true\" if both inputs are \"false.\" Otherwise, the output is \"false.\"</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1673262096/word/media/image1.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Truth Table of NOR Gate</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A B X</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">0 0 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">0 1 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 0 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 1 0</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1673262096/word/media/image2.png\"></p>\n",
                    solution_hi: " <p>1.(a)</span><span style=\"font-family:Cambria Math\"> NOR </span><span style=\"font-family:Cambria Math\">गेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कॉम्बिनेशन</span><span style=\"font-family:Cambria Math\"> OR</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गेट</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Cambria Math\">combination OR gate) </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जिसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">इन्वर्टर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">यानी</span><span style=\"font-family:Cambria Math\"> NOT </span><span style=\"font-family:Cambria Math\">गेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">होता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">यदि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दोनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">इनपुट</span><span style=\"font-family:Cambria Math\"> \"</span><span style=\"font-family:Cambria Math\">फॉल्स</span><span style=\"font-family:Cambria Math\">\" (false) </span><span style=\"font-family:Cambria Math\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">इसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आउटपुट</span><span style=\"font-family:Cambria Math\"> \"</span><span style=\"font-family:Cambria Math\">ट्रू</span><span style=\"font-family:Cambria Math\">\" (true) </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अन्यथा</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">आउटपुट</span><span style=\"font-family:Cambria Math\"> \"</span><span style=\"font-family:Cambria Math\">फॉल्स</span><span style=\"font-family:Cambria Math\">\" </span><span style=\"font-family:Cambria Math\">है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1673262096/word/media/image1.png\"/></p> <p><span style=\"font-family:Cambria Math\">NOR </span><span style=\"font-family:Cambria Math\">गेट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ट्रुथ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">टेबल</span><span style=\"font-family:Cambria Math\"> (truth table)</span></p> <p><span style=\"font-family:Cambria Math\">A B X</span></p> <p><span style=\"font-family:Cambria Math\">0 0 1</span></p> <p><span style=\"font-family:Cambria Math\">0 1 0</span></p> <p><span style=\"font-family:Cambria Math\">1 0 0</span></p> <p><span style=\"font-family:Cambria Math\">1 1 0</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1673262096/word/media/image2.png\"/></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Which of the following is not related to utility software?</span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2335;&#2367;&#2354;&#2367;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> (utility software) </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Antivirus program</p>\n", "<p>Text editor</p>\n", 
                                "<p>Railway reservation system</p>\n", "<p>Disk compression software</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2319;&#2306;&#2335;&#2368;&#2357;&#2366;&#2351;&#2352;&#2360; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; (Antivirus program)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2319;&#2337;&#2367;&#2335;&#2352; (Text editor)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2352;&#2375;&#2354;&#2357;&#2375; &#2352;&#2367;&#2332;&#2352;&#2381;&#2357;&#2375;&#2358;&#2344; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; (Railway reservation system)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2337;&#2367;&#2360;&#2381;&#2325; &#2325;&#2350;&#2381;&#2346;&#2381;&#2352;&#2375;&#2358;&#2344; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (Disk compression software)</span></p>\n"],
                    solution_en: "<p>2.(c)<span style=\"font-family: Cambria Math;\"> Railway reservation system is an example o</span><span style=\"font-family: Cambria Math;\">f </span><span style=\"font-family: Cambria Math;\"><strong>specific purpose software</strong> </span><span style=\"font-family: Cambria Math;\">which is designed to perform specific tasks.</span></p>\n",
                    solution_hi: "<p>2.(c) <span style=\"font-family: Cambria Math;\">&#2352;&#2375;&#2354;&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2367;&#2332;&#2352;&#2381;&#2357;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2375;&#2360;&#2367;&#2347;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2381;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> (specific purpose software)</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2375;&#2360;&#2367;&#2347;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2366;&#2360;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> (specific task) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2347;&#2377;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2332;&#2364;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> What is the use of Ctrl + 9 in ms excel ?</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\">&#2319;&#2350;&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> Ctrl+9 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>The function of Ctrl + 9 is to delete a column.</p>\n", "<p>It helps to unhide selected rows.</p>\n", 
                                "<p>It helps to hide selected rows.</p>\n", "<p>This shortcut key helps to select the entire row.</p>\n"],
                    options_hi: ["<p>Ctrl + 9 <span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2377;&#2354;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2335;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2351;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2306;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2342;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2351;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2306;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2367;&#2346;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2342;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2306;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2306;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2342;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n"],
                    solution_en: "<p>3.(c) <span style=\"font-family: Cambria Math;\">The shortcut key which helps to hide selected rows in ms excel is </span><strong><span style=\"font-family: Cambria Math;\">Ctrl + 9</span><span style=\"font-family: Cambria Math;\">.</span></strong></p>\n",
                    solution_hi: "<p>3.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2350;&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2351;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2306;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2367;&#2346;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2342;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2306;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Ctrl + 9</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: " <p>4.</span><span style=\"font-family:Cambria Math\"> To protect yourself from computer hacker intrusions, yo</span><span style=\"font-family:Cambria Math\">u should install a________</span></p>",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2328;&#2369;&#2360;&#2346;&#2376;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2369;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2330;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2310;&#2346;&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> ______ </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span></p>\n",
                    options_en: [" <p> firewall </span></p>", " <p> mailer</span></p>", 
                                " <p> macro</span></p>", " <p> script</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2347;&#2364;&#2366;&#2351;&#2352;&#2357;&#2377;&#2354; (firewall)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2350;&#2375;&#2354;&#2352; (mailer)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2350;&#2376;&#2325;&#2381;&#2352;&#2379; (macro)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2360;&#2381;&#2325;&#2381;&#2352;&#2367;&#2346;&#2381;&#2335; (script)</span></p>\n"],
                    solution_en: " <p>4.(a)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">A Firewall is a network security device that monitors and filters incoming and outgoing network traffic based on an organization\'s previously established security policies.</span></p>",
                    solution_hi: "<p>4.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2364;&#2366;&#2351;&#2352;&#2357;&#2377;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2327;&#2336;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2325;&#2350;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2313;&#2335;&#2327;&#2379;&#2311;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2376;&#2347;&#2364;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2332;&#2364;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2326;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2364;&#2367;&#2354;&#2381;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: " <p>5.</span><span style=\"font-family:Cambria Math\"> Which of the following fields of an e-mail hides the identity of the recipient</span><span style=\"font-family:Cambria Math\">s?</span></p>",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2312;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2330;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2369;&#2346;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: [" <p> Bcc</span></p>", " <p> To </span></p>", 
                                " <p> From </span></p>", " <p> Cc </span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2348;&#2368;.&#2360;&#2368;.&#2360;&#2368;(Bcc)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2335;&#2370;(To)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2347;&#2381;&#2352;&#2377;&#2350;(From)</span></p>\n", "<p><span style=\"font-weight: 400;\">Cc </span></p>\n"],
                    solution_en: " <p>5.(a)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Bcc </span><span style=\"font-family:Cambria Math\">stands for blind carbon copy which is simila</span><span style=\"font-family:Cambria Math\">r to that of </span><span style=\"font-family:Cambria Math\">Cc </span><span style=\"font-family:Cambria Math\">except that </span><span style=\"font-family:Cambria Math\">the Email address of the recipients specified in this field do not appear in the received message header and the recipients in the To or Cc fields will not know that a copy was sent to these addresses.</span></p>",
                    solution_hi: "<p>5.(a)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Bcc</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;</span><span style=\"font-family: Cambria Math;\">&#2381;&#2354;&#2366;&#2311;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2381;&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2377;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> Cc </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2357;&#2366;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2381;&#2342;&#2367;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\">(specified recipients) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2312;&#2350;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2368;&#2352;&#2381;&#2359;&#2354;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\">(header) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> To </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> Cc </span><span style=\"font-family: Cambria Math;\">&#2347;&#2364;&#2368;&#2354;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2354;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;</span><span style=\"font-family: Cambria Math;\">&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> Which is the highest reliability topology?</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2381;&#2357;&#2360;&#2344;&#2368;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">(highest reliability) </span><span style=\"font-family: Cambria Math;\">&#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Mesh topology</p>\n", "<p>Tree topology</p>\n", 
                                "<p>Bus topology</p>\n", "<p>Star topology</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2350;&#2375;&#2358; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(mesh topology)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2335;&#2381;&#2352;&#2368; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(tree topology)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2348;&#2360; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(bus topology)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2335;&#2366;&#2352; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(star topology)</span></p>\n"],
                    solution_en: "<p>6.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">The topology with highest reliability is <strong>\'</strong></span><strong><span style=\"font-family: Cambria Math;\">mesh topology</span></strong><span style=\"font-family: Cambria Math;\"><strong>\'</strong>.</span><span style=\"font-family: Cambria Math;\">A</span><span style=\"font-family: Cambria Math;\"> tree topology </span><span style=\"font-family: Cambria Math;\">is a special type of structure where many connected elements are arranged like the branches of a tree.</span><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Cambria Math;\">star topology</span><span style=\"font-family: Cambria Math;\"> is a topology for a Local Area Network (LAN) in which all nodes are individually connected to a central connection point, like a</span><span style=\"font-family: Cambria Math;\"> <strong>hub or a </strong></span><strong><span style=\"font-family: Cambria Math;\">switch</span></strong><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">A </span><strong><span style=\"font-family: Cambria Math;\">linear bus topology</span></strong><span style=\"font-family: Cambria Math;\"> consists of a main run of cable with a terminator at each end.</span></p>\n",
                    solution_hi: "<p>6.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2381;&#2357;&#2360;&#2344;&#2368;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">(highest reliability) </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;</span><strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">\'</span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\">\'</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">mesh topology</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\">(tree topology) </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2352;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2361;</span><span style=\"font-family: Cambria Math;\">&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2369;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2375;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2326;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\">(star topology)</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2379;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> (LAN) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2348;</span><span style=\"font-family: Cambria Math;\">(hub) </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2367;&#2330;</span><span style=\"font-family: Cambria Math;\">(switch) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2344;&#2375;&#2325;&#2381;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2369;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">&#2352;&#2376;&#2326;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\">(linear bus topology)</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2375;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Which application software is used for a special purpose? </span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2375;&#2358;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2381;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> (special purpose) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>General purpose software</p>\n", "<p>Special purpose software</p>\n", 
                                "<p>Important sof<span style=\"font-family: Cambria Math;\">tware</span></p>\n", "<p>System software</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2332;&#2344;&#2352;&#2354; &#2346;&#2352;&#2381;&#2346;&#2360; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (General purpose software)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2346;&#2375;&#2358;&#2354; &#2346;&#2352;&#2381;&#2346;&#2360; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (Special purpose software)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2311;&#2350;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335;&#2375;&#2344;&#2381;&#2335; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (Important software)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (System software)</span></p>\n"],
                    solution_en: "<p>7.(b)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Special purpose softwares</span></strong><span style=\"font-family: Cambria Math;\"> are designed to perform specific tasks. This type of application software generally has one purpose to execute for e.g.web browser,calculator etc.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>General purpose software</strong> </span><span style=\"font-family: Cambria Math;\">are those softwares which are used for any general purpose. They allow people to do simple computer tasks for e.g.word processor, spreadsheet.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">System software </span></strong><span style=\"font-family: Cambria Math;\">consists of several programs, which are directly responsible for contro</span><span style=\"font-family: Cambria Math;\">lling, integrating and managing the individual hardware components of a computer system for e.g. mac os,linux.</span></p>\n",
                    solution_hi: "<p>7.(b)<strong> <span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2375;&#2358;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2381;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2375;&#2360;&#2367;&#2347;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2366;&#2360;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2332;&#2364;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2350;&#2340;&#2380;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2381;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2325;&#2381;&#2351;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> (purpose execute) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352;</span><span style=\"font-family: Cambria Math;\"> (web browser), </span><span style=\"font-family: Cambria Math;\">&#2325;&#2376;&#2354;&#2325;&#2369;&#2354;&#2375;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> (calculator) </span><span style=\"font-family: Cambria Math;\">&#2310;&#2342;&#2367;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2381;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\">&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2344;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2381;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> (general purpose) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352;</span><span style=\"font-family: Cambria Math;\"> (word processor), </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2381;&#2352;&#2375;&#2337;&#2358;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> (spreadsheet) </span><span style=\"font-family: Cambria Math;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> (functions) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2350;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2379;&#2344;&#2375;&#2306;&#2335;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> (hardware components) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> (control) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2375;&#2327;&#2381;&#2352;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\"> (integrate) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2376;&#2344;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> (manage) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2367;&#2350;&#2381;&#2350;&#2375;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2376;&#2325;</span><span style=\"font-family: Cambria Math;\"> OS, </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2344;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> (linux)</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: " <p>8. </span><span style=\"font-family:Cambria Math\">Which of the followi</span><span style=\"font-family:Cambria Math\">ng is not an external command of DOS?</span></p>",
                    question_hi: " <p>8.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">डॉस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक्सटर्नल</span><span style=\"font-family:Cambria Math\">(external) </span><span style=\"font-family:Cambria Math\">कमांड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> CLS</span></p>", " <p> LABEL </span></p>", 
                                " <p> FORMAT</span></p>", " <p> CHKDSK </span></p>"],
                    options_hi: [" <p> CLS</span></p>", " <p> LABEL </span></p>",
                                " <p> FORMAT </span></p>", " <p> CHKDSK</span></p>"],
                    solution_en: " <p>8.(a)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">FORMAT. EXE, CHKDSK.COM and LABEL</span><span style=\"font-family:Cambria Math\"> are external commands.</span></p> <p><span style=\"font-family:Cambria Math\">CLS</span><span style=\"font-family:Cambria Math\"> is an internal command found inside windows command interpreter cmd, that is used for clearing the console window.</span></p>",
                    solution_hi: " <p>8.(a)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">FORMAT. </span><span style=\"font-family:Cambria Math\"> EXE, CHKDSK.COM </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> LABEL </span><span style=\"font-family:Cambria Math\">बाहरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कमांड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हैं।</span></p> <p><span style=\"font-family:Cambria Math\">CLS</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">इंटरनल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कमांड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विंडोज़</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कमांड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">इंटरप्रेट</span><span style=\"font-family:Cambria Math\">र</span><span style=\"font-family:Cambria Math\">(</span><span style=\"font-family:Cambria Math\">interpreter</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Cambria Math\">cmd</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंदर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पाया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">जिसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उपयोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंसोल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विंडो</span><span style=\"font-family:Cambria Math\">(console window) </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">साफ़</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> They can find files that are unnecessary to computer operation, or take up considerable amounts of space.</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> _____ </span><span style=\"font-family: Cambria Math;\">&#2313;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2347;&#2366;&#2311;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2326;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2330;&#2366;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> (computer operation) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2366;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2347;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> (space) </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Disk Formatting</p>\n", "<p>An<span style=\"font-family: Cambria Math;\">tivirus </span></p>\n", 
                                "<p>Sweep</p>\n", "<p>Disk cleaner</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2337;&#2367;&#2360;&#2381;&#2325; &#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335;&#2367;&#2306;&#2327; (Disk Formatting)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2306;&#2335;&#2368;&#2357;&#2366;&#2351;&#2352;&#2360; (Antivirus)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2360;&#2381;&#2357;&#2368;&#2346; (Sweep)</span></p>\n", "<p><span style=\"font-weight: 400;\"> &#2337;&#2367;&#2360;&#2381;&#2325; &#2325;&#2381;&#2354;&#2368;&#2344;&#2352; (Disk cleaner)</span></p>\n"],
                    solution_en: "<p>9.(d)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Disk cleaners</span></strong><span style=\"font-family: Cambria Math;\"> are computer programs that find and delete potentially unnecessary or potentially unwanted files from a computer for e.g. clean my pc.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Disk Formatting</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">is a process to configure the data-storage devices such as hard-drive, floppy disk and flash drive when we are going to use them for the very first time or we can say initial usage for e.g. HDD low level format tool.</span></p>\n",
                    solution_hi: "<p><strong>9.(d)</strong><span style=\"font-weight: 400;\"> </span><strong>&#2337;&#2367;&#2360;&#2381;&#2325; &#2325;&#2381;&#2354;&#2368;&#2344;&#2352; </strong><span style=\"font-weight: 400;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2376;&#2306; &#2332;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2375; &#2360;&#2306;&#2349;&#2366;&#2357;&#2367;&#2340; &#2352;&#2370;&#2346; &#2360;&#2375; &#2309;&#2344;&#2366;&#2357;&#2358;&#2381;&#2351;&#2325; &#2351;&#2366; &#2360;&#2306;&#2349;&#2366;&#2357;&#2367;&#2340; &#2352;&#2370;&#2346; &#2360;&#2375; &#2309;&#2357;&#2366;&#2306;&#2331;&#2367;&#2340; &#2347;&#2364;&#2366;&#2311;&#2354;&#2379;&#2306; (unwanted files) &#2325;&#2379; &#2338;&#2370;&#2306;&#2338;&#2340;&#2375; &#2324;&#2352; &#2337;&#2367;&#2354;&#2368;&#2335; (</span><span style=\"font-weight: 400;\">find and delete) </span><span style=\"font-weight: 400;\">&#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2381;&#2354;&#2368;&#2344; &#2350;&#2366;&#2351; PC (</span><span style=\"font-weight: 400;\">clean my </span><span style=\"font-weight: 400;\">PC</span><span style=\"font-weight: 400;\">)</span><span style=\"font-weight: 400;\">&#2404;</span></p>\r\n<p><strong>&#2337;&#2367;&#2360;&#2381;&#2325; &#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335;&#2367;&#2306;&#2327;</strong><span style=\"font-weight: 400;\"> &#2337;&#2375;&#2335;&#2366;-&#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2332;&#2376;&#2360;&#2375; &#2361;&#2366;&#2352;&#2381;&#2337;-&#2337;&#2381;&#2352;&#2366;&#2311;&#2357; (hard drive), &#2347;&#2381;&#2354;&#2377;&#2346;&#2368; &#2337;&#2367;&#2360;&#2381;&#2325; (floppy disk) &#2324;&#2352; &#2347;&#2381;&#2354;&#2376;&#2358; &#2337;&#2381;&#2352;&#2366;&#2311;&#2357; (flash drive) &#2325;&#2379; &#2325;&#2377;&#2344;&#2381;&#2347;&#2364;&#2367;&#2327;&#2352; (configure) &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2319;&#2325; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360; (process) &#2361;&#2376; &#2351;&#2366; &#2313;&#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2361; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; HDD &#2354;&#2379; &#2354;&#2375;&#2357;&#2354; &#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335; &#2335;&#2370;&#2354; (low level format tool)&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "31",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> The function of an assembler is___</span><span style=\"font-family: Cambria Math;\">_____</span></p>\n",
                    question_hi: " <p>10.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">असेम्बलर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\">र्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">________ </span></p>",
                    options_en: ["<p>to convert basic language into machine language</p>\n", "<p>to convert high level language into machine language</p>\n", 
                                "<p>to convert assembly language into machine language</p>\n", "<p>to convert assembly language into low level language</p>\n"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">बेसिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लैंग्वेज</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मशीनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लैंग्वेज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बदलना</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">हाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लेवल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लैंग्वेज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मशीनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लैंग्वेज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बदलना</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">असेम्बली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लैंग्वेज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मशीनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लैंग्वेज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बदलना</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">असेंबली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लैंग्वेज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लेवल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लैंग्वेज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बदलने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span></p>"],
                    solution_en: "<p>10.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">An assemb</span><span style=\"font-family: Cambria Math;\">ler is a program that </span><span style=\"font-family: Cambria Math;\"><strong>converts the assembly language into machine </strong>code</span><span style=\"font-family: Cambria Math;\">. It takes the basic commands and operations from assembly code and converts them into binary code that can be recognized by a specific type of processor.</span></p>\n",
                    solution_hi: " <p>10.(c)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">असेंबलर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रोग्राम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">असेंबली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भाषा</span><span style=\"font-family:Cambria Math\">(assembly language) </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मशीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कोड</span><span style=\"font-family:Cambria Math\">(machine code) </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">परिवर्तित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">असेंबली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कोड</span><span style=\"font-family:Cambria Math\">(assembly code) </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बेसिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कमांड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ऑपरेशंस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लेता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उन्हें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बाइनरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कोड</span><span style=\"font-family:Cambria Math\">(binary code) </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">परिवर्तित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जिसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विशिष्ट</span><span style=\"font-family:Cambria Math\">(specific) </span><span style=\"font-family:Cambria Math\">प</span><span style=\"font-family:Cambria Math\">्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रोसेसर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पहचाना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">यह</span><span style=\"font-family:Cambria Math\"> C </span><span style=\"font-family:Cambria Math\">प्रोग्राम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अनुवाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "31",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> Which of the following is the function of Shift + Tab in ms excel ?</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2350;&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> Shift+Tab </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>It is used in moving to the previous cell.</p>\n", "<p>It helps to add an outline to the selected cells.</p>\n", 
                                "<p>It helps to center align cell contents.</p>\n", "<p>It is used to edit a cell.</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2367;&#2331;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2351;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;&#2337;&#2364;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2342;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2335;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2352;&#2375;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2342;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2337;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n"],
                    solution_en: "<p>11.(a)<span style=\"font-family: Cambria Math;\"> If we want to move to the previous cell in ms excel then</span><span style=\"font-family: Cambria Math;\"> we must press </span><strong><span style=\"font-family: Cambria Math;\">Shift+Tab</span><span style=\"font-family: Cambria Math;\">.</span></strong></p>\n",
                    solution_hi: "<p>11.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2350;&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2367;&#2331;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2361;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Shift+Tab</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2348;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2327;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "31",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> What is included in an Email address?</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2312;&#2350;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Domain name followed by user&rsquo;s name</p>\n", "<p>User&rsquo;s name followed by domain name</p>\n", 
                                "<p>U<span style=\"font-family: Cambria Math;\">ser&rsquo;s name followed by postal address</span></p>\n", "<p>User&rsquo;s name followed by street address</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2337;&#2379;&#2350;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2379;&#2350;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2366;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2340;&#2366;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2381;&#2352;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2337;&#2381;&#2352;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span></p>\n"],
                    solution_en: "<p>12.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">A valid email address is an address composed of two parts, </span><strong><span style=\"font-family: Cambria Math;\">a username followed </span><span style=\"font-family: Cambria Math;\">by a domain name</span><span style=\"font-family: Cambria Math;\">.</span></strong></p>\n",
                    solution_hi: "<p><strong>12.(b)</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2357;&#2376;&#2343; &#2312;&#2350;&#2375;&#2354; &#2346;&#2340;&#2366; &#2342;&#2379; &#2349;&#2366;&#2327;&#2379;&#2306; &#2360;&#2375; &#2348;&#2344;&#2366; &#2319;&#2325; &#2346;&#2340;&#2366; &#2361;&#2376;, </span><strong>&#2337;&#2379;&#2350;&#2375;&#2344; &#2344;&#2366;&#2350; &#2325;&#2375; &#2348;&#2366;&#2342; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2325;&#2366; &#2344;&#2366;&#2350; &#2310;&#2340;&#2366; &#2361;&#2376;</strong><span style=\"font-weight: 400;\">&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "31",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> An alternate name for the completely interconnected network topology is</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2369;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2376;&#2325;&#2354;&#2381;&#2346;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">I</span></p>\n",
                    options_en: ["<p>mesh</p>\n", "<p>star</p>\n", 
                                "<p>tree</p>\n", "<p>ring</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2350;&#2375;&#2358;(mesh)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2360;&#2381;&#2335;&#2366;&#2352;(star)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2335;&#2381;&#2352;&#2368;(tree</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2352;&#2367;&#2306;&#2327;(ring</span></p>\n"],
                    solution_en: "<p>13.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">In </span><strong><span style=\"font-family: Cambria Math;\">mesh topology</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>all nodes are connected to each other. So bi-network links between each node is possible in Mesh network.A </span><strong><span style=\"font-family: Cambria Math;\">star topology</span></strong><span style=\"font-family: Cambria Math;\"> is a topology for a Local Area Network (LAN) in which all nodes are individually </span><span style=\"font-family: Cambria Math;\">connected to a central connection point, like a</span><span style=\"font-family: Cambria Math;\"> <strong>hub or a switch</strong></span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">A</span><span style=\"font-family: Cambria Math;\"> tree topology </span><span style=\"font-family: Cambria Math;\">is a special type of structure where many connected elements are arranged like the branches of a tree.</span><span style=\"font-family: Cambria Math;\">A<strong> </strong></span><strong><span style=\"font-family: Cambria Math;\">rin</span><span style=\"font-family: Cambria Math;\">g topology</span></strong><span style=\"font-family: Cambria Math;\"> is a </span><span style=\"font-family: Cambria Math;\">network</span><span style=\"font-family: Cambria Math;\"> configuration where device connections create a circular </span><span style=\"font-family: Cambria Math;\">data</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">path.</span></p>\n",
                    solution_hi: "<p><strong>13.(a)</strong><span style=\"font-weight: 400;\"> </span><strong>&#2350;&#2375;&#2358; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(mesh topology)</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; &#2360;&#2349;&#2368; &#2344;&#2379;&#2337; &#2319;&#2325; &#2342;&#2370;&#2360;&#2352;&#2375; &#2360;&#2375; &#2332;&#2369;&#2337;&#2364;&#2375; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319; &#2350;&#2375;&#2358; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325;(mesh network) &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2344;&#2379;&#2337; &#2325;&#2375; &#2348;&#2368;&#2330; &#2342;&#2381;&#2357;&#2367;-&#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325;(bi-network) &#2354;&#2367;&#2306;&#2325; &#2360;&#2306;&#2349;&#2357; &#2361;&#2376;&#2404;&#2319;&#2325; </span><strong>&#2360;&#2381;&#2335;&#2366;&#2352; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(star topology)</strong><span style=\"font-weight: 400;\"> &#2354;&#2379;&#2325;&#2354; &#2319;&#2352;&#2367;&#2351;&#2366; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; (LAN) &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2349;&#2368; &#2344;&#2379;&#2337; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340; &#2352;&#2370;&#2346; &#2360;&#2375; &#2319;&#2325; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2325;&#2344;&#2375;&#2325;&#2381;&#2358;&#2344; &#2348;&#2367;&#2306;&#2342;&#2369; &#2360;&#2375; &#2332;&#2369;&#2337;&#2364;&#2375; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2332;&#2376;&#2360;&#2375; &#2361;&#2348;(hub) &#2351;&#2366; &#2360;&#2381;&#2357;&#2367;&#2330;(switch)&#2404;</span><strong>&#2335;&#2381;&#2352;&#2368; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(tree topology) </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2357;&#2367;&#2358;&#2375;&#2359; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2360;&#2306;&#2352;&#2330;&#2344;&#2366; &#2361;&#2376; &#2332;&#2361;&#2366;&#2306; &#2325;&#2312; &#2332;&#2369;&#2337;&#2364;&#2375; &#2361;&#2369;&#2319; &#2340;&#2340;&#2381;&#2357; &#2319;&#2325; &#2346;&#2375;&#2337;&#2364; &#2325;&#2368; &#2358;&#2366;&#2326;&#2366;&#2323;&#2306; &#2325;&#2368; &#2340;&#2352;&#2361; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span><strong>&#2352;&#2367;&#2306;&#2327; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(ring topology)</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2377;&#2344;&#2381;&#2347;&#2364;&#2367;&#2327;&#2352;&#2375;&#2358;&#2344; &#2361;&#2376; &#2332;&#2361;&#2366;&#2306; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2325;&#2344;&#2375;&#2325;&#2381;&#2358;&#2344; &#2319;&#2325; &#2360;&#2352;&#2381;&#2325;&#2369;&#2354;&#2352; &#2337;&#2375;&#2335;&#2366; &#2346;&#2341; &#2348;&#2344;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2342;&#2379; &#2309;&#2344;&#2381;&#2351; &#2360;&#2375; &#2332;&#2369;&#2337;&#2364;&#2366; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2376;&#2360;&#2375; &#2319;&#2325; &#2360;&#2352;&#2381;&#2325;&#2354; &#2346;&#2352; &#2348;&#2367;&#2306;&#2342;&#2369;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "31",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> All of the following are examples of input device except</span></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> _____ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2337;&#2364;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2346;&#2369;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2357;&#2366;&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>keyboard</p>\n", "<p>scanner</p>\n", 
                                "<p>printer</p>\n", "<p>mouse</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337; (keyboard)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2360;&#2381;&#2325;&#2376;&#2344;&#2352; (scanner)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352; (printer)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2313;&#2360; (mouse)</span></p>\n"],
                    solution_en: "<p>14.(c) <span style=\"font-family: Cambria Math;\">A printer prints information and data from the computer and gives</span><span style=\"font-family: Cambria Math;\"> <strong>output</strong> </span><span style=\"font-family: Cambria Math;\">onto a paper. On the other hand, scanners ,mouse, and keyboards are input devices.</span></p>\n",
                    solution_hi: "<p>14.(c) <span style=\"font-family: Cambria Math;\">&#2346;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2352;&#2367;&#2306;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2370;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2367;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2327;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2313;&#2335;&#2346;&#2369;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2325;&#2376;&#2344;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2346;&#2369;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2357;&#2366;&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "31",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Antivirus software is an example of _______</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2306;&#2335;&#2368;&#2357;&#2366;&#2351;&#2352;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">_______</span></p>\n",
                    options_en: ["<p>a security</p>\n", "<p>business software</p>\n", 
                                "<p>an operating system</p>\n", "<p>an offic<span style=\"font-family: Cambria Math;\">e suite</span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2319;&#2325; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; (a security)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2319;&#2325; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; (a security)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2348;&#2367;&#2395;&#2344;&#2375;&#2360;&nbsp; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (business software)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2325; &#2321;&#2347;&#2367;&#2360; &#2360;&#2369;&#2311;&#2335; (an office suite)</span></p>\n"],
                    solution_en: "<p>15.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Antivirus software is an example of<strong> </strong></span><strong><span style=\"font-family: Cambria Math;\">security utility</span><span style=\"font-family: Cambria Math;\">.</span></strong></p>\n",
                    solution_hi: "<p>15.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2306;&#2335;&#2368;&#2357;&#2366;&#2351;&#2352;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">security utility</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "31",
                    question_en: "<p>16.<span style=\"font-family: Cambria Math;\"> All of the following are examples of real-security and privacy risks except</span></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2379;&#2346;&#2344;&#2368;&#2351;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;&#2326;&#2367;&#2350;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2357;&#2366;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>hackers</p>\n", "<p>spam</p>\n", 
                                "<p>vi<span style=\"font-family: Cambria Math;\">ruses </span></p>\n", "<p>identify theft</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2361;&#2376;&#2325;&#2352;&#2381;&#2360; (hackers)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2346;&#2376;&#2350; ( spam)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2357;&#2366;&#2351;&#2352;&#2360; (virus)</span></p>\n", "<p><span style=\"font-weight: 400;\"> &#2330;&#2379;&#2352;&#2368; &#2325;&#2368; &#2346;&#2361;&#2330;&#2366;&#2344; &#2325;&#2352;&#2344;&#2366; (identify theft)</span></p>\n"],
                    solution_en: "<p>16.(b) <span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>Spam</strong>&rarr;</span><span style=\"font-family: Cambria Math;\">Spam email is unsolicited and unwanted junk email sent out in bulk to an indiscriminate recipient list.</span></p>\n",
                    solution_hi: "<p>16.(b)<span style=\"font-family: Cambria Math;\"> Spam&rarr;</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2376;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2312;&#2350;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2366;&#2306;&#2331;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2366;&#2306;&#2331;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2312;&#2350;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2343;&#2366;&#2343;&#2369;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2325;&#2352;&#2381;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2370;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2341;&#2379;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2375;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "31",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">In which topology, every</span><span style=\"font-family: Cambria Math;\"> node is connected to two other nodes?</span></p>\n",
                    question_hi: "<p>17.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2379;&#2337;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2369;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Bus topology</p>\n", "<p>ring topology</p>\n", 
                                "<p>Star topology</p>\n", "<p>Mesh topology</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2348;&#2360; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368; (Bus topology)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2352;&#2367;&#2306;&#2327; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368; (ring topology)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\">&#2360;&#2381;&#2335;&#2366;&#2352; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368; (Star topology)</span></p>\n", "<p><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2358; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368; (Mesh topology)</span></p>\n"],
                    solution_en: "<p>17.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">A<strong> </strong></span><strong><span style=\"font-family: Cambria Math;\">rin</span><span style=\"font-family: Cambria Math;\">g topology</span></strong><span style=\"font-family: Cambria Math;\"> is a </span><span style=\"font-family: Cambria Math;\">network</span><span style=\"font-family: Cambria Math;\"> configuration where device connections create a circular </span><span style=\"font-family: Cambria Math;\">data</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">path. </span><span style=\"font-family: Cambria Math;\">Each networked device is connected to two others, like points on a circle.</span><strong><span style=\"font-family: Cambria Math;\">Mesh topology</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>is </span><span style=\"font-family: Cambria Math;\">a type of networking where all nodes cooperate to distribute data amongst each other.A </span><strong><span style=\"font-family: Cambria Math;\">star topology</span></strong><span style=\"font-family: Cambria Math;\"> is a topology for a Local Area Network (LAN) in which all nodes are individually connected to a central connection point, like a</span><span style=\"font-family: Cambria Math;\"> <strong>hub or a switch</strong></span><span style=\"font-family: Cambria Math;\"><strong>.</strong> A </span><span style=\"font-family: Cambria Math;\">linear bus topology</span><span style=\"font-family: Cambria Math;\"> consists of a main run of cable with a terminator at each end.</span></p>\n",
                    solution_hi: "<p><strong>17.(b)</strong><span style=\"font-weight: 400;\"> </span><strong>&#2352;&#2367;&#2306;&#2327; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(ring topology)</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2377;&#2344;&#2381;&#2347;&#2364;&#2367;&#2327;&#2352;&#2375;&#2358;&#2344; &#2361;&#2376; &#2332;&#2361;&#2366;&#2306; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2325;&#2344;&#2375;&#2325;&#2381;&#2358;&#2344; &#2319;&#2325; &#2360;&#2352;&#2381;&#2325;&#2369;&#2354;&#2352; &#2337;&#2375;&#2335;&#2366; &#2346;&#2341; &#2348;&#2344;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2342;&#2379; &#2309;&#2344;&#2381;&#2351; &#2360;&#2375; &#2332;&#2369;&#2337;&#2364;&#2366; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2376;&#2360;&#2375; &#2319;&#2325; &#2360;&#2352;&#2381;&#2325;&#2354; &#2346;&#2352; &#2348;&#2367;&#2306;&#2342;&#2369;&#2404;</span><strong> &#2350;&#2375;&#2358; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(mesh topology)</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2368; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325;&#2367;&#2306;&#2327; &#2361;&#2376; &#2332;&#2361;&#2366;&#2306; &#2360;&#2349;&#2368; &#2344;&#2379;&#2337; &#2319;&#2325; &#2342;&#2370;&#2360;&#2352;&#2375; &#2325;&#2375; &#2348;&#2368;&#2330; &#2337;&#2375;&#2335;&#2366; &#2357;&#2367;&#2340;&#2352;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2361;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2319;&#2325; </span><strong>&#2360;&#2381;&#2335;&#2366;&#2352; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(star topology) </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2354;&#2379;&#2325;&#2354; &#2319;&#2352;&#2367;&#2351;&#2366; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; (LAN) &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2360;&#2349;&#2368; &#2344;&#2379;&#2337; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2327;&#2340; &#2352;&#2370;&#2346; &#2360;&#2375; &#2319;&#2325; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2325;&#2344;&#2375;&#2325;&#2381;&#2358;&#2344; &#2348;&#2367;&#2306;&#2342;&#2369; &#2360;&#2375; &#2332;&#2369;&#2337;&#2364;&#2375; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2332;&#2376;&#2360;&#2375; &#2361;&#2348;(hub) &#2351;&#2366; &#2360;&#2381;&#2357;&#2367;&#2330;(switch)&#2404; &#2319;&#2325; </span><strong>&#2352;&#2376;&#2326;&#2367;&#2325; &#2348;&#2360; &#2335;&#2379;&#2346;&#2379;&#2354;&#2377;&#2332;&#2368;(linear bus topology) </strong><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2331;&#2379;&#2352; &#2346;&#2352; &#2319;&#2325; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2375;&#2335;&#2352; &#2325;&#2375; &#2360;&#2366;&#2341; &#2325;&#2375;&#2348;&#2354; &#2325;&#2366; &#2319;&#2325; &#2350;&#2369;&#2326;&#2381;&#2351; &#2349;&#2366;&#2327; &#2361;&#2379;&#2340;&#2366;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "31",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">Which one of the following is not an email service provider?</span></p>\n",
                    question_hi: "<p>18.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2312;&#2350;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;</span><span style=\"font-family: Cambria Math;\">&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Bing</p>\n", "<p>Hotmail</p>\n", 
                                "<p>Gmail</p>\n", "<p>Yahoo mail</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2348;&#2367;&#2306;&#2327;(Bing)</span></p>\n", "<p><span style=\"font-weight: 400;\"> &#2361;&#2377;&#2335;&#2350;&#2375;&#2354;(Hotmail)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\"> &#2332;&#2368;&#2350;&#2375;&#2354;(Gmail)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2351;&#2366;&#2361;&#2370; &#2350;&#2375;&#2354;(Yahoo mail)</span></p>\n"],
                    solution_en: "<p>18.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Microsoft Bing </span><strong><span style=\"font-family: Cambria Math;\">(commonly known as Bing)</span></strong><span style=\"font-family: Cambria Math;\"> is a web search engine owned and </span><span style=\"font-family: Cambria Math;\">operated by Microsoft.</span></p>\n",
                    solution_hi: "<p><strong>18.(a)</strong><span style=\"font-weight: 400;\"> &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2360;&#2377;&#2347;&#2381;&#2335; &#2348;&#2367;&#2306;&#2327; (</span><strong>&#2310;&#2350;&#2340;&#2380;&#2352; &#2346;&#2352; &#2348;&#2367;&#2306;&#2327; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;</strong><span style=\"font-weight: 400;\">) &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2360;&#2377;&#2347;&#2381;&#2335; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2360;&#2381;&#2357;&#2366;&#2350;&#2367;&#2340;&#2381;&#2357; &#2324;&#2352; &#2360;&#2306;&#2330;&#2366;&#2354;&#2367;&#2340; &#2319;&#2325; &#2357;&#2375;&#2348; &#2360;&#2352;&#2381;&#2330; &#2311;&#2306;&#2332;&#2344; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "31",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> Which ports provide slow speed data transmission.</span></p>\n",
                    question_hi: "<p>19. <span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2379;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2354;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2368;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2350;&#2367;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> (slow speed data transmission) </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Parallel</p>\n", "<p>Firewire</p>\n", 
                                "<p>USB</p>\n", "<p>Serial</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2346;&#2376;&#2352;&#2375;&#2354;&#2354; (Parallel)</span></p>\n", "<p><span style=\"font-weight: 400;\"> &#2347;&#2366;&#2351;&#2352;&#2357;&#2366;&#2351;&#2352; (Firewire)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p>USB</p>\n", "<p><span style=\"font-weight: 400;\"> &#2360;&#2368;&#2352;&#2367;&#2351;&#2354; (Serial</span></p>\n"],
                    solution_en: "<p>19.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>Serial Port </strong>&rarr;</span><span style=\"font-family: Cambria Math;\"> It transmits one bit of data through a single wire.Sinc</span><span style=\"font-family: Cambria Math;\">e, data is transmitted serially as a single bit. It provides </span><span style=\"font-family: Cambria Math;\">slow speed data transmission.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Parallel ports</strong> &rarr; </span><span style=\"font-family: Cambria Math;\">It is an interface for connecting eight or more data wires.Parallel port is used to connect</span><span style=\"font-family: Cambria Math;\"> printers</span><span style=\"font-family: Cambria Math;\"> to the computer.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Universal Serial Bus (USB) </strong>&rarr;</span><span style=\"font-family: Cambria Math;\"> I</span><span style=\"font-family: Cambria Math;\">t is a common and popular external port available with computers. Normally, two to four USB ports are provided on a PC.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Firewire</strong> &rarr;</span><span style=\"font-family: Cambria Math;\"> It is used to connect audio and video multimedia devices like video cameras.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Firewire is an expensive technology used for lar</span><span style=\"font-family: Cambria Math;\">ge data movement.</span></p>\n",
                    solution_hi: "<p><strong>19.(d)</strong><span style=\"font-weight: 400;\"> </span><strong>&#2360;&#2368;&#2352;&#2367;&#2351;&#2354; &#2346;&#2379;&#2352;&#2381;&#2335;</strong><span style=\"font-weight: 400;\"> &rarr; &#2351;&#2361; &#2319;&#2325; &#2348;&#2367;&#2335; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2319;&#2325; &#2340;&#2366;&#2352; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2346;&#2381;&#2352;&#2360;&#2366;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2330;&#2370;&#2306;&#2325;&#2367;, &#2337;&#2375;&#2335;&#2366; &#2319;&#2325; &#2348;&#2367;&#2335; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2381;&#2352;&#2350;&#2367;&#2325; &#2352;&#2370;&#2346; &#2360;&#2375; &#2346;&#2381;&#2352;&#2360;&#2366;&#2352;&#2367;&#2340; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; </span><strong>&#2360;&#2381;&#2354;&#2379; &#2360;&#2381;&#2346;&#2368;&#2337; &#2337;&#2375;&#2335;&#2366; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2350;&#2367;&#2358;&#2344;</strong><span style=\"font-weight: 400;\"> &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><strong>&#2346;&#2376;&#2352;&#2375;&#2354;&#2354; &#2346;&#2379;&#2352;&#2381;&#2335;</strong><span style=\"font-weight: 400;\"> &rarr; &#2351;&#2361; &#2310;&#2336; &#2351;&#2366; &#2309;&#2343;&#2367;&#2325; &#2337;&#2375;&#2335;&#2366; &#2340;&#2366;&#2352;&#2379;&#2306; &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2319;&#2325; &#2311;&#2306;&#2335;&#2352;&#2347;&#2364;&#2375;&#2360; (interface) &#2361;&#2376;&#2404; &#2346;&#2376;&#2352;&#2375;&#2354;&#2354; &#2346;&#2379;&#2352;&#2381;&#2335; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352; &#2325;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2375; &#2332;&#2379;&#2337;&#2364;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><strong>&#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360;&#2354; &#2360;&#2368;&#2352;&#2367;&#2351;&#2354; &#2348;&#2360; (USB)</strong><span style=\"font-weight: 400;\"> &rarr; &#2351;&#2361; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2360;&#2366;&#2341; &#2313;&#2346;&#2354;&#2348;&#2381;&#2343; &#2319;&#2325; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351; &#2324;&#2352; &#2354;&#2379;&#2325;&#2346;&#2381;&#2352;&#2367;&#2351; &#2319;&#2325;&#2381;&#2360;&#2335;&#2352;&#2381;&#2344;&#2354; &#2346;&#2379;&#2352;&#2381;&#2335; (external port) &#2361;&#2376;&#2404; &#2310;&#2350; &#2340;&#2380;&#2352; &#2346;&#2352;, &#2319;&#2325; PC &#2346;&#2352; &#2342;&#2379; &#2360;&#2375; &#2330;&#2366;&#2352; USB &#2346;&#2379;&#2352;&#2381;&#2335; &#2342;&#2367;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><strong>&#2347;&#2366;&#2351;&#2352;&#2357;&#2366;&#2351;&#2352; </strong><span style=\"font-weight: 400;\">&rarr; &#2311;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2357;&#2368;&#2337;&#2367;&#2351;&#2379; &#2325;&#2376;&#2350;&#2352;&#2379;&#2306; &#2332;&#2376;&#2360;&#2375; &#2321;&#2337;&#2367;&#2351;&#2379; &#2324;&#2352; &#2357;&#2368;&#2337;&#2367;&#2351;&#2379; &#2350;&#2354;&#2381;&#2335;&#2368;&#2350;&#2368;&#2337;&#2367;&#2351;&#2366; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2347;&#2366;&#2351;&#2352;&#2357;&#2366;&#2351;&#2352; &#2319;&#2325; &#2350;&#2361;&#2306;&#2327;&#2368; &#2340;&#2325;&#2344;&#2368;&#2325; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2348;&#2337;&#2364;&#2375; &#2337;&#2375;&#2335;&#2366; &#2360;&#2306;&#2330;&#2354;&#2344; (data movement) &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">Which of the following terms is used to specify the resolution of a laser printer?</span></p>\n",
                    question_hi: "<p>20.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2332;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2375;&#2332;&#2379;&#2354;&#2381;&#2351;&#2370;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2381;&#2342;&#2367;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2358;&#2348;&#2381;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>LPM</p>\n", "<p>CPM</p>\n", 
                                "<p>DPI</p>\n", "<p>PPM</p>\n"],
                    options_hi: ["<p>LPM</p>\n", "<p>CPM</p>\n",
                                "<p>D<span style=\"font-family: Cambria Math;\">PI </span></p>\n", "<p>PPM</p>\n"],
                    solution_en: "<p><strong>20.(c)</strong><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">The standard resolution in most laser printers today is </span><strong>dots per inch (dpi)</strong><span style=\"font-weight: 400;\">.</span></p>\r\n<p><strong>Lines per minute(LPM)</strong><span style=\"font-weight: 400;\">: A measure of the speed of a printer.</span></p>\r\n<p><strong>CPM(</strong><strong> Copies per minute) </strong><strong>&rarr;</strong><strong> </strong><span style=\"font-weight: 400;\">Printer has a maximum copy speed of 9 cpm for black &amp; white and 6 cpm for color.&nbsp;</span></p>\r\n<p><strong>PPM(Pages Per Minute) &rarr;</strong><span style=\"font-weight: 400;\">This</span><span style=\"font-weight: 400;\"> term refers to the quantity of letter-sized pages a machine can produce in one minute while operating at maximum capacity.</span></p>\n",
                    solution_hi: "<p><strong>20.(c)</strong><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">The standard resolution in most laser printers today is </span><strong>dots per inch (dpi)</strong><span style=\"font-weight: 400;\">.</span></p>\r\n<p><strong>Lines per minute(LPM)</strong><span style=\"font-weight: 400;\">: A measure of the speed of a printer.</span></p>\r\n<p><strong>CPM(</strong><strong> Copies per minute) </strong><strong>&rarr;</strong><strong> </strong><span style=\"font-weight: 400;\">Printer has a maximum copy speed of 9 cpm for black &amp; white and 6 cpm for color.&nbsp;</span></p>\r\n<p><strong>PPM(Pages Per Minute) &rarr;</strong><span style=\"font-weight: 400;\">This</span><span style=\"font-weight: 400;\"> term refers to the quantity of letter-sized pages a machine can produce in one minute while operating at maximum capacity.</span></p>\r\n<p>&nbsp;</p>\r\n<p><strong>[HSol]20.(c)</strong><span style=\"font-weight: 400;\"> &#2309;&#2343;&#2367;&#2325;&#2366;&#2306;&#2358; &#2354;&#2375;&#2332;&#2352; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352; &#2350;&#2375;&#2306; &#2350;&#2366;&#2344;&#2325; &#2352;&#2367;&#2332;&#2364;&#2377;&#2354;&#2381;&#2351;&#2370;&#2358;&#2344; </span><strong>&#2337;&#2377;&#2335;&#2381;&#2360; &#2346;&#2381;&#2352;&#2340;&#2367; &#2311;&#2306;&#2330; (DPI)</strong><span style=\"font-weight: 400;\"> &#2361;&#2376;&#2404;</span></p>\r\n<p><strong>&#2354;&#2366;&#2311;&#2344;&#2381;&#2360; &#2346;&#2381;&#2352;&#2340;&#2367; &#2350;&#2367;&#2344;&#2335; (LPM)</strong><span style=\"font-weight: 400;\">: &#2319;&#2325; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352; &#2325;&#2368; &#2360;&#2381;&#2346;&#2368;&#2337; &#2325;&#2366; &#2350;&#2366;&#2346; &#2361;&#2376;&#2404;</span></p>\r\n<p><strong>CPM (&#2325;&#2377;&#2346;&#2368; &#2346;&#2381;&#2352;&#2340;&#2367; &#2350;&#2367;&#2344;&#2335;) &rarr;</strong><span style=\"font-weight: 400;\"> &#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2352; &#2325;&#2368; &#2309;&#2343;&#2367;&#2325;&#2340;&#2350; &#2325;&#2377;&#2346;&#2368; &#2360;&#2381;&#2346;&#2368;&#2337; &#2348;&#2381;&#2354;&#2376;&#2325; &#2319;&#2306;&#2337; &#2357;&#2381;&#2361;&#2366;&#2311;&#2335; &#2325;&#2375; &#2354;&#2367;&#2319; 9 CPM &#2324;&#2352; &#2325;&#2354;&#2352; &#2325;&#2375; &#2354;&#2367;&#2319; 6 CPM &#2361;&#2376;&#2404;</span></p>\r\n<p><strong>PPM (&#2346;&#2375;&#2332; &#2346;&#2381;&#2352;&#2340;&#2367; &#2350;&#2367;&#2344;&#2335;) </strong><span style=\"font-weight: 400;\">&rarr; &#2351;&#2361; &#2358;&#2348;&#2381;&#2342; &#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306; &#2325;&#2375; &#2310;&#2325;&#2366;&#2352; &#2325;&#2375; &#2346;&#2371;&#2359;&#2381;&#2336;&#2379;&#2306; &#2325;&#2368; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366; &#2325;&#2379; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2379; &#2319;&#2325; &#2350;&#2358;&#2368;&#2344; &#2309;&#2343;&#2367;&#2325;&#2340;&#2350; &#2325;&#2381;&#2359;&#2350;&#2340;&#2366; &#2346;&#2352; &#2325;&#2366;&#2350; &#2325;&#2352;&#2340;&#2375; &#2361;&#2369;&#2319; &#2319;&#2325; &#2350;&#2367;&#2344;&#2335; &#2350;&#2375;&#2306; &#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344; &#2325;&#2352; &#2360;&#2325;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>