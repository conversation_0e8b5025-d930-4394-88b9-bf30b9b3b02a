<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 50</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">50</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 49,
                end: 49
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following was opened by Gour Mohan Addy in 1829?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन-सा, 1829 में गौर मोहन एडी द्वारा खोला गया था?</p>",
                    options_en: ["<p>Hindi Seminary</p>", "<p>Anglo-India Seminary</p>", 
                                "<p>Anglo-Indian School</p>", "<p>Oriental Seminary</p>"],
                    options_hi: ["<p>हिंदी सेमिनरी (Hindi Seminary)</p>", "<p>एंग्लो-इंडिया सेमिनरी (Anglo-India Seminary)</p>",
                                "<p>एंग्लो-इंडियन स्कूल (Anglo-Indian School)</p>", "<p>ओरिएंटल सेमिनरी (Oriental Seminary)</p>"],
                    solution_en: "<p>1.(d) <strong>Oriental Seminary</strong> was the earliest privately run, first-rate school for children of Hindu parents in Kolkata (then known as Calcutta). St George\'s Anglo- Indian Higher Secondary School was founded in 1715 as the Military (later Madras) Male Orphan Asylum and is one of the oldest schools in the world and the oldest in India.</p>",
                    solution_hi: "<p>1.(d) <strong>ओरिएंटल सेमिनरी</strong> (Oriental Seminary) कोलकाता (तब इसका नाम कलकत्ता था) में हिंदू माता-पिता के बच्चों के लिए निजी तौर पर संचालित सबसे पहला, सर्वोत्कृष्ट स्कूल था। सेंट जॉर्ज एंग्लो-इंडियन हायर सेकेंडरी स्कूल की स्थापना 1715 में मिलिट्री (बाद में मद्रास) पुरुष अनाथ आश्रम के रूप में की गई थी। यह भारत का सबसे पुराना स्कूल है तथा विश्व के सबसे पुराने स्कूलों में से एक है ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. In which pair of states is Holi also celebrated as Dola Purnima?</p>",
                    question_hi: "<p>2. किस राज्य के युग्मों में होली को डोला पूर्णिमा के रूप में भी मनाया जाता है?</p>",
                    options_en: ["<p>West Bengal and Odisha</p>", "<p>Rajasthan and Punjab</p>", 
                                "<p>Nagaland and Sikkim</p>", "<p>Maharashtra and Gujarat</p>"],
                    options_hi: ["<p>पश्चिम बंगाल और उड़ीसा</p>", "<p>राजस्थान और पंजाब</p>",
                                "<p>नागालैंड और सिक्किम</p>", "<p>महाराष्ट्र और गुजरात</p>"],
                    solution_en: "<p>2.(a)<strong> West Bengal and Odisha.</strong> Holi Celebrations and States: Rang Panchami ( Maharashtra), Lathmar Holi and Holi Milan (Uttar Pradesh), Holla Mohalla (Punjab), Shigmo (Goa), Kumaoni Holi (Uttarakhand), Manjal Kuli (Kerala).</p>",
                    solution_hi: "<p>2.(a)<strong> पश्चिम बंगाल और उड़ीसा।</strong> होली समारोह और राज्य: रंग पंचमी (महाराष्ट्र), लट्ठमार होली और होली मिलन (उत्तर प्रदेश), होला मोहल्ला (पंजाब), शिगमो (गोवा), कुमाऊंनी होली (उत्तराखंड), मंजल कुली (केरल)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which political party was in power when the Sardar Swaran Singh Committee made recommendations about Fundamental Duties?</p>",
                    question_hi: "<p>3. जब सरदार स्वर्ण सिंह समिति ने मौलिक कर्तव्यों के विषय में सिफारिशें कीं तब कौन-सी राजनीतिक पार्टी सत्ता में थी?</p>",
                    options_en: ["<p>Indian National Congress</p>", "<p>Bharatiya Janata Party</p>", 
                                "<p>Janata Dal</p>", "<p>Communist Party of India</p>"],
                    options_hi: ["<p>भारतीय राष्ट्रीय कांग्रेस</p>", "<p>भारतीय जनता पार्टी</p>",
                                "<p>जनता दल</p>", "<p>भारतीय कम्युनिस्ट पार्टी</p>"],
                    solution_en: "<p>3.(a) <strong>Indian National Congress (INC): </strong>Founded - 28 December 1885, Founder - Allan Octavian Hume. The idea of Fundamental Duties (Article 51A) has been borrowed from Russia. FDs were incorporated in Part IV-A of the Constitution by the 42nd Constitutional Amendment Act, 1976. Originally the duties were 10 in number, later on by 86th Amendment in 2002, they leveled up to 11.</p>",
                    solution_hi: "<p>3.(a) <strong>भारतीय राष्ट्रीय कांग्रेस (INC): </strong>स्थापना - 28 दिसंबर 1885, संस्थापक - एलन ऑक्टेवियन ह्यूम। मौलिक कर्तव्यों (अनुच्छेद 51A) का विचार रूस से लिया गया है। मौलिक कर्तव्य (FD) को 42वें संवैधानिक संशोधन अधिनियम, 1976 द्वारा संविधान के भाग IV-A में शामिल किया गया था। मूल रूप से मौलिक कर्तव्यों की संख्या 10 थी, बाद में 86वें संशोधन अधिनियम, 2002 के माध्यम से, 11वें मौलिक कर्तव्य को जोड़ा गया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. Which Article of the Indian Constitution is related with promotion of co-operative societies?",
                    question_hi: "<p>4. भारतीय संविधान का कौन-सा अनुच्छेद सहकारी समितियों को बढ़ावा देने से संबंधित है?</p>",
                    options_en: ["<p>Article 31 B</p>", "<p>Article 51 A</p>", 
                                "<p>Article 43 B</p>", "<p>Article 39 A</p>"],
                    options_hi: ["<p>अनुच्छेद 31 B</p>", "<p>अनुच्छेद 51 A</p>",
                                "<p>अनुच्छेद 43 B</p>", "<p>अनुच्छेद 39 A</p>"],
                    solution_en: "<p>4.(c) <strong>Article 43 B. </strong>Directive Principles of State Policy (DPSP): Articles 36-51 under Part-IV. They are borrowed from the Constitution of Ireland. Other important articles: Article 39A - Equal justice and free legal aid. Articles 51A - Fundamental duties. Article 31B - Validation of certain Acts and Regulations.</p>",
                    solution_hi: "<p>4.(c) <strong>अनुच्छेद 43 B</strong> । राज्य के नीति निर्देशक सिद्धांत (DPSP): भाग-IV के तहत अनुच्छेद 36-51 तक। इसे आयरलैंड के संविधान से लिया गया है। अन्य महत्वपूर्ण अनुच्छेद : अनुच्छेद 39A - समान न्याय और निःशुल्क विधिक सहायता। अनुच्छेद 51A - मौलिक कर्तव्य। अनुच्छेद 31B - कुछ अधिनियमों और विनियमों की मान्यता।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. The Indian government launched the \'Garib Kalyan Rojgar Abhiyan\' (\'GKRA\') on 20th June 2020 to provide work for how many days?",
                    question_hi: "<p>5. भारत सरकार ने कितने दिनों के लिए काम उपलब्ध कराने के लिए 20 जून 2020 को \'गरीब कल्याण रोज़गार अभियान\' (GKRA\') शुरू किया?</p>",
                    options_en: ["<p>125 days</p>", "<p>130 days</p>", 
                                "<p>115 days</p>", "<p>120 days</p>"],
                    options_hi: ["<p>125 दिन</p>", "<p>130 दिन</p>",
                                "<p>115 दिन</p>", "<p>120 दिन</p>"],
                    solution_en: "<p>5.(a) <strong>125 days.</strong> \'Garib Kalyan Rojgar Abhiyan\' (GKRA) - In this, a project worth Rs 50,000 crore will be implemented in a mission mode campaign in 116 districts of 6 states (Bihar, Uttar Pradesh, Madhya Pradesh, Rajasthan, Odisha, Jharkhand). The Prime Minister said the Atma Nirbhar Bharat Campaign itself was launched with a 1.75 Lakh Crore package under PM Garib Kalyan Yojana.</p>",
                    solution_hi: "<p>5.(a) <strong>125 दिन। \'</strong>गरीब कल्याण रोजगार अभियान\' (GKRA) - इसमें 50,000 करोड़ रुपये की परियोजना को 6 राज्यों (बिहार, उत्तर प्रदेश, मध्य प्रदेश, राजस्थान, ओडिशा, झारखंड) के 116 जिलों में मिशन मोड अभियान में लागू किया जाएगा। प्रधानमंत्री ने कहा कि आत्मनिर्भर भारत अभियान ही पीएम गरीब कल्याण योजना के तहत 1.75 लाख करोड़ के पैकेज के साथ शुरू किया गया था।।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. In 2016, Yamini Krishnamurthy received the Padma Vibhushan for her contribution to which fields of classical dance?",
                    question_hi: "<p>6. 2016 में यामिनी कृष्णमूर्ति को शास्त्रीय नृत्य के किस क्षेत्र में उनके योगदान के लिए पद्म विभूषण से सम्मानित किया गया?</p>",
                    options_en: ["<p>Bharatanatyam and Kathak</p>", "<p>Bharatanatyam and Manipuri</p>", 
                                "<p>Bharatanatyam and Odissi</p>", "<p>Bharatanatyam and Kuchipudi</p>"],
                    options_hi: ["<p>भरतनाट्यम और कथक</p>", "<p>भरतनाट्यम और मणिपुरी</p>",
                                "<p>भरतनाट्यम और ओडिसी</p>", "<p>भरतनाट्यम और कुचिपुड़ी</p>"],
                    solution_en: "<p>6.(d) <strong>Bharatanatyam and Kuchipudi. </strong>Yamini Krishnamurthy started the \'Yamini school of Dance\' in Delhi in 1990. Her Awards: Padma Shri (1968), Padma Bhushan (2001), Sangeet Natak Akademi Award (1977).</p>",
                    solution_hi: "<p>6.(d) <strong>भरतनाट्यम और कुचिपुड़ी। </strong>\"यामिनी स्कूल ऑफ़ डांस\" की स्थापना 1990 में दिल्ली में यामिनी कृष्णमूर्ति द्वारा की गई थी। इनको प्राप्त पुरस्कार: पद्म श्री (1968), पद्म भूषण (2001), संगीत नाटक अकादमी पुरस्कार (1977)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7.The number of white rooks used in chess is _______.",
                    question_hi: "<p>7. शतरंज में उपयोग होने वाले सफेद हाथियों (rooks) की संख्या_______होती है।</p>",
                    options_en: ["<p>Three</p>", "<p>One</p>", 
                                "<p>Two</p>", "<p>Four</p>"],
                    options_hi: ["<p>तीन</p>", "<p>एक</p>",
                                "<p>दो</p>", "<p>चार</p>"],
                    solution_en: "<p>7.(c) <strong>Two</strong>. There are six types of chess pieces. Each player starts with 16 pieces: eight pawns, two bishops, two knights, two rooks, one queen, and one king. The queen is the most powerful chess piece. When a game begins, each side starts with one queen.</p>",
                    solution_hi: "<p>7.(c) <strong>दो।</strong> शतरंज के मोहरे 6 प्रकार के होते हैं। प्रत्येक खिलाड़ी 16 मोहरों से शुरू करता है: आठ प्यादे, दो बिशप (ऊंट), दो नाइट(घोडा), दो हाथी, एक रानी (वजीर) और एक राजा। रानी शतरंज का सबसे शक्तिशाली मोहरा है। जब कोई खेल शुरू होता है, तो प्रत्येक पक्ष एक रानी से शुरू होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. As per Human Development Report 2021-22, what is the female Life Expectancy at birth in India?",
                    question_hi: "<p>8. मानव विकास रिपोर्ट 2021-22 के अनुसार, भारत में जन्म के समय महिला जीवन प्रत्याशा कितनी है?</p>",
                    options_en: ["<p>63.9 years</p>", "<p>68.9 years</p>", 
                                "<p>73.9 years</p>", "<p>65.9 years</p>"],
                    options_hi: ["<p>63.9 वर्ष</p>", "<p>68.9 वर्ष</p>",
                                "<p>73.9 वर्ष</p>", "<p>65.9 वर्ष</p>"],
                    solution_en: "<p>8.(b) <strong>68.9 years. </strong>Human Development Report (HDR) - The first HDR was launched in 1990 by the Pakistani economist Mahbub ul Haq and Indian Nobel laureate Amartya Sen. Four indicators - Life expectancy at birth, Expected years of schooling, Mean years of schooling and Gross national income (GNI). Human Development Index (HDI) is based on the health of people, their level of education attainment and their standard of living.</p>",
                    solution_hi: "<p>8.(b)<strong> 68.9 वर्ष। </strong>मानव विकास रिपोर्ट (HDR) - पहला HDR, 1990 में पाकिस्तानी अर्थशास्त्री &lsquo;महबूब उल हक&rsquo; और भारतीय नोबेल पुरस्कार विजेता &lsquo;अमर्त्य सेन&rsquo; द्वारा शुरू किया गया था। चार संकेतक - जन्म के समय जीवन प्रत्याशा, स्कूली शिक्षा के अपेक्षित वर्ष, स्कूली शिक्षा के औसत वर्ष और सकल राष्ट्रीय आय ( GNI)। मानव विकास सूचकांक (HDI) , लोगों के स्वास्थ्य, उनकी शिक्षा प्राप्ति के स्तर और उनके जीवन स्तर पर आधारित है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. When lava makes its way through cracks and fissures developed in the ground, it freezes almost perpendicular to the ground. It cools under the same conditions to develop a wall-like structure. What is this structure called?",
                    question_hi: "<p>9. जब लावा, भूमि में विकसित दरारें और विदरों से अपना रास्ता बनाता है, तो यह जमीन के लगभग लंबवत जम जाता है। यह दीवार जैसी संरचना को विकसित करने के लिए उसी स्थिति में ठंडा हो जाता है। इस संरचना को क्या कहा जाता है?</p>",
                    options_en: ["<p>Lacoliths</p>", "<p>Dykes</p>", 
                                "<p>Lapolith</p>", "<p>Phacolith</p>"],
                    options_hi: ["<p>लैकोलिथ (Lacolith)</p>", "<p>डाइक्स (Dykes)</p>",
                                "<p>लापोलिथ (Lapolith)</p>", "<p>फैकोलिथ (Phacolith)</p>"],
                    solution_en: "<p>9.(b) <strong>Dykes</strong>. Other Intrusive Volcanic Landforms: Laccolith -These are large dome-shaped intrusive bodies with a level base and linked by a pipe-like channel from below. The Karnataka plateau is patterned with dome hills of granite rocks. Lopolith - It develops into a saucer shape, concave to the sky body. Phacolith - It is a wavy mass of intrusive rocks found at the base of synclines or the top of the anticline in the folded igneous country. Sills - These are solidified horizontal lava layers inside the earth.</p>",
                    solution_hi: "<p>9.(b) <strong>डाइक्स</strong>। अन्य अन्तर्वेधी ज्वालामुखीय भू-आकृतियाँ: लैकोलिथ - ये समतल आधार वाले बड़े गुंबद के आकार के अन्तर्वेधी पिंड हैं और ये नीचे से एक पाइप जैसे चैनल से जुड़े होते हैं। कर्नाटक का पठार, ग्रेनाइट चट्टानों की गुंबददार पहाड़ियों से निर्मित है। लैपोलिथ - यह आकाशीय निकाय के लिए एक तश्तरी के अवतल आकार में विकसित होता है। फैकोलिथ - यह अन्तर्वेधी चट्टानों का एक लहरदार समूह है जो सिंकलाइन (synclines) के आधार पर या वलित आग्नेय देश में एंटीक्लाइन(anticline) के शीर्ष पर पाया जाता है। सिल्स (Sills) - ये पृथ्वी के अंदर जमी हुई क्षैतिज लावा परतें हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10.  Which of the following is NOT found in animal cells?",
                    question_hi: "<p>10. निम्नलिखित में से कौन-सा विकल्प, जन्तु कोशिकाओं में नहीं पाया जाता है?</p>",
                    options_en: ["<p>Endoplasmic reticulum</p>", "<p>Mitochondria</p>", 
                                "<p>Plastid</p>", "<p>Golgi Complex</p>"],
                    options_hi: ["<p>अंतर्द्रव्यी जालिका</p>", "<p>माइटोकॉन्ड्रिया</p>",
                                "<p>प्लास्टिड</p>", "<p>गॉल्जी सम्मिश्र</p>"],
                    solution_en: "<p>10.(c) The cell wall, a central vacuole and plastids which are present in plant cells but absent in animal cells. The cell parts found only in animals but not in plants - Lysosomes, Centrioles, Cilia (some plant sperm have flagella). Mitochondria provide energy in the form of ATP by breaking down carbohydrates and sugar molecules.</p>",
                    solution_hi: "<p>10.(c) कोशिका भित्ति, एक केंद्रीय रसधानी और प्लास्टिड है जो पादप कोशिकाओं में उपस्थित होते हैं, परंतु जन्तु कोशिकाओं में अनुपस्थित होते हैं। कोशिका के ऐसे भाग जो केवल जंतुओं में पाए जाते हैं, पादपों में नहीं - लाइसोसोम, तारक केन्द्र (सेंट्रीओल्स), सिलिया (कुछ पादप के शुक्राणु में फ्लैगेला होता है)। माइटोकॉन्ड्रिया, कार्बोहाइड्रेट और शर्करा अणुओं को तोड़कर ATP के रूप में ऊर्जा प्रदान करते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Ustad Latafat Hussain Khan is associated with the_________.</p>",
                    question_hi: "<p>11. उस्ताद लताफत हुसैन खान_____ से संबंधित हैं।</p>",
                    options_en: ["<p>Patiala gharana</p>", "<p>Agra gharana</p>", 
                                "<p>Kirana gharana</p>", "<p>Gwalior gharana</p>"],
                    options_hi: ["<p>पटियाला घराने</p>", "<p>आगरा घराने</p>",
                                "<p>किराना घराने</p>", "<p>ग्वालियर घराने</p>"],
                    solution_en: "<p>11.(b) <strong>Agra gharana. </strong>Famous Gharanas in Music: Agra Gharana - Ustad Ghaghe Khuda Baksh, Faiyaz Khan, Bharathi Prathap and Waseem Ahmed Khan. Patiala Gharana - Ustad Fateh Ali Khan, and Ustad Ali Baksh. Kirana gharana - Sanhita Nanda and Sumitra Guha. Gwalior gharana - Shashwati Mandal, Meeta Pandit and Vasundhara Komkali.</p>",
                    solution_hi: "<p>11.(b)<strong> आगरा घराने।</strong> संगीत में प्रसिद्ध घराने: आगरा घराना - उस्ताद घाघे खुदा बख्श, फैयाज खान, भारती प्रताप और वसीम अहमद खान। पटियाला घराना - उस्ताद फ़तेह अली खान, और उस्ताद अली बख्श। किराना घराना - संहिता नंदा और सुमित्रा गुहा। ग्वालियर घराना - शाश्वती मंडल, मीता पंडित और वसुन्धरा कोमकली।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. Which of the following Articles deals with the amendment of the Indian Constitution?",
                    question_hi: "<p>12. निम्नलिखित में से कौन सा अनुच्छेद भारतीय संविधान के संशोधन से संबंधित है?</p>",
                    options_en: ["<p>Article 360</p>", "<p>Article 352</p>", 
                                "<p>Article 356</p>", "<p>Article 368</p>"],
                    options_hi: ["<p>अनुच्छेद 360</p>", "<p>अनुच्छेद 352</p>",
                                "<p>अनुच्छेद 356</p>", "<p>अनुच्छेद 368</p>"],
                    solution_en: "<p>12.(d) <strong>Article 368 </strong>(Part XX).<strong> P</strong>owers of Parliament to amend the constitution were borrowed from South Africa. Other important article: Article 352 - Proclamation of national Emergency. Article 356 - Provisions in case of failure of constitutional machinery in States. Article 360 - Empowers the President to invoke financial emergency. </p>",
                    solution_hi: "<p>12.(d) <strong>अनुच्छेद 368</strong> (भाग XX)। संविधान में संशोधन करने की संसद की शक्तियाँ दक्षिण अफ्रीका से ली गई है। अन्य महत्वपूर्ण अनुच्छेद : अनुच्छेद 352 - राष्ट्रीय आपातकाल की उद्घोषणा। अनुच्छेद 356 - राज्यों में संवैधानिक तंत्र की विफलता की स्थिति में प्रावधान। अनुच्छेद 360 - राष्ट्रपति को वित्तीय आपातकाल लागू करने का अधिकार देता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which canal has a head regulator capacity of 1133 cumecs (40000 cusecs) and a length of 532 km for which it is considered the world\'s largest lined irrigation canal?</p>",
                    question_hi: "<p>13. किस नहर की हेड रेगुलेटर क्षमता 1133 क्यूमेक (40000 क्यूसेक) और लंबाई 532 km है जिसके लिए इसे दुनिया की सबसे बड़ी आच्छादित सिंचाई नहर माना जाता है?</p>",
                    options_en: ["<p>Trivandrum-Shoranur Canal</p>", "<p>Kalingarayan Canal</p>", 
                                "<p>Cumbarjua Canal</p>", "<p>Narmada Main Canal</p>"],
                    options_hi: ["<p>त्रिवेंद्रम-शोरानूर नहर</p>", "<p>कलिंगारायण नहर</p>",
                                "<p>कंबरजुआ नहर</p>", "<p>नर्मदा मुख्य नहर</p>"],
                    solution_en: "<p>13.(d) <strong>Narmada Main Canal </strong>brings water from the Sardar Sarovar Dam. Kalingarayan Canal is a 90.5 km long irrigation canal in the Erode district, Tamil Nadu. It connects the three tributaries of the Cauvery &mdash; Bhavani, Noyyal and Amaravathi. Cumbarjua Canal is a distributary channel formed by the merger of the Mandovi and Zuari rivers in Goa.</p>",
                    solution_hi: "<p>13.(d) <strong>नर्मदा मुख्य नहर </strong>में सरदार सरोवर बांध से पानी आता है। कलिंगारायण नहर तमिलनाडु के इरोड जिले में 90.5 किमी लंबी सिंचाई नहर है। यह कावेरी की तीन सहायक नदियों &lsquo;भवानी, नोय्यल और अमरावती&rsquo; को जोड़ती है। कंबरजुआ नहर, गोवा में मांडवी और ज़ुआरी नदियों के विलय से बनी एक वितरण नहर है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. Which of the following statements is NOT true about the two-sector model?",
                    question_hi: "<p>14.निम्नलिखित में से कौन-सा कथन दो सेक्टर मॉडल के बारे में सत्य नहीं है?</p>",
                    options_en: ["<p>There is no foreign sector</p>", "<p>The government sector plays an important role.</p>", 
                                "<p>The economy is considered to be closed.</p>", "<p>Firms are the only producing sector in the economy.</p>"],
                    options_hi: ["<p>कोई विदेशी क्षेत्र नहीं है।</p>", "<p>सरकारी क्षेत्र एक महत्वपूर्ण भूमिका निभाता है।</p>",
                                "<p>अर्थव्यवस्था को बंद माना जाता है।</p>", "<p>अर्थव्यवस्था में फर्म एकमात्र उत्पादक क्षेत्र हैं।</p>"],
                    solution_en: "<p>14.(b) Two sectors models contain individuals or households and businesses. In this model, it is assumed that households spend all their incomes as consumer expenditures and purchase the goods and services produced by businesses.</p>",
                    solution_hi: "<p>14.(b) दो सेक्टर मॉडल में व्यक्ति या घर और व्यवसाय शामिल हैं। इस मॉडल में, यह माना जाता है कि परिवार अपनी सभी आय उपभोक्ता व्यय के रूप में खर्च करते हैं और व्यवसायों द्वारा उत्पादित वस्तुओं और सेवाओं को खरीदते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. Which concept is related to the disposal of the equity of public sector units in the market for covering the fiscal deficit of the central government?",
                    question_hi: "<p>15. निम्नलिखित में से कौन-सी अवधारणा केंद्र सरकार के राजकोषीय घाटे को पूरा करने के लिए बाजार में सार्वजनिक क्षेत्र की इकाइयों की इक्विटी के निपटान (विक्रय) से संबंधित है?</p>",
                    options_en: ["<p>Liberalization</p>", "<p>Denationalization</p>", 
                                "<p>Disinvestment</p>", "<p>Globalization</p>"],
                    options_hi: ["<p>उदारीकरण</p>", "<p>विराष्ट्रीयकरण</p>",
                                "<p>विनिवेश</p>", "<p>भूमंडलीकरण</p>"],
                    solution_en: "<p>15.(c) <strong>Disinvestment</strong>. Globalisation is generally understood to mean integration of the economy of the country with the world economy, particularly through the movement of goods, services, and capital across borders. Liberalisation - It was introduced to put an end to restrictions and open various sectors of the economy. Denationalization - It is the process of transferring property or business from government ownership to private ownership.</p>",
                    solution_hi: "<p>15.(c) <strong>विनिवेश</strong>। भूमंडलीकरण का अर्थ सामान्यतः देश की अर्थव्यवस्था का विश्व की अर्थव्यवस्था के साथ विशेष रूप से सीमाओं के पार वस्तुओं, सेवाओं और पूंजी के आवागमन के माध्यम से एकीकरण समझा जाता है। उदारीकरण - इसे प्रतिबंधों को समाप्त करने और अर्थव्यवस्था के विभिन्न क्षेत्रों को खोलने के लिए पेश किया गया था। विराष्ट्रीयकरण - यह संपत्ति या व्यवसाय को सरकारी स्वामित्व से निजी स्वामित्व में स्थानांतरित करने की प्रक्रिया है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Article 51 of Directive Principles of State Policy enumerates which of the following?</p>",
                    question_hi: "<p>16. राज्य के नीति निर्देशक सिद्धांतों के अनुच्छेद 51 में निम्नलिखित में से कौन-सा शामिल है?</p>",
                    options_en: ["<p>Promotion of international peace and security</p>", "<p>Separation of judiciary from executive</p>", 
                                "<p>Uniform civil code for the citizens</p>", "<p>Organisation of agricultural and animal husbandry</p>"],
                    options_hi: ["<p>अंतर्राष्ट्रीय शांति और सुरक्षा को बढ़ावा देना</p>", "<p>कार्यपालिका से न्यायपालिका का पृथक्करण</p>",
                                "<p>नागरिकों के लिए समान नागरिक संहिता</p>", "<p>कृषि और पशुपालन का संगठन</p>"],
                    solution_en: "<p>16.(a) P<strong>romotion of international peace and security.</strong> Directive Principles of State Policy (DPSP, Part IV, Article 36&ndash;51). Article 51(b)- The State shall endeavour to maintain just and honourable relations between nations. Article 51(c) - The State shall endeavour to foster respect for international law and treaty obligations in the dealings of organized peoples with one another. Article 51(d)- The State shall endeavour to encourage settlement of international disputes by arbitration.</p>",
                    solution_hi: "<p>16.(a) <strong>अंतर्राष्ट्रीय शांति और सुरक्षा को बढ़ावा देना।</strong> राज्य के नीति निर्देशक सिद्धांत (DPSP, भाग IV, अनुच्छेद 36-51)। अनुच्छेद 51(b) - राज्य, राष्ट्रों के बीच न्यायपूर्ण और सम्मानजनक संबंध बनाए रखने का प्रयास करेगा। अनुच्छेद 51(c) - राज्य एक दूसरे के साथ संगठित लोगों के व्यवहार में अंतर्राष्ट्रीय कानून और संधि दायित्वों (treaty obligations) के प्रति सम्मान को बढ़ावा देने का प्रयास करेगा। अनुच्छेद 51 (d) - राज्य अंतर्राष्ट्रीय विवादों को मध्यस्थता द्वारा निपटाने को प्रोत्साहित करने का प्रयास करेगा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. Which species plays a keystone role in supporting insectivore populations with a year- round available food resource?",
                    question_hi: "<p>17. वर्ष भर उपलब्ध खाद्य संसाधन के साथ कीटभक्षी आबादी का सहायता करने में कौन-सी प्रजाति महत्वपूर्ण भूमिका निभाती है?</p>",
                    options_en: ["<p>Cycas species</p>", "<p>Ficus species</p>", 
                                "<p>Meliaceae species</p>", "<p>Ziziphus species</p>"],
                    options_hi: ["<p>साइकैस प्रजाति</p>", "<p>फाइकस प्रजाति</p>",
                                "<p>मीलिएसी प्रजाति</p>", "<p>ज़िज़िफस प्रजाति</p>"],
                    solution_en: "<p>17.(b) <strong>Ficus species</strong>. Cycas species: Cycads are evergreen plants, but their seeds mature seasonally and are not as readily available as fig fruits. Meliaceae species: Meliaceae is a family of trees that includes some species with edible fruits, but these fruits are not a primary food source for insectivores in most ecosystems. Ziziphus species: Ziziphus trees also produce fruits, but their fruiting season is typically shorter than that of fig trees.</p>",
                    solution_hi: "<p>17.(b) <strong>फाइकस प्रजाति।</strong> साइकस प्रजातियाँ: साइकैड्स सदाबहार पौधे हैं, लेकिन उनके बीज मौसम के अनुसार पकते हैं और अंजीर के फलों की तरह आसानी से उपलब्ध नहीं होते हैं। मीलिएसी प्रजाति: मेलियासी वृक्षों का एक परिवार है जिसमें खाद्य फलों वाली कुछ प्रजातियाँ शामिल हैं, लेकिन ये फल अधिकांश पारिस्थितिक तंत्रों में कीटभक्षी जीवों के लिए प्राथमिक खाद्य स्रोत नहीं हैं। ज़िज़िफस प्रजाति: ज़िज़िफ़स के पेड़ भी फल उत्पन्न करते हैं, लेकिन इनका फलन काल (fruiting season) सामान्यतः अंजीर के पेड़ों की तुलना में कम होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. Which produce was available as a surplus marketed by the farmers in the market during the Green Revolution?",
                    question_hi: "<p>18. हरित क्रांति के दौरान किसानों द्वारा बाजार में अधिशेष के रूप में कौन-सी उपज उपलब्ध थी?</p>",
                    options_en: ["<p>Oil seeds</p>", "<p>Wheat and jowar</p>", 
                                "<p>Wheat and rice</p>", "<p>Rice and maize</p>"],
                    options_hi: ["<p>तिलहन</p>", "<p>गेहूँ और ज्वार</p>",
                                "<p>गेहूँ और चावल</p>", "<p>चावल और मक्का</p>"],
                    solution_en: "<p>18.(c) <strong>Wheat and rice. </strong>At independence, about 75 per cent of the country&rsquo;s population was dependent on agriculture. In the first phase of the green revolution (approximately mid 1960s upto mid 1970s), the use of high yielding variety (HYV) seeds was restricted to the more affluent states such as Punjab, Andhra Pradesh and Tamil Nadu. Father of the Green Revolution in India - M.S. Swaminathan. \'Father of Green Revolution\' in the world - Norman Borlaug.</p>",
                    solution_hi: "<p>18.(c) <strong>गेहूं और चावल। </strong>स्वतंत्रता के समय देश की लगभग 75 प्रतिशत जनसंख्या कृषि पर निर्भर थी। हरित क्रांति के पहले चरण में (लगभग 1960 के दशक के मध्य से 1970 के दशक के मध्य तक), अधिक उपज देने वाली किस्म (HYV) की बीजों का उपयोग पंजाब, आंध्र प्रदेश और तमिलनाडु जैसे अधिक समृद्ध राज्यों तक ही सीमित था। भारत में हरित क्रांति के जनक - एम.एस. स्वामीनाथन। विश्व में \'हरित क्रांति के जनक\' - नॉर्मन बोरलॉग।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. Sunayana Hazarilal won the Padma Shri for which form of classical dance?",
                    question_hi: "<p>19. सुनयना हजारीलाल ने किस शास्त्रीय नृत्य के लिए पद्म श्री प्राप्त किया?</p>",
                    options_en: ["<p>Kathakali</p>", "<p>Odissi</p>", 
                                "<p>Kathak</p>", "<p>Bharatanatyam</p>"],
                    options_hi: ["<p>कथकली</p>", "<p>ओडिसी</p>",
                                "<p>कथक</p>", "<p>भरतनाट्यम</p>"],
                    solution_en: "<p>19.(c) <strong>Kathak</strong>. Sunayana Hazarilal belongs to Janakiprasad Gharana ( or Benares Gharana). She was awarded Padma Shri (2011). Other Famous classical dancer: Pandit Birju Maharaj (Kathak), Rukmini Devi Arundale (Bharatanatyam), Kelucharan Mohapatra (Odissi), Guru Vempati Chinna Satyam (Kuchipudi dance), Guru Bipin Singh (Manipuri dance).</p>",
                    solution_hi: "<p>19.(c) <strong>कथक</strong>। सुनयना हजारीलाल का संबंध जानकीप्रसाद घराने (या बनारस घराने) से हैं। उन्हें पद्मश्री (2011) से सम्मानित किया गया है। अन्य प्रसिद्ध शास्त्रीय नर्तक: पंडित बिरजू महाराज (कथक), रुक्मिणी देवी अरुंडेल (भरतनाट्यम), केलुचरण महापात्र (ओडिसी), गुरु वेम्पति चिन्ना सत्यम (कुचिपुड़ी नृत्य), गुरु बिपिन सिंह (मणिपुरी नृत्य)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. In which state of India was the first train introduced in the year 1853?",
                    question_hi: "<p>20. वर्ष 1853 में, भारत के किस राज्य में पहली ट्रेन चलाई गई थी?</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>West Bengal</p>", 
                                "<p>Karnataka</p>", "<p>Maharashtra</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>पश्चिम बंगाल</p>",
                                "<p>कर्नाटक</p>", "<p>महाराष्ट्र</p>"],
                    solution_en: "<p>20.(d) <strong>Maharashtra</strong>. India\'s first passenger train ran between Mumbai and Thane on 16 April 1853, covering a distance of 34 km. It has three engines named \'Sahib\', \'Sindh\' and \'Sultan\'. The Kolkata Metro (1984), the first operational rapid transit system in India. Delhi Metro is the longest metro network in the country.</p>",
                    solution_hi: "<p>20.(d) <strong>महाराष्ट्र</strong>। भारत की पहली यात्री ट्रेन 16 अप्रैल 1853 को मुंबई और ठाणे के बीच 34 किमी की दूरी तय की थी। इसमें \'साहिब\', \'सिंध\' और \'सुल्तान\' नाम के तीन इंजन थे। कोलकाता मेट्रो (1984), भारत में पहली ऑपरेशनल रैपिड ट्रांज़िट सिस्टम है। दिल्ली मेट्रो, देश का सबसे लंबा मेट्रो नेटवर्क है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21.  Who issued the silver rupee, which remained a standard coin for centuries after the ruler?",
                    question_hi: "<p>21. चांदी की मुद्रा किसने जारी की, जो शासक के बाद सदियों तक एक मानक सिक्का बना रहा?</p>",
                    options_en: ["<p>Babur</p>", "<p>Sher Shah Suri</p>", 
                                "<p>Humayun</p>", "<p>Akbar</p>"],
                    options_hi: ["<p>बाबर</p>", "<p>शेरशाह सूरी</p>",
                                "<p>हुमायूं</p>", "<p>अकबर</p>"],
                    solution_en: "<p>21.(b) <strong>Sher Shah Suri </strong>issued a coin of silver which was termed the Rupiya. This weighed 178 grains and was the precursor of the modern rupee. It remained largely unchanged till the early 20thCentury. Together with the silver Rupiya were issued gold coins called the &lsquo;Mohur&rsquo; weighing 169 grains and copper coins called &lsquo;Dam&rsquo;.</p>",
                    solution_hi: "<p>21.(b) <strong>शेरशाह सूरी </strong>ने चांदी का एक सिक्का जारी किया जिसे रुपिया कहा जाता था। इसका वजन 178 ग्रेन था और यह आधुनिक रुपये का पूर्ववर्ती था। 20वीं शताब्दी की शुरुआत तक यह व्यापक रूप से अपरिवर्तित रहा। इस समय चांदी के रुपए के साथ सोने के सिक्के भी जारी किए गए जिन्हें \'मोहर\' कहा जाता था, जिनका वजन 169 ग्रेन था और तांबे के सिक्के को \'दाम\' कहा जाता था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. Seema switched on an electric circuit and went away to drink some water. She came back after 2 minutes and noticed that the circuit had 2.5 A of current flowing through it. The net charge that had flown through the circuit is:",
                    question_hi: "<p>22. सीमा, विद्युत परिपथ को चालू करती है और जल पीने के लिए चली जाती है। वह 2 मिनट बाद वापस आती है और देखती है कि परिपथ में 2.5A की धारा प्रवाहित हो रही है। परिपथ से प्रवाहित शुद्ध आवेश ज्ञात कीजिए।</p>",
                    options_en: ["<p>1.25 C</p>", "<p>5 C</p>", 
                                "<p>300 C</p>", "<p>0.0833 C</p>"],
                    options_hi: ["<p>1.25 C</p>", "<p>5 C</p>",
                                "<p>300 C</p>", "<p>0.0833 C</p>"],
                    solution_en: "<p>22.(c) 300 C. Given, Current (I) = 2.5 A, Time (t) = 2 min = 2 &times; 60 = 120 sec.<br>Quantity of electricity charge (Q) = Current (I) &times; Time (t) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= 2.5 &times; 120 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= 300 C.</p>",
                    solution_hi: "<p>22.(c) 300 C. दिया गया है, धारा (I) = 2.5 A, समय (t) = 2 मिनट = 2 &times; 60 = 120 सेकंड।<br>विद्युत आवेश की मात्रा (Q) = धारा (I) &times; समय (t) <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= 2.5 &times; 120 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;= 300 C.</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. In which year was the Volleyball Federation of India established?</p>",
                    question_hi: "<p>23.भारतीय वॉलीबॉल संघ की स्थापना किस वर्ष में की गई थी?</p>",
                    options_en: ["<p>1948</p>", "<p>1949</p>", 
                                "<p>1950</p>", "<p>1951</p>"],
                    options_hi: ["<p>1948</p>", "<p>1949</p>",
                                "<p>1950</p>", "<p>1951</p>"],
                    solution_en: "<p>23.(d) <strong>1951</strong>. Volleyball Federation of India first meeting was held in Ludhiana (Punjab). First President - Mr. F. C. Arora (Punjab) and first Secretary general - Mr. Basu (West Bengal). The first Volleyball Championship was held in 1936 at Lahore (now in Pakistan).</p>",
                    solution_hi: "<p>23.(d) <strong>1951 </strong>। वॉलीबॉल फेडरेशन ऑफ इंडिया की पहली मीटिंग लुधियाना (पंजाब) में हुई थी। प्रथम अध्यक्ष - एफ.सी.अरोड़ा (पंजाब) और प्रथम महासचिव - बासु (पश्चिम बंगाल)। पहली वॉलीबॉल चैंपियनशिप 1936 में लाहौर (अब पाकिस्तान में) में आयोजित की गई थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Identify the artist who is NOT related to the Carnatic music family</p>",
                    question_hi: "<p>24. उस कलाकार की पहचान कीजिए, जो कर्नाटक संगीत परिवार से संबंधित नहीं है।</p>",
                    options_en: ["<p>TS Vinayakram</p>", "<p>Aruna Sairam</p>", 
                                "<p>Begum Akhtar</p>", "<p>MS Subbulakshmi</p>"],
                    options_hi: ["<p>टी.एस विनायक्रम</p>", "<p>अरुणा साईराम</p>",
                                "<p>बेगम अख्तर</p>", "<p>एम.एस सुब्बुलक्ष्मी</p>"],
                    solution_en: "<p>24.(c) Begum Akhtar, known for Hindustani Vocal Music and known as \"Mallika-e-Ghazal\" (Queen of Ghazals). Her Awards: Padma Bhushan (1975), Padma Shri (1968), Sangeet Natak Akademi Award (1972). Famous Carnatic Artists - Anuradha Sriram, M. Balamuralikrishna, Arya Shankar, Lalgudi Jayaraman.</p>",
                    solution_hi: "<p>24.(c) बेगम अख्तर, हिंदुस्तानी गायन संगीत के लिए और \"मल्लिका-ए-ग़ज़ल\" (ग़ज़लों की रानी) के रूप में जानी जाती हैं। उनको प्राप्त पुरस्कार: पद्म भूषण (1975), पद्म श्री (1968), संगीत नाटक अकादमी पुरस्कार (1972)। प्रसिद्ध कर्नाटक के कलाकार - अनुराधा श्रीराम, एम. बालमुरलीकृष्ण, आर्य शंकर, लालगुडी जयारमन।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. ‘Imperfect\' is the autobiography of which Indian cricketer?",
                    question_hi: "<p>25. &lsquo;इम्परफेक्ट\' किस भारतीय क्रिकेटर की आत्मकथा है?</p>",
                    options_en: ["<p>Sanjay Manjrekar</p>", "<p>Kapil Dev</p>", 
                                "<p>Sunil Gavaskar</p>", "<p>Ravi Shastri</p>"],
                    options_hi: ["<p>संजय मांजरेकर</p>", "<p>कपिल देव</p>",
                                "<p>सुनील गावस्कर</p>", "<p>रवि शास्त्री</p>"],
                    solution_en: "<p>25.(a) <strong>Sanjay Manjrekar i</strong>s an Indian cricket commentator and former cricketer. Autobiographies of famous Cricketers: &lsquo;Sunny Days&rsquo; (Sunil Gavaskar), &lsquo;Straight from the Heart&rsquo; (Kapil Dev), &lsquo;The Test of My Life&rsquo; (Yuvraj Singh), &lsquo;Playing It My Way&rsquo; (Sachin Tendulkar), &lsquo;No Spin&rsquo; (Shane Warne), &lsquo;A Century Is Not Enough&rsquo; (Sourav Ganguly), &lsquo;Playing with Fire&rsquo; (Nasser Hussain), &lsquo;Stargazing: The Players in My Life&rsquo; (Ravi Shastri).</p>",
                    solution_hi: "<p>25.(a) <strong>संजय मांजरेकर</strong> ,भारतीय क्रिकेट कमेंटेटर और पूर्व क्रिकेटर हैं। प्रसिद्ध क्रिकेटरों की आत्मकथाएँ: &lsquo;सनी डेज़&rsquo; (सुनील गावस्कर), &lsquo;स्ट्रेट फ्रॉम द हार्ट&rsquo; (कपिल देव), &lsquo;द टेस्ट ऑफ़ माई लाइफ&rsquo; (युवराज सिंह), &lsquo;प्लेइंग इट माई वे&rsquo; (सचिन तेंदुलकर), &lsquo;नो स्पिन&rsquo; (शेन वार्न), &lsquo;ए सेंचुरी इज़ नॉट इनफ&rsquo; (सौरव गांगुली), &lsquo;प्लेइंग विद फायर&rsquo; (नासिर हुसैन,) &lsquo;स्टारगेजिंग: द प्लेयर्स इन माई लाइफ&rsquo; (रवि शास्त्री)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Who among the following was re-elected in 1938 as the president of the Indian National Congress session for 1939?",
                    question_hi: "<p>26. निम्नलिखित में से किसे 1938 में 1939 के लिए भारतीय राष्ट्रीय कांग्रेस अधिवेशन के अध्यक्ष के रूप में फिर से निर्वाचित किया गया था?</p>",
                    options_en: ["<p>Satyendra Prasanna Sinha</p>", "<p>Subhash Chandra Bose</p>", 
                                "<p>Lal Mohan Ghosh</p>", "<p>Pattabhi Sittaramayya</p>"],
                    options_hi: ["<p>सत्येंद्र प्रसन्ना सिन्हा</p>", "<p>सुभाष चंद्र बोस</p>",
                                "<p>लाल मोहन घोष</p>", "<p>पट्टाभि सीतारमैय्या</p>"],
                    solution_en: "<p>26.(b) <strong>Subhash Chandra Bose.</strong> In the 1939 Tripuri Session, he won the elections for the Indian National Congress (INC) president by defeating Pattabhi Sitaramayya. INC session and President: 1885 (Bombay) - W.C. Bonnerjee, 1886 (Calcutta) - Dadabhai Naoroji, 1887 (madras)-Badruddin Tyabji, 1888 (Allahabad) - George Yule, 1896 (Calcutta) - Rahimtulla M. Sayani, 1905 (Benares) - Gopal Krishna Gokhale, 1940 (Ramgarh) - Abul Kalam Azad, 1946 (Meerut) - J.B Kripalani.</p>",
                    solution_hi: "<p>26.(b) <strong>सुभाष चंद्र बोस। </strong>1939 के त्रिपुरी अधिवेशन में, उन्होंने पट्टाभि सीतारमैया को हराकर भारतीय राष्ट्रीय कांग्रेस (INC) के अध्यक्ष का चुनाव जीता। कांग्रेस अधिवेशन और अध्यक्ष: 1885 (बॉम्बे) - डब्ल्यू.सी. बनर्जी, 1886 (कलकत्ता) - दादाभाई नौरोजी, 1887 (मद्रास)-बदरुद्दीन तैयबजी 1888 (इलाहाबाद) - जॉर्ज यूल, 1896 (कलकत्ता) - रहीमतुल्ला एम. सयानी, 1905 (बनारस) - गोपाल कृष्ण गोखले, 1940 (रामगढ़) - अबुल कलाम आज़ाद, 1946 (मेरठ) - जे.बी कृपलानी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. Which of the following were the two major internal land routes through which trade and commerce took place in the post-Mauryan period?",
                    question_hi: "<p>27. निम्नलिखित में से कौन से दो प्रमुख आंतरिक भूमि मार्ग थे जिनके माध्यम से मौर्य काल के बाद व्यापार और वाणिज्य होता था?</p>",
                    options_en: ["<p>Pashchim Pathak and Uttarapatha</p>", "<p>Uttarapatha and Dakshinapatha</p>", 
                                "<p>Dakshinapatha and Purvabhadra</p>", "<p>Dakshinapatha and Pashchim Pathak</p>"],
                    options_hi: ["<p>पश्चिमपथ और उत्तरापथ</p>", "<p>उत्तरापथ और दक्षिणापथ</p>",
                                "<p>दक्षिणापथ और पूर्वापथ</p>", "<p>दक्षिणापथ और पश्चिमपथ</p>"],
                    solution_en: "<p>27.(b) <strong>Uttarapatha and Dakshinapatha.</strong> The Uttarapatha route connected the eastern and northern parts of India with the northwestern areas, extending into present-day Pakistan and beyond. Dakshinapatha route connected peninsular India with the western and northern parts of the country. It facilitated trade between the Deccan Plateau and the Gangetic plains, promoting the exchange of goods like spices, textiles, and precious stones.</p>",
                    solution_hi: "<p>27.(b) <strong>उत्तरापथ और दक्षिणापथ</strong>। उत्तरापथ मार्ग भारत के पूर्वी और उत्तरी हिस्सों को उत्तर-पश्चिमी क्षेत्रों से जोड़ता था, जो वर्तमान पाकिस्तान और उससे आगे तक फैला हुआ था। दक्षिणापथ मार्ग प्रायद्वीपीय भारत को देश के पश्चिमी और उत्तरी भागों से जोड़ता था। इसने दक्कन के पठार और गंगा के मैदानी इलाकों के बीच व्यापार को सुविधाजनक बनाया, जिससे मसालों, वस्त्रों और कीमती पत्थरों जैसी वस्तुओं के आदान-प्रदान को बढ़ावा मिला।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. Tariffs and quotas in the trade issues are used to_________.",
                    question_hi: "<p>28. व्यापार मुद्दों में टैरिफ (प्रशुल्क) और कोटा का इस्तेमाल _____ के लिए किया जाता है।</p>",
                    options_en: ["<p>protect domestic companies</p>", "<p>promote free trade between countries</p>", 
                                "<p>protect only the public sector</p>", "<p>protect only the private sector</p>"],
                    options_hi: ["<p>घरेलू कंपनियों को संरक्षित करने</p>", "<p>देशों के बीच मुक्त व्यापार को बढ़ावा देने</p>",
                                "<p>केवल सार्वजनिक क्षेत्र को संरक्षित करने</p>", "<p>केवल निजी क्षेत्र को संरक्षित करने</p>"],
                    solution_en: "<p>28.(a) <strong>Protect domestic companies.</strong> Tariffs are a tax on imported goods; they make imported goods more expensive and discourage their use. Quotas specify the quantity of goods which can be imported. The effect of tariffs and quotas is that they restrict imports and, therefore, protect the domestic firms from foreign competition.</p>",
                    solution_hi: "<p>28.(a) <strong>घरेलू कंपनियों को संरक्षित करने।</strong> टैरिफ आयातित वस्तुओं पर लगने वाला कर है; वे आयातित वस्तुओं को अधिक महंगा बनाते हैं और उनके उपयोग को हतोत्साहित करते हैं। कोटा उन वस्तुओं की मात्रा निर्दिष्ट करता है जिन्हें आयात किया जा सकता है। टैरिफ और कोटा का प्रभाव यह है कि वे आयात को प्रतिबंधित करते हैं और इस प्रकार, वे घरेलू कंपनियों को विदेशी प्रतिस्पर्धा से बचाते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "29. When is the festival of Ram Navami celebrated in India?",
                    question_hi: "<p>29. भारत में रामनवमी का त्यौहार कब मनाया जाता है?</p>",
                    options_en: ["<p>Ninth day of Ashwin month</p>", "<p>Ninth day of Pausha month</p>", 
                                "<p>Ninth day of Magha month</p>", "<p>Ninth day of Chaitra month</p>"],
                    options_hi: ["<p>अश्विन मास की नवमी तिथि</p>", "<p>पौष मास की नवमी तिथि</p>",
                                "<p>माघ मास की नवमी तिथि</p>", "<p>चैत्र मास की नवमी तिथि</p>"],
                    solution_en: "<p>29.(d) <strong>Ninth day of Chaitra month</strong>. Ram Navami is a Hindu festival which celebrates the birth of Lord Ram. Fairs and Festivals - Makar Sankranti (January), Maha Shivaratri (February), Holi (March), Buddha Purnima (May), Eid-ul-Fitr (June), Raksha Bandhan, Janmashtami and Ganesh Chaturthi (August), Dussehra (October), Muharram (October), Diwali (Kartika), Guru Nanak Jayanti (November).</p>",
                    solution_hi: "<p>29.(d) <strong>चैत्र मास की नवमी तिथि। </strong>राम नवमी एक हिंदू त्योहार है जिसमें भगवान राम के जन्म का उत्सव मनाया जाता है। मेले और त्यौहार - मकर संक्रांति (जनवरी), महा शिवरात्रि (फरवरी), होली (मार्च), बुद्ध पूर्णिमा (मई), ईद-उल-फितर (जून), रक्षा बंधन, जन्माष्टमी और गणेश चतुर्थी (अगस्त), दशहरा (अक्टूबर), मुहर्रम (अक्टूबर), दिवाली (कार्तिक), गुरु नानक जयंती (नवंबर)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Which Viceroy of India took initiative in creating local government bodies in India?",
                    question_hi: "<p>30. भारत के किस वायसराय ने भारत में स्थानीय सरकारी निकाय बनाने की पहल की?</p>",
                    options_en: ["<p>Lord Harding II</p>", "<p>Lord Mayo</p>", 
                                "<p>Lord Rippon</p>", "<p>Lord Minto</p>"],
                    options_hi: ["<p>लॉर्ड हार्डिंग द्वितीय (Lord Harding II)</p>", "<p>लॉर्ड मेयो (Lord Mayo)</p>",
                                "<p>लॉर्ड रिपन (Lord Ripon)</p>", "<p>लॉर्ड मिंटो (Lord Minto)</p>"],
                    solution_en: "<p>30.(c) <strong>Lord Rippon</strong> (Viceroy 1880 to 1884): Events During his Regime - Repeal of the Vernacular Press Act, The first Factory Act, Government resolution on local self government (1882), The Ilbert Bill controversy (1883-84), Hunter Commission on education (1882). Governors-General and Viceroys - Lord Hardinge II (1910-1916), Lord Irwin (1926-1931), Lord Minto I (1807-1813), Lord Mountbatten (1947-1948).</p>",
                    solution_hi: "<p>30.(c) <strong>लॉर्ड रिपन</strong> (वायसराय 1880 से 1884): इनके शासनकाल की घटनाएँ - वर्नाक्युलर प्रेस एक्ट का निरसन, पहला कारखाना अधिनियम, स्थानीय स्वशासन पर सरकारी प्रस्ताव (1882), इल्बर्ट बिल विवाद (1883- 84), शिक्षा पर हंटर आयोग (1882)। गवर्नर-जनरल और वायसराय - लॉर्ड हार्डिंग द्वितीय (1910-1916), लॉर्ड इरविन (1926-1931), लॉर्ड मिंटो प्रथम (1807-1813), लॉर्ड माउंटबेटन (1947-1948)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "31. The originator of Buddhism, Gautam Buddha, was the prince of which of the following republican states during the Mahajanapada period in Ancient India ?",
                    question_hi: "<p>31. बौद्ध धर्म के प्रवर्तक, गौतम बुद्ध, प्राचीन भारत में महाजनपद काल के दौरान निम्नलिखित में से किस गणतांत्रिक राज्य के राजकुमार थे?</p>",
                    options_en: ["<p>Kamboja</p>", "<p>Shakya</p>", 
                                "<p>Kuru</p>", "<p>Vajji</p>"],
                    options_hi: ["<p>कंबोज</p>", "<p>शाक्य</p>",
                                "<p>कुरु</p>", "<p>वज्जि</p>"],
                    solution_en: "<p>31.(b) <strong>Shakya</strong>. Gautam Buddha (563 BC- 483 BC) began life as a prince named Siddhartha Gautama, in northern India. His father was the head of the Shakya clan, and his mother was a Koliyan Princess. He was born in Lumbini, located in Southern Nepal. He was married to Yashodhara and had a son, Rahula. </p>",
                    solution_hi: "<p>31.(b) <strong>शाक्य</strong>। गौतम बुद्ध (563 ईसा पूर्व - 483 ईसा पूर्व) ने उत्तरी भारत में सिद्धार्थ गौतम नामक एक राजकुमार के रूप में जीवन शुरू किया। उनके पिता शाक्य वंश के मुखिया थे, और उनकी माँ एक कोलियान राजकुमारी थीं। उनका जन्म दक्षिणी नेपाल में स्थित लुम्बिनी में हुआ था। उनका विवाह &lsquo;यशोधरा&rsquo; से हुआ था और उनके पुत्र का नाम &lsquo;राहुल&rsquo; था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32.Which of the following temples is built in the hoysala architecture style ?</p>",
                    question_hi: "<p>32.निम्नलिखित में से कौन-सा मंदिर होयसल स्थापत्य शैली में बनाया गया है?</p>",
                    options_en: ["<p>Kesava Temple</p>", "<p>Ladkhan Temple</p>", 
                                "<p>Konark temple</p>", "<p>Khajuraho Temple</p>"],
                    options_hi: ["<p>केशव मंदिर</p>", "<p>लाड खान मंदिर</p>",
                                "<p>कोणार्क मंदिर</p>", "<p>खजुराहो मंदिर</p>"],
                    solution_en: "<p>32.(a) <strong>Kesava Temple (Karnataka).</strong> The temple was consecrated in 1258 CE by Somanatha Dandanayaka, a general of the Hoysala King Narasimha III (1254 - 1291 CE). Other famous temples: Ladkhan Temple at Aihole in Karnataka, dedicated to Shiva. Konark temple (Sun temple) was built in 13th-century CE at Odisha and attributed to king Narasimhadeva I of the Eastern Ganga dynasty.</p>",
                    solution_hi: "<p>32.(a) <strong>केशव मंदिर (कर्नाटक)।</strong> मंदिर को 1258 ई. में होयसला राजा नरसिम्हा तृतीय (1254 - 1291 ई.) के सेनापति सोमनाथ दंडनायक द्वारा प्रतिष्ठित किया गया था। अन्य प्रसिद्ध मंदिर: कर्नाटक के ऐहोल में लाडखान मंदिर (शिव को समर्पित)। कोणार्क मंदिर (सूर्य मंदिर), 13वीं शताब्दी ईस्वी में ओडिशा में बनाया गया था और इसका श्रेय पूर्वी गंगवंश के राजा नरसिम्हदेव प्रथम को दिया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "33. From which of the following states/union territories does the Yamuna River originate?",
                    question_hi: "<p>33. यमुना नदी का उद्गम निम्नलिखित में से किस राज्य/केंद्र शासित प्रदेश से होता है?</p>",
                    options_en: ["<p>Uttar Pradesh</p>", "<p>Uttarakhand</p>", 
                                "<p>Jammu and Kashmir</p>", "<p>Himachal Pradesh</p>"],
                    options_hi: ["<p>उत्तर प्रदेश</p>", "<p>उत्तराखंड</p>",
                                "<p>जम्मू और कश्मीर</p>", "<p>हिमाचल प्रदेश</p>"],
                    solution_en: "<p>33.(b) <strong>Uttarakhand</strong>. The Yamuna is the western most and the longest tributary of the Ganga. its source in the Yamunotri glacier on the southwestern slopes of Bandarpunch range. Ganga originates from Gangotri glacier. Tapi River originates from Multai in Betul district Madhya Pradesh. Narmada River rises from Amarkantak Plateau in Madhya Pradesh. Krishna River originates from Mahabaleshwar in Maharashtra.</p>",
                    solution_hi: "<p>33.(b) <strong>उत्तराखंड</strong>। यमुना, गंगा की सबसे पश्चिमी और सबसे लंबी सहायक नदी है। इसका स्रोत बंदरपूंछ पर्वतमाला के दक्षिण-पश्चिमी ढलान पर यमुनोत्री ग्लेशियर में है। गंगा का उद्गम गंगोत्री ग्लेशियर से होता है। तापी नदी का उद्गम मध्य प्रदेश के बैतूल जिले के मुलताई से होता है। नर्मदा नदी मध्य प्रदेश के अमरकंटक पठार से निकलती है। कृष्णा नदी का उद्गम महाराष्ट्र के महाबलेश्वर से होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. Which of the following methods can be used for liberalization?",
                    question_hi: "<p>34. उदारीकरण के लिए निम्नलिखित में से किस विधि का उपयोग किया जा सकता है?</p>",
                    options_en: ["<p>Increase in taxes</p>", "<p>Abolishing licensing requirement in most industries</p>", 
                                "<p>Making procedures for imports and exports tough</p>", "<p>Restrictions in fixing the prices of goods and services</p>"],
                    options_hi: ["<p>करों में वृद्धि करना</p>", "<p>अधिकांश उद्योगों में लाइसेंसिंग अनिवार्यता को समाप्त करना</p>",
                                "<p>आयात और निर्यात के लिए प्रक्रियाओं को कठिन बनाना</p>", "<p>वस्तुओं और सेवाओं की कीमतें तय करने पर प्रतिबंध लगाना</p>"],
                    solution_en: "<p>34.(b) <strong>Abolishing licensing requirements in most industries. </strong>Liberalization in economic terms generally refers to reducing government control and intervention in an economy. It is a method of how a state raises limitations on some private individual ventures.</p>",
                    solution_hi: "<p>34.(b) <strong>अधिकांश उद्योगों में लाइसेंसिंग अनिवार्यता को समाप्त करना। </strong>आर्थिक दृष्टि से उदारीकरण का तात्पर्य आम तौर पर किसी अर्थव्यवस्था में सरकारी नियंत्रण और हस्तक्षेप को कम करना है। यह एक तरीका है कि कैसे कोई राज्य कुछ निजी व्यक्तिगत उद्यमों पर सीमाएं बढ़ाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. Playing It My Way: My Autobiography, is the book written by whom among the following cricketers?",
                    question_hi: "<p>35. &lsquo;प्लेइंग इट माय वेः माई ऑटोबायोग्राफी\' निम्नलिखित क्रिकेटरों में से किसके द्वारा लिखी गई पुस्तक है?</p>",
                    options_en: ["<p>Kapil Dev</p>", "<p>Rahul Dravid</p>", 
                                "<p>Virat Kohli</p>", "<p>Sachin Tendulkar</p>"],
                    options_hi: ["<p>कपिल देव</p>", "<p>राहुल द्रविड़</p>",
                                "<p>विराट कोहली</p>", "<p>सचिन तेंदुलकर</p>"],
                    solution_en: "<p>35.(d) <strong>Sachin Tendulkar </strong>became the first brand ambassador of UNICEF for South Asia in 2013. His awards: Arjuna Award (1994), Khel Ratna (1997), Padma Shri (1999), Padma Vibhushan (2008), Bharat Ratna (2014). Autobiographies of famous Cricketers - Courtney: Heart of the Lion (Courtney Walsh), Straight from the Heart (Kapil Dev), Line and Strength (Glenn McGrath), To The Point (Herschelle Gibbs), 281 and Beyond (VVS Laxman).</p>",
                    solution_hi: "<p>35.(d) <strong>सचिन तेंदुलकर</strong> 2013 में दक्षिण एशिया के लिए UNICEF के पहले ब्रांड एंबेसडर बने। पुरस्कार: अर्जुन पुरस्कार (1994), खेल रत्न (1997), पद्म श्री (1999), पद्म विभूषण (2008), भारत रत्न(2014)। प्रसिद्ध क्रिकेटरों की आत्मकथाएँ - कर्टनी: हार्ट ऑफ़ द लायन (कोर्टनी वॉल्श), स्ट्रेट फ्रॉम द हार्ट (कपिल देव), लाइन एंड स्ट्रेंथ (ग्लेन मैकग्राथ), टू द पॉइंट (हर्शल गिब्स), 281 एण्ड बियॉन्ड (वी.वी.एस लक्ष्मण)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. Dr. Atmaram Pandurang and Justice Ranade founded the_________ in Maharashtra espousing principles of enlightenment based on Vedas. ",
                    question_hi: "<p>36. डॉ. आत्माराम पांडुरंग और न्यायमूर्ति रानाडे ने वेदों पर आधारित ज्ञान के सिद्धांतों का समर्थन करते हुए महाराष्ट्र में _________ की स्थापना की।</p>",
                    options_en: ["<p>Prarthana Samaj</p>", "<p>Bharat Dharma Mahamandal</p>", 
                                "<p>Brahmo Samaj</p>", "<p>Arya Samaj</p>"],
                    options_hi: ["<p>प्रार्थना समाज</p>", "<p>भारत धर्म महामंडल</p>",
                                "<p>ब्रह्म समाज</p>", "<p>आर्य समाज</p>"],
                    solution_en: "<p>36.(a) <strong>Prarthana Samaj</strong> (1867 at Bombay) focused to remove caste restrictions, abolish child marriage, encourage the education of women, and end the ban on widow remarriage. Socio-Religious Movement and founders: Brahmo Samaj (Raja Ram Mohan Roy), Ramakrishna Mission (Swami Vivekanand), Deva Samaj (Narayan Agnihotri), Satyashodhak Samaj (Jyotiba Phule), Tattvabodhini Sabha (Debendranath Tagore).</p>",
                    solution_hi: "<p>36.(a) <strong>प्रार्थना समाज </strong>(1867 , बंबई में) ने जाति प्रतिबंधों को हटाने, बाल विवाह को समाप्त करने, महिलाओं की शिक्षा को प्रोत्साहित करने और विधवा पुनर्विवाह पर प्रतिबंध को समाप्त करने पर ध्यान केंद्रित किया। सामाजिक-धार्मिक आंदोलन और संस्थापक: ब्रह्म समाज (राजा राम मोहन राय), रामकृष्ण मिशन (स्वामी विवेकानंद), देव समाज (नारायण अग्निहोत्री), सत्यशोधक समाज (ज्योतिबा फुले), तत्त्वबोधिनी सभा (देवेंद्रनाथ टैगोर)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. Which Indian dance form has the Mahari dance style?",
                    question_hi: "<p>37. किस भारतीय नृत्य में महरी नृत्य शैली होती है?</p>",
                    options_en: ["<p>Kathak</p>", "<p>Kathakali</p>", 
                                "<p>Odissi</p>", "<p>Mohiniyattam</p>"],
                    options_hi: ["<p>कथक</p>", "<p>कथकली</p>",
                                "<p>ओडिसी</p>", "<p>मोहिनीअट्टम</p>"],
                    solution_en: "<p>37.(c) <strong>Odissi</strong>. The Mahari dance performed by devadasis in Odisha, particularly at the Jagannatha Temple in Puri. Kathak (Uttar pradesh): This vibrant dance form originated in northern India and, both male and female dancers can be played together. Kathakali (Kerala): The conversation related to storytelling of Hindu mythology takes place between dancers through their facial expressions and body gestures. Mohiniyattam (Kerala): It consists of soft, gentle movements, it is graceful and feminine style of dancing.</p>",
                    solution_hi: "<p>37.(c) <strong>ओडिसी</strong>। महरी नृत्य, ओडिशा में देवदासियों द्वारा विशेष रूप से पुरी के जगन्नाथ मंदिर में किया जाता है। कथक (उत्तर प्रदेश): इस जीवंत नृत्य शैली की उत्पत्ति उत्तरी भारत में हुई थी और इसे पुरुष और महिला दोनों नर्तक एक साथ अभिनय कर सकते हैं। कथकली (केरल): इसमें नर्तकियों के बीच हिंदू पौराणिक कथाओं की कहानी कहने से संबंधित बातचीत उनके मुखाकृतिक और शारीरिक मुद्रा के माध्यम से होती है। मोहिनीअट्टम (केरल): इसमें मधुर और सौम्य गतिविधि शामिल हैं, यह नृत्य की आकर्षक और स्त्रियोचित शैली है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Which Article allows the Council of Ministers to aid and advise the President?",
                    question_hi: "<p>38. कौन-सा अनुच्छेद मंत्रिपरिषद को राष्ट्रपति को सहायता एवं सलाह देने की अनुमति प्रदान करता है?</p>",
                    options_en: ["<p>Article 70</p>", "<p>Article 52</p>", 
                                "<p>Article 61</p>", "<p>Article 74</p>"],
                    options_hi: ["<p>अनुच्छेद 70</p>", "<p>अनुच्छेद 52</p>",
                                "<p>अनुच्छेद 61</p>", "<p>अनुच्छेद 74</p>"],
                    solution_en: "<p>38.(d) <strong>Article 74</strong>. Other important articles of the president: Article 52 - There shall be a President of India. Article 57 - Eligibility for re-election. Article 58 - Qualification for election. Article 61- Procedure for impeachment. Article 70 - It gave the President residuary powers to discharge his/her functions in other contingencies. Article 71 - Matters relating to, or connected with, the election of a President or Vice-President.</p>",
                    solution_hi: "<p>38.(d) <strong>अनुच्छेद 74 । </strong>राष्ट्रपति से संबंधित अन्य महत्वपूर्ण अनुच्छेद: अनुच्छेद 52 - भारत का एक राष्ट्रपति होगा। अनुच्छेद 57 - पुनः निर्वाचन के लिए पात्रता। अनुच्छेद 58 - निर्वाचन के लिए योग्यता। अनुच्छेद 61- महाभियोग की प्रक्रिया। अनुच्छेद 70 - इसने राष्ट्रपति को अन्य आकस्मिकताओं में अपने कार्यों के निर्वहन के लिए अवशिष्ट शक्तियां प्रदान की गई है । अनुच्छेद 71 - राष्ट्रपति या उपराष्ट्रपति के चुनाव से संबंधित या उससे जुड़े मामले।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39. The Brahmo Samaj, which promotes the principle of social equality, was founded by _________.",
                    question_hi: "<p>39. ब्रह्म समाज, जो सामाजिक समानता के सिद्धांत को बढ़ावा देता है, की स्थापना________ द्वारा की गई थी।</p>",
                    options_en: ["<p>Raja Ram Mohan Roy</p>", "<p>Debendranath Tagore</p>", 
                                "<p>Swami Vivekananda</p>", "<p>Swami Dayanand</p>"],
                    options_hi: ["<p>राजा राम मोहन राय</p>", "<p>देवेंद्रनाथ टैगोर</p>",
                                "<p>स्वामी विवेकानंद</p>", "<p>स्वामी दयानंद</p>"],
                    solution_en: "<p>39.(a) <strong>Raja Ram Mohan Roy. </strong>The Brahmo Samaj (1830) - It Prohibited all forms of idolatry and sacrifice, believed in the Upanishads, and forbade its members from criticising other religious practices. Socio-Religious Movement and founders: Young Bengal (1826-32) - Henry Louis Vivian Derozio, Veda Samaj (1864) - Keshab Chandra Sen and K. Sridharalu Naidu, Aligarh Movement (1875) - Sayyid Ahmed Khan.</p>",
                    solution_hi: "<p>39.(a) <strong>राजा राम मोहन राय। </strong>ब्रह्म समाज (1830) - इसने उपनिषदों में विश्वास करने वाले सभी प्रकार की मूर्तिपूजा और बलिदान को प्रतिबंधित किया, और इसके सदस्यों को अन्य धार्मिक प्रथाओं की आलोचना करने से रोक लगा दिया। सामाजिक-धार्मिक आंदोलन और संस्थापक: यंग बंगाल (1826-32) - हेनरी लुईस विवियन डेरोजियो। वेद समाज (1864) - केशव चंद्र सेन और के. श्रीधरलु नायडू। अलीगढ़ आंदोलन (1875) - सैय्यद अहमद खान।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40.Which portal was launched by SIDBI to improve accessibility of credit and handholding services to MSMEs?</p>",
                    question_hi: "<p>40. एमएसएमई (MSME) को क्रेडिट और हैंडहोल्डिंग सेवाओं की पहुंच में सुधार के लिए SIDBI द्वारा कौन सा पोर्टल लॉन्च किया गया था?</p>",
                    options_en: ["<p>Samadhan</p>", "<p>Udyami Mitra</p>", 
                                "<p>MSME Champions</p>", "<p>Sampark</p>"],
                    options_hi: ["<p>समाधान</p>", "<p>उद्यमी मित्र</p>",
                                "<p>एमएसएमई चैंपियंस</p>", "<p>संपर्क</p>"],
                    solution_en: "<p>40.(b) <strong>Udyami Mitra.</strong> In this, Scheduled Commercial Banks (SCBs) have been advised to ensure a target of 7.5% of Adjusted Net Bank Credit (ANBC) for Micro Enterprises, that collateral security is not required for loans up to Rs. 10 lakh to MSE sector, a simplified working capital requirement for MSEs. Samadhan Portal was launched pan-India on 17 september 2020 for filing industrial disputes under the Industrial Disputes Act, 1947.</p>",
                    solution_hi: "<p>40.(b) <strong>उद्यमी मित्र।</strong> इसमें अनुसूचित वाणिज्यिक बैंकों (SCB) को सूक्ष्म उद्यमों के लिए समायोजित नेट बैंक क्रेडिट (ANBC) का 7.5% लक्ष्य सुनिश्चित करने की सलाह दी गई है, MSE क्षेत्र के लिए 10 लाख रुपये तक के ऋण के लिए संपार्श्विक सुरक्षा की आवश्यकता नहीं है, बल्कि इसके लिए एक सरल कार्यशील पूंजी की आवश्यकता है। औद्योगिक विवाद अधिनियम, 1947 के तहत औद्योगिक विवादों को दर्ज करने के लिए समाधान पोर्टल, 17 सितंबर 2020 को सम्पूर्ण भारत में लॉन्च किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "41. In which year was the Malegam Committee appointed to study issues and concerns of the Microfinance Sector?",
                    question_hi: "<p>41. सूक्ष्म वित्त क्षेत्र के मुद्दों और चिंताओं का अध्ययन करने के लिए मालेगाम समिति का गठन किस वर्ष किया गया था?</p>",
                    options_en: ["<p>2010</p>", "<p>2018</p>", 
                                "<p>2015</p>", "<p>2005</p>"],
                    options_hi: ["<p>2010 में</p>", "<p>2018 में</p>",
                                "<p>2015 में</p>", "<p>2005 में</p>"],
                    solution_en: "<p>41.(a) <strong>2010</strong>. Malegam Committee: Appointed by Central Board of the Reserve Bank, Chairman - Shri Y.H. Malegam. It submitted its report in 2011.</p>",
                    solution_hi: "<p>41.(a) <strong>2010 </strong>। मालेगाम समिति: रिज़र्व बैंक के केंद्रीय बोर्ड द्वारा नियुक्त, अध्यक्ष - श्री वाई.एच. मालेगाम। इसने 2011 में अपनी रिपोर्ट सौंपी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "42. Identify the southernmost major port of India.",
                    question_hi: "<p>42. भारत के सबसे दक्षिणी प्रमुख बंदरगाह की पहचान कीजिए।</p>",
                    options_en: ["<p>Mangaluru</p>", "<p>Kochi</p>", 
                                "<p>Tuticorin</p>", "<p>Chennai</p>"],
                    options_hi: ["<p>मंगलुरु</p>", "<p>कोच्चि</p>",
                                "<p>तूतीकोरिन</p>", "<p>चेन्नई</p>"],
                    solution_en: "<p>42.(c)<strong>Tuticorin </strong>(V.O. Chidambaranar Port). It was declared to be a major port on 11 July 1974. It is the second-largest port in Tamil Nadu. Major Ports in India: Kandla (Gujarat), Jawaharlal Nehru (Maharashtra), Marmugao (Goa), New Mangalore (Karnataka), Cochin (Kerala), Ennore (Tamil Nadu), Visakhapatnam (Andhra Pradesh), Paradip (Orissa).</p>",
                    solution_hi: "<p>42.(c) <strong>तूतीकोरिन </strong>(वी.ओ. चिदंबरनार बंदरगाह)। इसे 11 जुलाई 1974 को एक प्रमुख बंदरगाह घोषित किया गया था। यह तमिलनाडु का दूसरा सबसे बड़ा बंदरगाह है। भारत में प्रमुख बंदरगाह: कांडला (गुजरात), जवाहरलाल नेहरू (महाराष्ट्र), मोरमुगाओ (गोवा), न्यू मैंगलोर (कर्नाटक), कोचीन (केरल), एन्नोर (तमिलनाडु), विशाखापत्तनम (आंध्र प्रदेश), पारादीप (उड़ीसा)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "43. ____________ has a curving shape, a feature found in Nagara architecture.",
                    question_hi: "<p>43._________ में एक घुमावदार आकृति होती है, जो नागर वास्तुकला में पाई जाने वाली एक विशेषता है।</p>",
                    options_en: ["<p>Shikhara</p>", "<p>Antarala</p>", 
                                "<p>Mandapa</p>", "<p>Kutina</p>"],
                    options_hi: ["<p>शिखर</p>", "<p>अंतराल</p>",
                                "<p>मंडप</p>", "<p>कुटीना</p>"],
                    solution_en: "<p>43.(a) <strong>Shikhara </strong>is found in North Indian temples and Vimana is found in South Indian temples. Antarala is a transition area between the Garbhagriha and the temple&rsquo;s main hall (mandapa). Mandapa - The entrance to the temple which may be a portico or colonnaded hall that incorporates space for a large number of worshippers. Garbhagriha or sanctum - Where the main idol of the deity is housed.</p>",
                    solution_hi: "<p>43.(a) <strong>शिखर </strong>उत्तर भारतीय मंदिरों में पाया जाता है और विमान दक्षिण भारतीय मंदिरों में पाया जाता है। अंतराल- गर्भगृह और मंदिर के मुख्य कक्ष (मंडप) के बीच एक संक्रमण क्षेत्र है। मंडप - मंदिर का प्रवेश द्वार जो एक बरामदा या स्तंभयुक्त कक्ष होता है जिसमें बड़ी संख्या में उपासकों के लिए जगह शामिल होता है। गर्भगृह (sanctum) - यहाँ देवता की मुख्य मूर्ति स्थित होती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. The Hunta dance of Jharkhand is associated with which tribal community?</p>",
                    question_hi: "<p>44. झारखंड का हुंटा नृत्य कौन-से आदिवासी समूह से संबंधित है?</p>",
                    options_en: ["<p>Oraon</p>", "<p>Munda</p>", 
                                "<p>Santhal</p>", "<p>Kol</p>"],
                    options_hi: ["<p>उराँव</p>", "<p>मुण्डा</p>",
                                "<p>संथाल</p>", "<p>कोल</p>"],
                    solution_en: "<p>44.(c) <strong>Santhal </strong>tribal community live in the hilly plateaus of the Chota Nagpur region of the Santhal Parganas. Hunta Dance performed only by men. Dance and tribal community in Jharkhand: Paika Dance (Munda tribe), Barao Dance (Oraon tribe), Jenana Jhumur (Santhal and Nagpuri tribe), Mardani Jhumur (by men of the Nagpuri community).</p>",
                    solution_hi: "<p>44.(c) <strong>संथाल </strong>आदिवासी समुदाय, संथाल परगना के छोटा नागपुर क्षेत्र के पहाड़ी पठारों में रहते हैं। हुंटा नृत्य , केवल पुरुषों द्वारा किया जाता है। झारखंड में नृत्य और आदिवासी समुदाय: पाइका नृत्य (मुंडा जनजाति), बाराओ नृत्य (उरांव जनजाति), जेनाना झुमुर (संथाल और नागपुरी जनजाति), मर्दानी झुमुर (नागपुरी समुदाय के पुरुषों द्वारा) किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "45.Who was the author of the famous work \'Shishupala Vadha\'?",
                    question_hi: "<p>45. प्रसिद्ध कृति \'शिशुपाल वध\' के लेखक कौन थे?</p>",
                    options_en: ["<p>Jayadeva</p>", "<p>Bhatti</p>", 
                                "<p>Bharavi</p>", "<p>Magha</p>"],
                    options_hi: ["<p>जयदेव</p>", "<p>भट्टि</p>",
                                "<p>भारवि</p>", "<p>माघ</p>"],
                    solution_en: "<p>45.(d) <strong>Magha </strong>lived under King Varmalata\'s court in Shrimala in present day Rajasthan state. His Mahakavya Shishupalavadham in twenty cantos is based on the Mahabharata episode of the slaying of the defiant king Shishupala by Krishna. Book and Author: Kirtarjuniya (Bharavi), Raghuvamnsa and Kumarasambhava (Kalidasa), Naishadha Charita (Sriharsha), Bhattikavya (Bhatti).</p>",
                    solution_hi: "<p>45.(d) <strong>माघ</strong>, वर्तमान राजस्थान राज्य के श्रीमाला में राजा वर्मलता के दरबार में रहते थे। बीस सर्गों में उनका महाकाव्य शिशुपालवधम् कृष्ण द्वारा उद्दंड राजा शिशुपाल वध के महाभारत प्रकरण पर आधारित है। पुस्तक और लेखक: किरातार्जुनीयम् (भारवि), रघुवंश और कुमारसंभव (कालिदास), नैषधीय चरित (श्रीहर्ष), भट्टिकाव्य (भट्टि)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following countries won the highest number of Kabaddi World Cups, as of 2022?</p>",
                    question_hi: "<p>46. 2022 तक प्राप्त जानकारी के अनुसार, निम्नलिखित में से किस देश ने सबसे अधिक कबड्डी विश्व कप जीते हैं?</p>",
                    options_en: ["<p>Bangladesh</p>", "<p>India</p>", 
                                "<p>Pakistan</p>", "<p>Iran</p>"],
                    options_hi: ["<p>बांग्लादेश</p>", "<p>भारत</p>",
                                "<p>पाकिस्तान</p>", "<p>ईरान</p>"],
                    solution_en: "<p>46.(b) <strong>India</strong>. Kabaddi World Cup, is an indoor international Kabaddi competition conducted by the International Kabaddi Federation (IKF) and Founded in 2004. Kabaddi mat dimensions : Senior men&rsquo;s (13m x 10m), Women (12m x 8m). Kabaddi Terminologies - Ankle hold, Toe Touch, Golden Raid, Lion Jump.</p>",
                    solution_hi: "<p>46.(b) <strong>भारत</strong>। कबड्डी विश्व कप, अंतर्राष्ट्रीय कबड्डी महासंघ (IKF) द्वारा आयोजित और 2004 में स्थापित एक इनडोर अंतर्राष्ट्रीय कबड्डी प्रतियोगिता है। कबड्डी मैट का परिमाप: सीनियर पुरुष के लिए (13 मी x 10 मी), महिलाओं के लिए (12 मी x 8 मी)। कबड्डी के शब्दावली - एंकल होल्ड, टो टच, गोल्डन रेड, लायन जंप ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47.The Annapurna Scheme was launched by the Government of India in the year _______ for providing food security.</p>",
                    question_hi: "<p>47. अन्नपूर्णा योजना भारत सरकार द्वारा खाद्य सुरक्षा प्रदान करने के लिए वर्ष______में शुरू की गई<br>थी।</p>",
                    options_en: ["<p>2002</p>", "<p>2000</p>", 
                                "<p>2001</p>", "<p>2003</p>"],
                    options_hi: ["<p>2002</p>", "<p>2000</p>",
                                "<p>2001</p>", "<p>2003</p>"],
                    solution_en: "<p>47.(b) <strong>2000</strong>. The Annapurna Scheme aims to fulﬁll the needs of Senior Citizens who, despite being eligible, have not received benefits under the National Old Age Pension Scheme (NOAPS, Launched - 1995), by providing food security. Government Schemes of india - Mid day Meal Scheme (1995), Pradhan Mantri Gramodaya Yojana (2000-01), National Rural Health Mission (2005), Aam Aadmi Bima Yojana (2007), Janani Suraksha Yojana (2005).</p>",
                    solution_hi: "<p>47.(b) <strong>2000 </strong>। अन्नपूर्णा योजना का लक्ष्य उन वरिष्ठ नागरिकों की जरूरतों को पूरा करना है, जो पात्र होने के बावजूद, खाद्य सुरक्षा प्रदान करके राष्ट्रीय वृद्धावस्था पेंशन योजना (NOAPS, लॉन्च - 1995) के तहत लाभ नहीं मिला है। भारत की सरकारी योजनाएँ - मध्याह्न भोजन योजना (1995), प्रधान मंत्री ग्रामोदय योजना (2000-01), राष्ट्रीय ग्रामीण स्वास्थ्य मिशन (2005), आम आदमी बीमा योजना (2007), जननी सुरक्षा योजना (2005)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "48. In which of the following years was the first edition of the Indian Super League (ISL) held?",
                    question_hi: "<p>48. इंडियन सुपर लीग (ISL) का पहला संस्करण निम्नलिखित में से किस वर्ष आयोजित किया गया था?</p>",
                    options_en: ["<p>2016</p>", "<p>2013</p>", 
                                "<p>2014</p>", "<p>2015</p>"],
                    options_hi: ["<p>2016</p>", "<p>2013</p>",
                                "<p>2014</p>", "<p>2015</p>"],
                    solution_en: "<p>48.(c) <strong>2014</strong>. Indian Super League was founded on 21 October 2013. It is the men\'s highest level of the Indian football league system. Football trophies - Durand Cup, Santosh Trophy, FIFA World Cup, Euro Cup.</p>",
                    solution_hi: "<p>48.(c) <strong>2014 </strong>। इंडियन सुपर लीग की स्थापना 21 अक्टूबर 2013 को हुई थी। यह भारतीय फुटबॉल लीग प्रणाली का पुरुषों का उच्चतम स्तर है। फुटबॉल संबंधित ट्रॉफियां - डूरंड कप, संतोष ट्रॉफी, फीफा विश्व कप, यूरो कप।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Who described India as a \'Quasi-Federation\'?</p>",
                    question_hi: "<p>49. भारत को \'अर्ध-संघ\' के रूप में किसने वर्णित किया?</p>",
                    options_en: ["<p>KC Wheare</p>", "<p>KM Munshi</p>", 
                                "<p>Harold Joseph Laski</p>", "<p>Ivor Jennings</p>"],
                    options_hi: ["<p>के.सी. व्हेअर (KC Wheare)</p>", "<p>के. एम. मुंशी (KM Munshi)</p>",
                                "<p>हैरोल्ड जोसेफ लॉस्की (Harold Joseph Laski)</p>", "<p>आइवर जेनिंग्स (Ivor Jennings)</p>"],
                    solution_en: "<p>49.(a) <strong>KC Wheare</strong>. He described the Constitution as adopting more unitary features and fewer federal features. Quasi-federalism means an intermediate form of state between a unitary state and a federation. Sir Ivor Jennings called the Constitution of India a &ldquo;lawyer\'s paradise&rdquo;.</p>",
                    solution_hi: "<p>49.(a) <strong>के.सी व्हेयर। </strong>इन्होंने संविधान को अधिक एकात्मक विशेषताओं और कम संघीय विशेषताओं को अपनाने वाला बताया। अर्ध-संघवाद का अर्थ एकात्मक राज्य और एक संघ के बीच राज्य का एक मध्यवर्ती रूप है। सर.आइवर जेनिंग्स ने भारत के संविधान को \"वकीलों का स्वर्ग\" कहा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "50",
                    section: "misc",
                    question_en: "<p>50. Which of the given statements about Vitamin A are correct? <br><strong>Statement A:</strong> Beta-carotene is a precursor to Vitamin A. <br><strong>Statement B: </strong>Leafy green vegetables and yellow fruits are good sources of Vitamin A.</p>",
                    question_hi: "<p>50. विटामिन A के बारे में दिए गए कथनों में से कौन-से सही हैं?<br><strong>कथन A:</strong> बीटा-कैरोटीन विटामिन A का पूर्ववर्ती (precursor) है।<br><strong>कथन B: </strong>पत्तेदार हरी सब्जियाँ और पीले फल विटामिन A के अच्छे स्रोत हैं।</p>",
                    options_en: ["<p>Only Statement B</p>", "<p>Only Statement A</p>", 
                                "<p>Both Statements A and B</p>", "<p>Neither Statement A nor B</p>"],
                    options_hi: ["<p>केवल कथन B</p>", "<p>केवल कथन A</p>",
                                "<p>A और B दोनों कथन</p>", "<p>न तो कथन A और न ही कथन B</p>"],
                    solution_en: "<p>50.(c) <strong>Both Statements A and B</strong>. Vitamins, Scientific Names and Food Source : Vitamin A (Retinol) - In Papaya, Green Leafy Vegetables, Oily Fish. Vitamin B1 (Thiamine) - In Cashews, Milk, Dates, Fresh Fruits, Peas. Vitamin B2 (Riboflavin) - In Cheese, Yoghurt, Spinach, Red Meat, Almond. Vitamin C (Ascorbic Acid) - Citrus Fruits, Goat Milk, Chestnuts, Broccoli. Vitamin D (Calciferol) - Cod liver Oil, Chicken, Cereals, Egg. Vitamin E (Tocopherol) - In Pumpkin, Mango, Guava, Potatoes. Vitamin K (Phytonadione) - Kale, Grapes, Tomatoes.</p>",
                    solution_hi: "<p>50.(c) <strong>दोनों कथन A और B </strong>। विटामिन, वैज्ञानिक नाम और खाद्य स्रोत: विटामिन A (रेटिनॉल) - पपीता, हरी पत्तेदार सब्जियां, तैलीय मछली में। विटामिन B1 (थायमिन) - काजू, दूध, खजूर, ताजे फल, मटर में। विटामिन B2 (राइबोफ्लेविन) - पनीर, दही, पालक, रेड मीट, बादाम में। विटामिन C (एस्कॉर्बिक अम्ल) - खट्टे फल, बकरी का दूध, चेस्टनट, ब्रोकोली। विटामिन D (कैल्सीफेरोल) - कॉड लिवर तेल, चिकन, अनाज (Cereals), अंडा। विटामिन E (टोकोफ़ेरॉल) - कद्दू (Pumpkin), आम, अमरूद, आलू में। विटामिन K (फाइटोनाडियोन) - केल (Kale), अंगूर, टमाटर।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>