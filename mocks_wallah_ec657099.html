<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which session of the congress led to the divide between extremists and moderates in 1907 ?</p>",
                    question_hi: "<p>1. 1907 में कांग्रेस के किस अधिवेशन में गर्म दल और नर्म दल में विभाजन हुआ ?</p>",
                    options_en: ["<p>Surat</p>", "<p>Madras</p>", 
                                "<p>Calcutta</p>", "<p>Bombay</p>"],
                    options_hi: ["<p>सूरत</p>", "<p>मद्रास</p>",
                                "<p>कलकत्ता</p>", "<p>बॉम्बे</p>"],
                    solution_en: "<p>1.(a) Surat session of the congress led to the divide between extremists and moderates in 1907. Extremist leaders were Bal Gangadhar Tilak, Bipin Chandra Pal, Lala Lajpatrai. Moderate leaders were Dadabhai Naoroji, Pherozeshah Mehta, Badruddin Tyabji, W.C Banerjee, Surendranath Banerjee, Romesh Chandra Duttand S.Subramania Iyer.</p>",
                    solution_hi: "<p>1.(a) 1907 में कांग्रेस के सूरत अधिवेशन में उग्रवादियों और नरमपंथियों के बीच विभाजन हुआ। चरमपंथी नेता बाल गंगाधर तिलक, बिपिन चंद्र पाल, लाला लाजपतराय थे। उदारवादी नेताओं में दादाभाई नौरोजी, फिरोजशाह मेहता, बदरुद्दीन तैयबजी, डब्ल्यू.सी. बनर्जी, सुरेंद्रनाथ बनर्जी, रोमेश चंद्र दत्त और एस.सुब्रमण्य अय्यर थे।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. Identify the name of the traditional folk theatre form of Maharashtra.",
                    question_hi: "2. महाराष्ट्र के पारंपरिक लोक रंगमंच के नाम की पहचान कीजिये ?",
                    options_en: [" Tamasha ", " Rasleela ", 
                                " Nautanki ", " Swang "],
                    options_hi: [" तमाशा", " रासलीला",
                                " नौटंकी", " स्वांग "],
                    solution_en: "2.(a) Tamasha is the traditional folk theatre form of Maharashtra. Rasleela is the traditional folk dance drama of Uttar Pradesh. Nautanki dance form is a folk dance from Uttar Pradesh. Swang is a famous folk dance of Haryana.",
                    solution_hi: "2.(a) तमाशा महाराष्ट्र का पारंपरिक लोक नाट्य रूप है। रासलीला उत्तर प्रदेश का पारंपरिक लोक नृत्य नाटक है। नौटंकी नृत्य रूप उत्तर प्रदेश का एक लोक नृत्य है। स्वांग हरियाणा का प्रसिद्ध लोक नृत्य है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which Indian state has the highest power generation capacity from thermal energy ?</p>",
                    question_hi: "<p>3. भारत के किस राज्य में तापीय ऊर्जा से सर्वाधिक विद्युत उत्पादन क्षमता है ?</p>",
                    options_en: ["<p>Andhra Pradesh</p>", "<p>Uttar Pradesh</p>", 
                                "<p>Maharashtra</p>", "<p>Gujarat</p>"],
                    options_hi: ["<p>आंध्र प्रदेश</p>", "<p>उत्तर प्रदेश</p>",
                                "<p>महाराष्ट्र</p>", "<p>गुजरात</p>"],
                    solution_en: "<p>3.(c) The highest power generation capacity from thermal energy (28,173.08 MW) is in Maharashtra. Mahagenco has the highest overall generation capacity and the highest thermal installed capacity amongst all the state power generation utilities in India. The Vindhyachal Thermal Power Station in the Singrauli district of Madhya Pradesh, with an installed capacity of 4,760MW, is currently the biggest thermal power plant in India.</p>",
                    solution_hi: "<p>3.(c) तापीय ऊर्जा (28,173.08 मेगावाट) से सबसे अधिक बिजली उत्पादन क्षमता महाराष्ट्र में है। महाजेनको की समग्र उत्पादन क्षमता उच्चतम है और भारत में सभी राज्य बिजली उत्पादन उपयोगिताओं के बीच उच्चतम थर्मल स्थापित क्षमता है। मध्य प्रदेश के सिंगरौली जिले में विंध्याचल थर्मल पावर स्टेशन, 4,760MW की स्थापित क्षमता के साथ, वर्तमान में भारत का सबसे बड़ा थर्मल पावर प्लांट है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. As of Nov 2020, who is the President of the World Bank ?</p>",
                    question_hi: "<p>4. नवंबर 2020 तक, विश्व बैंक के अध्यक्ष कौन हैं ?</p>",
                    options_en: ["<p>Kristalina Georgieva</p>", "<p>Shanta Devrajan</p>", 
                                "<p>JimYong Kim</p>", "<p>David R. Malpass</p>"],
                    options_hi: ["<p>क्रिस्टालिना जॉर्जीवा</p>", "<p>शांता देवराजन</p>",
                                "<p>जिमयोंग किम</p>", "<p>डेविड आर. मलपास</p>"],
                    solution_en: "<p>4.(d) David Malpass is the current (2021) president of the World Bank. Its headquarter is in Washington, D.C, United States. The World Bank was created in 1944 out of the Bretton Woods Agreement.</p>",
                    solution_hi: "<p>4.(d) डेविड मलपास विश्व बैंक के वर्तमान (2021) अध्यक्ष हैं। इसका मुख्यालय वाशिंगटन, डीसी, संयुक्त राज्य अमेरिका में है। विश्व बैंक की स्थापना 1944 में ब्रेटन वुड्स समझौते के तहत हुई थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following medicinal plants can be used to treat blood pressure ?</p>",
                    question_hi: "<p>5. निम्न में से किस औषधीय पौधे का उपयोग रक्तचाप के उपचार के लिए किया जा सकता है ?</p>",
                    options_en: ["<p>Jamun</p>", "<p>Sarpagandha</p>", 
                                "<p>Tulsi</p>", "<p>Babool</p>"],
                    options_hi: ["<p>जामुन</p>", "<p>सर्पगंधा</p>",
                                "<p>तुलसी</p>", "<p>बबूल</p>"],
                    solution_en: "<p>5.(b) Sarpagandha medicinal plants can be used to treat blood pressure, asthma, insomnia. Sarpagandha is taken from the root of a plant named Rauvolfia serpentine or Indian snakeroot and is a vital drug in ayurveda.</p>",
                    solution_hi: "<p>5.(b) सर्पगंधा औषधीय पौधों का उपयोग रक्तचाप, अस्थमा, अनिद्रा के इलाज के लिए किया जा सकता है। सर्पगंधा रौवोल्फिया सर्पेन्टिना या भारतीय स्नैकरूट नामक पौधे की जड़ से लिया जाता है और आयुर्वेद में एक महत्वपूर्ण दवा है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Who said the following, when laying the foundation stone ceremony of Banaras Hindu University. \"There is no salvation for India unless you strip yourself of this jewelry and hold it in trust for your country men in India.\"</p>",
                    question_hi: "<p>6. बनारस हिंदू विश्वविद्यालय का शिलान्यास करते समय निम्नलिखित में से किसने कहा था कि, \"भारत के लिए कोई मोक्ष नहीं है जब तक कि आप अपने आप को इस गहने से नहीं उतारते और भारत में अपने देश के लोगों में भरोसे में नहीं रखते।\"</p>",
                    options_en: ["<p>Mohammad Ali Jinnah</p>", "<p>Mahatma Gandhi</p>", 
                                "<p>Gopal Krishna Gokhale</p>", "<p>Annie Besant</p>"],
                    options_hi: ["<p>मुहम्मद अली जिन्ना</p>", "<p>महात्मा गांधी</p>",
                                "<p>गोपाल कृष्ण गोखले</p>", "<p>एनी बेसेंट</p>"],
                    solution_en: "<p>6.(b) This statement was said by Mahatma Gandhi during the foundation stone ceremony of Banaras Hindu University. BHU was established in 1916.</p>",
                    solution_hi: "<p>6.(b) यह बयान महात्मा गांधी ने बनारस हिंदू विश्वविद्यालय के शिलान्यास समारोह के दौरान कहा था। BHU की स्थापना 1916 में हुई थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Jon Beel mela is the only fair in India, where the barter system is still used. In which state does it take place ?</p>",
                    question_hi: "<p>7. जॉन बील मेला भारत का एकमात्र मेला है जहाँ अभी भी वस्तु विनिमय प्रणाली का उपयोग किया जाता है। यह किस राज्य में होता है ?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Tripura</p>", 
                                "<p>Assam</p>", "<p>Manipur</p>"],
                    options_hi: ["<p>नगालैंड</p>", "<p>त्रिपुरा</p>",
                                "<p>असम</p>", "<p>मणिपुर</p>"],
                    solution_en: "<p>7.(c) Assam\'s Jon Beel mela (near Jagiroad in Morigaon district of Assam) is the only fair in India, where the barter system is still used. A barter system is known as an old method of exchange.</p>",
                    solution_hi: "<p>7.(c) असम का जॉन बील मेला (असम के मोरीगाँव जिले में जगीरोड के पास) भारत का एकमात्र मेला है जहाँ अभी भी वस्तु विनिमय प्रणाली का उपयोग किया जाता है। वस्तु विनिमय प्रणाली को विनिमय की पुरानी पद्धति के रूप में जाना जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The first passenger train in India was operated between _______</p>",
                    question_hi: "<p>8. भारत में पहली यात्री रेलगाड़ी का संचालन ______के बीच किया गया था।</p>",
                    options_en: ["<p>Roorkee and Piran Kaliyar</p>", "<p>Howrah and Hoogly</p>", 
                                "<p>Royapuram and Wallajah Road</p>", "<p>Bombay and Thane</p>"],
                    options_hi: ["<p>रुड़की और पिरान कलियार</p>", "<p>हावड़ा और हुगली</p>",
                                "<p>रोयापुरम और वलजाह रोड</p>", "<p>बॉम्बे और ठाणे</p>"],
                    solution_en: "<p>8.(d) The first passenger train in India was operated between (Bori Bunder) Bombay and Thane, a distance of 34 km in 1853.</p>",
                    solution_hi: "<p>8.(d) भारत में पहली यात्री ट्रेन (बोरी बंदर) बॉम्बे और ठाणे के बीच 1853 में 34 किमी की दूरी के बीच संचालित की गई थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. As of Nov 2020, who is the Chief Justice of India ?</p>",
                    question_hi: "<p>9. नवंबर 2020 तक, भारत के मुख्य न्यायाधीश कौन हैं ?</p>",
                    options_en: ["<p>S Arvind Bobde</p>", "<p>J Chelameswar</p>", 
                                "<p>Kurian Joseph</p>", "<p>Deepak Mishra</p>"],
                    options_hi: ["<p>एस अरविंद बोबडे</p>", "<p>जे चेलमेश्वर</p>",
                                "<p>कुरियन जोसेफ</p>", "<p>दीपक मिश्रा</p>"],
                    solution_en: "<p>9.(a) The 47th Chief Justice of India was S Arvind Bobde. The 48th Chief Justice of India is N. V. Ramana.</p>",
                    solution_hi: "<p>9.(a) भारत के 47वें मुख्य न्यायाधीश एस अरविंद बोबड़े थे। भारत के 48वें मुख्य न्यायाधीश एन वी रमना हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. INTERPOL has its headquarters in ______",
                    question_hi: "10. इंटरपोल का मुख्यालय_____में है।",
                    options_en: [" Spain", " France ", 
                                " Switzerland ", " Germany "],
                    options_hi: [" स्पेन ", " फ्रांस ",
                                " स्विट्ज़रलैंड", " जर्मनी"],
                    solution_en: "10.(b) INTERPOL has its headquarters in Lyon, France. The International Criminal Police Organization commonly known as Interpol is an international organization that facilitates worldwide police cooperation and crime control.",
                    solution_hi: "10.(b) इंटरपोल का मुख्यालय फ्रांस के ल्यों में है। अंतर्राष्ट्रीय आपराधिक पुलिस संगठन जिसे आमतौर पर इंटरपोल के नाम से जाना जाता है, एक अंतरराष्ट्रीय संगठन है जो दुनिया भर में पुलिस सहयोग और अपराध नियंत्रण की सुविधा प्रदान करता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. In an effort to provide a safe and secure e-payment option, RBI has launched_______</p>",
                    question_hi: "<p>11. एक संरक्षित और सुरक्षित ई-भुगतान विकल्प प्रदान करने के प्रयास में, RBI ने _____लॉन्च किया है।</p>",
                    options_en: ["<p>Vision 2022</p>", "<p>Vision 2021</p>", 
                                "<p>Vision 2020</p>", "<p>Vision 2019</p>"],
                    options_hi: ["<p>विजन 2022</p>", "<p>विजन 2021</p>",
                                "<p>विजन 2020</p>", "<p>विजन 2019</p>"],
                    solution_en: "<p>11.(b) In an effort to provide a safe and secure e-payment option. RBI has launched Vision 2021 which keeps customer experience as the central theme.</p>",
                    solution_hi: "<p>11.(b) एक सुरक्षित और सुरक्षित ई-भुगतान विकल्प प्रदान करने के प्रयास में RBI ने विज़न 2021 लॉन्च किया है जो ग्राहक अनुभव को केंद्रीय विषय के रूप में रखता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. The Virupaksha temple at Hampi is dedicated to ______</p>",
                    question_hi: "<p>12. हम्पी में विरुपाक्ष मंदिर ______को समर्पित है।</p>",
                    options_en: ["<p>Lord Ganesha</p>", "<p>Lord Shiva</p>", 
                                "<p>Lord Brahma</p>", "<p>Lord Vishnu</p>"],
                    options_hi: ["<p>भगवान गणेश</p>", "<p>भगवान शिव</p>",
                                "<p>भगवान बुद्ध</p>", "<p>भगवान विष्णु</p>"],
                    solution_en: "<p>12.(b) The Virupaksha temple at Hampi (Karnataka) is dedicated to Lord Shiva. Queen Lokmahadevi built the Virupaksha temple in 740 AD.</p>",
                    solution_hi: "<p>12.(b) हम्पी (कर्नाटक) में विरुपाक्ष मंदिर भगवान शिव को समर्पित है। रानी लोकमहादेवी ने 740 ई. में विरुपाक्ष मंदिर का निर्माण करवाया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following is a nuclear research reactor operated by Bhabha Atomic Research Centre ?</p>",
                    question_hi: "13. भाभा परमाणु अनुसंधान केंद्र द्वारा संचालित निम्नलिखित में से कौन सा परमाणु अनुसंधान रिएक्टर है ?",
                    options_en: [" Dhruva ", " Shiva ", 
                                " Vishnu ", " Narayana "],
                    options_hi: [" ध्रुव", " शिव",
                                " विष्णु", " नारायण"],
                    solution_en: "13.(a) The Dhruva reactor is India\'s largest nuclear research reactor. It was the first nuclear reactor in Asia proper. Located in the Mumbai suburb of Trombay at the Bhabha Atomic Research Centre (BARC), it is India\'s primary generator of weapons-grade plutonium-bearing spent fuel for its nuclear weapons program.",
                    solution_hi: "13.(a) ध्रुव रिएक्टर भारत का सबसे बड़ा परमाणु अनुसंधान रिएक्टर है। यह एशिया का पहला परमाणु रिएक्टर था। भाभा परमाणु अनुसंधान केंद्र (बीएआरसी) में मुंबई के उपनगर ट्रॉम्बे में स्थित, यह अपने परमाणु हथियार कार्यक्रम के लिए हथियार-ग्रेड प्लूटोनियम-असर खर्च किए गए ईंधन का भारत का प्राथमिक जनरेटर है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Name the mission ISRO has conceived to study the sun.</p>",
                    question_hi: "<p>14. उस मिशन का नाम बताइए जिसे ISRO ने सूर्य का अध्ययन करने के लिए नियोजित किया है।</p>",
                    options_en: ["<p>Exoworld</p>", "<p>Exposat</p>", 
                                "<p>Aditya L1</p>", "<p>Suraj</p>"],
                    options_hi: ["<p>एक्सोवर्ल्ड</p>", "<p>एक्सपोसैट</p>",
                                "<p>आदित्य L1</p>", "<p>सूरज</p>"],
                    solution_en: "<p>14.(c) Aditya L1 is the mission, ISRO has conceived to study the sun. The launch date is planned in 2022. PSLV-XL C56 will be the rocket of the mission and the launch site will be from Satish Dhawan Space Centre.</p>",
                    solution_hi: "<p>14.(c) ISRO ने सूर्य का अध्ययन करने की कल्पना आदित्य एल1 मिशन से की है। प्रक्षेपण की तारीख 2022 में निर्धारित है। PSLV-XL C56 मिशन का रॉकेट होगा और प्रक्षेपण स्थल सतीश धवन अंतरिक्ष केंद्र से होगा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Which country won the first ICC Men&rsquo;s T20 Cricket world cup title ?</p>",
                    question_hi: "<p>15. पहला ICC पुरुष T20 क्रिकेट विश्व कप खिताब किस देश ने जीता ?</p>",
                    options_en: ["<p>India</p>", "<p>England</p>", 
                                "<p>West Indies</p>", "<p>Pakistan</p>"],
                    options_hi: ["<p>भारत</p>", "<p>इंगलैंड</p>",
                                "<p>वेस्ट इंडीज</p>", "<p>पाकिस्तान</p>"],
                    solution_en: "<p>15.(a) India won the first ICC Men&rsquo;s T20 Cricket world cup title in South Africa at the Wanderers Stadium in Johannesburg. MS Dhoni was the captain of India. Yuvraj Singh hit six sixes in one over against England in this competition.</p>",
                    solution_hi: "<p>15.(a) भारत ने दक्षिण अफ्रीका में जोहान्सबर्ग के वांडरर्स स्टेडियम में पहला ICC पुरुष T20 क्रिकेट विश्व कप खिताब जीता। एमएस धोनी भारत के कप्तान थे। युवराज सिंह ने एक ओवर में छह छक्के इसी वर्ल्ड कप में इंग्लैंड के विरुद्ध लगाए थे।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. Which one of the following contains CFC ?",
                    question_hi: "16. निम्नलिखित में से किसमें CFC शामिल है ?",
                    options_en: [" Aerated drinks ", " Wall Paints ", 
                                " Varnish ", " Refrigerants "],
                    options_hi: [" वातित पेय", " दीवार पेंट",
                                " रोग़न", " शीतलक "],
                    solution_en: "16.(d) Refrigerants contain Chlorofluorocarbons (CFCs). They are used in the manufacture of aerosol sprays, blowing agents for foams and packing materials, as solvents, and as refrigerants.",
                    solution_hi: "16.(d) रेफ्रिजरेंट में क्लोरोफ्लोरोकार्बन (CFCs) होते हैं। इनका उपयोग एरोसोल स्प्रे के निर्माण में, फोम और पैकिंग सामग्री के लिए ब्लोइंग एजेंट, सॉल्वैंट्स के रूप में और रेफ्रिजरेंट के रूप में किया जाता है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Which is the second highest constitutional office in India ?</p>",
                    question_hi: "<p>17. भारत में दूसरा सर्वोच्च संवैधानिक कार्यालय कौन सा है ?</p>",
                    options_en: ["<p>Vice President</p>", "<p>President</p>", 
                                "<p>Governor</p>", "<p>Prime Minister</p>"],
                    options_hi: ["<p>उप राष्ट्रपति</p>", "<p>राष्ट्रपति</p>",
                                "<p>राज्यपाल</p>", "<p>प्रधानमंत्री</p>"],
                    solution_en: "<p>17.(a) Vice President is the second-highest constitutional office in India. The current Vice President is M. Venkaiah Naidu. The vice president of India is also ex officio chairperson of the Rajya Sabha.</p>",
                    solution_hi: "<p>17.(a) उपराष्ट्रपति का कार्यालय भारत का दूसरा सर्वोच्च संवैधानिक कार्यालय है। वर्तमान उपराष्ट्रपति एम. वेंकैया नायडू हैं। भारत के उपराष्ट्रपति राज्यसभा के पदेन अध्यक्ष भी हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. In India, river dolphins are found in ______ river.</p>",
                    question_hi: "<p>18. भारत में, नदी डॉल्फ़िन ______नदी में पाई जाती है।</p>",
                    options_en: ["<p>Ghaghara</p>", "<p>Godavari</p>", 
                                "<p>Luni</p>", "<p>Krishna</p>"],
                    options_hi: ["<p>घाघरा</p>", "<p>गोदावरी</p>",
                                "<p>लूनी</p>", "<p>कृष्णा</p>"],
                    solution_en: "<p>18.(a) In India, river dolphins (Platanista gangetica) are found in the Ghaghara river. The national aquatic animal is &ldquo;The Ganges river Dolphins&rdquo;.</p>",
                    solution_hi: "<p>18.(a) भारत में, घाघरा नदी में डॉल्फ़िन (प्लैटनिस्टा गैंगेटिका) पाई जाती है। राष्ट्रीय जलीय जंतु \"गंगा नदी डॉल्फ़िन\" है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. ‘Natyashastra’ the famous treatise on dramatic art was written by_______",
                    question_hi: "19. नाटकीय कला पर प्रसिद्ध ग्रंथ \'नाट्यशास्त्र\' ______ द्वारा लिखा गया था।",
                    options_en: [" Harsha Vardhan ", " Vishnu Sharma ", 
                                " Bharata Muni", " Kalidasa"],
                    options_hi: [" हर्षवर्धन", " विष्णु शर्मा",
                                " भरत मुनि", " कालिदास"],
                    solution_en: "19.(c) ‘Natyashastra’ the famous treatise on dramatic art was written by Bharat Muni. The famous work of Vishnu Sharma is The Panchatantra. Books written by Harshvardhan are Ratnavali, Nagananda. Kalidas’s famous books are Kumarasambhava, Meghduta, Raghuvamsa.",
                    solution_hi: "19.(c) नाटकीय कला पर प्रसिद्ध ग्रंथ \'नाट्यशास्त्र\' भरत मुनि द्वारा लिखा गया था। विष्णु शर्मा की प्रसिद्ध कृति पंचतंत्र है। हर्षवर्धन द्वारा लिखित पुस्तकें रत्नावली, नागानंद हैं। कालिदास की प्रसिद्ध पुस्तकें कुमारसंभव, मेघदूत, रघुवंश हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Name the President of Confederation of Indian Industry (CII) for 2020-21.</p>",
                    question_hi: "<p>20. 2020-21 के लिए भारतीय उद्योग परिसंघ (CII) के अध्यक्ष का नाम क्या है ?</p>",
                    options_en: ["<p>Uday Kotak</p>", "<p>Vikram Kirloskar</p>", 
                                "<p>T.V. Narendran</p>", "<p>Rakesh Bharti Mittal</p>"],
                    options_hi: ["<p>उदय कोटक</p>", "<p>विक्रम किर्लोस्कर</p>",
                                "<p>टीवी नरेंद्रन</p>", "<p>राकेश भारती मित्तल</p>"],
                    solution_en: "<p>20.(a) Uday Kotak was the President of the Confederation of Indian Industry (CII) for 2020-21. T.V Narendran is present CII president for 2021-22.</p>",
                    solution_hi: "<p>20.(a) उदय कोटक 2020-21 के लिए भारतीय उद्योग परिसंघ (CII) के अध्यक्ष थे। टी वी नरेंद्रन 2021-22 के लिए सीआईआई के अध्यक्ष हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Which is the world&rsquo;s largest freshwater lake in terms of volume ?</p>",
                    question_hi: "<p>21. आयतन की दृष्टि से विश्व की सबसे बड़ी मीठे पानी की झील कौन सी है ?</p>",
                    options_en: ["<p>Lake Superior</p>", "<p>Lake Michigan Huron</p>", 
                                "<p>Caspian sea</p>", "<p>Lake Baikal</p>"],
                    options_hi: ["<p>सुपीरियर झील</p>", "<p>मिशिगन हूरोनो झील</p>",
                                "<p>कैस्पियन सागर</p>", "<p>बैकल झील</p>"],
                    solution_en: "<p>21.(d) Lake Baikal is the world&rsquo;s largest freshwater lake in terms of volume. It is situated in southeast Siberia. Lake Superior is the largest and northernmost of the Great Lakes of North America, and among freshwater lakes, it is the world\'s largest by surface area and the third-largest by volume. The Caspian Sea is the world\'s largest inland body of water, variously classed as the world\'s largest lake or a full-fledged sea. Lake Michigan is one of the five Great Lakes of North America. It is the second-largest of the Great lakes by volume and the third-largest by surface area, after Lake Superior and Lake Huron.</p>",
                    solution_hi: "<p>21.(d) बैकाल झील आयतन की दृष्टि से विश्व की सबसे बड़ी मीठे पानी की झील है। यह दक्षिण-पूर्व साइबेरिया में स्थित है। सुपीरियर झील उत्तरी अमेरिका की महान झीलों में सबसे बड़ी और सबसे उत्तरी है, और मीठे पानी की झीलों में, यह सतह क्षेत्र के हिसाब से दुनिया की सबसे बड़ी और आयतन के हिसाब से तीसरी सबसे बड़ी है। कैस्पियन सागर पानी का दुनिया का सबसे बड़ा अंतर्देशीय निकाय है, जिसे दुनिया की सबसे बड़ी झील या पूर्ण समुद्र के रूप में वर्गीकृत किया गया है। मिशिगन झील उत्तरी अमेरिका की पांच महान झीलों में से एक है। यह झील सुपीरियर और लेक ह्यूरन के बाद, मात्रा के हिसाब से ग्रेट लेक्स का दूसरा सबसे बड़ा और सतह क्षेत्र के हिसाब से तीसरा सबसे बड़ा है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. Who invented &lsquo;www&rsquo; ?</p>",
                    question_hi: "<p>22. &lsquo;www&rsquo; की खोज किसने की ?</p>",
                    options_en: ["<p>Robert E. Kahn</p>", "<p>Charles Babbage</p>", 
                                "<p>Vint Cerf</p>", "<p>Tim Berners - Lee</p>"],
                    options_hi: ["<p>रॉबर्ट ई. काहन</p>", "<p>चार्ल्स बैबेज</p>",
                                "<p>विंट सेर्फ़</p>", "<p>टीम बैरनर - ली</p>"],
                    solution_en: "<p>22.(d) World Wide Web(www) was invented by Tim Berners-Lee. Charles Babbage is known as the Father of Computers. Vint Cerf is known as the Father of the Internet. Robert Kahn is the inventor of TCP/IP protocols.</p>",
                    solution_hi: "<p>22.(d) वर्ल्ड वाइड वेब (www) का आविष्कार टिम बर्नर्स-ली ने किया था। चार्ल्स बैबेज को कंप्यूटर का जनक कहा जाता है। विंट सेर्फ़ को इंटरनेट का जनक कहा जाता है। रॉबर्ट कान टीसीपी/आईपी प्रोटोकॉल के आविष्कारक हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "23. Which Bollywood celebrity has collaborated with the famous American talk show host, Devid Letterman for Netflix ?",
                    question_hi: "23. नेटफ्लिक्स के लिए प्रसिद्ध अमेरिकी टॉक शो होस्ट डेविड लेटरमैन के साथ किस बॉलीवुड हस्ती ने सहयोग किया है ?",
                    options_en: [" Amitabh Bachchan ", " Salman Khan ", 
                                " Shahrukh Khan ", " Anil Kapoor "],
                    options_hi: [" अमिताभ बच्चन", " सलमान ख़ान",
                                " शाहरुख खान", " अनिल कपूर"],
                    solution_en: "23.(c) Shahrukh Khan has collaborated with the famous American talk show host, Devid Letterman for Netflix. Netflix is an online streaming platform.",
                    solution_hi: "23.(c) शाहरुख खान ने नेटफ्लिक्स के लिए प्रसिद्ध अमेरिकी टॉक शो होस्ट, डेविड लेटरमैन के साथ सहयोग किया है। नेटफ्लिक्स एक ऑनलाइन स्ट्रीमिंग प्लेटफॉर्म है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Which corporate organization has signed an MoU to plant Rudraksh trees in Uttarakhand as part of their Corporate Social Responsibility under &ldquo;Namami Ganga Programme \'\' in 2019 ?</p>",
                    question_hi: "<p>24. किस कॉर्पोरेट संगठन ने 2019 में &ldquo;नमामि गंगा कार्यक्रम&rdquo; के तहत अपनी कॉर्पोरेट सामाजिक जिम्मेदारी के तहत उत्तराखंड में रुद्राक्ष के पेड़ लगाने के लिए एक समझौता ज्ञापन पर हस्ताक्षर किए हैं ?</p>",
                    options_en: ["<p>HCL Foundation</p>", "<p>Wipro</p>", 
                                "<p>Infosys</p>", "<p>IBM</p>"],
                    options_hi: ["<p>HCL फाउंडेशन</p>", "<p>विप्रो</p>",
                                "<p>इंफोसिस</p>", "<p>IBM</p>"],
                    solution_en: "<p>24.(a) HCL Foundation has signed an MoU to plant Rudraksha trees in Uttarakhand as part of their Corporate Social Responsibility under &ldquo;Namami Ganga Programme \'\' in 2019. &ldquo;Namami Ganga Programme\' was started in June 2014.</p>",
                    solution_hi: "<p>24.(a) HCL फाउंडेशन ने 2019 में \"नमामि गंगा कार्यक्रम\" के तहत अपनी कॉर्पोरेट सामाजिक जिम्मेदारी के तहत उत्तराखंड में रुद्राक्ष के पेड़ लगाने के लिए एक समझौता ज्ञापन पर हस्ताक्षर किए हैं। जून 2014 में \"नमामि गंगा कार्यक्रम\" शुरू हुआ था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. Which state in India has the highest coal reserves ?",
                    question_hi: "25. भारत के किस राज्य में सर्वाधिक कोयला भंडार है ?",
                    options_en: [" Orissa ", " Jharkhand ", 
                                " West Bengal", " Chhattisgarh "],
                    options_hi: [" उड़ीसा", " झारखंड",
                                " पश्चिम बंगाल", " छत्तीसगढ"],
                    solution_en: "25.(b) Jharkhand has the highest coal reserves. Jharia coalfield is a large coal field located in the east of India in Jharia, Jharkhand. Highest coal reserves in the world are in the United States. Coal is a sedimentary rock.",
                    solution_hi: "25.(b) झारखंड में सबसे ज्यादा कोयला भंडार है। झरिया कोयला क्षेत्र भारत के पूर्व में झरिया, झारखंड में स्थित एक बड़ा कोयला क्षेत्र है। कोयला अवसादी चट्टान है। विश्व में सर्वाधिक कोयला भण्डार संयुक्त राज्य अमेरिका में हैं। ",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Who was responsible for introducing Enfield rifles that used the greased cartridges which became the immediate reason for the 1857 revolt ?",
                    question_hi: "26. चर्बी वाले कारतूसों एनफील्ड राइफलों को प्रयोग के लिए लागू करने के लिए कौन जिम्मेदार था, जो 1857 के विद्रोह का तत्काल कारण बन गए थे ?",
                    options_en: [" Lord William Bentinck ", " Henry Hardinge", 
                                " Captain Hearsey ", " Francis Grant "],
                    options_hi: [" लॉर्ड विलियम बेंटिक", " हेनरी हार्डिंग",
                                " केप्टन हर्से", " फ्रांसिस ग्रांट"],
                    solution_en: "26.(b) Henry Hardinge was responsible for introducing Enfield rifles that used the greased cartridges which became the immediate reason for the 1857 revolt. The 1857 revolt started from Meerut city.",
                    solution_hi: "26.(b) हेनरी हार्डिंग ने एनफील्ड राइफलों को पेश करने के लिए जिम्मेदार था, जो ग्रीस किए गए कारतूसों का इस्तेमाल करते थे जो 1857 के विद्रोह का तत्काल कारण बन गए थे। 1857 के विद्रोह की शुरुआत मेरठ शहर से हुई थी।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Glucose molecule breaks down into</p>",
                    question_hi: "<p>27. ग्लूकोज अणु _____में टूट जाता है।</p>",
                    options_en: ["<p>pyruvic acid</p>", "<p>lactic acid</p>", 
                                "<p>mitochondria</p>", "<p>cytoplasm</p>"],
                    options_hi: ["<p>पायरुविक अम्ल</p>", "<p>लैक्टिक अम्ल</p>",
                                "<p>माइटोकॉन्ड्रिया</p>", "<p>कोशिका द्रव्य</p>"],
                    solution_en: "<p>27.(a) Glucose (C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>) molecule breaks down into pyruvic acid. Pyruvic acid, (CH<sub>3</sub>COCOOH), is an organic acid that probably occurs in all living cells.</p>",
                    solution_hi: "<p>27.(a) ग्लूकोज (C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>)अणु पाइरुविक एसिड में टूट जाता है। पाइरुविक एसिड, (CH<sub>3</sub>COCOOH), एक कार्बनिक अम्ल है जो संभवतः सभी जीवित कोशिकाओं में होता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. A small text file created by a website that is stored in the user’s computer temporarily for that session is called",
                    question_hi: "28. किसी वेबसाइट द्वारा बनाई गई एक छोटी टेक्स्ट फ़ाइल जो उस सत्र के लिए अस्थायी रूप से उपयोगकर्ता के कंप्यूटर में संग्रहीत होती है, क्या कहलाती है ?",
                    options_en: [" Cache ", " Bug ", 
                                " Malware ", " Cookies "],
                    options_hi: [" कैशे", " बग ",
                                " मालवेयर ", " कूकीज "],
                    solution_en: "28.(d) A small text file created by a website that is stored in the user’s computer temporarily for that session is called cookies. The term \"malware\" refers to harmful software that disrupts or manipulates an electronic device\'s normal operation. Cache primarily refers to a thing that is hidden or stored somewhere, or to the place where it is hidden.",
                    solution_hi: "28.(d) किसी वेबसाइट द्वारा बनाई गई एक छोटी टेक्स्ट फ़ाइल जो उस सत्र के लिए अस्थायी रूप से उपयोगकर्ता के कंप्यूटर में संग्रहीत होती है, कुकीज कहलाती है। शब्द \"मैलवेयर\" हानिकारक सॉफ़्टवेयर को संदर्भित करता है जो इलेक्ट्रॉनिक डिवाइस के सामान्य संचालन को बाधित या हेरफेर करता है। कैश मुख्य रूप से किसी ऐसी चीज़ को संदर्भित करता है जो कहीं छिपी या संग्रहीत होती है, या उस स्थान पर जहां वह छिपी होती है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following is a similarity between acids and bases ?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन अम्ल और क्षार के बीच क्या समानता है ?</p>",
                    options_en: ["<p>They have pH less than 7.</p>", "<p>They are bitter.</p>", 
                                "<p>Process of mixing acid or base with water is exothermic.</p>", "<p>They are used as preservatives.</p>"],
                    options_hi: ["<p>उनका pH 7 से कम है।</p>", "<p>ये कडवे होते है।</p>",
                                "<p>अम्ल या क्षार को जल में मिलाने की प्रक्रिया ऊष्माक्षेपी होती है।</p>", "<p>इनका उपयोग परिरक्षकों के रूप में किया जाता है।</p>"],
                    solution_en: "<p>29.(c) The process of mixing acid or base with water is exothermic. Exothermic means producing heat (used about a chemical reaction or process).</p>",
                    solution_hi: "<p>29.(c) अम्ल या क्षार को पानी के साथ मिलाने की प्रक्रिया ऊष्माक्षेपी है। ऊष्माक्षेपी का अर्थ गर्मी पैदा करना है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. In the Computer field, FORTRAN stands for",
                    question_hi: "30. कंप्यूटर क्षेत्र में, FORTRAN का अर्थ क्या है ?",
                    options_en: [" Formula Translation", " Format Transformer", 
                                " Forensic Transistor", " Foreign Transmitter"],
                    options_hi: [" Formula Translation", " Format Transformer",
                                " Forensic Transistor", " Foreign Transmitter"],
                    solution_en: "30.(a) FORTRAN stands for Formula Translator. It is a general-purpose, compiled imperative programming language that is especially suited to numeric computation and scientific computing. Some examples of Third generation languages are BASIC, COBOL, Pascal, Fortran, C, C++, Perl, and Ada.",
                    solution_hi: "30.(a) FORTRAN का मतलब फॉर्मूला ट्रांसलेटर है। यह एक सामान्य-उद्देश्य, संकलित अनिवार्य प्रोग्रामिंग भाषा है जो विशेष रूप से संख्यात्मक गणना और वैज्ञानिक कंप्यूटिंग के अनुकूल है। तीसरी पीढ़ी की भाषाओं के कुछ उदाहरण BASIC, COBOL, Pascal, Fortran, C, C++, Perl और Ada है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Where was the 11th WTO Ministerial Meeting organized ?</p>",
                    question_hi: "<p>31. 11वीं विश्व व्यापार संगठन (WTO) की मंत्रिस्तरीय बैठक कहाँ आयोजित की गई थी ?</p>",
                    options_en: ["<p>China</p>", "<p>Switzerland</p>", 
                                "<p>England</p>", "<p>Argentina </p>"],
                    options_hi: ["<p>चीन</p>", "<p>स्विट्ज़रलैंड</p>",
                                "<p>इंगलैंड</p>", "<p>अर्जेंटीना</p>"],
                    solution_en: "<p>31.(d) Argentina hosted the 11th WTO Ministerial Meeting. The 12th WTO Ministerial Meeting was held in (Geneva, Switzerland).</p>",
                    solution_hi: "<p>31.(d) अर्जेंटीना ने 11वीं विश्व व्यापार संगठन मंत्रिस्तरीय बैठक की मेजबानी की। 12वीं विश्व व्यापार संगठन मंत्रिस्तरीय बैठक (जिनेवा, स्विट्जरलैंड) में आयोजित की गई थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. When is National Science Day celebrated in India ?",
                    question_hi: "32. भारत में राष्ट्रीय विज्ञान दिवस कब मनाया जाता है ?",
                    options_en: [" 21st March ", " 20th January  ", 
                                " 28th February  ", " 19th February "],
                    options_hi: [" 21 मार्च", " 20 जनवरी",
                                " 28 फरवरी", " 19 फरवरी"],
                    solution_en: "32.(c) National Science Day is celebrated on 28th February. It is held to commemorate the discovery of the Raman Effect. On February 28, 1928, Sir CV Raman discovered the Raman Effect for which the Physicist was awarded the Nobel Prize in 1930. The theme for this year\'s National Science Day (2021) celebration is “Future of STI: Impacts on Education, Skills and Work”.",
                    solution_hi: "32.(c) राष्ट्रीय विज्ञान दिवस 28 फरवरी को मनाया जाता है। यह रमन प्रभाव की खोज के उपलक्ष्य में मनाया जाता है। 28 फरवरी, 1928 को सर सीवी रमन ने रमन प्रभाव की खोज की जिसके लिए उन्हें 1930 में नोबेल पुरस्कार से सम्मानित किया गया। इस वर्ष के राष्ट्रीय विज्ञान दिवस (2021) उत्सव का विषय “STI का भविष्य: शिक्षा, कौशल और कार्य पर प्रभाव” है। \"",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which missile-destroyer of the Indian Navy has been decommissioned after 36 years in May-2019 ?</p>",
                    question_hi: "<p>33. भारतीय नौसेना के किस मिसाइल-विनाशक को 36 वर्षों के बाद मई-2019 में सेवामुक्त कर दिया गया है ?</p>",
                    options_en: ["<p>INS Rana</p>", "<p>INS Ranjit</p>", 
                                "<p>INS Vikrant</p>", "<p>INS Vikramaditya</p>"],
                    options_hi: ["<p>INS राणा</p>", "<p>INS रंजीत</p>",
                                "<p>INS विक्रांत</p>", "<p>INS विक्रमादित्य</p>"],
                    solution_en: "<p>33.(b) INS Ranjit of the Indian Navy has been decommissioned after 36 years in May-2019. INS Ranjit is the third of the five Rajput-class destroyers built for the Indian Navy. Ranjit was commissioned on 15 September 1983. The Indian Navy is a technology-driven force with its ships, submarines, and aircraft fitted with cutting-edge equipment.</p>",
                    solution_hi: "<p>33.(b) भारतीय नौसेना के INS रंजीत को 36 साल बाद मई-2019 में सेवामुक्त कर दिया गया है। INS रंजीत भारतीय नौसेना के लिए बनाए गए पांच राजपूत श्रेणी के विध्वंसक में से तीसरा है। INS रंजीत को 15 सितंबर 1983 को कमीशन किया गया था। भारतीय नौसेना अपने जहाजों, पनडुब्बियों और अत्याधुनिक उपकरणों से लैस विमानों के साथ एक प्रौद्योगिकी-संचालित बल है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. Which former ISRO chairman has been awarded France’s highest civilian honour in 2019 ?",
                    question_hi: "34. ISRO के किस पूर्व अध्यक्ष को 2019 में फ्रांस के सर्वोच्च नागरिक सम्मान से सम्मानित किया गया है ?",
                    options_en: [" A.S. Kiran Kumar ", " Kailasa Vadivoo Sivan ", 
                                " K. Radhakrishnan ", " G Madhavan Nair "],
                    options_hi: [" ए.एस. किरण कुमार", " कैलाशवादिवू सिवान",
                                " के राधाकृष्णन", " जी माधवन नायर"],
                    solution_en: "34.(a) A.S. Kiran Kumar, former ISRO chairman has been awarded France’s highest civilian honor in 2019. Dr. K Sivan is the present chairman of ISRO.",
                    solution_hi: "34.(a) ISRO के पूर्व अध्यक्ष किरण कुमार को 2019 में फ्रांस के सर्वोच्च नागरिक सम्मान से सम्मानित किया गया है। डॉ के सिवन ISRO के वर्तमान अध्यक्ष हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which article of the Indian constitution grants the right to equal opportunity in public employment ?</p>",
                    question_hi: "<p>35. भारतीय संविधान का कौन सा अनुच्छेद सार्वजनिक रोजगार में समान अवसर का अधिकार प्रदान करता है ?</p>",
                    options_en: ["<p>Article 16</p>", "<p>Article 14</p>", 
                                "<p>Article 15</p>", "<p>Article 13</p>"],
                    options_hi: ["<p>अनुच्छेद 16</p>", "<p>अनुच्छेद 14</p>",
                                "<p>अनुच्छेद 15</p>", "<p>अनुच्छेद 13</p>"],
                    solution_en: "<p>35.(a) Article 16 of the Indian constitution grants the right to equal opportunity in public employment. Article 14, (guarantees to every person the right to equality before law &amp; equal protection of the laws). Article 15, (prohibition of discrimination on grounds of religion, race, caste, sex, or place of birth). Article 13, (everyone has the right to freedom of movement and residence within the borders of each state).</p>",
                    solution_hi: "<p>35.(a) भारतीय संविधान का अनुच्छेद 16 सार्वजनिक रोजगार में समान अवसर का अधिकार प्रदान करता है। अनुच्छेद 14, (यह प्रत्येक व्यक्ति को कानून के समक्ष समानता के अधिकार की गारंटी देता है)। अनुच्छेद 15, (धर्म, मूलवंश, जाति, लिंग या जन्म स्थान के आधार पर भेदभाव का निषेध)। अनुच्छेद 13, (प्रत्येक को प्रत्येक राज्य की सीमाओं के भीतर आवाजाही और निवास की स्वतंत्रता का अधिकार है)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which of the following buildings was designed by F.W. Stevens ?</p>",
                    question_hi: "<p>36. निम्नलिखित में से कौन सी इमारत एफ डब्ल्यू स्टीवंस द्वारा डिजाइन की गई थी ?</p>",
                    options_en: ["<p>The Town Hall, Bombay</p>", "<p>Bombay Secretariat</p>", 
                                "<p>Chatrapati Shivaji Maharaj Terminus (Formerly Victoria Terminus)</p>", "<p>Horniman Circle (Formerly Elphinstone Circle)</p>"],
                    options_hi: ["<p>टाउन हॉल, बॉम्बे</p>", "<p>बॉम्बे सचिवालय</p>",
                                "<p>छत्रपति शिवाजी महाराज टर्मिनस (पूर्व में विक्टोरिया टर्मिनस)</p>", "<p>हॉर्निमैन सर्कल (पूर्व में एलफिंस्टन सर्कल)</p>"],
                    solution_en: "<p>36.(c) Chatrapati Shivaji Maharaj Terminus was designed by F.W. Stevens. The Bombay&nbsp;Secretariat was designed by Captain Henry St. Clair Wilkins.</p>",
                    solution_hi: "<p>36.(c) छत्रपति शिवाजी महाराज टर्मिनस को एफडब्ल्यू स्टीवंस द्वारा डिजाइन किया गया था। बॉम्बे सचिवालय को कैप्टन हेनरी सेंट क्लेयर विल्किंस द्वारा डिजाइन किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. The first Indian flag to be hoisted on foreign soil was unfurled in ______by Bhikaji Cama in 1907.</p>",
                    question_hi: "<p>37. विदेशी धरती पर फहराया जाने वाला पहला भारतीय ध्वज 1907 में भीकाजी कामा द्वारा ______ में फहराया गया था।</p>",
                    options_en: ["<p>Germany</p>", "<p>England</p>", 
                                "<p>Russia</p>", "<p>France</p>"],
                    options_hi: ["<p>जर्मनी</p>", "<p>इंगलैंड</p>",
                                "<p>रूस</p>", "<p>फ्रांस</p>"],
                    solution_en: "<p>37.(a) The first Indian flag to be hoisted on foreign soil was unfurled in Germany by Bhikaji Cama. The Indian flag is designed by Pingali Venkayya. On July 22, 1947, the Constituent Assembly adopted it as the Free India National Flag.</p>",
                    solution_hi: "<p>37.(a) विदेशी धरती पर फहराया जाने वाला पहला भारतीय ध्वज जर्मनी में भीकाजी कामा द्वारा फहराया गया था। भारतीय ध्वज को पिंगली वेंकय्या द्वारा डिजाइन किया गया है। 22 जुलाई 1947 को संविधान सभा ने इसे स्वतंत्र भारत के राष्ट्रीय ध्वज के रूप में अपनाया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Which of the following is an example of non-infectious disease ?",
                    question_hi: "38. निम्न में से कौन असंक्रामक रोग का उदाहरण है ?",
                    options_en: [" High Blood Pressure ", " Typhoid ", 
                                " Influenza ", " Pneumonia"],
                    options_hi: [" उच्च रक्त चाप", " टाइफाइड",
                                " इंफ्लुएंजा", " न्यूमोनिया"],
                    solution_en: "38.(a) High Blood Pressure is an example of a non-infectious disease. Typhoid is a bacterial disease. Influenza is a viral disease. Viruses, bacteria and fungi can all cause pneumonia. ",
                    solution_hi: "38.(a) उच्च रक्तचाप गैर-संक्रामक रोग का एक उदाहरण है। टाइफाइड एक जीवाणु रोग है। इन्फ्लुएंजा एक वायरल बीमारी है। वायरस, बैक्टीरिया और कवक सभी निमोनिया का कारण बन सकते हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. The present MD and CFO of the World Bank Anshula Kant was earlier the MD of______</p>",
                    question_hi: "<p>39. विश्व बैंक के वर्तमान MD और CFO अंशुला कांत पहले______की MD थीं।</p>",
                    options_en: ["<p>Bank of Baroda</p>", "<p>Oriental Bank of Commerce</p>", 
                                "<p>SBI</p>", "<p>IndusInd Bank</p>"],
                    options_hi: ["<p>बैंक ऑफ बड़ौदा</p>", "<p>ओरिएंटल बैंक ऑफ कॉमर्स</p>",
                                "<p>SBI</p>", "<p>इंडसइंड बैंक</p>"],
                    solution_en: "<p>39.(c) The present MD and CFO of the World Bank Anshula Kant was earlier the MD of State Bank of India. The President (2021) of the World Bank is David Malpass.</p>",
                    solution_hi: "<p>39.(c) विश्व बैंक की वर्तमान MD और CFO अंशुला कांत पहले भारतीय स्टेट बैंक की MD थीं। विश्व बैंक के अध्यक्ष (2021) डेविड मलपास हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. Which of the following is one of the founding countries of ASEAN ?</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन ASEAN के संस्थापक देशों में से एक है ?</p>",
                    options_en: ["<p>Australia</p>", "<p>Cambodia</p>", 
                                "<p>Malaysia</p>", "<p>India</p>"],
                    options_hi: ["<p>ऑस्ट्रेलिया</p>", "<p>कंबोडिया</p>",
                                "<p>मलेशिया</p>", "<p>भारत</p>"],
                    solution_en: "<p>40.(c) The Association of Southeast Asian Nations (ASEAN) is a regional grouping that promotes economic, political, and security cooperation among its ten members: Brunei, Cambodia, Indonesia, Laos, Malaysia, Myanmar, Philippines, Singapore, Thailand, and Vietnam. It was founded in 1967, Bangkok, Thailand.</p>",
                    solution_hi: "<p>40.(c) दक्षिण पूर्व एशियाई राष्ट्र संघ (ASEAN) एक क्षेत्रीय समूह है जो अपने दस सदस्यों: ब्रुनेई, कंबोडिया, इंडोनेशिया, लाओस, मलेशिया, म्यांमार, फिलीपींस, सिंगापुर, थाईलैंड और वियतनाम के बीच आर्थिक, राजनीतिक और सुरक्षा सहयोग को बढ़ावा देता है। इसकी स्थापना 1967, बैंकॉक, थाईलैंड में हुई थी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>