<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 26</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">26</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 25,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. From the 3 sets of statements, A, B and C given below, choose the set/s in which the third statement is a logical conclusion of the ﬁrst two statements.<br>A. Some cars are Suzuki. All Suzukis are MPVs. Some cars are MPVs.<br>B. All men are humans. No human is red. No man is red.<br>C. Every man loves his wife. All wives are beautiful. No beautiful has a husband.</p>",
                    question_hi: "<p>1. कथनों के 3 समूह से A B और C नीचे दिए गए हैं। उन समुच्चयों को चुनें जिनमें तीसरा कथन पहले दो कथनों का तार्किक निष्कर्ष है।<br>A. कुछ कारें सुजुकी हैं। सभी सुजुकी MPVs हैं। कुछ कारें MPVs हैं।<br>B. सभी पुरुष इंसान हैं। कोई इंसान लाल नहीं है। कोई पुरुष लाल नहीं है।<br>C. हर पुरुष अपनी पत्नी से प्यार करता है। सभी पत्नियां सुंदर हैं।कोई भी सूंदर पति नहीं है।</p>",
                    options_en: ["<p>Only B</p>", "<p>Only A</p>", 
                                "<p>A and B only</p>", "<p>B and C only</p>"],
                    options_hi: ["<p>केवल B</p>", "<p>केवल A</p>",
                                "<p>केवल A और B</p>", "<p>केवल B और C</p>"],
                    solution_en: "<p>1.(c) From the two statements in part A we can draw the following diagram,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949411796.png\" alt=\"rId6\" width=\"226\" height=\"125\"><br>So the conclusion &ldquo;Some cars are MPVs.&rdquo; definitely follows.<br>Again from the two statements in part B we can draw the following diagram,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949411925.png\" alt=\"rId7\" width=\"344\" height=\"108\"><br>So, the conclusion &ldquo; No man is red. &rdquo; definitely follows.<br>But the two statements of part C do not refer to the conclusion.<br>So, for A and B only the third statement is a logical conclusion of the ﬁrst two statements.</p>",
                    solution_hi: "<p>1.(c) भाग A के दो कथनों से हम निम्नलिखित चित्र बना सकते हैं,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412112.png\" alt=\"rId8\" width=\"243\" height=\"153\"><br>तो निष्कर्ष \"कुछ कारें MPVs हैं।\" निश्चित रूप से अनुसरण करता है।<br>भाग B के दो कथनों से पुनः हम निम्नलिखित चित्र बना सकते हैं,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412239.png\" alt=\"rId9\" width=\"330\" height=\"126\"><br>तो, निष्कर्ष &ldquo;कोई पुरुष लाल नहीं है। निश्चित रूप से अनुसरण करता है।<br>लेकिन भाग C के दो कथन निष्कर्ष का उल्लेख नहीं करते हैं।<br>अत: A और B के लिए केवल तीसरा कथन पहले दो कथनों का तार्किक निष्कर्ष है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "2. How many such consonants are there in the following arrangement, each of which is immediately followed by a vowel but not preceded by a number?<br />TRB50%U7C4#KFS2UE*18I3V@9IX@LAB",
                    question_hi: "2. निम्नलिखित व्यवस्था में ऐसे कितने व्यंजन हैं जिनमें से प्रत्येक के ठीक बाद एक स्वर है लेकिन पहले एक संख्या नहीं है?<br />TRB50%U7C4#KFS2UE*18I3V@9IX@LAB",
                    options_en: [" Two  ", " More than three", 
                                " Three ", " One "],
                    options_hi: [" दो ", " तीन से अधिक ",
                                " तीन ", " एक "],
                    solution_en: "2.(d) Here in case of only [@LA], consonant L is immediately followed by a vowel A but not preceded by a number, i.e. only one case.",
                    solution_hi: "2.(d) यहाँ केवल [@LA] के मामले में, व्यंजन L के ठीक बाद एक स्वर A है लेकिन पहले एक संख्या नहीं है, केवल एक ही मामला है ।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Identify the number that does NOT belong to the given series of numbers.<br>46, 31, 22, 17, 30, -32, -89, - l79</p>",
                    question_hi: "<p>3. उस संख्या की पहचान करें जो दी गई संख्याओं की श्रृंखला से संबंधित नहीं है।<br>46, 31, 22, 17, 30, -32, -89, - l79</p>",
                    options_en: ["<p>-32</p>", "<p>30</p>", 
                                "<p>89</p>", "<p>22</p>"],
                    options_hi: ["<p>-32</p>", "<p>30</p>",
                                "<p>89</p>", "<p>22</p>"],
                    solution_en: "<p>3.(b) 46, 31, 22, 17, <span style=\"text-decoration: underline;\">30</span>, -32, -89, -179<br>In the above series if we look carefully the numbers are arranged in a descending order, except the number 30. So, the odd number in this series will be 30.</p>",
                    solution_hi: "<p>3.(b) 46, 31, 22, 17, <span style=\"text-decoration: underline;\">30</span>, -32, -89, -179<br>उपरोक्त श्रृंखला में यदि हम ध्यान से देखें तो संख्या 30 को छोड़कर, संख्याओं को अवरोही क्रम में व्यवस्थित किया जाता है। अत: इस श्रंखला में विषम संख्या 30 होगी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "4. Select the letter from among the given options that can replace the question mark (?) in the following series.<br />Y, V, Q, J, ?",
                    question_hi: "4. दिए गए विकल्पों में से उस अक्षर का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।<br />Y, V, Q, J, ?",
                    options_en: [" A", " C", 
                                " D", " B"],
                    options_hi: [" A", " C",
                                " D", " B"],
                    solution_en: "4.(a) Here difference between the place values of the first and second letter = (25 - 22) = 3, difference between the place values of the second and third letter = (22-17) = 5,<br />difference between the place values of the third and fourth letter = (17-10) = 7,<br />So, clearly the difference between the fourth and the fifth number will be = 9 = (10 - 1)<br />i.e. the fifth number will be = 1 = A.",
                    solution_hi: "4.(a) यहाँ पहले और दूसरे अक्षर के स्थानीय मान में अंतर = (25-22) = 3,<br />दूसरे और तीसरे अक्षर के स्थानीय मान के बीच का अंतर = (22-17) = 5,<br />तीसरे और चौथे अक्षर के स्थानीय मान के बीच का अंतर = (17-10) = 7,<br />तो, स्पष्ट रूप से चौथी और पाँचवीं संख्या के बीच का अंतर होगा = 9 = (10 - 1)<br />यानी पांचवी संख्या = 1 = A होगी।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Choose the conclusion(s) which logically follow from the given statements.<br><strong>Statements:</strong><br>1. MS Dhoni is a popular cricketer <br>2. All cricketers are ﬁt and healthy <br>3. MS Dhoni earns a handsome amount every year through advertisements of various products.<br><strong>Conclusions:</strong><br>A. All popular cricketers earn a handsome amount through advertisement.<br>B. MS Dhoni is fit and healthy <br>C. MS Dhoni, being famous. advertises only famous products</p>",
                    question_hi: "<p>5. उस निष्कर्ष को चुनिए जो दिए गए कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>1. एम.एस. धोनी एक लोकप्रिय क्रिकेटर हैं<br>2. सभी क्रिकेटर फिट और स्वस्थ हैं।<br>3. एम.एस. धोनी हर साल विभिन्न उत्पादों के विज्ञापनों के जरिए अच्छी खासी कमाई करते हैं।<br><strong>निष्कर्ष:</strong><br>A. सभी लोकप्रिय क्रिकेटर विज्ञापन के माध्यम से अच्छी खासी कमाई करते हैं।<br>B. एम.एस. धोनी फिट और स्वस्थ हैं।<br>C. एम एस धोनी मशहूर होने के कारण केवल मशहूर उत्पादों का ही विज्ञापन करते हैं।</p>",
                    options_en: ["<p>Conclusions A and C follow</p>", "<p>Only conclusion B follows</p>", 
                                "<p>Only conclusion C follows</p>", "<p>Conclusions A and B follow</p>"],
                    options_hi: ["<p>निष्कर्ष A और C अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष B अनुसरण करता है</p>",
                                "<p>केवल निष्कर्ष C अनुसरण करता है</p>", "<p>निष्कर्ष A और B अनुसरण करते हैं</p>"],
                    solution_en: "<p>5.(b) From the given statements, It is definitely correct that MS Dhoni is fit and healthy. <br>But just because MS Dhoni earns a handsome amount, does not mean that all the cricketers earn a handsome amount. Also MS Dhoni advertises various products but it is not mentioned that he advertises only famous products. So, only conclusion B follows.</p>",
                    solution_hi: "<p>5.(b) दिए गए कथनों से, यह निश्चित रूप से सही है कि एमएस धोनी फिट और स्वस्थ हैं।<br>लेकिन सिर्फ इसलिए कि एमएस धोनी अच्छी कमाई करते हैं, इसका मतलब यह नहीं है कि सभी क्रिकेटर्स अच्छी कमाई करते हैं। साथ ही एमएस धोनी विभिन्न उत्पादों का विज्ञापन करते हैं लेकिन यह उल्लेख नहीं है कि वह केवल प्रसिद्ध उत्पादों का विज्ञापन करते हैं। अत: केवल निष्कर्ष B अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "6. Given below is a paragraph. While S1 and S6 are the first and last sentences of this paragraph the parts that are labelled 1, 2, 3 and 4 are jumbled up. Rearrange them to form a meaningful and coherent paragraph.<br />S1: Shruti has been trying to lose weight.<br />1. Regular exercising keeps our body ﬁt and healthy.<br />2. The trainer has suggested her to start with regular exercising in the morning.<br />3. She has not yet started her exercising sessions.<br />4. She says that because of late night ofﬁce hours, it is difﬁcult for her to get up early in the morning.<br />S6: I think it is just a lame excuse for her laziness.",
                    question_hi: "6. नीचे एक पैराग्राफ दिया गया है। जबकि S1 और S6 इस पैराग्राफ के पहले और आखिरी वाक्य हैं, जिन हिस्सों पर 1, 2, 3 और 4 का लेबल लगा है, उन्हें जोड़ दिया गया है। एक सार्थक और सुसंगत अनुच्छेद बनाने के लिए उन्हें पुनर्व्यवस्थित करें।<br />S1: श्रुति वजन कम करने की कोशिश कर रही है।<br />1. नियमित व्यायाम करने से हमारा शरीर चुस्त-दुरुस्त रहता है।<br />2. प्रशिक्षक ने उसे सुबह नियमित व्यायाम से शुरुआत करने का सुझाव दिया है।<br />3. उसने अभी तक अपने व्यायाम सत्र शुरू नहीं किए हैं।<br />4. उनका कहना है कि ऑफिस में देर रात होने के कारण उनके लिए सुबह जल्दी उठना मुश्किल हो जाता है।<br />S6: मुझे लगता है कि यह उसके आलस्य के लिए सिर्फ एक बहाना है।",
                    options_en: [" 1, 2, 4, 3", " 3, 2,1, 4", 
                                " 2, 1, 3, 4", " 4, 2, 3, 1"],
                    options_hi: [" 1, 2, 4, 3", " 3, 2,1, 4",
                                " 2, 1, 3, 4", " 4, 2, 3, 1"],
                    solution_en: "6.(c) The correct sequence for this will be : S1, 2, 1, 3, 4, S6",
                    solution_hi: "6.(c) इस जंबल्ड पैराग्राफ का सही क्रम होगा : S1, 2, 1, 3, 4, S6",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements:</strong><br>A. All papayas are fruits <br>B. Some fruits are vegetables <br>C. All vegetables are vegan <br><strong>Conclusions:</strong><br>i. Some vegetables are papayas <br>ii. Some vegans are fruits <br>iii. Some vegans are papayas <br>iv. Some fruits are papayas</p>",
                    question_hi: "<p>7. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है। भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होता हो। बताइये कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>A. सभी पपीते फल हैं।<br>B. कुछ फल सब्जियां हैं<br>C. सभी सब्जियां शाकाहारी हैं।<br><strong>निष्कर्ष:</strong><br>i. कुछ सब्जियां पपीता हैं<br>ii. कुछ शाकाहारी फल हैं<br>iii. कुछ शाकाहारी पपीते हैं<br>iv. कुछ फल पपीते हैं</p>",
                    options_en: ["<p>Only (i) follows</p>", "<p>Only (iv) follows</p>", 
                                "<p>Only (ii) and (iv) follow</p>", "<p>Only (ii) and (iii) follow</p>"],
                    options_hi: ["<p>केवल (i) अनुसरण करता है</p>", "<p>केवल (iv) अनुसरण करता है</p>",
                                "<p>केवल (ii) और (iv) अनुसरण करते हैं</p>", "<p>केवल (ii) और (iii) अनुसरण करते हैं</p>"],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412365.png\" alt=\"rId10\" width=\"289\" height=\"132\"><br>From the above venn diagram we can say that the conclusions,<br>&lsquo;Some vegans are fruits&rsquo; and &lsquo;Some fruits are papayas&rsquo; follow from the given statements.<br>So, only (ii) and (iv) follow.</p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412469.png\" alt=\"rId11\" width=\"287\" height=\"147\"><br>उपरोक्त वेन आरेख से हम कह सकते हैं कि निष्कर्ष,<br>\'कुछ शाकाहारी फल हैं\' और \'कुछ फल पपीते हैं\' दिए गए कथनों का अनुसरण करते हैं।<br>अत: केवल (ii) और (iv) अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Consider the given statement and decide which of the given assumptions is/are implicit in the statement.<br><strong>Statement:</strong><br>The electricity board has started going from home to home to collect bills.<br><strong>Assumptions:</strong><br>A. Electricity board considers going home to home an effective way to collect bills.<br>B. The electricity board has increased its focus on collecting bills.</p>",
                    question_hi: "<p>8. दिए गए कथनों पर विचार कीजिये और बताइये कि दी गई अवधारणाओं में से कौन सा कथन में अंतर्निहित हैं ।<br><strong>कथन:</strong><br>बिजली बोर्ड ने बिल जमा करने के लिए घर-घर जाना शुरू कर दिया है<br><strong>अवधारणा:</strong><br>A. बिजली बोर्ड घर-घर जाकर बिल जमा करने का कारगर तरीका मानता है।<br>B. बिजली बोर्ड ने बिल जमा करने पर अपना फोकस बढ़ाया है</p>",
                    options_en: ["<p>Both A and B are implicit</p>", "<p>Only assumption A is implicit</p>", 
                                "<p>Neither A nor B is implicit</p>", "<p>Only assumption B is implicit</p>"],
                    options_hi: ["<p>A और B दोनों अंतर्निहित हैं</p>", "<p>केवल धारणा A अंतर्निहित है</p>",
                                "<p>न तो A और न ही B अंतर्निहित है</p>", "<p>केवल धारणा B अंतर्निहित है</p>"],
                    solution_en: "<p>8.(a) From the statement, we can implicitly say that the electricity board considers going home to be an effective way to collect bills and the electricity board has increased its focus on collecting bills. So, both assumptions A and B are implicit.</p>",
                    solution_hi: "<p>8.(a) उपरोक्त कथन से हम परोक्ष रूप से कह सकते हैं कि, बिजली बोर्ड घर जाकर बिल जमा करने का एक प्रभावी तरीका मानता है और बिजली बोर्ड ने बिल जमा करने परअपना ध्यान बढ़ा दिया है। अतः दोनों मान्यताएँ A और B निहित हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "9. Given below is a paragraph. While S1 and S6 are the first and last sentences of this paragraph, the parts that are labelled 1,2, 3 and 4 are jumbled up. Rearrange them to form a meaningful and coherent paragraph.<br />S1: Several metro lines have been planned in the NCR.<br />1. Red line is the first among them. <br />2. They are expected to alleviate the problem of transportation.<br />3. It starts from Shahdara and terminates at Tis-Hazari in the initial phase.<br />4. It caters to over 1 lakh commuters at present<br />S6: Hopefully, the public transportation problem will not be as acute after all the metro lines are completed.",
                    question_hi: "9. नीचे एक पैराग्राफ दिया गया है। जबकि S1 और S6 इस पैराग्राफ के पहले और आखिरी वाक्य हैं, जिन हिस्सों पर 1,2, 3 और 4 का लेबल लगा है, उन्हें जोड़ दिया गया है। एक सार्थक और सुसंगत अनुच्छेद बनाने के लिए उन्हें पुनर्व्यवस्थित करें।<br />S1: एनसीआर में कई मेट्रो लाइनों की योजना बनाई गई है।<br />1. लाल रेखा इनमें प्रथम है।<br />2. उनसे परिवहन की समस्या को कम करने की उम्मीद की जाती है।<br />3. यह शाहदरा से शुरू होकर तीस हजारी पर प्रारंभिक चरण में समाप्त होता है।<br />4. यह वर्तमान समय में 1 लाख से अधिक यात्रियों को सेवा प्रदान करता है",
                    options_en: [" 2, 3, 4, 1", " 1, 3, 4, 2", 
                                " 2, 1, 3, 4", " 1, 2 ,3 ,4"],
                    options_hi: [" 2, 3, 4, 1", " 1, 3, 4, 2",
                                " 2, 1, 3, 4", " 1, 2 ,3 ,4"],
                    solution_en: "9.(c) The correct rearrangement is 2, 1, 3, 4.",
                    solution_hi: "9.(c) सही पुनर्व्यवस्था 2, 1, 3, 4 है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. Select the option that is related to the third term in the same way as the second term is related to the first term?<br />Gravity : Discovery  :: Telephone :?",
                    question_hi: "10. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है?<br />गुरुत्व :खोज  :: टेलीफोन :?",
                    options_en: [" Explore", " Invention ", 
                                " Experiment ", " Construct "],
                    options_hi: [" अन्वेषण", " आविष्कार",
                                " प्रयोग", " निर्माण"],
                    solution_en: "10.(b) As gravity was discovered, the telephone was invented.",
                    solution_hi: "10.(b) जैसे गुरुत्व की खोज हुई वैसे ही टेलीफोन का आविष्कार हुआ।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "11. Which number from among the given options will come in place of (*) in the given number series?<br />1,1, 2, 8, 3, 27,4, (*), 5,125....",
                    question_hi: "11. दिए गए विकल्पों में से कौन सी संख्या दी गई संख्या श्रृंखला में (*) के स्थान पर आएगी?<br />1,1, 2, 8, 3, 27,4, (*), 5,125....",
                    options_en: [" 96", " 64", 
                                " 36", " 32"],
                    options_hi: [" 96", " 64",
                                " 36", " 32"],
                    solution_en: "11.(b) This is an example of alternating series,<br />The 1st series consists of: 1, 2, 3, 4, 5..<br />The 2nd series consists of: 1, 8, 27, ___, 125..<br />So, the number in the blank will be <math display=\"inline\"><mo>⇒</mo><msup><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><mn>64</mn></math>.",
                    solution_hi: "11.(b) यह एक प्रत्यावर्ती श्रृंखला का उदाहरण है,<br />पहली श्रृंखला में शामिल हैं: 1, 2, 3, 4, 5..<br />दूसरी श्रृंखला में शामिल हैं: 1, 8, 27, ___, 125..<br />अत: रिक्त स्थान में संख्या, <math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><mn>64</mn></math>  होगी।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Two statements are given followed by two conclusions. Considering the two statements to be used irrespective of the commonly known facts, decide which of the two conclusions follow logically from these two statements.<br><strong>Statements:</strong><br>1: All hill stations have an echo-point <br>2: P is a hill station.<br><strong>Conclusions:</strong><br>1: P has an echo-point <br>2: Places other than hill stations do not have echo-points</p>",
                    question_hi: "<p>12. दो कथनों के बाद दो निष्कर्ष दिए गए हैं। सामान्य ज्ञात तथ्यों के बावजूद दो कथनों का उपयोग करने पर विचार करते हुए बताइये कि इन दो कथनों में से कौन सा निष्कर्ष तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>1: सभी पहाड़ी स्थानों पर प्रतिध्वनि बिंदु होता है <br>2: P एक पहाड़ी स्थान है <br><strong>निष्कर्ष:</strong><br>1: P एक प्रतिध्वनि बिंदु है।<br>2: पहाड़ी स्थानों के अलावा अन्य स्थानों पर कोई प्रतिध्वनि बिंदु नही है</p>",
                    options_en: ["<p>Both conclusion 1 and conclusion 2 follow</p>", "<p>Only conclusion 1 follows</p>", 
                                "<p>Only conclusion 2 follows</p>", "<p>Neither conclusion 1 nor conclusion 2 follows</p>"],
                    options_hi: ["<p>निष्कर्ष 1 और निष्कर्ष 2 दोनों अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष 1 अनुसरण करता है</p>",
                                "<p>केवल निष्कर्ष 2 अनुसरण करता है</p>", "<p>न तो निष्कर्ष 1 और न ही निष्कर्ष 2 अनुसरण करता है</p>"],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412580.png\" alt=\"rId12\" width=\"209\" height=\"166\"><br>From the given statements, we can see that every Hill Stations have an Eco-Point, and as P is a Hill Station, clearly the Conclusions &ldquo;P has an echo-point&rdquo; is definitely true.<br>But as there is nothing mentioned about non-hill-stations, the conclusion &ldquo;Places other than hill stations do not have echo-points&rdquo; is not correct.<br>i.e. only conclusion 1 follows.</p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412580.png\" alt=\"rId12\" width=\"209\" height=\"166\"><br>दिए गए कथनों से हम देख सकते हैं कि प्रत्येक हिल स्टेशनों में एक इको-पॉइंट होता है, और चूंकि P एक हिल स्टेशन है, स्पष्ट रूप से निष्कर्ष &ldquo;P में एक इको-पॉइंट है\" निश्चित रूप से सत्य है। लेकिन जैसा कि गैर-पहाड़ी स्टेशनों के बारे में कुछ भी उल्लेख नहीं किया गया है, निष्कर्ष \"हिल स्टेशनों के अलावा अन्य स्थानों में इको-पॉइंट नहीं हैं\" सही नहीं है।<br>अर्थात् केवल निष्कर्ष 1 अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. Choose the ﬁgure that is different from the others.",
                    question_hi: "13. वह आकृति चुनें जो दूसरों से अलग हो?",
                    options_en: [" <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412716.png\" alt=\"rId13\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412816.png\" alt=\"rId14\" />", 
                                " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412908.png\" alt=\"rId15\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413010.png\" alt=\"rId16\" />"],
                    options_hi: [" <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412716.png\" alt=\"rId13\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412816.png\" alt=\"rId14\" />",
                                " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412908.png\" alt=\"rId15\" />", " <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413010.png\" alt=\"rId16\" />"],
                    solution_en: "13.(b) Only in the option 2, (+) sign is there. <br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412816.png\" alt=\"rId14\" /><br />Rest of the options have (x) sign instead of (+) sign.",
                    solution_hi: "13.(b) केवल विकल्प 2 में (+) का चिन्ह है।<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949412816.png\" alt=\"rId14\" /><br />शेष विकल्पों में (x) चिह्न के स्थान पर (+) चिह्न है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Given below is a &lsquo;main statement\' followed by four subsidiary statements.<br>From the given options, choose the ordered pair of subsidiary statements, where the ﬁrst statement implies the second and the two statements are logically consistent with the main statement.<br><strong>Main Statement:</strong><br>You can drive over 60 km/h only on the national highways.<br><strong>Subsidiary Statements:</strong><br>A. You are on the national highway. <br>B. You cannot drive over 60 km/h.<br>C. You can drive over 60 km/h.<br>D. You are not on the national highway.</p>",
                    question_hi: "<p>14. नीचे दिया गया एक \' मुख्य कथन\' है जिसके बाद चार सहायक कथन हैं।<br>दिए गए विकल्पों में से, सहायक कथनों के क्रमित युग्म का चयन कीजिये जहाँ पहले कथन दूसरे कथन को अनुसरण करता है और दो कथन तार्किक रूप से मुख्य कथन के अनुरूप हैं।<br><strong>मुख्य कथन:</strong><br>आप केवल राष्ट्रीय राजमार्गों पर 60 km/h से अधिक की गति पर चल सकते हैं।<br><strong>सहायक कथन:</strong><br>A. आप राष्ट्रीय राजमार्ग पर हैं।<br>B. आप 60 km/h से अधिक गति पर नहीं चला सकते<br>C. आप 60 km/h से अधिक गति से चल सकते हैं।<br>D. आप राष्ट्रीय राजमार्ग पर नहीं हैं</p>",
                    options_en: ["<p>DB</p>", "<p>AB</p>", 
                                "<p>DA</p>", "<p>CD</p>"],
                    options_hi: ["<p>DB</p>", "<p>AB</p>",
                                "<p>DA</p>", "<p>CD</p>"],
                    solution_en: "<p>14.(b) From the given options, the sequences CD and DA do not make any sense.<br>From the remaining two options DB and AB , DB is more convincing because from the main statement it is crystal clear that if (D) You are not on the national highway then (B) You cannot drive over 60 km/h. Correct sequence will be DB.</p>",
                    solution_hi: "<p>14.(b) दिए गए विकल्पों में से अनुक्रम CD और DA का कोई मतलब नहीं है। शेष दो विकल्पों में से DB और AB, DB अधिक ठोस है, क्योंकि मुख्य कथन से यह स्पष्ट है कि यदि (D) आप राष्ट्रीय राजमार्ग पर नहीं हैं तो (B) आप 60 km/h से अधिक ड्राइव नहीं कर सकते हैं। अतः सही क्रम DB होगा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Last year,there were three sections in a competitive exam. Out of them 33 students cleared the cut-off in Section A, 34 students cleared the cut-off in Section B and 32 students cleared the cut-off in Section C. 10 students cleared the cut-off in section A and section B. 9 cleared the cut-off in section B and section C and 8 cleared the cut-off in section A and&nbsp;section C. The number of students who cleared only one section was equal and was 21 for each section. How many students cleared all the three sections?</p>",
                    question_hi: "<p>15. पिछले वर्ष, एक प्रतियोगी परीक्षा में तीन खंड थे। उनमें से 33 छात्रों ने खंड A में कट-ऑफ पार किया, 34 छात्रों ने खंड B में कट-ऑफ पार किया और 32 छात्रों ने खंड C में कट-ऑफ पार किया। 10 छात्रों ने खंड A और खंड B में कट-ऑफ पार किया। 9 ने खंड B और खंड C में कट-ऑफ पार किया और 8 ने खंड A और C में कट-ऑफ पार किया केवल एक खंड को पार करने वाले छात्रों की संख्या बराबर थी और प्रत्येक खंड के लिए यह 21 थी। कितने छात्रों ने तीनों खंडों को पार किया?</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>7</p>", "<p>9</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>7</p>", "<p>9</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413167.png\" alt=\"rId17\" width=\"220\" height=\"182\"><br>From the above diagram, we can observe that students who cleared cut off of all three sections <br>= 6</p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413167.png\" alt=\"rId17\" width=\"220\" height=\"182\"><br>उपरोक्त आरेख से हम देख सकते हैं कि सभी तीन वर्गों की कट ऑफ उत्तीर्ण करने वाले छात्र = 6 हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Five students Radha. Sujit,Mihir, Anshul and Vikas have a total of ﬁve books on the subjects of Accountancy, Business Studies, Mathematics, Economics and English written by authors Jain, Kohli, Das, Shanna and Edwin. Each student has only one book on one of the ﬁve subjects.<br>Jain is the author of the Accountancy book. which is not owned by Vikas or Radha<br>Anshul owns the book written by Edwin<br>Mihir owns the Mathematics book. <br>Vikas has the English book, which is not written by Kohli <br>The Economics books are written by Sharma<br>Identify the author of the business studies book.</p>",
                    question_hi: "<p>16. पांच छात्र राधा, सुजीत, मिहिर, अंशुल और विकास के पास लेखक जैन, कोहली, दास, शन्ना और एडविन द्वारा लिखित लेखाशास्त्र, व्यवसाय अध्ययन, गणित, अर्थशास्त्र और अंग्रेजी विषयों पर कुल पांच पुस्तकें हैं। प्रत्येक विद्यार्थी के पास पाँच विषयों में से एक विषय पर केवल एक पुस्तक है।<br>जैन लेखाशास्त्र पुस्तक के लेखक हैं। जिसका स्वामित्व विकास या राधा के पास नहीं है।<br>अंशुल एडविन द्वारा लिखित पुस्तक के मालिक हैं<br>मिहिर के पास गणित की पुस्तक है<br>विकास के पास अंग्रेजी की पुस्तक है जो कोहली ने नहीं लिखी है।<br>अर्थशास्त्र की पुस्तकें शर्मा द्वारा लिखी गई हैं<br>व्यवसाय अध्ययन पुस्तक के लेखक कौन है?</p>",
                    options_en: ["<p>Jain</p>", "<p>Edwin</p>", 
                                "<p>Das</p>", "<p>Sharma</p>"],
                    options_hi: ["<p>जैन</p>", "<p>एडविन</p>",
                                "<p>दास</p>", "<p>शर्मा</p>"],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413272.png\" alt=\"rId18\" width=\"567\" height=\"387\"><br>So, from the above chart, we can say that the author of the business studies book is Edwin and it is owned by Anshul.</p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413272.png\" alt=\"rId18\" width=\"567\" height=\"387\"><br>तो, उपरोक्त चार्ट से हम कह सकते हैं कि बिजनेस स्टडीज पुस्तक के लेखक एडविन हैं और इसका स्वामित्व अंशुल के पास है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Pointing to a photograph. Rohit said. \"She is the daughter of the only son of my father.\" How is Rohit related to the girl in the photograph?</p>",
                    question_hi: "<p>17. एक तस्वीर की ओर इशारा करते हुए रोहित ने कहा, \"वह मेरे पिता के इकलौते पुत्र की पुत्री है।\" तस्वीर में दिख रही लड़की से रोहित किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Uncle</p>", "<p>Father</p>", 
                                "<p>Cousin</p>", "<p>Brother</p>"],
                    options_hi: ["<p>चाचा</p>", "<p>पिता</p>",
                                "<p>चचेरा</p>", "<p>भाई</p>"],
                    solution_en: "<p>17.(b) From the given information we can draw the following diagram,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413365.png\" alt=\"rId19\" width=\"210\" height=\"187\"><br>So, Rohit is the father of the girl in the photograph.</p>",
                    solution_hi: "<p>17.(b) दी गई जानकारी से हम निम्नलिखित आरेख बना सकते हैं,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413462.png\" alt=\"rId20\" width=\"234\" height=\"216\"><br>तो, रोहित तस्वीर में दिख रही लड़की का पिता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the number from among the given options that can replace the question mark (?&rsquo;) in the following series: 4, 14, 60, 248,?</p>",
                    question_hi: "<p>18. दिए गए विकल्पों में से उस संख्या का चयन कीजिये जो निम्नलिखित श्रृंखला 4, 14, 60, 248, में प्रश्नवाचक चिह्न (?\') को प्रतिस्थापित कर सकती है?</p>",
                    options_en: ["<p>1020</p>", "<p>1008</p>", 
                                "<p>1012</p>", "<p>1016</p>"],
                    options_hi: ["<p>1020</p>", "<p>1008</p>",
                                "<p>1012</p>", "<p>1016</p>"],
                    solution_en: "<p>18.(b) In the series the pattern is,<br><math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>2</mn><mo>=</mo><mn>14</mn><mi>&#160;</mi></math> <br><math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>4</mn><mo>=</mo><mn>60</mn><mi>&#160;</mi></math> <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>4</mn></msup><mo>-</mo><mn>8</mn><mo>=</mo><mn>248</mn><mi mathvariant=\"normal\">&#160;</mi></math><br>Similarly the sixth term will be,<br><math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></msup><mo>-</mo><mn>16</mn><mo>=</mo><mn>1008</mn><mi>&#160;</mi></math></p>",
                    solution_hi: "<p>18.(b) श्रृंखला में पैटर्न है,<br><math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>2</mn><mo>=</mo><mn>14</mn><mi>&#160;</mi></math> <br><math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>4</mn><mo>=</mo><mn>60</mn><mi>&#160;</mi></math> <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>4</mn></msup><mo>-</mo><mn>8</mn><mo>=</mo><mn>248</mn><mi mathvariant=\"normal\">&#160;</mi></math><br>इसी प्रकार छठा पद होगा,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>5</mn></msup><mo>-</mo><mn>16</mn><mo>=</mo><mn>1008</mn><mi mathvariant=\"normal\">&#160;</mi></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "19. Four awards have been listed out of which three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "19. चार पुरस्कारों को सूचीबद्ध किया गया है। जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक अलग है। विजातीय का चयन कीजिये ?",
                    options_en: [" Padma Bhushan ", " Padma Vibhushan ", 
                                " Param Vir Chakra", " Padma Shri"],
                    options_hi: [" पद्म भूषण", " पद्म विभूषण",
                                " परम वीर चक्र", " पद्म श्री"],
                    solution_en: "19.(c) The Padma Bhushan, Padma Vibhushan, Padma Shri are the civilian awards and only the Param Vir Chakra is different because it is a gallantry award of India.",
                    solution_hi: "19.(c) पद्म भूषण, पद्म विभूषण, पद्म श्री, तीनों नागरिक पुरस्कार हैं। केवल परमवीर चक्र अलग है, क्योंकि यह भारत का वीरता पुरस्कार है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "20. Choose the word that is different from the other three.",
                    question_hi: "20. वह शब्द चुनें जो अन्य तीनों से भिन्न हो",
                    options_en: [" Treacherous ", " Faithful ", 
                                " Devoted ", " Loyal "],
                    options_hi: [" कपटी", " ईमानदार",
                                " समर्पित", " निष्ठावान"],
                    solution_en: "20.(a) Faithful, Devoted, Loyal are synonyms. The word, treacherous, is different because it means guilty of betrayal or deception which is opposite of the other three words. ",
                    solution_hi: "20.(a) वफादार, भक्त, वफादार शब्द पर्यायवाची हैं। केवल विश्वासघाती शब्द अलग है, क्योंकि इसका अर्थ विश्वासघात या धोखे का दोषी है जो अन्य तीन शब्दों के विपरीत है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21. In a certain code, \'best way to win\' is written as \'fa ka la ju\', \'the way to hell’ is written as ‘lu la hu fa\" ‘win of the day’ is written as ‘na lu fu ka\' and \'to sell of night\' is Written as \'na li ya la\'. Which of the following represents \'of the way\"?",
                    question_hi: "21. एक निश्चित कोड में, \'best way to win\' को \'fa ka la ju\'\' लिखा जाता है, \'the way to heli\' को \'‘iu la hu fa\"\' लिखा जाता है, ‘win of the day’ को ‘na lu fu ka\' लिखा जाता है और \'to sell of night\' को \'na li ya la\' लिखा जाता है। निम्नलिखित में से कौन \'of the way\" का प्रतिनिधित्व करता है?",
                    options_en: [" lu na ya", " lu na fa", 
                                " ka lu na", " na ka fa"],
                    options_hi: [" lu na ya", " lu na fa",
                                " ka lu na", " na ka fa"],
                    solution_en: "21.(b) After comparing all the lines, we get <br /><math display=\"inline\"><mo>⇒</mo></math> the = lu, of  = na, way = fa<br />So, \'of the way\" will be written as <math display=\"inline\"><mo>⇒</mo></math> ‘lu na fa’",
                    solution_hi: "21.(b) सभी पंक्तियों की तुलना करने के बाद, हम प्राप्त करते हैं,<br /><math display=\"inline\"><mo>⇒</mo></math> the = lu, of  = na, way = fa<br />तो, of the way को lu na fa के रूप में लिखा जाएगा।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. In which of the given letter-clusters is the letters skipped between adjacent letters in the order <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>1</mn></msup><mo>,</mo><msup><mn>2</mn><mn>2</mn></msup><mo>,</mo><msup><mn>2</mn><mn>3</mn></msup></math></p>",
                    question_hi: "<p>22. दिए गए अक्षरों-समूहों में से किस क्रम में <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>1</mn></msup><mo>,</mo><msup><mn>2</mn><mn>2</mn></msup><mo>,</mo><msup><mn>2</mn><mn>3</mn></msup></math>&nbsp;में आसन्न अक्षरों के बीच छोड़े गए अक्षर हैं?</p>",
                    options_en: ["<p>BEJS</p>", "<p>EIRZ</p>", 
                                "<p>AEJS</p>", "<p>CFIS</p>"],
                    options_hi: ["<p>BEJS</p>", "<p>EIRZ</p>",
                                "<p>AEJS</p>", "<p>CFIS</p>"],
                    solution_en: "<p>22.(a) In the letter-cluster BEJS, the letters skipped between adjacent letters in the same order as given in the question i.e. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>1</mn></msup><mo>=</mo><mn>2</mn><mo>,</mo><msup><mn>2</mn><mn>2</mn></msup><mo>=</mo><mn>4</mn><mo>,</mo><msup><mn>2</mn><mn>3</mn></msup><mo>=</mo><mn>8</mn></math><br>(B <span style=\"text-decoration: underline;\">CD</span> E <span style=\"text-decoration: underline;\">FGHI</span> J <span style=\"text-decoration: underline;\">KLMNOPQR</span> S)</p>",
                    solution_hi: "<p>22.(a) अक्षर-समूह BEJS में, अक्षरों को आसन्न अक्षरों के बीच उसी क्रम में छोड़ दिया गया है जैसा कि प्रश्न में दिया गया है अर्थात <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>1</mn></msup><mo>=</mo><mn>2</mn><mo>,</mo><msup><mn>2</mn><mn>2</mn></msup><mo>=</mo><mn>4</mn><mo>,</mo><msup><mn>2</mn><mn>3</mn></msup><mo>=</mo><mn>8</mn></math><br>(B <span style=\"text-decoration: underline;\">CD</span> E <span style=\"text-decoration: underline;\">FGHI</span> J <span style=\"text-decoration: underline;\">KLMNOPQR</span> S)</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Four brothers Aman. Gaurav. Aakash and Lokesh are at their family function sitting across a circular table. Their occupations are Lawyer, Doctor, Professor and Engineer. Lokesh, who is not the Professor, starts a conversation about the on-going IPL and after him the Engineer gives a long discourse about the teams that should reach the play&mdash;offs. Aman who is sitting across from the Engineer and next to the Professor responds to the Engineer\'s predictions. Akash speaks only at the end. Who is the Professor?</p>",
                    question_hi: "<p>23. चार भाई अमन, गौरव, आकाश और लोकेश अपने पारिवारिक समारोह में एक वृत्ताकार मेज पर बैठे हैं। इनका पेशा वकील, डॉक्टर, प्रोफेसर और इंजीनियर है। लोकेश जो प्रोफेसर नहीं है, चल रहे IPL के बारे में बातचीत शुरू करता है और उसके बाद इंजीनियर उन टीमों के बारे में एक लम्बी चर्चा करता है जिन्हें प्ले-ऑफ में पहुंचना चाहिए। अमन, जो इंजीनियर के सामने और प्रोफेसर के बगल में बैठा है, इंजीनियर के अनुमानों का जवाब देता है। आकाश अंत में ही बोलता है। प्रोफेसर कौन है?</p>",
                    options_en: ["<p>Aakash</p>", "<p>Lokesh</p>", 
                                "<p>Gaurav</p>", "<p>Cannot be determined</p>"],
                    options_hi: ["<p>आकाश</p>", "<p>लोकेश</p>",
                                "<p>गौरव</p>", "<p>ज्ञात नही किया जा सकता है</p>"],
                    solution_en: "<p>23.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413620.png\" alt=\"rId21\" width=\"373\" height=\"204\"></p>",
                    solution_hi: "<p>23.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413737.png\" alt=\"rId22\" width=\"369\" height=\"237\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "24. Select the option that is related to the third term in the same way as the second term is related to the ﬁrst term?<br />Happiness : Sorrow :: Conﬂict : ?",
                    question_hi: "24. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है?<br />सुख : दुख  :: संघर्ष  : ?",
                    options_en: [" War ", " Anger ", 
                                " Harmony ", " Competition "],
                    options_hi: [" युद्ध ", " क्रोध ",
                                " सद्भाव", " प्रतियोगिता"],
                    solution_en: "24.(c) Happiness is the opposite of sorrow. Similarly, conﬂict is the opposite of harmony. ",
                    solution_hi: "24.(c) सुख, दुख के विपरीत है। इसी तरह, संघर्ष सद्भाव के विपरीत है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. Read the following information and answer the question that follows.<br>(I) Five ladies Simran. Vaishali. Namita. Preeti, and Bhawna meet in a hotel for a party. They all sit around a circular table facing the centre of the table.<br>(ii) Bhawna is sitting to the right of Vaishali. <br>(iii) Simran is sitting to the left of Preeti <br>(iv) Preeti is sitting between Namita and Simran. <br>Who is sitting to the right of Namita?</p>",
                    question_hi: "<p>25. निम्नलिखित जानकारी को पढ़िए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>(I) पांच महिलाएं सिमरन, वैशाली, नमिता, प्रीति, और भावना एक पार्टी के लिए एक होटल में मिलती हैं। वे सभी एक वृताकार मेज के चारों ओर मेज के केंद्र की ओर मुख करके बैठी हैं।<br>(ii) भावना वैशाली के दायीं ओर बैठी है।<br>(iii) सिमरन प्रीति के दाहिनी ओर बैठी है।<br>(iv) प्रीति, नमिता और सिमरन के बीच बैठी है<br>नमिता के दायें कौन बैठा है?</p>",
                    options_en: ["<p>Vaishali</p>", "<p>Preeti</p>", 
                                "<p>Bhawna</p>", "<p>Simran</p>"],
                    options_hi: ["<p>वैशाली</p>", "<p>प्रीति</p>",
                                "<p>भावना</p>", "<p>सिमरन</p>"],
                    solution_en: "<p>25.(a) From the given information we can draw the following diagram,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949413867.png\" alt=\"rId23\" width=\"332\" height=\"212\"><br>So, we can see that Vaishali is sitting to the right of Namita.</p>",
                    solution_hi: "<p>25.(a) दी गई जानकारी से हम निम्नलिखित आरेख बना सकते हैं,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727949414018.png\" alt=\"rId24\" width=\"335\" height=\"212\"><br>तो, हम देख सकते हैं कि वैशाली, नमिता के दायीं ओर बैठी है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "26. In a certain code language. PAINT is coded as 83527 and SCORE is coded as 49061. How would you code RECENT in the same language?",
                    question_hi: "26. एक निश्चित कोड भाषा में PAINT को 83527 और SCORE को 49061 के रूप में कोडित किया जाता है। उसी भाषा में आप RECENT को कैसे कोडित करेंगे?",
                    options_en: [" 190985", " 648497", 
                                " 619127", " 921235"],
                    options_hi: [" 190985", " 648497",
                                " 619127", " 921235"],
                    solution_en: "26(c) In a certain code language PAINT is coded as 83527 and SCORE is coded as 49061. So, the codes for R = 6, E = 1, C = 9, N = 2 and T = 7<br /><math display=\"inline\"><mo>⇒</mo></math> the code for RECENT → 619127.",
                    solution_hi: "26(c) एक निश्चित कोड भाषा में PAINT को 83527 और SCORE को 49061 के रूप में कोडित किया जाता है। <br />हमें मिलता है, कोड<math display=\"inline\"><mo>→</mo></math>R= 6, E= 1, C= 9, N = 2 और T= 7 <br />इसलिए, RECENT के लिए कोड <math display=\"inline\"><mo>→</mo></math> 619127",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>