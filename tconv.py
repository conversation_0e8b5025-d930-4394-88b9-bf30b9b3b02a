'''
pip install cssutils aiohttp aiofiles beautifulsoup4 cos-python-sdk-v5
pip install python-magic-bin (for windows)
pip install python-magic (for colab/linux/ubuntu)
'''

import json
import uuid
import base64
import asyncio
import warnings
import functools
import traceback
from html import unescape
from urllib.parse import quote, urlparse
from concurrent.futures import ThreadPoolExecutor

import magic
import aiohttp
import cssutils
import aiofiles
import aiofiles.os as aios
from bs4 import BeautifulSoup
from qcloud_cos import CosConfig, CosS3Client
warnings.filterwarnings("ignore", category=UserWarning)

inp_txt = "b.txt"
main_out_file = "bfinal.txt"
failed_out_file = "failed.txt"

add_b64_image = True #Time consuming process
remove_google_ads = True #Instant (no time consuming)
replace_txt = True #Instant (no time consuming)
download_fonts = True #Time consuming process

to_replace = {
    "sub + img{max-width: 100%; height:32px;}": "",
}
# URL that will be added before the link that don't have domain url
purl = ""

headers = {
        "User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:129.0) Gecko/20100101 Firefox/129.0"
     }

workers = 5

THREADPOOL = ThreadPoolExecutor(max_workers=2000) #No need to change, not actual workers
def sync_upload_to_bucket(file_data, object_key, typ="data", **kwargs):
    object_key = object_key.lstrip("/")
    if isinstance(file_data, (dict, list)):
        file_data = json.dumps(file_data)
    for retries in range(3):
        uid = str(uuid.uuid4()).replace("-","") + "_"
        object_key = f"{object_key.rsplit('/', 1)[0] + '/' if '/' in object_key else ''}{uid}{object_key.rsplit('/', 1)[-1]}"
        secret_id = 'IKID42mIbhZkiRzJQI1YsypWzEE0d5IH1Yrw'
        secret_key = 'scGgspa7SVO7ez7B4kFXeZIp9KKDvXxA'
        region = 'ap-singapore'
        bucket = 'migration-assets-v2-1319671659'
        base_url = 'https://migration-contents-v2.classx.co.in/'
        config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key)
        client = CosS3Client(config)
        try:
            if typ == "data":
                response = client.put_object(Bucket=bucket, Body=file_data, Key=object_key, **kwargs)
            elif typ == "file":
                response = client.upload_file(Bucket=bucket, LocalFilePath=file_data, Key=object_key, PartSize=10, MAXThread=5)
        except Exception as ee:
            if retries < 2:
                continue
            print(f'Uploading Failed :- {ee}')
            return False
        return f'{base_url}{quote(object_key)}'

csv_lock = asyncio.Lock()
async def write_txt(txt, ofile=main_out_file, mode="a"):
    async with csv_lock:
        async with aiofiles.open(ofile, mode, encoding='utf-8') as cf:
            await cf.write(txt)

flock = asyncio.Lock()
async def write_file(dt, ofile, mode="a"):
    async with flock:
        async with aiofiles.open(ofile, mode) as ff:
            await ff.write(dt)

def format_duration(ss, t=60):
    min, sec = divmod(ss , t)
    return f"{min:02d}:{sec:02d}"

lang_dict = {
    "English":"en",
    "Hindi":"hn",
    "Bengali":"bn",
    "Marathi":"mr",
    "Telugu":"te",
    "Assamese":"as",
    "Gujarati":"gu",
    "Kannada":"kn",
    "Kashmiri":"ks",
    "Konkani":"kok",
    "Malayalam":"ml",
    "Manipuri":"mni",
    "Nepali":"ne",
    "Oria":"or",
    "Punjabi":"pa",
    "Sindhi":"sd",
    "Tamil":"ta",
    "Urdu":"ur",
    "Santhali":"sat",
    "Maithili":"mai",
    "Bodo":"brx",
    "Dogri":"doi",
    "Sanskrit":"sa",
    "Garo":"grt",
    "Khasi":"kha",
    "Mizo":"lus",
    "Tibetan":"bo",
    "Kokborak":"trp"
}

def jcopy(jsn):
    return json.loads(json.dumps(jsn))

def flatten_lang_keys(obj):
    lang_keys = lang_dict.values()
    if isinstance(obj, dict):
        result = {}
        for key, value in obj.items():
            processed_value = flatten_lang_keys(value)
            if (isinstance(processed_value, dict) and 
                len(processed_value) == 1 and 
                next(iter(processed_value.keys())) in lang_keys):
                result[key] = next(iter(processed_value.values()))
            else:
                result[key] = processed_value
        return result
    elif isinstance(obj, list):
        return [flatten_lang_keys(item) for item in obj]
    else:
        return obj

def get_custom_json_testbook(qus_data, ans_data):
    if "data" in qus_data:
        qus_data = qus_data["data"]
    if "data" in ans_data:
        ans_data = ans_data["data"]
    qjson = qus_data.copy()
    return_json = {"name": qjson.get("title")}
    return_json["duration"] = qjson.get("duration", 0) / 60
    sections = qjson.get("sections",[])
    return_json["marks"] = sum(section["maxM"] for section in sections)
    return_json["total_questions"] = sum(section["qCount"] for section in sections)
    return_json["sections"] = []
    langs = qus_data.get("languages", ["English"])
    aldict = {lang_dict[lname]: lname for lname in langs} #Available lang dicts {code: name}
    raldict = {v: k for k, v in aldict.items()} #Reversed lang dicts {name: code}
    rjsons = {"all": jcopy(return_json), **{k: jcopy(return_json) for k in aldict.keys()}}
    inst = "\n<br>".join([inst.get("value","") for inst in qjson.get("instructions",[{}])])
    instdict = {k: inst for k in aldict.keys()}
    rjsons["all"]["instructions"] = instdict
    rjsons["all"]["languages"] = aldict
    rjsons["all"]["primary_language"] = lang_dict[qjson["lang"]]
    for k in aldict.keys():
        rjsons[k]["instructions"] = {k: instdict[k]}
        rjsons[k]["languages"] = aldict[k]
        rjsons[k]["primary_language"] = k
    for sindex, sec in enumerate(sections):
        return_json["sections"].append({})
        secjson = {}
        secjson["section_name"], secjson["section_questions"], secjson["section_marks"], questions = sec.get("title"), sec.get("qCount"), sec.get("maxM"), sec.get("questions")
        secjson["pre"], secjson["question_list"] = [], []
        for k in rjsons.keys():
            rjsons[k]["sections"].append(jcopy(secjson))
        for qindex, ques in enumerate(questions):
            qid = ques.get("_id")
            oqtype = ques["type"]
            sqjson = {}
            for lcode, lname in aldict.items():
                qdt = ques.get(lcode,{}) if ques.get(lcode, {}).get("value") else ques.get("en", {})
                for k in [lcode, "all"]:
                    comp = (qdt.get("comp") or "").strip()
                    if comp:
                        for pr in rjsons[k]["sections"][sindex]["pre"]:
                            if comp in pr["text"].get(lcode,""):
                                pr["questions"].append(qindex)
                                break
                            if qindex+1 in pr["questions"]:
                                pr["text"][lcode] = comp
                                break
                        else:
                            rjsons[k]["sections"][sindex]["pre"].append({"questions":[qindex+1], "text": {lcode: comp}})
                    try: rjsons[k]["sections"][sindex]["question_list"][qindex]
                    except: rjsons[k]["sections"][sindex]["question_list"].append({"type": "", "question": {lcode:""}, "options": {lcode:[]}, "answer": "", "solution": {lcode: ""}})
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["question"][lcode] = qdt.get("value")
                    ansjsn = ans_data.get(qid, {})
                    if oqtype in ["mcq", "mamcq"]:
                        qtype = "mcq_multi_correct" if ansjsn.get("multiCorrectOptions") else "mcq_single_correct"
                        anss = ansjsn.get("correctOption") or ansjsn.get("multiCorrectOptions")
                        if len(qdt.get("options")) == 2:
                            qtype = "true_false"
                            anss = {"1": True, "2": False}.get(str(anss))
                            rjsons[k]["sections"][sindex]["question_list"][qindex].pop("options", None)
                        else:
                            rjsons[k]["sections"][sindex]["question_list"][qindex]["options"][lcode] = [opt["value"] for opt in qdt.get("options")]
                        rjsons[k]["sections"][sindex]["question_list"][qindex]["answer"] = anss
                    else:
                        qtype = "range"
                        start, end = ansjsn.get("range",{}).get("start"), ansjsn.get("range",{}).get("end")
                        anss = [start, end]
                        if start == end:
                            qtype == "integer" if str(start).isnumeric() and str(end).isnumeric() else "range"
                            anss = start
                        rjsons[k]["sections"][sindex]["question_list"][qindex]["answer"] = anss
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["type"] = qtype
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["solution"][lcode] = ansjsn.get("sol",{}).get(lcode,{}).get("value","") or ansjsn.get("sol",{}).get("en",{}).get("value","") or ""
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["positive_marks"] = ques.get("posMarks")
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["negative_makrs"] = ques.get("negMarks")
    for k in rjsons.keys():
        if k != "all":
            rjsons[k] = flatten_lang_keys(rjsons[k])
    if len(list(rjsons.keys())) == 2:
        return rjsons[k]
    return rjsons

def get_custom_cp_json(oldjson):
    if "translations" in oldjson["data"]:
        return get_custom_cp_json_v2(oldjson["data"])
    tjson = oldjson.copy()
    tjson = tjson.get("data",{})
    return_json = {
        "name": tjson.get("test", {}).get("name"),
        "instructions": tjson.get("instructions", ""),
        "duration": format_duration(tjson.get("test", {}).get("duration", 0), t = 60000),
        "marks": 0,
        "total_questions": 0,
        "languages": "English",
        "primary_language": "en",
        "sections": []
    }
    for sec in tjson.get("sections"):
        secname, secques, section_marks = sec.get("name"), len(sec.get("questions",[])), sec.get("sectionMarks",0)
        return_json["marks"]+=section_marks
        return_json["total_questions"]+=secques
        secjson = {"section_name": secname,"section_questions": secques,"section_marks": section_marks, "pre":[{"questions": [], "text": ""}], "question_list": []}
        for qn, ques in enumerate(sec.get("questions"), start=1):
            oqtype = ques["type"]
            if oqtype == "fil_ups":
                qtyp = "text_answer"
                anss = ques["options"][0]["solution"][0]
            elif oqtype == "integer":
                qtyp = "integer"
                anss = ques["options"][0]["solution"]
            else:
                qtyp = "mcq_multi_correct" if ques.get("hasMultipleAnswers") else "mcq_single_correct"
                correct_indices = [str(i + 1) for i, opt in enumerate(ques.get("options")) if opt["isCorrect"]]
                anss = correct_indices[0] if len(correct_indices) == 1 else correct_indices
            par = (ques.get("paragraph") or "").strip()
            if par:
                for pr in secjson["pre"]:
                    if par in pr["text"]:
                        pr["questions"].append(qn)
                        break
                else:
                    secjson["pre"].append({"questions":[qn], "text": par})
            qjson = {
                "type": qtyp,
                "question": ques.get("name"),
                "options": [opt["name"]
                    for opt in ques.get("options")
                ],
                "answer": anss,
                "solution": ques.get("solution"),
                "positive_marks": ques.get("marks", {}).get("positive"),
                "negative_marks": ques.get("marks", {}).get("negative")
            }
            if not "mcq" in qtyp: qjson.pop("options", None)
            secjson["question_list"].append(qjson)
        return_json["sections"].append(secjson)
    return return_json

def get_custom_cp_json_v2(oldjson):
    tjson = oldjson.copy()
    return_json = {"name": tjson.get("name")}
    return_json["duration"] = tjson.get("duration", 0) / 60000
    sections = tjson.get("sections",[])
    return_json["marks"] = sum(section.get("sectionMarks",0) for section in sections)
    return_json["total_questions"] = sum(len(section.get("questions",[])) for section in sections)
    return_json["sections"] = []
    langs = ["English"] + tjson.get("languageList",[])
    aldict = {lang_dict[lname]: lname for lname in langs} #Available lang dicts {code: name}
    raldict = {v: k for k, v in aldict.items()} #Reversed lang dicts {name: code}
    rjsons = {"all": jcopy(return_json), **{k: jcopy(return_json) for k in aldict.keys()}}
    inst = tjson.get("instructions")
    instdict = {"en": inst, **{lcd: tjson.get("translations",{}).get(lnm) for lcd, lnm in aldict.items()}}
    instdict = {k: inst for k in aldict.keys()}
    rjsons["all"]["instructions"] = instdict
    rjsons["all"]["languages"] = aldict
    rjsons["all"]["primary_language"] = "en"
    for k in aldict.keys():
        rjsons[k]["instructions"] = {k: instdict[k]}
        rjsons[k]["languages"] = aldict[k]
        rjsons[k]["primary_language"] = k
    for sindex, sec in enumerate(sections):
        return_json["sections"].append({})
        secjson = {}
        secjson["section_name"], secjson["section_questions"], secjson["section_marks"], questions = sec.get("name"), len(sec.get("questions")), sec.get("sectionMarks", 0), sec.get("questions")
        secjson["pre"], secjson["question_list"] = [{"questions": [], "text": {}}], []
        for k in rjsons.keys():
            rjsons[k]["sections"].append(jcopy(secjson))
        for qindex, ques in enumerate(questions):
            oqtype = ques["type"]
            sqjson = {}
            for lcode, lname in aldict.items():
                for k in [lcode, "all"]:
                    qsn = ques.get("name") if lcode == "en" else ques.get("translations",{}).get(lname, {}).get("name") or ques.get("name")
                    soln = ques.get("solution") if lcode == "en" else ques.get("translations",{}).get(lname, {}).get("solution") or ques.get("solution")
                    comp = ques.get("paragraph") if lcode == "en" else (ques.get("paragraph") or {}).get("translations", {}).get(lname, {}).get("name")
                    if comp:
                        for pr in rjsons[k]["sections"][sindex]["pre"]:
                            if comp in pr["text"].get(lcode,""):
                                pr["questions"].append(qindex)
                                break
                        else:
                            rjsons[k]["sections"][sindex]["pre"].append({"questions":[qindex], "text": {lcode: comp}})
                    try: rjsons[k]["sections"][sindex]["question_list"][qindex]
                    except: rjsons[k]["sections"][sindex]["question_list"].append({"type": "", "question": {lcode:""}, "options": {lcode:[]}, "answer": "", "solution": {lcode: ""}})
                    if oqtype == "fil_ups":
                        qtyp = "text_answer"
                        anss = ques["options"][0]["solution"][0] if lcode == "en" else ques["options"][0]["translations"][lname]["solution"][0]
                    elif oqtype == "integer":
                        qtyp = "integer"
                        anss = ques["options"][0]["solution"] if lcode == "en" else ques["options"][0]["translations"][lname]["solution"]
                    elif oqtype == "true_false":
                        qtyp = oqtype
                        for opt in ques["options"]:
                            if "true" in str(opt.get("nameText")).lower() and opt.get("isCorrect"):
                                anss = True
                            if "false" in str(opt.get("nameText")).lower() and opt.get("isCorrect"):
                                anss = False
                    else:
                        qtyp = "mcq_multi_correct" if ques.get("hasMultipleAnswers") else "mcq_single_correct"
                        correct_indices = [str(i + 1) for i, opt in enumerate(ques.get("options")) if opt["isCorrect"]]
                        anss = correct_indices[0] if len(correct_indices) == 1 else correct_indices
                        rjsons[k]["sections"][sindex]["question_list"][qindex]["options"][lcode] = [opt["name"] if lcode == "en" else opt["translations"][lname]["name"] for opt in ques["options"]]
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["answer"] = anss
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["type"] = qtyp
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["question"][lcode] = qsn
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["solution"][lcode] = soln
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["positive_marks"] = ques.get("marks", {}).get("positive")
                    rjsons[k]["sections"][sindex]["question_list"][qindex]["negative_makrs"] = ques.get("marks", {}).get("negative")
    for k in rjsons.keys():
        if k != "all":
            rjsons[k] = flatten_lang_keys(rjsons[k])
    if len(list(rjsons.keys())) == 2:
        return rjsons[k]
    return rjsons

def get_font_families(soup):
    rfonts = set()
    styled_elements = soup.find_all(attrs={'style': True})
    font_families = []
    for element in styled_elements:
        style_string = element['style']
        for declaration in style_string.split(';'):
            if 'font-family' in declaration.lower():
                font_family = declaration.split(':')[1].strip() if ":" in declaration else ""
                if "," in font_family:
                    font_families.extend(font_family.split(","))
                else:
                    font_families.append(font_family) if font_family else None
    font_family_elements = soup.find_all(attrs={'font-family': True})
    for element in font_family_elements:
        font_family = element['font-family']
        if "," in font_family:
            font_families.extend(font_family.split(","))
        else:
            font_families.append(font_family) if font_family else None
    for font in font_families:
        font = font.strip('''"' ''')
        if not font in ["var(--font-family)"]:
            rfonts.add(font)
    return rfonts

class Work:
    def __init__(self):
        self.sess = None
        self.loop = None
        self.images = {}
        self.sem = asyncio.Semaphore(workers)
        self.counters = {
            "Started": 0,
            "Downloaded": 0,
            "Converted": 0,
            "Uploaded": 0
        }

    async def upload_data(self, dt, key, *args, **kwargs):
        func = functools.partial(sync_upload_to_bucket, dt, key, *args, **kwargs)
        return await self.loop.run_in_executor(THREADPOOL, func)

    async def fetch(self, method, url, typ="json", session=None, **kwargs):
        session = session or self.sess
        kwargs["timeout"] = aiohttp.ClientTimeout(total=kwargs.pop("timeout", 40))
        files = kwargs.pop("files", None)
        if files:
            data = aiohttp.FormData()
            data.add_fields(files.items())
            kwargs["data"] = data
        for _ in range(6):
            try:
                async with session.request(method, url, **kwargs) as response:
                    response.raise_for_status()
                    if typ=="json":
                        return await response.json(content_type=None)
                    elif typ == "content":
                        return await response.read()
                    return await response.text()
            except:
                await asyncio.sleep(2)

    async def download_font(self, soup):
        await aios.makedirs("fonts", exist_ok=True)
        rfonts = get_font_families(soup)
        faces = ",".join([rf for rf in rfonts if not await aios.path.exists(f"fonts/{rf}.ttf")])
        if not faces:
            return
        furl = f'https://cms-gcp.classplusapp.com/parser/v3/css/combined?face={quote(faces)}'
        css = await self.fetch("get", furl, typ="text")
        if css:
            sheet = cssutils.parseString(css)
            font_faces = sheet.cssRules
            font_info = []
            for rule in font_faces:
                if rule.type == rule.FONT_FACE_RULE:
                    font_family = rule.style.getPropertyValue('font-family').strip("'"+'"')
                    src = rule.style.getPropertyValue('src')
                    url = src.split('(')[1].split(')')[0]
                    if await aios.path.exists(f'fonts/{font_family}'):
                        # css = css.replace(url, f'fonts/{font_family}')
                        continue
                    font_info.append({'font_name': font_family, 'url': url})
                    font_content = await self.fetch("get", url, ret="content")
                    if font_content:
                        await write_file(font_content, f'fonts/{font_family}.ttf', "wb")
        #                 css = css.replace(url, f'fonts/{font_family}.ttf')
        #     htm = htm.replace("</head>", f'<style>{css}</style></head>')
        # return htm

    async def remove_google_ads(self, soup):
        for tag in soup.find_all("ins", {"class":"adsbygoogle"}):
            tag.decompose()

        # Remove <iframe> elements with ad-related src
        for iframe in soup.find_all("iframe"):
            src = iframe.get("src", "")
            if "ads" in src or "doubleclick" in src or "googlesyndication" in src:
                iframe.decompose()

        # Remove divs that wrap ads (heuristic)
        for div in soup.find_all("div", id=lambda x: x and "aswift" in x.lower()):
            div.decompose()
        return soup

    async def change_link(self, htm):
        soup = BeautifulSoup(htm, "html.parser")
        if remove_google_ads:
            soup = await self.remove_google_ads(soup)
        if download_fonts:
            ftask = asyncio.create_task(self.download_font(soup))
        if not add_b64_image:
            await ftask
            return str(soup)
        img_tags = soup.find_all("img")
        print("found img tags")
        for i, img_tag in enumerate(img_tags):
            if img_tag.get("src"):
                old_link = img_tag["src"]
                if old_link.startswith("//"):
                    old_link = f'https:{old_link}'
                elif old_link.startswith("/"):
                    old_link = f'{purl}{old_link}'
                elif not old_link.startswith(("http://", "https://", "/")):
                    old_link = f'{purl}/{old_link}'
                if old_link in self.images:
                    img_tags[i]["src"] = self.images[old_link]
                    continue
                try:
                    img_data = await self.fetch("get", old_link, session=self.s2, typ="content")
                    if not img_data:
                        print("failed to get img data. url :- ", old_link)
                        continue
                    base64_data = base64.b64encode(img_data).decode("utf-8")
                    mime = magic.Magic(mime=True)
                    mime_type = mime.from_buffer(img_data)
                    bdata = f"data:{mime_type};base64,{base64_data}"
                    img_tags[i]["src"] = bdata
                    print("changed")
                    self.images[old_link] = bdata
                except:
                    pass
        if download_fonts:
            await ftask
        return str(soup)

    async def change_data(self, dt):
        if replace_txt:
            for k, v in to_replace.items():
                dt = dt.replace(k, v)
        if add_b64_image or remove_google_ads or download_fonts:
            dt = await self.change_link(dt)
        return dt

    async def process_recursive(self, data):
        if isinstance(data, dict):
            tasks = {k: asyncio.create_task(self.process_recursive(v)) for k, v in data.items()}
            return {k: await v for k, v in tasks.items()}
        elif isinstance(data, list):
            tasks = [self.process_recursive(item) for item in data]
            return await asyncio.gather(*tasks)
        elif isinstance(data, str):
            return await self.change_data(unescape(data))
        else:
            return data

    async def convert_to_custom(self, jsn):
        if "questions" in jsn and "answers" in jsn:
            return get_custom_json_testbook(jsn["questions"]["data"], jsn["answers"]["data"])
        else:
            return get_custom_cp_json(jsn)

    async def process_json(self, jsn):
        processed = await self.process_recursive(jsn)
        converted = await self.convert_to_custom(processed)
        if not "all" in converted:
            return {"single": converted}
        return converted

    def update(self, key):
        self.counters[key] += 1
        print(f'\r{self.counters}      ', end='')

    async def process_line(self, i, line):
        if not ":UM" in line:
            return
        link = line.split(":UM")[-1]
        if not link.startswith(("http://", "https://")):
            return
        async with self.sem:
            self.update("Started")
            rjson = await self.fetch("get", link)
            if not isinstance(rjson, dict):
                return await write_txt(f'{i}==={line}\n', failed_out_file)
            self.update("Downloaded")
            converted = await self.process_json(rjson)
            self.update("Converted")
            uploads = []
            for lang, cjson in converted.items():
                new_pth = str(urlparse(link).path).rsplit(".", 1)[0] + f"_{lang}_changed.json"
                nlink = await self.upload_data(cjson, new_pth)
                uploads.append([lang, nlink])
            self.update("Uploaded")
            nlinks = ",".join([f'{lng},{lnk}' for lng, lnk in uploads])
            await write_txt(f'{i}==={line},{nlinks}\n')

    async def main(self):
        self.loop = asyncio.get_event_loop()
        async with aiohttp.ClientSession(headers=headers) as self.sess:
            async with aiohttp.ClientSession(headers=headers) as self.s2:
                async with aiofiles.open(inp_txt, "r", encoding="utf-8") as ic:
                    lines = (await ic.read()).strip().split("\n")
                    self.tl = len(lines)
                    self.counters["Total"] = self.tl
                    print(f'Total {self.tl} Lines...')
                    tsks = []
                    for i, row in enumerate(lines, start=1):
                        if i == 1:
                            continue
                        tsks.append(self.process_line(i, row))
                    await asyncio.gather(*tsks)

asyncio.run(Work().main())