<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The average monthly expenditure of a family was ₹2,500 during the first 4 months, ₹2,750 during the next 5 months and ₹3,550 during the last 3 months of a year. If the total saving during the year was ₹5,500, what was the average monthly income of the family?</p>",
                    question_hi: "<p>1. एक परिवार का औसत मासिक खर्च पहले 4 माह के दौरान ₹2,500, अगले 5 माह के दौरान ₹2,750 और वर्ष के अंतिम 3 माह के दौरान ₹3,550 था। यदि वर्ष के दौरान कुल बचत ₹5,500 थी, तो परिवार की औसत मासिक आय कितनी थी?</p>",
                    options_en: ["<p>₹5,790</p>", "<p>₹7,355</p>", 
                                "<p>₹4,375</p>", "<p>₹3,325</p>"],
                    options_hi: ["<p>₹5,790</p>", "<p>₹7,355</p>",
                                "<p>₹4,375</p>", "<p>₹3,325</p>"],
                    solution_en: "<p>1.(d)<br>Total expenditure of the family = 2500 &times; 4 + 2750 &times; 5 + 3550 &times; 3 = 34400<br>Total saving of family = 5500<br>Now, income = expenditure + savings <br>income = 34400 + 5500 = Rs. 39900<br>Hence, average monthly income of family = <math display=\"inline\"><mfrac><mrow><mn>39900</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = Rs. 3325</p>",
                    solution_hi: "<p>1.(d)<br>परिवार का कुल खर्च = 2500 &times; 4 + 2750 &times; 5 + 3550 &times; 3 = 34400<br>परिवार की कुल बचत = 5500<br>अब, आय = व्यय + बचत <br>आय = 34400 + 5500 = रु. 39900<br>अतः, परिवार की औसत मासिक आय = <math display=\"inline\"><mfrac><mrow><mn>39900</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = रु. 3325</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. In June, Rohit&rsquo;s bank account balance is ₹5,000 for 25 days, ₹20,000 for 2 days and ₹1,500 for 3 days. What is the average balance (in ₹) in Rohit\'s bank account in June?</p>",
                    question_hi: "<p>2. जून में, रोहित के बैंक खाते का बैलेंस 25 दिनों के लिए ₹5,000, 2 दिनों के लिए ₹20,000 और 3 दिनों के लिए ₹1,500 है। जून में रोहित के बैंक खाते में औसत बैलेंस (₹ में) कितना है?</p>",
                    options_en: ["<p>5650</p>", "<p>5575</p>", 
                                "<p>6000</p>", "<p>5200</p>"],
                    options_hi: ["<p>5650</p>", "<p>5575</p>",
                                "<p>6000</p>", "<p>5200</p>"],
                    solution_en: "<p>2.(a)<br>Average balance = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 5650</p>",
                    solution_hi: "<p>2.(a)<br>औसत बैलेंस = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>25</mn><mo>&#215;</mo><mn>5000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>20000</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>3</mn><mo>&#215;</mo><mn>1500</mn><mo>)</mo></mrow><mrow><mn>25</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math>&nbsp;= 5650</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The salaries of T in twelve months of a year are: ₹12,000, ₹12,000, ₹12,000, ₹15,000, ₹15,000, ₹15,000, ₹21,000, ₹21,000, ₹21,000, ₹30,000, ₹30,000, ₹30,000. The average salary of T per month in ₹ is _________ .</p>",
                    question_hi: "<p>3. एक वर्ष के बारह माह में T का वेतन इस प्रकार है: ₹12,000, ₹12,000, ₹12,000, ₹15,000, ₹15,000, ₹15,000, ₹21,000, ₹21,000, ₹21,000, ₹30,000, ₹30,000, ₹30,000, T का प्रति माह औसत वेतन ₹ में कितना है?</p>",
                    options_en: ["<p>19500</p>", "<p>18500</p>", 
                                "<p>18000</p>", "<p>19000</p>"],
                    options_hi: ["<p>19500</p>", "<p>18500</p>",
                                "<p>18000</p>", "<p>19000</p>"],
                    solution_en: "<p>3.(a) <br>Total salary of T in one year = 3000(12 + 15 + 21 + 30) = 234000<br>Average salary of T = <math display=\"inline\"><mfrac><mrow><mn>234000</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 19,500</p>",
                    solution_hi: "<p>3.(a) <br>एक वर्ष में T का कुल वेतन = 3000(12 + 15 + 21 + 30) = 234000<br>T का औसत वेतन = <math display=\"inline\"><mfrac><mrow><mn>234000</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 19,500</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If the average of 50 numbers <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>1</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>2</mn></msub></math>,________<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>50</mn></msub></math> is M. Then the average of the numbers <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>1</mn></msub></math>-100, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>2</mn></msub></math>-100,________<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>50</mn></msub></math> - 100 will be:</p>",
                    question_hi: "<p>4. यदि 50 संख्याओं <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>1</mn></msub></math>,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>2</mn></msub></math>,________<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>50</mn></msub></math> का औसत M है, तो संख्याओं <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>1</mn></msub></math>-100,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>2</mn></msub></math>-100,________ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>50</mn></msub></math>- 100 का औसत ज्ञात कीजिए</p>",
                    options_en: ["<p>50M</p>", "<p>M-100</p>", 
                                "<p>100M</p>", "<p>M-50</p>"],
                    options_hi: ["<p>50M</p>", "<p>M-100</p>",
                                "<p>100M</p>", "<p>M-50</p>"],
                    solution_en: "<p>4.(b)<br>average of 50 numbers <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>1</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>2</mn></msub></math>,________<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>50</mn></msub></math> is M<br>M = <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>N</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msub><mo>+</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>N</mi></mrow><mrow><mn>50</mn></mrow></msub></mrow><mrow><mn>50</mn></mrow></mfrac></math> <br><math display=\"inline\"><msub><mrow><mi>N</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msub><mo>+</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>N</mi></mrow><mrow><mn>50</mn></mrow></msub></math> = 50M------(i)<br>Now, numbers N1-100, N2-100,-----------N50 - 100<br>Average = <math display=\"inline\"><mfrac><mrow><mo>(</mo><msub><mrow><mi>N</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>-</mo><mi>&#160;</mi><mn>100</mn><mo>)</mo><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><msub><mrow><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msub><mo>-</mo><mn>100</mn><mo>)</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>(</mo><msub><mrow><mi>N</mi></mrow><mrow><mn>50</mn></mrow></msub><mo>-</mo><mn>100</mn><mo>)</mo></mrow><mrow><mn>50</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>50</mn><mi>M</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>50</mn><mo>)</mo></mrow><mrow><mn>50</mn></mrow></mfrac></math><br>= M - 100<br>Hence, required average = M - 100</p>",
                    solution_hi: "<p>4.(b)<br>50 संख्याओं <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>1</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>2</mn></msub></math>,________<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>N</mi><mn>50</mn></msub></math> का औसत M है<br>M = <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>N</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msub><mo>+</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>N</mi></mrow><mrow><mn>50</mn></mrow></msub></mrow><mrow><mn>50</mn></mrow></mfrac></math> <br><math display=\"inline\"><msub><mrow><mi>N</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msub><mo>+</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msub><mrow><mi>N</mi></mrow><mrow><mn>50</mn></mrow></msub></math> = 50M------(i)<br>Now, numbers N1-100, N2-100,-----------N50 - 100<br>Average = <math display=\"inline\"><mfrac><mrow><mo>(</mo><msub><mrow><mi>N</mi></mrow><mrow><mn>1</mn></mrow></msub><mo>-</mo><mi>&#160;</mi><mn>100</mn><mo>)</mo><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><msub><mrow><mi>N</mi></mrow><mrow><mn>2</mn></mrow></msub><mo>-</mo><mn>100</mn><mo>)</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>_</mo><mo>(</mo><msub><mrow><mi>N</mi></mrow><mrow><mn>50</mn></mrow></msub><mo>-</mo><mn>100</mn><mo>)</mo></mrow><mrow><mn>50</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>50</mn><mi>M</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>50</mn><mo>)</mo></mrow><mrow><mn>50</mn></mrow></mfrac></math><br>= M - 100<br>Hence, required average = M - 100</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A class of 25 students took and English test. 15 students had an average score of 80. The other students had and average score of 60. What is the average score of the whole class?</p>",
                    question_hi: "<p>5. 25 छात्रों की एक कक्षा ने अंग्रेजी की परीक्षा दी। 15 छात्रों के औसत प्राप्तांक 80 थे। अन्य छात्रों के औसत प्राप्तांक 60 थे। पूरी कक्षा का औसत प्राप्तांक क्या है?</p>",
                    options_en: ["<p>80</p>", "<p>72</p>", 
                                "<p>76</p>", "<p>74</p>"],
                    options_hi: ["<p>80</p>", "<p>72</p>",
                                "<p>76</p>", "<p>74</p>"],
                    solution_en: "<p>5.(b) <br>Average of class = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>80</mn><mo>+</mo><mn>10</mn><mo>&#215;</mo><mn>60</mn><mi>&#160;</mi></mrow><mn>25</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mn>25</mn></mfrac></math>= 72</p>",
                    solution_hi: "<p>5.(b) <br>कक्षा का औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>80</mn><mo>+</mo><mn>10</mn><mo>&#215;</mo><mn>60</mn><mi>&#160;</mi></mrow><mn>25</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mn>25</mn></mfrac></math>= 72</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Find the average of natural numbers from 1 to 69. (both included)</p>",
                    question_hi: "<p>6. 1 से 69 तक की प्राकृत संख्याओं का औसत ज्ञात कीजिए। (दोनों का मिलाकर)</p>",
                    options_en: ["<p>37</p>", "<p>35</p>", 
                                "<p>33</p>", "<p>31</p>"],
                    options_hi: ["<p>37</p>", "<p>35</p>",
                                "<p>33</p>", "<p>31</p>"],
                    solution_en: "<p>6.(b)<br>Average of n natural number = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>69</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>70</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br>= 35</p>",
                    solution_hi: "<p>6.(b)<br>n प्राकृत संख्याओं का औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>69</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn><mi>&#160;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>70</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br>= 35</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. In a college, the average weight of 40 boys in a section among 72 students is 33 kg and that of the remaining students is 15 kg. What is the average weight of all the students in the section?</p>",
                    question_hi: "<p>7. एक कॉलेज में, 72 छात्रों के एक समूह में से 40 लड़कों का औसत भार 33 kg है और शेष छात्रों का औसत भार 15 kg है। समूह के सभी छात्रों का औसत भार कितना है?</p>",
                    options_en: ["<p>24 kg</p>", "<p>25 kg</p>", 
                                "<p>22 kg</p>", "<p>18 kg</p>"],
                    options_hi: ["<p>24 kg</p>", "<p>25 kg</p>",
                                "<p>22 kg</p>", "<p>18 kg</p>"],
                    solution_en: "<p>7.(b)<br>Sum of 40 students weight = 40 &times; 33 = 1320 kg<br>Sum of 32 students weight = 32 &times; 15 = 480 kg<br>Average of total students = <math display=\"inline\"><mfrac><mrow><mn>1320</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>480</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> = 25 kg<br><strong>Short tricks:-</strong> 40 : 32 = 5 : 4<br>Ratio -&nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;4<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;33&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;15<br>&nbsp; &nbsp;----------------------------<br>Final - 165&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;60<br>Required average = <math display=\"inline\"><mfrac><mrow><mn>165</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> = 25 kg</p>",
                    solution_hi: "<p>7.(b)<br>40 विद्यार्थियों का कुल भार = 40 &times; 33 = 1320 किग्रा<br>32 विद्यार्थियों का कुल भार = 32 &times; 15 = 480 किग्रा<br>कुल विद्यार्थियों का औसत = <math display=\"inline\"><mfrac><mrow><mn>1320</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>480</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> = 25 किग्रा<br><strong>शार्ट ट्रिक्स : - </strong>40 : 32 = 5 : 4<br>अनुपात-&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;4<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 33&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;15<br>&nbsp;----------------------------<br>अंतिम -&nbsp; &nbsp;165&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;60<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>165</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac></math> = 25 kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. If the average of a number and its reciprocal is 2, then the average of its cube and its reciprocal is equal to:</p>",
                    question_hi: "<p>8. यदि किसी संख्या और उसके व्युत्क्रम का औसत 2 है, तो उस संख्या के घन और घन के व्युत्क्रम का औसत ________के बराबर होगा।</p>",
                    options_en: ["<p>36</p>", "<p>48</p>", 
                                "<p>28</p>", "<p>26</p>"],
                    options_hi: ["<p>36</p>", "<p>48</p>",
                                "<p>28</p>", "<p>26</p>"],
                    solution_en: "<p>8.(d)<br>Let number be <math display=\"inline\"><mi>x</mi></math><br>According to the question,<br><math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>= 4 <br>if <math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>= a then,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi>a</mi><mn>3</mn></msup></math>- 3a<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>3</mn></msup></math> - 3&times;4 = 52<br>Hence, required average = <math display=\"inline\"><mfrac><mrow><mn>52</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 26</p>",
                    solution_hi: "<p>8.(d)<br>माना संख्या <math display=\"inline\"><mi>x</mi></math> है<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>= 4<br>यदि <math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>= a तो, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mi>a</mi><mn>3</mn></msup></math>- 3a<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>3</mn></msup></math> - 3&times;4 = 52<br>अतः, अभीष्ट औसत = <math display=\"inline\"><mfrac><mrow><mn>52</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 26</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A man bought 4 books for ₹50 each, 5 books for ₹60 each and 6 books for ₹70 each. The average cost of a book for the man is (round up to two decimal places):</p>",
                    question_hi: "<p>9. एक व्यक्ति ने ₹50 प्रति पुस्तक के मूल्य से 4 पुस्तकें, ₹60 प्रति पुस्तक के मूल्य से 5 पुस्तकें और ₹70 प्रति पुस्तक के मूल्य से 6 पुस्तकें खरीदीं। व्यक्ति लिए एक पुस्तक का औसत मूल्य क्या है (दशमलव के दो स्थानों तक सन्निकटित)?</p>",
                    options_en: ["<p>₹61.33</p>", "<p>₹50.33</p>", 
                                "<p>₹60.33</p>", "<p>₹36.33</p>"],
                    options_hi: ["<p>₹61.33</p>", "<p>₹50.33</p>",
                                "<p>₹60.33</p>", "<p>₹36.33</p>"],
                    solution_en: "<p>9.(a)<br>Cost of 4 books = 4 &times; 50 = 200 <br>Cost of 5 books = 5 &times; 60 = 300<br>Cost of 6 books = 6 &times; 70 = 420<br>Average cost = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>420</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>6</mn></mrow></mfrac></math> = 61.33</p>",
                    solution_hi: "<p>9.(a)<br>4 पुस्तकों का मूल्य = 4 &times; 50 = 200 <br>5 पुस्तकों का मूल्य = 5 &times; 60 = 300<br>6 पुस्तकों का मूल्य = 6 &times; 70 = 420<br>औसत लागत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>420</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>6</mn></mrow></mfrac></math>&nbsp;= 61.33</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Abhishek\'s marks in Mathematics were incorrectly entered as 93 instead of 63. Due to this, the average marks of the class in Mathematics got increased by 0.5. How many students were there in the class?</p>",
                    question_hi: "<p>10. गणित में अभिषेक के अंक गलती से 63 के बजाय 93 दर्ज कर दिए जाते है। जिसके कारण, गणित में कक्षा के औसत अंकों में 0.5 की वृद्धि हो जाती है। कक्षा में कितने छात्र थे?</p>",
                    options_en: ["<p>55</p>", "<p>60</p>", 
                                "<p>50</p>", "<p>45</p>"],
                    options_hi: ["<p>55</p>", "<p>60</p>",
                                "<p>50</p>", "<p>45</p>"],
                    solution_en: "<p>10.(b)<br>Let take total number of students be <math display=\"inline\"><mi>x</mi></math><br>According to question,<br>Total increase marks = <math display=\"inline\"><mi>x</mi></math> &times; 0.5 = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>So, <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 93 - 63<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 30 &times; 2 = 60</p>",
                    solution_hi: "<p>10.(b)<br>मान लीजिए छात्रों की कुल संख्या <math display=\"inline\"><mi>x</mi></math> है<br>प्रश्न के अनुसार,<br>अंको मे कुल वृद्धि = <math display=\"inline\"><mi>x</mi></math> &times; 0.5 = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>इसलिए, <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 93 - 63<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 30 &times; 2 = 60</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. The weight of the Five friends are 67 kg, 85 kg, 70 kg, 90 kg, and 103 kg. Find the average of their weights in kg.</p>",
                    question_hi: "<p>11. पांचों दोस्तों का भार 67 kg, 85 kg, 70 kg, 90 kg और 103 kg है। उनके भार का औसत (kg में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>90</p>", "<p>80</p>", 
                                "<p>83</p>", "<p>87</p>"],
                    options_hi: ["<p>90</p>", "<p>80</p>",
                                "<p>83</p>", "<p>87</p>"],
                    solution_en: "<p>11.(c)<br>Average weight = <math display=\"inline\"><mfrac><mrow><mn>67</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>85</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>70</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>103</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>415</mn><mn>5</mn></mfrac></math>= 83 kg</p>",
                    solution_hi: "<p>11.(c)<br>औसत भार = <math display=\"inline\"><mfrac><mrow><mn>67</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>85</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>70</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>103</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>415</mn><mn>5</mn></mfrac></math>= 83 kg</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The average of the first 10 natural numbers is _______.</p>",
                    question_hi: "<p>12. प्रथम 10 प्राकृतिक संख्याओं का औसत _______ है।</p>",
                    options_en: ["<p>5</p>", "<p>6</p>", 
                                "<p>6.5</p>", "<p>5.5</p>"],
                    options_hi: ["<p>5</p>", "<p>6</p>",
                                "<p>6.5</p>", "<p>5.5</p>"],
                    solution_en: "<p>12.(d)<br>Average of first 10 natural numbers = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5.5</p>",
                    solution_hi: "<p>12.(d)<br>प्रथम 10 प्राकृत संख्याओं का औसत = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. The average weight of 35 students in a class is 40 kgs. Five of the students of this class whose average weight is 42 kgs left the class. Find the average weight of the remaining 30 students.</p>",
                    question_hi: "<p>13. एक कक्षा के 35 विद्यार्थियों का औसत भार 40 kg है। इस कक्षा के पांच विद्यार्थी जिनका औसत भार 42 kg है, कक्षा छोड़ देते हैं। शेष 30 विद्यार्थियों का औसत भार ज्ञात कीजिए।</p>",
                    options_en: ["<p>39 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>kgs</p>", "<p>29 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>kgs</p>", 
                                "<p>49 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>kgs</p>", "<p>19 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>kgs</p>"],
                    options_hi: ["<p>39 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> kgs</p>", "<p>29 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> kgs</p>",
                                "<p>49 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> kgs</p>", "<p>19 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> kgs</p>"],
                    solution_en: "<p>13.(a) <br>Average weight of remaining 30 student = 40 - 5 &times;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>42</mn><mo>-</mo><mn>40</mn><mo>)</mo></mrow><mrow><mn>35</mn><mo>-</mo><mn>5</mn></mrow></mfrac></math> = 40 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>= 39<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>kgs</p>",
                    solution_hi: "<p>13.(a) <br>शेष 30 छात्रों का औसत वजन = 40 - 5 &times;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>42</mn><mo>-</mo><mn>40</mn><mo>)</mo></mrow><mrow><mn>35</mn><mo>-</mo><mn>5</mn></mrow></mfrac></math> = 40 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>= 39<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>kgs</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. The average salary of a group of 12 employees in an institution is ₹3,950 per month and that of another group of employees is ₹1,850. If the average salary of all employees is ₹2,150, then the total number of employees is:</p>",
                    question_hi: "<p>14. एक संस्थान में 12 कर्मचारियों के समूह का औसत वेतन ₹3,950 प्रति माह और कर्मचारियों के एक दूसरे समूह का औसत वेतन ₹1,850 प्रति माह है। यदि सभी कर्मचारियों का औसत वेतन ₹2,150 है, तो कर्मचारियों की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>84</p>", "<p>100</p>", 
                                "<p>88</p>", "<p>72</p>"],
                    options_hi: ["<p>84</p>", "<p>100</p>",
                                "<p>88</p>", "<p>72</p>"],
                    solution_en: "<p>14.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736832927621.png\" alt=\"rId5\" width=\"249\" height=\"206\"><br><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&rArr; x = 72<br>Total employees = (<math display=\"inline\"><mi>x</mi></math> + 12) = 84</p>",
                    solution_hi: "<p>14.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736832927820.png\" alt=\"rId6\" width=\"227\" height=\"184\"><br><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&rArr; x = 72<br>कुल कर्मचारी = (<math display=\"inline\"><mi>x</mi></math> + 12) = 84</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In a class with a certain number of students, if one student weighing 30 kg is added, then the average weight of the class increases by 1 kg. If one more student weighing 30 kg is added, then the average weight of the class increases by 1.5 kg over the original average. What is the original average weight (in kg) of the class ?</p>",
                    question_hi: "<p>15. छात्रों की एक निश्चित संख्या वाली कक्षा में, यदि 30 kg भार वाले एक छात्र को शामिल कर लिया जाता है, तो कक्षा के औसत भार में 1 kg की वृद्धि हो जाती है। यदि 30 kg भार वाले एक और छात्र को शामिल कर लिया जाता है, तो कक्षा के औसत भार में मूल औसत से 1.5 kg की वृद्धि हो जाती है। कक्षा का मूल औसत भार (kg में) कितना है ?</p>",
                    options_en: ["<p>26.5</p>", "<p>25.5</p>", 
                                "<p>27</p>", "<p>24</p>"],
                    options_hi: ["<p>26.5</p>", "<p>25.5</p>",
                                "<p>27</p>", "<p>24</p>"],
                    solution_en: "<p>15.(c)<br>Let <math display=\"inline\"><mi>n</mi></math>umber of student be n and their average weight be x kg<br>According to the question,<br>one student weighing 30 kg is added, then the average weight of the class increases by 1 kg<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mi>x</mi><mo>+</mo><mn>30</mn></mrow><mrow><mi>n</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> = x + 1<br><math display=\"inline\"><mi>n</mi><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn></math> = nx + x + n+1<br><math display=\"inline\"><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>n</mi></math> = 29<br><math display=\"inline\"><mi>n</mi></math> = 29 - x -----(i)<br>and, If one more student weighing 30 kg is added, then the average weight of the class increases by 1.5 kg over the original average<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mi>x</mi><mo>+</mo><mn>60</mn></mrow><mrow><mi>n</mi><mo>+</mo><mn>2</mn></mrow></mfrac></math> = x + 1.5<br><math display=\"inline\"><mi>n</mi><mi>x</mi></math> + 60 = nx + 2x + 1.5n + 3<br>1.5<math display=\"inline\"><mi>n</mi></math> + 2x = 57------(ii)<br>Putting the value of n from equation (i) to (ii) we get;<br>1.5(29 - <math display=\"inline\"><mi>x</mi></math>) + 2x = 57<br>43.5 + 0.5<math display=\"inline\"><mi>x</mi></math> = 57<br><math display=\"inline\"><mi>x</mi></math> = 27 kg<br>Hence, original average weight of class = 27 kg</p>",
                    solution_hi: "<p>15.(c)<br>माना छात्रों की संख्या <math display=\"inline\"><mi>n</mi></math> है और उनका औसत वजन x किलोग्राम है<br>प्रश्न के अनुसार,<br>30 किलोग्राम वजन वाला एक छात्र जोड़ा जाता है, तो कक्षा का औसत वजन 1 किलोग्राम बढ़ जाता है<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mi>x</mi><mo>+</mo><mn>30</mn></mrow><mrow><mi>n</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> = x + 1<br><math display=\"inline\"><mi>n</mi><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn></math> = nx + x + n+1<br><math display=\"inline\"><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>n</mi></math> = 29<br><math display=\"inline\"><mi>n</mi></math> = 29 - x -----(i)<br>और, यदि 30 किलोग्राम वजन वाला एक और छात्र जोड़ा जाता है, तो कक्षा का औसत वजन मूल औसत से 1.5 किलोग्राम बढ़ जाता है।<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mi>x</mi><mo>+</mo><mn>60</mn></mrow><mrow><mi>n</mi><mo>+</mo><mn>2</mn></mrow></mfrac></math> = x + 1.5<br><math display=\"inline\"><mi>n</mi><mi>x</mi></math> + 60 = nx + 2x + 1.5n + 3<br>1.5<math display=\"inline\"><mi>n</mi></math> + 2x = 57------(ii)<br>समीकरण (i) से (ii) में n का मान रखने पर हमें प्राप्त होता है;<br>1.5(29 - <math display=\"inline\"><mi>x</mi></math>) + 2x = 57<br>43.5 + 0.5<math display=\"inline\"><mi>x</mi></math> = 57<br><math display=\"inline\"><mi>x</mi></math> = 27 kg<br>अतः, कक्षा का मूल औसत वजन = 27 kg</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>