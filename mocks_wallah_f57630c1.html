<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, \'What you think you become\' is written as \'Fe Mo Su Ta Za\' and \'What you think you achieve\' is written as \'Mo Su Te Fe Za\'. How will \'achieve\' be written in that language?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, \'What you think you become\' को \'Fe Mo Su Ta Za\' के रूप में लिखा जाता है और \'What you think you achieve\' को \'Mo Su Te Fe Za\' के रूप में लिखा जाता है। उसी भाषा में \'achieve\' को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>Za</p>", "<p>Mo</p>", 
                                "<p>Te</p>", "<p>Fe</p>"],
                    options_hi: ["<p>Za</p>", "<p>Mo</p>",
                                "<p>Te</p>", "<p>Fe</p>"],
                    solution_en: "<p>1.(c) \'What you think you become\' <math display=\"inline\"><mo>&#8594;</mo></math> \'Fe Mo Su Ta Za\' &hellip;&hellip;&hellip;.. (i)<br>\'What you think you achieve\' <math display=\"inline\"><mo>&#8594;</mo></math> \'Mo Su Te Fe Za &hellip;&hellip;&hellip;.(ii)<br>From (i) and (ii) \'What you think you&rsquo; is common <br>So, the code of &lsquo;achieve&rsquo; is &lsquo;Te&rsquo;</p>",
                    solution_hi: "<p>1.(c) \'What you think you become\' <math display=\"inline\"><mo>&#8594;</mo></math> \'Fe Mo Su Ta Za\' &hellip;&hellip;&hellip;.. (i)<br>\'What you think you achieve\' <math display=\"inline\"><mo>&#8594;</mo></math> \'Mo Su Te Fe Za &hellip;&hellip;&hellip;.(ii)<br>(i) और (ii) से \'What you think you&rsquo; उभयनिष्ट है<br>तो, \'achieve\' का कोड \'te\' है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Which of the following terms will replace the question mark (?) in the given series? <br>YZUW, ?, QTQU, MQOT, INMS</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन-सा पद दी गई शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित करेगा? <br>YZUW, ?, QTQU, MQOT, INMS</p>",
                    options_en: ["<p>UWSV</p>", "<p>WUSV</p>", 
                                "<p>UWVS</p>", "<p>USWV</p>"],
                    options_hi: ["<p>UWSV</p>", "<p>WUSV</p>",
                                "<p>UWVS</p>", "<p>USWV</p>"],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697590952.png\" alt=\"rId6\" width=\"406\" height=\"160\"></p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697590952.png\" alt=\"rId6\" width=\"406\" height=\"160\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the correct mirror image of the given figure when the mirror is placed at MN as shown.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591147.png\" alt=\"rId7\" width=\"113\" height=\"117\"></p>",
                    question_hi: "<p>3. दर्पण को MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिम्ब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591147.png\" alt=\"rId7\" width=\"113\" height=\"117\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591356.png\" alt=\"rId8\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591621.png\" alt=\"rId9\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591873.png\" alt=\"rId10\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592178.png\" alt=\"rId11\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591356.png\" alt=\"rId8\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591621.png\" alt=\"rId9\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591873.png\" alt=\"rId10\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592178.png\" alt=\"rId11\" width=\"90\"></p>"],
                    solution_en: "<p>3.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591621.png\" alt=\"rId9\" width=\"90\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697591621.png\" alt=\"rId9\" width=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Identify the figure given in the options which when put in place of? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592393.png\" alt=\"rId12\" width=\"346\" height=\"85\"></p>",
                    question_hi: "<p>4. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592393.png\" alt=\"rId12\" width=\"346\" height=\"85\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592490.png\" alt=\"rId13\" width=\"80\" height=\"79\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592583.png\" alt=\"rId14\" width=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592674.png\" alt=\"rId15\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592774.png\" alt=\"rId16\" width=\"80\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592490.png\" alt=\"rId13\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592583.png\" alt=\"rId14\" width=\"80\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592674.png\" alt=\"rId15\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592774.png\" alt=\"rId16\" width=\"80\"></p>"],
                    solution_en: "<p>4.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592490.png\" alt=\"rId13\" width=\"80\" height=\"79\"></p>",
                    solution_hi: "<p>4.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592490.png\" alt=\"rId13\" width=\"80\" height=\"79\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the set in which the numbers are related in the same way as are the numbers of the given sets.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13- Operations on 13 such as adding/deleting/multiplying, etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(9, 36, 12)<br>(12, 48, 16)</p>",
                    question_hi: "<p>5. उस समुच्चय का चयन करें जिसमें दी गई संख्याएँ आपस में उसी प्रकार संबंधित हैं, जिस प्रकार प्रश्न में दिए गए समुच्चय की संख्याएँ आपस में संबंधित हैं।<br>(ध्यान दें : संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएँ की जानी चाहिए। जैसे, 13 के मामले में - 13 पर की जाने वाली विभिन्न गणितीय संक्रियाएँ जैसे 13 में जोड़ना/घटाना/ गुणा करना आदि 13 पर की जा सकती हैं। लेकिन 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>(9, 36, 12)<br>(12, 48, 16)</p>",
                    options_en: ["<p>(21, 84, 24)</p>", "<p>(21, 81,28)</p>", 
                                "<p>(21, 81,24)</p>", "<p>(21, 84, 28)</p>"],
                    options_hi: ["<p>(21, 84, 24)</p>", "<p>(21, 81,28)</p>",
                                "<p>(21, 81,24)</p>", "<p>(21, 84, 28)</p>"],
                    solution_en: "<p>5.(d) <strong>Logic</strong> :- (3rd number - 1st number) &times; 12 = 2nd number<br>(9, 36, 12) :- (12 - 9) &times; 12 &rArr; (3) &times; 12 = 36<br>(12, 48, 16) :- (16 - 12) &times; 12 &rArr; (4) &times;12 = 48<br>Similarly,<br>(21, 84, 28) :- (28 - 21) &times; 12 &rArr; (7) &times; 12 = 84</p>",
                    solution_hi: "<p>5.(d) <strong>तर्क</strong> :- (तीसरी संख्या - पहली संख्या ) &times; 12 = दूसरी संख्या<br>(9, 36, 12) :- (12 - 9) &times; 12 &rArr; (3) &times; 12 = 36<br>(12, 48, 16) :- (16 - 12) &times; 12 &rArr; (4) &times;12 = 48<br>इसी प्रकार,<br>(21, 84, 28) :- (28 - 21) &times; 12 &rArr; (7) &times; 12 = 84</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different. <br>Note : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>6. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एकसमान हैं और एक असमान है। निम्नलिखित में से असमान विकल्प को चुनिए। <br>नोट : अक्षर समूह में, असमान विकल्प व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।.</p>",
                    options_en: ["<p>YWVT</p>", "<p>NLKI</p>", 
                                "<p>WVTS</p>", "<p>SQPN</p>"],
                    options_hi: ["<p>YWVT</p>", "<p>NLKI</p>",
                                "<p>WVTS</p>", "<p>SQPN</p>"],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592888.png\" alt=\"rId17\" width=\"153\" height=\"60\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592989.png\" alt=\"rId18\" width=\"160\" height=\"62\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593089.png\" alt=\"rId19\" width=\"170\"></p>\n<p>but</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593180.png\" alt=\"rId20\"></p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592888.png\" alt=\"rId17\" width=\"153\" height=\"60\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697592989.png\" alt=\"rId18\" width=\"160\" height=\"62\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593089.png\" alt=\"rId19\" width=\"170\"></p>\n<p>लेकिन ,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593180.png\" alt=\"rId20\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, &lsquo;VERY&rsquo; is written as &lsquo;6314&rsquo; and &lsquo;VARY&rsquo; is written as &lsquo;1438&rsquo;. How will &lsquo;A&rsquo; be written in that language?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में, &lsquo;VERY&rsquo; को &lsquo;6314&rsquo; के रूप में लिखा जाता है और &lsquo;VARY&rsquo; को &lsquo;1438&rsquo; के रूप में लिखा जाता है। उसी भाषा में &lsquo;A&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>7.(b) <strong>Given:-</strong> VERY &rarr; 6314&hellip;&hellip;(i)<br>VARY &rarr; 1438&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;VRY&rsquo; and &lsquo;143&rsquo; are common. The code of &lsquo;A&rsquo; = &lsquo;8&rsquo;.</p>",
                    solution_hi: "<p>7.(b) <strong>दिया गया</strong>:- VERY &rarr; 6314&hellip;&hellip;(i)<br>VARY &rarr; 1438&hellip;&hellip;.(ii)<br>(i) और (ii) से \'VRY\' और \'143\' उभयनिष्ठ हैं। \'A\' का कोड = \'8\'.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593332.png\" alt=\"rId21\" width=\"150\" height=\"127\"></p>",
                    question_hi: "<p>8. जब दर्पण को MN पर रखा जाता हो तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593332.png\" alt=\"rId21\" width=\"150\" height=\"127\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593450.png\" alt=\"rId22\" width=\"140\" height=\"43\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593561.png\" alt=\"rId23\" width=\"140\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593655.png\" alt=\"rId24\" width=\"140\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593739.png\" alt=\"rId25\" width=\"140\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593450.png\" alt=\"rId22\" width=\"140\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593561.png\" alt=\"rId23\" width=\"140\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593655.png\" alt=\"rId24\" width=\"140\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593739.png\" alt=\"rId25\" width=\"140\"></p>"],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593450.png\" alt=\"rId22\" width=\"140\"></p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593450.png\" alt=\"rId22\" width=\"140\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language,<br>A # B means A is the sister of B.<br>A @ B means A is the son of B.<br>A &amp; B means A is the wife of B.<br>A % B means A is the father of B.<br>How is Q related to R if P &amp; Q % L # R &amp; K ?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में,<br>A # B का अर्थ है A, B की बहन है।<br>A @ B का अर्थ है A, B का पुत्र है।<br>A &amp; B का अर्थ है A, B की पत्नी है।।<br>A % B का अर्थ है A, B का पिता है।<br>यदि P &amp; Q % L # R &amp; K है, तो Q, R से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Sister</p>", "<p>Wife</p>", 
                                "<p>Father</p>", "<p>Mother</p>"],
                    options_hi: ["<p>बहन</p>", "<p>पत्नी</p>",
                                "<p>पिता</p>", "<p>माँ</p>"],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593890.png\" alt=\"rId26\" width=\"226\" height=\"131\"><br>Q is the father of R.</p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697593890.png\" alt=\"rId26\" width=\"226\" height=\"131\"><br>Q, R का पिता है.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. How many triangles are there in the given figure? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594043.png\" alt=\"rId27\" width=\"114\" height=\"103\"></p>",
                    question_hi: "<p>10. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594043.png\" alt=\"rId27\" width=\"114\" height=\"103\"></p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>4</p>", "<p>5</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>4</p>", "<p>5</p>"],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594193.png\" alt=\"rId28\" width=\"191\" height=\"181\"><br>There are 8 triangles<br>ACB, AEG, FHC, ADC, BIJ, IKL, IJM,IBM.</p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594193.png\" alt=\"rId28\" width=\"191\" height=\"181\"><br>8 त्रिभुज हैं<br>ACB, AEG, FHC, ADC, BIJ, IKL, IJM,IBM.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;+&rsquo; and &lsquo;-&rsquo; are interchanged and &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo; and &lsquo;&divide;\' are interchanged?<br>14 + 6 &times; 3 &divide;&nbsp;7 - 2 = ?</p>",
                    question_hi: "<p>11. यदि \'+\' और &lsquo;-&rsquo; को आपस में बदल दिया जाए तथा &lsquo;<math display=\"inline\"><mo>&#215;</mo></math>&rsquo;\' और &lsquo;&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा?<br>14 + 6 <math display=\"inline\"><mo>&#215;</mo></math> 3 &divide; 7 - 2 = ?</p>",
                    options_en: ["<p>10</p>", "<p>2</p>", 
                                "<p>3</p>", "<p>8</p>"],
                    options_hi: ["<p>10</p>", "<p>2</p>",
                                "<p>3</p>", "<p>8</p>"],
                    solution_en: "<p>11.(b) <strong>Given</strong> :- 14 + 6 &times; 3 <math display=\"inline\"><mo>&#247;</mo></math> 7 - 2 <br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get,<br>14 - 6 &divide; 3 &times; 7 + 2<br>14 - 2 &times; 7 + 2 = 2</p>",
                    solution_hi: "<p>11.(b) <strong>दिया गया :</strong>- 14 + 6 &times; 3 &divide; 7 - 2 <br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने के बाद हमें प्राप्त होता है,<br>14 - 6 &divide; 3 &times; 7 + 2<br>14 - 2 &times; 7 + 2 = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>FREE : GTHI : : GAPS : HCSW : : SOAR : ?</p>",
                    question_hi: "<p>12. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह, पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह, तीसरे अक्षर-समूह से संबंधित है।<br>FREE : GTHI : : GAPS : HCSW : : SOAR : ?</p>",
                    options_en: ["<p>TQEV</p>", "<p>TRDV</p>", 
                                "<p>TQDV</p>", "<p>TQDU</p>"],
                    options_hi: ["<p>TQEV</p>", "<p>TRDV</p>",
                                "<p>TQDV</p>", "<p>TQDU</p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594335.png\" alt=\"rId29\" width=\"170\" height=\"123\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594503.png\" alt=\"rId30\" width=\"170\"></p>\n<p>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594676.png\" alt=\"rId31\" width=\"170\"></p>",
                    solution_hi: "<p>12.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594335.png\" alt=\"rId29\" width=\"170\" height=\"123\">&nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594503.png\" alt=\"rId30\" width=\"170\"></p>\n<p>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594676.png\" alt=\"rId31\" width=\"170\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the number from among the given options that can replace the question mark (?) in the following series.<br>543, 518, 495, 474, 455, ?</p>",
                    question_hi: "<p>13. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित श्रृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>543, 518, 495, 474, 455, ?</p>",
                    options_en: ["<p>444</p>", "<p>442</p>", 
                                "<p>440</p>", "<p>438</p>"],
                    options_hi: ["<p>444</p>", "<p>442</p>",
                                "<p>440</p>", "<p>438</p>"],
                    solution_en: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594863.png\" alt=\"rId32\" width=\"255\" height=\"75\"></p>",
                    solution_hi: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594863.png\" alt=\"rId32\" width=\"255\" height=\"75\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Three of the following four number-pairs are alike in a certain way and thus form a group. Which number-pair does NOT belong to that group? <br><strong>(NOTE:</strong> The relation should be found without breaking down the numbers into its constituent digits)</p>",
                    question_hi: "<p>14. निम्नलिखित चार संख्या-युग्मों में से तीन एक निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा संख्या-युग्म उस समूह से संबंधित नहीं है? <br><strong>(नोट:</strong> संख्याओं को उसके घटक अंकों में विभाजित किए बिना संबंध ज्ञात किया जाना चाहिए</p>",
                    options_en: ["<p>83, 97</p>", "<p>73, 79</p>", 
                                "<p>61,71</p>", "<p>31, 41</p>"],
                    options_hi: ["<p>83, 97</p>", "<p>73, 79</p>",
                                "<p>61,71</p>", "<p>31, 41</p>"],
                    solution_en: "<p>14.(b) <strong>Logic:</strong> There is a prime number between both the prime numbers but in option&nbsp;(b) the prime numbers are consecutive.</p>",
                    solution_hi: "<p>14.(b) <strong>तर्क:</strong> दोनों अभाज्य संख्याओं के बीच एक अभाज्य संख्या है लेकिन विकल्प (b) में अभाज्य संख्याएँ क्रमागत हैं</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the option that can replace the question mark (?) in the following series.<br>AH21, CJ18, EL15, GN12, ?</p>",
                    question_hi: "<p>15. उस विकल्प का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आ सकता है।<br>AH21, CJ18, EL15, GN12, ?</p>",
                    options_en: ["<p>J8</p>", "<p>IP9</p>", 
                                "<p>HR11</p>", "<p>HQ10</p>"],
                    options_hi: ["<p>J8</p>", "<p>IP9</p>",
                                "<p>HR11</p>", "<p>HQ10</p>"],
                    solution_en: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594966.png\" alt=\"rId33\" width=\"390\" height=\"132\"></p>",
                    solution_hi: "<p>15.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697594966.png\" alt=\"rId33\" width=\"390\" height=\"132\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed). <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595070.png\" alt=\"rId34\" width=\"80\"></p>",
                    question_hi: "<p>16. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति (X) सन्निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595070.png\" alt=\"rId34\" width=\"80\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595195.png\" alt=\"rId35\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595371.png\" alt=\"rId36\" width=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595549.png\" alt=\"rId37\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595728.png\" alt=\"rId38\" width=\"80\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595195.png\" alt=\"rId35\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595371.png\" alt=\"rId36\" width=\"80\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595549.png\" alt=\"rId37\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595728.png\" alt=\"rId38\" width=\"80\"></p>"],
                    solution_en: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595549.png\" alt=\"rId37\" width=\"80\"></p>",
                    solution_hi: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595549.png\" alt=\"rId37\" width=\"80\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "17. The position of how many letters will remain unchanged if each of the letter in the word ‘IMPROVE’ is arranged in English alphabetical order?",
                    question_hi: "17. यदि शब्द ‘IMPROVE’ के प्रत्येक अक्षर को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी?",
                    options_en: [" Zero ", " One", 
                                " Three ", " Two<br /> "],
                    options_hi: [" शून्य", " एक",
                                " तीन", " दो"],
                    solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595974.png\" alt=\"rId39\" width=\"204\" height=\"72\"><br>From the above arrangement we can see that the position of all letters will change.</p>",
                    solution_hi: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697595974.png\" alt=\"rId39\" width=\"204\" height=\"72\"><br>उपरोक्त व्यवस्था से हम देख सकते हैं कि सभी अक्षरों की स्थिति बदल जाएगी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language, \'VAIN\' is written as \'OKBY&rsquo; and \'VAST\' is written as \'UUBY\'. How will \'VERB&rsquo; be written in that language?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में, \'VAIN\' को \'OKBY&rsquo; लिखा जाता है और &lsquo;VAST\' को \'UUBY\' लिखा जाता है। इसी कूट भाषा में \'VERB&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>CFTY</p>", "<p>CTFY</p>", 
                                "<p>CTYF</p>", "<p>CFYT</p>"],
                    options_hi: ["<p>CFTY</p>", "<p>CTFY</p>",
                                "<p>CTYF</p>", "<p>CFYT</p>"],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697596178.png\" alt=\"rId40\" width=\"132\" height=\"116\">&nbsp; &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697596319.png\" alt=\"rId41\" width=\"132\"></p>\n<p>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697596461.png\" alt=\"rId42\" width=\"132\"></p>",
                    solution_hi: "<p>18.(b)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697596178.png\" alt=\"rId40\" width=\"132\" height=\"116\">&nbsp; &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697596319.png\" alt=\"rId41\" width=\"132\"></p>\n<p>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697596461.png\" alt=\"rId42\" width=\"132\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In this question, three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements.<br><strong>Statements</strong> :<br>No rose is an orchid.<br>All roses are lilies.<br>All lilies are lotuses.<br><strong>Conclusions</strong> :<br>I. Some lilies are orchids.<br>II. Some lotuses are orchids.<br>III. All lilies are orchids.</p>",
                    question_hi: "<p>19. इस प्रश्न में तीन कथन दिए गए हैं, जिनके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए भले ही वे सामान्य रूप से ज्ञात तथ्यों सेअलग प्रतीत होते हों, निर्धारित करें कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन</strong> :<br>कोई गुलाब, ऑर्किड नहीं है।<br>सभी गुलाब, लिली हैं।<br>सभी लिली, कमल हैं।<br><strong>निष्कर्ष</strong> :<br>I. कुछ लिली, ऑर्किड हैं।<br>II. कुछ कमल, ऑर्किड हैं।<br>III. सभी लिली, ऑर्किड हैं।</p>",
                    options_en: ["<p>Both conclusions I and II follow.</p>", "<p>Both conclusions II and III follow.</p>", 
                                "<p>Neither conclusions I, II nor III follows.</p>", "<p>Both conclusions I and III follow.</p>"],
                    options_hi: ["<p>दोनों निष्कर्ष I और II अनुसरण करते हैं।</p>", "<p>दोनों निष्कर्ष II और III अनुसरण करते हैं।</p>",
                                "<p>न तो निष्कर्ष I, II और न ही III अनुसरण करते हैं।</p>", "<p>दोनों निष्कर्ष I और III अनुसरण करते हैं।</p>"],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697596567.png\" alt=\"rId43\" width=\"305\" height=\"158\"><br>Hence, neither conclusion I, II nor III follows.</p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697596694.png\" alt=\"rId44\" width=\"314\" height=\"147\"><br>अतः, न तो निष्कर्ष I, II और न ही III अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option that is related to the third word in the same way as the second word is related to the first word.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/ vowels in the word.)<br>Episode : Series :: Chapter : ?</p>",
                    question_hi: "<p>20. उस विकल्प का चयन कीजिए, जिसका तीसरे शब्द से ठीक वही संबंध हो, जो संबंध दूसरे शब्द का पहले शब्द से है।<br>(शब्दों को सार्थक अँग्रेजी/हिंदी शब्द माना जाना चाहिए और इन्हें शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर परस्पर संबंधित नहीं होना चाहिए।)<br>एपिसोड : सीरीज़ :: अध्याय : ?</p>",
                    options_en: ["<p>Book</p>", "<p>Page</p>", 
                                "<p>Author</p>", "<p>Drama</p>"],
                    options_hi: ["<p>पुस्तक</p>", "<p>पृष्ठ</p>",
                                "<p>लेखक</p>", "<p>नाटक</p>"],
                    solution_en: "<p>20.(a) As the series is the collection of episodes similarly the book is the collection of chapter.</p>",
                    solution_hi: "<p>20.(a) जिस प्रकार सीरीज़ , एपिसोड का संग्रह है उसी प्रकार पुस्तक अध्यायों का संग्रह है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. 3 is related to 2 following a certain logic. Following the same logic, 9 is related to 6. Which of the following numbers is related to 12 using the same logic ?</p>",
                    question_hi: "<p>21. एक निश्चित तर्क का अनुसरण करते हुए 3, 2 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 9, 6 से संबंधित है। उसी तर्क का उपयोग करते हुए निम्नलिखित में से कौन सी संख्या 12 से संबंधित है?</p>",
                    options_en: ["<p>18</p>", "<p>20</p>", 
                                "<p>14</p>", "<p>16</p>"],
                    options_hi: ["<p>18</p>", "<p>20</p>",
                                "<p>14</p>", "<p>16</p>"],
                    solution_en: "<p>21.(a) <strong>Logic</strong> :- Ratio between 1st number and 2nd number is 3 : 2<br>(3 , 2) :- 3 : 2 <br>(9, 6) :- 3 : 2<br>Similarly,<br>(18, 12) :- 3 : 2</p>",
                    solution_hi: "<p>21.(a) <strong>तर्क:-</strong> पहली संख्या और दूसरी संख्या के बीच का अनुपात 3 : 2 है<br>(3 , 2) :- 3 : 2 <br>(9, 6) :- 3 : 2<br>इसी प्रकार,<br>(18, 12) :- 3 : 2</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option that represents the letters that when placed from left to right in the blanks below will complete the given letter series.<br>DF_IJOD_HIJO_FHIJODF_IJOD_</p>",
                    question_hi: "<p>22. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर दी गई अक्षर श्रृंखला पूरी हो जाएगी।<br>DF_IJOD_HIJO_FHIJODF_IJOD_</p>",
                    options_en: ["<p>HFFDH</p>", "<p>FHDHF</p>", 
                                "<p>HFHDF</p>", "<p>HFDHF</p>"],
                    options_hi: ["<p>HFFDH</p>", "<p>FHDHF</p>",
                                "<p>HFHDF</p>", "<p>HFDHF</p>"],
                    solution_en: "<p>22.(d)<br>D F <span style=\"text-decoration: underline;\"><strong>H</strong></span> I J O / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> H I J O / <span style=\"text-decoration: underline;\"><strong>D</strong></span> F H I J O / D F <span style=\"text-decoration: underline;\"><strong>H</strong></span> I J O / D <span style=\"text-decoration: underline;\"><strong>F</strong></span></p>",
                    solution_hi: "<p>22.(d)<br>D F <span style=\"text-decoration: underline;\"><strong>H</strong></span> I J O / D <span style=\"text-decoration: underline;\"><strong>F</strong></span> H I J O / <span style=\"text-decoration: underline;\"><strong>D</strong></span> F H I J O / D F <span style=\"text-decoration: underline;\"><strong>H</strong></span> I J O / D <span style=\"text-decoration: underline;\"><strong>F</strong></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements</strong> :<br>Some bags are purses.<br>All purses are wallets.<br>All wallets are sacks.<br><strong>Conclusions</strong> :<br>(I) All purses are sacks.<br>(II) Some wallets are bags.</p>",
                    question_hi: "<p>23. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन</strong> :<br>कुछ बैग, पर्स हैं।<br>सभी पर्स, बटुए (वॉलेट) हैं।<br>सभी बटुए (वॉलेट), थैले (सैक) हैं।<br><strong>निष्कर्ष</strong> :<br>(I) सभी पर्स, थैले (सैक) हैं।<br>(II) कुछ बटुए (वॉलेट), बैग हैं।</p>",
                    options_en: ["<p>Both conclusions (I) and (II) are follow</p>", "<p>Only conclusion (II) is follow</p>", 
                                "<p>Neither conclusion (I) nor (II) is follow</p>", "<p>Only conclusion (I) is follow</p>"],
                    options_hi: ["<p>निष्कर्ष (I) और निष्कर्ष (II) दोनों अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                                "<p>न तो निष्कर्ष (I) और न ही निष्कर्ष (II) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>"],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697596918.png\" alt=\"rId45\" width=\"247\" height=\"116\"><br>Both conclusions (I) and (II) are follow</p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597104.png\" alt=\"rId46\" width=\"200\"><br>निष्कर्ष (I) और निष्कर्ष (II) दोनों अनुसरण करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. In a certain code language,<br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is B&rsquo;s wife&rsquo;.<br>&lsquo;A + B&rsquo; means &lsquo;A is B&rsquo;s brother&rsquo;.<br>&lsquo;A &times; B&rsquo; means &lsquo;A is B&rsquo;s father&rsquo;.<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is B&rsquo;s sister&rsquo;.<br>Using the same meaning of the mathematical operators as given above, in &lsquo;T &times; X &divide; Y + Z&rsquo; what is T of Z&rsquo;s ?</p>",
                    question_hi: "<p>24. एक निश्चित कूट भाषा में,<br>&lsquo;A - B\' का अर्थ है \'A, B की पत्नी है\'।<br>\'A + B\' का अर्थ है \'A, B का भाई है\'।<br>\'A &times; B\' का अर्थ है \'A, B के पिता है\'।<br>\'A &divide; B\' का अर्थ है \'A, B की बहन है\'।<br>ऊपर दिए गए गणितीय संकारकों के समान अर्थ का उपयोग करते हुए ज्ञात करें कि \'T &times; X &divide; Y + Z\' में T, Z का क्या है?</p>",
                    options_en: ["<p>Father</p>", "<p>Mother</p>", 
                                "<p>Son</p>", "<p>Brother</p>"],
                    options_hi: ["<p>पिता</p>", "<p>माँ</p>",
                                "<p>पुत्र</p>", "<p>भाई</p>"],
                    solution_en: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597342.png\" alt=\"rId47\" width=\"151\" height=\"87\"><br>T is Z&rsquo;s father.</p>",
                    solution_hi: "<p>24.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597342.png\" alt=\"rId47\" width=\"151\" height=\"87\"><br>T, Z का पिता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Identify the figure from the given options, which when put in place of the question mark (?), will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597480.png\" alt=\"rId48\" width=\"322\" height=\"73\"></p>",
                    question_hi: "<p>25. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए, जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर,शृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597480.png\" alt=\"rId48\" width=\"322\" height=\"73\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597577.png\" alt=\"rId49\" width=\"80\" height=\"75\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597710.png\" alt=\"rId50\" width=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597812.png\" alt=\"rId51\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597907.png\" alt=\"rId52\" width=\"80\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597577.png\" alt=\"rId49\" width=\"80\" height=\"75\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597710.png\" alt=\"rId50\" width=\"80\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597812.png\" alt=\"rId51\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597907.png\" alt=\"rId52\" width=\"80\"></p>"],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597710.png\" alt=\"rId50\" width=\"80\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697597710.png\" alt=\"rId50\" width=\"80\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Who was the first to develop a mathematical predictive heliocentric model of the solar system ?",
                    question_hi: "26. सौरमंडल का गणितीय पूर्वानुमानात्मक सूर्यकेन्द्रित मॉडल (heliocentric model) विकसित करने वाला पहले व्यक्ति कौन थे?",
                    options_en: [" Nicolaus Copernicus ", " Galileo Galilei ", 
                                " Pierre-Simon Laplace", " Immanuel Kant"],
                    options_hi: [" निकोलस कॉपरनिकस ", " गैलिलियो गैलिली ",
                                " पियरे-साइमन लाप्लास ", " इम्मैनुएल कांट"],
                    solution_en: "<p>26.(a) <strong>Nicolaus Copernicus.</strong> Although Aryabhatta (5th century A.D.) hinted at astronomical ideas, Copernicus (1473-1543) developed the first mathematical heliocentric model, where planets revolved around a fixed Sun. Galileo supported this model, and Johannes Kepler later refined it by introducing elliptical orbits, now known as Kepler\'s laws.</p>",
                    solution_hi: "<p>26.(a) <strong>निकोलस कॉपरनिकस।</strong> यद्यपि आर्यभट्ट (5वीं शताब्दी ई.) ने खगोलीय विचारों का संकेत दिया था, लेकिन कॉपरनिकस (1473-1543) ने पहला गणितीय सूर्यकेंद्रित मॉडल विकसित किया, जहाँ ग्रह एक स्थिर सूर्य के चारों ओर चक्कर लगाते थे। गैलीलियो ने इस मॉडल का समर्थन किया, और बाद में जोहान्स केपलर ने दीर्घवृत्ताकार कक्षाओं को प्रस्तुत करके इसे परिष्कृत किया, जिसे अब केपलर के नियम से जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Guru Mayadhar Raut was famous for which Indian classical dance form?</p>",
                    question_hi: "<p>27. गुरु मयाधर राउत किस भारतीय शास्त्रीय नृत्य शैली के लिए प्रसिद्ध थे?</p>",
                    options_en: ["<p>Bharatanatyam</p>", "<p>Kathak</p>", 
                                "<p>Odissi</p>", "<p>Kuchipudi</p>"],
                    options_hi: ["<p>भरतनाट्यम</p>", "<p>कथक</p>",
                                "<p>ओडिशी</p>", "<p>कुचिपुड़ी</p>"],
                    solution_en: "<p>27.(c) <strong>Odissi.</strong> He was honored with the Padma Shri award in 2010 for his contributions to Indian classical dance.</p>",
                    solution_hi: "<p>27.(c) <strong>ओडिशी।</strong> उन्हें भारतीय शास्त्रीय नृत्य में उनके योगदान के लिए 2010 में पद्म श्री पुरस्कार से सम्मानित किया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. According to the Department of Industrial Policy and Promotion, which of the following is NOT a core industry ?</p>",
                    question_hi: "<p>28. औद्योगिक नीति एवं संवर्धन विभाग के अनुसार, निम्नलिखित में से कौन सा मुख्य उद्योग नहीं है?</p>",
                    options_en: ["<p>Jute</p>", "<p>Fertilisers</p>", 
                                "<p>Crude oil</p>", "<p>Electricity</p>"],
                    options_hi: ["<p>जूट</p>", "<p>उर्वरक</p>",
                                "<p>कच्चा तेल</p>", "<p>बिजली</p>"],
                    solution_en: "<p>28.(a) <strong>Jute.</strong> The eight core industries included are - Coal, Crude oil, Natural Gas, Petroleum refinery products, Fertilizer, Cement, Steel, and Electricity generation.</p>",
                    solution_hi: "<p>28.(a) <strong>जूट।</strong> आठ प्रमुख उद्योगों में कोयला, कच्चा तेल, प्राकृतिक गैस, पेट्रोलियम रिफाइनरी उत्पाद, उर्वरक, सीमेंट, इस्पात और विद्युत उत्पादन शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which amongst the following statements is correct about Mughal emperor Akbar?</p>",
                    question_hi: "<p>29. मुगल बादशाह अकबर के बारे में निम्नलिखित में से कौन सा कथन सही है?</p>",
                    options_en: ["<p>He founded a new religion called &lsquo;Din-e Ilahi&rsquo;.</p>", "<p>He fought and won the third battle of Panipat against the Hindu King Hemu.</p>", 
                                "<p>He was declared emperor in 1602.</p>", "<p>He was highly educated and knew many languages.</p>"],
                    options_hi: ["<p>उसने \'दीन-ए इलाही\' नामक एक नए धर्म की स्थापना की।</p>", "<p>उसने हिंदू राजा हेमू के विरूद्ध पानीपत का तृतीय युद्ध लड़ा और जीता।</p>",
                                "<p>उसे 1602 में सम्राट घोषित किया गया था।</p>", "<p>वह बहुत अधिक शिक्षित था और कई भाषाओं को जानता था।</p>"],
                    solution_en: "<p>29.(a) <strong>He founded a new religion called &lsquo;Din-e Ilahi&rsquo;</strong>. The theory of Din-i-Ilahi means belief in one God. The first initiated disciples of Din-i-Ilahi during emperor Akbar included Birbal, Prince Salim, and Abul-Fazl ibn Mubarak. He became the third emperor of the Mughal dynasty in 1556 AD till 1605. The Second Battle of Panipat (November 5, 1556) was fought between Hemu, and the army of Akbar,</p>",
                    solution_hi: "<p>29.(a) <strong>उन्होंने \'दीन-ए-इलाही\' नामक एक नए धर्म की स्थापना की।</strong> दीन-ए-इलाही का अर्थ है एक ईश्वर में विश्वास। सम्राट अकबर के दौरान दीन-ए-इलाही के पहले दीक्षित शिष्यों में बीरबल, राजकुमार सलीम और अबुल-फ़ज़ल इब्न मुबारक शामिल थे। वह 1556 ई. से 1605 तक मुगल वंश का तीसरा सम्राट रहा। पानीपत की दूसरी लड़ाई (5 नवंबर, 1556) , हेमू और अकबर की सेना के बीच लड़ी गई थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. What is the global rank of LIC in the Brand Finance Insurance 100 report for 2025?",
                    question_hi: "30. ब्रांड फाइनेंस इंश्योरेंस 100 रिपोर्ट 2025 में LIC की वैश्विक रैंक क्या है?",
                    options_en: [" 1st      ", " 2nd      ", 
                                " 3rd      ", " 4th"],
                    options_hi: [" पहला    ", " दूसरा    ",
                                " तीसरा    ", " चौथा"],
                    solution_en: "30.(c) 3rd. LIC earned a BSI score of 88 out of 100 in the Brand Finance Insurance 100 - 2025 report. PZU from Poland was ranked 1st with a BSI score of 94.4.",
                    solution_hi: "30.(c) तीसरा।<br />Brand Finance Insurance 100 - 2025 रिपोर्ट में LIC को 100 में से 88 का BSI स्कोर प्राप्त हुआ। पोलैंड की कंपनी PZU पहले स्थान पर रही, जिसका BSI स्कोर 94.4 था।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following causes enlargement and extension growth of cells?</p>",
                    question_hi: "<p>31. निम्नलिखित में से कौन-सा कोशिकाओं के विस्तार और फैलाव का कारण बनता है?</p>",
                    options_en: ["<p>Pressure potential</p>", "<p>Osmotic pressure</p>", 
                                "<p>Imbibition</p>", "<p>Turgor pressure</p>"],
                    options_hi: ["<p>दाब विभव (Pressure potential)</p>", "<p>परासरण दाब (Osmotic pressure)</p>",
                                "<p>अंतःशोषण (Imbibition)</p>", "<p>स्फीति दाब (Turgor pressure)</p>"],
                    solution_en: "<p>31.(d) <strong>Turgor pressure.</strong> Pressure potential is controlled by solute potential (when solute potential decreases, pressure potential increases) and the opening and closing of stomata. Osmotic pressure is the minimum pressure which needs to be applied to a solution to prevent the inward flow of its pure solvent across a semipermeable membrane. Imbibition is a special type of diffusion that takes place when liquid is absorbed by solids-colloids causing an increase in volume.</p>",
                    solution_hi: "<p>31.(d) <strong>स्फीति दाब।</strong> दाब विभव को विलेय विभव (जब विलेय विभव घटता है, दाब विभव बढ़ता है) और रंध्रों के खुलने और बंद होने से नियंत्रित किया जाता है। परासरण दाब वह न्यूनतम दाब है जिसे एक अर्धपारगम्य झिल्ली में इसके शुद्ध विलायक के अंतर्मुखी प्रवाह को रोकने के लिए, एक विलयन पर लागू करने की आवश्यकता होती है। अंतःशोषण एक विशेष प्रकार का विसरण है जो तब होता है जब द्रव को ठोस-कोलाइड द्वारा अवशोषित किया जाता है जिससे आयतन में वृद्धि होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. What happens in case of market equilibrium:<br />(A) Market demand = market supply<br />(B) There is no excess supply in the market",
                    question_hi: "32. बाजार संतुलन की स्थिति में क्या होता है:<br />(A) बाजार मांग = बाजार आपूर्ति<br />(B) बाजार में कोई अतिरिक्त आपूर्ति नहीं है",
                    options_en: [" Neither (A) nor (B)  ", " Only (B) ", 
                                " Only (A)", " Both (a) and (b)"],
                    options_hi: [" न तो (A) और न ही (B)", " केवल (B)",
                                " केवल (A)", " दोनों (A) और (B)"],
                    solution_en: "<p>32.(d) <strong>Both (A) and (B). </strong>Market equilibrium is a market state where the supply in the market is equal to the demand in the market. The equilibrium price is the price of a good or service when the supply of it is equal to the demand for it in the market. supply and demand, in economics, relationship between the quantity of a commodity that producers wish to sell at various prices and the quantity that consumers wish to buy.</p>",
                    solution_hi: "<p>32.(d) <strong>दोनों (A) और (B)।</strong> बाजार संतुलन एक बाजार की स्थिति है जहाँ बाजार में आपूर्ति बाजार में मांग के बराबर होती है। संतुलन मूल्य किसी वस्तु या सेवा की कीमत है जब इसकी आपूर्ति बाजार में इसकी मांग के बराबर होती है। अर्थशास्त्र में आपूर्ति और मांग, किसी वस्तु की उस मात्रा के बीच का संबंध है जिसे उत्पादक विभिन्न कीमतों पर बेचना चाहते हैं और वह मात्रा जिसे उपभोक्ता खरीदना चाहते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Who represented India at the 69th Session of the United Nations Commission on the Status of Women (UNCSW)?</p>",
                    question_hi: "<p>33. संयुक्त राष्ट्र महिला स्थिति आयोग (UNCSW) के 69वें सत्र में भारत का प्रतिनिधित्व किसने किया?</p>",
                    options_en: ["<p>Smriti Irani</p>", "<p>Nirmala Sitharaman</p>", 
                                "<p>Annpurna Devi</p>", "<p>Meenakshi Lekhi</p>"],
                    options_hi: ["<p>स्मृति ईरानी</p>", "<p>निर्मला सीतारमण</p>",
                                "<p>अन्नपूर्णा देवी</p>", "<p>मीनाक्षी लेखी</p>"],
                    solution_en: "<p>33.(c) <strong>Annpurna Devi.</strong> India, led by Union Minister Annpurna Devi, participated in the 69th UNCSW Session (March 10-21, 2025) in New York, focusing on gender equality and women&rsquo;s empowerment. India highlighted flagship schemes, SDG commitment, and policy acceleration. Discussions emphasized gender equality mechanisms and international cooperation. Bilateral meetings were held with leaders from Sierra Leone, Uzbekistan, Guyana, and Chile.</p>",
                    solution_hi: "<p>33.(c) <strong>अन्नपूर्णा देवी।</strong> केंद्रीय मंत्री अन्नपूर्णा देवी के नेतृत्व में भारत ने न्यूयॉर्क में 10 से 21 मार्च 2025 के दौरान आयोजित 69वें UNCSW सत्र में भाग लिया, जिसका मुख्य विषय लैंगिक समानता और महिला सशक्तिकरण था। भारत ने इस दौरान प्रमुख योजनाओं, SDG प्रतिबद्धताओं और नीतिगत पहलों को प्रस्तुत किया। चर्चाओं में लैंगिक समानता तंत्र और अंतरराष्ट्रीय सहयोग पर विशेष जोर रहा। सिएरा लियोन, उज्बेकिस्तान, गुयाना और चिली के नेताओं के साथ द्विपक्षीय बैठकें भी हुईं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. What is the duration of single round in free style wrestling ?",
                    question_hi: "34. फ्री स्टाइल कुश्ती में एकल राउंड की अवधि क्या होती है?",
                    options_en: [" 3 min ", " 2.5 min ", 
                                " 3.5 min ", " 2 min"],
                    options_hi: [" 3 मिनट", " 2.5 मिनट",
                                " 3.5 मिनट", " 2 मिनट"],
                    solution_en: "<p>34.(a) <strong>3 min</strong>. A typical freestyle wrestling bout, much like Greco-Roman, is divided into two periods of three minutes each with a 30-second break in between. For official Under-15, cadets, and veteran competitions, the periods are curtailed to two minutes each.</p>",
                    solution_hi: "<p>34.(a) <strong>3 मिनट। </strong>एक सामान्य फ्रीस्टाइल कुश्ती मुकाबला, ग्रीको-रोमन की तरह, तीन-तीन मिनट के दो अवधियों में विभाजित होता है, जिसके बीच में 30 सेकंड का ब्रेक होता है। आधिकारिक अंडर-15, कैडेट और अनुभवी प्रतियोगिताओं के लिए, अवधि को दो-दो मिनट तक सीमित कर दिया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. Which state has adopted the RISE app for digital monitoring of routine child vaccination?",
                    question_hi: "35. किस राज्य ने नियमित बाल टीकाकरण की डिजिटल निगरानी के लिए RISE ऐप को अपनाया है?",
                    options_en: [" Maharashtra", " Uttar Pradesh", 
                                " Rajasthan", " Gujarat"],
                    options_hi: [" महाराष्ट्र", " उत्तर प्रदेश",
                                " राजस्थान", " गुजरात"],
                    solution_en: "<p>35.(b) <strong>Uttar Pradesh</strong>. RISE stands for Routine Immunization Service Enhancement, designed to improve immunization tracking through real-time data. The RISE app initiative in Uttar Pradesh is being supported by UNICEF, assisting in its rollout and impact assessment.</p>",
                    solution_hi: "<p>35.(b) <strong>उत्तर प्रदेश।</strong> RISE का पूर्ण रूप है Routine Immunization Service Enhancement, जिसे वास्तविक समय के डेटा के माध्यम से टीकाकरण निगरानी को बेहतर बनाने के लिए डिज़ाइन किया गया है। उत्तर प्रदेश में RISE ऐप की पहल को यूनिसेफ (UNICEF) का सहयोग प्राप्त है, जो इसके क्रियान्वयन और प्रभाव मूल्यांकन में सहायता कर रहा है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. Which of the following is NOT a part of Carnatic music ?",
                    question_hi: "36. निम्नलिखित में से कौन-सा कर्नाटक संगीत का हिस्सा नहीं है?",
                    options_en: [" Varnam", " Dhrupad ", 
                                " Charana ", " Pallavi"],
                    options_hi: [" वर्णम ", " ध्रुपद ",
                                " चरण ", " पल्लवी"],
                    solution_en: "<p>36.(b) <strong>Dhrupad</strong> is a genre in Hindustani classical music (HCM). Other vocal forms associated with HCM: Khayal, Ghazal, Dhammar, Tarana and Thumri. Main traditional forms of Carnatic music: Alapana, Niraval, Pallavi, Ragam, Kalpnaswaram, Thana.</p>",
                    solution_hi: "<p>36.(b) <strong>ध्रुपद</strong> हिंदुस्तानी शास्त्रीय संगीत (HCM) की एक शैली है। HCM से जुड़े अन्य गायन रूप: ख्याल, ग़ज़ल, धम्मर, तराना और ठुमरी। कर्नाटक संगीत शैली के मुख्य पारंपरिक रूप: अलापना, निरावल, पल्लवी, रागम, कल्पनास्वरम, थाना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. India is home to the second oldest cricket club in the world. Where is it situated?</p>",
                    question_hi: "<p>37. विश्व का दूसरा सबसे पुराना क्रिकेट क्लब भारत में है, यह कहां स्थित है?</p>",
                    options_en: ["<p>Mumbai</p>", "<p>Kolkata</p>", 
                                "<p>Hyderabad</p>", "<p>Chennai</p>"],
                    options_hi: ["<p>मुंबई</p>", "<p>कोलकाता</p>",
                                "<p>हैदराबाद</p>", "<p>चेन्नई</p>"],
                    solution_en: "<p>37.(b) <strong>Kolkata.</strong> The world\'s first Cricket club - Hambledon, England (1760). Marylebone Cricket Club (MCC) - 1787. First Cricket club in India - Calcutta Cricket club (1792). First ever international cricket match - US vs Canada in 1844. India played their first International cricket match - Against England in 1932.</p>",
                    solution_hi: "<p>37.(b) <strong>कोलकाता।</strong> दुनिया का पहला क्रिकेट क्लब - हैम्बल्डन, इंग्लैंड (1760)। मैरीलेबोन क्रिकेट क्लब (MCC) - 1787। भारत में पहला क्रिकेट क्लब - कलकत्ता क्रिकेट क्लब (1792)। पहला अंतरराष्ट्रीय क्रिकेट मैच - 1844 में अमेरिका बनाम कनाडा। भारत ने अपना पहला अंतरराष्ट्रीय क्रिकेट मैच 1932 में इंग्लैंड के खिलाफ खेला।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Which of the following statements is/are true with respect to MRTP Act?<br />i) The Monopolies and Restrictive Trade Practices Act (MRTP Act) was repealed and replaced by the Competition Act, 2002.<br />ii) The asset limit for MRTP companies was fixed at Rs. 25 crores by the first/original Monopolies and Restrictive Trade Practices Act (MRTP Act).<br />iii) The Monopolies and Restrictive Trade Practices Act (MRTP Act) was first passed in 1969.",
                    question_hi: "38.  MRTP अधिनियम के संबंध में निम्नलिखित में से कौन-सा / कौन-से कथन सत्य है/हैं?<br />i) एकाधिकार तथा प्रतिबंधात्मक व्यापार व्यवहार अधिनियम (MRTP Act) को प्रतिस्पर्धा अधिनियम, 2002 द्वारा निरस्त व प्रतिस्थापित किया गया था।<br />ii) पहले/मूल एकाधिकार तथा प्रतिबंधात्मक व्यापार व्यवहार अधिनियम (MRTP Act) द्वारा MRTP कंपनियों के लिए परिसंपत्ति सीमा (asset limit) 25 करोड़ रुपए निर्धारित की गई थी।<br />iii) एकाधिकार तथा प्रतिबंधात्मक व्यापार व्यवहार अधिनियम (MRTP Act) पहली बार 1969 में पारित किया गया था।",
                    options_en: [" Only iii ", " Only i and iii  ", 
                                " Only i and ii ", " Only ii and iii "],
                    options_hi: [" केवल iii ", "<p>केवल i और iii</p>",
                                " केवल i और ii ", " केवल ii और iii"],
                    solution_en: "<p>38.(b) <strong>Only i and iii</strong>. The MRTP Act was indeed first passed in 1969 and was later repealed and replaced by the Competition Act, 2002 . However, the asset limit for MRTP companies was fixed at Rs. 100 crores.</p>",
                    solution_hi: "<p>38.(b) <strong>केवल i और iii.</strong> MRTP अधिनियम वास्तव में पहली बार 1969 में पारित किया गया था और बाद में इसे निरस्त कर दिया गया और प्रतिस्पर्धा अधिनियम, 2002 द्वारा प्रतिस्थापित किया गया। हालांकि, MRTP कंपनियों के लिए परिसंपत्ति सीमा 100 करोड़ रुपये तय की गई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. In the 11th century, under Maḥmūd of Ghazni, Ghaznī, a small town in ______, became the capital of the vast empire of the Ghaznavids.</p>",
                    question_hi: "<p>39. 11वीं शताब्दी में, ग़ज़नी के महमूद के अधीन, गजनी, ________ में एक छोटा-सा शहर, गजनवी के विशाल साम्राज्य की राजधानी बन गया।</p>",
                    options_en: ["<p>Egypt</p>", "<p>Persia</p>", 
                                "<p>Afghanistan</p>", "<p>Turkey</p>"],
                    options_hi: ["<p>मिस्र</p>", "<p>फारस</p>",
                                "<p>अफ़ग़ानिस्तान</p>", "<p>टर्की</p>"],
                    solution_en: "<p>39.(c) <strong>Afghanistan.</strong> Mahmud of Ghazni was a Turkish conqueror who attacked India 17 times between 1000 to 1027 AD. In 1018 he plundered the holy city of Mathura and also attacked Kannauj. In 1019 and 1029 he undertook two raids on Gangetic valley. In 1025 he attacked Somnath (a town on the coast of Kathiawar). His last invasion was in 1027 to punish the Jats who obstructed him on his return journey from Somanath. He died in 1030. </p>",
                    solution_hi: "<p>39.(c) <strong>अफ़गानिस्तान।</strong> महमूद ग़ज़नी एक तुर्की विजेता था जिसने 1000 से 1027 ई. के बीच भारत पर 17 बार आक्रमण किया। 1018 में उसने मथुरा के पवित्र शहर को लूटा और कन्नौज पर भी आक्रमण किया। 1019 और 1029 में उसने गंगा घाटी पर दो बार आक्रमण किया। 1025 में उसने सोमनाथ (काठियावाड़ के तट पर स्थित एक शहर) पर आक्रमण किया। उसका अंतिम आक्रमण 1027 में जाटों को दण्डित करने के लिए हुआ था जिन्होंने सोमनाथ से उसकी वापसी यात्रा में बाधा डाली थी। 1030 में उसकी मृत्यु हो गई।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which type of electromagnetic radiation was discovered by Johann Wilhelm Ritter in 1801?</p>",
                    question_hi: "<p>40. 1801 में जोहान विल्हेम रिटर (Johann Wilhelm Ritter) ने किस प्रकार के विद्युत चुंबकीय विकिरण की खोज की?</p>",
                    options_en: ["<p>X-rays</p>", "<p>Infrared rays</p>", 
                                "<p>Gamma rays</p>", "<p>Ultraviolet rays</p>"],
                    options_hi: ["<p>एक्स-रे</p>", "<p>अवरक्त किरणें</p>",
                                "<p>गामा किरणें</p>", "<p>पराबैंगनी किरणें</p>"],
                    solution_en: "<p>40.(d) <strong>Ultraviolet rays.</strong> Johann Wilhelm Ritter observed that a chemical reaction occurred beyond the violet end of the visible spectrum, leading to the discovery of UV radiation. X-rays were discovered by Wilhelm R&ouml;ntgen in 1895. Infrared rays were discovered by William Herschel in 1800.</p>",
                    solution_hi: "<p>40.(d) <strong>पराबैंगनी किरणें।</strong> जोहान विल्हेम रिटर ने देखा कि दृश्यमान स्पेक्ट्रम के बैंगनी छोर से परे एक रासायनिक प्रतिक्रिया हुई, जिससे UV विकिरण की खोज हुई। एक्स-रे की खोज विल्हेम रॉन्टगन ने 1895 में की थी। अवरक्त किरणों की खोज विलियम हर्शेल ने 1800 में की थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. A school teacher asks students to stand up every time the national anthem is sung. Which Article enshrines this fundamental duty?</p>",
                    question_hi: "<p>41. एक स्कूल शिक्षक हर बार राष्ट्रगान गाए जाने पर छात्रों को खड़े होने के लिए कहता है। कौन-सा अनुच्छेद इस मौलिक कर्तव्य को सुनिश्चित करता है?</p>",
                    options_en: ["<p>Article 51A (a)</p>", "<p>Article 51 (b)</p>", 
                                "<p>Article 51 (d)</p>", "<p>Article 51 (c)</p>"],
                    options_hi: ["<p>अनुच्छेद 51A (a)</p>", "<p>अनुच्छेद 51 (b)</p>",
                                "<p>अनुच्छेद 51 (d)</p>", "<p>अनुच्छेद 51 (c)</p>"],
                    solution_en: "<p>41.(a)<strong> Article 51A (a). </strong>This article states that it is the duty of every citizen of India \"to abide by the Constitution and respect its ideals and institutions, the National Flag, and the National Anthem.\" Article 51A, added by the 42nd Amendment in 1976, lists the Fundamental Duties of Indian citizens, including respecting the National Anthem.</p>",
                    solution_hi: "<p>41.(a) <strong>अनुच्छेद 51A (a)</strong>. इस अनुच्छेद में कहा गया है कि भारत के प्रत्येक नागरिक का यह कर्तव्य है कि वह \"संविधान का पालन करे और उसके आदर्शों और संस्थाओं, राष्ट्रीय ध्वज और राष्ट्रगान का सम्मान करे।\" 1976 में 42वें संशोधन द्वारा जोड़ा गया अनुच्छेद 51A, राष्ट्र गान का सम्मान करने साथ हीं भारतीय नागरिकों के मौलिक कर्तव्यों को सूचीबद्ध करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "42. In which year did the Indian football team go on its first known official international tour?",
                    question_hi: "42. भारतीय फुटबॉल टीम अपने पहले ज्ञात आधिकारिक अंतर्राष्ट्रीय दौरे पर किस वर्ष गई थी?",
                    options_en: [" 1947 ", " 1950 ", 
                                " 1914 ", " 1924"],
                    options_hi: [" 1947 ", " 1950 ",
                                " 1914 ", " 1924"],
                    solution_en: "<p>42.(d) <strong>1924.</strong> An Indian team, comprising both Indian and British players, visited Sri Lanka (Ceylon at the time) for what was the Indian football team&rsquo;s first-ever official foreign tour. Legendary footballer Gostha Pal, regarded as the first captain of the Indian football team, led the squad during the trip.</p>",
                    solution_hi: "<p>42.(d) <strong>1924.</strong> भारतीय और ब्रिटिश दोनों खिलाड़ियों वाली एक भारतीय टीम श्रीलंका (उस समय सीलोन) गई थी, जो भारतीय फुटबॉल टीम का पहला आधिकारिक विदेशी दौरा था। इस दौरे में भारतीय फुटबॉल टीम के पहले कप्तान माने जाने वाले दिग्गज फुटबॉलर गोस्था पाल ने टीम का नेतृत्व किया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which cost is considered for calculating the national income in India?</p>",
                    question_hi: "<p>43. भारत में राष्ट्रीय आय की गणना के लिए किस लागत पर विचार किया जाता है ?</p>",
                    options_en: ["<p>Product cost</p>", "<p>Factor cost</p>", 
                                "<p>Market cost</p>", "<p>Sunk cost</p>"],
                    options_hi: ["<p>उत्पाद लागत</p>", "<p>कारक लागत</p>",
                                "<p>बाज़ार लागत</p>", "<p>विफल लागत</p>"],
                    solution_en: "<p>43.(b) <strong>Factor cost</strong> refers to the cost of factors of production such as labor, capital, and entrepreneurship. Market cost: The cost of a product or service in the market, influenced by supply and demand. Sunk cost: A cost that has already been incurred and cannot be changed or avoided.</p>",
                    solution_hi: "<p>43.(b) <strong>कारक लागत</strong> से तात्पर्य उत्पादन के कारकों जैसे श्रम, पूंजी और उद्यमशीलता की लागत से है। बाजार लागत: बाजार में किसी उत्पाद या सेवा की लागत, जो आपूर्ति और मांग से प्रभावित होती है। विफल लागत: एक लागत जो पहले ही खर्च हो चुकी है और जिसे बदला या टाला नहीं जा सकता।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Identify the INCORRECT pair regarding the material media and their refractive index?</p>",
                    question_hi: "<p>44. भौतिक माध्यम (material media) और उनके अपवर्तनांक के संबंध में गलत युग्म की पहचान करें?</p>",
                    options_en: ["<p>Kerosene &ndash; 1.44</p>", "<p>Benzene &ndash; 2.42</p>", 
                                "<p>Sapphire &ndash; 1.77</p>", "<p>Ruby &ndash; 1.71</p>"],
                    options_hi: ["<p>केरोसीन&ndash; 1.44</p>", "<p>बेंजीन&ndash; 2.42</p>",
                                "<p>नीलम&ndash; 1.77</p>", "<p>रूबी&ndash; 1.71</p>"],
                    solution_en: "<p>44.(b) Benzene &ndash; <strong>2.42.</strong> The refractive index of an optical medium is a dimensionless number that gives the indication of the light bending ability of that medium. Absolute refractive index of some material media: Benzene - 1.50, Rock salt - 1.54, Carbon disulphide - 1.63, Dense flint glass - 1.65, Diamond - 2.42, Crown glass - 1.52, Turpentine oil - 1.47, Water - 1.33, Alcohol - 1.36, Fused quartz - 1.46, Air - 1.0003.</p>",
                    solution_hi: "<p>44.(b) <strong>बेंजीन - 2.42</strong>. किसी प्रकाशीय माध्यम का अपवर्तनांक एक आयामहीन संख्या है जो उस माध्यम की प्रकाश को मोड़ने की क्षमता का संकेत देती है। कुछ भौतिक माध्यमों का निरपेक्ष अपवर्तनांक: बेंजीन - 1.50, सेंधा नमक - 1.54, कार्बन डाइसल्फ़ाइड - 1.63, घने चकमक पत्थर का कांच - 1.65, हीरा - 2.42, क्राउन ग्लास - 1.52, तारपीन का तेल - 1.47, जल - 1.33, एल्कोहल - 1.36, फ्यूज्ड क्वार्ट्ज - 1.46, वायु - 1.0003.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. The Preamble of the Constitution describes India as:</p>",
                    question_hi: "<p>45. संविधान की प्रस्तावना भारत को किस रूप में वर्णित करती है?</p>",
                    options_en: ["<p>an atheist state</p>", "<p>a unitary state</p>", 
                                "<p>a secular state</p>", "<p>a federal state </p>"],
                    options_hi: ["<p>एक नास्तिक राज्य</p>", "<p>एक एकात्मक राज्य</p>",
                                "<p>एक धर्मनिरपेक्ष राज्य</p>", "<p>एक संघीय राज्य</p>"],
                    solution_en: "<p>45.(c) <strong>A secular state. </strong>It does not consider anyone\'s religion as an official religion. India is a Sovereign Socialist Secular Democratic Republic with a Parliamentary form of government which is federal in structure with unitary features. Atheist State - No belief in gods. Unitary State - Central government holds complete authority. Federal State - Central and state governments share autonomy.</p>",
                    solution_hi: "<p>45.(c) <strong>एक धर्मनिरपेक्ष राज्य। </strong>यह किसी के धर्म को आधिकारिक धर्म नहीं मानता। भारत एक संप्रभु समाजवादी धर्मनिरपेक्ष लोकतांत्रिक गणराज्य है, जिसमें संसदीय शासन प्रणाली है, जो एकात्मक विशेषताओं के साथ संरचना में संघीय है। नास्तिक राज्य - देवताओं में कोई विश्वास नहीं। एकात्मक राज्य - केंद्र सरकार के पास पूर्ण अधिकार होता है। संघीय राज्य - केंद्र और राज्य सरकारें स्वायत्तता साझा करती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following statements are true about the positive impact of the Green Revolution (GR) on yield levels in India?<br>i. During the post GR period, the yield of rice grew at a much faster rate than that of wheat.<br>ii. GR technology had penetrated more in wheat crop than in the rice crop.<br>iii. During post GR period, the area under rice achieved a relatively slow growth when compared to the area under wheat.</p>",
                    question_hi: "<p>46. भारत में उपज के स्तर पर हरित क्रांति (GR) के सकारात्मक प्रभाव के संबंध में निम्नलिखित में से कौन-सा कथन सही है?<br>i. हरित क्रांति के बाद, चावल की उपज गेहूं की उपज की तुलना में बहुत तेज दर से बढ़ी।<br>ii. हरित क्रांति तकनीक चावल की फसल की तुलना में गेहूं की फसल में अधिक व्याप्त हो चुकी थी।<br>iii. हरित क्रांति के बाद, गेहूं के तहत क्षेत्र की तुलना में चावल के तहत क्षेत्र ने अपेक्षाकृत धीमी वृद्धि हासिल की।</p>",
                    options_en: ["<p>Only ii and iii</p>", "<p>Only i and iii</p>", 
                                "<p>Only i and ii</p>", "<p>i, ii and iii</p>"],
                    options_hi: ["<p>केवल ii और iii</p>", "<p>केवल i और iii</p>",
                                "<p>केवल i और ii</p>", "<p>i, ii और iii</p>"],
                    solution_en: "<p>46.(a) <strong>Only ii and iii. </strong>The Green Revolution technology had a greater impact on wheat crops than on rice crops. Important Crops in the Revolution: Main crops were - Wheat, Rice, Jowar, Bajra and Maize. Non-food grains were excluded from the ambit of the new strategy. Wheat remained the mainstay of the Green Revolution for years. Mankombu Sambasivan Swaminathan is known as the \"Father of India\'s Green Revolution\" for his work to increase food production and alleviate world hunger.</p>",
                    solution_hi: "<p>46.(a) <strong>केवल ii और iii.</strong> हरित क्रांति तकनीक का चावल की फसलों की तुलना में गेहूं की फसलों पर अधिक प्रभाव पड़ा। क्रांति में महत्वपूर्ण फसलें: मुख्य फसलें थीं - गेहूं, चावल, ज्वार, बाजरा और मक्का। गैर-खाद्यान्नों को नई रणनीति के दायरे से बाहर रखा गया। गेहूं कई वर्षों तक हरित क्रांति का मुख्य आधार बना रहा। मनकोम्बु सम्बाशिवन स्वामीनाथन को खाद्य उत्पादन बढ़ाने और विश्व में भूखमरी को कम करने के लिए किए गए उनके कार्य के लिए \"भारत की हरित क्रांति के जनक\" के रूप में जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. The Press and Periodicals Registration Bill, 2023 replaced which act with a simplified law that decriminalizes various provisions and brings digital media under its ambit?</p>",
                    question_hi: "<p>47. प्रेस और पत्र-पत्रिका पंजीकरण विधेयक, 2023 (The Press and Periodicals Registration Bill, 2023) ने किस अधिनियम को एक सरलीकृत कानून से प्रतिस्थापित किया जो विभिन्न प्रावधानों को अपराधमुक्त करता है और डिजिटल मीडिया को इसके दायरे में लाता है?</p>",
                    options_en: ["<p>Newspaper (Incitement to Offences) Act, 1908</p>", "<p>Censorship of Press Act, 1799</p>", 
                                "<p>Press (Objectionable Matters) Act, 1951</p>", "<p>Press and Registration of Books Act, 1867</p>"],
                    options_hi: ["<p>समाचार पत्र (अपराधों को उकसाना) अधिनियम, 1908 {Newspaper (Incitement to Offences) Act, 1908}</p>", "<p>प्रेस सेंसरशिप अधिनियम, 1799 (Censorship of Press Act, 1799)</p>",
                                "<p>प्रेस (आपत्तिजनक मामले) अधिनियम, 1951 {Press (Objectionable Matters) Act, 1951}</p>", "<p>प्रेस और पुस्तक पंजीकरण अधिनियम, 1867 (Press and Registration of Books Act, 1867)</p>"],
                    solution_en: "<p>47.(d) <strong>Press and Registration of Books Act, 1867.</strong> The Indian Newspaper (Incitement to Offence) Act, 1908 - Allowed the District Magistrate to forfeit any publication that could instigate political violence or acts of murder. Censorship of Press Act, 1799 -Introduced by Lord Wellesley to restrict the French from spreading anti-British government news in India. Press (Objectionable Matters) Act, 1951: To punish the misuse of press freedom.</p>",
                    solution_hi: "<p>47.(d<strong>) प्रेस और पुस्तक पंजीकरण अधिनियम, 1867।</strong> भारतीय समाचार पत्र (अपराध के लिए उकसाना) अधिनियम, 1908 - जिला मजिस्ट्रेट को किसी भी प्रकाशन को जब्त करने की अनुमति देता है जो राजनीतिक हिंसा या हत्या के कृत्यों को भड़का सकता है। प्रेस सेंसरशिप अधिनियम, 1799 - लॉर्ड वेलेजली द्वारा भारत में ब्रिटिश सरकार के खिलाफ समाचार फैलाने से फ्रांसीसियों को प्रतिबंधित करने के लिए पेश किया गया। प्रेस (आपत्तिजनक मामले) अधिनियम, 1951: प्रेस की स्वतंत्रता के दुरुपयोग को दंडित करने के लिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Oil India Limited (OIL) is under the administrative structure of:</p>",
                    question_hi: "<p>48. ऑयल इंडिया लिमिटेड (OIL) किसके प्रशासनिक ढांचे के अंतर्गत है?</p>",
                    options_en: ["<p>Ministry of Commerce and Industry</p>", "<p>Ministry of Power</p>", 
                                "<p>Ministry of New and Renewable Energy</p>", "<p>Ministry of Petroleum and Natural Gas</p>"],
                    options_hi: ["<p>वाणिज्य एवं उद्योग मंत्रालय</p>", "<p>बिजली मंत्रालय</p>",
                                "<p>नवीन एवं अक्षय ऊर्जा मंत्रालय</p>", "<p>पेट्रोलियम एवं प्राकृतिक गैस मंत्रालय</p>"],
                    solution_en: "<p>48.(d) <strong>Ministry of Petroleum and Natural Gas</strong>. Oil India Limited (OIL) is a Maharatna company. Headquarter - Noida. Established - 1959. For a company to be categorized as a Maharatna status, It should be listed on an Indian stock exchange and have an average annual turnover of over ₹25,000 crore during the previous three years. India now has 13 Maharatna,14 Navratna and 74 Miniratna CPSEs.</p>",
                    solution_hi: "<p>48.(d) <strong>पेट्रोलियम और प्राकृतिक गैस मंत्रालय। </strong>ऑयल इंडिया लिमिटेड (OIL) एक महारत्न कंपनी है। मुख्यालय - नोएडा। स्थापना - 1959. महारत्न का दर्जा पाने के लिए किसी कंपनी को भारतीय स्टॉक एक्सचेंज में सूचीबद्ध होना चाहिए और पिछले तीन वर्षों के दौरान उसका औसत वार्षिक कारोबार ₹25,000 करोड़ से अधिक होना चाहिए। भारत में अब 13 महारत्न, 14 नवरत्न और 74 मिनीरत्न CPSE हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Who calculated the approximate location of the planet Neptune by studying gravity- induced disturbances in the motion of Uranus?</p>",
                    question_hi: "<p>49. यूरेनस की गति में गुरुत्वाकर्षण-प्रेरित विक्षोभ (disturbances) का अध्ययन करके नेपच्यून ग्रह के अनुमानित स्थान की गणना किसने की?</p>",
                    options_en: ["<p>John Couch Adams</p>", "<p>Percival Lowell</p>", 
                                "<p>Johann Gottfried Galle</p>", "<p>Urbain-Jean-Joseph Le Verrier</p>"],
                    options_hi: ["<p>जॉन काउच एडम्स</p>", "<p>पर्सिवल लोवेल</p>",
                                "<p>जोहान गॉटफ्राइड गाले</p>", "<p>अर्बेन-जीन-जोसेफ लेवेरियर</p>"],
                    solution_en: "<p>49.(d) <strong>Urbain-Jean-Joseph Le Verrier</strong>. He was a French astronomer and mathematician. Neptune is the eighth and most distant planet in our solar system. It was discovered in 1846. One day on Neptune takes about 16 hours. Uranus is the seventh planet from the Sun. One day on Uranus takes about 17 hours. The order of the planets in the solar system, starting nearest the sun and working outward is the following: Mercury, Venus, Earth, Mars, Jupiter, Saturn, Uranus, Neptune and then the possible Planet Nine.</p>",
                    solution_hi: "<p>49.(d) <strong>अर्बेन-जीन-जोसेफ लेवेरियर।</strong> वह एक फ्रांसीसी खगोलशास्त्री और गणितज्ञ थे। नेपच्यून हमारे सौरमंडल का आठवां और सबसे दूर का ग्रह है। इसकी खोज 1846 में हुई थी। नेपच्यून पर एक दिन में लगभग 16 घंटे होते हैं। यूरेनस सूर्य के नजदीक सातवाँ ग्रह है। यूरेनस पर एक दिन में लगभग 17 घंटे होते हैं। सौरमंडल में ग्रहों का क्रम, सूर्य के सबसे निकट से शुरू होकर बाहर के क्रम में इस प्रकार है: बुध, शुक्र, पृथ्वी, मंगल, बृहस्पति, शनि, यूरेनस, नेपच्यून और कुल संभावित ग्रह नौ है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. According to the Census of India-2011, what is the sex ratio (Females per 1000 males) of India?</p>",
                    question_hi: "<p>50. भारत की जनगणना-2011 के अनुसार, भारत का लिंगानुपात (प्रति 1000 पुरुषों पर महिलाएँ) कितना है?</p>",
                    options_en: ["<p>934</p>", "<p>927</p>", 
                                "<p>933</p>", "<p>943</p>"],
                    options_hi: ["<p>934</p>", "<p>927</p>",
                                "<p>933</p>", "<p>943</p>"],
                    solution_en: "<p>50.(d) <strong>943.</strong> The sex ratio is the ratio of males to females in a population, usually expressed as the number of females per 1000 males. States with Highest Sex Ratio (census 2011): Kerala (1084), Puducherry (1037), Tamil Nadu (996), Andhra Pradesh (993), Chhattisgarh (991). Haryana has the lowest sex ratio (877) in Indian states while in union territory Daman &amp; Diu has lowest sex ratio of 618.</p>",
                    solution_hi: "<p>50.(d) <strong>943.</strong> लिंगानुपात किसी जनसंख्या में पुरुषों और महिलाओं का अनुपात है, जिसे आमतौर पर प्रति 1000 पुरुषों पर महिलाओं की संख्या के रूप में व्यक्त किया जाता है। उच्चतम लिंगानुपात वाले राज्य (जनगणना 2011): केरल (1084), पुडुचेरी (1037), तमिलनाडु (996), आंध्र प्रदेश (993), छत्तीसगढ़ (991)। हरियाणा में भारतीय राज्यों में सबसे कम लिंगानुपात (877) है जबकि केंद्र शासित प्रदेश दमन और दीव में सबसे कम लिंगानुपात 618 है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Find the average of 642, 253, 834 and 303.</p>",
                    question_hi: "<p>51. 642, 253, 834 और 303 का औसत ज्ञात करें।</p>",
                    options_en: ["<p>508</p>", "<p>509</p>", 
                                "<p>512</p>", "<p>452</p>"],
                    options_hi: ["<p>508</p>", "<p>509</p>",
                                "<p>512</p>", "<p>452</p>"],
                    solution_en: "<p>51.(a)<br>Required average = <math display=\"inline\"><mfrac><mrow><mn>642</mn><mo>+</mo><mn>253</mn><mo>+</mo><mn>834</mn><mo>+</mo><mn>303</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2032</mn><mn>4</mn></mfrac></math> = 508</p>",
                    solution_hi: "<p>51.(a)<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>642</mn><mo>+</mo><mn>253</mn><mo>+</mo><mn>834</mn><mo>+</mo><mn>303</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2032</mn><mn>4</mn></mfrac></math> = 508</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The given table shows the number of books on different subjects.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598011.png\" alt=\"rId53\" width=\"191\" height=\"120\"> <br>What is the ratio of the number of books for subject B1 to the average number of books per subject?</p>",
                    question_hi: "<p>52. दी गई तालिका विभिन्न विषयों की पुस्तकों की संख्या को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598104.png\" alt=\"rId54\" width=\"155\" height=\"130\"> <br>विषय B1 की पुस्तकों की संख्या और प्रति विषय पुस्तकों की औसत संख्या का अनुपात क्या है?</p>",
                    options_en: ["<p>3 : 5</p>", "<p>10 : 11</p>", 
                                "<p>4 : 5</p>", "<p>2 : 5</p>"],
                    options_hi: ["<p>3 : 5</p>", "<p>10 : 11</p>",
                                "<p>4 : 5</p>", "<p>2 : 5</p>"],
                    solution_en: "<p>52.(b) Average number of books = <math display=\"inline\"><mfrac><mrow><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mn>120</mn><mi>&#160;</mi><mo>+</mo><mn>120</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>660</mn><mn>5</mn></mfrac></math> = 132<br>Number of books B1 = 120<br>Required ratio = 120 : 132 or 10 : 11</p>",
                    solution_hi: "<p>52.(b) पुस्तकों की औसत संख्या = <math display=\"inline\"><mfrac><mrow><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mn>120</mn><mi>&#160;</mi><mo>+</mo><mn>120</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>660</mn><mn>5</mn></mfrac></math> = 132<br>B1 पुस्तकों की संख्या = 120<br>आवश्यक अनुपात = 120 : 132 या 10 : 11</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. If sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math>, then the value of tan&theta; is:</p>",
                    question_hi: "<p>53. यदि sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math>, तो tan&theta; का मान हैं :</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><mn>2</mn><mi>xy</mi></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>x</mi><mi>y</mi></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>2</mn><mi>x</mi><mi>y</mi></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>"],
                    solution_en: "<p>53.(a) <br>(2xy&nbsp;: x<sup>2 </sup>+ y<sup>2</sup> : x<sup>2</sup> - y<sup>2</sup>) are pythagorean triplet&nbsp;<br>sin&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Perpendicular</mi><mo>&#160;</mo></mrow><mi>Hypotenuse</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math><br>So, tan&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Perpendicular</mi><mi>Base</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                    solution_hi: "<p>53.(a) <br>(2xy&nbsp;: x<sup>2 </sup>+ y<sup>2</sup> : x<sup>2</sup> - y<sup>2</sup>) पायथागॉरियन त्रिक हैं <br>sin&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math><br>तो , tan&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2310;&#2343;&#2366;&#2352;</mi><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>xy</mi></mrow><mrow><msup><mi mathvariant=\"normal\">x</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The side of an equilateral triangle is 28 cm. Taking each vertex as the centre, a circle is described with a radius equal to half the length of the side of the triangle. Find the area of that&nbsp;part of the triangle which is not included in the circles (use <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> and&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 1.73 ).</p>",
                    question_hi: "<p>54. एक समबाहु त्रिभुज की भुजा 28 cm है। प्रत्येक शीर्ष को केंद्र मानकर, त्रिभुज की भुजा की आधी लंबाई के बराबर त्रिज्या वाले एक वृत्त को चित्रित किया गया है। त्रिभुज के उस भाग का क्षेत्रफल ज्ञात कीजिए, जो वृत्त में शामिल नहीं है ( <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = 1.73 का उपयोग कीजिए )।</p>",
                    options_en: ["<p>30.89 cm&sup2;</p>", "<p>38.08 cm&sup2;</p>", 
                                "<p>31.08 cm&sup2;</p>", "<p>39.08 cm&sup2;</p>"],
                    options_hi: ["<p>30.89 cm&sup2;</p>", "<p>38.08 cm&sup2;</p>",
                                "<p>31.08 cm&sup2;</p>", "<p>39.08 cm&sup2;</p>"],
                    solution_en: "<p>54.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598266.png\" alt=\"rId55\" width=\"262\" height=\"179\"><br>Area of 3 sectors = 3 &times; (<math display=\"inline\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14<sup>2</sup> ) = 308 cm&sup2;<br>Area of <math display=\"inline\"><mo>&#9651;</mo></math>ABC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; 28<sup>2</sup> = 339.08 cm&sup2;<br>Then, area of shaded region = 339.08 - 308 = 31.08 cm&sup2;</p>",
                    solution_hi: "<p>54.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598266.png\" alt=\"rId55\" width=\"262\" height=\"179\"><br>3 सेक्टरों का क्षेत्रफल = 3 &times; (<math display=\"inline\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 14<sup>2</sup> ) = 308 cm&sup2;<br><math display=\"inline\"><mo>&#9651;</mo></math>ABC का क्षेत्रफल = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math> &times; 28<sup>2</sup> = 339.08 cm&sup2;<br>फिर, छायांकित क्षेत्र का क्षेत्रफल = 339.08 - 308 = 31.08 cm&sup2;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. In the 8<sup>th</sup> class, consisting of 30 students, a Mathematics test was taken. 10 students had an average score of 90. The other students had an average score of 75. What is the average score of the whole class?</p>",
                    question_hi: "<p>55. 30 विद्यार्थियों वाली 8वीं कक्षा में, गणित की परीक्षा ली गई। 10 विद्यार्थियों का औसत स्कोर 90 था। अन्य विद्यार्थियों का औसत स्कोर 75 था। पूरी कक्षा का औसत स्कोर ज्ञात करें।</p>",
                    options_en: ["<p>78</p>", "<p>80</p>", 
                                "<p>76</p>", "<p>74</p>"],
                    options_hi: ["<p>78</p>", "<p>80</p>",
                                "<p>76</p>", "<p>74</p>"],
                    solution_en: "<p>55.(b)<br>Total students = 30<br>Average score of 10 students = 90<br>Average score of 20 students = 75<br>Average score of 30 students = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>75</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>900</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1500</mn></mrow><mn>30</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2400</mn><mn>30</mn></mfrac></math> = 80 marks</p>",
                    solution_hi: "<p>55.(b)<br>कुल छात्र = 30<br>10 विद्यार्थियों का औसत अंक = 90<br>20 छात्रों का औसत स्कोर = 75<br>30 विद्यार्थियों का औसत अंक = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>20</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>75</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>900</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1500</mn></mrow><mn>30</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2400</mn><mn>30</mn></mfrac></math>&nbsp;= 80 अंक</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. 2 biscuits and 1 chocolate cost ₹69. 2 chocolates and 3 cups of coffee cost ₹127. 3 biscuits, 4 chocolates and 2 cups of coffee cost ₹229. Find the total cost (in ₹) of 5 biscuits, 5 chocolates and 5 cups of coffee.</p>",
                    question_hi: "<p>56. 2 बिस्कुट और 1 चॉकलेट की कीमत ₹69 है। 2 चॉकलेट और 3 कप कॉफी की कीमत ₹127 है। 3 बिस्कुट, 4 चॉकलेट और 2 कप कॉफी की कीमत ₹229 है। 5 बिस्कुट, 5 चॉकलेट और 5 कप कॉफी की कुल कीमत (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>304</p>", "<p>375</p>", 
                                "<p>345</p>", "<p>355</p>"],
                    options_hi: ["<p>304</p>", "<p>375</p>",
                                "<p>345</p>", "<p>355</p>"],
                    solution_en: "<p>56.(d) <br>Let cost of 1 biscuit, 1 chocolate, 1 cup coffee be x, y, z&nbsp;respectively.<br>According to question,<br>2x + y = 69 &hellip; (i)<br>2y&nbsp;+ 3z = 127&hellip; (ii)<br>3x + 4y + 2z = 229&hellip; (iii)<br>Adding (i) and (iii) <br>5x&nbsp;+ 5y + 2z = 69 + 229<br>5x&nbsp;+ 5y + 2z = 298&hellip; (iv)<br>now,<br>Multiply eq . (i) by 2 and then subtracting eq . (ii) we get<br>4x&nbsp;- 3z = 138 - 127 <br>4x&nbsp;- 3z = 11 &hellip; (v)<br>Multiply eq . (ii) by 2 and then subtracting eq . (iii) we get<br>- 3x&nbsp;+ 4z = 254 - 229<br>- 3x&nbsp;+ 4z = 25 &hellip; (vi)<br>By solving eq . (v) and (vi) we get <br>z = 19<br>From eq . (iv) adding 3z&nbsp;we get<br>5x&nbsp;+ 5y + 5z = 298 + 19 &times; 3 = 298 + 57 = 355</p>",
                    solution_hi: "<p>56.(d) <br>माना 1 बिस्किट, 1 चॉकलेट, 1 कप कॉफी की लागत क्रमशः x, y, z है।<br>प्रश्न के अनुसार,<br>2x + y = 69 &hellip; (i)<br>2y&nbsp;+ 3z = 127&hellip; (ii)<br>3x + 4y + 2z = 229&hellip; (iii)<br>(i) और (iii) जोड़ने पर <br>5x&nbsp;+ 5y + 2z = 69 + 229<br>5x&nbsp;+ 5y + 2z = 298&hellip; (iv)<br>अब,<br>समीकरण (i) को 2 से गुणा करें और फिर समीकरण (ii) को घटाने पर हमें प्राप्त होता है। <br>4x&nbsp;- 3z = 138 - 127 <br>4x&nbsp;- 3z = 11 &hellip; (v)<br>समीकरण (ii) को 2 से गुणा करें और फिर समीकरण (iii) को घटाने पर हमें प्राप्त होता है। <br>- 3x&nbsp;+ 4z = 254 - 229<br>- 3x&nbsp;+ 4z = 25 &hellip; (vi)<br>समीकरण (v) और (vi) को हल करने पर हमें प्राप्त होता है <br>z = 19<br>समीकरण (iv) में 3z जोड़ने पर हमें प्राप्त होता है<br>5<math display=\"inline\"><mi>x</mi></math> + 5y + 5z = 298 + 19 &times; 3 = 298 + 57 = 355</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Study the given pie-charts and answer the question that follows.<br>The pie-charts show the characteristics of foreign tourists visiting India during a given year.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598424.png\" alt=\"rId56\" width=\"226\" height=\"225\">&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598561.png\" alt=\"rId57\" width=\"276\" height=\"236\"> <br>If in a given year, 2,50,000 tourists visited India and the age wise distribution of data applies to all the countries, then the number of Russian tourists who visited India during the year and were in the age group above 50 years is:</p>",
                    question_hi: "<p>57. दिए गए पाई-चार्ट का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>पाई-चार्ट किसी दिए गए वर्ष के दौरान भारत में आने वाले विदेशी पर्यटकों की विशेषताओं को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598662.png\" alt=\"rId58\" width=\"241\" height=\"242\">&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598771.png\" alt=\"rId59\" width=\"250\" height=\"241\"> <br>यदि किसी दिए गए वर्ष में 2,50,000 पर्यटक भारत आए और डेटा का आयु-वार वितरण सभी देशों पर लागू होता है, तो वर्ष के दौरान भारत आने वाले रूसी पर्यटकों की संख्या, जो 50 वर्ष से अधिक आयु वर्ग में थे, है।</p>",
                    options_en: ["<p>375</p>", "<p>385</p>", 
                                "<p>3,750</p>", "<p>3,850</p>"],
                    options_hi: ["<p>375</p>", "<p>385</p>",
                                "<p>3,750</p>", "<p>3,850</p>"],
                    solution_en: "<p>57.(c)<br>Total number of tourist from Russia who visited india = <math display=\"inline\"><mfrac><mrow><mn>250000</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 15 = 37500<br>Russian tourist whose age above 50 = 37500 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 3750</p>",
                    solution_hi: "<p>57.(c)<br>रूस से भारत आने वाले पर्यटकों की कुल संख्या = <math display=\"inline\"><mfrac><mrow><mn>250000</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 15 = 37500<br>रूसी पर्यटक जिसकी आयु 50 से अधिक है = 37500 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 3750</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A chord of the larger among two concentric circles is of length 20 cm and it is tangent to the smaller circle. What is the area (in cm<sup>2</sup>) of the annular portion between the two circles?</p>",
                    question_hi: "<p>58. दो संकेंद्रित वृतों में से बड़े वृत कि एक जीवा कि लंबाई 20 cm है और यह छोटे वृत कि स्पर्शरेखा है दोनों वृतों के बीच के वलयाकार भाग का क्षेत्रफल (cm<sup>2</sup> में) कितना है ?</p>",
                    options_en: ["<p>100<math display=\"inline\"><mi>&#960;</mi></math></p>", "<p>164<math display=\"inline\"><mi>&#960;</mi></math></p>", 
                                "<p>122<math display=\"inline\"><mi>&#960;</mi></math></p>", "<p>175<math display=\"inline\"><mi>&#960;</mi></math></p>"],
                    options_hi: ["<p>100<math display=\"inline\"><mi>&#960;</mi></math></p>", "<p>164<math display=\"inline\"><mi>&#960;</mi></math></p>",
                                "<p>122<math display=\"inline\"><mi>&#960;</mi></math></p>", "<p>175<math display=\"inline\"><mi>&#960;</mi></math></p>"],
                    solution_en: "<p>58.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598969.png\" alt=\"rId60\" width=\"200\"><br>In the right angled &nbsp;&Delta;OCA<br><math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = (10)<sup>2</sup> + r<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 100<br>Area of annular portion = <math display=\"inline\"><mi>&#960;</mi></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>R</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup></math>) = 100&pi;</p>",
                    solution_hi: "<p>58.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697598969.png\" alt=\"rId60\" width=\"200\"><br>समकोण &Delta;OCA में<br><math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = (10)<sup>2</sup> + r<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 100<br>वलयाकार भाग का क्षेत्रफल = <math display=\"inline\"><mi>&#960;</mi></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>R</mi><mn>2</mn></msup><mo>-</mo><msup><mi>r</mi><mn>2</mn></msup></math>) = 100&pi;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Renson has three pieces of wood of length 91 m, 112 m and 49 m. If he wants to cut the wood into equal planks, what will be the maximum possible length (in m) of each plank ?</p>",
                    question_hi: "<p>59. रेन्सन के पास 91 m, 112 m और 49 m लंबाई के तीन लकड़ी के टुकड़े हैं। यदि वह लकड़ी को बराबर तख्तों में काटना चाहता है, तो प्रत्येक तख्ते की अधिकतम संभव लंबाई (m में) कितनी होगी ?</p>",
                    options_en: ["<p>7</p>", "<p>3</p>", 
                                "<p>21</p>", "<p>19</p>"],
                    options_hi: ["<p>7</p>", "<p>3</p>",
                                "<p>21</p>", "<p>19</p>"],
                    solution_en: "<p>59.(a) maximum possible length = H.C.F.(91 , 112, 49) = 7</p>",
                    solution_hi: "<p>59.(a) तख्ते की अधिकतम संभव लंबाई = H.C.F.(91 , 112, 49) = 7</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Solve the following:<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow></mfrac></math> = ?</p>",
                    question_hi: "<p>60. निम्&zwj;न को हल कीजिए।<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow></mfrac></math> = ?</p>",
                    options_en: ["<p>2cosec<sup>2</sup>&theta;</p>", "<p>2sin<sup>2</sup>&theta;</p>", 
                                "<p>2sec<sup>2</sup>&theta;</p>", "<p>2tan<sup>2</sup>&theta;</p>"],
                    options_hi: ["<p>2cosec<sup>2</sup>&theta;</p>", "<p>2sin<sup>2</sup>&theta;</p>",
                                "<p>2sec<sup>2</sup>&theta;</p>", "<p>2tan<sup>2</sup>&theta;</p>"],
                    solution_en: "<p>60.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>+</mo><mi>cos&#952;</mi><mo>&#160;</mo></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math>= 2 cosec<sup>2</sup>&theta;</p>",
                    solution_hi: "<p>60.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>1</mn><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>-</mo><mo>&#160;</mo><mi>cos&#952;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>1</mn><mo>+</mo><mi>cos&#952;</mi><mo>&#160;</mo></mrow><mrow><mn>1</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math>= 2 cosec<sup>2</sup>&theta;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A fruit seller has 10 kg of apples and 5 kg of grapes. The price of 1 kg of apples is ₹94.50 and that of grapes is ₹105. He sold all the fruits with him. What is the average amount (in ₹) received by the fruit seller ?</p>",
                    question_hi: "<p>61. एक फल विक्रेता के पास 10 kg सेब और 5 kg अंगूर हैं। 1 kg सेब की कीमत ₹94.50 और 1 kg अंगूर की कीमत ₹105 है। उसने अपने सारे फल बेच दिए। फल विक्रेता को प्राप्त औसत राशि (₹ में) ज्ञात करें।</p>",
                    options_en: ["<p>97.50</p>", "<p>101.50</p>", 
                                "<p>98</p>", "<p>100</p>"],
                    options_hi: ["<p>97.50</p>", "<p>101.50</p>",
                                "<p>98</p>", "<p>100</p>"],
                    solution_en: "<p>61.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Apples&nbsp; &nbsp; &nbsp; &nbsp;Grapes<br>No of fruits -&nbsp; &nbsp; &nbsp; &nbsp;10&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; =&nbsp; 2 : 1<br>Price /kg -&nbsp; &nbsp; &nbsp; &nbsp; 94.50&nbsp; :&nbsp; &nbsp; 105<br>Average amount received by the fruit seller = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>94</mn><mo>.</mo><mn>50</mn><mo>+</mo><mn>1</mn><mo>&#215;</mo><mn>105</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>189</mn><mo>+</mo><mn>105</mn></mrow><mn>3</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>294</mn><mn>3</mn></mfrac></math> = ₹98</p>",
                    solution_hi: "<p>61.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; सेब&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; अंगूर<br>फलों की संख्या-&nbsp; &nbsp; 10&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 5&nbsp; &nbsp; = 2 : 1<br>मूल्य/किग्रा-&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;94.50 :&nbsp; &nbsp;105<br>फल विक्रेता द्वारा प्राप्त औसत राशि = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>94</mn><mo>.</mo><mn>50</mn><mo>+</mo><mn>1</mn><mo>&#215;</mo><mn>105</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>189</mn><mo>+</mo><mn>105</mn></mrow><mn>3</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>294</mn><mn>3</mn></mfrac></math> = ₹98</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Two athletes X and Y are jogging on a circular path whose circumference is 500 m. Athlete X jogs at a speed of 18 km/h and Y jog at 9 km/h, jogging in opposite directions. The time at which they will meet for the first time is:</p>",
                    question_hi: "<p>62. दो एथलीट X और Y एक वृत्ताकार पथ पर दौड़ रहे हैं जिसकी परिधि 500 m है। एथलीट X, 18 km/h की चाल से दौड़ता है और Y विपरीत दिशा में 9 km/h की चाल से दौड़ता है। कितने समय में वे पहली बार मिलेंगे?</p>",
                    options_en: ["<p>66.66 s</p>", "<p>50 s</p>", 
                                "<p>200 s</p>", "<p>27.77 s</p>"],
                    options_hi: ["<p>66.66 s</p>", "<p>50 s</p>",
                                "<p>200 s</p>", "<p>27.77 s</p>"],
                    solution_en: "<p>62.(a) Time taken to meet again for the first time = <math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mo>(</mo><mn>18</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>9</mn><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn></mrow><mrow><mn>5</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow></mfrac></math> = 66.66sec</p>",
                    solution_hi: "<p>62.(a) पहली बार दोबारा मिलने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mo>(</mo><mn>18</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>9</mn><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>500</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>2</mn></mrow><mrow><mn>5</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>3</mn></mrow></mfrac></math> = 66.66 सेकंड</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. The interest accrued on a sum of ₹16,000 lent at a simple interest rate of 14% p.a. for the time in those many years, which gives a simple interest of three-fifths of a certain sum at 10% p.a., is:</p>",
                    question_hi: "<p>63. उन कई वर्षों में 14% की साधारण ब्याज की दर पर उधार दी गई ₹16,000 की राशि पर अर्जित ब्याज कितना होगा, जितने वर्षों में 10% पर एक निश्चित राशि का <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> भाग साधारण ब्याज के रूप में प्राप्त होता है?`</p>",
                    options_en: ["<p>₹13,440</p>", "<p>₹12,970</p>", 
                                "<p>₹15,360</p>", "<p>₹14,280</p>"],
                    options_hi: ["<p>₹13,440</p>", "<p>₹12,970</p>",
                                "<p>₹15,360</p>", "<p>₹14,280</p>"],
                    solution_en: "<p>63.(a) Le<math display=\"inline\"><mi>t</mi></math> the time be t and rate = 10% <br>According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> Simple interest = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; principal<br>Then, Simple interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>principal</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>Rate</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>Time</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>100</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; principal =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>principal</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>10</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> t = 6<br>Now, Simple interest = <math display=\"inline\"><mfrac><mrow><mn>1600</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>14</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 13,440</p>",
                    solution_hi: "<p>63.(a) माना समय t&nbsp;है और दर = 10% <br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> साधारण ब्याज = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; मूलधन<br>तो, साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>100</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> &times; मूलधन =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>10</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> t = 6<br>अब, साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>1600</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>14</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹13,440</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. The height of Aaryan was 120 cm. After 1 year,his height increased by 15%, and in the next 1 year, his height increased by 16%. What will be the height (in cm) of Aaryan after the second years?</p>",
                    question_hi: "<p>64. आर्यन की लम्बाई 120 cm थी। 1 वर्ष के बाद, उसकी लम्बाई 15% बढ़ जाती है, और अगले 1 वर्ष में, उसकी लम्बाई 16% बढ़ जाती है। दूसरे वर्ष के अंत में आर्यन की लम्बाई (cm में) क्या होगी?</p>",
                    options_en: ["<p>161.08</p>", "<p>150.56</p>", 
                                "<p>160.08</p>", "<p>138</p>"],
                    options_hi: ["<p>161.08</p>", "<p>150.56</p>",
                                "<p>160.08</p>", "<p>138</p>"],
                    solution_en: "<p>64.(c)<br>Height of Aaryan after 2 year = 120 <math display=\"inline\"><mo>&#215;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>100</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>116</mn><mn>100</mn></mfrac></math> = 160.08 cm</p>",
                    solution_hi: "<p>64.(c)<br>2 वर्ष बाद आर्यन की ऊंचाई = 120 <math display=\"inline\"><mo>&#215;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>100</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>116</mn><mn>100</mn></mfrac></math> = 160.08 cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. In a company, the ratio of men and women is 5 : 3, respectively. When 150 additional women are employed in the company, the ratio becomes 5 : 4. How many men are employed in the company?</p>",
                    question_hi: "<p>65. एक कंपनी में, पुरुषों और महिलाओं का अनुपात क्रमशः 5 : 3 है। जब कंपनी में 150 अतिरिक्त महिलाओं को नियोजित किया जाता है, तो अनुपात 5 : 4 हो जाता है। कंपनी में कितने पुरुष कार्यरत हैं?</p>",
                    options_en: ["<p>850</p>", "<p>750</p>", 
                                "<p>550</p>", "<p>950</p>"],
                    options_hi: ["<p>850</p>", "<p>750</p>",
                                "<p>550</p>", "<p>950</p>"],
                    solution_en: "<p>65.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697599138.png\" alt=\"rId61\" width=\"248\" height=\"120\"><br>According to question <br><math display=\"inline\"><mo>&#8658;</mo></math>1 unit = 150<br>No of men employee in company (5 unit ) = 5 &times; 150 = 750</p>",
                    solution_hi: "<p>65.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697599228.png\" alt=\"rId62\" width=\"236\" height=\"119\"><br>प्रश्न के अनुसार <br><math display=\"inline\"><mo>&#8658;</mo></math> 1 इकाई = 150<br>कंपनी में पुरुष कर्मचारियों की संख्या (5 इकाई) = 5 &times; 150 = 750</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. The value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mo>)</mo></mrow></mfrac></math> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>)</mo></mrow></mfrac></math>&nbsp;is</p>",
                    question_hi: "<p>66. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mo>)</mo></mrow></mfrac></math> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>)</mo></mrow></mfrac></math>&nbsp;का मान ज्ञात करें।</p>",
                    options_en: ["<p>&minus;450000</p>", "<p>45000</p>", 
                                "<p>450000</p>", "<p>&minus;45000</p>"],
                    options_hi: ["<p>&minus;450000</p>", "<p>45000</p>",
                                "<p>450000</p>", "<p>&minus;45000</p>"],
                    solution_en: "<p>66.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mo>)</mo></mrow></mfrac></math> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>)</mo></mrow></mfrac></math><br>&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><msup><mo>)</mo><mn>3</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>725</mn><msup><mo>)</mo><mn>3</mn></msup></mrow><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>275</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>275</mn><mo>)</mo></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>275</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>725</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>)</mo></mrow></mfrac></math><br>&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi><mo>[</mo><mo>(</mo><mn>275</mn><msup><mo>)</mo><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>725</mn><msup><mo>)</mo><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>275</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn></mrow><mrow><mo>(</mo><mn>275</mn><msup><mo>)</mo><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>725</mn><msup><mo>)</mo><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>275</mn><mo>)</mo></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>725</mn><mo>)</mo><mo>&#160;</mo><mo>[</mo><msup><mrow><mo>(</mo><mn>275</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>725</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn></mrow><mrow><msup><mrow><mo>(</mo><mn>275</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>725</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>)</mo></mrow></mfrac></math><br>&nbsp;= (275 + 725 ) <math display=\"inline\"><mo>&#215;</mo></math> (275 - 725)<br>= 1000&nbsp;<math display=\"inline\"><mo>&#215;</mo></math> (-450) <br>= - 4,50,000</p>",
                    solution_hi: "<p>66.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold\">&#160;</mi><mn>725</mn><mi mathvariant=\"bold\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold\">&#160;</mi><mn>275</mn><mo>)</mo></mrow></mfrac></math> &times;&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>)</mo></mrow></mfrac></math><br>&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><msup><mo>)</mo><mn>3</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>725</mn><msup><mo>)</mo><mn>3</mn></msup></mrow><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>275</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>275</mn><mo>)</mo></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>275</mn><mo>)</mo></mrow><mn>3</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>725</mn><mo>)</mo></mrow><mn>3</mn></msup></mrow><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>)</mo></mrow></mfrac></math><br>&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mo>)</mo><mi mathvariant=\"normal\">&#160;</mi><mo>[</mo><mo>(</mo><mn>275</mn><msup><mo>)</mo><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>725</mn><msup><mo>)</mo><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>275</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn></mrow><mrow><mo>(</mo><mn>275</mn><msup><mo>)</mo><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>725</mn><msup><mo>)</mo><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>725</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>275</mn><mo>)</mo></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>275</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>725</mn><mo>)</mo><mo>&#160;</mo><mo>[</mo><msup><mrow><mo>(</mo><mn>275</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>725</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>275</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>725</mn></mrow><mrow><msup><mrow><mo>(</mo><mn>275</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>725</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><mn>725</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>275</mn><mo>)</mo></mrow></mfrac></math><br>&nbsp;= (275 + 725 ) <math display=\"inline\"><mo>&#215;</mo></math> (275 - 725)<br>= 1000&nbsp;<math display=\"inline\"><mo>&#215;</mo></math> (-450) <br>= - 4,50,000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The distance between the centres of two circles of radii 22 cm and 10 cm is 37 cm. If the points of contact of a direct common tangent to these circles are M and Q, then find the length of the line segment MQ .</p>",
                    question_hi: "<p>67. 22 cm और 10 cm त्रिज्या वाले दो वृत्तों के केंद्रों के बीच की दूरी 37 cm है। यदि इन वृत्तों की सीधी उभयनिष्&zwj;ठ अनुस्पर्श रेखा के स्पर्श बिंदु (points of contact) M और Q हैं, तो रेखाखंड MQ की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>35 cm</p>", "<p>39 cm</p>", 
                                "<p>29 cm</p>", "<p>25 cm</p>"],
                    options_hi: ["<p>35 cm</p>", "<p>39 cm</p>",
                                "<p>29 cm</p>", "<p>25 cm</p>"],
                    solution_en: "<p>67.(a) <br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697599325.png\" alt=\"rId63\" width=\"288\" height=\"142\"><br>Length of the common tangent(MQ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>distance</mi><mi mathvariant=\"normal\">&#160;</mi><mi>between</mi><mi mathvariant=\"normal\">&#160;</mi><mi>centre</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>two</mi><mi mathvariant=\"normal\">&#160;</mi><mi>circle</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>MQ = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>22</mn><mi>&#160;</mi><mo>-</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>MQ = <math display=\"inline\"><msqrt><mn>1369</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>144</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1225</mn></msqrt></math> = 35 cm</p>",
                    solution_hi: "<p>67.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697599325.png\" alt=\"rId63\" width=\"288\" height=\"142\"><br>उभयनिष्ठ स्पर्शरेखा की लंबाई(MQ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>&#2342;&#2379;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2357;&#2371;&#2340;&#2381;&#2340;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2375;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2375;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2348;&#2368;&#2330;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2370;&#2352;&#2368;</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>MQ = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>22</mn><mi>&#160;</mi><mo>-</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>37</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>12</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>MQ = <math display=\"inline\"><msqrt><mn>1369</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>144</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>1225</mn></msqrt></math> = 35 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. The value of sin<sup>2</sup>15&deg; + sin<sup>2</sup>25&deg; + sin<sup>2</sup>35&deg; + sin<sup>2</sup>45&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>65&deg;+ sin<sup>2</sup>75&deg; is:</p>",
                    question_hi: "<p>68. sin<sup>2</sup>15&deg; + sin<sup>2</sup>25&deg; + sin<sup>2</sup>35&deg; + sin<sup>2</sup>45&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>65&deg;+ sin<sup>2</sup>75&deg; का मान ज्ञात कीजिए</p>",
                    options_en: ["<p>4</p>", "<p>7</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>4</p>", "<p>7</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>68.(c)<br>sin<sup>2</sup>15&deg; + sin<sup>2</sup>25&deg; + sin<sup>2</sup>35&deg; + sin<sup>2</sup>45&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>65&deg;+ sin<sup>2</sup>75&deg;<br>= sin<sup>2</sup>(90&deg; - 75&deg;) + sin<sup>2</sup>(90&deg; - 65&deg;) + sin<sup>2</sup>(90&deg; - 55&deg;) + sin<sup>2</sup>45&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>65&deg;+ sin<sup>2</sup>75&deg;<br>= cos<sup>2</sup> 75&deg; + cos<sup>2</sup> 65&deg; + cos<sup>2</sup> 55&deg; + sin<sup>2</sup>45&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>65&deg;+ sin<sup>2</sup>75&deg;<br>= cos<sup>2</sup> 75&deg; + sin<sup>2</sup>75&deg; + cos<sup>2</sup>65&deg; + sin<sup>2</sup>65&deg; + cos<sup>2</sup>55&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>45&deg;<br>= 1 + 1 + 1 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>= 3 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>68.(c)<br>sin<sup>2</sup>15&deg; + sin<sup>2</sup>25&deg; + sin<sup>2</sup>35&deg; + sin<sup>2</sup>45&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>65&deg;+ sin<sup>2</sup>75&deg;<br>= sin<sup>2</sup>(90&deg; - 75&deg;) + sin<sup>2</sup>(90&deg; - 65&deg;) + sin<sup>2</sup>(90&deg; - 55&deg;) + sin<sup>2</sup>45&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>65&deg;+ sin<sup>2</sup>75&deg;<br>= cos<sup>2</sup> 75&deg; + cos<sup>2</sup> 65&deg; + cos<sup>2</sup> 55&deg; + sin<sup>2</sup>45&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>65&deg;+ sin<sup>2</sup>75&deg;<br>= cos<sup>2</sup> 75&deg; + sin<sup>2</sup>75&deg; + cos<sup>2</sup>65&deg; + sin<sup>2</sup>65&deg; + cos<sup>2</sup>55&deg; + sin<sup>2</sup>55&deg; + sin<sup>2</sup>45&deg;<br>= 1 + 1 + 1 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>= 3 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>2</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A vendor sells his goods using weights 19% less than true weights and makes a profit of 35%. His net gain percentage (rounded off to 2 decimal places) is:</p>",
                    question_hi: "<p>69. एक विक्रेता वास्तविक भार वाले बाट से 19% कम भार के बाट का उपयोग करके अपना माल बेचता है और 35% का लाभ अर्जित करता है। उसका निवल लाभ प्रतिशत (दशमलव के 2 स्थानों तक सन्निकटित) क्या है?</p>",
                    options_en: ["<p>81.33%</p>", "<p>66.33%</p>", 
                                "<p>81.67%</p>", "<p>66.67%</p>"],
                    options_hi: ["<p>81.33%</p>", "<p>66.33%</p>",
                                "<p>81.67%</p>", "<p>66.67%</p>"],
                    solution_en: "<p>69.(d) 35% =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;Initial :&nbsp; Final<br>By weight <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 81&nbsp; &nbsp;:&nbsp; 100<br>By price <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 20&nbsp; &nbsp; :&nbsp; 27<br>-------------------------------------------<br>Final <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp;1620&nbsp; : 2700<br>Profit% = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>2700</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1620</mn><mo>)</mo></mrow><mrow><mn>1620</mn></mrow></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1080</mn><mn>1620</mn></mfrac></math> &times; 100&nbsp;= 66.67%</p>",
                    solution_hi: "<p>69.(d) 35 % = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>अनुपात -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;आरंभिक : अंतिम<br>वज़न के हिसाब से <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 81&nbsp; &nbsp; :&nbsp; &nbsp;100<br>कीमत के हिसाब से <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;20&nbsp; &nbsp;:&nbsp; &nbsp; 27<br>----------------------------------------------------<br>अंतिम <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;1620 : 2700<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>2700</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1620</mn><mo>)</mo></mrow><mrow><mn>1620</mn></mrow></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1080</mn><mn>1620</mn></mfrac></math> &times; 100 = 66.67%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Mitu can do a piece of work in 12 hours, Titu and Situ together in 2 hours, and Mitu and Situ together in 3 hours. How long will Titu alone take to do it?</p>",
                    question_hi: "<p>70. मीतू एक काम को 12 घंटे में पूरा कर सकती है, इसी काम को टीटू और सीटू एक साथ मिलकर 2 घंटे में कर सकते हैं, तथा मीतू और सीटू एक साथ मिलकर 3 घंटे में कर सकते हैं। टीटू को अकेले इस काम को पूरा करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>7 hours</p>", "<p>8 hours</p>", 
                                "<p>4 hours</p>", "<p>6 hours</p>"],
                    options_hi: ["<p>7 घंटे</p>", "<p>8 घंटे</p>",
                                "<p>4 घंटे</p>", "<p>6 घंटे</p>"],
                    solution_en: "<p>70.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697599431.png\" alt=\"rId64\" width=\"276\" height=\"179\"><br>Efficiency of Situ = 4 - 1 = 3 unit<br>Efficiency of Titu = 6 - 3 = 3 unit<br>Time taken by Titu to complete the work = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 4 hours</p>",
                    solution_hi: "<p>70.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744697599535.png\" alt=\"rId65\" width=\"306\" height=\"189\"><br>सीटू की क्षमता = 4 - 1 = 3 इकाई<br>टीटू की क्षमता = 6 - 3 = 3 इकाई<br>टीटू द्वारा कार्य पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 4 घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. &lsquo;x&lsquo; varies directly with the cube of &lsquo;y&rsquo; and inversely with the square of &lsquo;z&rsquo;. If x = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math> when y = 2 and z = 3, then what is the value of 800x when y = 3 and z = 5 ?</p>",
                    question_hi: "<p>71. x\', \'y\' के घन के अनुक्रमानुपाती और \'z\' के वर्ग के व्युत्क्रमानुपाती है। यदि y = 2 और z = 3 होने पर x = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math> है, तो y = 3 और z = 5 होने पर 800x का मान क्या होगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>800</mn></mrow></mfrac></math></p>", "<p>27</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p>9</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>800</mn></mrow></mfrac></math></p>", "<p>27</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>800</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p>9</p>"],
                    solution_en: "<p>71.(b)<br><math display=\"inline\"><mi>x</mi></math> &alpha;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math> = k &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>2</mn><mn>3</mn></msup><msup><mn>3</mn><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mi>k</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mrow><mn>36</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math><br>Now,<br>800<math display=\"inline\"><mi>x</mi></math> when y = 3 and z = 5<br><math display=\"inline\"><mi>x</mi></math> &alpha;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mi>x</mi></math> = k &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>3</mn><mn>3</mn></msup><msup><mn>5</mn><mn>2</mn></msup></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>25</mn></mfrac></math><br>800<math display=\"inline\"><mi>x</mi></math> = 800 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>25</mn></mfrac></math> = 27</p>",
                    solution_hi: "<p>71.(b)<br><math display=\"inline\"><mi>x</mi></math> &alpha;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>36</mn></mrow></mfrac></math> = k &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>2</mn><mn>3</mn></msup><msup><mn>3</mn><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mi>k</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mrow><mn>36</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math><br>अब,<br>800<math display=\"inline\"><mi>x</mi></math> जब y = 3 और z = 5<br><math display=\"inline\"><mi>x</mi></math> &alpha;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mn>3</mn></msup><msup><mi mathvariant=\"normal\">z</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mi>x</mi></math> = k &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>3</mn><mn>3</mn></msup><msup><mn>5</mn><mn>2</mn></msup></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>25</mn></mfrac></math><br>800<math display=\"inline\"><mi>x</mi></math> = 800 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>32</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>25</mn></mfrac></math> = 27</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Two trains, A and B started travelling towards each other at the same time, from places P to Q and Q to P, respectively. After crossing each other, A and B took 9 hours and 16 hours to reach Q and P, respectively. If the speed of A was 56 km/h, then what was the speed (in km/h) of B?</p>",
                    question_hi: "<p>72. दो रेलगाड़ियाँ A और B क्रमशः स्थान P से Q और स्थान Q से P की ओर एक ही समय पर एक दूसरे की ओर चलना शुरू करती हैं। एक दूसरे को पार करने के बाद, रेलगाड़ी A और B को क्रमशः स्थान Q और P तक पहुँचने के लिए 9 घंटे और 16 घंटे लगते हैं। यदि रेलगाड़ी A की चाल 56 km/h थी, तो रेलगाड़ी B की चाल (km/h में) क्या थी?</p>",
                    options_en: ["<p>40</p>", "<p>46</p>", 
                                "<p>42</p>", "<p>38</p>"],
                    options_hi: ["<p>40</p>", "<p>46</p>",
                                "<p>42</p>", "<p>38</p>"],
                    solution_en: "<p>72.(c)<br>We know that,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">S</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">S</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><msub><mi mathvariant=\"normal\">T</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">T</mi><mn>1</mn></msub></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><msub><mi mathvariant=\"normal\">S</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>16</mn><mn>9</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> S<sub>2</sub> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>56</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math> = 42 km/hr</p>",
                    solution_hi: "<p>72.(c)<br>हम जानते है कि, <br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">S</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">S</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><msub><mi mathvariant=\"normal\">T</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">T</mi><mn>1</mn></msub></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>56</mn><msub><mi mathvariant=\"normal\">S</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>16</mn><mn>9</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> S<sub>2</sub> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>56</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>4</mn></mfrac></math> = 42 km/hr</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. At an election involving two candidates, 68 votes are declared invalid. The winning candidate gets 52% of the valid votes and wins by 98 votes. The total number of votes polled is:</p>",
                    question_hi: "<p>73. दो उम्मीदवारों के चुनाव में 68 मत अवैध घोषित किए गए। जीतने वाले उम्मीदवार को वैध मतों का 52% प्राप्त होता है और वह 98 मतों से जीत जाता है। डाले गए मतों की कुल संख्या है:</p>",
                    options_en: ["<p>2518</p>", "<p>2475</p>", 
                                "<p>2500</p>", "<p>2250</p>"],
                    options_hi: ["<p>2518</p>", "<p>2475</p>",
                                "<p>2500</p>", "<p>2250</p>"],
                    solution_en: "<p>73.(a)<br>Let the total number of votes = 100x<br>Valid votes = 100x - 68<br>According to the question,<br>(100x - 68) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>100</mn></mfrac></math> = 98<br>100x&nbsp;= (98 &times; 25) + 68 = 2518</p>",
                    solution_hi: "<p>73.(a)<br>माना कि मतो की कुल संख्या = 100<math display=\"inline\"><mi>x</mi></math><br>वैध मतो की संख्या = 100x - 68<br>प्रश्न के अनुसार,<br>(100x - 68) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>100</mn></mfrac></math> = 98<br>100x = (98 &times; 25) + 68 = 2518</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The marked price of an item was ₹12,500. Two successive discounts of 12% each was offered on its sale. What was the selling price (in ₹) of the item ?</p>",
                    question_hi: "<p>74. एक वस्तु का अंकित मूल्य ₹ 12,500 था। इसकी बिक्री पर 12-12% की दो क्रमागत छूट प्रदान की गई थी। वस्तु का विक्रय मूल्य (₹ में) क्या था ?</p>",
                    options_en: ["<p>9680</p>", "<p>9500</p>", 
                                "<p>9720</p>", "<p>9620</p>"],
                    options_hi: ["<p>9680</p>", "<p>9500</p>",
                                "<p>9720</p>", "<p>9620</p>"],
                    solution_en: "<p>74.(a)<br>SP of the item = 12500 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = ₹9680</p>",
                    solution_hi: "<p>74.(a)<br>वस्तु का विक्रय मूल्य = 12500 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> = ₹9680</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. If the mean of a data is 65 and its mode is 23 then the median using empirical formula is:</p>",
                    question_hi: "<p>75. यदि किन्हीं आंकड़ों का माध्य 65 है, और उनका बहुलक 23 है, तो मूलानुपाती सूत्र का उपयोग करके, उनकी माध्यिका ज्ञात कीजिए ।</p>",
                    options_en: ["<p>53</p>", "<p>54</p>", 
                                "<p>51</p>", "<p>52</p>"],
                    options_hi: ["<p>53</p>", "<p>54</p>",
                                "<p>51</p>", "<p>52</p>"],
                    solution_en: "<p>75.(c)<br>Mode = 3 Median - 2 Mean<br><math display=\"inline\"><mo>&#8658;</mo></math>23 = 3 Median - 2 &times; 65<br><math display=\"inline\"><mo>&#8658;</mo></math>3 Median = 23 + 130<br><math display=\"inline\"><mo>&#8658;</mo></math>3Median = 153<br><math display=\"inline\"><mo>&#8658;</mo></math> Median = 51</p>",
                    solution_hi: "<p>75.(c)<br>बहुलक = 3 माध्यिका - 2 माध्य<br><math display=\"inline\"><mo>&#8658;</mo></math>23 = 3 माध्यिका - 2 &times; 65<br><math display=\"inline\"><mo>&#8658;</mo></math>3 माध्यिका = 23 + 130<br><math display=\"inline\"><mo>&#8658;</mo></math>3माध्यिका = 153<br><math display=\"inline\"><mo>&#8658;</mo></math> माध्यिका = 51</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>When I saw the dress, I knew it was exactly what I had looked for.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the grammatical error.<br>When I saw the dress, I knew it was exactly what I had looked for.</p>",
                    options_en: ["<p>When I saw the dress</p>", "<p>I knew it was exactly</p>", 
                                "<p>what I had looked for</p>", "<p>No error</p>"],
                    options_hi: ["<p>When I saw the dress</p>", "<p>I knew it was exactly</p>",
                                "<p>what I had looked for</p>", "<p>No error</p>"],
                    solution_en: "<p>76.(c) what I had looked for<br>We will use the Past Perfect Continuous Tense(Had been + V<sub>-ing</sub>) in the later part of the given sentence to show that something started in the past and continued up until another time in the&nbsp;past. Hence, &lsquo;what I had been looking for&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(c) what I had looked for<br>दिए गए वाक्य के बाद के भाग में हम Past Perfect Continuous Tense(Had been V-<sub>ing</sub>) का प्रयोग यह&nbsp;दर्शाने के लिए करते हैं कि past में कुछ शुरू हुआ और past में किसी अन्य समय तक जारी रहा।&nbsp;इसलिए, &lsquo;what I had been looking for&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>He and his friend are <span style=\"text-decoration: underline;\"><strong>sailing in the same</strong></span> boat.</p>",
                    question_hi: "<p>77. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>He and his friend are <span style=\"text-decoration: underline;\"><strong>sailing in the same</strong></span> boat.</p>",
                    options_en: ["<p>sailing together in the same boat</p>", "<p>sharing the financial and social condition</p>", 
                                "<p>being in the same difficult situation</p>", "<p>getting rid of the difficult situation</p>"],
                    options_hi: ["<p>sailing together in the same boat</p>", "<p>sharing the financial and social condition</p>",
                                "<p>being in the same difficult situation</p>", "<p>getting rid of the difficult situation</p>"],
                    solution_en: "<p>77.(c) Sailing in the same boat- being in the same difficult situation.<br>Eg- During corona most labourers were sailing in the same boat.</p>",
                    solution_hi: "<p>77.(c) Sailing in the same boat - उसी कठिन परिस्थिति में होना । <br>उदाहरण - During corona most labourers were sailing in the same boat.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the segment in the sentence, which contains the grammatical error.<br>The number of applications has risen this year by as many as 50%.</p>",
                    question_hi: "<p>78. Identify the segment in the sentence, which contains the grammatical error.<br>The number of applications has risen this year by as many as 50%.</p>",
                    options_en: ["<p>The number of applications has risen</p>", "<p>this year by</p>", 
                                "<p>as many as 50%</p>", "<p>No error</p>"],
                    options_hi: ["<p>The number of applications has risen</p>", "<p>this year by</p>",
                                "<p>as many as 50%</p>", "<p>No error</p>"],
                    solution_en: "<p>78.(c) as many as 50%<br>We will replace &lsquo;many&rsquo; with &lsquo;much&rsquo; in the given sentence because we generally use \"much\" for uncountable nouns. Hence, &lsquo;as much as 50%&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(c) as many as 50%<br>हम दिए गए वाक्य में &lsquo;many&rsquo; के स्थान पर &lsquo;much&rsquo; का प्रयोग करेंगे क्योंकि हम आम तौर पर uncountable&nbsp;nouns के लिए \"much\" का प्रयोग करते हैं। इसलिए, &lsquo;as much as 50%&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate synonym of the given word.<br>Envoy</p>",
                    question_hi: "<p>79. Select the most appropriate synonym of the given word.<br>Envoy</p>",
                    options_en: ["<p>Carrier</p>", "<p>Chief</p>", 
                                "<p>Ambassador</p>", "<p>Receiver</p>"],
                    options_hi: ["<p>Carrier</p>", "<p>Chief</p>",
                                "<p>Ambassador</p>", "<p>Receiver</p>"],
                    solution_en: "<p>79.(c) <strong>Ambassador-</strong> an official representative of a country sent to a foreign nation.<br><strong>Envoy-</strong> a messenger or representative, especially one on a diplomatic mission.<br><strong>Carrier-</strong> someone or something that carries or delivers items.<br><strong>Chief-</strong> the leader or person in charge.<br><strong>Receiver-</strong> a person who receives something.</p>",
                    solution_hi: "<p>79.(c) <strong>Ambassador</strong> (राजदूत)- an official representative of a country sent to a foreign nation.<br><strong>Envoy</strong> (प्रतिनिधि)- a messenger or representative, especially one on a diplomatic mission.<br><strong>Carrier</strong> (वाहक)- someone or something that carries or delivers items.<br><strong>Chief</strong> (प्रमुख/नेता)- the leader or person in charge.<br><strong>Receiver</strong> (प्राप्तकर्ता )- a person who receives something.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>My <span style=\"text-decoration: underline;\"><strong>opinion of</strong></span> the play is that it will win the national award.</p>",
                    question_hi: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>My <span style=\"text-decoration: underline;\"><strong>opinion of</strong></span> the play is that it will win the national award.</p>",
                    options_en: ["<p>opinion to</p>", "<p>opinion about</p>", 
                                "<p>opinion on</p>", "<p>No improvement</p>"],
                    options_hi: ["<p>opinion to</p>", "<p>opinion about</p>",
                                "<p>opinion on</p>", "<p>No improvement</p>"],
                    solution_en: "<p>80.(b) opinion about.<br>This is a phrasal verb which means what one thinks about something. <br>Eg- What is your opinion about the new law?</p>",
                    solution_hi: "<p>80.(b) opinion about.<br>Opinion about phrasal verb है जिसका अर्थ है कि कोई, किसी के बारे में क्या सोचता है। <br>जैसे- What is your opinion about the new law?/नए कानून के बारे में आपकी क्या राय है?</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;I must go next week&rdquo;.</p>",
                    question_hi: "<p>81. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;I must go next week&rdquo;.</p>",
                    options_en: ["<p>He said he would had to go the following week.</p>", "<p>He told that he would have to go the next week.</p>", 
                                "<p>He said that he would have to go the following week.</p>", "<p>He said that he will have to go the following week.</p>"],
                    options_hi: ["<p>He said he would had to go the following week.</p>", "<p>He told that he would have to go the next week.</p>",
                                "<p>He said that he would have to go the following week.</p>", "<p>He said that he will have to go the following week.</p>"],
                    solution_en: "<p>81.(c) He said that he would have to go the following week.<br>(a) He said he <strong>would had to go</strong> the following week. (With &ldquo;would&rdquo; had is not used)<br>(b) He told that he would have to go the <strong>next week.</strong> (Incorrect word)<br>(d) He said that he <strong>will have to go</strong> the following week. (Incorrect change of tense)</p>",
                    solution_hi: "<p>81.(c) He said that he would have to go the following week.<br>(a) He said he <strong>would had to go</strong> the following week. ( &ldquo;Would&rdquo; के साथ had का प्रयोग नहीं किया जाता है)<br>(b) He told that he would have to go the <strong>next week.</strong> (गलत शब्द का प्रयोग)<br>(d) He said that he <strong>will have to go</strong> the following week. (Tense का गलत परिवर्तन)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>Who gave you ice cream?</p>",
                    question_hi: "<p>82. Choose the most appropriate option to change the voice (active/passive) form of the given sentence.<br>Who gave you ice cream?</p>",
                    options_en: ["<p>Who has given you ice cream?</p>", "<p>By whom were you given ice cream?</p>", 
                                "<p>By whom was you given ice cream?</p>", "<p>Who had given you ice cream?</p>"],
                    options_hi: ["<p>Who has given you ice cream?</p>", "<p>By whom were you given ice cream?</p>",
                                "<p>By whom was you given ice cream?</p>", "<p>Who had given you ice cream?</p>"],
                    solution_en: "<p>82.(b) By whom were you given ice-cream ?<br>(a). Who <strong>has given</strong> you ice-cream? (Tense has changed)<br>(c). By whom <strong>was</strong> you given ice-cream ? (Incorrect helping verb)<br>(d). Who <strong>had given</strong> you ice-cream? (Tense has changed)</p>",
                    solution_hi: "<p>82.(b) By whom were you given ice-cream ?<br>(a) Who <strong>has given</strong> you ice-cream? (Tense बदल गया है)<br>(c) By whom <strong>was</strong> you given ice-cream ? (गलत helping verb)<br>(d) Who <strong>had given</strong> you ice-cream? (Tense बदल गया है)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the correctly spelt word :</p>",
                    question_hi: "<p>83. Select the correctly spelt word :</p>",
                    options_en: ["<p>ingreedient</p>", "<p>ingridient</p>", 
                                "<p>ingredeint</p>", "<p>ingredient</p>"],
                    options_hi: ["<p>ingreedient</p>", "<p>ingridient</p>",
                                "<p>ingredeint</p>", "<p>ingredient</p>"],
                    solution_en: "<p>83.(d) ingredient</p>",
                    solution_hi: "<p>83.(d) ingredient<br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>We are reaching the end of the syllabus.</p>",
                    question_hi: "<p>84. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>We are reaching the end of the syllabus.</p>",
                    options_en: ["<p>The syllabus is ended by us.</p>", "<p>The end of the syllabus is being reached by us.</p>", 
                                "<p>This is our end to the syllabus.</p>", "<p>The syllabus has reached its end by us.</p>"],
                    options_hi: ["<p>The syllabus is ended by us.</p>", "<p>The end of the syllabus is being reached by us.</p>",
                                "<p>This is our end to the syllabus.</p>", "<p>The syllabus has reached its end by us.</p>"],
                    solution_en: "<p>84.(b) <strong>The end of the syllabus is being reached by us.</strong> <br>(a) The syllabus is ended by us. (Meaning of sentence changed)<br>(c) This is our end to the syllabus. (Meaning of sentence changed)<br>(d) The syllabus <strong>has reached</strong> its end by us. (Tense has changed)</p>",
                    solution_hi: "<p>84.(b) <strong>The end of the syllabus is being reached by us.</strong> <br>(a) The syllabus is ended by us. (Sentence का अर्थ बदल गया)<br>(c) This is our end to the syllabus. (Sentence का अर्थ बदल गया)<br>(d) The syllabus <strong>has reached</strong> its end by us. (Tense बदल गया )</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Given below are four jumbled sentences. Select the option that gives their correct logical sequence.<br>A. It contains high levels of Vitamin A and D.<br>B. Cod liver oil is thus used to treat arthritis.<br>C. This supplement is made from the liver of cod fish.<br>D. Cod liver oil is a nutritional supplement.</p>",
                    question_hi: "<p>85. Given below are four jumbled sentences. Select the option that gives their correct logical sequence.<br>A. It contains high levels of Vitamin A and D.<br>B. Cod liver oil is thus used to treat arthritis.<br>C. This supplement is made from the liver of cod fish.<br>D. Cod liver oil is a nutritional supplement.</p>",
                    options_en: ["<p>ABCD</p>", "<p>CBAD</p>", 
                                "<p>DCAB</p>", "<p>BACD</p>"],
                    options_hi: ["<p>ABCD</p>", "<p>CBAD</p>",
                                "<p>DCAB</p>", "<p>BACD</p>"],
                    solution_en: "<p>85.(c) <strong>DCAB</strong><br>Sentence D will be the starting line as it introduces the main idea of the parajumble i.e. &lsquo;Cod liver oil is a nutritional supplement.&rsquo; And, Sentence C states that this supplement is made from the liver of cod fish. So, C will follow D. Further, Sentence A states that it contains high levels of Vitamin A and D &amp; Sentence B states that Cod liver oil is used to treat arthritis. So, B will follow A. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>85.(c) <strong>DCAB</strong><br>Sentence D प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार &lsquo;Cod liver oil is a nutritional supplement&rsquo; को प्रस्तुत करता है। और, Sentence C बताता है कि यह supplement, cod fish के liver से बनाया जाता है। इसलिए, D के बाद C आएगा। इसके अलावा, Sentence A बताता है कि इसमें Vitamin A और D का उच्च स्तर होता है और Sentence B बताता है कि Cod liver oil का उपयोग arthritis के इलाज के लिए किया जाता है। इसलिए, A के बाद B आएगा। अतः options के माध्यम से जाने पर, option &lsquo;c&rsquo; में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate ANTONYM of the underlined word.<br>Would you please <span style=\"text-decoration: underline;\">support</span> me for once in your life?</p>",
                    question_hi: "<p>86. Select the most appropriate ANTONYM of the underlined word.<br>Would you please support me for once in your life?</p>",
                    options_en: ["<p>document</p>", "<p>refute</p>", 
                                "<p>establish</p>", "<p>disclaim</p>"],
                    options_hi: ["<p>document</p>", "<p>refute</p>",
                                "<p>establish</p>", "<p>disclaim</p>"],
                    solution_en: "<p>86.(b) <strong>Refute-</strong> to prove wrong by argument or evidence.<br><strong>Support-</strong> to help or encourage somebody.<br><strong>Document-</strong> a piece of written, printed, or electronic matter that provides information.<br><strong>Establish-</strong> set up on a firm or permanent basis.<br><strong>Disclaim-</strong> to deny or reject.</p>",
                    solution_hi: "<p>86.(b) <strong>Refute</strong> (खंडन) - to prove wrong by argument or evidence.<br><strong>Support</strong> (समर्थन) - to help or encourage somebody.<br><strong>Document</strong> (दस्तावेज) - a piece of written, printed, or electronic matter that provides information.<br><strong>Establish</strong> (स्थापित) - set up on a firm or permanent basis.<br><strong>Disclaim</strong> (अस्वीकार) - to deny or reject.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>Some shots were fired <span style=\"text-decoration: underline;\">at random</span>.</p>",
                    question_hi: "<p>87. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>Some shots were fired <span style=\"text-decoration: underline;\">at random</span>.</p>",
                    options_en: ["<p>without any aim</p>", "<p>for a long time</p>", 
                                "<p>to end quarrel</p>", "<p>thoroughly</p>"],
                    options_hi: ["<p>without any aim</p>", "<p>for a long time</p>",
                                "<p>to end quarrel</p>", "<p>thoroughly</p>"],
                    solution_en: "<p>87.(a) without any aim<br>Example- The man shot at random and failed to hit the target.</p>",
                    solution_hi: "<p>87.(a) without any aim /बिना किसी उद्देश्य के<br>उदाहरण - The man shot at random and failed to hit the target./ आदमी ने बिना किसी लक्ष्य के गोली मारी और लक्ष्य भेदने में विफल रहा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>During the Miss World contest in London, it was\' a difficult moment to decide whether Miss India or Miss Greece <span style=\"text-decoration: underline;\"><strong>is the most beautiful</strong></span>.</p>",
                    question_hi: "<p>88. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>During the Miss World contest in London, it was\' a difficult moment to decide whether Miss India or Miss Greece <span style=\"text-decoration: underline;\"><strong>is the most beautiful</strong></span>.</p>",
                    options_en: ["<p>was more beautiful</p>", "<p>was the most beautiful</p>", 
                                "<p>is more beautiful</p>", "<p>No improvement</p>"],
                    options_hi: ["<p>was more beautiful</p>", "<p>was the most beautiful</p>",
                                "<p>is more beautiful</p>", "<p>No improvement</p>"],
                    solution_en: "<p>88.(b) was the most beautiful<br>Past tense should be used.</p>",
                    solution_hi: "<p>88.(b) was the most beautiful<br>Past tense का प्रयोग करना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. In the following question, out of the four alternatives choose the one which can be substituted for the given words/ sentence.<br>Words or lines written on the tomb of a person</p>",
                    question_hi: "<p>89. In the following question, out of the four alternatives choose the one which can be substituted for the given words/ sentence.<br>Words or lines written on the tomb of a person</p>",
                    options_en: ["<p>Epitaph</p>", "<p>Anecdote</p>", 
                                "<p>Calligraphy</p>", "<p>Cenotaph</p>"],
                    options_hi: ["<p>Epitaph</p>", "<p>Anecdote</p>",
                                "<p>Calligraphy</p>", "<p>Cenotaph</p>"],
                    solution_en: "<p>89.(a) <strong>Epitaph</strong> <br><strong>Epitaph</strong> - a phrase or form of words written in memory of a person who has died, especially as an inscription on a tombstone.<br><strong>Anecdote</strong> - a short amusing or interesting story about a real incident or person.<br><strong>Calligraphy</strong> - decorative handwriting or handwritten lettering.<br><strong>Cenotaph</strong> - a monument to someone buried elsewhere, especially one commemorating people who died in a war.</p>",
                    solution_hi: "<p>89.(a) <strong>Epitaph</strong> <br><strong>Epitaph</strong> - विशेष रूप से एक मकबरे पर एक शिलालेख के रूप में लिखा गया वाक्यांश है।<br><strong>Anecdote</strong> - किसी वास्तविक घटना या व्यक्ति के बारे में एक छोटी मनोरंजक या दिलचस्प कहानी।<br><strong>Calligraphy</strong> - सजावटी लिखावट या हस्तलिखित अक्षर।<br><strong>Cenotaph</strong> - किसी को कहीं और दफन करने के लिए एक स्मारक, विशेष रूप से एक युद्ध में मारे गए लोगों को याद करते हुए</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Choose the one which can be substituted for the given words/ sentence : <br>A person who has had one or more limbs removed.</p>",
                    question_hi: "<p>90. Choose the one which can be substituted for the given words/ sentence : <br>A person who has had one or more limbs removed.</p>",
                    options_en: ["<p>limber</p>", "<p>amputee</p>", 
                                "<p>fatalist</p>", "<p>handicap</p>"],
                    options_hi: ["<p>limber</p>", "<p>amputee</p>",
                                "<p>fatalist</p>", "<p>handicap</p>"],
                    solution_en: "<p>90.(b) <strong>Amputee-</strong> one who had to get a limb removed<br><strong>Limber-</strong> flexible.<br><strong>Fatalist-</strong> one who accepts everything as inevitable.<br><strong>Handicap-</strong> a disadvantage.</p>",
                    solution_hi: "<p>90.(b) <strong>Amputee-</strong> जिसका एक अंग हटाना पड़ा । <br><strong>Limber-</strong> लचीला।<br><strong>Fatalist-</strong> भाग्यवादी।<br><strong>Handicap-</strong> विकलांगता ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement.<br>O: population is expected to triple in<br>P: the next 50 years<br>Q: among the world&rsquo;s poorest countries<br>R: population shrinkage in that period<br>S: whereas many rich countries will see</p>",
                    question_hi: "<p>91. In the given question, a statement divided into different segments is given. Rearrange all the segments to form a coherent statement.<br>O: population is expected to triple in<br>P: the next 50 years<br>Q: among the world&rsquo;s poorest countries<br>R: population shrinkage in that period<br>S: whereas many rich countries will see</p>",
                    options_en: ["<p>POSQR</p>", "<p>RSOPQ</p>", 
                                "<p>SPORQ</p>", "<p>QOPSR</p>"],
                    options_hi: ["<p>POSQR</p>", "<p>RSOPQ</p>",
                                "<p>SPORQ</p>", "<p>QOPSR</p>"],
                    solution_en: "<p>91.(d) <strong>QOPSR</strong><br>The given sentence starts with Part Q as it introduces the main idea of the sentence, i.e. among the world&rsquo;s poorest countries. Part O states that population is expected to triple &amp; Part P states the period in which population will triple. So, P will follow O. Further, Part S talks about many rich countries &amp; Part R talks about population shrinkage in rich countries. So, R will follow S. Going through the options, option &lsquo;d&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>91.(d) <strong>QOPSR</strong><br>दिया गया sentence, Part Q से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार among the world&rsquo;s poorest countries को प्रस्तुत करता है। Part O बताता है कि जनसंख्या के तीन गुना होने की उम्मीद है और Part P बताता है कि जनसंख्या किस अवधि में तीन गुना हो जाएगी। इसलिए, O के बाद P आएगा। इसके अलावा, Part S अनेक rich countries के बारे में बात करता है और Part R , rich countries में जनसंख्या में कमी के बारे में बात करता है। इसलिए, S के बाद R आएगा। अतः options के माध्यम से जाने पर, option &lsquo;d&rsquo; में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "92.  Select the most appropriate synonym of the given word.<br />Stunning",
                    question_hi: "92.  Select the most appropriate synonym of the given word.<br />Stunning",
                    options_en: [" Ugly ", " Hideous ", 
                                " Gorgeous ", " Plain"],
                    options_hi: [" Ugly ", " Hideous ",
                                " Gorgeous ", " Plain"],
                    solution_en: "<p>92.(c) <strong>Gorgeous-</strong> very beautiful or attractive.<br><strong>Stunning-</strong> extremely impressive or beautiful.<br><strong>Ugly-</strong> very unattractive.<br><strong>Hideous-</strong> extremely frightening.<br><strong>Plain-</strong> something that is not attractive.</p>",
                    solution_hi: "<p>92.(c) <strong>Gorgeous</strong> (भव्य/शानदार) - very beautiful or attractive.<br><strong>Stunning</strong> (अद्भुत) - extremely impressive or beautiful.<br><strong>Ugly</strong> (कुरूप) - very unattractive.<br><strong>Hideous</strong> (भयावह) - extremely frightening.<br><strong>Plain</strong> (साफ-सुथरा) - something that is not attractive.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate article to fill in the blank.<br>_________ bed you sleep in is broken.</p>",
                    question_hi: "<p>93. Select the most appropriate article to fill in the blank.<br>_________ bed you sleep in is broken.</p>",
                    options_en: ["<p>On</p>", "<p>The</p>", 
                                "<p>An</p>", "<p>A</p>"],
                    options_hi: ["<p>On</p>", "<p>The</p>",
                                "<p>An</p>", "<p>A</p>"],
                    solution_en: "<p>93.(b) The<br>&lsquo;Bed&rsquo; mentioned in the given sentence is specific and we generally use the definite article &lsquo;the&rsquo; before any specific or particular noun. Hence, &lsquo;the bed&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(b) The<br>दिए गए sentence में उल्लिखित (mentioned) &lsquo;Bed&rsquo; specific है। और हम सामान्यतः किसी specific या particular noun से पहले definite article &lsquo;the&rsquo; का प्रयोग करते हैं। अतः, &lsquo;the bed&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94.Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>How can you be so <span style=\"text-decoration: underline;\">joyless</span> on hearing the news?</p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>How can you be so <span style=\"text-decoration: underline;\">joyless</span> on hearing the news?</p>",
                    options_en: ["<p>Blissful</p>", "<p>Rapt</p>", 
                                "<p>Dubious</p>", "<p>Beaming</p>"],
                    options_hi: ["<p>Blissful</p>", "<p>Rapt</p>",
                                "<p>Dubious</p>", "<p>Beaming</p>"],
                    solution_en: "<p>94.(a) <strong>Blissful</strong> - extremely happy.<br><strong>Joyless</strong> - unhappy.<br><strong>Rapt</strong> - completely focused on something.<br><strong>Dubious</strong> - doubtful.<br><strong>Beaming</strong> - smiling broadly.</p>",
                    solution_hi: "<p>94.(a) <strong>Blissful</strong> (परम आनंद) - extremely happy.<br><strong>Joyless</strong> (आनंद रहित) - unhappy.<br><strong>Rapt</strong> (लीन/मगन) - completely focused on something.<br><strong>Dubious</strong> (संदिग्ध) - doubtful.<br><strong>Beaming</strong> (मुस्कराना) - smiling broadly.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "95. Select the most appropriate option to fill in the blank. <br />Serena _________ from fever since the 5th of this month. ",
                    question_hi: "95. Select the most appropriate option to fill in the blank. <br />Serena _________ from fever since the 5th of this month. ",
                    options_en: [" suffers ", " suffered  ", 
                                " has been suffering", " is suffering "],
                    options_hi: [" suffers ", " suffered  ",
                                " has been suffering", " is suffering <br />             "],
                    solution_en: "<p>95.(c) has been suffering<br>Present perfect continuous tense is used when period of time is mentioned using the preposition &lsquo;since/for&rsquo;. &lsquo;Singular Sub. + has been + V<sub>ing</sub>&rsquo; is the correct structure for it. Hence, &lsquo;has been suffering&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(c) has been suffering<br>Present perfect continuous tense का प्रयोग तब किया जाता है जब समयावधि (period of time) का उल्लेख preposition &lsquo;since/for&rsquo; के साथ किया जाता है। &lsquo;Singular Sub. + has been + V<sub>ing</sub>&rsquo; इसके लिए सही structure है। अतः, &lsquo;has been suffering&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-&nbsp;</strong><br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-</strong>&nbsp;<br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: ["<p>surprised</p>", "<p>worried</p>", 
                                "<p>anxious</p>", "<p>shocked</p>"],
                    options_hi: ["<p>surprised</p>", "<p>worried</p>",
                                "<p>anxious</p>", "<p>shocked</p>"],
                    solution_en: "<p>96.(d) shocked<br>&lsquo;Shocked&rsquo; means to cause an unpleasant feeling of surprise in somebody. The given passage states that Ramu came to Somu&rsquo;s house and was shocked(surprised) to see him with a bandage on his left leg. Hence, &lsquo;shocked&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) shocked<br>\'Shocked\' का अर्थ है चकित। दिए गए अंश में कहा गया है कि रामू सोमू के घर आया और उसे अपने बाएं पैर में पट्टी बांधे देखकर चौंक गया (आश्चर्यचकित)। इसलिए, \'shocked\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-</strong> <br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:-</strong> <br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: ["<p>happen</p>", "<p>happened</p>", 
                                "<p>happening</p>", "<p>happens</p>"],
                    options_hi: ["<p>happen</p>", "<p>happened</p>",
                                "<p>happening</p>", "<p>happens</p>"],
                    solution_en: "<p>97.(b) happened<br>&lsquo;Happened&rsquo; means to take place, usually without being planned first. The given passage states that he asked Somu what had happened to him. Hence, &lsquo;happened&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) <strong>happened</strong><br>\'Happened\' का अर्थ होता है घटित होना। दिए गए अंश में कहा गया है कि उसने सोमू से पूछा कि उसके साथ क्या हुआ था। इसलिए, \'happened\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-&nbsp;</strong><br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-&nbsp;</strong><br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: ["<p>retiring</p>", "<p>retreating</p>", 
                                "<p>returning</p>", "<p>restoring</p>"],
                    options_hi: ["<p>retiring</p>", "<p>retreating</p>",
                                "<p>returning</p>", "<p>restoring</p>"],
                    solution_en: "<p>98.(c) returning. <br>&lsquo;Returning&rsquo; means to come or go back to a place. The given passage states that &lsquo;he met with an accident the day before when he was coming back(returning) home&rsquo;. Hence, &lsquo;returning&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(c) returning. <br>\'Returning\' का अर्थ है किसी स्थान पर आना या वापस जाना। दिए गए passage में कहा गया है कि &lsquo;वह एक दिन पहले दुर्घटना का शिकार हुआ जब वह घर वापस आ रहा था (returning)&rsquo;। इसलिए, \'returning\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-</strong> <br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong><br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: ["<p>returned</p>", "<p>reported</p>", 
                                "<p>recorde</p>", "<p>replied</p>"],
                    options_hi: ["<p>returned</p>", "<p>reported</p>",
                                "<p>recorde</p>", "<p>replied</p>"],
                    solution_en: "<p>99.(d) replied. <br>&lsquo;Replied&rsquo; means to say, write or do something as an answer to somebody/something.<br>The given passage states that ,Somu replied that a car had hit him,. Hence, &lsquo;replied&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) replied. <br>\'Replied\' का अर्थ है किसी को उत्तर के रूप में कुछ कहना, लिखना या करना। दिए गए passage में कहा गया है कि &lsquo;सोमू ने उत्तर दिया कि एक कार ने उसे टक्कर मारी थी&rsquo;। इसलिए, \'replied\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-</strong> <br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-</strong> <br>Ramu came to Somu&rsquo;s house and was (96)____to see him with a bandage on his left leg. He asked Somu what had (97)____to him. Then Somu told him that he met with an accident the day before when he was (98)____home from the playground on his cycle near the Woodlands Cinema Theatre. Ramu enquired how it all happened. Somu (99)____that a car had hit him from (100)____ and that he had lost his consciousness immediately.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: ["<p>back</p>", "<p>arrears</p>", 
                                "<p>behind</p>", "<p>hindsight</p>"],
                    options_hi: ["<p>back</p>", "<p>arrears</p>",
                                "<p>behind</p>", "<p>hindsight</p>"],
                    solution_en: "<p>100.(c) behind<br>&lsquo;Behind&rsquo; means in, at, or to the back of somebody/something. The given passage states that &lsquo;Somu replied that a car had hit him from behind&rsquo;. Hence, &lsquo;behind&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) behind<br>\'Behind\' का अर्थ है, किसी के पास, या किसी के पीछे। दिए गए passage में कहा गया है कि&rsquo; सोमू ने उत्तर दिया कि एक कार ने उसे पीछे से टक्कर मारी थी&rsquo;। इसलिए, &lsquo;behind&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>