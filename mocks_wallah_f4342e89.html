<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. Deepika Pallikal is associated with_____.</p>",
                    question_hi: "<p>1. दीपिका पल्लीकल ______से जुड़ी हैं।</p>",
                    options_en: ["<p>squash</p>", "<p>billiards</p>", 
                                "<p>chess</p>", "<p>cricket</p>"],
                    options_hi: ["<p>स्क्वेश</p>", "<p>बिलियर्ड्स</p>",
                                "<p>शतरंज</p>", "<p>क्रिकेट</p>"],
                    solution_en: "<p>1.(a) <strong>Squash.</strong> She is the first Indian to break into the top 10 in the Professional Squash Association Women\'s rankings. In 2011, she won three WISPA tour titles to attain a career-best ranking of 13th. The <strong>World Squash Federation</strong> (WSF) - The global governing body for the sport of squash; Headquarters - Hastings (UK); Establishment - 1967.</p>",
                    solution_hi: "<p>1.(a) <strong>स्क्वेश ।</strong> वह प्रोफेशनल स्क्वेश एसोसिएशन महिला रैंकिंग में शीर्ष 10 में जगह बनाने वाली प्रथम भारतीय हैं। 2011 में, उन्होंने करियर की सर्वश्रेष्ठ 13वीं रैंकिंग हासिल करने के लिए तीन WISPA टूर खिताब जीती । <strong>वर्ल्ड स्क्वैश फेडरेशन</strong> (WSF) - स्क्वेश के खेल के लिए वैश्विक शासी निकाय; मुख्यालय - हेस्टिंग्स (UK); स्थापना - 1967 ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. In a certain code language , SCHOOL is coded as the number 72, What number will FLOWER be coded as in that language ?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में, SCHOOL को संख्या 72 के रूप में कोडित किया जाता है, उस भाषा में FLOWER को किस संख्या में कोडित किया जाएगा?</p>",
                    options_en: ["<p>54</p>", "<p>71</p>", 
                                "<p>79</p>", "<p>89</p>"],
                    options_hi: ["<p>54</p>", "<p>71</p>",
                                "<p>79</p>", "<p>89</p>"],
                    solution_en: "<p>2.(c) <br>Addition of alphabetic place value .<br>S + C + H + O + O + L = 72<br>Similarly FLOWER = 79</p>",
                    solution_hi: "<p>2.(c) वर्णमाला के स्थानीय मान का जोड़।<br>S + C + H + O + O + L = 72<br>इसी प्रकार FLOWER = 79</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. Hymns of Rigveda are also known as _______.</p>",
                    question_hi: "<p>3. ऋग्वेद के स्तोत्रों (Hymns) को_______ के रूप में भी जाना जाता है।</p>",
                    options_en: ["<p>Sukta</p>", "<p>Knowledge</p>", 
                                "<p>Rudra</p>", "<p>Rituals</p>"],
                    options_hi: ["<p>सूक्त</p>", "<p>ज्ञान</p>",
                                "<p>रुद्र</p>", "<p>कर्मकांड</p>"],
                    solution_en: "<p>3.(a) <strong>Suktas</strong> (Group of mantra). <strong>Rig Veda</strong> (Book Of Mantras) - An ancient Indian collection of Vedic Sanskrit hymns, the oldest known Vedic Sanskrit text. The text is layered, consisting of the Samhita, Brahmanas, Aranyakas and Upanishads. The Rigveda Samhita is the core text and is a collection of 10 books (mandalas) with 1,028 hymns (suktas) in about 10,600 verses.</p>",
                    solution_hi: "<p>3.(a) <strong>सूक्त </strong>(मंत्रों का समूह)। <strong>ऋग्वेद</strong> (मंत्रों की पुस्तक) - वैदिक संस्कृत भजनों (सूक्तों) का एक प्राचीन भारतीय संग्रह, सबसे पुराना ज्ञात वैदिक संस्कृत पाठ है, जिसमें संहिता, ब्राह्मण, आरण्यक और उपनिषद शामिल हैं। ऋग्वेद संहिता मुख्य पाठ है और लगभग 10,600 छंदों में 1,028 भजनों (सूक्तों) के साथ 10 पुस्तकों (मंडलों) का संग्रह है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. 0.<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>23</mn><mo>&#175;</mo></mover></math>&nbsp;is :</p>",
                    question_hi: "<p>4. 0.<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>23</mn><mo>&#175;</mo></mover></math>&nbsp;______ है |</p>",
                    options_en: ["<p>a prime number</p>", "<p>a composite number</p>", 
                                "<p>a rational number</p>", "<p>an irrational number</p>"],
                    options_hi: ["<p>एक अभाज्य संख्या</p>", "<p>भाज्य संख्या</p>",
                                "<p>एक परिमेय संख्या</p>", "<p>एक अपरिमेय संख्या</p>"],
                    solution_en: "<p>4.(c) 0.<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>23</mn><mo>&#175;</mo></mover></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>99</mn></mfrac></math> &rArr; It is a rational <br>number because it can be written in the fraction of two integers(<math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>Q</mi></mrow></mfrac></math>)</p>",
                    solution_hi: "<p>4.(c) 0.<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>23</mn><mo>&#175;</mo></mover></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>99</mn></mfrac></math><br>&rArr; यह एक परिमेय संख्या है क्योंकि इसे दो पूर्णांकों के भिन्न (<math display=\"inline\"><mfrac><mrow><mi>P</mi></mrow><mrow><mi>Q</mi></mrow></mfrac></math>) के रूप में लिखा जा सकता है</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. In a class of 46 students, 18 play carom; 17 play chess, including 6 who play carom; 16 play cards, including 4 who play chess, but not carom; and 5 play only tennis. Which of the following figures represents these facts?</p>",
                    question_hi: "<p>5. 46 छात्रों की एक कक्षा में, 18 कैरम खेलते हैं; 17 शतरंज खेलते हैं, जिनमें 6 कैरम खेलते हैं; 16 ताश खेलते हैं, जिसमें 4 शतरंज खेलते हैं, लेकिन कैरम नहीं; और 5 केवल टेनिस खेलते हैं। निम्नलिखित में से कौन सी आकृति इन तथ्यों का प्रतिनिधित्व करती है?</p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451958322.png\" alt=\"rId4\" width=\"87\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451958538.png\" alt=\"rId5\" width=\"108\" height=\"78\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451958913.png\" alt=\"rId6\" width=\"78\" height=\"72\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451959166.png\" alt=\"rId7\" width=\"98\" height=\"58\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451958322.png\" alt=\"rId4\" width=\"87\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451958538.png\" alt=\"rId5\" width=\"100\" height=\"72\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451958913.png\" alt=\"rId6\" width=\"88\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451959166.png\" alt=\"rId7\" width=\"114\" height=\"68\"></p>"],
                    solution_en: "<p>5.(a) From the given information we can draw the following venn diagram :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451959420.png\" alt=\"rId8\" width=\"145\" height=\"85\"><br>So, the suitable venn diagram will be option (a) ;</p>",
                    solution_hi: "<p>5.(a) दी गई जानकारी से हम निम्नलिखित वेन आरेख बना सकते हैं:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451959692.png\" alt=\"rId9\" width=\"175\" height=\"101\"><br>इसलिए, उपयुक्त वेन आरेख विकल्प (a) होगा;</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. Name the first student satellite built by an Indian high school students team and launched by NASA.</p>",
                    question_hi: "<p>6.. भारतीय स्कूली छात्रों की टीम द्वारा निर्मित और NASA द्वारा लॉन्च किए गए पहले छात्र उपग्रह का नाम बताइए।</p>",
                    options_en: ["<p>Pratham</p>", "<p>Anusat</p>", 
                                "<p>Kalamsat</p>", "<p>SRMsat</p>"],
                    options_hi: ["<p>प्रथम</p>", "<p>अनुसैट</p>",
                                "<p>कलामसैट</p>", "<p>SRM-सैट</p>"],
                    solution_en: "<p>6.(c) <strong>Kalamsat -</strong> Named after the former Indian President and rocket scientist Dr. APJ Abdul Kalam. It was launched into space as part of a sub-orbital test flight mission by NASA.</p>",
                    solution_hi: "<p>6.(c) <strong>कलामसैट -</strong> इसका नाम पूर्व भारतीय राष्ट्रपति और रॉकेट वैज्ञानिक डॉ. एपीजे अब्दुल कलाम के नाम पर रखा गया है। इसे NASA द्वारा एक उप-कक्षीय परीक्षण उड़ान मिशन के हिस्से के रूप में अंतरिक्ष में लॉन्च किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. At 5% simple interest per annum, a certain sum yields a total amount of ₹ 2,790 at the end of 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> years. The sum invested was:</p>",
                    question_hi: "<p>7. एक धनराशि 5 % वार्षिक साधारण ब्याज पर 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> वर्ष के अंत में कुल ₹2,790 हो जातीहै। निवेश की गई धनराशि राशि कितनी थी?</p>",
                    options_en: ["<p>₹2,400</p>", "<p>₹2,350</p>", 
                                "<p>₹2,600</p>", "<p>₹2,550</p>"],
                    options_hi: ["<p>₹2,400</p>", "<p>₹2,350</p>",
                                "<p>₹2,600</p>", "<p>₹2,550</p>"],
                    solution_en: "<p>7.(a) Let the principal be 100%<br>SI on certain sum at 5% for <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> yrs <br>= <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 5% = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%&nbsp;<br>Amount = 100% + <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>465</mn><mn>4</mn></mfrac></math>%&nbsp;<br>ATQ, <math display=\"inline\"><mfrac><mrow><mn>465</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>% = ₹2,790<br>Then, 100% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2790</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>465</mn></mfrac></math> &times; 100 = ₹ 2400</p>",
                    solution_hi: "<p>7.(a) माना कि मूलधन 100% है<br><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> वर्षों के लिए निश्चित राशि पर 5% साधारण ब्याज</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 5% = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%&nbsp;<br>मिश्रधन = 100% + <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>465</mn><mn>4</mn></mfrac></math>%&nbsp;<br>प्रश्न के अनुसार, <math display=\"inline\"><mfrac><mrow><mn>465</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>% = ₹2,790<br>फिर, 100% =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2790</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>465</mn></mfrac></math> &times; 100 = ₹ 2400</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. Laho dance is related to which&nbsp;state of India?</p>",
                    question_hi: "<p>8. लाहो नृत्य का संबंध भारत के किस राज्य से है ?</p>",
                    options_en: ["<p>Andhra Pradesh</p>", "<p>Rajasthan</p>", 
                                "<p>Odisha</p>", "<p>Meghalaya</p>"],
                    options_hi: ["<p>आंध्र प्रदेश</p>", "<p>राजस्थान</p>",
                                "<p>ओडिशा</p>", "<p>मेघालय</p>"],
                    solution_en: "<p>8(d) <strong>Meghalaya.</strong> It is performed during the Behdienkhlam festival (both men and women). It is performed by the Jaintia (Pnar) tribe. Dance festivals of India: Meghalaya - Derogata, Shad Suk Mynsiem, Nongkrem. Andhra pradesh - Kuchipudi (classical), Vilasini Natyam, Andhra Natyam, Bhamakalapam, Veeranatyam, Dappu, Tappeta Gullu, Lambadi, Dhimsa, Kolattam, Butta Bommalu. Odisha - Odissi (classical), Savari, Ghumara, Painka, Munari, Chhau.</p>",
                    solution_hi: "<p>8.(d) <strong>मेघालय</strong>। यह बेहदीनखलम उत्सव (पुरुषों और महिलाओं दोनों) के दौरान किया जाता है। यह जैंतिया (पनार) जनजाति द्वारा किया जाता है। भारत के नृत्य उत्सव: मेघालय - डेरोगाटा, शाद सुक माइन्सिएम, नोंगक्रेम। आंध्र प्रदेश - कुचिपुड़ी (शास्त्रीय), विलासिनी नाट्यम, आंध्र नाट्यम, भामाकलापम, वीरनाट्यम, दप्पू, तप्पेटा गुल्लू, लंबाडी, ढिम्सा, कोलाट्टम, बुट्टा बोम्मलु। उड़ीसा- ओडिसी (शास्त्रीय), सावरी, घुमरा, पैंका, मुनारी, छऊ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. As per Article 172 (2)_______of a State shall NOT be subject to dissolution.</p>",
                    question_hi: "<p>9. अनुच्छेद 172 (2) के अनुसार किसी राज्य की _________ भंग नहीं होगी।</p>",
                    options_en: ["<p>Legislative Assembly</p>", "<p>Legislative Council</p>", 
                                "<p>House of People</p>", "<p>Council of States</p>"],
                    options_hi: ["<p>विधानसभा</p>", "<p>विधान-परिषद</p>",
                                "<p>लोकसभा</p>", "<p>राज्यसभा</p>"],
                    solution_en: "<p>9.(b)<strong> Legislative Council</strong> (Vidhan Parishad/Saasana Mandali) - It is the upper house in those states of India that have a bicameral state legislature. Six States have a Legislative Council: Andhra Pradesh, Telangana, Uttar Pradesh, Bihar, Maharashtra, Karnataka. The State Legislative Assembly (Vidhan Sabha) - A legislative body in the states and union territories of India. Council of States (Rajya Sabha). House of People (Lok Sabha).</p>",
                    solution_hi: "<p>9.(b) <strong>विधान परिषद</strong> (विधान परिषद/शासन मंडली) - यह भारत के उन राज्यों में उच्च सदन है जहाँ द्विसदनीय राज्य विधायिका है। छह राज्यों में विधान परिषद है: आंध्र प्रदेश, तेलंगाना, उत्तर प्रदेश, बिहार, महाराष्ट्र, कर्नाटक। राज्य विधान सभा - भारत के राज्यों और केंद्र शासित प्रदेशों में एक विधायी निकाय। राज्यों की परिषद (राज्यसभा)। लोगों का सदन (लोकसभा)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. Name the Governor-general under whom a new policy of &lsquo;paramountcy&rsquo; was initiated?</p>",
                    question_hi: "<p>10. उस गवर्नर-जनरल का नाम बताइए जिसके तहत \'सर्वोच्चता\' की एक नई नीति शुरू की गई थी?</p>",
                    options_en: ["<p>Lord Wellesley</p>", "<p>Lord Hasting</p>", 
                                "<p>Lord Cornwallis</p>", "<p>Lord Dalhousie</p>"],
                    options_hi: ["<p>लॉर्ड वैलेजली</p>", "<p>लॉर्ड हेस्टिंग</p>",
                                "<p>लॉर्ड कार्नवालिस</p>", "<p>लॉर्ड डलहौजी</p>"],
                    solution_en: "<p>10.(b) <strong>Lord Hasting</strong> (Governor -General of India from 1813 to 1823). The &lsquo;paramountcy&rsquo; policy asserted the Governor General&rsquo;s right to intervene in the internal affairs of the Indian princely states, and to supersede their authority if necessary.</p>",
                    solution_hi: "<p>10.(b) <strong>लॉर्ड हेस्टिंग</strong> (1813 से 1823 तक भारत के गवर्नर-जनरल)। \'सर्वोच्चता\' नीति ने गवर्नर जनरल को भारतीय रियासतों के आंतरिक मामलों में हस्तक्षेप करने और यदि आवश्यक हो तो उनके अधिकार को खत्म करने पर जोर दिया गया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. Determine the LCM of <br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math>,&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math>&nbsp;and <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>21</mn></mfrac></math>.</p>",
                    question_hi: "<p>11. निम्न का LCM ज्ञात कीजिये ?</p>\n<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math>,&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math> और <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>21</mn></mfrac></math>.</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>11.(d)<br>LCM of <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math>,&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math>&nbsp;and <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>21</mn></mfrac></math>.<br>=<math display=\"inline\"><mfrac><mrow><mi>L</mi><mi>C</mi><mi>M</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>2</mn><mo>,</mo><mi>&#160;</mi><mn>4</mn><mo>,</mo><mi>&#160;</mi><mn>8</mn><mo>,</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mi>H</mi><mi>C</mi><mi>F</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mn>3</mn><mo>,</mo><mi>&#160;</mi><mn>9</mn><mo>,</mo><mi>&#160;</mi><mn>15</mn><mo>,</mo><mi>&#160;</mi><mn>21</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>11.(d) <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math>,&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math> और <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>21</mn></mfrac></math> का LCM<br>= <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>,</mo><mi>&#160;</mi><mn>4</mn><mo>,</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>,</mo><mi>&#160;</mi><mn>10</mn><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>L</mi><mi>C</mi><mi>M</mi><mi>&#160;</mi></mrow><mrow><mn>3</mn><mo>,</mo><mn>9</mn><mo>,</mo><mn>15</mn><mo>,</mo><mn>21</mn><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>H</mi><mi>C</mi><mi>F</mi></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. Lanthanides are often called:</p>",
                    question_hi: "<p>12. लैंथेनाइड को प्राय: क्या कहा जाता है ?</p>",
                    options_en: ["<p>alkali metals</p>", "<p>rare earth elements</p>", 
                                "<p>inert gases</p>", "<p>d-block elements</p>"],
                    options_hi: ["<p>क्षार धातु</p>", "<p>दुर्लभ मृदा तत्व</p>",
                                "<p>अक्रिय गैसें</p>", "<p>डी-ब्लॉक तत्व</p>"],
                    solution_en: "<p>12.(b) <strong>rare earth elements</strong>. Lanthanides are the elements with atomic numbers from 57 to 71. Actinides are the 15 elements with atomic numbers from 89 to 103. Lanthanides and Actinides are located mostly in the \"f-block\" of the periodic table. The f-block, also known as inner transition element, consists of elements in which 4 f and 5 f orbitals are progressively filled. <strong>Inert gas (Noble gas)</strong> - Gas which does not undergo chemical reactions, Group 18 (or VIIIA) Inert gases:- Helium (He), neon (Ne), argon (Ar), krypton (Kr), xenon (Xe), and radon (Rn).</p>",
                    solution_hi: "<p>12.(b) <strong>दुर्लभ मृदा तत्व</strong> I लैंथेनाइड्स 57 से 71 तक परमाणु संख्या वाले तत्व हैं। एक्टिनाइड्स 89 से 103 तक परमाणु संख्या वाले 15 तत्व हैं। लैंथेनाइड्स और एक्टिनाइड्स ज्यादातर आवर्त सारणी के \"f-block\" में स्थित हैं। f-block, जिसे आंतरिक संक्रमण तत्व के रूप में भी जाना जाता है, में ऐसे तत्व होते हैं जिनमें 4 f और 5 f ऑर्बिटल्स उत्तरोत्तर भरे होते हैं। <strong>अक्रिय गैस (नोबल गैस) </strong>- गैस जो रासायनिक अभिक्रियाओं में भाग नहीं लेतीं , समूह 18 (या VIIIA) निष्क्रिय गैसें: - हीलियम (He), नियॉन (Ne), आर्गन (Ar), क्रिप्टन (Kr), क्सीनन (Xe), और रेडॉन (Rn)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. What will come next in the series?<br>12, 14, 31, 97, 393, ?</p>",
                    question_hi: "<p>13. श्रृंखला में आगे क्या आएगा?<br>12, 14, 31, 97, 393, ?</p>",
                    options_en: ["<p>1965</p>", "<p>1970</p>", 
                                "<p>1972</p>", "<p>1971</p>"],
                    options_hi: ["<p>1965</p>", "<p>1970</p>",
                                "<p>1972</p>", "<p>1971</p>"],
                    solution_en: "<p>13.(d) <br>12 &times; 1 + 2 = 14<br>14 &times; 2 + 3 = 31<br>31 &times; 3 + 4 = 97<br>97 &times; 4 + 5 = 393<br>393 &times; 5 + 6 = 1971<br>? = 1971</p>",
                    solution_hi: "<p>13.(d) <br>12 &times; 1 + 2 = 14<br>14 &times; 2 + 3 = 31<br>31 &times; 3 + 4 = 97<br>97 &times; 4 + 5 = 393<br>393 &times; 5 + 6 = 1971<br>? = 1971</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. Which of the following statements is NOT correct ?<br>1. There are only four single-digit prime numbers.<br>2. There are infinitely many prime numbers.<br>3. A prime number has only two factors.<br>4. All prime numbers are odd.</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन सा कथन सही नहीं है?<br>1. केवल चार एक-अंकीय अभाज्य संख्याएँ हैं।<br>2. अपरिमित रूप से अनेक अभाज्य संख्याएँ होती हैं।<br>3. एक अभाज्य संख्या के केवल दो गुणनखंड होते हैं।<br>4. सभी अभाज्य संख्याएँ विषम होती हैं।</p>",
                    options_en: ["<p>1</p>", "<p>4</p>", 
                                "<p>2</p>", "<p>3</p>"],
                    options_hi: ["<p>1</p>", "<p>4</p>",
                                "<p>2</p>", "<p>3</p>"],
                    solution_en: "<p>14.(b) All prime numbers are odd.</p>",
                    solution_hi: "<p>14.(b) सभी अभाज्य संख्याएँ विषम होती हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. In January 2023, Captain Shiva Chouhan was in the news for being the first women officer to be deployed at which battlefield ?</p>",
                    question_hi: "<p>15. जनवरी 2023 में, कैप्टन शिवा चौहान किस युद्धक्षेत्र में तैनात होने वाली पहली महिला अधिकारी होने के कारण चर्चा में थीं ?</p>",
                    options_en: ["<p>Kargil</p>", "<p>Leh</p>", 
                                "<p>Dras</p>", "<p>Siachen</p>"],
                    options_hi: ["<p>कारगिल</p>", "<p>लेह</p>",
                                "<p>द्रास</p>", "<p>सियाचिन</p>"],
                    solution_en: "<p>15.(d) <strong>Siachen.</strong> It is the highest battlefield in the world. First in India (Female) : Sarla Thakral - First female pilot in India. Surekha Yadav - First female train driver in India. Captain Lakshmi Sehgal - First female army officer in India. Bhawana Kanth - First female fighter pilot in India. Durba Banerjee - First female airline pilot in India. Ashapurna Devi - First woman to get Jnanpith award.</p>",
                    solution_hi: "<p>15.(d) <strong>सियाचिन।</strong> यह विश्व का सबसे ऊँचा युद्धक्षेत्र है। भारत में प्रथम (महिला) : सरला ठकराल - भारत की पहली महिला पायलट। सुरेखा यादव - भारत की पहली महिला ट्रेन ड्राइवर। कैप्टन लक्ष्मी सहगल - भारत की पहली महिला सेना अधिकारी। भावना कंठ - भारत की पहली महिला लड़ाकू पायलट। दुर्बा बनर्जी - भारत की पहली महिला एयरलाइन पायलट। आशापूर्णा देवी - ज्ञानपीठ पुरस्कार प्राप्त करने वाली पहली महिला।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. What will be the value of the given expression if 4 and 7 are interchanged and also \'&times;\' and ,\'&divide;\' are interchanged?<br>63 &times; 3 - 4 + 18 &divide; 7</p>",
                    question_hi: "<p>16. यदि 4 और 7 के स्थान के स्थान परस्पर बदल दिए जाएं और साथ ही \'&times;\' और \'&divide;\' के स्थान के स्थान परस्पर बदल दिए जाएं, तो दिए गए व्यंजक का मान कितना होगा?<br>63 &times; 3 - 4 + 18 &divide; 7</p>",
                    options_en: ["<p>56</p>", "<p>86</p>", 
                                "<p>48</p>", "<p>68</p>"],
                    options_hi: ["<p>56</p>", "<p>86</p>",
                                "<p>48</p>", "<p>68</p>"],
                    solution_en: "<p>16.(b) Given,<br>63 &times; 3 - 4 + 18 &divide; 7<br>According to question By interchanging (4 and 7) and (&times; and &divide;) we get,<br>63 &divide; 3 - 7 + 18 &times; 4 = 86 (ans)</p>",
                    solution_hi: "<p>16.(b) दिया गया है, 63 &times; 3 - 4 + 18 &divide; 7<br>प्रश्न के अनुसार (4 और 7) और (&times; और &divide;) को परस्पर बदलने पर,<br>63 &divide; 3 - 7 + 18 &times; 4 = 86 (उत्तर)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. &lsquo;Church&rsquo; is related to &lsquo;Christians&rsquo; in the same way &lsquo;Synagogue&rsquo; is related to ________</p>",
                    question_hi: "<p>17. &lsquo;चर्च\' का संबंध \'ईसाइयों\' से है, उस प्रकार \'स्यानागोगी\' का संबंध ________ से है।</p>",
                    options_en: ["<p>Muslims</p>", "<p>Jains</p>", 
                                "<p>Parsis</p>", "<p>Jews</p>"],
                    options_hi: ["<p>मुस्लिम</p>", "<p>जैन</p>",
                                "<p>पारसी</p>", "<p>यहूदी</p>"],
                    solution_en: "<p>17.(d) Church is the religious place for Christians. In the same way, Synagogue is the religious place for jews.</p>",
                    solution_hi: "<p>17.(d) ईसाइयों के लिए धार्मिक स्थान चर्च है। उसी प्रकार स्यानागोगी ​यहूदियों के लिए धार्मिक स्थान है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. Find the roots of the equation <br>6p<sup>2</sup> - 5p - 6 = 0.</p>",
                    question_hi: "<p>18. समीकरण 6p<sup>2</sup> - 5p - 6 = 0. के मूल ज्ञात कीजिए।</p>",
                    options_en: ["<p>- <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> and <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> and <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> and <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>-<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> and <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math></p>"],
                    options_hi: ["<p>- <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> और <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> और <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> और <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>-<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> और <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math></p>"],
                    solution_en: "<p>18.(a) 6p<sup>2</sup> - 5p - 6 = 0<br>&rArr; 6p<sup>2</sup> - 9p + 4p - 6 = 0<br>&rArr; 3p(2p - 3) + 2(2p - 3)<br>&rArr; (2p - 3)(3p + 2) = 0<br>&rArr; - <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> and <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>18.(a) 6p<sup>2</sup> - 5p - 6 = 0<br>&rArr; 6p<sup>2</sup> - 9p + 4p - 6 = 0<br>&rArr; 3p(2p - 3) + 2(2p - 3)<br>&rArr; (2p - 3)(3p + 2) = 0<br>&rArr; - <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> और <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. The land for the maintenance of a school mentioned in the Chola inscription was known as_____.</p>",
                    question_hi: "<p>19. चोल शिलालेख में उल्लिखित गुरुकुल के अनुरक्षण हेतु प्रदान की गई भूमि को ______ कहा जाता था।</p>",
                    options_en: ["<p>Vellanvagai</p>", "<p>Shalabhoga</p>", 
                                "<p>Brahmadeya</p>", "<p>Pallichchhandam</p>"],
                    options_hi: ["<p>वेल्लनवगाई (Vellanvagai)</p>", "<p>शालाभोग (Shalabhoga)</p>",
                                "<p>ब्रह्मदेय (Brahmadeya)</p>", "<p>पल्लिच्चंदम (Pallichchhandam)</p>"],
                    solution_en: "<p>19.(b) <strong>Shalabhoga</strong>. Vellanvagai - Land of non-Brahmana peasant proprietors. Brahmadeya - Land gift either in the form of a single plot or whole villages donated to Brahmanas. Pallichchhandam - Land donated to Jaina institutions. Devadana - Land gifted to temples.</p>",
                    solution_hi: "<p>19.(b) <strong>शालाभोग</strong> । वेल्लनवागई - गैर-ब्राह्मण किसान मालिकों की भूमि। ब्रह्मादेय - भूमि दान या तो एक भूखंड के रूप में या पूरे गाँव के रूप में ब्राह्मणों को दान दिया जाता है। पल्लिच्चंदम - जैन संस्थाओं को दान में दी गई भूमि। देवदान - मंदिरों को दान में दी गई भूमि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. In a row of 21 children facing North, Nitin is third to the right of Sita who is ninth from the right end of the row. What is Nitin\'s position from the left end?</p>",
                    question_hi: "<p>20. उत्तर की ओर मुंह करके खड़े 21 बच्चों की एक पंक्ति में, नितिन सीता के दाईं ओर तीसरे स्थान पर हैं, जो पंक्ति के दाईं छोर से नौवें स्थान पर हैं। बाई छोर से नितिन की स्थिति ज्ञात कीजिए</p>",
                    options_en: ["<p>16th</p>", "<p>15th</p>", 
                                "<p>12th</p>", "<p>6th</p>"],
                    options_hi: ["<p>16 वां</p>", "<p>15 वां</p>",
                                "<p>12 वां</p>", "<p>6 वां</p>"],
                    solution_en: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451959960.png\" alt=\"rId10\" width=\"217\" height=\"85\"><br>From the above diagram it is clearly seen that <strong>Nitin&rsquo;s</strong> position is 16<sup>th</sup> from the left end.</p>",
                    solution_hi: "<p>20.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451960168.png\" alt=\"rId11\" width=\"222\" height=\"87\"><br>उपरोक्त आरेख से यह स्पष्ट रूप से देखा जाता है <br>कि <strong>नितिन</strong> का स्थान बाएं छोर से <strong>16वें</strong> स्थान पर है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. Two trains start at the same time from two stations and proceed towards each other at 30 km/h and 35 km/h, respectively . When they meet, it is found that one train has covered 30 km more than the other. Find the distance between the two stations.</p>",
                    question_hi: "<p>21. दो ट्रेन , एक ही समय पर दो स्टेशनों से चलना शुरू करती हैं, और क्रमश: 30km/h और 35 km/h की चाल से एक-दूसरे की ओर बढ़ती हैं। जब वे मिलती हैं, तो पाया जाता है कि एक ट्रेन ने दूसरी ट्रेन से 30km अधिक दूरी तय की है। दोनों स्टेशनों के बीच की दूरी ज्ञात कीजिए।</p>",
                    options_en: ["<p>390 km</p>", "<p>400 km</p>", 
                                "<p>410 km</p>", "<p>380 km</p>"],
                    options_hi: ["<p>390 km</p>", "<p>400 km</p>",
                                "<p>410 km</p>", "<p>380 km</p>"],
                    solution_en: "<p>21.(a) Let the distance covered by Train 1 be x km<br>Now , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>30</mn></mrow><mn>35</mn></mfrac></math></p>\n<p>&rArr; 5x = 900&nbsp;</p>\n<p>&rArr;x = 180 km<br>Total distance = distance covered by train1 and train 2 = 180 + 180 + 30 <br>= 390 km</p>",
                    solution_hi: "<p>21.(a)<br>माना ट्रेन 1 द्वारा तय की गई दूरी x किमी है,<br>Now, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>30</mn></mrow><mn>35</mn></mfrac></math></p>\n<p>&rArr; 5x = 900&nbsp;</p>\n<p>&rArr; x = 180 km</p>\n<p>कुल दूरी = ट्रेन 1 और ट्रेन 2 द्वारा तय की गई दूरी&nbsp;<br>= 180 + 180 + 30 = 390 किमी</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. Where does the Narmada river rise?</p>",
                    question_hi: "<p>22. नर्मदा नदी कहां से निकलती है?</p>",
                    options_en: ["<p>Western Ghats</p>", "<p>Gawilgarh Hills</p>", 
                                "<p>Satpura Ranges</p>", "<p>Amarkantak Ranges</p>"],
                    options_hi: ["<p>पश्चिमी घाट</p>", "<p>गाविलगढ़ पहाड़ियों</p>",
                                "<p>सतपुड़ा पर्वतमाला</p>", "<p>अमरकंटक पर्वतमाला</p>"],
                    solution_en: "<p>22.(d) <strong>Amarkantak Ranges.</strong> The Narmada River flows through Madhya Pradesh, Maharashtra, and Gujarat between Vindhya and Satpura hill range before falling into the Gulf of Cambay in the Arabian Sea. The Dhuandhar Falls is located on the Narmada River. River and their Origin : Ganga - Gangotri (Uttarakhand); Yamuna - Yamunotri (Uttarakhand); Mahanadi - Sihawa (Chhattisgarh); Krishna - Mahabaleshwar (Maharashtra); Kaveri - Brahmagiri Hills, (Karnataka).</p>",
                    solution_hi: "<p>22.(d) <strong>अमरकंटक पर्वतमाला। </strong>अरब सागर में खंभात की खाड़ी में गिरने से पहले नर्मदा नदी विंध्य और सतपुड़ा पर्वत श्रृंखला के बीच मध्य प्रदेश, महाराष्ट्र और गुजरात से होकर बहती है। धुआंधार जलप्रपात नर्मदा नदी पर स्थित है। नदी और उनका उद्गम : गंगा - गंगोत्री (उत्तराखंड); यमुना - यमुनोत्री (उत्तराखंड); महानदी - सिहावा (छत्तीसगढ़); कृष्णा - महाबलेश्वर (महाराष्ट्र); कावेरी - ब्रह्मगिरि पहाड़ियाँ, (कर्नाटक)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. If a : b = 5 : 3 and b : c = 3 : 7 , then what will be the value of a : c ?</p>",
                    question_hi: "<p>23. यदि a : b = 5 : 3 और b : c = 3 : 7 तो a : c का मान क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>23.(b) a : b = 5 : 3 and b : c = 3 : 7<br>B is same in both ratio ,<br>so that a : c = 5 : 7</p>",
                    solution_hi: "<p>23.(b) a : b = 5 : 3 और b : c = 3 : 7<br>B दोनों अनुपात में समान है, इस कारण <br>a : c = 5 : 7</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. What is the name of India&rsquo;s first Railway University?</p>",
                    question_hi: "<p>24. भारत के पहले रेलवे विश्वविद्यालय का नाम क्या है?</p>",
                    options_en: ["<p>National Rail and Transportation Institute</p>", "<p>Rashtriya Rail Vishwavidyalaya</p>", 
                                "<p>Institute of Rail Management of India</p>", "<p>Indian Rail and Roadways Institute</p>"],
                    options_hi: ["<p>राष्ट्रीय रेल और परिवहन संस्थान</p>", "<p>राष्ट्रीय रेल विश्वविद्यालय</p>",
                                "<p>इंस्टिट्यूट ऑफ़ रेल मैनेजमेंट ऑफ़ इंडिया</p>", "<p>भारतीय रेल और सड़क मार्ग संस्थान</p>"],
                    solution_en: "<p>24.(a) National Rail and Transportation Institute (Gati Shakti Vishwavidyalaya) - India&rsquo;s first University in the Transportation and Logistics sectors, Located in Vadodara (Gujarat) India. Motto - Gyaanasya Abhyaasam Kuru. Established - 2018 and converted to Central University in August 2022.</p>",
                    solution_hi: "<p>24.(a) <strong>राष्ट्रीय रेल और परिवहन संस्थान</strong> (गति शक्ति विश्वविद्यालय) - परिवहन और रसद क्षेत्रों में भारत का प्रथम विश्वविद्यालय, बड़ोदरा (गुजरात) में स्थित है। आदर्श वाक्य - ज्ञानस्य अभ्यासं कुरु। स्थापना - 2018 और अगस्त 2022 में केंद्रीय विश्वविद्यालय में परिवर्तित।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. The Yellowstone National Park is located in:</p>",
                    question_hi: "<p>25. येलोस्टोन नेशनल पार्क _____में स्थित है।</p>",
                    options_en: ["<p>Canada</p>", "<p>USA</p>", 
                                "<p>France</p>", "<p>Spain</p>"],
                    options_hi: ["<p>कनाडा</p>", "<p>USA</p>",
                                "<p>फ्रांस</p>", "<p>स्पेन</p>"],
                    solution_en: "<p>25.(b) <strong>United States of America (USA).</strong> On March 1, 1872 the Yellowstone National Park became the USA&rsquo;s first national park. It has been designated a UNESCO world heritage site since 1978. Other National park (N.P) - Northeast Greenland N.P (World largest park); Kruger N.P (South Africa); Serengeti N.P (Tanzania); Hemis N.P (India).</p>",
                    solution_hi: "<p>25.(b) <strong>संयुक्त राज्य अमेरिका (USA)।</strong> 1 मार्च, 1872 को येलोस्टोन नेशनल पार्क संयुक्त राज्य अमेरिका का प्रथम राष्ट्रीय उद्यान बन गया। इसे 1978 से यूनेस्को द्वारा विश्व धरोहर स्थल नामित किया गया है। अन्य राष्ट्रीय उद्यान (N.P) - पूर्वोत्तर ग्रीनलैंड N.P (विश्व का सबसे बड़ा पार्क); क्रूगर N.P (दक्षिण अफ्रीका); सेरेनगेटी N.P (तंजानिया); हेमिस N.P (भारत)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. When was the Asiatic Society formed by Sir William Jones at Calcutta?</p>",
                    question_hi: "<p>26. सर विलियम जोन्स द्वारा कलकत्ता (अब कलकत्ता) में एशियाटिक सोसाइटी का गठन कब किया गया था?</p>",
                    options_en: ["<p>1782</p>", "<p>1784</p>", 
                                "<p>1780</p>", "<p>1788</p>"],
                    options_hi: ["<p>1782</p>", "<p>1784</p>",
                                "<p>1780</p>", "<p>1788</p>"],
                    solution_en: "<p>26.(b) <strong>1784. </strong>The Asiatic Society of Bengal - It is the centre of learning and research in the whole continent of Asia. Since 1984, the Asiatic Society has been declared as an Institution of National Importance by an Act of the Parliament of India. The Asiatic Society of Mumbai (1804) was founded by Sir James Mackintosh.</p>",
                    solution_hi: "<p>26.(b) <strong>1784</strong> । एशियाटिक सोसाइटी ऑफ बंगाल - यह एशिया के पूरे महाद्वीप में सीखने और अनुसंधान केंद्र है। 1984 से, एशियाटिक सोसाइटी को भारत की संसद के एक अधिनियम द्वारा राष्ट्रीय महत्व के संस्थान के रूप में घोषित किया गया है। मुंबई की एशियाटिक सोसाइटी (1804) की स्थापना सर जेम्स मैकिंटोश ने की थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. The perimeter of a right triangle is 60 cm and its hypotenuse is 26 cm. Find the area of the triangle.</p>",
                    question_hi: "<p>27. एक समकोण त्रिभुज का परिमाप 60 cm है और इसका कर्ण 26 cm है त्रिभुज का क्षेत्रफल ज्ञात कीजिए।</p>",
                    options_en: ["<p>160 cm<sup>2</sup></p>", "<p>180 cm<sup>2</sup></p>", 
                                "<p>120 cm<sup>2</sup></p>", "<p>240 cm<sup>2</sup></p>"],
                    options_hi: ["<p>160 cm<sup>2</sup></p>", "<p>180 cm<sup>2</sup></p>",
                                "<p>120 cm<sup>2</sup></p>", "<p>240 cm<sup>2</sup></p>"],
                    solution_en: "<p>27.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451960399.png\" alt=\"rId12\" width=\"128\" height=\"117\"><br>Given that perimeter = 60 cm<br>&there4; p + b + 26 = 60&nbsp;</p>\n<p>&rArr; p + b = 34<br>And, p<sup>2</sup> + b<sup>2 </sup>= 26<sup>2</sup><br>(p + b)<sup>2</sup> = p<sup>2</sup> + b<sup>2</sup> + 2pb<br>&rArr; 34<sup>2</sup> = 26<sup>2</sup> + 2pb&nbsp;</p>\n<p>&rArr; 2pb = 34<sup>2</sup> - 26<sup>2</sup><br>&rArr; 2pb = 1156 - 676 = 480<br>&there4; pb = 240<br>Area of &Delta;&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> pb = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 240 = 120 cm<sup>2</sup><br><strong>Alternate method:</strong><br>Given that perimeter = 60 cm<br>&there4; p + b + 26 = 60 &rArr; p + b = 34<br>Now, we know the triplet: 10, 24, 26<br>So, p &times; b = 10 &times; 24<br>Now, Area of &Delta;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>pb = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 10 &times; 24 = 120 cm<sup>2</sup></p>",
                    solution_hi: "<p>27.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451960399.png\" alt=\"rId12\" width=\"128\" height=\"117\"><br>दिया गया है कि परिमाप = 60 cm</p>\n<p>&there4; p + b + 26 = 60&nbsp;</p>\n<p>&rArr; p + b = 34</p>\n<p>और,&nbsp;</p>\n<p>p<sup>2</sup> + b<sup>2 </sup>= 26<sup>2</sup><br>(p + b)<sup>2</sup> = p<sup>2</sup> + b<sup>2</sup> + 2pb<br>&rArr; 34<sup>2</sup> = 26<sup>2</sup> + 2pb&nbsp;</p>\n<p>&rArr; 2pb = 34<sup>2</sup> - 26<sup>2</sup><br>&rArr; 2pb = 1156 - 676 = 480<br>&there4; pb = 240</p>\n<p>त्रिभुज का क्षेत्रफल&nbsp;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> pb = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 240 = 120 cm<sup>2</sup><br><strong>वैकल्पिक तरीका:</strong><br>दिया गया है कि परिमाप = 60 सेमी<br>&there4; p + b + 26 = 60 &rArr; p + b = 34<br>10, 24, 26 त्रिक हैं <br>इसलिए, p &times; b = 10 &times; 24<br>अब, &Delta; का क्षेत्रफल&nbsp; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>pb</p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; 10 &times; 24 = 120 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. Which of the following numbers is divisible by 7 , 11 and 13 ?</p>",
                    question_hi: "<p>28. निम्नलिखित में से कौन सी संख्या 7,11 और 13 से विभाज्य है?</p>",
                    options_en: ["<p>1002001</p>", "<p>1003001</p>", 
                                "<p>1005001</p>", "<p>1004001</p>"],
                    options_hi: ["<p>1002001</p>", "<p>1003001</p>",
                                "<p>1005001</p>", "<p>1004001</p>"],
                    solution_en: "<p>28.(a) LCM of 7, 11 and 13 = 1001<br>(1001)<sup>2</sup> = 1002001<br>So, option (a) is divisible by 7, 11 and 13.</p>",
                    solution_hi: "<p>28.(a) 7, 11 और 13 का LCM = 1001<br>(1001)<sup>2</sup> = 1002001<br>अतः, विकल्प (a) 7, 11 और 13 से विभाज्य है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. Some statements are followed by Some conclusions numbered I, II, III and so on. Assuming the statements to be true, even if they do not conform to real world knowledge, decide which of the given conclusions/possibilities can be true on the basis of the statements.<br><strong>Statements:</strong><br>1. All springs are brooks.<br>2. Some brooks are creeks.<br>3. No creek is tap.<br><strong>Conclusions:</strong><br>I . Some taps are creeks.<br>II. No spring is tap.<br>III Some brooks are springs.</p>",
                    question_hi: "<p>29. कुछ कथनों के बाद कुछ निष्कर्ष क्रमांक I, II, III और इसी प्रकार आगे दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे वास्तविक जगत की जानकारी के अनुरूप न हों, तय कीजिए कि कथनों के आधार पर दिए गए निष्कर्ष में से कौन से सत्य हो सकते हैं।<br><strong>कथन:</strong><br>1. सभी झरने नाले हैं।<br>2. कुछ नाले खाड़ियां हैं।<br>3. कोई खाड़ी नल नहीं है।<br><strong>निष्कर्ष:</strong><br>I. कुछ नल खाड़ियां हैं।<br>॥ कोई झरना नल नहीं है।<br>III. कुछ नाले झरने हैं।</p>",
                    options_en: ["<p>Only conclusion I follows.</p>", "<p>Only conclusions III follows.</p>", 
                                "<p>Only conclusions II and III follow.</p>", "<p>Only conclusions I and II follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I अनुसरण करता है।</p>", "<p>केवल निष्कर्ष III अनुसरण करता है।</p>",
                                "<p>केवल निष्कर्ष II और III अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष I और II अनुसरण करते हैं।</p>"],
                    solution_en: "<p>29.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451960674.png\" alt=\"rId13\" width=\"236\" height=\"69\"><br>Only Conclusion III follows.</p>",
                    solution_hi: "<p>29.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451960985.png\" alt=\"rId14\" width=\"271\" height=\"79\"><br>केवल निष्कर्ष III अनुसरण करता है ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. Vesak, the festival that is observed on the full-moon day of the lunar month of Vaisakha, which falls in April or May, is the festival for which religion?</p>",
                    question_hi: "<p>30. चंद्र मास वैशाख की पूर्णिमा के दिन मनाया जाने वाला त्यौहार वेसक, जो अप्रैल या मई में आता है, किस धर्म का त्यौहार है?</p>",
                    options_en: ["<p>Jainism</p>", "<p>Buddhism</p>", 
                                "<p>Jews</p>", "<p>Sikhism</p>"],
                    options_hi: ["<p>जैन धर्म</p>", "<p>बुद्ध धर्म</p>",
                                "<p>यहूदी धर्म</p>", "<p>सिख धर्म</p>"],
                    solution_en: "<p>30.(b) <strong>Buddhism. Other Buddhist festivals </strong>- Losar (celebrated in Ladakh, Arunachal Pradesh and Sikkim) - It begins on the day of a new moon that marks the first day of the first month on the Tibetan calendar. Saga Dawa (Sikkim, related to Buddha\'s birth). Tendong Lho Rum Faat (Sikkim, celebrated on the full moon day of the 7th month of the lunar calendar). Festivals related to other religions - Jainism (Paryushan festival), Jews (yom kippur), Sikhism (Baisakhi).</p>",
                    solution_hi: "<p>30.(b) <strong>बुद्ध धर्म। अन्य बुद्ध त्यौहार </strong>- लोसर (लद्दाख, अरुणाचल प्रदेश और सिक्किम में मनाया जाता है) - यह अमावस्या के दिन शुरू होता है जो तिब्बती कैलेंडर के पहले महीने के पहले दिन को चिह्नित करता है। सागा दावा (सिक्किम, बुद्ध के जन्म से संबंधित)। तेंदोंग ल्हो रम फा (सिक्किम, चंद्र कैलेंडर के 7 वें महीने की पूर्णिमा के दिन मनाया जाता है)। अन्य धर्मों से जुड़े त्यौहार - जैन धर्म (पर्यूषण पर्व), यहूदी (योम किप्पुर), सिख धर्म (बैसाखी)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. Vijayanagara emperor Krishnadeva Raya founded a suburban township near Vijayanagara called Nagalapuram after his__________..</p>",
                    question_hi: "<p>31. विजयनगर के सम्राट कृष्णदेव राय ने अपनी/अपने ________ के नाम पर विजयनगर के समीप नगलपुरम नामक एक उपनगर की स्थापना की थी।</p>",
                    options_en: ["<p>father</p>", "<p>teacher</p>", 
                                "<p>sister</p>", "<p>mother</p>"],
                    options_hi: ["<p>पिता</p>", "<p>गुरु</p>",
                                "<p>बहन</p>", "<p>माता</p>"],
                    solution_en: "<p>31.(d) <strong>Mother. </strong>Vijayanagara Empire (City of victory) : Founded in 1336 AD by Harihara and Bukka of the Sangama dynasty. Capital - Hampi (along the river Tungabhadra). Vijayanagar Empire was ruled by Sangama, Saluva, Tuluva, Aravidu Dynasties. <strong>Krishnadevaraya </strong>: Ruler of the Tuluva dynasty of Vijayanagar empire (1509-29 AD), Book - &ldquo;Amuktamalyada&rdquo;.</p>",
                    solution_hi: "<p>31.(d) <strong>माता।</strong> विजयनगर साम्राज्य (सिटी ऑफ़ विक्ट्री ): 1336 ई. में संगम वंश के हरिहर और बुक्का द्वारा स्थापित किया गया था । राजधानी - हम्पी (तुंगभद्रा नदी के किनारे स्थित है )। विजयनगर साम्राज्य पर संगम, सालुव, तुलुव, अराविदु राजवंशों का शासन था। <strong>कृष्णदेवराय:</strong> विजयनगर साम्राज्य के तुलुव वंश के शासक (1509-29 ई.), पुस्तक - &ldquo;अमुक्तमाल्यद&rdquo;।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. A shadow of a tower standing on a level ground is found to be 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> meters longer when the Sun\'s altitude is 30&deg; when it is 60&deg;. The height of the tower is:</p>",
                    question_hi: "<p>32. सूर्य का उन्नयन कोण 60&deg; से बदलकर 30&deg; होने पर , भूतल पर स्थित किसी मीनार की छाया की लम्बाई में 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> मीटर की वृद्धि हो जाती है। मीनार की ऊंचाई ज्ञात कीजिये।</p>",
                    options_en: ["<p>70 m</p>", "<p>60 m</p>", 
                                "<p>40 m</p>", "<p>50 m</p>"],
                    options_hi: ["<p>70 m</p>", "<p>60 m</p>",
                                "<p>40 m</p>", "<p>50 m</p>"],
                    solution_en: "<p>32.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451961347.png\" alt=\"rId15\" width=\"150\" height=\"92\"><br>In triangle ABD,<br>tan 60&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>D</mi></mrow></mfrac></math> &rArr; AB = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>h<br>In triangle ABC,<br>tan 30&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>D</mi><mo>+</mo><mi>C</mi><mi>D</mi></mrow></mfrac></math></p>\n<p>&rArr; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br>Now, <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>h = &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br>&rArr; 3h = h + 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;</p>\n<p>&rArr; 3h - h = 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>&rArr; 2h = 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>\n<p>&rArr; h = 20<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>Now, height of the tower (AB) <br>=&nbsp;<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>h = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &times; 20<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>\n<p>= 20 &times; 3 = 60 m</p>",
                    solution_hi: "<p>32.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451961347.png\" alt=\"rId15\" width=\"171\" height=\"106\"><br>त्रिभुज ABD में,<br>tan 60&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>D</mi></mrow></mfrac></math> &rArr; AB = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>h<br>त्रिभुज ABC में ,</p>\n<p>tan 30&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>B</mi><mi>D</mi><mo>+</mo><mi>C</mi><mi>D</mi></mrow></mfrac></math></p>\n<p>&rArr; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math></p>\n<p>अब , <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>h = &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>h</mi><mo>+</mo><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br>&rArr; 3h = h + 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>&nbsp;</p>\n<p>&rArr; 3h - h = 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br>&rArr; 2h = 40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>\n<p>&rArr; h = 20<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>\n<p>अब , मीनार की ऊंचाई (AB)</p>\n<p>=&nbsp;<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>h = <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> &times; 20<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>\n<p>= 20 &times; 3 = 60 m</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. The value of 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &divide; [2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> - 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>] is:</p>",
                    question_hi: "<p>33. 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &divide; [2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> - 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>] का मान ज्ञात कीजिये ?</p>",
                    options_en: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", 
                                "<p>1<math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math></p>",
                                "<p>1<math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>41</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>33.(c) <br>4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &divide; [2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> - 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>]<br>= <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math>)]<br>= <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math>)]<br>= <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>41</mn><mn>18</mn></mfrac></math>]</p>\n<p>= <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>41</mn></mfrac></math>]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mn>41</mn></mfrac></math>= 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>41</mn></mfrac></math></p>",
                    solution_hi: "<p>33.(c) </p>\n<p>4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &divide; [2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> - 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>]<br>= <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math>)]<br>= <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math>)]<br>= <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>41</mn><mn>18</mn></mfrac></math>]</p>\n<p>= <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>41</mn></mfrac></math>]<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>78</mn><mn>41</mn></mfrac></math>= 1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>41</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. Which of the following terms define the Real National Income?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन सा शब्द वास्तविक राष्ट्रीय आय को परिभाषित करता है?</p>",
                    options_en: ["<p>GDP at current amount</p>", "<p>GDP at constant price</p>", 
                                "<p>GDP at average price</p>", "<p>GDP at factors price</p>"],
                    options_hi: ["<p>वर्तमान राशि (current amount ) पर जीडीपी (GDP)</p>", "<p>स्थिर मूल्य पर जीडीपी (GDP) )</p>",
                                "<p>औसत मूल्य पर जीडीपी (GDP)</p>", "<p>साधन मूल्य (factors price) पर जीडीपी (GDP)</p>"],
                    solution_en: "<p>34.(b) <strong>GDP </strong>(Gross Domestic Product) <strong>at constant price</strong>. Real national income is nominal or money national income (output) adjusted for inflation. It is also national income at constant prices. The most frequently used measure of national income is Gross Domestic Product (GDP).</p>",
                    solution_hi: "<p>34.(b) <strong>स्थिर मूल्य पर GDP</strong> (सकल घरेलू उत्पाद)। वास्तविक राष्ट्रीय आय मुद्रास्फीति के लिए समायोजित नाममात्र या धन राष्ट्रीय आय (आउटपुट) है। यह स्थिर कीमतों पर राष्ट्रीय आय भी है। राष्ट्रीय आय का सबसे अधिक उपयोग किया जाने वाला उपाय सकल घरेलू उत्पाद (GDP) है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. In certain code language, LEADER is written as ELDARE. How will FOUNTAIN be written as in that code language?</p>",
                    question_hi: "<p>35. एक कूट भाषा में LEADER को ELDARE के रूप में लिखा जाता है। FOUNTAIN को उसी कोड भाषा में कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>OFNUATNI</p>", "<p>FOUNTANI</p>", 
                                "<p>OFUNATIN</p>", "<p>FONUATIN</p>"],
                    options_hi: ["<p>OFNUATNI</p>", "<p>FOUNTANI</p>",
                                "<p>OFUNATIN</p>", "<p>FONUATIN</p>"],
                    solution_en: "<p>35.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451961628.png\" alt=\"rId16\" width=\"154\" height=\"88\"> ,</p>\n<p>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451961842.png\" alt=\"rId17\" width=\"175\" height=\"74\"></p>",
                    solution_hi: "<p>35.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451961628.png\" alt=\"rId16\" width=\"150\" height=\"86\"></p>\n<p>इसी तरह<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451961842.png\" alt=\"rId17\" width=\"168\" height=\"71\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. The mean of three numbers is 53. The range of this data set is 28 while the difference between the two smallest numbers is 8. The greatest of the three&nbsp;numbers is:</p>",
                    question_hi: "<p>36. तीन संख्याओं का माध्य 53 है। इस डेटा सेट की सीमा 28 है जबकि दो सबसे छोटी संख्याओं के बीच का अंतर 8 है। तीन संख्याओं में से सबसे बड़ी संख्या है:</p>",
                    options_en: ["<p>72</p>", "<p>71</p>", 
                                "<p>73</p>", "<p>69</p>"],
                    options_hi: ["<p>72</p>", "<p>71</p>",
                                "<p>73</p>", "<p>69</p>"],
                    solution_en: "<p>36.(d)<br>Let , smallest number = x,</p>\n<p>middle number = y and</p>\n<p>greatest numbers = z&nbsp;<br>A/Q , x + y + z = 53 &times; 3<br>&rArr; x + y + z = 159 -----(i)<br>And z - x = 28 ----(ii)<br>Also y = x + 8 -----(iii)<br>From equation (i),(ii) &amp; (iii)<br>x + x + 8 + x + 28 = 159<br>&rArr; 3x + 36 = 159</p>\n<p>&rArr; x = 41<br>The greatest number = z = 28 + 41 = 69</p>",
                    solution_hi: "<p>36.(d)<br>माना, सबसे छोटी संख्या = x,</p>\n<p>मध्य संख्या = y और</p>\n<p>सबसे बड़ी संख्या = z<br>प्रश्न के अनुसार, &rArr; x + y + z = 53 &times; 3<br>&rArr; x + y + z = 159 -----(i)<br>और z - x = 28 ---- (ii)<br>साथ ही y = x + 8 -----(iii)<br>समीकरण (i),(ii) और (iii) से<br>x + x + 8 + x + 28 =159<br>3x + 36 = 159</p>\n<p>&rArr; x = 41<br>सबसे बड़ी संख्या = z = 28 + 41 = 69</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. The minimum value of 4sin<sup>2</sup>&theta; + 5cos<sup>2</sup>&theta; is :</p>",
                    question_hi: "<p>37. 4sin<sup>2</sup>&theta; + 5cos<sup>2</sup>&theta; का न्यूनतम मान क्या है ?</p>",
                    options_en: ["<p>0</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>0</p>", "<p>2</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>37.(c) 4sin<sup>2</sup>&theta; + 5cos<sup>2</sup>&theta;<br>=&nbsp;4sin<sup>2</sup>&theta; + 4cos<sup>2</sup>&theta; + cos<sup>2</sup>&theta;<br>=&nbsp;4(sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;) + cos<sup>2</sup>&theta;<br>= 4 &times; 1 + cos<sup>2</sup>&theta;<br>= 4 + 0 (minimum value of cos&theta; = 0) = 4</p>",
                    solution_hi: "<p>37.(c) 4sin<sup>2</sup>&theta; + 5cos<sup>2</sup>&theta;<br>=&nbsp;4sin<sup>2</sup>&theta; + 4cos<sup>2</sup>&theta; + cos<sup>2</sup>&theta;<br>=&nbsp;4(sin<sup>2</sup>&theta; + cos<sup>2</sup>&theta;) + cos<sup>2</sup>&theta;<br>= 4 &times; 1 + cos<sup>2</sup>&theta;<br>= 4 + 0 (minimum value of cos&theta; = 0) = 4</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. In which year did ISRO launch the Mars Orbiter Mission?</p>",
                    question_hi: "<p>38. ISRO ने किस वर्ष मार्स आर्बिटर मिशन (मंगलयान) प्रक्षेपित किया था ?</p>",
                    options_en: ["<p>2013</p>", "<p>2015</p>", 
                                "<p>2012</p>", "<p>2014</p>"],
                    options_hi: ["<p>2013</p>", "<p>2015</p>",
                                "<p>2012</p>", "<p>2014</p>"],
                    solution_en: "<p>38.(a) <strong>2013</strong>. Mars Orbiter Mission (MOM): India\'s first interplanetary mission to planet Mars was launched onboard PSLV-C25 on November 05, 2013. Indian Space Research Organization (ISRO) has become the fourth space agency to successfully send a spacecraft to Mars orbit. Tianwen-1 - China\'s Mission to Mars. Mariner 4 mission- United States&rsquo; mission to Mars. Mars 3 - Russia&rsquo;s mission to Mars.</p>",
                    solution_hi: "<p>38.(a) <strong>2013 </strong>। मार्स ऑर्बिटर मिशन (MOM): मंगल ग्रह के लिए भारत का पहला इंटरप्लेनेटरी मिशन 05 नवंबर, 2013 को PSLV-C25 से लॉन्च किया गया था। भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO) मंगल की कक्षा में अंतरिक्ष यान को सफलतापूर्वक भेजने वाली चौथी अंतरिक्ष एजेंसी बन गई है। तियानवेन-1 - चीन का मंगल अभियान। मेरिनर 4 मिशन- मंगल ग्रह के लिए संयुक्त राज्य अमेरिका का मिशन। मंगल 3 - मंगल ग्रह के लिए रूस का मिशन।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. What will come next in the series?<br>D5A, F9C, H13E, J17G, ?</p>",
                    question_hi: "<p>39. श्रृंखला में आगे क्या आएगा?<br>D5A, F9C, H13E, J17G, ?</p>",
                    options_en: ["<p>J25L</p>", "<p>L21I</p>", 
                                "<p>I21L</p>", "<p>L25J</p>"],
                    options_hi: ["<p>J25L</p>", "<p>L21I</p>",
                                "<p>I21L</p>", "<p>L25J</p>"],
                    solution_en: "<p>39.(b) First letter : D + 2 = F,F + 2 = H, H + 2 = J , J + 2 =<strong> L</strong><br>2nd letter : 5 + 4 = 9, 9 + 4 = 13,13 + 4 <br>= 17, 17 + 4 = <strong>21</strong><br>3rd letter : A + 2 = C,C + 2 = E, E + 2 = G, <br>G + 2 = I , <strong>? = L 21 I</strong></p>",
                    solution_hi: "<p>39.(b) पहला अक्षर : D + 2 = F, F + 2 = H, H + 2 = J , J + 2 = <strong>L</strong><br>दूसरा अक्षर : 5 + 4 = 9, 9 + 4 = 13, <br>13 + 4 = 17, 17 + 4 =<strong> 21</strong><br>तीसरा अक्षर : A + 2 = C,C + 2 = E, <br>E + 2 = G,G + 2 = I , <strong>? = L 21 I</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. Which of the following is NOT a biotic component of the ecosystem?</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन सा पारिस्थितिकी तंत्र का जैविक घटक नहीं है?</p>",
                    options_en: ["<p>Frog</p>", "<p>Soil</p>", 
                                "<p>Insect</p>", "<p>Flower</p>"],
                    options_hi: ["<p>मेंढक</p>", "<p>मिट्टी</p>",
                                "<p>कीट</p>", "<p>फूल</p>"],
                    solution_en: "<p>40.(b) <strong>Soil.</strong> Examples of Biotic components (living things present in the ecosystem) - Producers, consumers, decomposers, and detritivores). Examples of Abiotic components (all non -living things present in the atmosphere, hydrosphere, and lithosphere) - Sunlight, air, precipitation, weather, water, temperature, humidity, altitude, minerals, and soil.</p>",
                    solution_hi: "<p>40.(b) <strong>मिट्टी </strong>। जैविक घटकों के उदाहरण (पारिस्थितिकी तंत्र में मौजूद जीवित चीजें) - उत्पादक, उपभोक्ता, अपघटक और अपरदाहारी (डेट्रिटिवोर्स)। अजैविक घटकों के उदाहरण (वायुमंडल, जलमंडल और स्थलमंडल में मौजूद सभी निर्जीव चीजें) - सूर्य का प्रकाश, वायु, वर्षा, मौसम, पानी, तापमान, आर्द्रता, ऊंचाई, खनिज और मिट्टी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. Select the option that is related to the fifth number in the same way as the second number is related to the first number and the fourth number is related to the third number.<br>864 : 12 :: 1372 : 14 :: 2048 : ?</p>",
                    question_hi: "<p>41. उस विकल्प का चयन कीजिए जो पाँचवीं संख्या से उसी प्रकार संबंधित है जिस प्रकार दूसरी संख्या पहली संख्या से संबंधित है और चौथी संख्या तीसरी संख्या से संबंधित है।<br>864 : 12 :: 1372 : 14 :: 2048 : ?</p>",
                    options_en: ["<p>16</p>", "<p>12</p>", 
                                "<p>18</p>", "<p>14</p>"],
                    options_hi: ["<p>16</p>", "<p>12</p>",
                                "<p>18</p>", "<p>14</p>"],
                    solution_en: "<p>41.(a) <strong>Logic:- </strong>First number = (Second number)<sup>3</sup> &divide; 2 <br>(864 : 12):- 864 = (12)<sup>3</sup> &divide; 2,&nbsp;1728 &divide; 2 = 864<br>(1372 : 14):- 1372 = (14)<sup>3</sup> &divide; 2 ,&nbsp;2744 &divide; 2 = 1372<br>Similarly,<br>(2048 : ?):- 2048 = (?)<sup>3</sup> &divide; 2 ,&nbsp;(16)<sup>3</sup> &divide;&nbsp; 2 , 4096 &divide; 2 = 2048</p>",
                    solution_hi: "<p>41.(a)&nbsp;<strong>तर्क :-&nbsp;</strong>पहली संख्या = (दूसरी संख्या)<sup>3</sup> &divide; 2<br>(864 : 12):-&nbsp;864 = (12)<sup>3</sup> &divide; 2 , 1728 &divide; 2 = 864<br>(1372 : 14):-&nbsp;1372 = (14)<sup>3</sup> &divide; 2 , 2744 &divide; 2 = 1372<br>इसी प्रकार,<br>(2048 : ?):- 2048 = (?)<sup>3</sup> <math display=\"inline\"><mo>&#247;</mo></math> 2 ,&nbsp;(16)3 &divide; 2 , 4096 &divide; 2 = 2048</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. 2.666...+ 2.77... in fraction form is:</p>",
                    question_hi: "<p>42. 2.666... ​​+ 2.77... भिन्न रूप में है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>31</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>49</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>31</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>47</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>49</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>42.(c) 2.666... ​​+ 2.77...<br>= 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>9</mn></mfrac></math> + 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math></p>\n<p>= 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>9</mn></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>9</mn></mfrac></math></p>",
                    solution_hi: "<p>42.(c) 2.666... ​​+ 2.77...<br>= 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>9</mn></mfrac></math> + 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>9</mn></mfrac></math></p>\n<p>= 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>9</mn></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>9</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. Which city will host the 2028 Olympics games?</p>",
                    question_hi: "<p>43. कौन-सा शहर 2028 ओलंपिक खेलों की मेजबानी करेगा?</p>",
                    options_en: ["<p>Boston, USA</p>", "<p>Los Angeles, USA</p>", 
                                "<p>San Diego, USA</p>", "<p>New York, USA</p>"],
                    options_hi: ["<p>बोस्टन, यूएसए (Boston, USA)</p>", "<p>लॉस एंजेल्स , यूएसए (Los Angeles, USA)</p>",
                                "<p>सैन डिएगो, यूएसए (San Diego, USA)</p>", "<p>न्यूयॉर्क, यूएसए (New York, USA)</p>"],
                    solution_en: "<p>43.(b) <strong>Los Angeles, USA</strong>. The 2024 Summer Olympics and Paralympics Games was hosted by Paris, France. Other upcoming Olympic games and host: 2030 Winter Olympics: French Alps. 2032 Summer Olympics: Brisbane, Australia. 2034 Winter Olympics and Paralympics: Salt Lake City, USA.</p>",
                    solution_hi: "<p>43.(b) <strong>लॉस एंजिल्स, USA</strong> । 2024 ग्रीष्मकालीन ओलंपिक और पैरालिंपिक खेलों की मेज़बानी पेरिस, फ्रांस द्वारा की गई । अन्य आगामी ओलंपिक खेल और मेज़बान देश: 2030 शीतकालीन ओलंपिक: फ्रेंच आल्प्स। 2032 ग्रीष्मकालीन ओलंपिक: ब्रिस्बेन, ऑस्ट्रेलिया। 2034 शीतकालीन ओलंपिक और पैरालिंपिक: साल्ट लेक सिटी, USA।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. A statement is given followed by two arguments I and II. Read the statement and the arguments carefully and select the appropriate answer from the given options.<br><strong>Statement:</strong><br>People are becoming more conscious about moving towards a healthy lifestyle.<br><strong>Arguments:</strong><br>I. Many people are switching to organic farming as the food is healthier.<br>II. People are planning meals and cooking at home more than before.</p>",
                    question_hi: "<p>44. एक कथन और उसके बाद दो तर्क I और ॥ दिए गए हैं। कथन और तर्कों का ध्यानपूर्वक अध्ययन कीजिए, और दिए गए विकल्पों में से उपयुक्त उत्तर का चयन कीजिए।<br><strong>कथन:</strong><br>लोग स्वस्थ जीवन शैली अपनाने के बारे में अधिक जागरूक हो रहे हैं।<br><strong>तर्क:</strong><br>I. बहुत से लोग जैविक खेती को अपना रहे हैं, क्योंकि इसके द्वारा उत्पन्न खाद्य पदार्थ स्वास्थ्यवर्धक होते हैं।<br>II. लोग घर पर खाना पकाने और उसे खाने को पहले से अधिक तरजीह दे रहे हैं।</p>",
                    options_en: ["<p>Both I and II weaken the statement</p>", "<p>I weakens, while II strengthens the statement</p>", 
                                "<p>II weakens, while I strengthens the statement</p>", "<p>Both I and II strengthen the statement</p>"],
                    options_hi: ["<p>I और II दोनों कथन का समर्थन नहीं करते हैं।</p>", "<p>I कथन का समर्थन नहीं करता, जबकि II . कथन का समर्थन करता है।</p>",
                                "<p>II कथन का समर्थन नहीं करता, जबकि । कथन का समर्थन करता है।</p>", "<p>I और II दोनों कथन का समर्थन करते हैं।</p>"],
                    solution_en: "<p>44.(d) As from the statement Both I and II arguments strengthen the statement.</p>",
                    solution_hi: "<p>44.(d) कथन के अनुसार I और II दोनों तर्क कथन का समर्थन करते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. Seven friends A, B, C, D, E, F and G are sitting facing the center in a circular position. F is sitting to the immediate right of C. C is at the second position to the right of G. E is sitting at the third position to the right of A. B is an immediate neighbour of both D and E. Which of the following statements is FALSE ?</p>",
                    question_hi: "<p>45. सात मित्र A, B, C, D, E, F और G एक वृत्ताकार स्थिति में केंद्र की ओर मुख करके बैठे हैं। F, C के ठीक दायें बैठा है। C, G के दायें से दूसरे स्थान पर है। E, A के दायें से तीसरे स्थान पर बैठा है। B, D और E दोनों का निकटतम पडोसी है। निम्नलिखित में से कौन सा कथन FALSE है?</p>",
                    options_en: ["<p>C is sitting at the third position to the right of D</p>", "<p>A is sitting to the immediate left of C.</p>", 
                                "<p>B is sitting to the immediate right of G</p>", "<p>E is sitting at the third position to the left of G.</p>"],
                    options_hi: ["<p>C, D के दायें से तीसरे स्थान पर बैठा है</p>", "<p>A, C के तत्काल बाईं ओर बैठा है।</p>",
                                "<p>B, G के ठीक दायें बैठा है</p>", "<p>E, G के बायें से तीसरे स्थान पर बैठा है।</p>"],
                    solution_en: "<p>45.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451962089.png\" alt=\"rId18\" width=\"100\" height=\"106\"><br>According to the option, Only (c) is incorrect.</p>",
                    solution_hi: "<p>45.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451962089.png\" alt=\"rId18\" width=\"100\" height=\"106\"><br>विकल्प के अनुसार, केवल (c) गलत है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. ₹2,000 is divided among A, B and C such that half of A\'s part, one-third of B\'s part and one-fifth of C\'s part are equal. What is A\'s part (in₹) ?</p>",
                    question_hi: "<p>46. ₹2,000 की राशि को A, B और C के बीच इस प्रकार बांटा जाता है, कि A के हिस्से का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> भाग, B के हिस्से का <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> भाग, और C के हिस्से का <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> भाग बराबर हैं। A का हिस्सा (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>600</p>", "<p>400</p>", 
                                "<p>1000</p>", "<p>200</p>"],
                    options_hi: ["<p>600</p>", "<p>400</p>",
                                "<p>1000</p>", "<p>200</p>"],
                    solution_en: "<p>46.(b) As per question,<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> &times; C = k&nbsp;<br>A = 2k ,B = 3k ,C = 5k <br>2K + 3K + 5K = 2000 <br>&rArr; 10K = 2000</p>\n<p>&rArr; K = 200<br>So, A&rsquo;s part = 2K = 2 &times; 200 = 400</p>",
                    solution_hi: "<p>46.(b) प्रश्न के अनुसार,</p>\n<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; A = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> &times; B = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> &times; C = k&nbsp;<br>A = 2k ,B = 3k ,C = 5k <br>2K + 3K + 5K = 2000 <br>&rArr; 10K = 2000</p>\n<p>&rArr; K = 200</p>\n<p>अत: A का भाग = 2K = 2 &times; 200 = 400</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. The word \'direct free kick\' is related to which of the following sports?</p>",
                    question_hi: "<p>47. &lsquo;डायरेक्ट फ्री किक\' शब्द निम्नलिखित में से किस खेल से संबंधित है ?</p>",
                    options_en: ["<p>Basketball</p>", "<p>Baseball</p>", 
                                "<p>Volleyball</p>", "<p>Football</p>"],
                    options_hi: ["<p>बास्केटबाल</p>", "<p>बेसबॉल</p>",
                                "<p>वॉलीबाल</p>", "<p>फुटबॉल</p>"],
                    solution_en: "<p>47.(d) <strong>Football. Sports Terminology : Football -</strong> Corner kick, Goalkeeper, Offside, Penalty kick, Counterattack, Clean sheet, Assist. <strong>Baseball </strong>- Pinching, Homerun, Base runner, Perfect game, Throw, Strike, Put out. <strong>Volleyball</strong> - Ace, Antenna, Approach, Assist, Deuce Booster, Spikers, Service, Attack Error, Cross-court attack, Rally scoring. <strong>Basketball</strong> - Free throw, Common foul, Under head, Technical foul.<strong> Cricket</strong> - L.B.W, Hit Wicket, Googley, Dead ball, Bouncer, Ashes, Spin. <strong>Chess </strong>- Gambit, Checkmate, Stalemate, Grand master, International master.</p>",
                    solution_hi: "<p>47.(d) <strong>फुटबॉल। खेल शब्दावली: फुटबॉल - </strong>कॉर्नर किक, गोलकीपर, ऑफसाइड, पेनल्टी किक, काउंटरअटैक, क्लीन शीट, असिस्ट। <strong>बेसबॉल </strong>- पिंचिंग, होमरून, बेस रनर, परफेक्ट गेम, थ्रो, स्ट्राइक, पुट आउट। <strong>वॉलीबॉल </strong>- ऐस, ऐन्टेन, अप्रोच, असिस्ट, ड्यूस बूस्टर, स्पाइकर्स, सर्विस, अटैक एरर, क्रॉस-कोर्ट अटैक, रैली स्कोरिंग। <strong>बास्केटबॉल </strong>- फ़्री थ्रो, कॉमन फ़ाउल, अंडर हेड, टेक्निकल फ़ाउल। <strong>क्रिकेट</strong> - L.B.W, हिट विकेट, गूगली, डेड बॉल, बाउंसर, एशेज, स्पिन। <strong>शतरंज </strong>- गैम्बिट, चेकमेट, स्टेलेमेट, ग्रैंड मास्टर, इंटरनेशनल मास्टर।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. Ajit Kumar Mohanty took over the charge of Chairman, _____, in May 2023.</p>",
                    question_hi: "<p>48. मई 2023 में, अजीत कुमार मोहंती (Ajit Kumar Mohanty) ने _________ के अध्यक्ष के रूप में पद संभाला?</p>",
                    options_en: ["<p>Bhabha Atomic Research Centre</p>", "<p>National Commission for Backward Classes</p>", 
                                "<p>National Human Rights Commission of India</p>", "<p>Atomic Energy Commission</p>"],
                    options_hi: ["<p>भाभा परमाणु अनुसंधान केंद्र</p>", "<p>राष्ट्रीय पिछड़ा वर्ग आयोग</p>",
                                "<p>राष्ट्रीय मानवाधिकार आयोग भारत</p>", "<p>परमाणु ऊर्जा आयोग</p>"],
                    solution_en: "<p>48.(d) <strong>Atomic Energy Commission. </strong>Formed in - August 1948. It is the governing body of the Department of Atomic Energy (DAE), Government of India. The DAE is under the direct charge of the Prime Minister. Preceding agency - Department of Scientific Research.</p>",
                    solution_hi: "<p>48.(d) <strong>परमाणु ऊर्जा आयोग</strong>। अगस्त 1948 में गठित। यह भारत सरकार के परमाणु ऊर्जा विभाग (DAE) का शासी निकाय है। DAE प्रधानमंत्री के प्रत्यक्ष प्रभार के अधीन है। पूर्ववर्ती एजेंसी - वैज्ञानिक अनुसंधान विभाग।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. Fatima and Abdul are siblings. Fatima&rsquo;s father Aziz is the only son of his parents. Ansari is the paternal grandfather of Abdul. Sara is the daughter -in - law of Ansari .How is Sara related to Aziz?</p>",
                    question_hi: "<p>49. फातिमा और अब्दुल सहोदर हैं। फातिमा के पिता अजीज अपने माता-पिता के इकलौते बेटे हैं। अंसारी अब्दुल के दादा हैं। सारा, अंसारी की बहू है। सारा, अजीज से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Mother</p>", "<p>Wife</p>", 
                                "<p>Aunt</p>", "<p>Sister</p>"],
                    options_hi: ["<p>माता</p>", "<p>पत्नी</p>",
                                "<p>चाची</p>", "<p>बहन</p>"],
                    solution_en: "<p>49.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451962271.png\" alt=\"rId19\" width=\"131\" height=\"122\"><br>Sara is the wife of Aziz.</p>",
                    solution_hi: "<p>49.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451962493.png\" alt=\"rId20\" width=\"123\" height=\"115\"><br>सारा अजीज की पत्नी है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. Three numbers are in the ratio 1 : 2 : 5 and their LCM is 1600. Find the HCF of the numbers.</p>",
                    question_hi: "<p>50. तीन संख्याओं का अनुपात 1 : 2 : 5 है, और उनका लघुत्तम समापवर्त्य (LCM) 1600 है। संख्याओं का महत्तम समापवर्तक (HCF) ज्ञात कीजिए ।</p>",
                    options_en: ["<p>800</p>", "<p>320</p>", 
                                "<p>160</p>", "<p>480</p>"],
                    options_hi: ["<p>800</p>", "<p>320</p>",
                                "<p>160</p>", "<p>480</p>"],
                    solution_en: "<p>50.(c) <br>The numbers = <math display=\"inline\"><mfrac><mrow><mn>1600</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1600</mn><mn>2</mn></mfrac></math> , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1600</mn><mn>5</mn></mfrac></math><br>= 1600 , 800 , 320<br>HCF of (1600 , 800 , 320 ) = 160</p>",
                    solution_hi: "<p>50.(c) संख्या = <math display=\"inline\"><mfrac><mrow><mn>1600</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1600</mn><mn>2</mn></mfrac></math> , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1600</mn><mn>5</mn></mfrac></math><br>= 1600 , 800 , 320<br>(1600 , 800 , 320 ) का महत्तम समापवर्तक<br>= 160</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. If the perimeter of one face of a cube is 24 cm, then its volume is:</p>",
                    question_hi: "<p>51. यदि एक घन के एक फलक का परिमाप 24 सेमी है, तो उसका आयतन कितना होगा ?</p>",
                    options_en: ["<p>180 cm<sup>3</sup></p>", "<p>154 cm<sup>3</sup></p>", 
                                "<p>200 cm<sup>3</sup></p>", "<p>216 cm<sup>3</sup></p>"],
                    options_hi: ["<p>180 सेमी<sup>3</sup></p>", "<p>154 सेमी<sup>3</sup></p>",
                                "<p>200 सेमी<sup>3</sup></p>", "<p>216 सेमी<sup>3</sup></p>"],
                    solution_en: "<p>51.(d) Perimeter of one face of cube<br>= 4a = 24 cm , a = 6 cm<br>Then volume = a<sup>3</sup> = 6<sup>3</sup> = 216 cm<sup>3</sup></p>",
                    solution_hi: "<p>51.(d) घन के एक फलक का परिमाप = 4a <br>24 सेमी = 4a ,&nbsp;a = 6 सेमी<br>तब आयतन = a<sup>3</sup> = 6<sup>3</sup> = 216 सेमी<sup>3</sup></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52.Select the figure which does NOT belong to the group.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451962608.png\" alt=\"rId21\" width=\"251\" height=\"81\"></p>",
                    question_hi: "<p>52. उस चित्र का चयन करें जो समूह से संबंधित नहीं है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451962608.png\" alt=\"rId21\" width=\"251\" height=\"81\"></p>",
                    options_en: ["<p>4</p>", "<p>1</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>4</p>", "<p>1</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>52.(a) The number of straight lines are 3 in image 1,2 and 3 except image 4.<br>The correct answer is <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451962810.png\" alt=\"rId22\" width=\"112\" height=\"112\"></p>",
                    solution_hi: "<p>52.(a) छवि 4 को छोड़कर छवि 1,2 और 3 में सीधी रेखाओं की संख्या 3 है।<br>सही उत्तर है<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451962810.png\" alt=\"rId22\" width=\"112\" height=\"112\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. Which was the first country to incorporate Fundamental Duties in its Constitution?</p>",
                    question_hi: "<p>53. अपने संविधान में मौलिक कर्तव्यों को शामिल करने वाला पहला देश कौन-सा था?</p>",
                    options_en: ["<p>Albania</p>", "<p>USSR</p>", 
                                "<p>India</p>", "<p>Poland</p>"],
                    options_hi: ["<p>अल्बानिया</p>", "<p>सोवियत संघ (USSR)</p>",
                                "<p>भारत</p>", "<p>पोलैंड</p>"],
                    solution_en: "<p>53.(b) <strong>USSR. </strong>The Soviet Union adopted the first constitution on 31st January 1924. Russia adopted its constitution in 1993. Fundamental Duties (Part IV A, Article 51 A) - They (Initial 10) were added by The 42nd Amendment Act of 1976 on the recommendation of The Swaran Singh Committee in 1976. The last one was added by the 86th Constitution Amendment Act 2002. <strong>The United States of America </strong>- First country to make and adopt a constitution.</p>",
                    solution_hi: "<p>53.(b) <strong>सोवियत संघ (USSR)I </strong>सोवियत संघ ने 31 जनवरी 1924 को पहला संविधान अपनाया। रूस ने 1993 में अपना संविधान अपनाया। मौलिक कर्तव्यों (भाग IV A, अनुच्छेद 51 A) इन्हें (प्रारंभिक 10) 1976 में स्वर्ण सिंह समिति की सिफारिश पर 42वें संशोधन अधिनियम 1976 द्वारा जोड़ा गया था। अंतिम 86वें संविधान संशोधन अधिनियम 2002 द्वारा जोड़ा गया था। संयुक्त राज्य अमेरिका - संविधान बनाने और उसे अपनाने वाला पहला देश।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. The 5th edition of the Khelo India Youth Games 2023 was hosted in January-February, 2023 by _______</p>",
                    question_hi: "<p>54. खेलो इंडिया यूथ गेम्स (Khelo India Youth Games) 2023 के 5वें संस्करण की मेजबानी जनवरी- फरवरी, 2023 में _______ द्वारा की गई थी।</p>",
                    options_en: ["<p>Uttar Pradesh</p>", "<p>Madhya Pradesh</p>", 
                                "<p>Rajasthan</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>उत्तर प्रदेश</p>", "<p>मध्य प्रदेश</p>",
                                "<p>राजस्थान</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>54.(b) <strong>Madhya Pradesh.</strong> The Khelo India Youth Games 2023 took place (total 11) across eight cities in Madhya Pradesh and New Delhi. Medal tally: 1<sup>st</sup> - Maharashtra, 2<sup>nd</sup> - Haryana, 3<sup>rd</sup> - Madhya Pradesh.</p>",
                    solution_hi: "<p>54.(b) <strong>मध्य प्रदेश।</strong> खेलो इंडिया यूथ गेम्स 2023 मध्य प्रदेश और नई दिल्ली के आठ शहरों में (कुल 11) आयोजित किए गए। पदक तालिका: प्रथम - महाराष्ट्र, द्वितीय - हरियाणा, तृतीय - मध्य प्रदेश।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. In this question, a group of letters is coded using numbers as per the table given below and the conditions which follow. The correct combination of codes following the conditions is your answer.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451962951.png\" alt=\"rId23\" width=\"296\" height=\"43\"> <br><strong>Conditions:</strong><br>(i) If the first element is a vowel and the last, a consonant, the codes for these two (the first and the last elements) are to be interchanged.<br>(ii) If the first element is a consonant and the last, a vowel, the first and last elements are to be coded as 2.<br>(iii) If both the third and the fifth elements are vowels, the fifth element is to be coded as the code for the third element and vice-versa.<br>Question: GRAVITY</p>",
                    question_hi: "<p>55. इस प्रश्न में, अक्षरों / प्रतीकों के एक समूह को नीचे दी गई तालिका और उसके बाद दी गई शर्तों के अनुसार संख्याओं का उपयोग करके कूटबद्ध किया जाता है। शर्तों का पालन करने वाले कूटों का सही संयोजन आपका उत्तर है।</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451963063.png\" alt=\"rId24\" width=\"282\" height=\"58\"> <br><strong>स्थितियाँ:</strong><br>(i) यदि पहला घटक एक स्वर है और अंतिम एक&nbsp;व्यंजन है, तो इन दोनों (पहला और अंतिम घटक)&nbsp;के कोड को आपस में बदलना है।<br>(ii) यदि पहला घटक एक व्यंजन है और अंतिम एक स्वर है, तो पहले और अंतिम घटकों को 2 के रूप में कोडित किया जाएगा।<br>(iii) यदि तीसरा और पांचवां दोनों घटक स्वर हैं, तो पांचवें घटक को तीसरे घटक के कोड के रूप में कोडित किया जाएगा और इसके विपरीत।<br>प्रश्न: GRAVITY</p>",
                    options_en: ["<p>5603984</p>", "<p>5038964</p>", 
                                "<p>5309846</p>", "<p>5098364</p>"],
                    options_hi: ["<p>5603984</p>", "<p>5038964</p>",
                                "<p>5309846</p>", "<p>5098364</p>"],
                    solution_en: "<p>55.(b)<br>Since, condition (iii) follows only, <br>According to condition (iii), Fifth element is coded as the code for Third element and vice versa<br>Given, GR<strong><span style=\"text-decoration: underline;\">A</span></strong>V<strong><span style=\"text-decoration: underline;\">I</span></strong>TY &rarr;&nbsp;(50<span style=\"text-decoration: underline;\"><strong>9</strong></span>8<span style=\"text-decoration: underline;\"><strong>3</strong></span>64) should be interchanged,<br>We get, (5038964)</p>",
                    solution_hi: "<p>55.(b)<br>चूंकि, शर्त (iii) केवल अनुसरण करती है,<br>शर्त (iii) के अनुसार, पांचवें घटक को तीसरे घटक के कोड के रूप में कोडित किया गया है और इसके विपरीत&hellip;.।<br>दिया गया है, GR<span style=\"text-decoration: underline;\"><strong>A</strong></span>V<span style=\"text-decoration: underline;\"><strong>I</strong></span>TY &rarr; (50<span style=\"text-decoration: underline;\"><strong>9</strong></span>8<span style=\"text-decoration: underline;\"><strong>3</strong></span>64) को आपस में बदलना चाहिए,<br>हमें प्राप्त होता है, (5038964)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. Select the one that is different.</p>",
                    question_hi: "<p>56. उस एक का चयन करें जो अलग है।</p>",
                    options_en: ["<p>64 : 216</p>", "<p>216 : 512</p>", 
                                "<p>125 : 343</p>", "<p>343 : 728</p>"],
                    options_hi: ["<p>64 : 216</p>", "<p>216 : 512</p>",
                                "<p>125 : 343</p>", "<p>343 : 728</p>"],
                    solution_en: "<p>56.(d) <strong>Pattern</strong> <br>n<sup>3</sup> : (n + 2)<sup>3</sup><br>4<sup>3</sup> : 6<sup>3</sup> = 64 : 216</p>\n<p>&rArr; 6<sup>3</sup> : 8<sup>3</sup> = 216 : 512<br>5<sup>3</sup> : 7<sup>3</sup> = 125 : 343</p>\n<p>&rArr; 7<sup>3</sup> : 9<sup>3</sup> = 343 : 729<br><strong>Option d is correct</strong></p>",
                    solution_hi: "<p>56.(d) <strong>पैटर्न </strong>:&nbsp;</p>\n<p>n<sup>3</sup> : (n + 2)<sup>3</sup><br>4<sup>3</sup> : 6<sup>3</sup> = 64 : 216</p>\n<p>&rArr; 6<sup>3</sup> : 8<sup>3</sup> = 216 : 512<br>5<sup>3</sup> : 7<sup>3</sup> = 125 : 343</p>\n<p>&rArr; 7<sup>3</sup> : 9<sup>3</sup> = 343 : 729</p>\n<p>विकल्प d सही है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57 If a man bought 6 pencils for ₹5, and sold them at 5 pencils for ₹6, then the gain percentage is_____________</p>",
                    question_hi: "<p>57. यदि एक व्यक्ति ₹5 में 6 पेंसिल खरीदता है, और उन्हें ₹6 में 5 पेंसिल की दर पर बेचता है, तो प्रतिशत लाभ __________है।</p>",
                    options_en: ["<p>43%</p>", "<p>41%</p>", 
                                "<p>42%</p>", "<p>44%</p>"],
                    options_hi: ["<p>43%</p>", "<p>41%</p>",
                                "<p>42%</p>", "<p>44%</p>"],
                    solution_en: "<p>57.(d) C.P of 6 pencils = ₹ 5<br>C.P of 30 pencils = ₹ 25<br>S.P of 5 pencils = ₹ 6<br>S.P of 30 pencils = ₹ 36<br>Gain% = <math display=\"inline\"><mfrac><mrow><mn>36</mn><mo>-</mo><mn>25</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 44%</p>",
                    solution_hi: "<p>57.(d) 6 पेंसिल का क्रय मूल्य = ₹ 5<br>30 पेंसिल का क्रय मूल्य = ₹ 25<br>5 पेंसिल का विक्रय मूल्य = ₹ 6<br>30 पेंसिल का विक्रय मूल्य = ₹ 36<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>36</mn><mo>-</mo><mn>25</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 = 44%</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. Which of the following ports lies on the east coast of India?</p>",
                    question_hi: "<p>58. निम्नलिखित में से कौन सा बंदरगाह भारत के पूर्वी तट पर स्थित है?</p>",
                    options_en: ["<p>Kandla</p>", "<p>Kochi</p>", 
                                "<p>Nhava Sheva</p>", "<p>Tuticorin</p>"],
                    options_hi: ["<p>कांडला</p>", "<p>कोच्चि</p>",
                                "<p>न्हावा शेवा</p>", "<p>तूतीकोरिन</p>"],
                    solution_en: "<p>58.(d)&nbsp;<strong>Tuticorin (Thoothukudi) -</strong> It is traditionally known for pearl fishing and shipping activities, production of salt, and other related business. This is a port city in the southern region of Tamilnadu. The major ports on the east coast are Ennore (TamilNadu), Chennai (TamilNadu), Haldia &amp; Kolkata (West Bengal), Paradip (Odisha), Visakhapatnam (Andhra Pradesh), and Port Blair (Andaman &amp; Nicobar Island).</p>",
                    solution_hi: "<p>58.(d) <strong>तूतीकोरिन (तूतूकुड़ी) </strong>- यह पारंपरिक रूप से मोती मछली पकड़ने और शिपिंग गतिविधियों, नमक के उत्पादन और अन्य संबंधित व्यवसाय के लिए जाना जाता है। यह तमिलनाडु के दक्षिणी क्षेत्र में एक बंदरगाह शहर है। पूर्वी तट पर प्रमुख बंदरगाह एन्नोर (तमिलनाडु), चेन्नई (तमिलनाडु), हल्दिया और कोलकाता (पश्चिम बंगाल), पारादीप (ओडिशा), विशाखापत्तनम (आंध्र प्रदेश), और पोर्ट ब्लेयर (अंडमान और निकोबार द्वीप) हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. Select the industry in which goods are produced in the home of the producer.</p>",
                    question_hi: "<p>59. उस उद्योग का चयन कीजिये जिसमें उत्पादक के घर में सामान का उत्पादन होता है।</p>",
                    options_en: ["<p>Large-scale industry</p>", "<p>Cottage industry</p>", 
                                "<p>Secondary industry</p>", "<p>Quaternary industry </p>"],
                    options_hi: ["<p>बड़े पैमाने का उद्योग</p>", "<p>कुटीर-उद्योग</p>",
                                "<p>दितीयक उद्योग</p>", "<p>चतुर्धातुक उद्योग</p>"],
                    solution_en: "<p>59.(b) <strong>Cottage industry.</strong> Other Examples - Carpentry, smithy, carpet, weaving, pottery, blanket making, stone carving. The heavy industries like steel, textile, and automobile manufacturing industry falls under the category of large-scale industry. The secondary industry sector (manufacturing industry) processes the raw materials supplied by primary industries. Quaternary industries are those that involve advanced technologies (generally in computers and communications).</p>",
                    solution_hi: "<p>59.(b) <strong>कुटीर-उद्योग। </strong>अन्य उदाहरण - बढ़ईगीरी, लोहार, कालीन, बुनाई, मिट्टी के बर्तन, कंबल बनाना, पत्थर पर नक्काशी। इस्पात, कपड़ा और ऑटोमोबाइल विनिर्माण उद्योग जैसे भारी उद्योग बड़े पैमाने के उद्योग की श्रेणी में आते हैं। द्वितीयक उद्योग क्षेत्र (विनिर्माण उद्योग) प्राथमिक उद्योगों द्वारा आपूर्ति किए गए कच्चे माल को संसाधित करता है। चतुर्धातुक उद्योग वे हैं जिनमें उन्नत प्रौद्योगिकियां (आमतौर पर कंप्यूटर और संचार में) शामिल होती हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. What will come next in the series?<br>D, E, Z, I, V, M, ?</p>",
                    question_hi: "<p>60. श्रृंखला में आगे क्या आएगा?<br>D, E, Z, I, V, M, ?</p>",
                    options_en: ["<p>T</p>", "<p>R</p>", 
                                "<p>S</p>", "<p>Q</p>"],
                    options_hi: ["<p>T</p>", "<p>R</p>",
                                "<p>S</p>", "<p>Q</p>"],
                    solution_en: "<p>60.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451963277.png\" alt=\"rId25\" width=\"167\" height=\"69\"></p>",
                    solution_hi: "<p>60.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451963277.png\" alt=\"rId25\" width=\"167\" height=\"69\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. 85L of a mixture contains milk and water in the ratio 27 : 7. How much more water should be added to get a new mixture containing milk and water in the ratio 3 : 1 ?</p>",
                    question_hi: "<p>61. एक मिश्रण के 85 लीटर में दूध और पानी का अनुपात 27 : 7 है। दूध और पानी का एक नया मिश्रण 3 : 1 के अनुपात में प्राप्त करने के लिए कितना अधिक पानी मिलाना चाहिए ?</p>",
                    options_en: ["<p>8 L</p>", "<p>5 L</p>", 
                                "<p>15 L</p>", "<p>13 L</p>"],
                    options_hi: ["<p>8 L</p>", "<p>5 L</p>",
                                "<p>15 L</p>", "<p>13 L</p>"],
                    solution_en: "<p>61.(b) Ratio of milk and water = 27 : 7<br>After adding water <br>M : W = 3 : 1<br>We added water but the quantity of milk remains same <br>M : W = 9 (3 : 1) = 27 : 9<br>Increased water = 9 - 7 = 2<br>A/Q ,<br>27 + 7 = 34 unit = 85 litre<br>2 unit = <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> &times; 2 = 5L</p>",
                    solution_hi: "<p>61.(b) दूध और पानी का अनुपात = 27 : 7<br>पानी डालने के बाद,<br>दूध : पानी = 3 : 1<br>हमने पानी डाला है इसलिए दूध की मात्रा समान रहेगा, <br>दूध : पानी = 9 (3 : 1) = 27 : 9<br>बढ़ा हुआ पानी = 9 - 7 = 2<br>प्रश्न के अनुसार,<br>27 + 7 = 34 इकाई = 85 इकाई <br>2 इकाई =<math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> &times; 2 = 5 लीटर</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. Read the given statement of courses of action carefully . Assuming that the information given in the statement is true, decide which of the given courses of action logically follow(s) from the statement.<br><strong>Statement:</strong><br>There is a huge increase in the migration of Citizens of Country X to foreign countries as the unemployment rate has been increasing in Country X.<br><strong>Courses of action:</strong><br>I. The citizens of country X should be provided with alternate sources of income.<br>II. Substantial fees should be imposed on all foreign travels by the citizens of country X.</p>",
                    question_hi: "<p>62. कार्य योजना के दिए गए कथन को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथन में दी गई जानकारी सत्य है, तय करें कि दिए गए कार्यों में से कौन सा कथन का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>देश X के नागरिकों के विदेशों में प्रवासन में भारी वृद्धि हुई है क्योंकि देश X में बेरोजगारी दर बढ़ रही है।<br><strong>कार्यवाही :</strong><br>I. देश x के नागरिकों को आय के वैकल्पिक स्रोत उपलब्ध कराए जाने चाहिए।<br>II. देश X के नागरिकों द्वारा सभी विदेश यात्राओं पर पर्याप्त शुल्क लगाया जाना चाहिए।</p>",
                    options_en: ["<p>Only I follows</p>", "<p>Neither I nor II follows</p>", 
                                "<p>Only II follows</p>", "<p>Both I and II follow</p>"],
                    options_hi: ["<p>केवल I अनुसरण करता हूं</p>", "<p>न तो I न ही II अनुसरण करता है</p>",
                                "<p>केवल II अनुसरण करता है</p>", "<p>I और II दोनों अनुसरण करते हैं</p>"],
                    solution_en: "<p>62.(a)<br>According to the statement, only I follow.</p>",
                    solution_hi: "<p>62.(a) कथन के अनुसार, केवल निष्कर्ष 1 अनुसरण करता है</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. Simplify the expression <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>25</mn></msqrt></math> + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><mn>0</mn><mo>.</mo><mn>0049</mn></mrow></msqrt></mstyle></math> - 0.3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><mn>0</mn><mo>.</mo><mn>09</mn></mrow></msqrt></mstyle></math></p>",
                    question_hi: "<p>63. व्यंजक को सरल कीजिए:</p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>25</mn></msqrt></math> + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><mn>0</mn><mo>.</mo><mn>0049</mn></mrow></msqrt></mstyle></math> - 0.3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><mn>0</mn><mo>.</mo><mn>09</mn></mrow></msqrt></mstyle></math></p>",
                    options_en: ["<p>0.1</p>", "<p>2.73</p>", 
                                "<p>5.05</p>", "<p>0.55</p>"],
                    options_hi: ["<p>0.1</p>", "<p>2.73</p>",
                                "<p>5.05</p>", "<p>0.55</p>"],
                    solution_en: "<p>63.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>25</mn></msqrt></math> + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><mn>0</mn><mo>.</mo><mn>0049</mn></mrow></msqrt></mstyle></math> - 0.3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><mn>0</mn><mo>.</mo><mn>09</mn></mrow></msqrt></mstyle></math><br>= 0.5 + 2 &times; 0.07 - 0.3 &times; 0.3 <br>= 0.5 + 0.14 - 0.09</p>\n<p>&rArr; 0.55</p>",
                    solution_hi: "<p>63.(d) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>0</mn><mo>.</mo><mn>25</mn></msqrt></math> + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><mn>0</mn><mo>.</mo><mn>0049</mn></mrow></msqrt></mstyle></math> - 0.3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><msqrt><mrow><mn>0</mn><mo>.</mo><mn>09</mn></mrow></msqrt></mstyle></math><br>= 0.5 + 2 &times; 0.07 - 0.3 &times; 0.3 <br>= 0.5 + 0.14 - 0.09</p>\n<p>&rArr; 0.55</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. In a certain code language, each word is given a number code. Accordingly, 1875 means &lsquo;wound that round Watch&rsquo;, 6143 means &lsquo;a cake is round&rsquo;, and 7321 means &lsquo;Watch a round Wheel&rsquo;. Find the code for &lsquo;Watch&rsquo;.</p>",
                    question_hi: "<p>64. एक निश्चित कूट भाषा में, प्रत्येक शब्द को एक आंकिक कूट के रूप में लिखा जाता है। तदनुसार, 1875 का अर्थ \'wound the round watch\' है, 6143 का अर्थ \'a cake is round\' है, और 7321 का अर्थ \'watch a round wheel\' है। उस कूट भाषा में \'watch\' का कूट क्या होगा ?</p>",
                    options_en: ["<p>8</p>", "<p>1</p>", 
                                "<p>5</p>", "<p>7</p>"],
                    options_hi: ["<p>8</p>", "<p>1</p>",
                                "<p>5</p>", "<p>7</p>"],
                    solution_en: "<p>64.(d)<br>1875 = Wound that <strong>round</strong> watch&hellip;&hellip;(i)<br>6<strong>1</strong>4<strong>3</strong> = <strong>a</strong> cake is <strong>round</strong>&hellip;&hellip;&hellip;&hellip;&hellip;..(ii)<br>7<strong>3</strong>2<strong>1</strong> = watch a <strong>round</strong> wheel&hellip;&hellip;&hellip;(iii)<br>&lsquo;Watch&rsquo; and 7 is common in (i) and (iii)<br>So, code of watch = 7</p>",
                    solution_hi: "<p>64.(d)<br>1875 = Wound that <strong>round</strong> watch&hellip;&hellip;(i)<br>6<strong>1</strong>4<strong>3</strong> = <strong>a</strong> cake is <strong>round</strong>&hellip;&hellip;&hellip;&hellip;&hellip;..(ii)<br>7<strong>3</strong>2<strong>1</strong> = watch a <strong>round</strong> wheel&hellip;&hellip;&hellip;(iii)<br>watch और 7 (i) और (iii) में उभयनिष्ठ है|<br>अत: watch का कोड = 7</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. A statement is given followed by two assumptions numbered I and II. You have to assume everything in the statement to be true and decide which of the assumptions is/are implicit in the statement<br><strong>Statement:</strong><br>Increase in the tax rates will hurt the pockets of the salaried middle class <br><strong>Assumptions:</strong><br>I. Increase in the prices of goods will hurt the poor too.<br>II. The middle class will reduce their extra expenditure to save money</p>",
                    question_hi: "<p>65. एक कथन और उसके बाद दो धारणाएं | और II दी गई हैं। आपको कथन में दी गई संपूर्ण जानकारी को सत्य मानते यह तय करना है दी गई धारणाओं में से कौन-सी कथन में निहित है/हैं<br><strong>कथन:</strong><br>कर की दरों में वृद्धि से वेतनभोगी मध्यम वर्ग की जेब पर असर पड़ेगा। <br><strong>धारणाएं:</strong><br>I. वस्तुओं की कीमतों में वृद्धि से गरीबों को भी नुकसान होगा।<br>II. मध्यम वर्ग पैसे बचाने के लिए अपने अतिरिक्त खर्च को कम करेगा।</p>",
                    options_en: ["<p>Only assumption I is implicit</p>", "<p>Neither assumption I nor II is implicit</p>", 
                                "<p>Both assumptions I and II are implicit</p>", "<p>Only assumption II is implicit</p>"],
                    options_hi: ["<p>केवल धारणा I निहित है</p>", "<p>न तो धारणा । और न ही ॥ निहित है</p>",
                                "<p>I और II दोनों धारणाएं निहित हैं</p>", "<p>केवल धारणा ॥ निहित है</p>"],
                    solution_en: "<p>65.(d) <strong>Statement :-</strong> Increase in the tax rates will hurt the pockets of the salaried middle class.<br>According to the statement, it can be&nbsp;concluded that an increase in tax rates will lead to reduction in the savings of salaried middle class. In order to keep the savings same, salaried middle class will try to reduce their extra expenditure.Only assumption (II) is implicit.</p>",
                    solution_hi: "<p>65.(d) <strong>कथन :- </strong>कर की दरों में वृद्धि से वेतनभोगी मध्यम वर्ग की जेब पर असर पड़ेगा।<br>कथन के अनुसार, यह निष्कर्ष निकाला जा सकता है कि कर दरों में वृद्धि से वेतनभोगी मध्यम वर्ग की बचत में कमी आएगी। बचत को समान रखने के लिए, वेतनभोगी मध्यम वर्ग अपने अतिरिक्त व्यय को कम करने का प्रयास करेगा। केवल धारणा (II) निहित है</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. If x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 5 , Then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math> will be:</p>",
                    question_hi: "<p>66. यदि x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 5 तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math> का मान ज्ञात कीजिये ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>66.(b)<br>x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 5&nbsp;</p>\n<p>&rArr; x<sup>2</sup> + 1 = 5x<br>Now, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><msup><mrow><mn>2</mn><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn><mo>)</mo><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>10</mn><mi>x</mi><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>5</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>66.(b)&nbsp;</p>\n<p>x + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 5&nbsp;</p>\n<p>&rArr; x<sup>2</sup> + 1 = 5x</p>\n<p>अब,&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><msup><mrow><mn>2</mn><mi>x</mi></mrow><mn>2</mn></msup><mo>+</mo><mn>2</mn><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>2</mn><mo>(</mo><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn><mo>)</mo><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>10</mn><mi>x</mi><mo>-</mo><mn>5</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi></mrow><mrow><mn>5</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. Vitamins and minerals are helpful for which of the following?</p>",
                    question_hi: "<p>67. विटामिन और खनिज निम्नलिखित में से किसके लिए सहायक होते हैं?</p>",
                    options_en: ["<p>Proper breathing</p>", "<p>Proper sweating</p>", 
                                "<p>Proper fat storage</p>", "<p>Carry out metabolic reactions in our body</p>"],
                    options_hi: ["<p>उचित श्वसन</p>", "<p>उचित पसीना</p>",
                                "<p>उचित वसा भंडारण</p>", "<p>हमारे शरीर में उपापचयी अभिक्रियाओं के लिए</p>"],
                    solution_en: "<p>67.(d) Vitamin A, D, E and K - Fat soluble. Vitamin B and C - Water soluble. <strong>Vitamins and their sources:</strong> Vitamin A - Milk, egg. Vitamin D - Cheese, butter, milk. Vitamin E - vegetable oils, lettuce, turnip leaves. Vitamin K - spinach and soybeans. Vitamin B - Seafood, milk, meat, peas. <strong>Minerals and their sources:</strong> Iron - Meat, fish, liver, eggs, turnip, germinating wheat grains and yeast. Calcium - Milk. Phosphorus - Meat, egg, fish, whole grains. Potassium - Green and yellow vegetables. Sodium - meat and milk. Iodine - Iodised salt, seafood and water. Fluoride - Coffee, spinach, onion and tea. Copper - Grains, nuts and chocolate. Zinc - Meat, eggs and fish. Chloride - Meat, milk and fish.</p>",
                    solution_hi: "<p>67.(d) विटामिन A, D, E और K - वसा में&nbsp;घुलनशील होते है । विटामिन B और C - जल में घुलनशील होते है। <strong>विटामिन और उनके स्रोत</strong>: विटामिन A - दूध, अंडा। विटामिन D - पनीर, मक्खन, दूध। विटामिन E - वनस्पति तेल, सलाद, शलजम के पत्ते। विटामिन K - पालक और सोयाबीन। विटामिन B - समुद्री भोजन, दूध, मांस, मटर। <strong>खनिज और उनके स्रोत: </strong>आयरन - मांस, मछली, लिवर, अंडे, शलजम, अंकुरित गेहूं के दाने और खमीर। कैल्शियम - दूध, फास्फोरस - मांस, अंडा, मछली, साबुत अनाज। पोटैशियम - हरी और पीली सब्जियाँ। सोडियम - मांस और दूध। आयोडीन - आयोडीन युक्त नमक, समुद्री भोजन और जल। फ्लोराइड - कॉफी, पालक, प्याज और चाय। कॉपर (ताँबा) - अनाज, मेवे और चॉकलेट। जिंक - मांस, अंडे और मछली। क्लोराइड - मांस, दूध और मछली।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. The present worth of Rs. 338 due in 2 years at 4% per annum compound interest is...</p>",
                    question_hi: "<p>68. 2 वर्षों में 4% प्रति वर्ष चक्रवृद्धि ब्याज पर 338 रूपये का वर्तमान मूल्य क्या है ?</p>",
                    options_en: ["<p>Rs. 312.50</p>", "<p>Rs. 294.00</p>", 
                                "<p>Rs. 365.58</p>", "<p>Rs. 350.50</p>"],
                    options_hi: ["<p>Rs. 312.50</p>", "<p>Rs. 294.00</p>",
                                "<p>Rs. 365.58</p>", "<p>Rs. 350.50</p>"],
                    solution_en: "<p>68.(a) R = 4% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math><br>Let P = 625<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451963603.png\" alt=\"rId26\" width=\"119\" height=\"107\"><br>CI in 2 years = 25 &times; 2 + 1 = 51<br>A = 625 + 51 = 676<br>676 unit = 338<br>P = 625 unit = <math display=\"inline\"><mfrac><mrow><mn>338</mn></mrow><mrow><mn>676</mn></mrow></mfrac></math> &times; 625 = Rs 312.5</p>",
                    solution_hi: "<p>68.(a) R = 4% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> <br>माना मूलधन ​= 625<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451963822.png\" alt=\"rId27\" width=\"118\" height=\"118\"><br>2 वर्षों में चक्रवृद्धि ब्याज <br>= 25 &times; 2 + 1 = 51<br>राशि = 625 + 51 = 676 &rArr; 676 इकाई = 338<br>मूलधन = 625 इकाई = <math display=\"inline\"><mfrac><mrow><mn>338</mn></mrow><mrow><mn>676</mn></mrow></mfrac></math> &times; 625 = Rs 312.5</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.<br>M N L_M N L L_ _L L M_L L_N L L_N</p>",
                    question_hi: "<p>69. अक्षरों के उस संयोजन का चयन करें जो दी गई श्रृंखला के रिक्त स्थान में क्रम से रखने पर श्रृंखला को पूरा करेगा।<br>M N L_M N L L_ _L L M_L L_N L L_N</p>",
                    options_en: ["<p>N M N M M L</p>", "<p>L N M N M M</p>", 
                                "<p>L M N N M M</p>", "<p>L N N M M M</p>"],
                    options_hi: ["<p>N M N M M L</p>", "<p>L N M N M M</p>",
                                "<p>L M N N M M</p>", "<p>L N N M M M</p>"],
                    solution_en: "<p>69.(c) <br>MNL<strong>L</strong>/MNLL/<strong>MN</strong>LL/ M<strong>N</strong>LL / <strong>M</strong>NLL /<strong>M</strong>N</p>",
                    solution_hi: "<p>69.(c) <br>MNL<strong>L</strong>/MNLL/<strong>MN</strong>LL/ M<strong>N</strong>LL / <strong>M</strong>NLL /<strong>M</strong>N</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. COBOL is an example of ________ generation programming language.</p>",
                    question_hi: "<p>70. COBOL ________ पीढ़ी की प्रोग्रामिंग भाषा का एक उदाहरण है</p>",
                    options_en: ["<p>1st</p>", "<p>4th</p>", 
                                "<p>2nd</p>", "<p>3rd</p>"],
                    options_hi: ["<p>1st</p>", "<p>4th</p>",
                                "<p>2nd</p>", "<p>3rd</p>"],
                    solution_en: "<p>70.(d) <strong>3rd</strong>. 1st Generation of Computers, Programming language &ndash; machine language, 2nd Generation -assembly language, 3rd Generation-high level language (FORTRAN, BASIC, Pascal, COBOL, C, etc.), 4th generation-high level language (Python, JavaScript, Rust, Kotlin, etc.)</p>",
                    solution_hi: "<p>70.(d)<strong> 3rd </strong>| कंप्यूटर की पहली जनरेशन, प्रोग्रामिंग भाषा - मशीन भाषा (programming language- machine language), दूसरी जनरेशन - असेंबली भाषा (assembly language), तीसरी जनरेशन -उच्च स्तरीय भाषा- high level language (फोरट्रान (FORTRAN), बेसिक (BASIC), पास्कल (PASCAL), कोबोल (COBOL), सी (C), आदि, चौथी जनरेशन -उच्च स्तरीय भाषा- high level language (पायथन (python), जावास्क्रिप्ट (javascript), रस्ट (rust), कोटलिन (kotlin), आदि ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. The Fundamental Duties of citizens were added to the Constitution of India, upon the recommendations of which of the following committees?</p>",
                    question_hi: "<p>71. निम्नलिखित में से किस समिति की सिफारिशों पर नागरिकों के मौलिक कर्तव्य भारत के संविधान में जोड़े गए थे?</p>",
                    options_en: ["<p>Raja Chelliah Committee</p>", "<p>Santhanam Committee</p>", 
                                "<p>Kelkar Committee</p>", "<p>Swaran Singh Committee</p>"],
                    options_hi: ["<p>राजा चेलैया समिति</p>", "<p>संथानम समिति</p>",
                                "<p>केलकर समिति</p>", "<p>स्वर्ण सिंह समिति</p>"],
                    solution_en: "<p>71.(d) <strong>Swaran Singh Committee. </strong>Fundamental Duties added by 42nd Constitutional Amendment Act, 1976. The <strong>Raja Chelliah Committee</strong> (Tax Reforms Committee) constituted in 1991 to recommend reforms in direct and indirect taxes prevailing in India. <strong>Kelkar Committee</strong>, chaired by Dr. Vijay Kelkar in 2002, recommended direct tax reforms like increasing the income tax exemption limit and abolishing long-term capital gains tax and wealth tax. The <strong>Santhanam Committee</strong> - appointed in 1962 to inquire into allegations of corruption against certain public servants.</p>",
                    solution_hi: "<p>71.(d) <strong>स्वर्ण सिंह समिति।</strong> 42वें संवैधानिक संशोधन अधिनियम, 1976 द्वारा मौलिक कर्तव्य जोड़े गए। भारत में प्रचलित प्रत्यक्ष और अप्रत्यक्ष करों में सुधार की सिफारिश करने के लिए 1991 में <strong>राजा चेलैया समिति</strong> (कर सुधार समिति) का गठन किया गया। 2002 में डॉ. विजय केलकर की अध्यक्षता में <strong>केलकर समिति </strong>ने आयकर छूट सीमा बढ़ाने और दीर्घकालिक पूंजीगत लाभ कर और संपत्ति कर को समाप्त करने जैसे प्रत्यक्ष कर सुधारों की सिफारिश की। <strong>संथानम समिति</strong> - कुछ लोक सेवकों के खिलाफ भ्रष्टाचार के आरोपों की जांच के लिए 1962 में इस समिति का गठन किया गया ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. Note : The following lines show the percentage of the number of eligible candidates in the examination compared to the total number of candidates present during the six-year period 2001 to 2006.<br>Which pair of below given years had a lesser percentage difference in the percentage of candidates who were eligible?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451963960.png\" alt=\"rId28\" width=\"263\" height=\"169\"></p>",
                    question_hi: "<p>72. नोट : निम्नलिखित पंक्तियाँ छह साल की अवधि 2001 से 2006 के दौरान उपस्थित उम्मीदवारों की कुल संख्या की तुलना में परीक्षा में योग्य उम्मीदवारों की संख्या का प्रतिशत दर्शाती हैं। नीचे दिए गए वर्षों की किस जोड़ी में योग्य उम्मीदवारों के प्रतिशत में कम अंतर था?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451963960.png\" alt=\"rId28\" width=\"238\" height=\"153\"></p>",
                    options_en: ["<p>2003 - 2004</p>", "<p>2004 - 2005</p>", 
                                "<p>2002 - 2003</p>", "<p>2001 - 2002</p>"],
                    options_hi: ["<p>2003 - 2004</p>", "<p>2004 - 2005</p>",
                                "<p>2002 - 2003</p>", "<p>2001 - 2002</p>"],
                    solution_en: "<p>72.(a)<br>Percentage difference in 2001 <math display=\"inline\"><mo>-</mo></math> 2002 &rarr; (48 - 34) = 14<br>Percentage difference in 2002 <math display=\"inline\"><mo>-</mo></math> 2003 &rarr; (59 - 48) = 11<br>Percentage difference in 2003 <math display=\"inline\"><mo>-</mo></math> 2004 &rarr; (60 - 59) = 1<br>Percentage difference in 2004 <math display=\"inline\"><mo>-</mo></math> 2005 &rarr; (60 - 40) = 20<br>Clearly , there is lesser percentage change in 2003 <math display=\"inline\"><mo>-</mo></math> 2004 .</p>",
                    solution_hi: "<p>72.(a)<br>2001 - 2002 में प्रतिशत अंतर&nbsp;&rarr; (48 - 34) = 14 <br>2002 - 2003 में प्रतिशत अंतर&nbsp;&rarr; (59 - 48) = 11<br>2003 - 2004 में प्रतिशत अंतर &rarr; (60 - 59) = 1<br>2004 - 2005 में प्रतिशत अंतर&nbsp;&rarr; (60 - 40) = 20<br>स्पष्ट रूप से, 2003 - 2004 में कम प्रतिशत परिवर्तन हुआ है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. In February 2022 India won a record-extending fifth U-19 World Cup title, beating which country by four wickets in the final ?</p>",
                    question_hi: "<p>73. फरवरी 2022 में, भारत ने फाइनल में किस देश को चार विकेट से हराकर पांचवां अंडर-19 विश्व कप ख़िताब जीतने का रिकॉर्ड बनाया?</p>",
                    options_en: ["<p>England</p>", "<p>Pakistan</p>", 
                                "<p>Canada</p>", "<p>Australia</p>"],
                    options_hi: ["<p>इंग्लैंड</p>", "<p>पाकिस्तान</p>",
                                "<p>कनाडा</p>", "<p>ऑस्ट्रेलिया</p>"],
                    solution_en: "<p>73.(a) <strong>England.</strong> U-19 World Cup 2022 hosted by countries: Guyana, St Kitts, and Nevis, Trinidad and Tobago, and Antigua and Barbuda. India won a total of five titles (2000, 2008, 2012, 2018, 2022). It was started in 1988 and Organized by the International Cricket Council (Headquarter - Dubai). Board of Control for Cricket in India (BCCI) was set up in December, 1928, (Headquarter - Mumbai).</p>",
                    solution_hi: "<p>73.(a) <strong>इंग्लैंड।</strong> U-19 विश्व कप 2022 की मेजबानी इन देशों द्वारा की जाएगी: गुयाना, सेंट किट्स और नेविस, त्रिनिदाद और टोबैगो, और एंटीगुआ और बारबुडा। भारत ने कुल पांच खिताब (2000, 2008, 2012, 2018, 2022) जीते। इसकी शुरुआत 1988 में हुई थी और इसका आयोजन अंतर्राष्ट्रीय क्रिकेट परिषद (मुख्यालय - दुबई) द्वारा किया गया था। भारतीय क्रिकेट कंट्रोल बोर्ड (BCCI) की स्थापना दिसंबर, 1928, (मुख्यालय - मुंबई) को की गई थी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. Which movement was negative enough to be peaceful but positive&nbsp;enough to be effective?</p>",
                    question_hi: "<p>74. कौन सा आंदोलन शांतिपूर्ण होने के लिए पर्याप्त नकारात्मक था लेकिन प्रभावी होने के लिए पर्याप्त सकारात्मक था?</p>",
                    options_en: ["<p>Non-Cooperation Movement</p>", "<p>Swadeshi Movement</p>", 
                                "<p>Quit India Movement</p>", "<p>Khilafat Movement</p>"],
                    options_hi: ["<p>असहयोग आंदोलन</p>", "<p>स्वदेशी आंदोलन</p>",
                                "<p>भारत छोड़ो आंदोलन</p>", "<p>खिलाफत आंदोलन</p>"],
                    solution_en: "<p>74.(a)<strong> The Non-cooperation Movement </strong>was launched on 5th September 1920 by the Indian National Congress (INC) under the leadership of Mahatma Gandhi in the Congress session in Calcutta. Swadeshi Movement (1905), Quit India Movement (1942), Khilafat Movement (1919-24).</p>",
                    solution_hi: "<p>74.(a) <strong>असहयोग आंदोलन</strong> 5 सितंबर 1920 को कलकत्ता में कांग्रेस सत्र में महात्मा गांधी के नेतृत्व में भारतीय राष्ट्रीय कांग्रेस (INC) द्वारा शुरू किया गया था। स्वदेशी आंदोलन (1905), भारत छोड़ो आंदोलन (1942), खिलाफत आंदोलन (1919-24)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "<p>75. A number when decreased by 22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% becomes 217. The number is</p>",
                    question_hi: "<p>75. एक संख्या जब 22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% कम हो जाती है तो 217 बन जाती है। संख्या ज्ञात कीजिये ?</p>",
                    options_en: ["<p>420</p>", "<p>280</p>", 
                                "<p>212</p>", "<p>315</p>"],
                    options_hi: ["<p>420</p>", "<p>280</p>",
                                "<p>212</p>", "<p>315</p>"],
                    solution_en: "<p>75.(b) A number, when decreased by 22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%, becomes 217.<br>As we know, 22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>40</mn></mfrac></math><br>i.e if the initial number was 40 units the after decrement it becomes (40 - 9) = 31 units<br>Now 31 units &rarr; 217 ,<br>So, the initial number = 40 units <br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>217</mn><mn>31</mn></mfrac></math> &times; 40 = 280</p>",
                    solution_hi: "<p>75.(b) एक संख्या, जब 22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% कम हो जाती है, तो वह 217 हो जाती है।<br>जैसा कि हम जानते हैं, 22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>40</mn></mfrac></math><br>अर्थात, यदि प्रारंभिक संख्या 40 इकाई थी तो घटने के बाद यह (40 - 9) = 31 इकाई हो जाती है<br>अब 31 इकाइयाँ &rarr; 217,<br>तो, प्रारंभिक संख्या, 40 इकाइयाँ &rarr; <math display=\"inline\"><mfrac><mrow><mn>217</mn></mrow><mrow><mn>31</mn></mrow></mfrac></math> &times; 40 = 280</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. Find the mean of the following data:<br><img src=\"data:image/png;base64,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\" width=\"199\" height=\"41\"></p>",
                    question_hi: "<p>76. निम्नलिखित डेटा का माध्य ज्ञात कीजिये ?<br><img src=\"data:image/png;base64,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\" width=\"199\" height=\"41\"></p>",
                    options_en: ["<p>20</p>", "<p>30</p>", 
                                "<p>28</p>", "<p>25</p>"],
                    options_hi: ["<p>20</p>", "<p>30</p>",
                                "<p>28</p>", "<p>25</p>"],
                    solution_en: "<p>76.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451964117.png\" alt=\"rId29\" width=\"125\" height=\"156\"><br>&sum;f = 106&nbsp; &nbsp; &sum;fx = 2650<br>Mean = <math display=\"inline\"><mfrac><mrow><mn>2650</mn></mrow><mrow><mn>106</mn></mrow></mfrac></math> = 25</p>",
                    solution_hi: "<p>76.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451964117.png\" alt=\"rId29\" width=\"128\" height=\"160\"><br>&sum;f = 106&nbsp; &nbsp; &sum;fx = 2650<br>माध्य =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>2650</mn></mrow><mrow><mn>106</mn></mrow></mfrac></math>= 25</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77. Ao is a prominent language spoken in the state of _______.</p>",
                    question_hi: "<p>77. आओ (Ao) , _______ राज्य में बोली जाने वाली एक प्रमुख भाषा है।</p>",
                    options_en: ["<p>Chhattisgarh</p>", "<p>Tripura</p>", 
                                "<p>Nagaland</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>छत्तीसगढ़</p>", "<p>त्रिपुरा</p>",
                                "<p>नागालैंड</p>", "<p>ओडिशा</p>"],
                    solution_en: "<p>77.(c) <strong>Nagaland.</strong> The Ao or Central Naga languages are a small family of Sino-Tibetan languages spoken by various Naga peoples of Nagaland. Each tribe in Nagaland has its own dialect. Chokri, Khezha, Chang, Zeme, Yachumi are other languages spoken in Nagaland. Nagaland state has different tribes named Angami, Chakhesang, Chang, Khiamniungan, Kuki, Konyak, Kachari, Lotha, Phom, Pochury, Rengma, Sumi, Sangtam, Yimchunger, Ziliang.</p>",
                    solution_hi: "<p>77.(c) <strong>नागालैंड । </strong>आओ या मध्य नागा भाषाएँ नागालैंड के विभिन्न नागा लोगों द्वारा बोली जाने वाली चीनी-तिब्बती भाषाओं का एक छोटा परिवार है। नागालैंड में प्रत्येक जनजाति की अपनी बोली है। नागालैंड में चोकरी, खेझा, चांग, ज़ेमे, याचुमी अन्य भाषाएँ बोली जाती हैं। नागालैंड राज्य में अंगामी, चखेसांग, चांग, ​​खियमनिउंगन, कुकी, कोन्याक, कचारी, लोथा (ल्होता), फोम, पोचुरी, रेंगमा, सुमी, संगताम , यिमचुंगेर, ज़िलियांग नाम की अलग-अलग जनजातियाँ हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. Classical musician TR Mahalingam is associated with which musical instrument?</p>",
                    question_hi: "<p>78. शास्त्रीय संगीतकार टीआर महालिंगम किस वाद्य यंत्र से संबंधित हैं?</p>",
                    options_en: ["<p>Mridang</p>", "<p>Flute</p>", 
                                "<p>Tabla</p>", "<p>Santoor</p>"],
                    options_hi: ["<p>मृदंग</p>", "<p>बाँसुरी</p>",
                                "<p>तबला</p>", "<p>संतूर</p>"],
                    solution_en: "<p>78.(b) <strong>Flute </strong>(Wind instrument): It is an aerophone, producing sound with a vibrating column of air. Other famous Indian flutists: Pandit Hariprasad Chaurasia, Pannalal Ghosh, Pandit Ronu Majumdar. Mridang: A percussion instrument made of leather and jackwood. Tabla: The most famous percussion instrument of North India. Santoor: A stringed instrument made of wood, steel, and bamboo.</p>",
                    solution_hi: "<p>78.(b) <strong>बाँसुरी</strong> (वायु यंत्र): यह एक एयरोफोन है, जो हवा के कंपन स्तंभ के साथ ध्वनि उत्पन्न करता है। अन्य प्रसिद्ध भारतीय बांसुरीवादक: पंडित हरिप्रसाद चौरसिया, पन्नालाल घोष, पंडित रोनू मजूमदार। मृदंग: चमड़े और जैकवुड से बना एक ताल वाद्य। तबला: उत्तर भारत का सबसे प्रसिद्ध तालवाद्य। संतूर: लकड़ी, स्टील और बांस से बना एक तार वाला वाद्य यंत्र।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. Where was Swami Mahavir born?</p>",
                    question_hi: "<p>79. स्वामी महावीर का जन्म कहाँ हुआ था?</p>",
                    options_en: ["<p>Pataliputra</p>", "<p>Lumbini</p>", 
                                "<p>Kundagram</p>", "<p>Pavapuri</p>"],
                    options_hi: ["<p>पाटलिपुत्र</p>", "<p>लुंबिनी</p>",
                                "<p>कुंडग्राम</p>", "<p>पावापुरी</p>"],
                    solution_en: "<p>79.(c) <strong>Kundagram. Mahavira</strong>: Birth place - Kundagrama (Vaishali), Birth Name - Vardhamana (24th tirthankara, Symbol - Lion), Clan - Jnatrika, Enlightenment - Beneath Sala tree, First sermon - Pava, Spiritual knowledge - Kaivalya (conquered misery and happiness) at the age of 42 years. <strong>Mahavira </strong>is considered as the holy person who gave Jainism its present form. 1st tirthankara of Jainism - Rishabnatha (symbol - Bull). 1st Jain council - Patliputra presided by sthulbhadra, 2nd Jain council - Vallabhi presided by Devardhi Kshmasramana. Siddhartha Gautama, the Lord Buddha was born in Lumbini.</p>",
                    solution_hi: "<p>79.(c) <strong>कुंडग्राम। महावीर:</strong> जन्म स्थान - कुंडग्राम (वैशाली), जन्म का नाम - वर्धमान (24वां तीर्थंकर, प्रतीक - सिंह), वंश - जनत्रिका, आत्मज्ञान - साल वृक्ष के नीचे, पहला उपदेश - पावा, आध्यात्मिक ज्ञान - 42 वर्ष की आयु में कैवल्य (दुःख और सुख पर विजय प्राप्त की)। महावीर को वह पवित्र व्यक्ति माना जाता है, जिसने जैन धर्म को उसका वर्तमान स्वरूप प्रदान किया । जैन धर्म के प्रथम तीर्थंकर - ऋषभनाथ (प्रतीक - बैल)। प्रथम जैन परिषद - स्थूलभद्र की अध्यक्षता में पाटलिपुत्र, द्वितीय जैन परिषद - वल्लभी की अध्यक्षता देवार्धि क्षमाश्रमण ने की। सिद्धार्थ गौतम, भगवान बुद्ध का जन्म लुम्बिनी में हुआ था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. Study the Venn Diagram given below and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451964400.png\" alt=\"rId30\" width=\"211\" height=\"105\"> <br>How many female engineers are drivers but NOT doctors?</p>",
                    question_hi: "<p>80. निम्नांकित वेन आरेख का ध्यानपूर्वक अध्ययन करें, और उसके आधार पर पूछे गए प्रश्न का उत्तर दें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451964701.png\" alt=\"rId31\" width=\"198\" height=\"105\"> <br>कितनी महिला अभियंता (female engineers), ड्राइवर (drivers) हैं, लेकिन डॉक्टर (doctors) नहीं हैं?</p>",
                    options_en: ["<p>22</p>", "<p>8</p>", 
                                "<p>13</p>", "<p>19</p>"],
                    options_hi: ["<p>22</p>", "<p>8</p>",
                                "<p>13</p>", "<p>19</p>"],
                    solution_en: "<p>80.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451964400.png\" alt=\"rId30\" width=\"211\" height=\"105\"><br>From the above figure, we can see that only 22 female engineers are drivers but NOT doctors.</p>",
                    solution_hi: "<p>80.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451964701.png\" alt=\"rId31\" width=\"206\" height=\"109\"><br>ऊपर दिए गए आंकड़े से हम देख सकते हैं कि केवल 22 महिला अभियंता ड्राइवर हैं लेकिन डॉक्टर नहीं हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. Which option will replace the question mark in the below series?<br>A25 : Y1 : : L14 : ?</p>",
                    question_hi: "<p>81. नीचे दी गई श्रृंखला में प्रश्नवाचक चिन्ह के स्थान पर कौन सा विकल्प आएगा?<br>A25 : Y1 : : L14 : ?</p>",
                    options_en: ["<p>H13</p>", "<p>13 H</p>", 
                                "<p>N12</p>", "<p>M12</p>"],
                    options_hi: ["<p>H13</p>", "<p>13 H</p>",
                                "<p>N12</p>", "<p>M12</p>"],
                    solution_en: "<p>81.(c) A &rarr; Opposite place value - 1 = Y And</p>\n<p>25 &rarr; Opposite place value Number - 1&nbsp;<br>= 2 - 1 = 1<br>Similarly , L14 : N12</p>",
                    solution_hi: "<p>81.(c) A &rarr; विपरीत स्थान मान - 1 = Y और <br>25 &rarr; विपरीत स्थान मान - 1 = 2 - 1 = 1<br>उसी प्रकार, L14 : N12</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. If the volume of a sphere is divided by its surface area, the result is 9 cm. The radius (in cm) of the sphere is _______________</p>",
                    question_hi: "<p>82. यदि एक गोले के आयतन को उसके पृष्ठीय क्षेत्रफल से विभाजित किया जाता है, तो परिणाम 9 cm प्राप्त होता है। गोले की त्रिज्या (cm में) ज्ञात कीजिए ।</p>",
                    options_en: ["<p>27</p>", "<p>81</p>", 
                                "<p>4.5</p>", "<p>18</p>"],
                    options_hi: ["<p>27</p>", "<p>81</p>",
                                "<p>4.5</p>", "<p>18</p>"],
                    solution_en: "<p>822.(a)<br>Volume of the sphere = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;R<sup>3</sup><br>Surface area of sphere = 4&pi;R<sup>2</sup><br>According to question, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>R</mi><mn>3</mn></msup></mrow><mrow><mn>4</mn><mi>&#960;</mi><msup><mi>R</mi><mn>2</mn></msup></mrow></mfrac></math> = 9</p>\n<p>&rArr; R = 27 cm</p>",
                    solution_hi: "<p>82.(a) गोले का आयतन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;R<sup>3</sup><br>गोले का पृष्ठीय क्षेत्रफल = 4&pi;R<sup>2</sup><br>प्रश्न के अनुसार , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi>&#960;</mi><msup><mi>R</mi><mn>3</mn></msup></mrow><mrow><mn>4</mn><mi>&#960;</mi><msup><mi>R</mi><mn>2</mn></msup></mrow></mfrac></math> = 9</p>\n<p>&rArr; R = 27 cm</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "<p>83. Who among the following served the shortest tenure as the Prime Minister of India?</p>",
                    question_hi: "<p>83. निम्नलिखित में से किसने भारत के प्रधान मंत्री के रूप में सबसे कम कार्यकाल तक कार्य किया?</p>",
                    options_en: ["<p>HD Deva Gowda</p>", "<p>VP Singh</p>", 
                                "<p>Chandra Shekhar</p>", "<p>IK Gujral</p>"],
                    options_hi: ["<p>एच. डी. देवगौड़ा</p>", "<p>वी. पी. सिंह</p>",
                                "<p>चंद्र शेखर</p>", "<p>आई. के. गुजराल</p>"],
                    solution_en: "<p>83.(c) <strong>Chandra Shekha</strong>r. Shri Gulzari Lal Nanda (11 January 1966 - 24 January 1966 and 27 May 1964 - 9 June 1964) served the shortest tenure as PM. H D Deva Gowda (June 1, 1996 - April 21, 1997) - 11th Prime Minister of India. V. P. Singh (December 2, 1989 - November 10, 1990) - 7th Prime Minister of India. Chandra Shekhar (10 November 1990 and 21 June 1991) - 8th Prime Minister of India. The longest-serving prime minister was Jawaharlal Nehru, (16 years and 286 days).</p>",
                    solution_hi: "<p>83.(c) <strong>चंद्र शेखर।</strong> श्री गुलज़ारी लाल नंदा (11 जनवरी 1966 - 24 जनवरी 1966 और 27 मई 1964 - 9 जून 1964) ने प्रधानमंत्री के रूप में सबसे छोटा कार्यकाल पूरा किया। एच डी देवगौड़ा (1 जून, 1996 - 21 अप्रैल, 1997) - भारत के 11वें प्रधान मंत्री। वी. पी. सिंह (2 दिसंबर, 1989 - 10 नवंबर, 1990) - भारत के 7वें प्रधान मंत्री। चन्द्रशेखर (10 नवंबर 1990 और 21 जून 1991) - भारत के 8वें प्रधान मंत्री। सबसे लंबे समय तक कार्यकाल पर रहने वाले प्रधान मंत्री जवाहरलाल नेहरू (16 वर्ष और 286 दिन) थे।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. In 1931, the first Indian movie with sound was released . Name the film.</p>",
                    question_hi: "<p>84. 1931 में, ध्वनि के साथ प्रथम भारतीय फिल्म रिलीज़ हुई थी। फिल्म का नाम बताइए।</p>",
                    options_en: ["<p>Jungle Ka Jawan</p>", "<p>Raja harishchandra</p>", 
                                "<p>Kisaan Kanya</p>", "<p>Alam Ara</p>"],
                    options_hi: ["<p>जंगल का जवान</p>", "<p>राजा हरिश्चंद्र</p>",
                                "<p>किसान कन्या</p>", "<p>आलम आरा</p>"],
                    solution_en: "<p>84.(d) <strong>Alam Ara. </strong>It is an Indian Hindustani-language historical fantasy film directed and produced by Ardeshir Irani. Famous movies and their directors- Jungle Ka Jawan (1938, Chunilal Parekh); Raja harishchandra (first full length film of India, 1913, Dadasaheb Phalke), Kisan Kanya (India&rsquo;s first indigenously made film in colour) - (1937, Moti B. Gidwani).</p>",
                    solution_hi: "<p>84.(d) <strong>आलम आरा।</strong> यह एक भारतीय हिंदुस्तानी भाषा की ऐतिहासिक काल्पनिक फिल्म है, जिसका निर्देशन और निर्माण अर्देशिर ईरानी द्वारा किया गया । प्रसिद्ध फ़िल्में और उनके निर्देशक - जंगल का जवान (1938, चुन्नीलाल पारेख); राजा हरिश्चंद्र (भारत की प्रथम पूर्ण लंबाई वाली फिल्म, 1913, दादा साहब फाल्के), किसान कन्या (किसान कन्या (भारत की पहली स्वदेश निर्मित रंगीन फिल्म) - (1937, मोती बी. गिडवानी)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. Line ABC is a tangent to a circle at B. PQ || AC and &ang;QBC = 70&deg;. &ang;PBQ is = ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451965006.png\" alt=\"rId32\" width=\"206\" height=\"126\"></p>",
                    question_hi: "<p>85. रेखा ABC, B पर एक वृत्त की स्पर्श रेखा है। PQ || AC और &ang;QBC = 70&deg; तो &ang;PBQ = ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451965006.png\" alt=\"rId32\" width=\"206\" height=\"126\"></p>",
                    options_en: ["<p>20&deg;</p>", "<p>70&deg;</p>", 
                                "<p>40&deg;</p>", "<p>110&deg;</p>"],
                    options_hi: ["<p>20&deg;</p>", "<p>70&deg;</p>",
                                "<p>40&deg;</p>", "<p>110&deg;</p>"],
                    solution_en: "<p>85.(c) as PQ || AC<br>&there4; &ang;QBC = &ang;PQB = 70&deg; (alternate interior angle), in △PBQ<br>&ang;PBQ = 180&deg; - (70&deg; + 70&deg;) = 40&deg;</p>",
                    solution_hi: "<p>85.(c) क्योंकि PQ || AC<br>&there4; &ang;QBC = &ang;PQB = 70 (अंतः एकांतर कोण)<br>△PBQ में &ang;PBQ<br>= 180&deg; - (70&deg; + 70&deg;) = 40&deg;</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. Where in India are Sundari trees that provide durable and hard timber found?</p>",
                    question_hi: "<p>86. टिकाऊ और कठोर लकड़ी प्रदान करने वाले सुंदरी वृक्ष भारत में कहाँ पाए जाते हैं?</p>",
                    options_en: ["<p>Tropical Deciduous Forests</p>", "<p>Mangrove Forests</p>", 
                                "<p>Montane Forests</p>", "<p>Tropical Evergreen Forests</p>"],
                    options_hi: ["<p>उष्णकटिबंधीय पर्णपाती वन</p>", "<p>मैंग्रोव वन</p>",
                                "<p>पर्वतीय वन</p>", "<p>उष्णकटिबंधीय सदाबहार वन</p>"],
                    solution_en: "<p>86.(b) <strong>Mangrove Forests</strong> - Sundari Tree (Scientific name - Heritiera fomes) - It is a small tree or shrub that grows along coastlines, taking root in salty sediments, often underwater, found abundantly in the Sundarbans delta (UNESCO World Heritage Site, 1987). <strong>Mangroves in India</strong> - West Bengal (42.45%); Gujarat (23.66%); Andaman and Nicobar Islands (12.39%); Pichavaram in Tamil Nadu; backwaters in Kerala; Deltas of the Ganges, Mahanadi, Krishna, Godavari, and the Cauvery rivers.</p>",
                    solution_hi: "<p>86.(b) <strong>मैंग्रोव वन </strong>- सुंदरी वृक्ष (वैज्ञानिक नाम - हेरिटिएरा फोम्स) - यह एक छोटा पेड़ या झाड़ी है जो समुद्र तट के किनारे उगता है और अक्सर पानी के नीचे खारा तलछट में जड़ें जमा लेता है, जो सुंदरवन डेल्टा (UNESCO विश्व धरोहर स्थल, 1987) में प्रचुर मात्रा में पाया जाता है। <strong>भारत में मैंग्रोव</strong> - पश्चिम बंगाल (42.45%); गुजरात (23.66%); अंडमान और निकोबार द्वीप समूह (12.39%); तमिलनाडु में पिचवरम; केरल में बैकवाटर; गंगा, महानदी, कृष्णा, गोदावरी और कावेरी नदियों के डेल्टा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. In Vaishnavism, how many avatars or incarnation of deity were recognised?</p>",
                    question_hi: "<p>87. वैष्णव धर्म में, कितने अवतारों या देवता के अवतार को मान्यता दी गई थी?</p>",
                    options_en: ["<p>5</p>", "<p>7</p>", 
                                "<p>9</p>", "<p>10</p>"],
                    options_hi: ["<p>5</p>", "<p>7</p>",
                                "<p>9</p>", "<p>10</p>"],
                    solution_en: "<p>87.(d) <strong>10. </strong>Avatar is a concept within Hinduism that in Sanskrit literally means \"descent\". Lord Vishnu with his 10 avatars (incarnations): Matsya (Fish), Kurma (Tortoise), Varaha (Boar), Narasimha (Man-Lion), Vamana (Dwarf), Parashurama (Rama-with-the-Axe), King Rama, Krishna, Buddha, and Kalki.</p>",
                    solution_hi: "<p>87.(d) <strong>10. </strong>अवतार हिंदू धर्म के भीतर एक अवधारणा है जिसका संस्कृत में शाब्दिक अर्थ है \"वंश\"। भगवान विष्णु 10 के अवतार: मत्स्य (मछली), कूर्म (कछुआ), वराह (सूअर), नरसिम्ह, वामन (बौना), परशुराम (राम-साथ-कुल्हाड़ी), राजा राम, कृष्ण, बुद्ध और कल्कि।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. A is twice as efficient as B and together, they finish a piece of work in 20 days. A alone can finish the work in :</p>",
                    question_hi: "<p>88. A, B से दोगुना कुशल है और दोनों मिलकर एक कार्य को 20 दिनों में पूरा करते हैं। A अकेले काम को कितने दिनों में पूरा कर सकता है?</p>",
                    options_en: ["<p>26 days</p>", "<p>30 days</p>", 
                                "<p>28 days</p>", "<p>20 days</p>"],
                    options_hi: ["<p>26 दिन</p>", "<p>30 दिन</p>",
                                "<p>28 दिन</p>", "<p>20 दिन</p>"],
                    solution_en: "<p>88.(b) A B<br>Efficiency&nbsp; &rarr;&nbsp; 2&nbsp; :&nbsp; 1<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; 1&nbsp; &nbsp;:&nbsp; 2<br>Time (A + B) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &rArr; 20 days<br>If <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &rarr; 20 days<br>1 &rarr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>2</mn></mfrac></math> = 30 days<br>A alone can finish the work in 30 days.</p>",
                    solution_hi: "<p>88.(b) A B<br>दक्षता &rarr; 2 : 1<br>समय &rarr; 1 : 2<br>समय (A + B) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &rArr; 20 दिन<br>यदि , <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &rarr; 20 दिन<br>1 &rarr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>2</mn></mfrac></math> = 30 दिन<br>A अकेला उस कार्य को 30 दिनों में समाप्त कर सकता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. What will come next in the series?<br><math display=\"inline\"><mfrac><mrow><mi>C</mi></mrow><mrow><mn>29</mn></mrow></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mi>F</mi></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>I</mi><mn>21</mn></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mi>L</mi></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>O</mi><mn>13</mn></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mo>?</mo><mo>?</mo></mfrac></math></p>",
                    question_hi: "<p>89. श्रृंखला में आगे क्या आएगा?<br><math display=\"inline\"><mfrac><mrow><mi>C</mi></mrow><mrow><mn>29</mn></mrow></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mi>F</mi></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>I</mi><mn>21</mn></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mi>L</mi></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>O</mi><mn>13</mn></mfrac></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mo>?</mo><mo>?</mo></mfrac></math></p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mi>R</mi></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mi>S</mi></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>S</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mi>R</mi></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mi>R</mi></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mi>S</mi></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>S</mi></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mi>R</mi></mrow></mfrac></math></p>"],
                    solution_en: "<p>89.(d)<strong> Logic: </strong>+ 3 in letter part and -4 in number part and then the numerator and denominator are exchanged to get the next term of the series.</p>",
                    solution_hi: "<p>89.(d) <strong>तर्क: </strong>अक्षर भाग में + 3 और संख्या भाग में - 4 और फिर श्रृंखला के अगले पद को प्राप्त करने के लिए अंश और हर को बदल दिया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>90. When did India first win the first World Cup title in Hockey?</p>",
                    question_hi: "<p>90. भारत ने पहली बार हॉकी में विश्व कप का खिताब कब जीता था?</p>",
                    options_en: ["<p>1975</p>", "<p>1978</p>", 
                                "<p>1971</p>", "<p>1982</p>"],
                    options_hi: ["<p>1975 में</p>", "<p>1978 में</p>",
                                "<p>1971 में</p>", "<p>1982 में</p>"],
                    solution_en: "<p>90.(a) <strong>1975.</strong> India defeated Pakistan under the Captaincy of Ajit Pal Singh in 3rd edition of Hockey World Cup. Hosted by - (Malaysia). India has won 8 Gold medals in Olympics hockey six of them in a row (from 1928-1956) and added two more at Tokyo (1964) and Moscow (1980).<strong> International Hockey Federation </strong>(FIH): Established - 1924, Headquarters - Lausanne (Switzerland), First President - Paul Leautey ( France).</p>",
                    solution_hi: "<p>90.(a) <strong>1975 </strong>I हॉकी विश्व कप के तीसरे संस्करण में अजीत पाल सिंह की कप्तानी में भारत ने पाकिस्तान को हराया। मेजबानी - (मलेशिया) द्वारा। भारत ने ओलंपिक हॉकी में 8 स्वर्ण पदक जीते हैं, जिनमें से छह लगातार (1928-1956 तक) जीते और दो और टोक्यो (1964) और मॉस्को (1980) में जीते। <strong>अंतर्राष्ट्रीय हॉकी महासंघ</strong> (FIH): स्थापना - 1924, मुख्यालय - लॉज़ेन (स्विट्जरलैंड), प्रथम अध्यक्ष - पॉल लेउटी (फ्रांस)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p>91. Who completed the construction of the Qutub Minar?</p>",
                    question_hi: "<p>91. कुतुब मीनार का निर्माण किसने पूर्ण करवाया था?</p>",
                    options_en: ["<p>Nasir-ud-Din Muhammad</p>", "<p>Muhi-ud-Din Muhammad</p>", 
                                "<p>Firoz Shah Tuglak</p>", "<p>Qutub-ud-Din Aibak</p>"],
                    options_hi: ["<p>नसीर-उद-दीन मुहम्मद</p>", "<p>मुही-उद-दीन मुहम्मद</p>",
                                "<p>फिरोज शाह तुगलक</p>", "<p>कुतुब-उद-दीन ऐबक</p>"],
                    solution_en: "<p>91.(c) <strong>Firoz Shah Tuglak.</strong> Qutub Minar (Victory tower): UNESCO World Heritage Site (Cultural); Qutab-ud-din Aibak (Founder of Delhi Sultanate) started the construction in 1199 AD. Iltutmish added three more storeys to the minar. Other structures in Qutub Complex: Quwwat-ul-Islam Mosque; Iron Pillar; Alai-Darwaza (southern gateway of the Quwwat-ul-Islam mosque); Tomb of Iltutmish.</p>",
                    solution_hi: "<p>91.(c) <strong>फ़िरोज़ शाह तुगलक।</strong> <strong>कुतुब मीनार </strong>(विजय मीनार): यूनेस्को विश्व विरासत स्थल (सांस्कृतिक); कुतुब-उद्-दीन ऐबक (दिल्ली सल्तनत का संस्थापक) ने 1199 ई. में निर्माण प्रारंभ किया। इल्तुतमिश ने मीनार में तीन मंजिलें और जोड़ीं। कुतुब परिसर में अन्य संरचनाएँ: कुव्वत-उल-इस्लाम मस्जिद; लौह स्तंभ; अलाई-दरवाज़ा (कुव्वत-उल-इस्लाम मस्जिद का दक्षिणी प्रवेश द्वार); इल्तुतमिश का मकबरा।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. Which option will replace the question mark in the given figure? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451965344.png\" alt=\"rId33\" width=\"189\" height=\"60\"></p>",
                    question_hi: "<p>92. दी गई आकृति में प्रश्न चिह्न के स्थान पर कौन सा विकल्प आएगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451965344.png\" alt=\"rId33\" width=\"189\" height=\"60\"></p>",
                    options_en: ["<p>17</p>", "<p>13</p>", 
                                "<p>15</p>", "<p>19</p>"],
                    options_hi: ["<p>17</p>", "<p>13</p>",
                                "<p>15</p>", "<p>19</p>"],
                    solution_en: "<p>92.(c) <strong>Logic:-</strong> <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>First</mi><mo>&#160;</mo><mi>term</mi><mo>+</mo><mi>Second</mi><mi mathvariant=\"normal\">&#160;</mi><mi>term</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = Third term<br>Similarly ,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>89</mn><mo>+</mo><mn>16</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi></mrow></mfrac></math> = 15</p>",
                    solution_hi: "<p>92.(c) <strong>Logic:- </strong><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>First</mi><mo>&#160;</mo><mi>term</mi><mo>+</mo><mi>Second</mi><mi mathvariant=\"normal\">&#160;</mi><mi>term</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>7</mn><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = Third term<br>Similarly ,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>89</mn><mo>+</mo><mn>16</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi></mrow></mfrac></math> = 15</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. There are six members in a family, i.e, K, L, W, X, M and Z. Each has a car of different colour. L has a white car. W does not have a black or yellow car. M has a red car and K does not have a blue car. Z has a brown car. Which of the following is the colour of W&rsquo;s car?</p>",
                    question_hi: "<p>93. एक परिवार में छह सदस्य, K, L, W, X, M और Z हैं। प्रत्येक के पास अलग-अलग रंग की कार होती है। Lके पास सफेद रंग की कार है। Wके पास काले या पीले रंग की कार नहीं है। M के पास लाल रंग की कार है और K के पास नीली कार नहीं है। Z के पास भूरी कार है। निम्नलिखित में से कौन सा W की कार का रंग है?</p>",
                    options_en: ["<p>Black</p>", "<p>Blue</p>", 
                                "<p>Red</p>", "<p>Yellow</p>"],
                    options_hi: ["<p>काला</p>", "<p>नीला</p>",
                                "<p>लाल</p>", "<p>पीला</p>"],
                    solution_en: "<p>93.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451965636.png\" alt=\"rId34\" width=\"179\" height=\"105\"></p>",
                    solution_hi: "<p>93.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451965817.png\" alt=\"rId35\" width=\"134\" height=\"117\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. If sin&theta; + cosec&theta; = 2, then the value of sin<sup>8</sup>&theta; + cosec<sup>8</sup>&theta; is :</p>",
                    question_hi: "<p>94. यदि sin&theta; + cosec&theta; = 2, तो sin<sup>8</sup>&theta; + cosec<sup>8</sup>&theta; का मान ज्ञात कीजिये ?</p>",
                    options_en: ["<p>2</p>", "<p>2<sup>8</sup></p>", 
                                "<p>1</p>", "<p>2<sup>4</sup></p>"],
                    options_hi: ["<p>2</p>", "<p>2<sup>8</sup></p>",
                                "<p>1</p>", "<p>2<sup>4</sup></p>"],
                    solution_en: "<p>94.(a) As sin&theta; + cosec&theta; = 2 , <br>&rArr; sin&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>sin&#952;</mi></mfrac></math> = 2<br>&rArr; sin<sup>2</sup>&theta; - 2sin&theta; + 1 = 0<br>&rArr; (sin&theta; - 1)<sup>2</sup> = 0<br>&rArr; sin&theta; = 1 then cosec&theta; = 1<br>So, sin<sup>8</sup>&theta; + cosec<sup>8</sup>&theta;&nbsp;</p>\n<p>= (1)<sup>8</sup> + (1)<sup>8 </sup>= 2<br><strong>Alternate Method:</strong><br>As sin&theta; + cosec&theta; = 2 (given) , <br>&rArr; sin&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>sin&#952;</mi></mfrac></math> = 2<br>Let sin&theta; = x Now, x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = 2<br>We get, value of x&nbsp;will be 1<br>So, sin&theta; = 1 i.e. &theta; = 90&deg;<br>So,&nbsp;sin<sup>8</sup>90&deg; + cosec<sup>8</sup>90&deg;</p>\n<p>= (1)<sup>8</sup> + (1)<sup>8</sup> = 2</p>",
                    solution_hi: "<p>94.(a) जैसे,&nbsp; sin&theta; + cosec&theta; = 2 , <br>&rArr; sin&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>sin&#952;</mi></mfrac></math> = 2<br>&rArr; sin<sup>2</sup>&theta; - 2sin&theta; + 1 = 0<br>&rArr; (sin&theta; - 1)<sup>2</sup> = 0<br>&rArr; sin&theta; = 1 then cosec&theta; = 1<br>तो , sin<sup>8</sup>&theta; + cosec<sup>8</sup>&theta;&nbsp;</p>\n<p>= (1)<sup>8</sup> + (1)<sup>8 </sup>= 2</p>\n<p><strong>वैकल्पिक तरीका:</strong><br>चूँकि sin&theta; + cosec&theta; = 2 (दिया गया है), <br>&rArr; sin&theta; + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>sin&#952;</mi></mfrac></math> = 2<br>माना sin&theta; = x अब, x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = 2<br>हम पाते हैं, x का मान 1 होगा इसलिए, sin&theta; = 1 i.e. &theta; = 90&deg;<br>तो , sin<sup>8</sup>90&deg; + cosec<sup>8</sup>90&deg;</p>\n<p>= (1)<sup>8</sup> + (1)<sup>8</sup> = 2</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. Sound is a :</p>",
                    question_hi: "<p>95. ध्वनि निम्न में क्या एक है:</p>",
                    options_en: ["<p>Surface wave</p>", "<p>Tidal wave</p>", 
                                "<p>Mechanical wave</p>", "<p>Electromagnetic wave</p>"],
                    options_hi: ["<p>सतहीय तरंग</p>", "<p>ज्वारीय तरंग</p>",
                                "<p>यांत्रिक तरंग</p>", "<p>विद्युत चुम्बकीय तरंग</p>"],
                    solution_en: "<p>95.(c) <strong>Mechanical</strong> wave. Sound is a vibration that propagates through a medium in the form of a mechanical wave. The medium in which it propagates can either be a solid, a liquid or a gas. Sound travels fastest in solids, relatively slower in liquids and slowest in gases. Sound waves are longitudinal waves. Humans can detect sounds in a frequency range from about 20 Hz to 20 kHz. Loudness of sound is measured in decibels (dB). The number of periods or cycles per second is called frequency. The SI unit for frequency is the hertz (Hz). Audiometer - Tool used to measure a person\'s hearing capacity.</p>",
                    solution_hi: "<p>95.(c) <strong>यांत्रिक तरंग</strong>। ध्वनि एक कंपन है जो एक माध्यम से यांत्रिक तरंग के रूप में प्रसारित होती है। जिस माध्यम में यह प्रसारित है वह ठोस, द्रव या गैस हो सकता है। ध्वनि ठोस में सबसे तेज, द्रव में अपेक्षाकृत धीमी और गैस में सबसे धीमी गति से चलती है। ध्वनि तरंगें अनुदैर्ध्य तरंगें होती हैं। मनुष्य लगभग 20 हर्ट्ज से 20 किलोहर्ट्ज़ की आवृत्ति रेंज में ध्वनियों का पता लगा सकते हैं। ध्वनि की तीव्रता डेसिबल (dB) में मापी जाती है। प्रति सेकेण्ड आवर्त या चक्रों की संख्या को आवृत्ति कहते हैं। आवृत्ति के लिए SI इकाई हर्ट्ज़ (Hz) है। ऑडियोमीटर - किसी व्यक्ति की सुनने की क्षमता को मापने के लिए इस्तेमाल किया जाने वाला उपकरण।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. In which year was Steel Authority of India incorporated?</p>",
                    question_hi: "<p>96.स्टील अथॉरिटी ऑफ़ इण्डिया का निगमन कब हुआ था?</p>",
                    options_en: ["<p>1973</p>", "<p>1982</p>", 
                                "<p>1987</p>", "<p>1979</p>"],
                    options_hi: ["<p>1973</p>", "<p>1982</p>",
                                "<p>1987</p>", "<p>1979</p>"],
                    solution_en: "<p>96.(a) <strong>1973. Steel Authority</strong> <strong>of India (SAIL)</strong> was established on January 24, 1973. It was earlier known as Hindustan Steel Limited. Ministry- Ministry of Steel. Headquarters - New Delhi. Bharat Petroleum Corporation Limited (1952), Indian Oil Corporation Limited (1959), Hindustan Petroleum Corporation Limited (1974), Oil and Natural Gas Corporation (1956), Coal India Limited (1975), Gas Authority of India Limited (1984), National Thermal Power Corporation (1975), Power Grid Corporation of India (1989 ), REC limited (1969).</p>",
                    solution_hi: "<p>96.(a) <strong>1973। भारतीय इस्पात प्राधिकरण (SAIL)</strong> की स्थापना 24 जनवरी, 1973 को हुई थी। इसे पहले हिंदुस्तान स्टील लिमिटेड के नाम से जाना जाता था। मंत्रालय- इस्पात मंत्रालय। मुख्यालय-नई दिल्ली। भारत पेट्रोलियम कॉर्पोरेशन लिमिटेड (1952), इंडियन ऑयल कॉर्पोरेशन लिमिटेड (1959), हिंदुस्तान पेट्रोलियम कॉर्पोरेशन लिमिटेड (1974), तेल और प्राकृतिक गैस निगम (1956), कोल इंडिया लिमिटेड (1975), गैस अथॉरिटी ऑफ इंडिया लिमिटेड (1984), नेशनल थर्मल पावर कॉर्पोरेशन (1975), पावर ग्रिड कॉर्पोरेशन ऑफ इंडिया (1989), REC लिमिटेड (1969)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p>97. how many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451965943.png\" alt=\"rId36\" width=\"174\" height=\"149\"></p>",
                    question_hi: "<p>97. दी गई आकृति में कितने त्रिभुज हैं?<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451965943.png\" alt=\"rId36\" width=\"174\" height=\"149\"></p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>10</p>", "<p>5</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>10</p>", "<p>5</p>"],
                    solution_en: "<p>97.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451966145.png\" alt=\"rId37\" width=\"153\" height=\"136\"><br>Here triangles are = afg, gbj, jei, hid, fhc, ahe, fdb, bic, ajd, cge. <br>Hence 10 triangles are present in the given figure.</p>",
                    solution_hi: "<p>97.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728451966145.png\" alt=\"rId37\" width=\"153\" height=\"136\"><br>यहाँ त्रिभुज = afg, gbj, jei, hid, fhc, ahe, fdb, bic, ajd, cge हैं।<br>अत: दी गई आकृति में 10 त्रिभुज उपस्थित हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. If the second half of the given series is reversed, then how many letters, from left to right, is preceded by a letter and succeeded by a number?<br>9＄YX8N6OLBUJZT@1QFD%</p>",
                    question_hi: "<p>98. यदि दी गई श्रृंखला का दूसरा भाग उलट दिया जाता है, तो बाएँ से दाएँ कितने अक्षर किसी अक्षर के बाद और किसी संख्या से आगे चले जाते हैं? <br>9＄YX8N6OLBUJZT@1QFD%</p>",
                    options_en: ["<p>0</p>", "<p>3</p>", 
                                "<p>1</p>", "<p>2</p>"],
                    options_hi: ["<p>0</p>", "<p>3</p>",
                                "<p>1</p>", "<p>2</p>"],
                    solution_en: "<p>98.(d)<br><strong>First half </strong>&rarr; 9＄YX8N6OLB <br>Second half &rarr; UJZT@1QFD%<br>After reversing the second half of series <br>%<strong>DFQ1</strong>@TZJU<br>Only Q is preceded by a letter and succeeded by a number.</p>",
                    solution_hi: "<p>98.(d) पहला आधा &rarr; 9＄YX8N6OLB <br>दूसरा आधा &rarr; UJZT@1QFD%<br><strong>श्रृंखला के दूसरे भाग को उलटने के बाद</strong><br>%<strong>DFQ1</strong>@TZJU<br>केवल Q के पहले एक अक्षर है और उसके बाद एक संख्या है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p>99. Identify an endoparasite found in animals.</p>",
                    question_hi: "<p>99. जंतुओं में पाए जाने वाले अंतः परजीवी की पहचान कीजिए।</p>",
                    options_en: ["<p>Pleurobrachia</p>", "<p>Ctenoplana</p>", 
                                "<p>Flatworms</p>", "<p>Adamsia</p>"],
                    options_hi: ["<p>प्लूरोब्रेकिया</p>", "<p>टीनोप्लाना</p>",
                                "<p>चपटे कृमि</p>", "<p>ऐडैम्सिया</p>"],
                    solution_en: "<p>99.(c) F<strong>latworms. Endoparasites </strong>are those that live inside the host body at different sites (liver, kidney, lungs, red blood cells, etc.). Example - Pinworms, hookworms and roundworms. <strong>Pleurobrachia</strong> is a common genus of Ctenophora phylum. <strong>Ctenoplana</strong> is a genus of comb jellies, and the only genus in the family Ctenoplanidae.<strong> Adamsia</strong> is a genus of sea anemones in the family Hormathiidae.</p>",
                    solution_hi: "<p>99.(c) <strong>चपटे कृमि। अंतःपरजीवी</strong> (एंडोपैरासाइट) वे हैं जो मेजबान शरीर के अंदर विभिन्न स्थानों (यकृत, गुर्दे, फेफड़े, लाल रक्त कोशिकाएं, आदि) में रहते हैं। उदाहरण - पिनवर्म, हुकवर्म और राउंडवर्म। <strong>प्लुरोब्राचिया</strong> केटेनोफोरा फाइलम का एक सामान्य जीनस है। <strong>केटेनोप्लाना </strong>कंघी जेली की एक प्रजाति है, और केटेनोप्लानिडे परिवार में एकमात्र जीनस है। <strong>एडम्सिया</strong> होर्माथिडे परिवार में समुद्री एनीमोन की एक प्रजाति है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. Lactose intolerance is:</p>",
                    question_hi: "<p>100. लैक्टोज असहिष्णुता है:</p>",
                    options_en: ["<p>characterised by adverse reactions to gluten.</p>", "<p>a condition where small intestines are unable to break down gluten.</p>", 
                                "<p>a condition in which the small intestine are unable to break down the sugar found in dairy products.</p>", "<p>a condition where fructose isn&rsquo;t efficiently absorbed into the blood.</p>"],
                    options_hi: ["<p>ग्लूटेन के लिए प्रतिकूल अभिक्रियाओं की विशेषता।</p>", "<p>एक ऐसी स्थिति जहां छोटी आंतें ग्लूटेन को तोड़ने में असमर्थ होती हैं।</p>",
                                "<p>एक ऐसी स्थिति जिसमें छोटी आंत डेयरी उत्पादों में पाई जाने वाली शर्करा को तोड़ने में असमर्थ होती है।</p>", "<p>ऐसी स्थिति जहां फ्रुक्टोज रक्त में कुशलता से अवशोषित नहीं होता है ।</p>"],
                    solution_en: "<p>100.(c) <strong>Lactose (C<sub>12</sub>H<sub>22</sub>O<sub>11</sub>)</strong> - It is a disaccharide sugar containing glucose and galactose units. It makes up around 2 - 8% of milk.<strong> Gluten</strong> - A protein (made up of two proteins - gliadin and glutenin) found in some grains (wheat, barley, and rye). <strong>Fructose</strong> (C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>) - A monosaccharide sugar (fruit sugar). <strong>Sucrose </strong>(C<sub>12</sub>H<sub>22</sub>O<sub>11</sub>) - A disaccharide sugar composed of glucose and fructose subunits.</p>",
                    solution_hi: "<p>100.(c) <strong>लैक्टोज</strong> (<strong>C<sub>12</sub>H<sub>22</sub>O<sub>11</sub></strong>) - यह एक डिसैकराइड शर्करा है जिसमें ग्लूकोज और गैलेक्टोज इकाइयाँ होती हैं। यह लगभग 2 - 8% दूध का निर्माण करता है। <strong>ग्लूटेन</strong> - एक प्रोटीन (दो प्रोटीन से बना - ग्लियाडिन और ग्लूटेनिन) जो कुछ अनाजों (गेहूं, जौ और राई) में पाया जाता है। <strong>फ्रुक्टोज़ </strong>(C<sub>6</sub>H<sub>12</sub>O<sub>6</sub>) - एक मोनोसैकराइड शर्करा (फ्रूट शर्करा )। <strong>सुक्रोज</strong> (C<sub>12</sub>H<sub>22</sub>O<sub>11</sub>) - ग्लूकोज और फ्रक्टोज सब यूनिट से बनी एक डिसैकराइड शर्करा है ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>