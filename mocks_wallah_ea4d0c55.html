<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p><strong>1. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour. <br>Select the most appropriate option to fill in blank no.1</p>",
                    question_hi: "<p><strong>1. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour. <br>Select the most appropriate option to fill in blank no.1</p>",
                    options_en: ["<p>ripening</p>", "<p>development</p>", 
                                "<p>progress</p>", "<p>maturity</p>"],
                    options_hi: ["<p>ripening</p>", "<p>development</p>",
                                "<p>progress</p>", "<p>maturity</p>"],
                    solution_en: "<p>1.(b) development..<br>&lsquo;Development&rsquo; means a process that creates growth, progress, and positive change. The given passage states that it is essential to form a link between formal education and skill development. Hence, &lsquo;development&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(b) development.<br>&lsquo;Development&rsquo; का अर्थ है, एक ऐसी प्रक्रिया जो वृद्धि (growth), प्रगति (progress) और सकारात्मक परिवर्तन (positive changes) लाती है। दिए गए passage में कहा गया है कि औपचारिक शिक्षा (formal education) एवं कौशल विकास (skill development) के बीच एक कड़ी (link) बनाना आवश्यक है। अतः, &lsquo;development&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p><strong>2. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour.<br>Select the most appropriate option to fill in blank no.2</p>",
                    question_hi: "<p><strong>2. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour.<br>Select the most appropriate option to fill in blank no.2</p>",
                    options_en: ["<p>essential</p>", "<p>domestic</p>", 
                                "<p>needless</p>", "<p>external</p>"],
                    options_hi: ["<p>essential</p>", "<p>domestic</p>",
                                "<p>needless</p>", "<p>external</p>"],
                    solution_en: "<p>2.(a) essential.<br>&lsquo;Essential&rsquo; means something completely necessary. The given passage states that multidisciplinary and personality enhancement skills are necessary requirements of every sector. Hence, &lsquo;essential&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(a) essential.<br>&lsquo;Essential&rsquo; का अर्थ है कुछ पूर्णतः आवश्यक। दिए गए passage में कहा गया है कि बहु-विषयक (multidisciplinary) और व्यक्तित्व निखार (personality enhancement) कौशल हर क्षेत्र की आवश्यक (necessary) आवश्यकताएं हैं। अतः, &lsquo;essential&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p><strong>3. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour.<br>Select the most appropriate option to fill in blank no.3</p>",
                    question_hi: "<p><strong>3. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour. <br>Select the most appropriate option to fill in blank no.3</p>",
                    options_en: ["<p>settled</p>", "<p>introduced</p>", 
                                "<p>improved</p>", "<p>related</p>"],
                    options_hi: ["<p>settled</p>", "<p>introduced</p>",
                                "<p>improved</p>", "<p>related</p>"],
                    solution_en: "<p>3.(b) introduced.<br>&lsquo;Introduce&rsquo; means to bring in something new for the first time. However, &ldquo;being + V<sub>3</sub>&rdquo; is grammatically the correct structure for the sentence. Hence, &lsquo;introduced(V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>3.(b) introduced.<br>&lsquo;Introduce&rsquo; का अर्थ है पहली बार कुछ नया लाना। हालाँकि, दिए गए sentence के लिए &ldquo;being + V<sub>3</sub>&rdquo; grammatically सही structure है। अतः, &lsquo;introduced(V<sub>3</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p><strong>4. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour. <br>Select the most appropriate option to fill in blank no.4</p>",
                    question_hi: "<p><strong>4. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour.<br>Select the most appropriate option to fill in blank no.4</p>",
                    options_en: ["<p>need</p>", "<p>duty</p>", 
                                "<p>charge</p>", "<p>urgency</p>"],
                    options_hi: ["<p>need</p>", "<p>duty</p>",
                                "<p>charge</p>", "<p>urgency</p>"],
                    solution_en: "<p>4.(a) need.<br>&lsquo;Need&rsquo; means requirement or want of something.The given passage states that reforms are being introduced in higher education to co-relate academics to the need of various service sectors. Hence, &lsquo;need&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(a) need.<br>&lsquo;Need&rsquo; का अर्थ है किसी चीज़ की आवश्यकता या जरूरत। दिए गए passage में कहा गया है उच्च शिक्षा (higher education) में सुधार किए जा रहे हैं ताकि अकादमिक (शिक्षा) को विभिन्न सेवा क्षेत्रों की ज़रूरत से सह-संबंधित किया जा सके। अतः, &lsquo;need&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p><strong>5. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour.<br>Select the most appropriate option to fill in blank no.5</p>",
                    question_hi: "<p><strong>5. Cloze Test :</strong><br>It is essential to form a link between formal education and skill (1)______. Multidisciplinary and personality enhancement skills are the (2)______ requirements of every sector. Many reforms are being (3)______ in higher education to co-relate academics to the (4)______of various service sectors. Innovative models can help to (5)______ the varied needs of skilled and unskilled labour. <br>Select the most appropriate option to fill in blank no.5</p>",
                    options_en: ["<p>met</p>", "<p>meeting</p>", 
                                "<p>meets</p>", "<p>meet</p>"],
                    options_hi: ["<p>met</p>", "<p>meeting</p>",
                                "<p>meets</p>", "<p>meet</p>"],
                    solution_en: "<p>5.(d) meet.<br>&lsquo;To + V<sub>1</sub>&rsquo;, also known as the infinitive, is the correct grammatical structure. Hence, &lsquo;meet&rsquo;(V<sub>1</sub>) is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(d) meet.<br>&lsquo;To + V<sub>1</sub>&rsquo;, जिसे infinitive भी कहा जाता है, सही grammatical structure है। अतः, &lsquo;meet&rsquo;(V<sub>1</sub>,) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p><strong>6. Cloze Test :</strong> <br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 6.</p>",
                    question_hi: "<p><strong>6. Cloze Test :</strong><br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 6.</p>",
                    options_en: ["<p>grown</p>", "<p>grow</p>", 
                                "<p>grows</p>", "<p>grew</p>"],
                    options_hi: ["<p>grown</p>", "<p>grow</p>",
                                "<p>grows</p>", "<p>grew</p>"],
                    solution_en: "<p>6.(a) grown. <br>&ldquo;is/are + V<sub>3</sub>&rdquo; is the correct grammatical structure for the given sentence which is in passive voice. However, &lsquo;grow&rsquo; means to develop into an adult form like fruits &amp; vegetables. Hence, are &lsquo;grown&rsquo;(V<sub>3</sub>) is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(a) grown. <br>दिए गए sentence के लिए &ldquo;is/are + V<sub>3</sub>&rdquo; सही grammatical structure है, जो passive voice में है। हालाँकि, &lsquo;grow&rsquo; का अर्थ है वयस्क रूप में विकसित होना, जैसे फल एवं सब्जियों। अतः, are &lsquo;grown&rsquo;(V<sub>3</sub>) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p><strong>7. Cloze Test :</strong><br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 7.</p>",
                    question_hi: "<p><strong>7. Cloze Test :</strong><br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 7.</p>",
                    options_en: ["<p>locally</p>", "<p>nearby</p>", 
                                "<p>closely</p>", "<p>nearly</p>"],
                    options_hi: ["<p>locally</p>", "<p>nearby</p>",
                                "<p>closely</p>", "<p>nearly</p>"],
                    solution_en: "<p>7.(a) locally. <br>Locally means within a particular area or place. The given passage states that whatever we buy in Bhutan is locally produced in Bhutan. Hence, &lsquo;locally&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(a) locally. <br>\"Locally\" का अर्थ है किसी विशेष क्षेत्र या स्थान के भीतर। दिए गए passage में कहा गया है कि हम Bhutan में जो कुछ भी खरीदते हैं वह स्थानीय रूप से Bhutan में ही उत्पादित होता है। अतः, &lsquo;locally&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p><strong>8. Cloze Test :</strong><br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 8.</p>",
                    question_hi: "<p><strong>8. Cloze Test :</strong> <br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 8.</p>",
                    options_en: ["<p>common</p>", "<p>mass</p>", 
                                "<p>majority</p>", "<p>community</p>"],
                    options_hi: ["<p>common</p>", "<p>mass</p>",
                                "<p>majority</p>", "<p>community</p>"],
                    solution_en: "<p>8.(c) majority. <br>&lsquo;Majority&rsquo; means the largest number or part of a group of people. The given passage states that a large number of the population of Bhutan is indulged in farming. Hence, &lsquo;majority&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(c) majority. <br>&lsquo;Majority&rsquo; का अर्थ है लोगों के समूह की सबसे बड़ी संख्या या सबसे बड़ा हिस्सा। दिए गए passage में कहा गया है कि Bhutan की जनसंख्या का एक बड़ा हिस्सा खेती में लगा हुआ है। अतः, &lsquo;majority&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p><strong>9. Cloze Test :</strong><br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 9.</p>",
                    question_hi: "<p><strong>9. Cloze Test :</strong><br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 9.</p>",
                    options_en: ["<p>nutrition</p>", "<p>sustenance</p>", 
                                "<p>conservation</p>", "<p>provision</p>"],
                    options_hi: ["<p>nutrition</p>", "<p>sustenance</p>",
                                "<p>conservation</p>", "<p>provision</p>"],
                    solution_en: "<p>9.(b) sustenance. <br>Sustenance means the process of making something continue to exist; support; livelihood .The given passage states that the majority of the population indulges in farming for sustenance(for a living). Hence, &lsquo;sustenance&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(b) sustenance. <br>\"Sustenance\" का अर्थ है किसी चीज़ को अस्तित्व में बनाए रखने की प्रक्रिया; सहारा; निर्वाह। दिए गए passage में कहा गया है कि population का अधिकांश हिस्सा निर्वाह के लिए (यानी जीविका चलाने के लिए) खेती में लगा हुआ है। अतः, &lsquo;sustenance&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p><strong>10. Cloze Test :</strong><br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 10.</p>",
                    question_hi: "<p><strong>10. Cloze Test :</strong><br>&ldquo;Everything in Bhutan is chemical-free, all fruits and vegetables are (6)______ organically, so whatever you buy is (7)______ farm produced as the (8)______ of the population indulge in farming for (9)______,&rdquo; says my taxi driver Karma Chime. &ldquo;We use turmeric on wounds, also to dye (10)______ and it is added extensively in our cooking. Also, many believe it is anti- cancer and can treat tumours.<br>Select the most appropriate option to fill in blank number 10.</p>",
                    options_en: ["<p>covering</p>", "<p>attires</p>", 
                                "<p>fashions</p>", "<p>garments</p>"],
                    options_hi: ["<p>covering</p>", "<p>attires</p>",
                                "<p>fashions</p>", "<p>garments</p>"],
                    solution_en: "<p>10.(d) garments. <br>Garment means a piece of clothing. The given passage states that the people of Bhutan use turmeric on wounds and also to dye(to colour) garments. Hence, &lsquo;garments &rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>10.(d) garments. <br>\"Garment\" का अर्थ है वस्त्र या कपड़ा। दिए गए passage में कहा गया है कि Bhutan के लोग हल्दी का उपयोग घावों पर और वस्त्रों को रंगने (डाई करने) के लिए भी करते हैं। अतः, &lsquo;garments&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p><strong>11. Cloze Test :</strong><br>Water, a substance composed of the chemical elements hydrogen and oxygen (11)______ in gaseous, liquid, and solid states. It is (12)______ of the most plentiful and essential (13)______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14)______ to dissolve many other substances. Indeed, the (15)______ of water as a solvent is essential to living organisms. <br>Select the most appropriate option to fill in blank number (11)</p>",
                    question_hi: "<p><strong>11. Cloze Test </strong>:<br>Water, a substance composed of the chemical elements hydrogen and oxygen (11)______ in gaseous, liquid, and solid states. It is (12)______ of the most plentiful and essential (13)______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14)______ to dissolve many other substances. Indeed, the (15)______ of water as a solvent is essential to living organisms. <br>Select the most appropriate option to fill in blank number (11)</p>",
                    options_en: ["<p>exists</p>", "<p>lasts</p>", 
                                "<p>survives</p>", "<p>endures</p>"],
                    options_hi: ["<p>exists</p>", "<p>lasts</p>",
                                "<p>survives</p>", "<p>endures</p>"],
                    solution_en: "<p>11.(a) exists. <br>Exist means to be real or to be found in the real world. The given passage states that water exists in gaseous, liquid, and solid states. Hence, &rsquo;exists&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>11.(a) exists. <br>\"Exist\" का अर्थ है वास्तविक होना या वास्तविक दुनिया में पाया जाना। दिए गए passage में कहा गया है कि पानी गैसीय (gaseous), तरल (liquid), एवं ठोस (solid) अवस्थाओं में मौजूद है या पाया जाता है। अतः, &lsquo;exists&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p><strong>12. Cloze Test :</strong><br>Water, a substance composed of the chemical elements hydrogen and oxygen (11) ______ in gaseous, liquid, and solid states. It is (12) ______ of the most plentiful and essential (13) ______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14) ______ to dissolve many other substances. Indeed, the (15) ______ of water as a solvent is essential to living organisms.<br>Select the most appropriate option to fill in blank number (12)</p>",
                    question_hi: "<p><strong>12. Cloze Test :</strong><br>Water, a substance composed of the chemical elements hydrogen and oxygen (11)______ in gaseous, liquid, and solid states. It is (12)______ of the most plentiful and essential (13)______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14)______ to dissolve many other substances. Indeed, the (15)______ of water as a solvent is essential to living organisms.<br>Select the most appropriate option to fill in blank number (12)</p>",
                    options_en: ["<p>unique</p>", "<p>lone</p>", 
                                "<p>one</p>", "<p>single</p>"],
                    options_hi: ["<p>unique</p>", "<p>lone</p>",
                                "<p>one</p>", "<p>single</p>"],
                    solution_en: "<p>12.(c) one. <br>According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, &lsquo;One of + Plural Subject&rsquo; is grammatically the correct structure. In the given sentence, &lsquo;compounds&rsquo; is a plural subject. Hence, &lsquo;one&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(c) one. <br>&ldquo;Subject-Verb Agreement Rule&rdquo;, के अनुसार, &lsquo;One of + Plural Subject&rsquo;, grammatically सही structure है। दिए गए sentence में, &lsquo;compounds&rsquo; एक plural subject है। अतः, &lsquo;one&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p><strong>13. Cloze Test :</strong><br>Water, a substance composed of the chemical elements hydrogen and oxygen (11)______ in gaseous, liquid, and solid states. It is (12)______ of the most plentiful and essential (13)______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14)______ to dissolve many other substances. Indeed, the (15)______ of water as a solvent is essential to living organisms.<br>Select the most appropriate option to fill in blank number (13)</p>",
                    question_hi: "<p><strong>13. Cloze Test :</strong><br>Water, a substance composed of the chemical elements hydrogen and oxygen (11)______ in gaseous, liquid, and solid states. It is (12)______ of the most plentiful and essential (13)______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14)______ to dissolve many other substances. Indeed, the (15)______ of water as a solvent is essential to living organisms.<br>Select the most appropriate option to fill in blank number (13)</p>",
                    options_en: ["<p>by</p>", "<p>in</p>", 
                                "<p>for</p>", "<p>of</p>"],
                    options_hi: ["<p>by</p>", "<p>in</p>",
                                "<p>for</p>", "<p>of</p>"],
                    solution_en: "<p>13.(d) of. <br>According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, &lsquo;One of + Plural Subject&rsquo; is grammatically the correct structure. The given passage states that it is one of the most plentiful and essential of compounds. Hence, &rsquo;of&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>13.(d) of. <br>&ldquo;Subject-Verb Agreement Rule&rdquo;, के अनुसार, &lsquo;One of + Plural Subject&rsquo;, grammatically सही structure है। दिए गए passage में कहा गया है कि यह सबसे प्रचुर (plentiful) तथा आवश्यक (essential) यौगिकों (compounds) में से एक है। अतः, &lsquo;of&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p><strong>14. Cloze Test :</strong><br>Water, a substance composed of the chemical elements hydrogen and oxygen (11)______ in gaseous, liquid, and solid states. It is (12)______ of the most plentiful and essential (13)______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14)______ to dissolve many other substances. Indeed, the (15)______ of water as a solvent is essential to living organisms.<br>Select the most appropriate option to fill in blank number (14)</p>",
                    question_hi: "<p><strong>14. Cloze Test :</strong><br>Water, a substance composed of the chemical elements hydrogen and oxygen (11)______ in gaseous, liquid, and solid states. It is (12)______ of the most plentiful and essential (13)______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14)______ to dissolve many other substances. Indeed, the (15)______ of water as a solvent is essential to living organisms.<br>Select the most appropriate option to fill in blank number (14)</p>",
                    options_en: ["<p>ability</p>", "<p>knack</p>", 
                                "<p>proficiency</p>", "<p>competence</p>"],
                    options_hi: ["<p>ability</p>", "<p>knack</p>",
                                "<p>proficiency</p>", "<p>competence</p>"],
                    solution_en: "<p>14.(a) ability. <br>Ability means the mental or physical power that makes it possible to do something. The given passage states that water has the important ability to dissolve many other substances. Hence, &rsquo;ability&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>14.(a) ability. <br>\"Ability\" का अर्थ है mental या physical power जो कुछ करना possible बनाती है। दिए गए passage में कहा गया है कि पानी में कई अन्य पदार्थों को घुलना की महत्वपूर्ण क्षमता (important ability) होती है। अतः, &lsquo;ability&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "15. Cloze Test :<br />Water, a substance composed of the chemical elements hydrogen and oxygen (11)______ in gaseous, liquid, and solid states. It is (12)______ of the most plentiful and essential (13)______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14)______ to dissolve many other substances. Indeed, the (15)______ of water as a solvent is essential to living organisms.<br />Select the most appropriate option to fill in blank number 15. ",
                    question_hi: "15. Cloze Test :<br />Water, a substance composed of the chemical elements hydrogen and oxygen (11)______ in gaseous, liquid, and solid states. It is (12)______ of the most plentiful and essential (13)______ compounds. A tasteless and odourless liquid at room temperature, it has the important (14)______ to dissolve many other substances. Indeed, the (15)______ of water as a solvent is essential to living organisms.<br />Select the most appropriate option to fill in blank number 15. ",
                    options_en: [" veracity ", " versatility ", 
                                " velocity ", " validity "],
                    options_hi: [" veracity ", " versatility ",
                                " velocity ", " validity "],
                    solution_en: "15.(b) versatility. <br />Versatility means the ability to adapt or be adapted to many different functions or activities. The given passage states that the versatility of water as a solvent is essential for living organisms. Hence, ’versatility’ is the most appropriate answer.",
                    solution_hi: "15.(b) versatility. <br />\"Versatility\" का अर्थ है कई विभिन्न कार्यों या गतिविधियों के लिए अनुकूलित होने या किए जाने की क्षमता। दिए गए passage में कहा गया है कि एक विलायक (solvent) के रूप में जल की बहुमुखी प्रतिभा (versatility), जीवित जीवों के लिए आवश्यक है। अतः, ‘versatility’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p><strong>16. Cloze Test :</strong> <br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections. <br>Select the most appropriate option to fill in blank number (16)</p>",
                    question_hi: "<p><strong>16. Cloze Test :</strong><br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections.<br>Select the most appropriate option to fill in blank number (16)</p>",
                    options_en: ["<p>Still</p>", "<p>Even</p>", 
                                "<p>Yet</p>", "<p>Although</p>"],
                    options_hi: ["<p>Still</p>", "<p>Even</p>",
                                "<p>Yet</p>", "<p>Although</p>"],
                    solution_en: "<p>16.(d) Although.<br>Although is a conjunction which means despite the fact that or despite being. The given passage states that despite the fact that some archaeologists study living cultures, most archaeologists concern themselves with the distant past. Hence, &lsquo;although&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>16.(d) Although.<br>&lsquo;Although&rsquo; एक conjunction है जिसका अर्थ है &lsquo;इस तथ्य के बावजूद कि&rsquo; या यद्यपि। दिए गए passage में कहा गया है कि इस तथ्य (fact) के बावजूद कि कुछ पुरातत्वविद (archaeologists) जीवित संस्कृतियों (living cultures) का अध्ययन करते हैं, अधिकांश पुरातत्वविद सुदूर अतीत से सरोकार रखते हैं। अतः, &lsquo;although&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p><strong>17. Cloze Test :</strong><br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections.<br>Select the most appropriate option to fill in blank number (17)</p>",
                    question_hi: "<p><strong>17. Cloze Test :</strong><br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections.<br>Select the most appropriate option to fill in blank number (17)</p>",
                    options_en: ["<p>itself</p>", "<p>them</p>", 
                                "<p>himself</p>", "<p>themselves</p>"],
                    options_hi: ["<p>itself</p>", "<p>them</p>",
                                "<p>himself</p>", "<p>themselves</p>"],
                    solution_en: "<p>17.(d) themselves.<br>Themselves is a reflexive pronoun that is used for a plural subject mentioned earlier like archaeologists(plural) are mentioned in the given passage. Hence, &lsquo;themselves&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>17.(d) themselves.<br>&lsquo;Themselves&rsquo; एक reflexive pronoun है जिसका प्रयोग पहले उल्लिखित &lsquo;plural subjec&rsquo; के लिए किया जाता है। जैसे कि दिए गए passage में archaeologists(plural) का उल्लेख किया गया है। अतः, &lsquo;themselves&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p><strong>18. Cloze Test :</strong><br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections.<br>Select the most appropriate option to fill in blank number (18)</p>",
                    question_hi: "<p><strong>18. Cloze Test :</strong><br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections.<br>Select the most appropriate option to fill in blank number (18)</p>",
                    options_en: ["<p>ahead</p>", "<p>in</p>", 
                                "<p>up</p>", "<p>over</p>"],
                    options_hi: ["<p>ahead</p>", "<p>in</p>",
                                "<p>up</p>", "<p>over</p>"],
                    solution_en: "<p>18.(c) up. <br>&lsquo;Dig up&rsquo; is a phrase that means to remove something from under the ground by digging. However, &lsquo;have + past participle form&rdquo; is grammatically the correct structure &amp; &lsquo;dug up&rsquo; is the past participle form of &lsquo;dig up&rsquo;. Hence, &lsquo;up&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>18.(c) up. <br>&lsquo;Dig up&rsquo; एक Phrase है, जिसका अर्थ है, खोदकर (ज़मीन के नीचे से) कुछ निकालना।। हालाँकि, &lsquo;have + past participle form&rdquo; grammatically सही structure है तथा &lsquo;dug up&rsquo;, &lsquo;dig up&rsquo; का past participle form है। अतः, &lsquo;dig up&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p><strong>19. Cloze Test :</strong><br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections.<br>Select the most appropriate option to fill in blank number (19).</p>",
                    question_hi: "<p><strong>19.Cloze Test :</strong><br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections.<br>Select the most appropriate option to fill in blank number (19)</p>",
                    options_en: ["<p>Regularly</p>", "<p>Commonly</p>", 
                                "<p>Often</p>", "<p>Seldom</p>"],
                    options_hi: ["<p>Regularly</p>", "<p>Commonly</p>",
                                "<p>Often</p>", "<p>Seldom</p>"],
                    solution_en: "<p>19.(c) Often.<br>&lsquo;Often&rsquo; means many times. The given passage states that at many times these people were not scholars, but looters and grave robbers. Hence, &lsquo;often&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>19.(c) Often.<br>&lsquo;Often&rsquo; का अर्थ है कई बार। दिए गए passage में कहा गया है कि कई बार ये लोग विद्वान नहीं, बल्कि लुटेरे और कब्र खोदने वाले थे। अतः, &lsquo;often&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p><strong>20.Cloze Test :</strong><br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections. <br>Select the most appropriate option to fill in blank number (20).</p>",
                    question_hi: "<p><strong>20.Cloze Test :</strong><br>The word &ldquo;archaeology&rdquo; comes from the Greek word &ldquo;arkhaios,&rdquo; which means &ldquo;ancient.&rdquo; (16)______ some archaeologists study living cultures, most archaeologists concern (17)______ with the distant past. People have dug (18)______ monuments and collected artefacts for thousands of years. (19)______, these people were not scholars, but looters and grave robbers looking to make money or (20)______ up their personal collections.<br>Select the most appropriate option to fill in blank number (20).</p>",
                    options_en: ["<p>create</p>", "<p>build</p>", 
                                "<p>construct</p>", "<p>assemble</p>"],
                    options_hi: ["<p>create</p>", "<p>build</p>",
                                "<p>construct</p>", "<p>assemble</p>"],
                    solution_en: "<p>20.(b) build. <br>&lsquo;Build up&rsquo; means an increase of something over a period. The given passage states that many times looters and grave robbers made money or increased their personal collections. Hence, &lsquo;build&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>20.(b) build. <br>&lsquo;Build up&rsquo; का अर्थ है समय के साथ किसी चीज़ में वृद्धि होना। दिए गए passage में कहा गया है कि कई बार लुटेरे (looters) और कब्र चोर/लुटेरे (grave robbers) पैसा कमाते थे, या अपने व्यक्तिगत संग्रहो में वृद्धि करते थे। अतः, &lsquo;build&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>