<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the letter-cluster from among the given options that can replace the question mark (?) in the following series.&nbsp;<br>EFG, KLM, QRS, ?</p>",
                    question_hi: "<p>1. दिए गए विकल्पों में से उस अक्षर-समूह को चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सकता है।&nbsp;<br>EFG, KLM, QRS, ?</p>",
                    options_en: [
                        "<p>WXY</p>",
                        "<p>XYZ</p>",
                        "<p>UVW</p>",
                        "<p>WXZ</p>"
                    ],
                    options_hi: [
                        "<p>WXY</p>",
                        "<p>XYZ</p>",
                        "<p>UVW</p>",
                        "<p>WXZ</p>"
                    ],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504450213.png\" alt=\"rId4\" width=\"235\" height=\"88\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504450213.png\" alt=\"rId4\" width=\"235\" height=\"88\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Three statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong> <br>Some tears are drops. <br>Some drops are streams. <br>All streams are rivers.<br><strong>Conclusions :</strong> <br>I. All tears can never be rivers. <br>II. Some drops are rivers. <br>III. All tears being streams is a possibility.</p>",
                    question_hi: "<p>2. तीन कथन और उसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन-सा/से निष्कर्ष इन कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन :</strong> <br>कुछ आंसू, बूंदें हैं। <br>कुछ बूंदें, धाराएं हैं। <br>सभी धाराएं, नदियां हैं। <br><strong>निष्कर्ष :</strong> <br>I. सभी आंसू कभी भी नदियां नहीं हो सकते हैं। <br>II. कुछ बूंदें, नदियां हैं। <br>III. सभी आंसुओं के धाराएं होने की संभावना है।</p>",
                    options_en: [
                        "<p>Both I and II conclusion follow</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Only conclusion III follows</p>",
                        "<p>Both II and III conclusion follow</p>"
                    ],
                    options_hi: [
                        "<p>निष्&zwj;कर्ष I और II दोनों अनुसरण करते है</p>",
                        "<p>केवल निष्&zwj;कर्ष I अनुसरण करता है</p>",
                        "<p>केवल निष्&zwj;कर्ष III अनुसरण करता है</p>",
                        "<p>निष्&zwj;कर्ष II और III दोनों अनुसरण करते है</p>"
                    ],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504450479.png\" alt=\"rId5\" width=\"279\" height=\"73\"><br>Both II and III conclusion follow.</p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504450914.png\" alt=\"rId6\" width=\"278\" height=\"77\"><br>निष्कर्ष II और III दोनों अनुसरण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Six letters B, F, D, X, N and A are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to D.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504451143.png\" alt=\"rId7\" width=\"175\" height=\"91\"></p>",
                    question_hi: "<p>3. एक पासे के विभिन्न फलकों पर छह अक्षर B, F, D, X, N और A लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। D के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504451143.png\" alt=\"rId7\" width=\"175\" height=\"91\"></p>",
                    options_en: [
                        "<p>A</p>",
                        "<p>N</p>",
                        "<p>B</p>",
                        "<p>F</p>"
                    ],
                    options_hi: [
                        "<p>A</p>",
                        "<p>N</p>",
                        "<p>B</p>",
                        "<p>F</p>"
                    ],
                    solution_en: "<p>3.(a) from the both dices opposite faces are<br>F &harr; N, B &harr; X, D &harr; A</p>",
                    solution_hi: "<p>3.(a) दोनों पासों के विपरीत फलक हैं<br>F &harr; N, B &harr; X, D &harr; A</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Which two signs should be interchanged to make the given equation correct ?&nbsp;<br>289 + 17 &times; 12 &divide; 216 &minus; 300 = 120</p>",
                    question_hi: "<p>4. दिए गए समीकरण को संतुलित करने के लिए किन दो चिह्नों को परस्पर बदला जाना चाहिए ?<br>289 + 17 &times; 12 &divide; 216 &minus; 300 = 120</p>",
                    options_en: [
                        "<p>&divide; and +</p>",
                        "<p>+ and &times;</p>",
                        "<p>&divide; and &minus;</p>",
                        "<p>&minus; and +</p>"
                    ],
                    options_hi: [
                        "<p>&divide; और +</p>",
                        "<p>+ और &times;</p>",
                        "<p>&divide; और &minus;</p>",
                        "<p>&minus; और +</p>"
                    ],
                    solution_en: "<p>4.(a) <strong>Given :-</strong> 289 + 17 &times; 12 &divide; 216 - 300 = 120<br>After checking all the options, only option (a) satisfied. After interchanging &divide; and + we get<br>289 &divide; 17 &times; 12 + 216 - 300 <br>17 &times; 12 - 84<br>204 - 84 = 120<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>4.(a) <strong>दिया गया :- </strong>289 + 17 &times; 12 &divide; 216 - 300 = 120<br>सभी विकल्पों की एक- एक करके जांच करने पर, केवल विकल्प (a) संतुष्ट करता है। &divide; और + को आपस में बदलने के बाद हमें प्राप्त होता है<br>289 &divide; 17 &times; 12 + 216 - 300 <br>17 &times; 12 - 84<br>204 - 84 = 120<br>L.H.S. = R.H.S.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. 7 is related to 40 by certain logic. Following the same logic, 5 is related to 16. To which of the following is 9 related, following the same logic ? (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>5. 7 एक निश्चित तर्क का अनुसरण करते हुए 40 से संबंधित है। इसी तर्क का अनुसरण करते हुए 5, 16 से संबंधित है। समान तर्क का अनुसरण करते हुए, 9 निम्नलिखित में से किससे संबंधित है? नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: [
                        "<p>72</p>",
                        "<p>70</p>",
                        "<p>48</p>",
                        "<p>54</p>"
                    ],
                    options_hi: [
                        "<p>72</p>",
                        "<p>70</p>",
                        "<p>48</p>",
                        "<p>54</p>"
                    ],
                    solution_en: "<p>5.(a)<br><strong>Logic:- </strong>(1<sup>st</sup>no.)<sup>2</sup>&nbsp;- 9 = 2<sup>nd</sup>no.<br>(7, 40) :- (7)<sup>2</sup>&nbsp;- 9 &rArr; 49 - 9 = 40<br>(5, 16) :- (5)<sup>2</sup>&nbsp;- 9 &rArr; 25 - 9 = 16<br>similarly<br>(9, ?) :- (9)<sup>2</sup>&nbsp;- 9 &rArr; 81 - 9 = 72</p>",
                    solution_hi: "<p>5.(a)<br><strong>तर्क :-</strong> (पहला नंबर)<sup>2</sup>&nbsp;- 9 = दूसरा नंबर<br>(7, 40) :- (7)<sup>2</sup>&nbsp;- 9 &rArr; 49 - 9 = 40<br>(5, 16) :- (5)<sup>2</sup> - 9 &rArr; 25 - 9 = 16<br>इसी प्रकार<br>(9, ?) :- (9)<sup>2</sup> - 9 &rArr; 81 - 9 = 72</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, <br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;; <br>&lsquo;A &minus; B&rsquo; means &lsquo;A is the brother of B&rsquo;; <br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo; and <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the father of B&rsquo;. <br>Based on the above, how is A related to D if &lsquo;A &times; B &divide; C + D&rsquo; ?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है \'A, B की माता है\';<br>\'A &minus; B\' का अर्थ है \'A, B का भाई है\';<br>\'A &times; B\' का अर्थ है \'A, B की पत्नी है\' और<br>\'A &divide; B\' का अर्थ है \'A, B का पिता है\'।<br>उपर्युक्त के आधार पर, यदि \'A &times; B &divide; C + D\' है, तो A, D से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Mother&rsquo;s mother</p>",
                        "<p>Father&rsquo;s father</p>",
                        "<p>Mother&rsquo;s father</p>",
                        "<p>Father&rsquo;s mother</p>"
                    ],
                    options_hi: [
                        "<p>माता की माता</p>",
                        "<p>पिता का पिता</p>",
                        "<p>माता का पिता</p>",
                        "<p>पिता की माता</p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504451556.png\" alt=\"rId8\" width=\"100\" height=\"152\"><br>&lsquo;A&rsquo; is mother&rsquo; mother of &lsquo;D&rsquo;.</p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504451556.png\" alt=\"rId8\" width=\"100\" height=\"152\"><br>\'A\', \'D\' की माता की माता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. The position of how many letters will remain unchanged if each of the letter in the word &lsquo;HUSBAND&rsquo; is arranged in English alphabetical order ?</p>",
                    question_hi: "<p>7. यदि शब्द \'HUSBAND\' के प्रत्येक अक्षर को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी ?</p>",
                    options_en: [
                        "<p>Three</p>",
                        "<p>Zero</p>",
                        "<p>One</p>",
                        "<p>Two</p>"
                    ],
                    options_hi: [
                        "<p>Three</p>",
                        "<p>Zero</p>",
                        "<p>One</p>",
                        "<p>Two</p>"
                    ],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504453102.png\" alt=\"rId9\" width=\"156\" height=\"73\"><br>The Position of all letters will be change.</p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504453102.png\" alt=\"rId9\" width=\"156\" height=\"73\"><br>सभी अक्षरों की स्थिति बदल जाएगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the number from among the given options that can replace the question mark (?) in the following series.<br>96, 77, 60, 47, 36, ?</p>",
                    question_hi: "<p>8. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>96, 77, 60, 47, 36, ?</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>29</p>",
                        "<p>28</p>",
                        "<p>32</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>29</p>",
                        "<p>28</p>",
                        "<p>32</p>"
                    ],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504453311.png\" alt=\"rId10\" width=\"290\" height=\"76\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504453311.png\" alt=\"rId10\" width=\"290\" height=\"76\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group ?<br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>9. निलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है ?<br>(<strong>ध्यान दें : </strong>असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>JMN</p>",
                        "<p>UXY</p>",
                        "<p>DGH</p>",
                        "<p>ORT</p>"
                    ],
                    options_hi: [
                        "<p>JMN</p>",
                        "<p>UXY</p>",
                        "<p>DGH</p>",
                        "<p>ORT</p>"
                    ],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504454389.png\" alt=\"rId11\" width=\"112\" height=\"74\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504454863.png\" alt=\"rId12\" width=\"111\" height=\"80\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504454997.png\" alt=\"rId13\" width=\"105\" height=\"77\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504455909.png\" alt=\"rId14\" width=\"103\" height=\"82\"></p>",
                    solution_hi: "<p>9.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504454389.png\" alt=\"rId11\" width=\"112\" height=\"74\">&nbsp; , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504454863.png\" alt=\"rId12\" width=\"111\" height=\"80\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504454997.png\" alt=\"rId13\" width=\"105\" height=\"77\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504455909.png\" alt=\"rId14\" width=\"103\" height=\"82\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Which two numbers (not digits) should be interchanged to make the given equation correct ?&nbsp;<br>19 + 125 &divide; 25 &ndash; 100 &divide; 5 + 4 &times; 3 = 52</p>",
                    question_hi: "<p>10. दिए गए समीकरण को सही करने के लिए किन दो संख्याओं (अंक नहीं) को आपस में बदलना चाहिए ?<br>19 + 125 &divide; 25 &ndash; 100 &divide; 5 + 4 &times; 3 = 52</p>",
                    options_en: [
                        "<p>19 and 4</p>",
                        "<p>125 and 100</p>",
                        "<p>25 and 5</p>",
                        "<p>4 and 5</p>"
                    ],
                    options_hi: [
                        "<p>19 और 4</p>",
                        "<p>125 और 100</p>",
                        "<p>25 और 5</p>",
                        "<p>4 और 5</p>"
                    ],
                    solution_en: "<p>10.(c)<br><strong>Given: </strong>19 + 125 &divide; 25 &ndash; 100 &divide; 5 + 4 &times; 3 = 52<br>After checking all options one by one , only option (c) satisfied.<br>19 + 125 &divide; 5 &ndash; 100 &divide; 25 + 4 &times; 3 = 52<br>19 + 25 - 4 + 4 &times; 3 <br>19 + 25 - 4 + 12<br>56 - 4 = 52<br>52 = 52 (L.H.S = R.H.S)</p>",
                    solution_hi: "<p>10.(c)<br><strong>दिया गया: </strong>19 + 125 &divide; 25 &ndash; 100 &divide; 5 + 4 &times; 3 = 52<br>एक-एक करके सभी विकल्पों की जांच करने के बाद, केवल विकल्प (c) संतुष्ट करता है।<br>19 + 125 &divide; 5 &ndash; 100 &divide; 25 + 4 &times; 3 = 52<br>19 + 25 - 4 + 4 &times; 3 <br>19 + 25 - 4 + 12<br>56 - 4 = 52<br>52 = 52 (L.H.S = R.H.S)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below. <br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.) <br>Palate : Mouth</p>",
                    question_hi: "<p>11. उस शब्द-युग्म का चयन किजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है। <br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक-दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>तालुआ : मुंह</p>",
                    options_en: [
                        "<p>Hill : Range</p>",
                        "<p>Rivulet : Dam</p>",
                        "<p>Ceiling : Room</p>",
                        "<p>Rainbow : Sky</p>"
                    ],
                    options_hi: [
                        "<p>पहाड़ी : पर्वतमाला</p>",
                        "<p>नाला : बांध</p>",
                        "<p>छत : कमरा</p>",
                        "<p>इन्द्रधनुष : आकाश</p>"
                    ],
                    solution_en: "<p>11.(c)&nbsp; As Palate is the upper part of mouth similarly the Ceiling is the upper part of Room.</p>",
                    solution_hi: "<p>11.(c) जैसे तालुआ मुंह का ऊपरी भाग है उसी प्रकार छत कमरे का ऊपरी भाग है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. In a certain code language, \'PARTIAL\' is written as \'MZJSSZQ\' and \'RADICAL\' is written as \'MZDHEZS\'. How will \'RESPOND\' be written as in that language ?</p>",
                    question_hi: "<p>12. एक निश्चित कूट भाषा में \'PARTIAL\' को \'MZJSSZQ\' और \'RADICAL\' को \'MZDHEZS\' लिखा जाता है। उस भाषा में \'RESPOND\' को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>SDTOPME</p>",
                        "<p>EMQOTDS</p>",
                        "<p>EMPOTDS</p>",
                        "<p>EMPQTDS</p>"
                    ],
                    options_hi: [
                        "<p>SDTOPME</p>",
                        "<p>EMQOTDS</p>",
                        "<p>EMPOTDS</p>",
                        "<p>EMPQTDS</p>"
                    ],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504456279.png\" alt=\"rId15\" width=\"175\" height=\"92\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504456522.png\" alt=\"rId16\" width=\"173\" height=\"91\"><br>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504457396.png\" alt=\"rId17\" width=\"174\" height=\"90\"></p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504456279.png\" alt=\"rId15\" width=\"175\" height=\"92\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504456522.png\" alt=\"rId16\" width=\"173\" height=\"91\"><br>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504457396.png\" alt=\"rId17\" width=\"174\" height=\"90\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. What should come in place of the question mark (?) in the given series ?&nbsp;<br>8796, 4392, 1092, 176, ?, &minus;4.4</p>",
                    question_hi: "<p>13. दी गई शृंखला में प्रश्न-चिह्ल (?) के स्थान पर क्या आना चाहिए ?<br>8796, 4392, 1092, 176, ?, &minus;4.4</p>",
                    options_en: [
                        "<p>16</p>",
                        "<p>22</p>",
                        "<p>38</p>",
                        "<p>11.6</p>"
                    ],
                    options_hi: [
                        "<p>16</p>",
                        "<p>22</p>",
                        "<p>38</p>",
                        "<p>11.6</p>"
                    ],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504459032.png\" alt=\"rId18\" width=\"321\" height=\"45\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504459032.png\" alt=\"rId18\" width=\"321\" height=\"45\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the option in which the numbers share the same relationship as that shared by the given triad of numbers .<br>(4, 16 , 60)<br>(2, 4 , 6)<br>(<strong>NOTE : </strong>Operations should be preformed on the whole numbers , without breaking drown the numbers into its constituent digits . E.g. 13 - Operations on 13 such as adding / subtracting /multiplying etc. to 13 can be performed . Breaking drown 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.</p>",
                    question_hi: "<p>14. उस विकल्प का चयन कीजिए, जिसमें संख्याएं वही संबंध साझा करती हैं, जो दी गई तीन संख्याओं के समूह द्वारा साझा किया जाता है।<br>(4, 16, 60)<br>(2, 4, 6)<br>(<strong>नोटः </strong>संख्याओं को उनके घटक अंकों में तोड़े बिना संक्रियाएँ केवल पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए- 13 पर की जाने वाली संक्रियाएँ, जैसे 13 में जोड़ना / घटाना / गुणा करना इत्यादि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>5 , 25 , 125</p>",
                        "<p>3 , 9 , 24</p>",
                        "<p>8 , 56 , 448</p>",
                        "<p>7 , 49 , 42</p>"
                    ],
                    options_hi: [
                        "<p>5 , 25 , 125</p>",
                        "<p>3 , 9 , 24</p>",
                        "<p>8 , 56 , 448</p>",
                        "<p>7 , 49 , 42</p>"
                    ],
                    solution_en: "<p>14.(b)<br><strong>Logic:-</strong> 1<sup>st</sup>no. &times; 2<sup>nd</sup>no. - 1<sup>st</sup>no. = 3<sup>rd</sup>no.<br>(4, 16, 60):- 4 &times; 16 - 4 &rarr; 64 - 4 = 60<br>(2, 4, 6):- 2 &times; 4 - 2 &rarr; 8 - 2 = 6<br>Similarly<br>(3, 9, 24):- 3 &times; 9 - 3 &rarr; 27 - 3 = 24</p>",
                    solution_hi: "<p>14.(b)<br><strong>तर्क:- </strong>पहली संख्या &times; दूसरी संख्या - पहली संख्या = तीसरी संख्या <br>(4, 16, 60):- 4 &times; 16 - 4 &rarr; 64 - 4 = 60<br>(2, 4, 6):- 2 &times; 4 - 2 &rarr; 8 - 2 = 6<br>इसी प्रकार<br>(3, 9, 24):- 3 &times; 9 - 3 &rarr; 27 - 3 = 24</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Which figure should replace the question mark (?) if the following series were to be continued ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504459598.png\" alt=\"rId19\" width=\"383\" height=\"79\"></p>",
                    question_hi: "<p>15. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504459598.png\" alt=\"rId19\" width=\"383\" height=\"79\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504459997.png\" alt=\"rId20\" width=\"85\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504460740.png\" alt=\"rId21\" width=\"85\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504461851.png\" alt=\"rId22\" width=\"85\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504462349.png\" alt=\"rId23\" width=\"85\" height=\"87\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504459997.png\" alt=\"rId20\" width=\"85\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504460740.png\" alt=\"rId21\" width=\"85\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504461851.png\" alt=\"rId22\" width=\"85\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504462349.png\" alt=\"rId23\" width=\"84\" height=\"86\"></p>"
                    ],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504461851.png\" alt=\"rId22\" width=\"86\" height=\"89\"></p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504461851.png\" alt=\"rId22\" width=\"86\" height=\"89\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504462463.png\" alt=\"rId24\" width=\"132\" height=\"115\"></p>",
                    question_hi: "<p>16. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504462463.png\" alt=\"rId24\" width=\"132\" height=\"115\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504463489.png\" alt=\"rId25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504463760.png\" alt=\"rId26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504464058.png\" alt=\"rId27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504468250.png\" alt=\"rId28\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504463489.png\" alt=\"rId25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504463760.png\" alt=\"rId26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504464058.png\" alt=\"rId27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504468250.png\" alt=\"rId28\"></p>"
                    ],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504463489.png\" alt=\"rId25\"></p>",
                    solution_hi: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504463489.png\" alt=\"rId25\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Which option represents the correct order of the given words as they would appear in an English dictionary ?<br>1. Measurement<br>2. Methodology<br>3. Merchandise<br>4. Meaningless<br>5. Memorabilia</p>",
                    question_hi: "<p>17. निम्नलिखित में से कौन-सा विकल्प दिए गए शब्दों के उस सही क्रम का निरूपण करता है, जिस क्रम में वे अंग्रेजी शब्दकोश में दिखाई देते हैं ?<br>1. Measurement<br>2. Methodology<br>3. Merchandise<br>4. Meaningless<br>5. Memorabilia</p>",
                    options_en: [
                        "<p>4, 1, 5, 3, 2</p>",
                        "<p>2, 3, 4, 5, 1</p>",
                        "<p>5, 2, 1, 3, 4</p>",
                        "<p>5, 2, 4, 1, 3</p>"
                    ],
                    options_hi: [
                        "<p>4, 1, 5, 3, 2</p>",
                        "<p>2, 3, 4, 5, 1</p>",
                        "<p>5, 2, 1, 3, 4</p>",
                        "<p>5, 2, 4, 1, 3</p>"
                    ],
                    solution_en: "<p>17.(a) <strong>The correct order is</strong> <br>Meaningless(4) &rarr; Measurement(1) &rarr; Memorabilia(5) &rarr; Merchandise(3) &rarr; Methodology(2)</p>",
                    solution_hi: "<p>17.(a) <strong>सही क्रम है</strong><br>Meaningless(4) &rarr; Measurement(1) &rarr; Memorabilia(5) &rarr; Merchandise(3) &rarr; Methodology(2)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504469662.png\" alt=\"rId29\" width=\"118\" height=\"112\"></p>",
                    question_hi: "<p>18. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504469662.png\" alt=\"rId29\" width=\"118\" height=\"112\"></p>",
                    options_en: [
                        "<p>16</p>",
                        "<p>18</p>",
                        "<p>15</p>",
                        "<p>17</p>"
                    ],
                    options_hi: [
                        "<p>16</p>",
                        "<p>18</p>",
                        "<p>15</p>",
                        "<p>17</p>"
                    ],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504470372.png\" alt=\"rId30\" width=\"203\" height=\"198\"><br>There are 16 triangles = ABH, BCD, DEF, FGH, BHF, BDF, JMN, KJN, KNL, LON, JNL, OVP, PQR, RST, TUV, PWV,</p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504470372.png\" alt=\"rId30\" width=\"203\" height=\"198\"><br>16 त्रिभुज हैं = ABH, BCD, DEF, FGH, BHF, BDF, JMN, KJN, KNL, LON, JNL, OVP, PQR, RST, TUV, PWV,</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504470982.png\" alt=\"rId31\" width=\"110\" height=\"125\"></p>",
                    question_hi: "<p>19. उस विकल्प आकृति का चयन कीजिए, जिसमें दी गई आकृति (X) उसके एक भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504470982.png\" alt=\"rId31\" width=\"110\" height=\"125\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504472616.png\" alt=\"rId32\" width=\"98\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504472812.png\" alt=\"rId33\" width=\"99\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504473669.png\" alt=\"rId34\" width=\"97\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504474341.png\" alt=\"rId35\" width=\"98\" height=\"86\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504472616.png\" alt=\"rId32\" width=\"99\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504472812.png\" alt=\"rId33\" width=\"104\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504473669.png\" alt=\"rId34\" width=\"98\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504474341.png\" alt=\"rId35\" width=\"102\" height=\"90\"></p>"
                    ],
                    solution_en: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504475207.png\" alt=\"rId36\" width=\"115\" height=\"97\"></p>",
                    solution_hi: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504475207.png\" alt=\"rId36\" width=\"115\" height=\"97\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If WATER is coded as 30 and ORANGE is coded as 36, what will EIGHT be coded as ?</p>",
                    question_hi: "<p>20. यदि WATER को 30 के रूप में और ORANGE को 36 के रूप में कूट बद्ध किया जाता है, तो EIGHT को किस रूप में कूट बद्ध किया जाएगा ?</p>",
                    options_en: [
                        "<p>25</p>",
                        "<p>36</p>",
                        "<p>42</p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>25</p>",
                        "<p>36</p>",
                        "<p>42</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>20.(d) <strong>Logic :- </strong>(Number of letter) &times; 6<br>WATER :- (5 Letter) &times; 6 = 30<br>ORANGE :- (6 Letter) &times; 6 = 36<br>Similarly,<br>EIGHT :- (5 Letter) &times; 6 = 30</p>",
                    solution_hi: "<p>20.(d) <strong>तर्क :- </strong>(अक्षर की संख्या) &times; 6<br>WATER :- (5 अक्षर) &times; 6 = 30<br>ORANGE :- (6 अक्षर) &times; 6 = 36<br>इसी प्रकार,<br>EIGHT :- (5 अक्षर) &times; 6 = 30</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the option that represents the letters that, when sequentially placed in the blanks below, will complete the letter-series.<br>e_bho_etb_ _met_hom_tbhom</p>",
                    question_hi: "<p>21. उस विकल्प का चयन कीजिए, जो उन अक्षरों का निरूपण करता हो, जिन्हें नीचे दिए गए रिक्त स्थानों में क्रमिक रूप से रखे जाने पर यह अक्षर-शृंखला पूरी होगी।<br>e_bho_etb_ _met_hom_tbhom</p>",
                    options_en: [
                        "<p>tmbhoe</p>",
                        "<p>mhtobe</p>",
                        "<p>hmtebo</p>",
                        "<p>tmhobe</p>"
                    ],
                    options_hi: [
                        "<p>tmbhoe</p>",
                        "<p>mhtobe</p>",
                        "<p>hmtebo</p>",
                        "<p>tmhobe</p>"
                    ],
                    solution_en: "<p>21.(d) e<span style=\"text-decoration: underline;\"><strong>t</strong></span>bho<span style=\"text-decoration: underline;\"><strong>m</strong></span> / etb<strong><span style=\"text-decoration: underline;\">ho</span></strong>m / et<span style=\"text-decoration: underline;\"><strong>b</strong></span>hom / <span style=\"text-decoration: underline;\"><strong>e</strong></span>tbhom</p>",
                    solution_hi: "<p>21.(d) e<span style=\"text-decoration: underline;\"><strong>t</strong></span>bho<span style=\"text-decoration: underline;\"><strong>m</strong></span> / etb<strong><span style=\"text-decoration: underline;\">ho</span></strong>m / et<span style=\"text-decoration: underline;\"><strong>b</strong></span>hom / <span style=\"text-decoration: underline;\"><strong>e</strong></span>tbhom</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Radha visits her brother Rohan, who stays with their father Prasant, mother Sarala and grandfather Raghuvir. Rohan has two children, Binny and Champak. Binny\'s husband is Keshab, and Champak&rsquo;s wife is Tina. Tina&rsquo;s daughter helped Radha to find their home. How is Tina&rsquo;s daughter related to Rohan ?</p>",
                    question_hi: "<p>22. राधा अपने भाई रोहन से मिलने जाती है, जो अपने पिता प्रशांत, माता सरला और दादा रघुवीर के साथ रहता है। रोहन के दो बच्चे बिन्नी और चंपक हैं। बिन्नी के पति केशव और चंपक की पत्नी टीना हैं। टीना की पुत्री ने राधा को उनका घर ढूंढने में मदद की। टीना की पुत्री का रोहन से क्या संबंध है ?</p>",
                    options_en: [
                        "<p>Son</p>",
                        "<p>Daughter</p>",
                        "<p>Son&rsquo;s son</p>",
                        "<p>Son&rsquo;s daughter</p>"
                    ],
                    options_hi: [
                        "<p>पुत्र</p>",
                        "<p>पुत्री</p>",
                        "<p>पुत्र का पुत्र</p>",
                        "<p>पुत्र की पुत्री</p>"
                    ],
                    solution_en: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504477540.png\" alt=\"rId37\" width=\"282\" height=\"255\"><br>Tina&rsquo;s daughter is the daughter of Rohan&rsquo;s son.</p>",
                    solution_hi: "<p>22.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504478162.png\" alt=\"rId38\" width=\"295\" height=\"275\"><br>टीना की बेटी रोहन के बेटे की बेटी है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Three statements are given, followed by Three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Only a few ladders are walls.<br>Some ladders are vans.<br>All vans are cans.<br><strong>Conclusions :</strong><br>I. Some cans are ladders.<br>II. All walls being cans is a possibility.<br>III. No vans can be walls.</p>",
                    question_hi: "<p>23. तीन कथन दिए गए हैं जिनके बाद ।, II और III से संख्यांकित तीन निष्कर्ष दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन सा/से निष्कर्ष, कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं ?<br><strong>कथन :</strong><br>केवल कुछ सीढ़ियाँ, दीवार हैं।<br>कुछ सीढ़ियाँ, वैन हैं।<br>सभी वैन, कैन हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ कैन, सीढ़ियां हैं।<br>II. सभी दीवारों के कैन होने की संभावना है।<br>III. कोई भी वैन दीवार नहीं हो सकती।</p>",
                    options_en: [
                        "<p>Both conclusions I and II follow</p>",
                        "<p>All conclusions, I, II and III, follow</p>",
                        "<p>Both conclusions I and III follow</p>",
                        "<p>Both conclusions II and III follow</p>"
                    ],
                    options_hi: [
                        "<p>निष्कर्ष । और II दोनों अनुसरण करते हैं</p>",
                        "<p>निष्कर्ष ।, II और III सभी अनुसरण करते हैं</p>",
                        "<p>निष्कर्ष I और III दोनों अनुसरण करते हैं</p>",
                        "<p>निष्कर्ष II और III दोनों अनुसरण करते हैं</p>"
                    ],
                    solution_en: "<p>23.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504478690.png\" alt=\"rId39\" width=\"314\" height=\"107\"><br>Both conclusions I and II follow.</p>",
                    solution_hi: "<p>23.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504479103.png\" alt=\"rId40\" width=\"313\" height=\"114\"><br>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Four words are EQUATION, MATHS, WINTER and RAIN. When counted from the left, if every fourth letter in each of the above words is changed to the previous letter in the English alphabetical order, how many words thus formed will have no letter appearing more than once ?</p>",
                    question_hi: "<p>24. चार शब्द EQUATION, MATHS, WINTER और RAIN हैं। बाएँ से गिनने पर, यदि उपरोक्त प्रत्येक शब्द के प्रत्येक चौथे अक्षर को अंग्रेजी वर्णमाला क्रम में पिछले अक्षर से बदल दिया जाए, तो इस प्रकार बने कितने शब्दों में कोई अक्षर एक से अधिक बार नहीं आएगा ?</p>",
                    options_en: [
                        "<p>One</p>",
                        "<p>Four</p>",
                        "<p>Three</p>",
                        "<p>Two</p>"
                    ],
                    options_hi: [
                        "<p>एक</p>",
                        "<p>चार</p>",
                        "<p>तीन</p>",
                        "<p>दो</p>"
                    ],
                    solution_en: "<p>24.(b)<strong> Given :- </strong>EQUATION , MATHS, WINTER , RAIN<br>According to the given instruction after every fourth letter changed to previous letter we get<br>EQU<strong>Z</strong>TION , MAT<strong>G</strong>S , WIN<strong>S</strong>ER , RAI<strong>M</strong><br>Four words thus formed have no letter appear more than once.</p>",
                    solution_hi: "<p>24.(b) <strong>दिया गया:-</strong> EQUATION , MATHS, WINTER , RAIN<br>दिए गए निर्देश के अनुसार प्रत्येक चौथा अक्षर पिछले अक्षर में बदलने के बाद हमें प्राप्त होता है<br>EQU<strong>Z</strong>TION , MAT<strong>G</strong>S , WIN<strong>S</strong>ER , RAI<strong>M</strong><br>इस प्रकार बने चार शब्दों में कोई भी अक्षर एक से अधिक बार नहीं आया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option figure that will replace the question mark (?) in the figure given below and complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504479648.png\" alt=\"rId41\" width=\"108\" height=\"108\"></p>",
                    question_hi: "<p>25. उस विकल्प आकृति का चयन करें जो नीचे दी गई आकृति में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी और पैटर्न को पूरा करेगीI<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504479648.png\" alt=\"rId41\" width=\"108\" height=\"108\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504480133.png\" alt=\"rId42\" width=\"80\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504480833.png\" alt=\"rId43\" width=\"80\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504481403.png\" alt=\"rId44\" width=\"81\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504481808.png\" alt=\"rId45\" width=\"80\" height=\"79\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504480133.png\" alt=\"rId42\" width=\"80\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504480833.png\" alt=\"rId43\" width=\"81\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504481403.png\" alt=\"rId44\" width=\"79\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504481808.png\" alt=\"rId45\" width=\"80\" height=\"79\"></p>"
                    ],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504480833.png\" alt=\"rId43\" width=\"81\" height=\"81\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504480833.png\" alt=\"rId43\" width=\"81\" height=\"81\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. The movement of electric charges produces electric current and thereby produces electromagnetic force, which results in _______.</p>",
                    question_hi: "<p>26. विद्युत आवेशों की गति से विद्युत धारा उत्पन्न होती है और इसके परिणामस्वरूप विद्युत चुम्बकीय बल उत्पन्न होता है, जिसके परिणामस्वरूप ______ होता है।</p>",
                    options_en: [
                        "<p>optics</p>",
                        "<p>electricity</p>",
                        "<p>Magnetism</p>",
                        "<p>gravity</p>"
                    ],
                    options_hi: [
                        "<p>प्रकाशिकी</p>",
                        "<p>बिजली</p>",
                        "<p>चुंबकत्व</p>",
                        "<p>गुरुत्वाकर्षण</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>Magnetism. </strong>The movement of electric charges generates an electric current, which creates both electric and magnetic fields. This phenomenon results in magnetism. When a charge moves at a constant velocity, it produces steady electric and magnetic fields, but these fields do not change with time, so they do not generate electromagnetic waves.</p>",
                    solution_hi: "<p>26.(c) <strong>चुंबकत्व।</strong> विद्युत आवेशों की गति से विद्युत धारा उत्पन्न होती है, जो विद्युत और चुंबकीय दोनों क्षेत्रों का निर्माण करती है। इस घटना के परिणामस्वरूप चुंबकत्व उत्पन्न होता है। जब कोई आवेश स्थिर वेग से गति करता है, तो वह स्थिर विद्युत और चुंबकीय क्षेत्र उत्पन्न करता है, लेकिन ये क्षेत्र समय के साथ नहीं बदलते, इसलिए वे विद्युत चुम्बकीय तरंगें उत्पन्न नहीं करते।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. In which Indian state was the country&rsquo;s first organic fisheries cluster launched ?</p>",
                    question_hi: "<p>27. देश का पहला जैविक मत्स्य पालन क्लस्टर किस भारतीय राज्य में लॉन्च किया गया ?</p>",
                    options_en: [
                        "<p>Meghalaya</p>",
                        "<p>Arunachal Pradesh</p>",
                        "<p>Sikkim</p>",
                        "<p>Manipur</p>"
                    ],
                    options_hi: [
                        "<p>मेघालय</p>",
                        "<p>अरुणाचल प्रदेश</p>",
                        "<p>सिक्किम</p>",
                        "<p>मणिपुर</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>Sikkim.</strong> It was launched in Soreng District, Sikkim, by Union Animal Husbandry Minister Rajiv Ranjan Singh. Organic fisheries cluster focuses on ecologically healthy fish farming systems avoiding the use of harmful chemicals, antibiotics and pesticides.</p>",
                    solution_hi: "<p>27.(c) <strong>सिक्किम।</strong> इसे सिक्किम के सोरेंग जिले में केंद्रीय पशुपालन मंत्री राजीव रंजन सिंह ने लॉन्च किया। जैविक मत्स्य पालन क्लस्टर हानिकारक रसायनों, एंटीबायोटिक दवाओं और कीटनाशकों के उपयोग से बचते हुए पारिस्थितिक रूप से स्वस्थ मछली पालन प्रणालियों पर ध्यान केंद्रित करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28 Which two beaches in India recently received the prestigious Blue Flag certification ?</p>",
                    question_hi: "<p>28. भारत के किन दो समुद्र तटों को हाल ही में प्रतिष्ठित ब्लू फ्लैग प्रमाणन प्राप्त हुआ है ?</p>",
                    options_en: [
                        "<p>Marina and Juhu Beaches</p>",
                        "<p>Kappad and Chal Beaches</p>",
                        "<p>Varkala and Kovalam Beaches</p>",
                        "<p>Palolem and Baga Beaches</p>"
                    ],
                    options_hi: [
                        "<p>मरीना और जुहू समुद्र तट</p>",
                        "<p>कप्पड़ और चाल समुद्र तट</p>",
                        "<p>वर्कला और कोवलम समुद्र तट</p>",
                        "<p>पालोलेम और बागा समुद्र तट</p>"
                    ],
                    solution_en: "<p>28.(b) <strong>Kappad and Chal Beaches.</strong> Kerala\'s Kappad beach in Kozhikode and Chal beach in Kannur have received the prestigious Blue Flag certification from Denmark\'s Foundation for Environmental Education (FEE). This recognition highlights their high standards in environmental protection, safety, and sustainability.</p>",
                    solution_hi: "<p>28.(b)<strong> कप्पड़ और चाल समुद्र तट। </strong>केरल के कोझिकोड में कप्पड़ समुद्र तट और कन्नूर में चाल समुद्र तट को डेनमार्क के पर्यावरण शिक्षा फाउंडेशन (FEE) से प्रतिष्ठित ब्लू फ्लैग प्रमाणन प्राप्त हुआ है। यह मान्यता पर्यावरण संरक्षण, सुरक्षा और स्थिरता में उनके उच्च मानकों को उजागर करती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who launched the first comic book in the \"Heroes of the Indian Air Force\" series on August 28, 2024 ?</p>",
                    question_hi: "<p>29. 28 अगस्त, 2024 को \"हीरोज ऑफ द इंडियन एयर फोर्स\" श्रृंखला की पहली कॉमिक बुक किसने प्रारंभ की ?</p>",
                    options_en: [
                        "<p>Air Chief Marshal V R Chaudhari</p>",
                        "<p>Raghuram Rajan</p>",
                        "<p>Air Marshal Vikram Singh</p>",
                        "<p>Dr. Bibhab Kumar Talukdar</p>"
                    ],
                    options_hi: [
                        "<p>एयर चीफ मार्शल वी आर चौधरी</p>",
                        "<p>रघुराम राजन</p>",
                        "<p>एयर मार्शल विक्रम सिंह</p>",
                        "<p>डॉ. बिभाब कुमार तालुकदार</p>"
                    ],
                    solution_en: "<p>29.(a) <strong>Air Chief Marshal V R Chaudhari. </strong>He launched the first comic book in the series, developed by the National Film Development Corporation and Rogue Communications, to inspire young readers by showcasing IAF personnel\'s bravery and heroism.</p>",
                    solution_hi: "<p>29.(a) <strong>एयर चीफ मार्शल वी आर चौधरी।</strong> उन्होंने राष्ट्रीय फिल्म विकास निगम और दुष्ट संचार द्वारा विकसित श्रृंखला की पहली कॉमिक बुक प्रारंभ की, जिसका उद्देश्य भारतीय वायुसेना कर्मियों की बहादुरी और वीरता को प्रदर्शित करके युवा पाठकों को प्रेरित करना है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Sir Garfield Sobers Trophy is related to which of the following sports events ?</p>",
                    question_hi: "<p>30. सर गारफील्ड सोबर्स ट्रॉफी निम्नलिखित में से किस खेल आयोजन से संबंधित है ?</p>",
                    options_en: [
                        "<p>Tennis</p>",
                        "<p>Football</p>",
                        "<p>Hockey</p>",
                        "<p>Cricket</p>"
                    ],
                    options_hi: [
                        "<p>टेनिस</p>",
                        "<p>फुटबॉल</p>",
                        "<p>हॉकी</p>",
                        "<p>क्रिकेट</p>"
                    ],
                    solution_en: "<p>30.(d) <strong>Cricket.</strong> The Sir Garfield Sobers Trophy is a prestigious award presented annually by the International Cricket Council (ICC) to the ICC Men\'s Cricketer of the Year. Named after the legendary West Indies cricketer Sir Garfield Sobers, the trophy was first awarded to Rahul Dravid in 2004. The latest recipient is Jasprit Bumrah (India, 2024).</p>",
                    solution_hi: "<p>30.(d) <strong>क्रिकेट। </strong>सर गारफील्ड सोबर्स ट्रॉफी एक प्रतिष्ठित पुरस्कार है जो अंतर्राष्ट्रीय क्रिकेट परिषद (ICC) द्वारा ICC पुरुष क्रिकेटर ऑफ द ईयर को प्रतिवर्ष प्रदान किया जाता है। वेस्टइंडीज के महान क्रिकेटर सर गारफील्ड सोबर्स के नाम पर, यह ट्रॉफी पहली बार 2004 में राहुल द्रविड़ को प्रदान की गई थी। हाल ही में प्राप्तकर्ता जसप्रीत बुमराह (भारत, 2024) हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. In October 2020, which of the following Ministries launched the Ayushman Sahakar Scheme, to assist cooperatives in the creation of healthcare infrastructure ?</p>",
                    question_hi: "<p>31. अक्टूबर 2020 में, निम्नलिखित में से किस मंत्रालय ने स्वास्थ्य सेवा संबंधी आधारभूत संरचना के निर्माण में सहकारी समितियों की सहायता के लिए आयुष्मान सहकार स्कीम का शुभारंभ किया ?</p>",
                    options_en: [
                        "<p>Ministry of Commerce and Industry</p>",
                        "<p>Ministry of Health and Family Welfare</p>",
                        "<p>Ministry of Agriculture and Farmers Welfare</p>",
                        "<p>Ministry of Corporate Affairs</p>"
                    ],
                    options_hi: [
                        "<p>वाणिज्य एवं उद्योग मंत्रालय</p>",
                        "<p>स्वास्थ्य एवं परिवार कल्याण मंत्रालय</p>",
                        "<p>कृषि एवं किसान कल्याण मंत्रालय</p>",
                        "<p>कॉर्पोरेट कार्यमंत्रालय</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>Ministry of Agriculture and Farmers Welfare. </strong>Union Minister of State for Agriculture, Shri Parshottam Rupala, launched the &lsquo;Ayushman Sahakar Scheme&rsquo; in 2020 to assist cooperatives in developing healthcare infrastructure. The scheme is implemented by the National Cooperative Development Corporation (NCDC). Other related schemes include Ayushman Bharat Pradhan Mantri Jan Arogya Yojana (AB-PMJAY), launched by the Ministry of Health and Family Welfare in September 2018, and the National Rural Health Mission (NRHM), initiated by the same ministry in 2005.</p>",
                    solution_hi: "<p>31.(c)<strong> कृषि एवं किसान कल्याण मंत्रालय। </strong>केंद्रीय कृषि राज्य मंत्री श्री पुरुषोत्तम रूपाला ने स्वास्थ्य सेवा के बुनियादी ढांचे के विकास में सहकारी समितियों की सहायता के लिए 2020 में &lsquo;आयुष्मान सहकार योजना&rsquo; शुरू की। इस योजना का क्रियान्वयन राष्ट्रीय सहकारी विकास निगम (NCDC) द्वारा किया जाता है। अन्य संबंधित योजनाओं में आयुष्मान भारत प्रधानमंत्री जन आरोग्य योजना (AB-PMJAY) शामिल है, जिसे सितंबर 2018 में स्वास्थ्य एवं परिवार कल्याण मंत्रालय द्वारा शुरू किया गया था, और राष्ट्रीय ग्रामीण स्वास्थ्य मिशन (NRHM), जिसे 2005 में इसी मंत्रालय द्वारा शुरू किया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Sashastra Seema Bal is the lead intelligence unit for the_______and Bhutan borders and the coordinating agency for national security activities.</p>",
                    question_hi: "<p>32. सशस्त्र सीमा बल ______और भूटान सीमाओं के लिए प्रमुख खुफिया इकाई और राष्ट्रीय सुरक्षा गतिविधियों के लिए समन्वय संस्था है।</p>",
                    options_en: [
                        "<p>Nepal</p>",
                        "<p>Myanmar</p>",
                        "<p>Pakistan</p>",
                        "<p>Bangladesh</p>"
                    ],
                    options_hi: [
                        "<p>नेपाल</p>",
                        "<p>म्यांमार</p>",
                        "<p>पाकिस्तान</p>",
                        "<p>बांग्लादेश</p>"
                    ],
                    solution_en: "<p>32.(a) <strong>Nepal. </strong>Sashastra Seema Bal (SSB) was established as the Special Service Bureau in May 1963. Headquarter - New Delhi. Motto - Service, Security and Brotherhood. The Border Security Force (BSF) is responsible for the Bangladesh and Pakistan borders, while the Indo-Tibetan Border Police (ITBP) oversees the border with China.</p>",
                    solution_hi: "<p>32.(a) <strong>नेपाल। </strong>सशस्त्र सीमा बल (SSB) की स्थापना मई 1963 में विशेष सेवा ब्यूरो के रूप में की गई थी। मुख्यालय - नई दिल्ली। आदर्श वाक्य - सेवा, सुरक्षा और भाईचारा। सीमा सुरक्षा बल (BSF) बांग्लादेश और पाकिस्तान सीमाओं की निगरानी लिए उत्तरदायी है, जबकि भारत-तिब्बत सीमा पुलिस (ITBP) चीन के साथ लगे सीमाओं की निगरानी करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. The Karbi tribe, which celebrates the cultural festival &lsquo;Rongker&rsquo;, belongs to the Indian state of ________.</p>",
                    question_hi: "<p>33. कार्बी जनजाति, जो सांस्कृतिक त्योहार \'रोंगकर\' मनाती है, भारतीय राज्य ______से संबंधित है।</p>",
                    options_en: [
                        "<p>Kerala</p>",
                        "<p>Maharashtra</p>",
                        "<p>Assam</p>",
                        "<p>Uttarakhand</p>"
                    ],
                    options_hi: [
                        "<p>केरल</p>",
                        "<p>महाराष्ट्र</p>",
                        "<p>असम</p>",
                        "<p>उत्तराखंड</p>"
                    ],
                    solution_en: "<p>33.(c) <strong>Assam. </strong>Rongker, winter post-harvest festival, is celebrated either on 5th January or 5th February as per the convenience of the villagers. Festival and their tribes : Kherai Puja of the Bodos; the Baikhu and Pharkantis of the Rabhas; the Ali-ai-ligang and Parag of the Mishing tribe; and the Sagra-misawa, wansawa, and laghun of the Tiwas.</p>",
                    solution_hi: "<p>33.(c) <strong>असम।</strong> रोंगकर, सर्दियों में फसल कटाई के बाद का त्यौहार है, जो ग्रामीणों की सुविधा के अनुसार 5 जनवरी या 5 फरवरी को मनाया जाता है। त्योहार और उनकी जनजातीय समुदाय: बोडो जनजाति की खेराई पूजा; राभा जनजाति की बैकु और फर्खांतीस; मिशिंग जनजाति की अली-ऐ-लिगांग और पराग; और तिवा जनजाति की सगरा - मिसावा, वांसावा और लघुन।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who among the following physicists invented the voltaic pile, the forerunner of the modern battery, in 1800 ?</p>",
                    question_hi: "<p>34. 1800 में, निम्नलिखित भौतिक विज्ञानियों में से किसने, आधुनिक बैटरी के पूर्वगामी वोल्टाइक पाइल का आविष्कार किया था ?</p>",
                    options_en: [
                        "<p>Andre-Marie Ampere</p>",
                        "<p>George Simon</p>",
                        "<p>Alessandro Volta</p>",
                        "<p>Hans Christian Oersted</p>"
                    ],
                    options_hi: [
                        "<p>आंद्रे-मैरी एम्पीयर (Andre-Marie Ampere)</p>",
                        "<p>जॉर्ज साइमन (George Simon)</p>",
                        "<p>एलेसेंड्रो वोल्टा (Alessandro Volta)</p>",
                        "<p>हैंस क्रिश्चियन ओस्टेड (Hans Christian Oersted)</p>"
                    ],
                    solution_en: "<p>34.(c) <strong>Alessandro Volta.</strong> Important Inventions and Discoveries: George Stephenson - In 1814, he constructed a locomotive, called &lsquo;The Blutcher&rsquo;, that could pull a weight of 30 tons up a hill at 4 mph. Hans Christian Oersted - In 1820 he discovered a compass needle deflected when an electric current passed through a metallic wire placed nearby. Andr&eacute;-Marie Amp&egrave;re - Well-known for his contributions to the field of electromagnetism (specifically Amp&egrave;re\'s law).</p>",
                    solution_hi: "<p>34.(c)<strong> एलेसेंड्रो वोल्टा।</strong> महत्वपूर्ण आविष्कारक एवं आविष्कार: जॉर्ज स्टीफेंसन - 1814 में, उन्होंने \'द ब्लचर\' नामक एक लोकोमोटिव का निर्माण किया, जो 4 मील प्रति घंटे की रफ़्तार से 30 टन वजन को पहाड़ी पर खींच सकता था। हैंस क्रिश्चियन ओर्स्टेड - 1820 में उन्होंने एक कम्पास सुई की खोज की, जो पास में रखे एक धातु के तार से विद्युत प्रवाह गुजरने पर विक्षेपित हो जाती थी। आंद्रे-मैरी एम्पीयर - विद्युत चुंबकत्व (विशेष रूप से एम्पीयर का नियम) के क्षेत्र में उनके योगदान के लिए प्रसिद्ध।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Truth, Love and a Little Malice is the autobiography of:</p>",
                    question_hi: "<p>35. ट्रुथ, लव एंड ए लिटिल मैलिस (Truth, Love and a Little Malice) किसकी आत्मकथा है ?</p>",
                    options_en: [
                        "<p>Arjun Singh</p>",
                        "<p>Satyajit Ray</p>",
                        "<p>Phoolan Devi</p>",
                        "<p>Khushwant Singh</p>"
                    ],
                    options_hi: [
                        "<p>अर्जुन सिंह</p>",
                        "<p>सत्यजीत रे</p>",
                        "<p>फूलन देवी</p>",
                        "<p>खुशवंत सिंह</p>"
                    ],
                    solution_en: "<p>35.(d) <strong>Khushwant Singh.</strong> He was an Indian author, lawyer, diplomat, journalist and politician. Awards - Padma Bhushan (1974), Padma Vibhushan (2007), Sahitya Akademi Fellowship (2010). Books by Him: &lsquo;&rsquo;Train to Pakistan&rsquo;&rsquo;, &lsquo;&rsquo;Sex, Scotch &amp; Scholarship&rsquo;&rsquo;, &lsquo;&rsquo;Land of Five Rivers&rsquo;&rsquo;, &lsquo;&rsquo;A History of the Sikhs&rsquo;&rsquo;, &lsquo;&rsquo;I Shall Not Hear the Nightingale&rsquo;&rsquo;.</p>",
                    solution_hi: "<p>35.(d) <strong>खुशवंत सिंह </strong>एक भारतीय लेखक, वकील, राजनयिक, पत्रकार और राजनीतिज्ञ थे। पुरस्कार - पद्म भूषण (1974), पद्म विभूषण (2007), साहित्य अकादमी फेलोशिप (2010)। उनकी पुस्तकें: &lsquo;ट्रेन टू पाकिस्तान&rsquo;, &lsquo;सेक्स, स्कॉच एंड स्कॉलरशिप&rsquo;, &lsquo;लैंड ऑफ फाइव रिवर्स&rsquo;, &lsquo;ए हिस्ट्री ऑफ द सिख&rsquo;, &lsquo;आई शैल नॉट हियर द नाइटिंगेल&rsquo;।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Identify the INCORRECT pair.</p>",
                    question_hi: "<p>36. गलत युग्म की पहचान कीजिए।</p>",
                    options_en: [
                        "<p>Nor Westers - West Bengal</p>",
                        "<p>Bardoisila - Assam</p>",
                        "<p>Blossom Shower - Andhra Pradesh</p>",
                        "<p>Mango Shower - Kerala</p>"
                    ],
                    options_hi: [
                        "<p>उत्तर पश्चिमी हवा (Nor Westers) - पश्चिम बंगाल</p>",
                        "<p>बार्डोइसिला (Bardoisila) - असम</p>",
                        "<p>मंजरी वर्षण (Blossom Shower) - आंध्र प्रदेश</p>",
                        "<p>आम्र वर्षा (Mango Shower) - केरल</p>"
                    ],
                    solution_en: "<p>36.(c)<strong> Blossom Shower - Andhra Pradesh.</strong> Blossom shower is a local weather phenomenon that occurs during the hot weather season and causes coffee flowers to bloom in Kerala and nearby areas. It is also associated with the ripening of mango trees. These showers are common in Kerala and Karnataka.</p>",
                    solution_hi: "<p>36.(c) <strong>मंजरी वर्षण </strong>(Blossom Shower) - <strong>आंध्र प्रदेश। </strong>मंजरी वर्षा (Blossom Shower) एक स्थानीय मौसमी घटना है, जो ग्रीष्म ऋतु के दौरान केरल और इसके आसपास के क्षेत्रों में होती है। यह वर्षा कॉफी के फूलों के खिलने में मदद करती है और इसे आमतौर पर आम के पेड़ों के फल पकने से भी जोड़ा जाता है। मंजरी वर्षा मुख्यतः केरल और कर्नाटक में होती है और कृषि के लिए इसका विशेष महत्व है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Who among the following is NOT a vocalist ?</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन-से एक गायक नहीं हैं ?</p>",
                    options_en: [
                        "<p>Gangubai Hangal</p>",
                        "<p>Pt. Shiv Kumar Sharma</p>",
                        "<p>Pt. Kumar Gandharva</p>",
                        "<p>Pt. Bhimsen Joshi</p>"
                    ],
                    options_hi: [
                        "<p>गंगूबाई हंगल</p>",
                        "<p>पं. शिव कुमार शर्मा</p>",
                        "<p>पं. कुमार गंधर्व</p>",
                        "<p>पं. भीमसेन जोशी</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>Pandit Shivkumar Sharma</strong> was an Indian classical musician and santoor player. His Awards: Padma Vibhushan (2001), Padma shri (1991). Famous santoor players : Rahul Sharma, Sedat Anar, Satish Vyas, Tarun Bhattacharya.</p>",
                    solution_hi: "<p>37.(b) <strong>पं. शिव कुमार शर्मा </strong>एक भारतीय शास्त्रीय संगीतकार और संतूर वादक थे। उनके पुरस्कार: पद्म विभूषण (2001), पद्म श्री (1991)। प्रसिद्ध संतूर वादक: राहुल शर्मा, सेदत अनार, सतीश व्यास, तरूण भट्टाचार्य।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. What is the primary benefit offered to businesses in Special Economic Zones (SEZs) ?</p>",
                    question_hi: "<p>38. विशेष आर्थिक क्षेत्रों [Special Economic Zones (SEZs)] में व्यवसायों को दिया जाने वाला प्राथमिक लाभ क्या है ?</p>",
                    options_en: [
                        "<p>Tax and duty concessions</p>",
                        "<p>Guaranteed market share</p>",
                        "<p>Mandatory government contracts</p>",
                        "<p>Unlimited foreign investment</p>"
                    ],
                    options_hi: [
                        "<p>कर और शुल्क रियायतें</p>",
                        "<p>गारंटीयुक्त मार्केट शेयर</p>",
                        "<p>अनिवार्य सरकारी अनुबंध</p>",
                        "<p>असीमित विदेशी निवेश</p>"
                    ],
                    solution_en: "<p>38.(a)<strong> Tax and duty concessions. </strong>The incentives and facilities offered to the units in SEZs for attracting investments into the SEZs, including foreign investment include:- Duty free import/domestic procurement of goods for development, operation and maintenance of SEZ units. 100% Income Tax exemption on export income for SEZ units under Section 10AA of the Income Tax Act for first 5 years, 50% for next 5 years thereafter and 50% of the ploughed back export profit for next 5 years.</p>",
                    solution_hi: "<p>38.(a) <strong>कर और शुल्क रियायतें। </strong>विदेशी निवेश सहित विशेष आर्थिक क्षेत्र में निवेश को आकर्षित करने के लिए विशेष आर्थिक क्षेत्र में इकाइयों को दिए जाने वाले प्रोत्साहन और सुविधाओं में शामिल हैं:- विशेष आर्थिक क्षेत्र इकाइयों के विकास, संचालन और रखरखाव के लिए वस्तुओं का शुल्क मुक्त आयात/घरेलू खरीद। विशेष आर्थिक क्षेत्र इकाइयों के लिए आयकर अधिनियम की धारा 10AA के तहत निर्यात आय पर पहले 5 वर्षों के लिए 100% आयकर छूट, उसके बाद अगले 5 वर्षों के लिए 50% और अगले 5 वर्षों के लिए वापस किए गए निर्यात लाभ का 50%।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following statements is correct with respect to the Badminton game and match length ?</p>",
                    question_hi: "<p>39. बैडमिंटन खेल और मैच की लंबाई के संबंध में निम्नलिखित में से कौन-सा कथन सही है ?</p>",
                    options_en: [
                        "<p>A match consists of the best of two games of 21 points</p>",
                        "<p>A match consists of the best of five games of 21 points</p>",
                        "<p>A match consists of the best of three games of 21 points.</p>",
                        "<p>A match consists of the best of four games of 21 points.</p>"
                    ],
                    options_hi: [
                        "<p>एक मैच में 21 अंकों के सर्वश्रेष्ठ दो गेम शामिल होते हैं।</p>",
                        "<p>एक मैच में 21 अंकों के सर्वश्रेष्ठ पांच गेम शामिल होते हैं।</p>",
                        "<p>एक मैच में 21 अंकों के सर्वश्रेष्ठ तीन गेम शामिल होते हैं।</p>",
                        "<p>एक मैच में 21 अंकों के सर्वश्रेष्ठ चार गेम शामिल होते हैं।</p>"
                    ],
                    solution_en: "<p>39.(c) In badminton, a standard match is played as the best of three games. Each game is played to 21 points, and a player must win by at least a 2-point margin. If the score reaches 20-20, the game continues until one player or team leads by 2 points up to a maximum of 30 points. The player or team that wins two out of three games wins the match.</p>",
                    solution_hi: "<p>39.(c) बैडमिंटन में, एक मानक मैच तीन खेलों में से सर्वश्रेष्ठ के रूप में खेला जाता है। प्रत्येक खेल 21 अंकों तक खेला जाता है, और एक खिलाड़ी को कम से कम 2 अंकों के अंतर से जीतना चाहिए। यदि स्कोर 20-20 हो जाता है, तो खेल तब तक जारी रहता है जब तक कि एक खिलाड़ी या टीम 2 अंकों से आगे नहीं हो जाती, जो अधिकतम 30 अंकों तक हो सकता है। जो खिलाड़ी या टीम तीन में से दो गेम जीतती है, वह मैच जीत जाती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Marathi language is in the_______language family</p>",
                    question_hi: "<p>40. मराठी भाषा, _______भाषा परिवार में है।</p>",
                    options_en: [
                        "<p>Indo Aryan</p>",
                        "<p>Austric</p>",
                        "<p>Sino-India</p>",
                        "<p>Austro</p>"
                    ],
                    options_hi: [
                        "<p>इंडो आर्यन</p>",
                        "<p>ऑस्ट्रिक</p>",
                        "<p>चीन-भारत</p>",
                        "<p>ऑस्ट्रो</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Indo Aryan.</strong> Marathi is a language spoken in the Indian state of Maharashtra and belongs to the Indo-Aryan branch of the Indo-European language family. It is closely related to languages such as Hindi, Gujarati, and Punjabi. Austric: These are the languages spoken by the Munda or Kol group and spoken in central, eastern, and northeastern India.</p>",
                    solution_hi: "<p>40.(a) <strong>इंडो आर्यन। </strong>मराठी, भारतीय राज्य महाराष्ट्र में बोली जाने वाली एक भाषा है और यह इंडो-यूरोपीय भाषा परिवार की इंडो-आर्यन शाखा से संबंधित है। यह हिंदी, गुजराती और पंजाबी जैसी भाषाओं से बहुत मिलती-जुलती है। आस्ट्रिक : ये मुंडा या कोल समूह द्वारा बोली जाने वाली भाषाएँ हैं और मध्य, पूर्वी और पूर्वोत्तर भारत में बोली जाती हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The liquid water present in oceans, lakes and rivers gets converted to the gaseous form in the presence of sunlight in the water cycle. This step is called :</p>",
                    question_hi: "<p>41. जल चक्र (water cycle) में सूर्य के प्रकाश की उपस्थिति में महासागरों, झीलों और नदियों में उपस्थित द्रव जल, गैसीय अवस्था में परिवर्तित हो जाता है। इस चरण को क्या कहा जाता है ?</p>",
                    options_en: [
                        "<p>Evaporation</p>",
                        "<p>Precipitation</p>",
                        "<p>Melting</p>",
                        "<p>Condensation</p>"
                    ],
                    options_hi: [
                        "<p>वाष्पीकरण (Evaporation)</p>",
                        "<p>वर्षण (Precipitation)</p>",
                        "<p>गलन (Melting)</p>",
                        "<p>संघनन (Condensation)</p>"
                    ],
                    solution_en: "<p>41.(a) <strong>Evaporation. </strong>It is the process that changes liquid water to gaseous water (water vapor). Condensation is the process of water vapor turning back into liquid water. Precipitation is the process of water falling back to Earth as rain, snow, or hail. Melting is the process by which a substance changes from the solid phase to the liquid phase.</p>",
                    solution_hi: "<p>41.(a) <strong>वाष्पीकरण। </strong>यह वह प्रक्रिया है जो द्रव जल को गैसीय जल (जल वाष्प) में बदल देती है। संघनन वह प्रक्रिया है जिसमें जल वाष्प पुनः द्रव जल में बदल जाता है। वर्षण, बर्फ या ओलों के रूप में पृथ्वी पर वापस गिरने वाले जल की प्रक्रिया है। गलन वह प्रक्रिया है जिसके द्वारा कोई पदार्थ ठोस अवस्था से द्रव अवस्था में परिवर्तित होता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Select the correct order of processes used for water purification in waterworks.<br>1. Filtration<br>2. Chlorination<br>3. Suspended impurity sedimentation<br>4. Solid impurity sedimentation</p>",
                    question_hi: "<p>42. जलकल (waterworks) में जल शुद्धिकरण के लिए उपयोग की जाने वाली प्रक्रियाओं के सही क्रम का चयन कीजिए।<br>1. निस्पंदन<br>2. क्लोरीनीकरण<br>3. निलंबित अशुद्धता अवसादन<br>4. ठोस अशुद्धता अवसादन</p>",
                    options_en: [
                        "<p>2-1-4-3</p>",
                        "<p>1-2-3-4</p>",
                        "<p>4-3-2-1</p>",
                        "<p>4-3-1-2</p>"
                    ],
                    options_hi: [
                        "<p>2-1-4-3</p>",
                        "<p>1-2-3-4</p>",
                        "<p>4-3-2-1</p>",
                        "<p>4-3-1-2</p>"
                    ],
                    solution_en: "<p>42.(d) <strong>4-3-1-2. </strong>Sedimentation - Removes suspended particles from water by allowing them to settle in a tank due to their weight. Filtration - Separates suspended and colloidal impurities from water by passing it through a porous medium, such as layers of sand and gravel. Chlorination - Adds chlorine or chlorine compounds to water to kill bacteria, viruses, and other microbes.</p>",
                    solution_hi: "<p>42.(d) <strong>4-3-1-2. </strong>अवसादन - जल से निलंबित कणों को उनके भार के कारण एक टैंक में जमा होने की अनुमति देकर हटा देता है। निस्पंदन - बालू और कंकड़ की परतों जैसे एक छ्ननी माध्यम से जल को पारित करके निलंबित और कोलाइडी अशुद्धियों को अलग करता है। क्लोरीनीकरण - बैक्टीरिया, वायरस और अन्य सूक्ष्म जीवों को मारने के लिए जल में क्लोरीन या क्लोरीन यौगिक मिलाते है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. The headquarters of the Indian Institute of Tourism and Travel Management are located at:</p>",
                    question_hi: "<p>43. भारतीय पर्यटन और यात्रा प्रबंधन संस्थान का मुख्यालय _______में स्थित है।</p>",
                    options_en: [
                        "<p>Guwahati</p>",
                        "<p>Bhuvneshwar</p>",
                        "<p>Gwalior</p>",
                        "<p>New Delhi</p>"
                    ],
                    options_hi: [
                        "<p>गुवाहाटी</p>",
                        "<p>भुवनेश्वर</p>",
                        "<p>ग्वालियर</p>",
                        "<p>नई दिल्ली</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>Gwalior. </strong>The Indian Institute of Tourism and Travel Management, an autonomous body, operates under the Ministry of Tourism, Government of India. Other institutes include: Rajeev Gandhi National Youth Development (Sriperumbudur, Tamil Nadu), Swami Vivekanand National Institute of Rehabilitation Training and Research (Cuttack), Marine Engineering and Research (Kolkata), and Aryabhatta Research Institute (Nainital).</p>",
                    solution_hi: "<p>43.(c) <strong>ग्वालियर। </strong>भारतीय पर्यटन एवं यात्रा प्रबंधन संस्थान एक स्वायत्त निकाय है जो भारत सरकार के पर्यटन मंत्रालय के अधीन कार्य करता है। अन्य संस्थान : राजीव गांधी राष्ट्रीय युवा विकास (श्रीपेरंबदूर, तमिलनाडु), स्वामी विवेकानंद राष्ट्रीय पुनर्वास प्रशिक्षण एवं अनुसंधान संस्थान (कटक), समुद्री इंजीनियरिंग एवं अनुसंधान (कोलकाता), तथा आर्यभट्ट अनुसंधान संस्थान (नैनीताल)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following is NOT an important function of the Supreme Court ?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सा विकल्प सर्वोच्च न्यायालय का एक महत्वपूर्ण कार्य नहीं है ?</p>",
                    options_en: [
                        "<p>It acts to check on executive authorities and enforce the rule of law</p>",
                        "<p>It maintains federal equilibrium</p>",
                        "<p>It maintains of land records and revenue collection</p>",
                        "<p>It is the protector and guarantor of fundamental rights</p>"
                    ],
                    options_hi: [
                        "<p>यह कार्यकारी अधिकारियों की जाँच करने और कानून के शासन को लागू करने का कार्य करता है</p>",
                        "<p>यह संघीय संतुलन बनाए रखता है</p>",
                        "<p>यह भूमि अभिलेखों और राजस्व संग्रह का रखरखाव करता है</p>",
                        "<p>यह मौलिक अधिकारों का रक्षक और गारंटर है</p>"
                    ],
                    solution_en: "<p>44.(c) Articles 124 to 147 in Part V of the Constitution deal with the organisation, independence, jurisdiction, powers and procedures of the Supreme Court. The SC gives the final verdict against an appeal from the other subsidiary courts i.e., High courts. As per Article 141, Law declared by the Supreme Court to be binding on all courts.</p>",
                    solution_hi: "<p>44.(c) संविधान के भाग V में अनुच्छेद 124 से 147 सर्वोच्च न्यायालय के संगठन, स्वतंत्रता,&nbsp; अधिकार क्षेत्र, शक्तियों एवं प्रक्रियाओं से संबंधित हैं। सर्वोच्च न्यायालय अन्य सहायक न्यायालयों अर्थात उच्च न्यायालयों की अपील के विरुद्ध अंतिम निर्णय देता है। अनुच्छेद 141 के अनुसार, उच्चतम न्यायालय द्वारा घोषित कानून भारत के क्षेत्र के भीतर सभी न्यायालयों पर लागू होगा।।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Under which of the following Acts, the Board of Control was established in England to control and supervise the administration of British India ?</p>",
                    question_hi: "<p>45. निम्नलिखित में से किस अधिनियम के तहत, ब्रिटिश भारत के प्रशासन को नियंत्रित और संचालित करने के लिए इंग्लैंड में नियंत्रण बोर्ड की स्थापना की गई थी ?</p>",
                    options_en: [
                        "<p>Charter Act of 1813</p>",
                        "<p>Regulating Act of 1773</p>",
                        "<p>Charter Act of 1793</p>",
                        "<p>Pitt&rsquo;s India Act of 1784</p>"
                    ],
                    options_hi: [
                        "<p>चार्टर अधिनियम 1813</p>",
                        "<p>विनियमन अधिनियम 1773</p>",
                        "<p>चार्टर अधिनियम 1793</p>",
                        "<p>पिट्स इंडिया अधिनियम 1784</p>"
                    ],
                    solution_en: "<p>45.(d) <strong>Pitt&rsquo;s India Act of 1784.</strong> It was introduced during the tenure of Lord Warren Hastings against the backdrop of growing concerns about the East India Company\'s mismanagement and corruption in India. Provisions: It gave the British government control over the East India Company.</p>",
                    solution_hi: "<p>45.(d) <strong>पिट्स इंडिया अधिनियम 1784.</strong> इसे लॉर्ड वॉरेन हेस्टिंग्स के कार्यकाल के दौरान भारत में ईस्ट इंडिया कंपनी के कुप्रबंधन और भ्रष्टाचार के बारे में बढ़ती चिंताओं की पृष्ठभूमि में पेश किया गया था। प्रावधान: इसने ब्रिटिश सरकार को ईस्ट इंडिया कंपनी पर नियंत्रण दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following gases is generally used to flush bags of chips by the manufacturers ?</p>",
                    question_hi: "<p>46. विनिर्माता सामान्यतः निम्नलिखित में से किस गैस का उपयोग चिप्स के बैग को भरने (flush) के लिए करते हैं ?</p>",
                    options_en: [
                        "<p>Compressed air</p>",
                        "<p>Oxygen</p>",
                        "<p>Carbon dioxide</p>",
                        "<p>Nitrogen</p>"
                    ],
                    options_hi: [
                        "<p>संपीडित वायु</p>",
                        "<p>ऑक्सीजन</p>",
                        "<p>कार्बन डाईऑक्साइड</p>",
                        "<p>नाइट्रोजन</p>"
                    ],
                    solution_en: "<p>46.(d) <strong>Nitrogen </strong>is used to flush bags of chips because it is an inert gas, meaning it doesn\'t react with the contents inside the bag. It helps preserve the freshness of the chips by preventing oxidation. Rancidity occurs when unsaturated fats in food are oxidized by air, producing unpleasant odors or flavors. Exposure to air converts unsaturated fats into hydroperoxides, which break down into volatile aldehydes, esters, alcohols, ketones, and hydrocarbons, resulting in a bad smell.</p>",
                    solution_hi: "<p>46.(d) <strong>नाइट्रोजन </strong>का उपयोग चिप्स के बैग को भरने (flush) के लिए किया जाता है क्योंकि यह एक निष्क्रिय गैस है, जिसका अर्थ है कि यह बैग के अंदर की पदार्थ के साथ अभिक्रिया नहीं करती है। यह ऑक्सीकरण को रोककर चिप्स की ताज़गी को बनाए रखने में मदद करता है। बासीपन तब होता है जब भोजन में असंतृप्त वसा वायु द्वारा ऑक्सीकृत हो जाती है, जिससे अप्रिय गंध या स्वाद उत्पन्न होता है। वायु के संपर्क में आने से असंतृप्त वसा हाइड्रोपेरॉक्साइड में परिवर्तित हो जाती है, जो वाष्पशील एल्डिहाइड, एस्टर, अल्कोहल, कीटोन और हाइड्रोकार्बन में टूट जाती है, जिसके परिणामस्वरूप दुर्गंध आती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Who founded the Prarthana Samaj in Mumbai in 1867 ?</p>",
                    question_hi: "<p>47. 1867 में मुंबई में प्रार्थना-समाज की स्थापना किसने की थी ?</p>",
                    options_en: [
                        "<p>Atmaram Pandurang</p>",
                        "<p>Gopal Krishna Gokhale</p>",
                        "<p>Shri Ram Bajpai</p>",
                        "<p>Ram Mohan Roy</p>"
                    ],
                    options_hi: [
                        "<p>आत्माराम पांडुरंग</p>",
                        "<p>गोपाल कृष्ण गोखल</p>",
                        "<p>श्री राम बाजपेय</p>",
                        "<p>राम मोहन राय</p>"
                    ],
                    solution_en: "<p>47.(a) <strong>Atmaram Pandurang. </strong>Prarthana Samaj was founded with an aim to make people believe in one God and worship only one God. Other Societies and Founders: Brahmo Samaj (1828) - Ram Mohan Roy, Servants of India Society (1905) - Gopal Krishna Gokhale, Seva Samiti Boy Scouts Association (1914) - Shri Ram Bajpai.</p>",
                    solution_hi: "<p>47.(a)<strong> आत्माराम पांडुरंग ।</strong> प्रार्थना समाज की स्थापना लोगों को एक ईश्वर में विश्वास करने और केवल एक ईश्वर की पूजा करने के उद्देश्य से की गई थी। अन्य समाज एवं संस्थापक : ब्रह्म समाज (1828) - राम मोहन रॉय, सर्वेंट्स ऑफ इंडिया सोसाइटी (1905) - गोपाल कृष्ण गोखले, सेवा समिति बॉय स्काउट्स एसोसिएशन (1914) - श्री राम बाजपेयी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. A process like river bifurcation can create valuable landforms for the environment, providing growth of vegetation. An example of such a landform is ______.</p>",
                    question_hi: "<p>48. नदी विभाजन (river bifurcation) जैसी प्रक्रिया पर्यावरण के लिए मूल्यवान स्थलाकृतियाँ (landforms) बना सकती है, जो वनस्पति के विकास में सहायता करती हैं। ऐसी स्थलाकृति का एक उदाहरण _______है।</p>",
                    options_en: [
                        "<p>river deltas</p>",
                        "<p>potholes</p>",
                        "<p>V-shaped valleys</p>",
                        "<p>cascades</p>"
                    ],
                    options_hi: [
                        "<p>नदी डेल्टा</p>",
                        "<p>जलज गर्तिका</p>",
                        "<p>V-नुमा घाटियाँ</p>",
                        "<p>कैस्केड</p>"
                    ],
                    solution_en: "<p>48.(a)<strong> river deltas. </strong>It is a landform shaped like a triangle. It is where the mouth of a river flows into an ocean. Potholes are deep, rounded depressions in the riverbed caused by erosion. V-shaped valleys are formed through the erosive action of rivers, especially in mountainous areas. Cascades refer to small waterfalls or rapids in rivers.</p>",
                    solution_hi: "<p>48.(a) <strong>नदी डेल्टा।</strong> यह त्रिभुज के आकार की एक भू-आकृति है। यह वह स्थान है जहाँ नदी का मुहाना महासागर में मिलता है। जलज गर्तिका नदी के तल में कटाव के कारण बने गहरे, गोल गड्ढे होते हैं। V- आकार नुमा घाटियाँ नदियों की कटाव क्रिया के कारण बनती हैं, विशेष रूप पहाड़ी क्षेत्रों में। झरने नदियों में छोटी जलप्रपातों या तेज बहाव को संदर्भित करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which Fundamental Right guarantees protection of interests of the minorities in the matter of their language, script or culture ?</p>",
                    question_hi: "<p>49. कौन-सा मौलिक अधिकार अल्पसंख्यकों को उनकी भाषा, लिपि या संस्कृति के मामले में हितों की सुरक्षा की गारंटी प्रदान करता है ?</p>",
                    options_en: [
                        "<p>Right to Equality</p>",
                        "<p>Cultural and Educational Rights</p>",
                        "<p>Right to Freedom</p>",
                        "<p>Right against Exploitation</p>"
                    ],
                    options_hi: [
                        "<p>समानता का अधिकार</p>",
                        "<p>सांस्कृतिक और शैक्षिक अधिकार</p>",
                        "<p>स्वतंत्रता का अधिकार</p>",
                        "<p>शोषण के विरुद्ध अधिकार</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>Cultural and Educational Rights. </strong>Article 29(1) states that any section of the citizens residing in the territory of India or any part thereof having a distinct language, script, or culture of its own shall have the right to conserve the same. Article 30(1) states that all minorities, whether based on religion or language, shall have the right to establish and administer educational institutions of their choice. Right to Equality (Article 14 to 18). Right to Freedom (Articles 19 - 22). Right Against Exploitation (Article 23 to 24).</p>",
                    solution_hi: "<p>49.(b) <strong>सांस्कृतिक और शैक्षिक अधिकार। </strong>अनुच्छेद 29 (1) में कहा गया है कि भारत के किसी क्षेत्र या उसके भाग में रहने वाले नागरिकों के किसी भी वर्ग को अपनी अलग भाषा, लिपि या संस्कृति संरक्षित करने का अधिकार है। अनुच्छेद 30(1) में कहा गया है कि सभी अल्पसंख्यकों को, चाहे वे धर्म या भाषा पर आधारित हों, अपनी पसंद के शैक्षणिक संस्थान स्थापित करने और उन्हें संचालित करने का अधिकार होगा। समानता का अधिकार (अनुच्छेद 14 से 18)। स्वतंत्रता का अधिकार (अनुच्छेद 19 - 22)। शोषण के विरुद्ध अधिकार (अनुच्छेद 23 से 24)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. What are the five Fs of indirect transmission ?</p>",
                    question_hi: "<p>50. अप्रत्यक्ष संचरण के पांच F कौन-से हैं ?</p>",
                    options_en: [
                        "<p>Flies, fingers, fomites, food and fluid</p>",
                        "<p>Fruit, fingers, flu, food and fluid</p>",
                        "<p>Flies, fingers, friends, food and fruit</p>",
                        "<p>Flies, fingers, fomites, food and fruit</p>"
                    ],
                    options_hi: [
                        "<p>Flies (मक्खियां), fingers (उंगलियां), fomites (फोमाइट), food (भोजन) और fluid (तरल)</p>",
                        "<p>fruit (फल), fingers (उंगलियां), flu (फ्लू), food (भोजन) और fluid (तरल)</p>",
                        "<p>Flies (मक्खियां), fingers (उंगलियां), friends (मित्र), food (भोजन) और fruit (फल)</p>",
                        "<p>Flies (मक्खियां), fingers (उंगलियां), fomites (फोमाइट), food (भोजन) और fruit (फल)</p>"
                    ],
                    solution_en: "<p>50.(a) <strong>Flies, fingers, fomites, food and fluid. </strong>Indirect transmission of communicable diseases occurs through various means, commonly referred to as the \"5Fs\" : flies, fingers, fomites (objects like towels and handkerchiefs that can carry infections), food, and fluids. Some diseases can spread through contaminated water, food, ice, blood, and even body tissues or organs. Examples of such diseases include typhoid, diarrhea, polio, intestinal parasites, and infective hepatitis.</p>",
                    solution_hi: "<p>50.(a) <strong>Flies (मक्खियां), fingers (उंगलियां), fomites (फोमाइट), food (भोजन) और fluid (तरल)। </strong>संक्रामक रोगों का अप्रत्यक्ष संचरण विभिन्न माध्यमों से होता है, जिन्हें आमतौर पर \"5F\" कहा जाता है: मक्खियाँ, उंगलियाँ, फोमाइट्स (तौलिए और रूमाल जैसी वस्तुएँ जो संक्रमण फैला सकती हैं), भोजन और तरल पदार्थ। कुछ बीमारियाँ दूषित जल, भोजन, बर्फ, रक्त और यहाँ तक कि शरीर के ऊतकों या अंगों से भी फैल सकती हैं। ऐसी बीमारियों के उदाहरणों में टाइफाइड, डायरिया, पोलियो, आंतों के परजीवी और संक्रामक हेपेटाइटिस शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A work can be finished in a day by 20 men, or by 30 women, or by 50 boys. 2 men and 5 boys work on alternate days and 6 women work on all days. If men work on the first day, the work is finished in ______days</p>",
                    question_hi: "<p>51. एक दिन में 20 पुरुषों, या 30 महिलाओं द्वारा, या 50 लड़कों द्वारा काम समाप्त किया जा सकता है। 2 पुरुष और 5 लड़के वैकल्पिक दिनों में काम करते हैं और 6 महिलाएं सभी दिनों में काम करती हैं। यदि पुरुष पहले दिन काम करते हैं, तो काम ______दिनों में समाप्त हो जाता है</p>",
                    options_en: [
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>51.(d) 20 M = 30 W = 50 B<br>Efficiency of Man, Women and Boy<br>&nbsp;M :&nbsp; W : B<br>15 : 10 : 6<br>Total work = 20M = 20 &times; 15 = 300 unit<br>Now, work done in first day <br>= (2M + 6W) = (2 &times; 15 + 6 &times; 10) = 90 unit<br>Work done in second day <br>= (5B + 6W) = (5 &times; 6 + 6 &times; 10) = 90 unit<br>Now, work done in third day <br>= (2M + 6W) = (2 &times; 15 + 6 &times; 10) = 90 unit<br>Now, remaining work = 300 - (90 + 90 + 90) = 30 unit<br>Remaining work be done by (5B + 6W) = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> day<br>Hence, total time taken to complete the whole work = 3 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days.</p>",
                    solution_hi: "<p>51.(d) 20 M = 30 W = 50 B<br>पुरुष, महिला और लड़के की कार्य दक्षता का अनुपात<br>&nbsp;M :&nbsp; W : B<br>15 : 10 : 6<br>कुल कार्य = 20M = 20 &times; 15 = 300 unit<br>अब, पहले दिन का कार्य<br>= (2M + 6W) = (2 &times; 15 + 6 &times; 10) = 90 इकाई <br>दूसरे दिन का कार्य<br>= (5B + 6W) = (5 &times; 6 + 6 &times; 10) = 90 इकाई<br>तीसरे दिन का कार्य<br>= (2M + 6W) = (2 &times; 15 + 6 &times; 10) = 90 इकाई<br>शेष कार्य = 300 - (90 + 90 + 90) = 30 इकाई<br>(5B + 6W) द्वारा शेष कार्य को पूरा करने में लगने वाला समय = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन<br>अतः कार्य पूरा होने में लगा कुल समय = 3 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Simplify <br>22.0625 - [15 + 0.25 &times; (4.78 - 1.5 &times; 3.02)]</p>",
                    question_hi: "<p>52. 22.0625 - [15 + 0.25 &times; (4.78 - 1.5 &times; 3.02)] को हल कीजिए।</p>",
                    options_en: [
                        " 7.3",
                        "<p>7.0</p>",
                        " 7.1 ",
                        " 7.2 <br /> "
                    ],
                    options_hi: [
                        " 7.3",
                        " 7. 0 ",
                        " 7.1 ",
                        " 7.2 "
                    ],
                    solution_en: "<p>52.(b)<br>22.0625 - [15 + 0.25 &times; (4.78 - 1.5 &times; 3.02)]<br>22.0625 - [15 + 0.25 &times;(4.78 - 4.53)]<br>22.0625 - [15 + 0.25 &times; 0.25]<br>22.0625 - [15 + 0.0625]<br>22.0625 - 15.0625 = 7.0</p>",
                    solution_hi: "<p>52.(b)<br>22.0625 - [15 + 0.25 &times; (4.78 - 1.5 &times; 3.02)]<br>22.0625 - [15 + 0.25 &times;(4.78 - 4.53)]<br>22.0625 - [15 + 0.25 &times; 0.25]<br>22.0625 - [15 + 0.0625]<br>22.0625 - 15.0625 = 7.0</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. If the height of a solid right circular cylinder is 27 cm and diameter of its base is 30 cm, the total surface area (in cm&sup2;) of the right circular cylinder is: <br>(Таке &pi;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p>53. यदि एक ठोस लम्बवृत्तीय बेलन की ऊंचाई 27 cm है और इसके आधार का व्यास 30 cm है. तो लम्बवृत्तीय बेलन का कुल पृष्ठीय क्षेत्रफल (cm&sup2; में) कितना है ?<br>(&pi;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;का प्रयोग करें)</p>",
                    options_en: [
                        "<p>3978</p>",
                        "<p>3960</p>",
                        "<p>3976</p>",
                        "<p>3964</p>"
                    ],
                    options_hi: [
                        "<p>3978</p>",
                        "<p>3960</p>",
                        "<p>3976</p>",
                        "<p>3964</p>"
                    ],
                    solution_en: "<p>53.(b)<br>TSA of the cylinder = 2&pi;r(h + r)&nbsp;<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 15(27 + 15)<br>= 2 &times; 22 &times; 15 &times; 6<br>= 3960cm&sup2;</p>",
                    solution_hi: "<p>53.(b)<br>बेलन का कुल पृष्ठीय क्षेत्रफल = 2&pi;r(h + r)&nbsp;<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 15(27 + 15)<br>= 2 &times; 22 &times; 15 &times; 6<br>= 3960cm&sup2;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The sum of the digits of a two-digit number is 9. If the digits are reversed, then the number is increased by 27. Find the number.</p>",
                    question_hi: "<p>54. दो अंकों की एक संख्या के अंकों का योग 9 है। यदि अंकों को उलट दिया जाए है, तो संख्या 27 बढ़ जाती है। संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>63</p>",
                        "<p>36</p>",
                        "<p>54</p>",
                        "<p>45</p>"
                    ],
                    options_hi: [
                        "<p>63</p>",
                        "<p>36</p>",
                        "<p>54</p>",
                        "<p>45</p>"
                    ],
                    solution_en: "<p>54.(b) <br>Let the original number be (10x + y)<br>On reversing its digits, no will be (10y + x)<br>y+x = 9 ----------- (i)<br>According to the question,<br>(10y + x) - (10x + y) = 27<br>9y - 9x = 27<br>y - x = 3 ------------ (ii)<br>On solving eqn(i) and (ii) we have ;<br>y = 6, x = 3<br>So, the number = 10 &times; 3 + 6 = 36</p>",
                    solution_hi: "<p>54.(b) <br>माना मूल संख्या (10x + y) है<br>इसके अंकों को उलटने पर कोई (10y + x) नहीं होगा<br>y+x = 9 ----------- (i)<br>प्रश्न के अनुसार,<br>(10y + x) - (10x + y) = 27<br>9y - 9x = 27<br>y - x = 3 ------------ (ii)<br>समीकरण(i) और (ii) को हल करने पर हमारे पास है;<br>y = 6, x = 3<br>तो, संख्या = 10 &times; 3 + 6 = 36</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "55. The lengths of the three sides of a triangle are 19 cm, 25 cm and y cm. Which of the following options most accurately gives the possible values of ‘y’?",
                    question_hi: "55. एक त्रिभुज की तीन भुजाओं की लंबाई 19 cm, 25 cm और y cm है। निम्नलिखित में से कौन-सा विकल्प अधिक सटीकता से \'y\' के संभावित मान देता है?",
                    options_en: [
                        " 6 ≤ y ≤ 44 ",
                        " 6 < y < 44 ",
                        " 6 < y ≤ 44 ",
                        " 6 ≤ y < 44"
                    ],
                    options_hi: [
                        " 6 ≤ y ≤ 44 ",
                        " 6 < y < 44 ",
                        " 6 < y ≤ 44 ",
                        " 6 ≤ y < 44"
                    ],
                    solution_en: "55.(b)<br />Concept:- if any triangle has three sides x, y and z <br />Then, (x + y) > z > (x - y)<br />According to question,<br />(25-19) < y < (19 + 25) <br />6 < y < 44 ",
                    solution_hi: "55.(b)<br />संकल्पना:- यदि किसी त्रिभुज की तीन भुजाएँ x, y और z हैं <br />फिर, (x + y) > z > (x - y)<br />प्रश्न के अनुसार,<br />(25-19) < y < (19 + 25) <br />6 < y < 44 ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A box filled with gift articles weights 25 kg. If the weight of the box and the gift articles, respectively, are in the ratio 1 : 9, then the weight of the articles (in grams) is:</p>",
                    question_hi: "<p>56. उपहार वस्तुओं से भरे एक बॉक्स का वजन 25 kg है। यदि बॉक्स और उपहार वस्तुओं का वजन क्रमशः 1 : 9 के अनुपात में है, तो वस्तुओं का वजन (ग्राम में) क्या है ?</p>",
                    options_en: [
                        "<p>22500</p>",
                        "<p>23500</p>",
                        "<p>21500</p>",
                        "<p>24500</p>"
                    ],
                    options_hi: [
                        "<p>22500</p>",
                        "<p>23500</p>",
                        "<p>21500</p>",
                        "<p>24500</p>"
                    ],
                    solution_en: "<p>56.(a)<br>According to the question,<br>Let the weight of the box&nbsp;and the gift articles is x and 9x gram respectively.<br>x + 9x = 25 &times; 1000<br>10x&nbsp;= 25000<br>x = 2500<br>So the weight of the articles will be 9 &times; 2500 = 22500 gram</p>",
                    solution_hi: "<p>56.(a)<br>प्रश्न के अनुसार,<br>माना कि बक्से और उपहार सामग्री का वजन क्रमशः x&nbsp;और 9x ग्राम है।<br>x + 9x = 25 &times; 1000<br>10x&nbsp;= 25000<br>x = 2500<br>तो वस्तुओं का वजन (9 &times; 2500) = 22500 ग्राम होगा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A train 550 metres long is running at a speed of 78 km/h. How many seconds will it take to cross a 350 metres long train running in the opposite direction at a speed of 30 km/h ?</p>",
                    question_hi: "<p>57. 550 मीटर लंबी एक ट्रेन 78 km/h की चाल से चल रही है। 30 km/h की चाल से विपरीत दिशा में चल रही 350 मीटर लंबी एक दूसरी ट्रेन को पार करने में इसे कितने सेकंड का समय लगेगा ?</p>",
                    options_en: [
                        "<p>50 seconds</p>",
                        "<p>20 seconds</p>",
                        "<p>40 seconds</p>",
                        "<p>30 seconds</p>"
                    ],
                    options_hi: [
                        "<p>50 सेकंड</p>",
                        "<p>20 सेकंड</p>",
                        "<p>40 सेकंड</p>",
                        "<p>30 सेकंड</p>"
                    ],
                    solution_en: "<p>57.(d)<br>Relative speed = (78 + 30) &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 30 m/sec<br>So, required time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>550</mn><mo>+</mo><mn>350</mn></mrow><mn>30</mn></mfrac></math> = 30 seconds</p>",
                    solution_hi: "<p>57.(d)<br>सापेक्ष गति = (78 + 30) &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 30 मीटर/सेकंड<br>तो, आवश्यक समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>550</mn><mo>+</mo><mn>350</mn></mrow><mn>30</mn></mfrac></math> = 30 सेकंड</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p style=\"text-align: left;\">58. The list price of a washing machine is ₹ 41000. It is sold to a retailer after two successive discounts of 12% and 30%. The retailer wants to earn a profit of 25% on its cost price after allowing a 50% discount (on its new list price) to the customer. At what price should he list the washing machine ?</p>",
                    question_hi: "<p>58. किसी वॉशिंग मशीन का अंकित मूल्य ₹41000 है। इसे 12% और 30% की दो क्रमिक छूट पर एक खुदरा विक्रेता को बेचा जाता है। खुदरा विक्रेता ग्राहक को 50% की छूट (इसके नए अंकित मूल्य पर) देकर अपने क्रय मूल्य पर 25% का लाभ अर्जित करना चाहता है। उसे वॉशिंग मशीन का अंकित मूल्य कितना रखना चाहिए ?</p>",
                    options_en: [
                        "<p>₹ 63076</p>",
                        "<p>₹ 63140</p>",
                        "<p>₹ 62956</p>",
                        "<p>₹ 62967</p>"
                    ],
                    options_hi: [
                        "<p>₹ 63076</p>",
                        "<p>₹ 63140</p>",
                        "<p>₹ 62956</p>",
                        "<p>₹ 62967</p>"
                    ],
                    solution_en: "<p>58.(b) 12% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 30% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math><br>Cost price for retailer = 41000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math> = ₹25,256<br>Required list price = 25,256 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math> = ₹63,140</p>",
                    solution_hi: "<p>58.(b) 12% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 30% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math><br>खुदरा विक्रेता के लिए लागत मूल्य = 41000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math> = ₹25,256<br>आवश्यक अंकित मूल्य = 25,256 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math> = ₹63,140</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The price of fuel decreases by 25%, 60% and 10% in three successive months, but increases by 60% in the fourth month. What is the percentage increase/decrease in the price of fuel in the fourth month as compared to its original price ?</p>",
                    question_hi: "<p>59. ईंधन की कीमत क्रमागत तीन महीनों में 25%, 60% और 10% कम की गई है. लेकिन चौथे महीने में 60% वृद्धि की गई है। चौथे महीने में ईंधन की कीमत में उसकी मूल कीमत की तुलना में कितने प्रतिशत की वृद्धि/कमी हुई है ?</p>",
                    options_en: [
                        "<p>Increases by 61.02%</p>",
                        "<p>Decreases by 59.51%</p>",
                        "<p>Decreases by 56.79%</p>",
                        "<p>Increases by 59.84%</p>"
                    ],
                    options_hi: [
                        "<p>61.02% की वृद्धि</p>",
                        "<p>59.51% की कमी</p>",
                        "<p>56.79% की कमी</p>",
                        "<p>59.84% की वृद्धि</p>"
                    ],
                    solution_en: "<p>59.(c)<br>&nbsp; &nbsp; Ratio&nbsp; &nbsp;&rarr; Initial&nbsp; :&nbsp; Final<br>1<sup>st </sup>month &rarr;&nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 3<br>2<sup>nd</sup> month &rarr;&nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 2<br>3<sup>rd </sup>month &rarr;&nbsp; &nbsp;10&nbsp; &nbsp; :&nbsp; &nbsp; 9<br>4<sup>th</sup> month &rarr;&nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;8<br>--------------------------------------<br>Net Price&nbsp; &rarr; 1000 : 432<br>% decrease = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mo>-</mo><mn>432</mn></mrow><mn>1000</mn></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>568</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 100 = 56.8 % ≃ 56.79 %</p>",
                    solution_hi: "<p>59.(c)<br>&nbsp; &nbsp; &nbsp; &nbsp;अनुपात&nbsp; &rarr; आरंभिक : अंतिम<br>पहला महीना&nbsp; &rarr;&nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;3<br>दूसरा महीना&nbsp; &rarr;&nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;2<br>तीसरा महीना &rarr;&nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;9<br>चौथा महीना&nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;8<br>-----------------------------------------<br>कुल कीमत&nbsp; &nbsp; &rarr;&nbsp; &nbsp; 1000&nbsp; :&nbsp; &nbsp;432<br>% कमी = <math display=\"inline\"><mfrac><mrow><mn>1000</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>432</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>568</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 100 = 56.8 % ≃ 56.79 %</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. In an election between two candidates, 80% of the voters cast their vote out of which 53 votes were declared invalid. The winner got 48% of all the voters in the list and he won by 1013 votes. The number of voters on the list was:</p>",
                    question_hi: "<p>60. दो उम्मीदवारों के बीच एक चुनाव में, 80% मतदाता अपना मत डालते हैं, जिनमें से 53 मत अवैध घोषित कर दिए जाते हैं। विजेता को सूची के सभी मतदाताओं में से 48% मत प्राप्त होता है और वह 1013 मतों से जीत जाता है। सूची में मतदाताओं की संख्या कितनी थी ?</p>",
                    options_en: [
                        "<p>5000</p>",
                        "<p>6500</p>",
                        "<p>5600</p>",
                        "<p>6000</p>"
                    ],
                    options_hi: [
                        "<p>5000</p>",
                        "<p>6500</p>",
                        "<p>5600</p>",
                        "<p>6000</p>"
                    ],
                    solution_en: "<p>60.(d) According to question,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504482022.png\" alt=\"rId46\" width=\"92\" height=\"124\"><br>&rArr; 16 % = (1013 - 53) = 960<br>&rArr; 100 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>16</mn></mfrac></math> &times; 100 = 6000</p>",
                    solution_hi: "<p>60.(d) प्रश्न के अनुसार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504482022.png\" alt=\"rId46\" width=\"92\" height=\"124\"><br>&rArr; 16 % = (1013 - 53) = 960<br>&rArr; 100 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>960</mn><mn>16</mn></mfrac></math> &times; 100 = 6000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The side of a rhombus is 20 cm and the length of one of the diagonals is 32 cm. Find the area (in cm<sup>2</sup>) of the rhombus.</p>",
                    question_hi: "<p>61. एक समचतुर्भुज की भुजा 20 cm है और विकर्णों में से एक की लंबाई 32 cm है। समचतुर्भुज का क्षेत्रफल (cm<sup>2</sup> में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>424</p>",
                        "<p>484</p>",
                        "<p>384</p>",
                        "<p>560</p>"
                    ],
                    options_hi: [
                        "<p>424</p>",
                        "<p>484</p>",
                        "<p>384</p>",
                        "<p>560</p>"
                    ],
                    solution_en: "<p>61.(c) <br>Given :- AD = 20 cm , AC = 32 cm<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504482211.png\" alt=\"rId47\" width=\"124\" height=\"122\"><br>OA = OC = <math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 16<br>From triangle AOD ,<br>Using pythagoras theorem,<br>AD<sup>2</sup> = OD<sup>2</sup> + OA<sup>2</sup><br>20<sup>2</sup> = OD<sup>2</sup> + 16<sup>2</sup><br>OD = <math display=\"inline\"><msqrt><mn>400</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>256</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math> = 12 cm<br>DB = 2 &times; OD = 2 &times; 12 = 24 cm<br>Area of rhombus = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>​ &times; product of diagonals<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; AC &times; DB​<br>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 32 &times; 24 = 384 cm<sup>2</sup></p>",
                    solution_hi: "<p>61.(c) <br>दिया गया है :- AD = 20 सेमी, AC = 32 सेमी<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504482211.png\" alt=\"rId47\" width=\"124\" height=\"122\"><br>OA = OC = <math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 16 सेमी<br>त्रिभुज AOD से,<br>पाइथागोरस प्रमेय का उपयोग करने पर ,<br>AD<sup>2</sup> = OD<sup>2</sup> + OA<sup>2</sup><br>20<sup>2</sup> = OD<sup>2</sup> + 16<sup>2</sup><br>OD = <math display=\"inline\"><msqrt><mn>400</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>256</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math> = 12 सेमी<br>DB = 2 &times; OD = 2 &times; 12 = 24 सेमी<br>समचतुर्भुज का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>​ &times; विकर्णों का गुणनफल<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; AC &times; DB​<br>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 32 &times; 24 = 384&nbsp;सेमी<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Three rectangular fields having areas of 70 m&sup2;, 84 m&sup2;, and 112 m&sup2; are to be divided into identical rectangular flower beds, each having a length of 7 m. Find the breadth of each flower bed.</p>",
                    question_hi: "<p>62. 70 m&sup2;, 84 m&sup2; और 112 m&sup2; क्षेत्रफल वाले तीन आयताकार मैदानों को समान आयताकार फूलों की क्यारियों में बांटा जाना है, जिनमें से प्रत्येक की लंबाई 7m है। प्रत्येक फूलों की क्यारी की चौड़ाई ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>4 m</p>",
                        "<p>3 m</p>",
                        "<p>2 m</p>",
                        "<p>6 m</p>"
                    ],
                    options_hi: [
                        "<p>4 m</p>",
                        "<p>3 m</p>",
                        "<p>2 m</p>",
                        "<p>6 m</p>"
                    ],
                    solution_en: "<p>62.(c)<br>Maximum area of identical rectangular flower beds = HCF of (70, 84, 112) = 14<br>So, the breadth of each flower bed = <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 2 m</p>",
                    solution_hi: "<p>62.(c)<br>समान आयताकार फूलों की क्यारियों का अधिकतम क्षेत्रफल = (70, 84, 112) का HCF = 14<br>अतः, प्रत्येक फूलों की क्यारी की चौड़ाई = <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = 2 मीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. For what positive values of k do the following pair of linear equations have infinitely many solutions ?<br>kx&nbsp;+ 3y &ndash; (k &ndash; 3) = 0<br>12x&nbsp;+ ky &ndash; k = 0</p>",
                    question_hi: "<p>63. K के किस धनात्मक मान के लिए रैखिक समीकरणों के निम्नलिखित युग्म के अनंत रूप से अनेक हल हैं ?<br>kx&nbsp;+ 3y &ndash; (k &ndash; 3) = 0<br>12x&nbsp;+ ky &ndash; k = 0</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>63.(d) <br>When equation has infinite number of solutions then,<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br>Hence, <br><math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mi>k</mi></mfrac></mstyle><mo>&#160;</mo><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn></mrow><mi>k</mi></mfrac></mstyle></math><br>For <math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mi>k</mi></mfrac></mstyle><mo>&#160;</mo></math><br><math display=\"inline\"><msup><mrow><mi>k</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 36<br>k = 6<br>For <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mi>k</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn></mrow><mi>k</mi></mfrac></mstyle></math><br>k = 6<br>Therefore, k satisfied the both equation,<br>Hence, the value of k is 6</p>",
                    solution_hi: "<p>63.(d)&nbsp;<br>जब समीकरण के अनंत संख्या में हल हों तो,<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br>इसलिए,<br><math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mi>k</mi></mfrac></mstyle><mo>&#160;</mo><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn></mrow><mi>k</mi></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>12</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mi>k</mi></mfrac></mstyle><mo>&#160;</mo></math>के लिए<br><math display=\"inline\"><msup><mrow><mi>k</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 36<br>k = 6<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mi>k</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>k</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn></mrow><mi>k</mi></mfrac></mstyle></math>के लिए<br>k = 6<br>इसलिए, k ने दोनों समीकरण को संतुष्ट किया,<br>अतः, k का मान 6 है</p>\n<p>&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If A = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>36</mn></mfrac></math>, B = 9 &divide; [(16 &divide; 25) &divide; 36], C = [9 &divide; (16 &divide; 25)] &divide; 36,D =(9 &divide; 16) &divide; (25 &divide; 36) then which of the following is true ?</p>",
                    question_hi: "<p>64. यदि A = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>36</mn></mfrac></math>, B = 9 &divide; [(16 &divide; 25) &divide; 36], C = [9 &divide; (16 &divide; 25)] &divide; 36,D =(9 &divide; 16) &divide; (25 &divide; 36)तो निम्नलिखित में से कौन-सा कथन सत्य है?</p>",
                    options_en: [
                        "<p>B and D are equal.</p>",
                        "<p>A and B are equal.</p>",
                        "<p>C and D are equal.</p>",
                        "<p>A and D are equal.</p>"
                    ],
                    options_hi: [
                        "<p>B और D बराबर हैं।</p>",
                        "<p>A और B बराबर हैं।</p>",
                        "<p>C और D बराबर हैं।</p>",
                        "<p>A और D बराबर हैं।</p>"
                    ],
                    solution_en: "<p>64.(d) <br>A = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>36</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>25</mn></mfrac><mo>&#160;</mo></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>100</mn></mfrac></math><br>B = 9 &divide; [(16 &divide; 25) &divide; 36]<br>= 9 &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>36</mn></mfrac></math>]<br>= 9 &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>225</mn></mfrac></math>]<br>= 9 &times; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><mn>4</mn></mfrac></math>] = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2025</mn><mn>4</mn></mfrac></math><br>C = [9 &divide; (16 &divide; 25)] &divide; 36<br>= [9 &divide; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math>] &divide; 36<br>= [9 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>16</mn></mfrac></math>] &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>36</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>64</mn></mfrac></math><br>D = (9 &divide; 16) &divide; (25 &divide; 36)<br>= (<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>36</mn></mfrac></math>)<br>= (<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>25</mn></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>100</mn></mfrac></math><br>Clearly, value of A and D are equal.</p>",
                    solution_hi: "<p>64.(d) <br>A = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>36</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>25</mn></mfrac><mo>&#160;</mo></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>100</mn></mfrac></math><br>B = 9 &divide; [(16 &divide; 25) &divide; 36]<br>= 9 &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>36</mn></mfrac></math>]<br>= 9 &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>225</mn></mfrac></math>]<br>= 9 &times; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><mn>4</mn></mfrac></math>] = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2025</mn><mn>4</mn></mfrac></math><br>C = [9 &divide; (16 &divide; 25)] &divide; 36<br>= [9 &divide; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>25</mn></mfrac></math>] &divide; 36<br>= [9 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>16</mn></mfrac></math>] &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>36</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>64</mn></mfrac></math><br>D = (9 &divide; 16) &divide; (25 &divide; 36)<br>= (<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>) &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>36</mn></mfrac></math>)<br>= (<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>36</mn><mn>25</mn></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>81</mn><mn>100</mn></mfrac></math><br>स्पष्टतः, A और D का मान बराबर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Ram invested a certain sum of money at simple interest. It amounted to <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> of itself in 3 years. What will be its rate per cent per annum ?</p>",
                    question_hi: "<p>65. राम ने साधारण ब्याज पर एक निश्चित धनराशि निवेश की। 3 वर्षों में वह अपने आप का <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> गुना हो जाती है। इसकी वार्षिक प्रतिशत दर क्या होगी ?</p>",
                    options_en: [
                        "<p>8.20%</p>",
                        "<p>9.12%</p>",
                        "<p>7.25%</p>",
                        "<p>8.33%</p>"
                    ],
                    options_hi: [
                        "<p>8.20%</p>",
                        "<p>9.12%</p>",
                        "<p>7.25%</p>",
                        "<p>8.33%</p>"
                    ],
                    solution_en: "<p>65.(d)<br>Let simple interest = 100 unit<br>Amount after 3 year = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 125 unit<br>Interest in 3 year = 25 unit<br>So, rate % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#215;</mo><mn>25</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 8.33%</p>",
                    solution_hi: "<p>65.(d)<br>माना साधारण ब्याज = 100 इकाई<br>3 वर्ष के बाद राशि = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 125 इकाई<br>3 वर्ष में ब्याज = 25 इकाई<br>तो, दर % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#215;</mo><mn>25</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = 8.33%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Abhishek\'s marks in Mathematics were incorrectly entered as 93 instead of 63. Due to this, the average marks of the class in Mathematics got increased by 0.5. How many students were there in the class ?</p>",
                    question_hi: "<p>66. गणित में अभिषेक के अंक गलती से 63 के बजाय 93 दर्ज कर दिए जाते है। जिसके कारण, गणित में कक्षा के औसत अंकों में 0.5 की वृद्धि हो जाती है। कक्षा में कितने छात्र थे ?</p>",
                    options_en: [
                        "<p>55</p>",
                        "<p>60</p>",
                        "<p>50</p>",
                        "<p>45</p>"
                    ],
                    options_hi: [
                        "<p>55</p>",
                        "<p>60</p>",
                        "<p>50</p>",
                        "<p>45</p>"
                    ],
                    solution_en: "<p>66.(b)<br>Let take total number of students be x<br>According to question,<br>Total increase marks = x&nbsp;&times; 0.5 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math><br>So, <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 93 - 63<br>&rArr; x = 30 &times; 2 = 60</p>",
                    solution_hi: "<p>66.(b)<br>मान लीजिए छात्रों की कुल संख्या x&nbsp;है<br>प्रश्न के अनुसार,<br>अंको मे कुल वृद्धि = x&nbsp;&times; 0.5 = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>इसलिए, <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 93 - 63<br>&rArr; x = 30 &times; 2 = 60</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The pie chart given below shows the expenditure (in percentage) of Aditya. The monthly income of Aditya is <strong>₹54,000.</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504484028.png\" alt=\"rId48\" width=\"312\" height=\"279\"> <br>How much does he spend (in₹) on Food ?</p>",
                    question_hi: "<p>67. नीचे दिया गया पाई चार्ट आदित्य द्वारा किए जाने वाले खर्च (प्रतिशत में) को दर्शाता है। आदित्य की मासिक आय ₹54,000 है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504484234.png\" alt=\"rId49\" width=\"302\" height=\"271\"> <br>वह भोजन पर कितना (₹ में) खर्च करता है ?</p>",
                    options_en: [
                        "<p>12,240</p>",
                        "<p>13,420</p>",
                        "<p>11,240</p>",
                        "<p>12,420</p>"
                    ],
                    options_hi: [
                        "<p>12,240</p>",
                        "<p>13,420</p>",
                        "<p>11,240</p>",
                        "<p>12,420</p>"
                    ],
                    solution_en: "<p>67.(d)<br>Aaditya spend on food = 54000 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>100</mn></mfrac></math> = Rs.12420</p>",
                    solution_hi: "<p>67.(d)<br>आदित्य द्वारा खाने पर खर्च = 54000 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>100</mn></mfrac></math> = Rs.12420</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. P and Q walk along a circular track in opposite directions. P completes two rounds of the track in 1 minute and Q completes three rounds of the track in the same time. They start from the same point and walk for 2 minutes. How many times will they meet on the track between start time and end time ?</p>",
                    question_hi: "<p>68. P और Q एक वृत्ताकार पथ पर विपरीत दिशाओं में चलते हैं। P, 1 मिनट में पथ के दो चक्कर पूरे करता है और Q समान समय में पथ के तीन चक्कर पूरे करता है। वे एक ही बिंदु से शुरू करते हैं और 2 मिनट तक चलते हैं। प्रारंभ समय और समाप्ति समय के बीच वे कितनी बार पथ पर मिलेंगे ?</p>",
                    options_en: [
                        "<p>10</p>",
                        "<p>8</p>",
                        "<p>9</p>",
                        "<p>11</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>8</p>",
                        "<p>9</p>",
                        "<p>11</p>"
                    ],
                    solution_en: "<p>68.(c) Given,<br>P completes 2 rounds in 1 minute, so P\'s speed is 2 rounds per minute.<br>Q completes 3 rounds in 1 minute, so Q\'s speed is 3 rounds per minute.<br>Relative speed = 2 + 3 = 5 rounds per minute .............. (opposite direction)<br>The number of times they meet in 2 minutes is 5 &times; 2 = 10.<br>However, since they start together at the beginning, we subtract one meeting (the initial meeting at the start). Therefore, the total number of meetings is: 10 &minus; 1 = 9.</p>",
                    solution_hi: "<p>68.(c) दिया गया है,<br>P, 1 मिनट में 2 चक्कर पूरा करता है, इसलिए P की गति = 2 चक्कर प्रति मिनट <br>Q, 1 मिनट में 3 चक्कर पूरा करता है, इसलिए Q की गति = 3 चक्कर प्रति मिनट <br>सापेक्ष गति = 2 + 3 = 5 चक्कर प्रति मिनट .............. (विपरीत दिशा)<br>2 मिनट में मिलने की संख्या = 5 &times; 2 = 10 <br>हालांकि, क्योंकि वे शुरुआत में एक साथ शुरू करते हैं, इसलिए हमें एक बार मिलने को (शुरुआत का मिलना) घटाना होगा।<br>इसलिए, कुल मिलाकर मिलने की संख्या है: 10 &minus; 1 = 9.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The following graph shows the demand and production of buses of five companies in 2022 (in thousands). Study the given graph carefully and answer the question that follows</p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdlboiOiDHLZr0GSt3whmTnWhS2UGEHzcf4zh8h8XuIjM4hT2rMEWOaxy1yr_ulw-Nd6Dh8GV2OBqHi7lvMie5aituyS4rBmXJdJb_2W6_PGwDdVMVta34YMSA5JYRMulVWwcLfbA?key=b4VIFV3Zv31mAs2jG64b4cHM\" width=\"381\" height=\"263\"></p>\n<p dir=\"ltr\">The number of companies whose production of buses is equal to or more than the average demand of buses of all five companies is ?</p>\n<p>&nbsp;</p>",
                    question_hi: "<p>69. दिया गया ग्राफ, 2022 में पांच कंपनियों की बसों की मांग और उत्पादन (हजारों में) को दर्शाता है। दिए गए ग्राफ का ध्यानपूर्वक अध्ययन कीजिए और दिए गए प्रश्न का उत्तर दीजिए।</p>\n<p dir=\"ltr\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcc-gJ28fKmvlTFulx0hlMvoksKB8YgHgNQuNJ7c8-ySEPgtz-uFx89vNk_IpO79__o0Ou5pz9En0WmtaVlKVGY3wLSIzH2dBWHhkmkvGUQw1mH8mbR_-Dcl2_3NNijXOuv3QcGRQ?key=b4VIFV3Zv31mAs2jG64b4cHM\" width=\"369\" height=\"255\"></p>\n<p dir=\"ltr\">उन कंपनियों की संख्या ज्ञात कीजिए, जिनकी बसों का उत्पादन सभी पांच कंपनियों की बसों की औसत मांग के बराबर या उससे अधिक है?</p>\n<p>&nbsp;</p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>4</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>69.(d)</p>\n<p dir=\"ltr\">Average demand of buses of all five companies =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><mn>150</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>145</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>160</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>165</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>155</mn></mrow><mn>5</mn></mfrac></mstyle></math> = 155</p>\n<p dir=\"ltr\">A, C and D(3 Companies) have production equal to or more than average demand.</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>69.(d)</p>\n<p dir=\"ltr\">पांचों कंपनियों की बसों की औसत मांग = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"18px\"><mfrac><mrow><mn>150</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>145</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>160</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>165</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>155</mn></mrow><mn>5</mn></mfrac></mstyle></math> = 155</p>\n<p dir=\"ltr\">A, C और D(3 कंपनियां) का उत्पादन औसत मांग के बराबर या उससे अधिक है&nbsp;</p>\n<p>&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Evaluate the value of (cosec 56&deg; cos 34&deg;- cos 59&deg;cosec 31&deg;).</p>",
                    question_hi: "<p>70. (cosec 56&deg; cos 34&deg;- cos 59&deg;cosec 31&deg;) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>-1</p>",
                        "<p>0</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>-1</p>",
                        "<p>0</p>"
                    ],
                    solution_en: "<p>70.(d)<br><strong>Concept used :</strong> <br>cosec(90&deg; - &theta;) = sec&theta;<br>cos(90&deg; - &theta;) = sin&theta;<br>Now, cosec 56&deg; cos 34&deg;- cos 59&deg;cosec 31&deg;<br>cosec(90&deg;- 34&deg;).cos34&deg; - cos 59&deg;cosec(90&deg;- 59&deg;)<br>sec34&deg;.cos34&deg; - cos 59&deg;.sec59&deg;<br>1 - 1 = 0</p>",
                    solution_hi: "<p>70.(d)<br><strong>प्रयुक्त अवधारणा :</strong> <br>cosec(90&deg; - &theta;) = sec&theta;<br>cos(90&deg; - &theta;) = sin&theta;<br>अब , cosec 56&deg; cos 34&deg;- cos 59&deg;cosec 31&deg;<br>cosec(90&deg;- 34&deg;).cos34&deg; - cos 59&deg;cosec(90&deg;-59&deg;)<br>sec34&deg;.cos34&deg; - cos 59&deg;.sec59&deg;<br>1 - 1 = 0</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. The average of v numbers is w<sup>2</sup> and that of w numbers is v<sup>2</sup> Then the average of all the numbers is:</p>",
                    question_hi: "<p>71. v संख्याओं का औसत w<sup>2</sup> है और w संख्याओं का औसत v<sup>2</sup> है। तो सभी संख्याओं का औसत _______ है।</p>",
                    options_en: [
                        "<p>vw<sup>2</sup></p>",
                        "<p>v + w</p>",
                        "<p>vw</p>",
                        "<p>v<sup>2</sup> + w<sup>2</sup></p>"
                    ],
                    options_hi: [
                        "<p>vw<sup>2</sup></p>",
                        "<p>v + w</p>",
                        "<p>vw</p>",
                        "<p>v<sup>2</sup> + w<sup>2</sup></p>"
                    ],
                    solution_en: "<p>71.(c)<br>The average of v numbers = w<sup>2</sup><br>&rArr; sum of v numbers = v.w<sup>2</sup><br>And <br>Average of w numbers = v<sup>2</sup><br>&rArr; sum of w numbers = w.v<sup>2</sup><br>Now, <br>Average of all numbers = <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>u</mi><mi>m</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>a</mi><mi>l</mi><mi>l</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi></mrow></mfrac></math><br>&rArr; A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>v</mi><mo>.</mo><msup><mi>w</mi><mn>2</mn></msup><mo>+</mo><mi>w</mi><mo>.</mo><msup><mi>v</mi><mn>2</mn></msup></mrow><mrow><mi>v</mi><mo>+</mo><mi>w</mi></mrow></mfrac></math><br>&rArr; A = v.w</p>",
                    solution_hi: "<p>71.(c)<br>v संख्याओं का औसत = w<sup>2</sup><br>&rArr; v संख्याओं का योग = v.w<sup>2</sup><br>और<br>w संख्याओं का औसत = v<sup>2</sup><br>&rArr; w संख्याओं का योग = w.v<sup>2</sup><br>अब, <br>सभी संख्याओं का औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2360;&#2349;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2351;&#2379;&#2327;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</mi></mrow></mfrac></math><br>&rArr; A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>v</mi><mo>.</mo><msup><mi>w</mi><mn>2</mn></msup><mo>+</mo><mi>w</mi><mo>.</mo><msup><mi>v</mi><mn>2</mn></msup></mrow><mrow><mi>v</mi><mo>+</mo><mi>w</mi></mrow></mfrac></math><br>&rArr; A = v.w</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. What is the value of sec (t), if tan(t) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> ?</p>",
                    question_hi: "<p>72. यदि tan (t) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> है, तो sec (t) का मान कितना है ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>10</mn></msqrt></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>10</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>10</mn></msqrt></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>10</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>72.(c)<br>tan(t) = <math display=\"inline\"><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow><mrow><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>Hypotenuse = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>3</mn><msup><mo>)</mo><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>1</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math><br>Now, sec(t) = <math display=\"inline\"><mfrac><mrow><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><msqrt><mn>10</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>72.(c)<br>tan(t) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2354;&#2350;&#2381;&#2348;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>विकर्ण = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>3</mn><msup><mo>)</mo><mn>2</mn></msup><mo>+</mo><mo>(</mo><mn>1</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>10</mn></msqrt></math><br>अब, sec(t) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2357;&#2367;&#2325;&#2352;&#2381;&#2339;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math> = <math display=\"inline\"><mfrac><mrow><msqrt><mn>10</mn></msqrt></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Pipe X can fill a tank in 9 hours and Pipe Y can fill it in 21 hours. If they are opened on alternate hours and Pipe X is opened first, in how many hours shall the tank be full ?</p>",
                    question_hi: "<p>73. पाइप X एक टंकी को 9 घंटे में भर सकता है और पाइप Y इसे 21 घंटे में भर सकता है। यदि उन्हें एकांतर घंटों में (बारी-बारी से एक-एक घंटे पर) खोला जाता है और पाइप X को पहले खोला जाता है, तो टंकी कितने घंटों में पूरी भर जाएगी ?</p>",
                    options_en: [
                        "<p>10<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>10<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>9<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>73.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504484994.png\" alt=\"rId52\" width=\"213\" height=\"153\"><br>If Pipes are opened on alternate hr , than <br>In 2hr , tank fill in = (7 + 3) = 10 unit <br>&rArr; 12 hr = 60 unit <br>Remaining 3 unit fill by pipe X in <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hr<br><math display=\"inline\"><mo>&#8756;</mo></math> Total time taken to full the tank = 12 hr + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>hr = 12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> hr</p>",
                    solution_hi: "<p>73.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504485158.png\" alt=\"rId53\" width=\"183\" height=\"161\"><br>यदि पाइप वैकल्पिक घंटे पर खोले जाते हैं, तो <br>2 घंटे में, टैंक भरेगा = (7 + 3) = 10 इकाई <br>&rArr; 12 घंटे = 60 इकाई <br>शेष 3 इकाई , पाइप X द्वारा <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे में भरा जाएगा <br><math display=\"inline\"><mo>&#8756;</mo></math> टैंक को पूरा भरने में लगा कुल समय = 12 घंटे + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>घंटे = 12<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. In a circle, the chords AB and CD intersect each other at point L (internally). If AL = 8 cm, LB = 6 cm and LD = 5 cm, then find the CL.</p>",
                    question_hi: "<p>74. एक वृत्त में जीवा AB और CD एक दूसरे को बिंदु L पर (आंतरिक रूप से) काटती हैं। यदि AL = 8 cm, LB = 6 cm और LD = 5 cm है, तो CL का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>10.6 cm</p>",
                        "<p>9.6 cm</p>",
                        "<p>7.6 cm</p>",
                        "<p>8.6 cm</p>"
                    ],
                    options_hi: [
                        "<p>10.6 cm</p>",
                        "<p>9.6 cm</p>",
                        "<p>7.6 cm</p>",
                        "<p>8.6 cm</p>"
                    ],
                    solution_en: "<p>74.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504485340.png\" alt=\"rId54\" width=\"229\" height=\"188\"><br>We know that,<br>CL &times; LD = BL &times; LA<br>CL &times; 5 = 6 &times; 8<br>CL = 9.6 cm</p>",
                    solution_hi: "<p>74.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743504485340.png\" alt=\"rId54\" width=\"229\" height=\"188\"><br>हम जानते हैं कि ,<br>CL &times; LD = BL &times; LA<br>CL &times; 5 = 6 &times; 8<br>CL = 9.6 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Determine the value of the product <br>(4a + 3b) (16a<sup>2</sup> &minus; 12ab + 9b<sup>2</sup>) for a = 2 and b = 3.</p>",
                    question_hi: "<p>75. a = 2 और b = 3 के लिए गुणनफल (4a + 3b) (16a<sup>2</sup> &minus; 12ab + 9b<sup>2</sup>) का मान निर्धारित कीजिए।</p>",
                    options_en: [
                        "<p>1131</p>",
                        "<p>1141</p>",
                        "<p>1231</p>",
                        "<p>1241</p>"
                    ],
                    options_hi: [
                        "<p>1131</p>",
                        "<p>1141</p>",
                        "<p>1231</p>",
                        "<p>1241</p>"
                    ],
                    solution_en: "<p>75.(d)<br>(4a + 3b) (16a<sup>2</sup> &minus; 12ab + 9b<sup>2</sup>) <br>= (4a + 3b) [(4a)<sup>2</sup>&nbsp;&minus; 4a &times; 3b + (3b)<sup>2</sup>]<br>= (4a)<sup>3</sup>&nbsp;+ (3b)<sup>3</sup><br><strong>On putting a&nbsp;= 2 and b = 3</strong><br>(4 &times; 2)<sup>3</sup>&nbsp;+ (3 &times; 3)<sup>3</sup> = 512 + 729 = 1241</p>",
                    solution_hi: "<p>75.(d)<br>(4a + 3b) (16a<sup>2</sup> &minus; 12ab + 9b<sup>2</sup>) <br>= (4a + 3b) [(4a)<sup>2</sup>&nbsp;&minus; 4a &times; 3b + (3b)<sup>2</sup>]<br>= (4a)<sup>3</sup>&nbsp;+ (3b)<sup>3</sup><br><strong>a = 2 और b = 3 रखने पर</strong><br>(4 &times; 2)<sup>3</sup>&nbsp;+ (3 &times; 3)<sup>3</sup> = 512 + 729 = 1241</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been divided into four segments. Identify the segment that contains an error in the usage of the interjection.<br>He replied / in a dry tone, / &ldquo;Ouch! I will not work / for you anymore.&rdquo;</p>",
                    question_hi: "<p>76. The following sentence has been divided into four segments. Identify the segment that contains an error in the usage of the interjection.<br>He replied / in a dry tone, / &ldquo;Ouch! I will not work / for you anymore.&rdquo;</p>",
                    options_en: [
                        "<p>in a dry tone,</p>",
                        "<p>He replied</p>",
                        "<p>for you anymore.&rdquo;</p>",
                        "<p>\'\'Ouch! I will not work</p>"
                    ],
                    options_hi: [
                        "<p>in a dry tone,</p>",
                        "<p>He replied</p>",
                        "<p>for you anymore.&rdquo;</p>",
                        "<p>\'\'Ouch! I will not work</p>"
                    ],
                    solution_en: "<p>76.(d) \'\'Ouch! I will not work<br>&lsquo;Ouch!&rsquo; is used to express pain. However, in the given sentence, the speaker replied in a dry tone. So, the correct interjection to use is &lsquo;Ugh!&rsquo;. Hence, &lsquo;Ugh! I will not work for you anymore&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(d) \'\'Ouch! I will not work<br>&lsquo;Ouch!&rsquo; का प्रयोग दर्द (pain) को व्यक्त करने के लिए किया जाता है। हालाँकि, दिए गए sentence में, speaker ने रूखे स्वर (dry tone) में उत्तर दिया। इसलिए, उपयोग करने के लिए सही interjection &lsquo;Ugh!&rsquo; है। अतः, &lsquo;Ugh! I will not work for you anymore&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Identify the segment in the sentence, which contains the error.<br>People who live on glass houses should not throw stones.</p>",
                    question_hi: "<p>77. Identify the segment in the sentence, which contains the error.<br>People who live on glass houses should not throw stones.</p>",
                    options_en: [
                        "<p>People who live on</p>",
                        "<p>glasshouses</p>",
                        "<p>should not throw stones.</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>People who live on</p>",
                        "<p>glasshouses</p>",
                        "<p>should not throw stones.</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>77.(a) People who live on<br>Here, it is a preposition related error. Hence, &ldquo;people who live in&rdquo; should be used.</p>",
                    solution_hi: "<p>77.(a) People who live on<br>यह preposition से संबंधित त्रुटि है। इसलिए, \"people who live in\" का उपयोग किया जाना चाहिए।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Find the correctly spelled word:</p>",
                    question_hi: "<p>78. Find the correctly spelled word:</p>",
                    options_en: [
                        "<p>entrepreneurship</p>",
                        "<p>leadeship</p>",
                        "<p>scholership</p>",
                        "<p>partnarship</p>"
                    ],
                    options_hi: [
                        "<p>entrepreneurship</p>",
                        "<p>leadeship</p>",
                        "<p>scholership</p>",
                        "<p>partnarship</p>"
                    ],
                    solution_en: "<p>78.(a) entrepreneurship</p>",
                    solution_hi: "<p>78.(a) entrepreneurship</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>I can&rsquo;t <span style=\"text-decoration: underline;\"><strong>make out</strong></span> my mind about taking a vacation in winter.</p>",
                    question_hi: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>I can&rsquo;t <span style=\"text-decoration: underline;\"><strong>make out</strong></span> my mind about taking a vacation in winter.</p>",
                    options_en: [
                        "<p>make up</p>",
                        "<p>made out</p>",
                        "<p>No substitution required</p>",
                        "<p>Make on</p>"
                    ],
                    options_hi: [
                        "<p>make up</p>",
                        "<p>made out</p>",
                        "<p>No substitution required</p>",
                        "<p>Make on</p>"
                    ],
                    solution_en: "<p>79.(a) Make up<br>&lsquo;Make up one&rsquo;s mind&rsquo; is a phrase that means to make a decision. Similarly in the given sentence, the narrator is unable to decide about taking a vacation. Hence, &lsquo;make up&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>79.(a) make up<br>\'Make up one&rsquo;s mind\' एक phrase है जिसका अर्थ है to make a decision/एक निर्णय करना, इसी तरह दिए गए वाक्य में, the narrator is unable to decide about taking a vacation. इसलिए, \'make up\' सबसे उपयुक्त है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that expresses the given sentence in active voice. <br>Lovely tunes are composed by Domnica.</p>",
                    question_hi: "<p>80. Select the option that expresses the given sentence in active voice. <br>Lovely tunes are composed by Domnica.</p>",
                    options_en: [
                        "<p>Domnica composed lovely tunes</p>",
                        "<p>Domnica composes tunes lovely.</p>",
                        "<p>Domnica will compose lovely tunes.</p>",
                        "<p>Domnica composes lovely tunes.</p>"
                    ],
                    options_hi: [
                        "<p>Domnica composed lovely tunes</p>",
                        "<p>Domnica composes tunes lovely.</p>",
                        "<p>Domnica will compose lovely tunes.</p>",
                        "<p>Domnica composes lovely tunes.</p>"
                    ],
                    solution_en: "<p>80.(d) Domnica composes lovely tunes. (Correct)<br>(a) Domnica <span style=\"text-decoration: underline;\">composed</span> lovely tunes. (Incorrect Tense)<br>(b) Domnica composes tunes lovely. (Incorrect Sentence Structure)<br>(c) Domnica <span style=\"text-decoration: underline;\">will compose</span> lovely tunes. (Incorrect Tense)</p>",
                    solution_hi: "<p>80.(d) Domnica composes lovely tunes. (Correct)<br>(a) Domnica <span style=\"text-decoration: underline;\">composed</span> lovely tunes. (गलत Tense)<br>(b) Domnica composes tunes lovely. (गलत Sentence Structure)<br>(c) Domnica <span style=\"text-decoration: underline;\">will compose</span> lovely tunes. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>They are going to build a new airport near the old one.</p>",
                    question_hi: "<p>81. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>They are going to build a new airport near the old one.</p>",
                    options_en: [
                        "<p>A new airport going to be built near- the old one.</p>",
                        "<p>A new airport is being built near the old one.</p>",
                        "<p>A new airport will be built near the old one.</p>",
                        "<p>A new airport is going to be built near the old one.</p>"
                    ],
                    options_hi: [
                        "<p>A new airport going to be built near- the old one.</p>",
                        "<p>A new airport is being built near the old one.</p>",
                        "<p>A new airport will be built near the old one.</p>",
                        "<p>A new airport is going to be built near the old one.</p>"
                    ],
                    solution_en: "<p>81.(d) A new airport is going to be built near the old one.<br>a. A new <strong>airport going </strong>to be built near- the old one.(Helping verb is missing)<br>b. A new airport is being built near the old one.(The verb &lsquo;go&rsquo; is missing)<br>c. A new airport <strong>will be built</strong> near the old one.(Tense has changed)</p>",
                    solution_hi: "<p>81.(d) A new airport is going to be built near the old one. <br>a. A new <strong>airport going </strong>to be built near- the old one.(Helping verb गायब है)<br>b. A new airport is being built near the old one.(Verb &lsquo;go&rsquo; गायब है)<br>c. A new airport <strong>will be built </strong>near the old one.(Tense बदल गया है)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Find a word that is the synonym of<br>Benign</p>",
                    question_hi: "<p>82. Find a word that is the synonym of<br>Benign</p>",
                    options_en: [
                        "<p>Beneficiary</p>",
                        "<p>Boisterous</p>",
                        "<p>Benevolent</p>",
                        "<p>Boring</p>"
                    ],
                    options_hi: [
                        "<p>Beneficiary</p>",
                        "<p>Boisterous</p>",
                        "<p>Benevolent</p>",
                        "<p>Boring</p>"
                    ],
                    solution_en: "<p>82.(c) Benevolent<br>Benign is kind. So option c benevolent (kind and well intended) will be the most appropriate answer.<br>Beneficiary is a person who gets advantage from something like a trust will etc.<br>Boisterous is loud and full of energy.</p>",
                    solution_hi: "<p>82.(c) Benevolent<br>Benign का अर्थ दयालु है। तो विकल्प c benevolent (दयालु और नेक इरादा) सबसे उपयुक्त उत्तर होगा।<br>Beneficiary - वह व्यक्ति होता है जिसे किसी से लाभ मिलता है।<br>Boisterous - वह व्यक्ति जो जोश और ऊर्जा से भरा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the word which means the same as the group of words given. <br>Fear of closed spaces</p>",
                    question_hi: "<p>83. Select the word which means the same as the group of words given. <br>Fear of closed spaces</p>",
                    options_en: [
                        "<p>Claustrophobia</p>",
                        "<p>Aquaphobia</p>",
                        "<p>Botanophobia</p>",
                        "<p>Kleptomania</p>"
                    ],
                    options_hi: [
                        "<p>Claustrophobia</p>",
                        "<p>Aquaphobia</p>",
                        "<p>Botanophobia</p>",
                        "<p>Kleptomania</p>"
                    ],
                    solution_en: "<p>83.(a) Claustrophobia<br><strong>Claustrophobia </strong>- Extreme or irrational fear of confined places<br><strong>Aquaphobia </strong>- Fear of water<br><strong>Botanophobia</strong> - Botanophobia is the fear of plants.<br><strong>Kleptomania</strong> - A recurrent urge to steal, typically without regard for need or profit.</p>",
                    solution_hi: "<p>83.(a) Claustrophobia<br><strong>Claustrophobia</strong> - एक छोटी जगह में बंद होने का डर<br><strong>Aquaphobia</strong> - पानी का डर<br><strong>Botanophobia</strong> - पौधों का डर <br><strong>Kleptomania</strong> - चोरी करने की बीमारी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) They were not really answerable to the common people.<br>(B) In fact only a small minority could vote, or had any say in the formation of the government.<br>(C) Kings and queens claimed to rule by divine right.<br>(D) Even when democratic principles of voting were first introduced, they did not include the whole population.</p>",
                    question_hi: "<p>84. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) They were not really answerable to the common people.<br>(B) In fact only a small minority could vote, or had any say in the formation of the government.<br>(C) Kings and queens claimed to rule by divine right.<br>(D) Even when democratic principles of voting were first introduced, they did not include the whole population.</p>",
                    options_en: [
                        "<p>CADB</p>",
                        "<p>CBDA</p>",
                        "<p>ACBD</p>",
                        "<p>ADCB</p>"
                    ],
                    options_hi: [
                        "<p>CADB</p>",
                        "<p>CBDA</p>",
                        "<p>ACBD</p>",
                        "<p>ADCB</p>"
                    ],
                    solution_en: "<p>84.(a) CADB<br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e. Kings and Queens claimed to rule by divine right. Sentence A states that they were not really answerable to common people . So, A will follow C. Further, Sentence D states that they didn&rsquo;t include common people even when democratic principles of voting were introduced and Sentence B concludes that only a small minority had the right to vote. So, B will follow D. Going through the options, option&nbsp;<br>(a) CADB has the correct sequence.</p>",
                    solution_hi: "<p dir=\"ltr\">84.(a) CADB<br>Sentence C प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है अर्थात Kings and Queens ने divine right द्वारा शासन करने का दावा किया है। Sentence A बताता है कि वे वास्तव में common people के प्रति जवाबदेह नहीं थे। तो, C के बाद A आएगा । आगे, Sentence D कहता है कि उन्होंने मतदान के democratic principles को पेश किए जाने पर भी आम लोगों को शामिल नहीं किया और Sentence B का निष्कर्ष है कि केवल एक small minority को वोट देने का अधिकार था। तो, D के बाद B आएगा । विकल्पों के माध्यम से, option (a) CADB में सही क्रम है।<br><br></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the INCORRECTLY spelt word</p>",
                    question_hi: "<p>85. Select the INCORRECTLY spelt word</p>",
                    options_en: [
                        "<p>buoyant</p>",
                        "<p>assasination</p>",
                        "<p>schedule</p>",
                        "<p>obscurity</p>"
                    ],
                    options_hi: [
                        "<p>buoyant</p>",
                        "<p>assasination</p>",
                        "<p>schedule</p>",
                        "<p>obscurity</p>"
                    ],
                    solution_en: "<p>85.(b) assasination<br>&lsquo;Assassination&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(b) assasination<br>&lsquo;Assassination&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Then I would leave him on the edge of the jungle.<br>(B) While I went into the forest to get some luscious twigs for his dinner.<br>(C) I would take him by the ear.<br>(D) Because that is the easiest way to lead an elephant.</p>",
                    question_hi: "<p>86. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Then I would leave him on the edge of the jungle.<br>(B) While I went into the forest to get some luscious twigs for his dinner.<br>(C) I would take him by the ear.<br>(D) Because that is the easiest way to lead an elephant.</p>",
                    options_en: [
                        "<p>BCAD</p>",
                        "<p>CDAB</p>",
                        "<p>CABD</p>",
                        "<p>BADC</p>"
                    ],
                    options_hi: [
                        "<p>BCAD</p>",
                        "<p>CDAB</p>",
                        "<p>CABD</p>",
                        "<p>BADC</p>"
                    ],
                    solution_en: "<p>86.(b) CDAB <br>The parajumble is about how the narrator goes to the jungle with an elephant. C will be the first as it explains the way the narrator would take him by the ear . It will be followed by D because it is given that this is the best way to lead an elephant. A will follow D as in A it is mentioned that after reaching the jungle the narrator would leave him on the edge of the jungle. B will be at the end. So, option (b) CDAB is the suitable answer.</p>",
                    solution_hi: "<p>86.(b) CDAB <br>Parajumble इस बारे में है कि कैसे narrator एक हाथी के साथ जंगल में जाता है। C सबसे पहले होगा क्योंकि यह बताता है कि narrator उसे ear से कैसे ले जाएगा। इसके बाद D होगा क्योंकि यह दिया गया है कि हाथी का lead करने का यह सबसे अच्छा तरीका है। D के बाद A आएगा क्योंकि A में उल्लेख किया गया है कि जंगल में पहुँचने के बाद narrator उसे जंगल के edge पर छोड़ देगा। B अंत में होगा। इसलिए, option (b) CDAB उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. <strong>Select the most appropriate meaning of idiom in the sentence.</strong><br><span style=\"text-decoration: underline;\"><strong>Let sleeping dogs lie.</strong></span></p>",
                    question_hi: "<p>87. <strong>Select the most appropriate meaning of idiom in the sentence.</strong><br><span style=\"text-decoration: underline;\"><strong>Let sleeping dogs lie.</strong></span></p>",
                    options_en: [
                        "<p>Dogs can raise tempers</p>",
                        "<p>Do not allow dogs to stand</p>",
                        "<p>Prevent dog mobility</p>",
                        "<p>Do not bring up an old controversial Issue</p>"
                    ],
                    options_hi: [
                        "<p>Dogs can raise tempers</p>",
                        "<p>Do not allow dogs to stand</p>",
                        "<p>Prevent dog mobility</p>",
                        "<p>Do not bring up an old controversial Issue</p>"
                    ],
                    solution_en: "<p>87.(d) Do not bring up an old controversial issue</p>",
                    solution_hi: "<p>87.(d) Do not bring up an old controversial issue/ किसी पुराने विवादित मुद्दे को न उठाएं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate meaning of idiom in the sentence.<br>Why are you <span style=\"text-decoration: underline;\"><strong>jumping down my throat</strong></span> ? I wasn&rsquo;t even in the house when it happened.</p>",
                    question_hi: "<p>88. Select the most appropriate meaning of idiom in the sentence.<br>Why are you <span style=\"text-decoration: underline;\"><strong>jumping down my throat</strong></span> ? I wasn&rsquo;t even in the house when it happened.</p>",
                    options_en: [
                        "<p>making a joke</p>",
                        "<p>scolding me</p>",
                        "<p>forcing me to act</p>",
                        "<p>running away</p>"
                    ],
                    options_hi: [
                        "<p>making a joke</p>",
                        "<p>scolding me</p>",
                        "<p>forcing me to act</p>",
                        "<p>running away</p>"
                    ],
                    solution_en: "<p>88.(b) Jumping down my throat - &lsquo;Scolding me&rsquo; <br>Example- Don\'t jump down my throat just because I did not support you before the principal.</p>",
                    solution_hi: "<p>88.(b) jumping down my throat - &lsquo;Scolding me&rsquo; (डांट पड़ना)<br>उदाहरण-प्रिंसिपल के सामने मैंने आपका साथ नहीं दिया इसके लिए मुझे डाटो मत।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The dress <strong><span style=\"text-decoration: underline;\">may pleasantly</span></strong> be delivered to us at the earliest.</p>",
                    question_hi: "<p>89. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The dress <span style=\"text-decoration: underline;\"><strong>may pleasantly</strong></span> be delivered to us at the earliest.</p>",
                    options_en: [
                        "<p>No substitution required</p>",
                        "<p>may kindly</p>",
                        "<p>might kindly</p>",
                        "<p>must pleasantly</p>"
                    ],
                    options_hi: [
                        "<p>No substitution required</p>",
                        "<p>may kindly</p>",
                        "<p>might kindly</p>",
                        "<p>must pleasantly</p>"
                    ],
                    solution_en: "<p>89.(b) may kindly<br>We will replace &lsquo;pleasantly&rsquo; with &lsquo;kindly&rsquo; to grammatically correct the given sentence. Hence, &lsquo;may kindly&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(b) may kindly<br>दिए गए वाक्य को grammatically सही करने के लिए हम \'pleasantly\' को \'kindly\' से बदल देंगे। इसलिए, \'may kindly\' सबसे उपयुक्त है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate option to fill in the blank.<br>With danger_______ the door, you cannot sit idle.</p>",
                    question_hi: "<p>90. Select the most appropriate option to fill in the blank.<br>With danger_______ the door, you cannot sit idle.</p>",
                    options_en: [
                        "<p>at</p>",
                        "<p>in</p>",
                        "<p>of</p>",
                        "<p>near</p>"
                    ],
                    options_hi: [
                        "<p>at</p>",
                        "<p>in</p>",
                        "<p>of</p>",
                        "<p>near</p>"
                    ],
                    solution_en: "<p>90.(a) At the door - It means near, before. Example - Due to recession, unemployment is at the door.</p>",
                    solution_hi: "<p>90.(a) At the door - It means near, before./इसका अर्थ है निकट, पहले।<br><strong>Example -</strong> Due to recession, unemployment is at the door./मंदी के कारण बेरोजगारी निकट है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the word which means the same as the group of words given.<br>Strong taste or liking for something</p>",
                    question_hi: "<p>91. Select the word which means the same as the group of words given.<br>Strong taste or liking for something</p>",
                    options_en: [
                        "<p>Insolvent</p>",
                        "<p>Parole</p>",
                        "<p>Ebullient</p>",
                        "<p>Penchant</p>"
                    ],
                    options_hi: [
                        "<p>Insolvent</p>",
                        "<p>Parole</p>",
                        "<p>Ebullient</p>",
                        "<p>Penchant</p>"
                    ],
                    solution_en: "<p>91.(d) <strong>Penchant</strong>- strong taste or liking for something<br><strong>Insolvent</strong>- not having enough money to pay what you owe<br><strong>Parole</strong>- permission that is given to a prisoner to leave prison early on the condition that he/she behaves well<br><strong>Ebullient-</strong> full of confidence and energy</p>",
                    solution_hi: "<p>91.(d) <strong>Penchant</strong> - किसी चीज के लिए तीव्र स्वाद या पसंद.<br><strong>Insolvent</strong> - आपके पास जो बकाया है उसे चुकाने के लिए पर्याप्त पैसा नहीं है<br><strong>Parole</strong> - किसी कैदी को जल्दी जेल से छूटने की अनुमति इस शर्त पर दी जाती है कि वह अच्छा व्यवहार करे<br><strong>Ebullient</strong> - आत्मविश्वास और ऊर्जा से भरपूर</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Find a word that is synonym of :<br>Espionage</p>",
                    question_hi: "<p>92. Find a word that is synonym of :<br>Espionage</p>",
                    options_en: [
                        "<p>planning</p>",
                        "<p>pioneering</p>",
                        "<p>lineage</p>",
                        "<p>spying</p>"
                    ],
                    options_hi: [
                        "<p>planning</p>",
                        "<p>pioneering</p>",
                        "<p>lineage</p>",
                        "<p>spying</p>"
                    ],
                    solution_en: "<p>92.(d) spying<br>The word <strong>Espionage </strong>means : spying; the activity of secretly getting important political or military information about other country etc.</p>",
                    solution_hi: "<p>92.(d) spying<br><strong>Espionage </strong>शब्द का अर्थ है : जासूसी - गुप्त रूप से दूसरे देश आदि के बारे में महत्वपूर्ण राजनीतिक या सैन्य जानकारी प्राप्त करने की गतिविधि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Pick a word opposite in meaning to <br>DILIGENT</p>",
                    question_hi: "<p>93. Pick a word opposite in meaning to <br>DILIGENT</p>",
                    options_en: [
                        "<p>confident</p>",
                        "<p>hardworking</p>",
                        "<p>lazy</p>",
                        "<p>shy</p>"
                    ],
                    options_hi: [
                        "<p>confident</p>",
                        "<p>hardworking</p>",
                        "<p>lazy</p>",
                        "<p>shy</p>"
                    ],
                    solution_en: "<p>93.(c) lazy<br>Diligent - having or showing care and conscientiousness in one\'s work or duties.</p>",
                    solution_hi: "<p>93.(c) lazy<br>Diligent (मेहनती) - Having or showing care and conscientiousness in one\'s work or duties./ किसी काम में सावधान और ईमानदार होना । </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Find a word that is antonym of :<br>Tardy</p>",
                    question_hi: "<p>94. Find a word that is antonym of :<br>Tardy</p>",
                    options_en: [
                        "<p>prompt</p>",
                        "<p>gradual</p>",
                        "<p>late</p>",
                        "<p>tired</p>"
                    ],
                    options_hi: [
                        "<p>prompt</p>",
                        "<p>gradual</p>",
                        "<p>late</p>",
                        "<p>tired</p>"
                    ],
                    solution_en: "<p>94.(a) The word Tardy means: slow to act, move or happen; late in happening.<br>The word Prompt means : immediate; done without delay; punctual.</p>",
                    solution_hi: "<p>94.(a) शब्द <strong>Tardy </strong>का अर्थ है - कार्य करने या होने में धीमा, देर होना।<br>शब्द <strong>Prompt </strong>का अर्थ है - तत्काल; बिना देर किए किया, समयनिष्ठ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Sentence is given with blank to be filled in with an appropriate word. Four alternatives are suggested for each question.<br>A reward is a _______ which motivates a person to achieve excellence in his field.</p>",
                    question_hi: "<p>95. Sentence is given with blank to be filled in with an appropriate word. Four alternatives are suggested for each question.<br>A reward is a _______ which motivates a person to achieve excellence in his field.</p>",
                    options_en: [
                        "<p>monument</p>",
                        "<p>collection</p>",
                        "<p>recognition</p>",
                        "<p>memorial</p>"
                    ],
                    options_hi: [
                        "<p>monument</p>",
                        "<p>collection</p>",
                        "<p>recognition</p>",
                        "<p>memorial</p>"
                    ],
                    solution_en: "<p>95.(c) <strong>Recognition </strong>- identification of someone or something or person from previous encounters or knowledge.<br>(a) <strong>Monument </strong>- a statue, building, or other structure erected to commemorate a notable person or event.<br>(b) <strong>Collection </strong>- the action or process of collecting someone or something.<br>(d) <strong>Memorial </strong>- a statue or structure established to remind people of a person or event.</p>",
                    solution_hi: "<p>95.(c) <strong>Recognition </strong>- मान्यता - identification of someone or something or person from previous encounters or knowledge.<br>(a) <strong>Monument </strong>- स्मारक - a statue, building, or other structure erected to commemorate a notable person or event.<br>(b) <strong>Collection </strong>- संग्रह - the action or process of collecting someone or something.<br>(d) <strong>Memorial </strong>- शहीद स्मारक - a statue or structure established to remind people of a person or event.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: [
                        "<p>denies</p>",
                        "<p>suggests</p>",
                        "<p>garners</p>",
                        "<p>offers</p>"
                    ],
                    options_hi: [
                        "<p>denies</p>",
                        "<p>suggests</p>",
                        "<p>garners</p>",
                        "<p>offers</p>"
                    ],
                    solution_en: "<p>96.(d) offers<br>The phrase &lsquo;offer insight&rsquo; means to gain an accurate and deep understanding of something. The given passage states that literature is a reflection of society and often offers insight into the human experience. Hence, &lsquo;offers&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) offers<br>Phrase &lsquo;offer insight&rsquo; का अर्थ है किसी चीज़ की सटीक और गहरी समझ हासिल करना। दिए गए passage में कहा गया है कि साहित्य समाज का प्रतिबिंब है और अक्सर मानवीय अनुभव में अंतर्दृष्टि प्रदान करता है। इसलिए, &lsquo;offers&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: [
                        "<p>exploit</p>",
                        "<p>explore</p>",
                        "<p>exclaim</p>",
                        "<p>explain</p>"
                    ],
                    options_hi: [
                        "<p>exploit</p>",
                        "<p>explore</p>",
                        "<p>exclaim</p>",
                        "<p>explain</p>"
                    ],
                    solution_en: "<p>97.(b) explore<br>Explore means to try to discover something. The given passage states that literature allows us to explore different emotions and perspectives. Hence, &lsquo;explore&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) explore<br>Explore का अर्थ है कुछ खोजने की कोशिश करना। दिए गए passage में कहा गया है कि literature हमें विभिन्न भावनाओं और दृष्टिकोणों का पता लगाने की अनुमति देता है। इसलिए, &lsquo;explore&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98.<strong> Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: [
                        "<p>platform</p>",
                        "<p>planking</p>",
                        "<p>plank</p>",
                        "<p>planks</p>"
                    ],
                    options_hi: [
                        "<p>platform</p>",
                        "<p>planking</p>",
                        "<p>plank</p>",
                        "<p>planks</p>"
                    ],
                    solution_en: "<p>98.(a) platform<br>&lsquo;Platform&rsquo; means an opportunity to make your ideas or beliefs known publicly. The given passage states that it can serve as a platform for social issues, shedding light on topics that need attention. Hence, &lsquo;platform&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(a) platform<br>&lsquo;Platform&rsquo; का अर्थ है अपने ideas या beliefs को सार्वजनिक रूप से बताने का अवसर। दिए गए passage में कहा गया है कि यह सामाजिक मुद्दों के लिए एक मंच के रूप में काम कर सकता है, उन विषयों पर प्रकाश डाल सकता है जिन पर ध्यान देने की आवश्यकता है। इसलिए, &lsquo;platform&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: [
                        "<p>provokes</p>",
                        "<p>withers</p>",
                        "<p>avoids</p>",
                        "<p>cultivates</p>"
                    ],
                    options_hi: [
                        "<p>provokes</p>",
                        "<p>withers</p>",
                        "<p>avoids</p>",
                        "<p>cultivates</p>"
                    ],
                    solution_en: "<p>99.(d) cultivates <br>Cultivate means to nurture and help grow. The given passage states that reading literature not only enhances our language skills but also cultivates empathy. Hence, &lsquo;cultivates&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) cultivates <br>Cultivate का अर्थ है nurture और grow में मदद करना। दिए गए passage में कहा गया है कि साहित्य पढ़ने से न केवल हमारी भाषा कौशल में सुधार होता है बल्कि सहानुभूति भी बढ़ती है। इसलिए, &lsquo;cultivates&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test :</strong><br>Literature is a reflection of society and often (96)______ insight into the human experience. Through various forms like novels, poems, and plays, literature allows us to (97)______ different emotions and perspectives. It can serve as a (98)______ for social issues, shedding light on topics that need attention. Reading literature not only enhances our language skills but also (99)______ empathy by helping us understand the feelings of characters. In a rapidly changing world, literature (100)______ a timeless portal to diverse worlds and cultures.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: [
                        "<p>became</p>",
                        "<p>become</p>",
                        "<p>becomes</p>",
                        "<p>becoming</p>"
                    ],
                    options_hi: [
                        "<p>became</p>",
                        "<p>become</p>",
                        "<p>becomes</p>",
                        "<p>becoming</p>"
                    ],
                    solution_en: "<p>100.(c) becomes<br>&lsquo;Literature&rsquo; is a singular subject that will take &lsquo;becomes&rsquo; as a singular verb. Hence, &lsquo;becomes&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) becomes<br>&lsquo;Literature&rsquo; एक singular subject है इसलिए &lsquo;becomes&rsquo; singular verb की form में प्रयोग होगा। इसलिए, &lsquo;becomes&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>